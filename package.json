{"name": "flat-creator-lucky", "private": true, "version": "1.0.2", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "build:test": "NODE_ENV=test vite build --mode test", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --host", "prepare": "simple-git-hooks", "sourcemap:clean": "rimraf ./dist/**/*.map", "proto:generate": "mkdir -p ./src/proto && node scripts/proto-gen.mjs", "postinstall": "npx simple-git-hooks"}, "dependencies": {"@fe-design/utils": "2.25.1", "@icon-park/react": "^1.4.2", "@react-vant/icons": "^0.1.0", "@sentry/react": "8.5.0", "@unocss/reset": "^0.64.1", "antd": "^5.22.2", "axios": "1.7.2", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "1.11.11", "firebase": "^11.0.2", "glob": "^11.0.0", "i18next": "^23.16.4", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "patch-package": "^8.0.0", "react": "18.2.0", "react-dom": "18.2.0", "react-helmet": "^6.1.0", "react-i18next": "^15.1.0", "react-intl": "6.6.8", "react-router-dom": "6.23.1", "react-use": "^17.5.1", "react-vant": "3.3.5", "sass": "^1.85.1", "swr": "^2.2.5", "ts-proto": "^2.2.5", "zustand": "4.5.2"}, "devDependencies": {"@fe-design/vite-plugin-build-callback": "^0.0.20", "@fe-design/vite-plugin-inject-initial-state": "^0.0.6", "@protobuf-ts/protoc": "^2.9.4", "@types/adm-zip": "^0.5.7", "@types/lodash": "^4.17.13", "@types/node": "20.12.13", "@types/react": "18.2.66", "@types/react-dom": "18.2.22", "@types/react-helmet": "^6.1.11", "@typescript-eslint/eslint-plugin": "7.2.0", "@typescript-eslint/parser": "7.2.0", "@vite-pwa/assets-generator": "0.2.4", "@vitejs/plugin-legacy": "5.4.0", "@vitejs/plugin-react-swc": "3.5.0", "autoprefixer": "10.4.19", "commitlint": "^19.5.0", "eslint": "8.57.0", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.6", "lint-staged": "15.2.5", "postcss": "8.4.38", "postcss-px-to-viewport-8-plugin": "1.2.5", "prettier": "3.2.5", "prettier-plugin-tailwindcss": "0.6.1", "simple-git-hooks": "2.11.1", "tailwindcss": "3.4.3", "typescript": "5.2.2", "unocss": "^0.64.1", "vconsole": "3.15.1", "vite": "5.2.12", "vite-plugin-pages": "0.32.2", "vite-plugin-pwa": "0.20.0"}, "simple-git-hooks": {"pre-commit": "npx lint-staged"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix --ext .ts,.tsx"]}, "volta": {"node": "20.14.0"}}