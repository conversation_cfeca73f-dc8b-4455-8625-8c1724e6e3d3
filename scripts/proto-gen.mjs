import path from 'node:path';
import fs from 'node:fs/promises';
import os from 'node:os';
import { exec } from 'node:child_process';
import { promisify } from 'node:util';
import { glob } from 'glob';
const execAsync = promisify(exec);

const PROTO_BRANCH = 'dev';
const PROTO_REPO_URL = '************:social/lucky/proto.git';
const PROTO_REPO_URL_HTTP = `https://gitit.cc/api/v4/projects/1535/repository/archive?sha=${PROTO_BRANCH}`;
// eslint-disable-next-line no-undef
const TARGET_DIRECTORY = path.resolve(process.cwd(), 'proto');
// 创建临时目录
const tempDir = await fs.mkdtemp(path.join(os.tmpdir(), 'proto-'));
// eslint-disable-next-line no-undef
if (process.env.CARNI_PROTO_PRIVATE_TOKEN) {
  const tempTarball = path.join(tempDir, 'archive.tar');
  // eslint-disable-next-line no-undef
  const archiveCommand = `curl ${PROTO_REPO_URL_HTTP} -H "PRIVATE-TOKEN: ${process.env.CARNI_PROTO_PRIVATE_TOKEN}" \
  -o ${tempTarball} && \
  mkdir -p ${TARGET_DIRECTORY} && \
  tar --extract --file ${tempTarball} --strip-components=1 -C ${TARGET_DIRECTORY} && \
  rm ${tempTarball}
  `;
  await execAsync(archiveCommand);
} else {
  const archiveCommand = `git archive --remote=${PROTO_REPO_URL} ${PROTO_BRANCH} \
  --format=zip -o ${path.join(tempDir, 'archive.zip')}`;
  await execAsync(archiveCommand);

  // 确保目标目录存在
  await fs.rm(TARGET_DIRECTORY, { recursive: true, force: true });
  await fs.mkdir(TARGET_DIRECTORY, { recursive: true });

  // 解压文件
  const unzipCommand = `unzip -o ${path.join(tempDir, 'archive.zip')} -d ${TARGET_DIRECTORY}`;
  await execAsync(unzipCommand);
}

// 清理临时目录
await fs.rm(tempDir, { recursive: true, force: true });
await fs.rm(path.join(globalThis.process.cwd(), 'src/proto/*/'), { recursive: true, force: true });
await fs.mkdir(path.join(globalThis.process.cwd(), 'src/proto'), { recursive: true });

console.log(`Generating proto to ts code...`);
const cmd = `npx protoc \
  --plugin=./node_modules/.bin/protoc-gen-ts_proto \
  --ts_proto_out=./src/proto \
  --proto_path=./proto \
  --ts_proto_opt=outputServices=generic-definitions,outputEncodeMethods=false,snakeToCamel=false,outputJsonMethods=from-only \
`;

const files = await glob.glob(`./proto/**/*.proto`, {
  nodir: true,
  ignore: `./proto/**/svr/**/*.proto`
});

await execAsync(`${cmd} ${files.join(' ')}`);
console.clear();
console.log(`Generate proto to ts code done.`);
