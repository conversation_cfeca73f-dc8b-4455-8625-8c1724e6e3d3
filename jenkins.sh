npx zx@7.2.3 << 'EOF'
const fs = require('fs');
const env = $.env;
const afterSha = env.gitlabAfter;
const isPackage = env.IS_CI_PACKAGE;
const isFullSPA = env.FULL_SPA;
let commitID = env.CommitID;

if(isPackage === 'true') {
  echo`===> Install dependencies...`;
  $`rm package-lock.json`;
  await $`npm i`;
}

const creatorPathArr = await getPathArr();

if(creatorPathArr.length === 0) {
  echo`===> Nothing to build`;
} else {
  echo`===> Will build: ${creatorPathArr.join('; ')}`;

  for (const appPath of creatorPathArr) {
    echo`===> Build project [${appPath}] ...`;
    echo`===> Is offline package? ${isFullSPA}`;
    if(isFullSPA === 'true') {
      await $`APP=${appPath} FULL_SPA=1 npm run build`;
    }else{
      await $`APP=${appPath} npm run build`;
    }
  }
}

fs.writeFileSync('.envinject', `build_message=\
- 构建应用：${creatorPathArr.length > 0 ? creatorPathArr.join('; '): '无'}\
`);

// ==================== 分界线，主要放置 function 定义 ======================
// 获取需要构建的应用路径
async function getPathArr () {
   const reg = /^[0-9a-f]{40}$/;
  // commitID 可以是应用路径（即APP）
  console.log('=====>>>=====', commitID || 'Auto trigger', ', Hash:', reg.test(commitID));
  if(commitID) {
    if(!reg.test(commitID)) {
      if(commitID.includes(',')) {
        return commitID.split(',').map(item => item.trim());
      }
      return [commitID];
    }
  } else if (!afterSha) {
  	return [];
  }

  // 获取需要构建的 commitID，因为有3种情况：1. 手动在界面上输入commitID构建；2. 是 merge到 main 分支； 3. 是直接 push 到 main 分支
  let allDiffedPaths = '';
  let hasFileStr = '';
  if(!commitID) {
    const str = await $`git log -5 --pretty=format:"{ \\"commit\\": \\"%H\\", \\"author\\": \\"%an\\", \\"email\\": \\"%ae\\", \\"date\\": \\"%ad\\", \\"message\\": \\"%s\\" }"`;
    const arr = JSON.parse(`[${str.toString().trim().split('\n').join(',')}]`);
    let isValid = false;

    for(let i = 0; i < arr.length; i++) {
      const item = arr[i];
      if(item.commit === afterSha) {
        isValid = true;
      }

      if(isValid && !item.message.startsWith('Merge branch ')) {
        allDiffedPaths = await $`git diff-tree --no-commit-id --name-only -r ${item.commit}`;
        hasFileStr = allDiffedPaths.stdout ?? '';
        // 场景是开发分支 A，合并到 main 分支 A，然后 push 到 main，故取下一个 commit
        commitID = !hasFileStr ? arr[i+1].commit : item.commit;
        break;
      }
    }
  }

  if(!hasFileStr && commitID) {
    allDiffedPaths = await $`git diff-tree --no-commit-id --name-only -r ${commitID}`;
    hasFileStr = allDiffedPaths.stdout ?? '';
  }

  const pathMatcher = /^src\/pages\/(.*)\//;
  const shortReg = /\/component|\/image|\/img|\/module|\/util|\/store|\/common/ig;
  const fullPathMap = {};
  const allPaths = hasFileStr
  	.split('\n')
    .filter(item => item.match(pathMatcher))
  	.map(item => {
      const path = item.match(pathMatcher)[1];
      shortReg.lastIndex = 0;

      // 应用根目录的映射
      if(shortReg.test(path)) {
        const indexPath = path.split(shortReg)[0];
        const indexAbsPath = item.split(shortReg)[0]
        fullPathMap[indexPath] = indexAbsPath;
      }
      fullPathMap[path] = item.slice(0, item.lastIndexOf('/'));
      return path
    })
    .map(item => item.split(shortReg)[0]);

  let pathArr = [];

  allPaths.forEach(item => {
    const tempArr = item.split('/');
    if(tempArr.length === 1) {
      // aaa
      pathArr.push(item);
      // 最多支持到 4 级
    } else if([2, 3, 4].includes(tempArr.length)) {
      // aaa/bbb 或者 aaa/bbb/ccc，分割重组
      pathArr.push(item);
      for(let i = 1; i < tempArr.length; i++) {
        const key = tempArr.slice(0, i).join('/');
        const right = tempArr.slice(i).join('/');
        if(fullPathMap[item]) {
          const value = fullPathMap[item].replace(`/${right}`, '');
          fullPathMap[key] = value;
          pathArr.push(key);
        }
      }
    }
  });
  pathArr.sort((a, b) => a.length - b.length).sort((a, b) => a.split('/').length - b.split('/').length);
  // ['aaa', 'bbb/ccc', 'bbb/ccc/ddd', 'bbb/ccc/eee', 'bbb/ccc/fff']
  let uniqeArr = [...new Set(pathArr)];

  let indicatorArr = [];
  uniqeArr = uniqeArr.filter(item => {
    const isExist = fs.existsSync(fullPathMap[item]+ '/index.tsx');

    // 逻辑 B 版（A 版的优化）
    let key = '';
    for(let i = 0; i < indicatorArr.length; i++) {
      const indicator = indicatorArr[i];
      if(item.startsWith(indicator)){
        key = indicator;
      }
    }

    let levelGap = item.split('/').length - key.split('/').length;
    // 最多支持到 4 级
    if(key && [1, 2, 3].includes(levelGap)) {
      return false;
    } else {
      if(isExist) {
        indicatorArr.push(item);
      }
      return isExist;
    }
  });

  return uniqeArr;
}

EOF
