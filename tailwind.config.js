/** @type {import('tailwindcss').Config} */

import { AppFolder } from './vite.config.ts';

export default {
  content: ['./index.html', './src/components/**/*.{js,ts,jsx,tsx}', `./${AppFolder}/**/*.{js,ts,jsx,tsx}`],
  theme: {
    extend: {
      keyframes: {
        // 公共，示例
        scroll_down: {
          '0%': { backgroundPosition: '0% 0%' },
          '100%': { backgroundPosition: '0% 100%' }
        }
      },
      animation: {
        // 公共，示例
        scroll_down: 'scroll_down 2s linear infinite'
      }
    }
  },
  plugins: []
};
