import { definePreset, Preset } from 'unocss';
import { rules } from './rules';
export const vwPreset = definePreset((options: Preset): Preset => {
  return {
    name: 'vw-preset',
    prefix: 'vw',
    rules: [...rules('vw')]
  };
});
export const pxPreset = definePreset((options: Preset): Preset => {
  return {
    name: 'px-preset',
    prefix: 'px',
    rules: [...rules('px')]
  };
});
export const percentagePreset = definePreset((options: Preset): Preset => {
  return {
    name: 'percentage-preset',
    prefix: '%',
    rules: [...rules('%')]
  };
});
