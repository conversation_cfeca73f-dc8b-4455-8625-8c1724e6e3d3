import { Rule } from 'unocss';

export const rules = (unit: string) =>
  [
    // padding
    [/^p-(\d+\.?\d*)$/, ([, d]) => ({ padding: `${d}${unit}` }), { autocomplete: ['p-<num>'] }],
    [/^px-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-inline': `${d}${unit}` }), { autocomplete: ['px-<num>'] }],
    [/^py-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-block': `${d}${unit}` }), { autocomplete: ['py-<num>'] }],
    [/^pt-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-top': `${d}${unit}` }), { autocomplete: ['pt-<num>'] }],
    [/^pb-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-bottom': `${d}${unit}` }), { autocomplete: ['pb-<num>'] }],
    [/^pl-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-left': `${d}${unit}` }), { autocomplete: ['pl-<num>'] }],
    [/^pr-(\d+\.?\d*)$/, ([, d]) => ({ 'padding-right': `${d}${unit}` }), { autocomplete: ['pr-<num>'] }],
    // margin
    [/^m-(\d+\.?\d*)$/, ([, d]) => ({ margin: `${d}${unit}` }), { autocomplete: ['m-<num>'] }],
    [/^mx-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-inline': `${d}${unit}` }), { autocomplete: ['mx-<num>'] }],
    [/^my-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-block': `${d}${unit}` }), { autocomplete: ['my-<num>'] }],
    [/^mt-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-top': `${d}${unit}` }), { autocomplete: ['mt-<num>'] }],
    [/^mb-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-bottom': `${d}${unit}` }), { autocomplete: ['mb-<num>'] }],
    [/^ml-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-left': `${d}${unit}` }), { autocomplete: ['ml-<num>'] }],
    [/^mr-(\d+\.?\d*)$/, ([, d]) => ({ 'margin-right': `${d}${unit}` }), { autocomplete: ['mr-<num>'] }],
    // width
    [/^w-(\d+\.?\d*)$/, ([, d]) => ({ width: `${d}${unit}` }), { autocomplete: ['w-<num>'] }],
    [/^min-w-(\d+\.?\d*)$/, ([, d]) => ({ 'min-width': `${d}${unit}` }), { autocomplete: ['min-w-<num>'] }],
    [/^max-w-(\d+\.?\d*)$/, ([, d]) => ({ 'max-width': `${d}${unit}` }), { autocomplete: ['max-w-<num>'] }],
    // height
    [/^h-(\d+\.?\d*)$/, ([, d]) => ({ height: `${d}${unit}` }), { autocomplete: ['h-<num>'] }],
    [/^min-h-(\d+\.?\d*)$/, ([, d]) => ({ 'min-height': `${d}${unit}` }), { autocomplete: ['min-h-<num>'] }],
    [/^max-h-(\d+\.?\d*)$/, ([, d]) => ({ 'max-height': `${d}${unit}` }), { autocomplete: ['max-h-<num>'] }],
    // font-size
    [/^text-(\d+\.?\d*)$/, ([, d]) => ({ 'font-size': `${d}${unit}` }), { autocomplete: ['text-<num>'] }],
    [/^leading-(\d+\.?\d*)$/, ([, d]) => ({ 'line-height': `${d}${unit}` }), { autocomplete: ['leading-<num>'] }],
    // gap
    [/^gap-(\d+\.?\d*)$/, ([, d]) => ({ gap: `${d}${unit}` }), { autocomplete: ['gap-<num>'] }],
    // position
    [/^left-(\d+\.?\d*)$/, ([, d]) => ({ left: `${d}${unit}` }), { autocomplete: ['left-<num>'] }],
    [/^right-(\d+\.?\d*)$/, ([, d]) => ({ right: `${d}${unit}` }), { autocomplete: ['right-<num>'] }],
    [/^top-(\d+\.?\d*)$/, ([, d]) => ({ top: `${d}${unit}` }), { autocomplete: ['top-<num>'] }],
    [/^bottom-(\d+\.?\d*)$/, ([, d]) => ({ bottom: `${d}${unit}` }), { autocomplete: ['bottom-<num>'] }]
  ] as Rule[];
