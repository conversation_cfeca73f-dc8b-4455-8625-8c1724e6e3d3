npx zx@7.2.3 << 'EOF'
import fs from 'fs';
import AdmZip from 'adm-zip';
import dayjs from 'dayjs'
const env = $.env;
const appUrl = env.OFFLINE_APP_URL;

const appInfo = fs.readFileSync('.envinject', 'utf-8');
const appName = appInfo.split('：')[1].replaceAll('/', '-');
const filename = `${appName}_${getTimeStr()}_offline-pkg.zip`;
fetch(appUrl)
  .then(response => response.text())
  .then(text => {
    fs.writeFileSync('./dist/index.html', text);
    const zip = new AdmZip();
    zip.addLocalFolder('./dist');
    zip.writeZip(`./release/${filename}`);
    console.log(`请前往工作空间获取离线包：/release/${filename}`);
  });
const dirs = fs.readdirSync('./dist');
console.log(appUrl);
console.log(dirs);

function p0(n) {
  return n.toString().padStart(2, '0');
}

function getTimeStr() {
  return dayjs().format('YYYY-MM-DD_HH-mm');
}

EOF