import { DEFAULT_LOCALE, SUPPORTED_LOCALES, TypedSupportedLocale } from '@/configs';
import { AndroidPageProps, IOSPageProps } from '@/configs/types';
import dayjs from 'dayjs';
import { CSSProperties, ReactNode, createElement } from 'react';

export const getLang = () => {
  if (location.search.length > 2) {
    const qs = new URLSearchParams(location.search);
    const lang = qs.get('lan') as TypedSupportedLocale;
    if (SUPPORTED_LOCALES.includes(lang)) {
      return lang;
    }
  }
  const lang = navigator.languages.find(lang => SUPPORTED_LOCALES.includes(lang.split('-')[0] as TypedSupportedLocale));
  return lang ?? DEFAULT_LOCALE;
};

export const getBaseUrl = (env: string) => {
  const baseUrl = env !== 'prod' ? 'http://test-api.crushu.net:8001/api/crushu' : 'https://api.crushu.net/api/crushu';
  return baseUrl;
};

export const getBaseUrlV2 = (appname: string, domain: string, env: string) => {
  return env == 'prod' ? `https://api.${domain}/api/${appname}` : `http://test-api.${domain}/api/${appname}`;
};

export const isAppWebview = () => {
  return window.navigator.userAgent.includes('crushu');
};

export const isAppWebviewV2 = () => {
  let isLagecy = false;
  let isInApp = false;
  const ua = window.navigator.userAgent;
  const pattern = ua.split(';').slice(-1)[0].trim().split('/');

  if (pattern.length === 3 && /^\d\.\d{2,2}\.\d{2,2}$/.test(pattern[1])) {
    isLagecy = true;
    isInApp = true;
  } else if (pattern[0] === 'crushu' && /^\d{8,8}$/.test(pattern[1])) {
    isLagecy = false;
    isInApp = true;
  }

  return {
    isLagecy,
    isInApp
  };
};

export const getEnv = () => {
  return window.__INITIAL_STATE__?.env || 'local';
};

export const getQueryParams = () => {
  const url = window.location.search;
  const params = new URLSearchParams(url);
  return Object.fromEntries(params.entries());
};

export const objectNumberToString = obj => {
  const o = {};
  for (const k in obj) {
    if (typeOf(obj[k]) === 'number') {
      o[k] = String(obj[k]);
    } else if (typeOf(obj[k]) === 'object') {
      o[k] = objectNumberToString(obj[k]);
    } else if (typeOf(obj[k]) === 'array') {
      o[k] = obj[k].map(v => {
        if (typeOf(v) === 'string') {
          return v;
        }
        if (typeOf(v) === 'number') {
          return v.toString();
        } else {
          return objectNumberToString(v);
        }
      });
    } else {
      o[k] = obj[k];
    }
  }
  return o;
};

export const typeOf = obj => {
  return Object.prototype.toString.call(obj).slice(8, -1).toLowerCase();
};

export const wait = (ms: number) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * 高亮文本
 * @param d 文本
 * @param key 高亮关键字
 * @param s 高亮文本
 * @param color 高亮颜色
 * @returns
 */
export const highLight = (d: string, key: string, s: ReactNode, styles: CSSProperties = { color: '#ffaf00' }) => {
  const arr = d.split(`{{${key}}}`);
  const h = createElement('span', { style: styles }, s);
  const n: (string | JSX.Element)[] = [];
  arr.forEach((item, index) => {
    if (index !== arr.length - 1) {
      n.push(item, h);
    } else {
      n.push(item);
    }
  });
  return n;
};

export const formatAndroidVestPageConfig = () => {
  const env = getEnv();
  const appData = window.__INITIAL_STATE__.appData.data;
  console.log(appData);
  const config: AndroidPageProps = {
    Name: appData.name,
    subanm: appData.subanm,
    Slogan: appData.slogan,
    Email: appData.email,
    GradientColors: appData.colorSet.split(',').slice(0, 2),
    images: {
      imgBG: appData.img_bg,
      imgLogo: appData.img_logo,
      imgHero: appData.img_hero,
      imgFeatures: appData.img_features
    },
    Features: [
      {
        icon: appData.feature1.icon,
        title: appData.feature1.title,
        desc: appData.feature1.desc
      },
      {
        icon: appData.feature2.icon,
        title: appData.feature2.title,
        desc: appData.feature2.desc
      },
      {
        icon: appData.feature3.icon,
        title: appData.feature3.title,
        desc: appData.feature3.desc
      }
    ],
    firebaseConfig: {
      ...JSON.parse(appData.firebase),
      appId: appData.firebaseAppId[env] || appData.firebaseAppId.test
    },
    baseUrl: appData.baseurl,
    pkg: appData.pkg,
    appstoreLink: appData.appstoreLink,
    enableDelAcc: appData.enableDelAcc || false,
    enableStoreEntry: appData.enableStoreEntry || false
  };

  return config;
};

export const formatIOSVestPageConfig = () => {
  const env = getEnv();
  const appData = window.__INITIAL_STATE__.appData.data;
  console.log(appData);
  const config: IOSPageProps = {
    Name: appData.name,
    subanm: appData.subanm,
    Slogan: appData.slogan,
    Email: appData.email,
    GradientColors: appData.colorSet?.split(',').slice(0, 2) || ['', ''],
    images: {
      imgBG: appData.img_bg,
      imgLogo: appData.img_logo,
      imgHero: appData.img_hero,
      imgFeatures: appData.img_features
    },
    Features: [
      {
        icon: appData.feature1?.icon,
        title: appData.feature1?.title,
        desc: appData.feature1?.desc
      },
      {
        icon: appData.feature2?.icon,
        title: appData.feature2?.title,
        desc: appData.feature2?.desc
      },
      {
        icon: appData.feature3?.icon,
        title: appData.feature3?.title,
        desc: appData.feature3?.desc
      }
    ],
    baseUrl: appData.baseurl,
    pkg: appData.pkg,
    appstoreLink: appData.appstoreLink,
    appleConfig: {
      appId: appData.appId,
      team_id: appData.team_id,
      key_id: appData.key_id,
      key_auth: appData.key_auth
    },
    enableDelAcc: appData.enableDelAcc || false,
    enableStoreEntry: appData.enableStoreEntry || false
  };

  return config;
};

export const getResourceUrl = (env: string) => {
  return env !== 'prod' ? 'https://test-res.crushu.net' : 'https://res.crushu.net';
};

export function formatAvatatUrl(uid, env = 'prod') {
  if (!uid) {
    return;
  }
  const imgIds = uid.substring(uid.length - 4, uid.length);
  const imgId1 = imgIds.substring(0, 2);
  const imgId2 = imgIds.substring(2, 4);
  return `${getResourceUrl(env)}/crushu/user/avatar/${imgId1}/${imgId2}/${uid}.jpg?x-oss-process=style/hq`;
}

/**
 * 根据提供的对象格式化选项数组。
 * @param obj 一个键值对对象，键可以是字符串或数字，值可以是任意类型。
 * @param isNumberValue 是否将转换出的 value 转换为数字类型。
 * @returns 返回一个对象数组，每个对象包含`label`和`value`属性。`label`对应原对象的值，`value`根据`isNumberValue`参数决定是键的字符串还是数字类型。
 */
export const formatOptionsOfObj = (
  obj: Record<string | number, any>,
  isNumberValue: boolean = false,
  key: string = 'value'
) =>
  Object.keys(obj).map(_key => ({
    text: obj[_key], // 将原对象的值作为label
    [key]: isNumberValue ? +_key : _key // 根据isNumberValue参数决定value的类型
  })) as { text: string; value: string; key: string }[];

export const formatUnix = (ts: number, format: string = 'YYYY-MM-DD HH:mm:ss') =>
  ts ? dayjs.unix(ts).format(format) : '-';
