interface NavBar {
  visible?: number;
  bg_color?: string;
  nav_alpha?: number;
  text_color?: string;
  title?: string;
  left_btn_config?: number;
}

interface MenuOptions {
  title: string;
  desc?: string;
  image_url?: string;
  link: string;
  type: 'system' | 'app';
}

/**
 * 统一调用函数
 */
export const callHandler = async <T = any>(name: string, data = {}, headers?: any) => {
  return new Promise<T>(resolve => {
    console.log('callHandler name, data:', name, data, headers);
    const callHandlerFun = () => {
      const resp = response => {
        console.log(`callHandler response => ${name}:`, response);
        resolve(response.data);
      };
      window?.JSBridge?.callHandler(name, data, resp);
    };
    if (window?.JSBridge) {
      callHandlerFun();
    } else {
      injectJSBridgeCallbacks(callHandlerFun);
    }
  });
};

/**
 * 统一注册web方法
 */
export const registerHandler = async (name: string, callback) => {
  const registerHandlerFunc = () => {
    window?.JSBridge?.registerHandler(name, data => {
      callback(data);
    });
  };
  if (window?.JSBridge) {
    registerHandlerFunc();
  } else {
    injectJSBridgeCallbacks(registerHandlerFunc);
  }
};
/**
 * 打点
 */
export const report = async (action: string, param?: { [keyName: string]: string | number }) => {
  if (!action) {
    throw new Error('Action is required');
  }
  return await callHandler('report', { action, param });
};

/**
 * 获取用户信息
 */
export const getUserInfo = async () => {
  /* if (window?.jsCallNative) {
    return await getBaseParamsAsync();
  } */
  if (window?.callAppFunction) {
    return new Promise((resolve, reject) => {
      window?.callAppFunction({
        name: 'getUserInfo',
        callback: res => {
          const data = res?.data ?? {};
          return resolve({
            ...data,
            uid: parseInt(data?.uid ?? 0),
            coin: parseInt(data?.coin ?? 0)
          });
        }
      });
    });
  }

  let res: any = await callHandler('getLoginUser');
  if (typeof res === 'string') {
    try {
      res = JSON.parse(res);
    } catch (error) {
      console.log('parse LoginUser error:', error);
    }
  }
  return res;
};

/**
 * 路由跳转
 */
export const openUrl = async (url: string) => {
  if (window?.callAppFunction) {
    return new Promise((resolve, reject) => {
      window?.callAppFunction({
        name: 'jumpAction',
        data: {
          url
        }
      });
    });
  }
  callHandler('openUrl', {
    url
  });
};

// ============================== 以上为 JSBridge 标准调用方法，以下为业务兼容方法 ==============================

/**
 * 检测当前用户是否登录
 */
export const checkLoginStatus = async () => {
  const res: any = await callHandler('checkLoginStatus');
  return res.status;
};

/**
 * 获取当前用户（旧）
 */
export const getLoginUser = async () => {
  if (window?.nativeCallback) {
    return await getBaseParamsAsync();
  }

  let res: any = await callHandler('getLoginUser');
  if (typeof res === 'string') {
    try {
      res = JSON.parse(res);
    } catch (error) {
      console.log('parse LoginUser error:', error);
    }
  }
  return res;
};

/**
 * 打开 VDM Cash积分墙
 */
export const openVDMCashTaskList = async options => {
  await callHandler('getTaskList', options);
};

/**
 * 获取设备信息
 */
export const getDeviceInfo = async () => {
  return await callHandler('getDeviceInfo');
};

/**
 * 关闭当前网页
 */
export const closeWebview = async () => {
  await callHandler('jumpAction');
};

/**
 * 设置导航栏信息
 */
export const setNavBar = async (data: NavBar) => {
  await callHandler('setNavBar', data);
};

/**
 * 调用系统邮件
 */
export const sendEmail = async (data: {
  to_email?: string; // 收件人，多个用","隔开
  cc_email?: string; // 收件人，多个用","隔开
  bcc_email?: string; // 密抄人，多个用","隔开
  subject?: string; // 邮件主题
  body?: string; // 邮件内容
}) => {
  await callHandler('sendEmail', data);
};

/**
 * 评分弹窗
 */
export const showRating = async () => {
  await callHandler('showRating');
};

/**
 * 切换到指定tab
 */
export const switchTab = async (tab: string) => {
  await callHandler('switchTab', { tab });
};

/**
 * 获取签到业务数据
 */
export const getSignIn = async (): Promise<any> => {
  let res = await callHandler('getSignIn');
  if (typeof res === 'string') {
    try {
      res = JSON.parse(res);
    } catch (error) {
      console.log('parse BusinessData error: ', error);
    }
  }
  return res;
};

/**
 * 关闭客户端窗口
 * @param act: number 【默认1】1-退出浏览器,2-返回上一步,3-刷新,4-禁止返回键动作，5-取消禁止返回键动作
 */
export const closeView = async (act?: number) => {
  await callHandler('setBehavior', { action: act ? act : 1 });
};

export const disableBackButton = async () => {
  await callHandler('setBehavior', { action: 4 });
};

export const cancelDisableBackButton = async () => {
  await callHandler('setBehavior', { action: 5 });
};

/**
 * 打开分享面板
 */
export const openShareMenu = (options: MenuOptions) => {
  callHandler('openShareMenu', options);
};

/**
 * 打开分享面板
 */
export const networkRequest = (options: MenuOptions) => {
  callHandler('networkRequest', options);
};

/**
 * 打开app
 */
export const openApp = async (scheme: string, failCallback?: () => void) => {
  let res: any = await callHandler('openApp', {
    scheme: scheme
  });
  if (typeof res === 'string') {
    try {
      res = JSON.parse(res);
    } catch (error) {
      console.log('parse openApp error: ', error);
    }
  }
  // 未安装app或者未实现该方法时执行失败回调
  if (res.status === false || res.status === undefined) {
    typeof failCallback === 'function' && failCallback();
  }
};

/**
 * 打开客户端路由
 * @param page:string 传完整的 URL，如： 'crushu://www.crushu.net?lang=en'
 */
export const openNavtiveUrl = async (page?: string) => {
  callHandler('openUrl', {
    url: `crushu://www.crushu.net?${page}`
  });
};

/**
 * 打开充值面板
 */
export const openPayPage = (scene: string) => {
  callHandler('showPayPage', {
    scene
  });
};

/**
 * 震动一下
 */
export const vibrate = () => {
  callHandler('vibrate');
};

/**
 * 注入JSbridg初始化回调
 */
const injectJSBridgeCallbacks = callback => {
  if (window.JSBridgeCallbacks) {
    console.log('add callback to JSBridgeCallbacks');
    window.JSBridgeCallbacks.push(callback);
  }
  if (window.JSBridge) {
    console.log('callback removed');
    window?.JSBridgeCallbacks?.pop();
    callback();
  }
};

/**
 * window?.jsCallNative -> nativeCallback
 */
const nativeCallback = data => {
  if (typeof window[data?.callback] === 'function') {
    const func: any = window[data?.callback];
    func(data.data);
  } else {
    alert('Callback function does not exist');
  }
};

/**
 * window?.jsCallNative -> init
 */
const init = () => {
  if (!window?.nativeCallback) {
    window.nativeCallback = nativeCallback;
  }
};

/**
 * window?.jsCallNative -> isInClient
 */
const isInClient = () => {
  if (!window?.jsCallNative) {
    console.log('Not in client');
    return false;
  }
  return true;
};

let baseParamsCallbackId = 0; // 兼容多次同时调用getBaseParams的情况

/**
 * window?.jsCallNative -> getBaseParams
 */
const getBaseParams = callback => {
  if (!isInClient()) {
    if (callback) {
      callback();
    }
    return null;
  }

  init();
  const callbackFunctionName = 'getBaseParamsCallback' + baseParamsCallbackId++; // 生成不同的回调函数名
  const params = {
    module: 'data',
    method: 'getBaseParams',
    callback: callbackFunctionName
  };

  try {
    window[callbackFunctionName] = callback;
    window.jsCallNative.postMessage(JSON.stringify(params));
  } catch (error) {
    console.log('Get client data error: ', error);
  }
};

/**
 * window?.jsCallNative -> getBaseParamsAsync
 */
const getBaseParamsAsync = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    try {
      getBaseParams(baseinfo => {
        try {
          if (!baseinfo) {
            reject(null);
            return;
          }

          resolve(JSON.parse(baseinfo));
        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      reject(error);
    }
  });
};
