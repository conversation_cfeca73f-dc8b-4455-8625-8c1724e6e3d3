import netRequest from '@fe-design/utils/net-request';
interface ReqParamsType {
  url: string;
  data: object;
  headers?: Record<string, any>;
}

interface ResponseType<T> {
  status: number;
  msg: string;
  data: T;
}

// Demo请求模块
const demoRequest = <T>(opt: ReqParamsType): Promise<T> => {
  const { url, data, headers } = opt;
  return new Promise((resolve, reject) => {
    netRequest
      .post(
        url, // 业务请求URL
        {
          ...data // 请求参数
        },
        {
          'X-proxy': true,
          'X-showMessage': true,
          headers: {
            'X-SIGN': true,
            'X-NOT-ENCODE': true,
            ...headers
          }
        }
      )
      .then((res: ResponseType<T>) => {
        console.log('res:', res);
        if (res.status === 1) {
          resolve(res.data);
        }
      })
      .catch((err: T) => {
        reject(err); // 测试 URL 默认无数据
      });
  });
};

export default demoRequest;
