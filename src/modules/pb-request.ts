import netRequest from '@fe-design/utils/net-request';
import { Toast } from 'react-vant';
import * as JSBridge from '@fe-design/utils/js-bridge';
import { getEnv } from '@/modules/utils';

const ServerError = 'The server is a bit busy! Please try again later';
interface Headers {
  'X-SIGN'?: string | boolean | null;
  'x-token-bin'?: string | boolean | null;
  'x-pubpara-bin'?: string | boolean | null;
  'x-reqcontrol-bin'?: string | boolean | null;
  'X-NOT-ENCODE'?: boolean | null;
}
interface ReqParamsType {
  data: object;
  xMethod: string;
  headers?: Headers;
  proxyUrl?: string;
  service?: string;
}

interface ResponseType<T> {
  status: number;
  msg: string;
  data: T;
}
interface anyObj {
  [key: string]: string;
}

// 将 base64 解码为字符串 - 解决中文等乱码问题
function base64Decode(base64Str: string) {
  if (!base64Str) {
    return '{}';
  }
  const binaryString = atob(base64Str);
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }
  const decoder = new TextDecoder();
  return decoder.decode(uint8Array);
}

export const getDomain = () => {
  const env: string = getEnv();
  const hostMap: anyObj = {
    local: 'http://test-api.crushu.net:8001/sgw/api',
    test: 'http://test-api.crushu.net:8001/sgw/api',
    prod: 'http://test-api.crushu.net:8001/sgw/api'
  };
  return hostMap[env];
};

// 业务侧公共模块
const request = <T>(opt: ReqParamsType): Promise<T> => {
  const { data, xMethod, service = 'api.micro.social.revenue' } = opt;
  // todo: 判断是否端内，端内使用JSBridge访问，端外使用HTTP请求模拟
  let proxyUrl = getDomain();
  // JSBridge获取公参数据, JSBridge 文档地址: https://nemo.yuque.com/zgk9q7/uwryp9/rkz36lddo8rdvpdx?singleDoc#gSpCm
  if (window.JSBridge) {
    return new Promise((resolve, reject) => {
      const reqParams = {
        url: proxyUrl,
        service,
        method: xMethod,
        rpcType: 0,
        isOld: 0,
        param: {
          ...data // 请求参数
        }
      };
      console.log('JSBridge reqParams:', reqParams);
      JSBridge.netRequest(reqParams)
        .then((res: unknown) => {
          console.log('jsBridge.netRequest done: , res: ', res);
          if (typeof res === 'object' && res !== null) {
            const response = res as {
              status: number;
              data: { body: string; netCode: number; message: string; code: number; serverTime: number };
            };
            let resObj = {} as T;
            const { message, code, body, serverTime } = response.data;
            if (code !== 0 && message) {
              reject({ message, code });
            } else {
              try {
                const resObjStr = base64Decode(body);
                resObj = JSON.parse(resObjStr);
              } catch (error) {
                console.log(error);
              }
              resolve({ ...resObj, serverTime });
            }
          }
        })
        .catch((err: ResponseType<T>) => {
          Toast.info({ message: err.msg || ServerError, className: 'wide-toast' });
          reject(err);
        });
    });
  } else {
    if (opt.proxyUrl) {
      proxyUrl = opt.proxyUrl;
    }
    let headers: Headers = {
      // 'x-token-bin':
      //   '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
      'x-pubpara-bin': '{}',
      'x-reqcontrol-bin': JSON.stringify({
        service,
        method: xMethod
      }),
      'X-SIGN': true,
      'X-NOT-ENCODE': true
    };
    if (opt.headers) {
      headers = { ...headers, ...opt.headers };
    }
    const interfaceUrl = `${proxyUrl}?debug-uid=234929769`;
    return new Promise((resolve, reject) => {
      netRequest
        .post(
          interfaceUrl, // 业务请求URL
          {
            ...data // 请求参数
          },
          {
            'X-proxy': true,
            // 'X-showMessage': true,
            headers
          }
        )
        .then((res: ResponseType<T>) => {
          if (res.status === 1) {
            const resObj = res.data;
            console.log('http Request, data:', resObj);
            resolve(resObj);
          }
        })
        .catch((err: ResponseType<T>) => {
          // X-showMessage 设置为 true 则 可删除Toast.info()行代码
          Toast.info({ message: err.msg || ServerError, className: 'wide-toast', duration: 3000 });
          reject(err);
        });
    });
  }
};

export default request;
