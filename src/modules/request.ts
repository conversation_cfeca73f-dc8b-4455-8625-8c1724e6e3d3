import newRequest from '@fe-design/utils/net-request';
import { Toast } from 'react-vant';
import { callHandler } from './bridge';
import { getQueryParams, isAppWebviewV2, objectNumberToString } from './utils';

export const requestConfig = {
  baseURL: '/fe-api'
};

const ServerError = 'The server is a bit busy! Please try again later';

export interface BaseResp<T = any> {
  status: number;
  msg: string;
  data: T;
}

export const request = <T>(url, params = {}, options = {}) => {
  // const { proxyUrl } = window.__INITIAL_STATE__.appData?.data || { proxyUrl: '' };
  // const interfaceUrl = `${proxyUrl}${url}`;
  const { isInApp } = isAppWebviewV2();
  const interfaceUrl = url;
  return new Promise<BaseResp<T>>((resolve, reject) => {
    if (isInApp) {
      const networkRequestParams: any = {
        url: interfaceUrl,
        param: objectNumberToString(params) || {},
        ...options
      };
      if (options['X-WEBSOCKET']) {
        networkRequestParams.is_websocket = '1';
      }
      callHandler('networkRequest', networkRequestParams).then(resData => {
        console.log('+++resData:', resData);
        if (!(resData.status === 1)) {
          if (options['X-showMessage'] !== false && !options['X-multiCode']) {
            Toast.fail({
              message: resData.msg || ServerError,
              className: 'wide-toast'
            });
          }
          return reject(new Error(resData.msg || ServerError));
        }

        if (options['X-showMessage'] === true) {
          console.log(resData.msg || ServerError);
        }
        if (typeof resData.data === 'string') {
          try {
            resData.data = JSON.parse(resData.data);
          } catch (err) {
            reject(err);
          }
        }
        resolve(resData);
      });
    } else {
      const qs = getQueryParams();
      newRequest
        .get(`${interfaceUrl}`, { ...qs, ...params }, { ...requestConfig, ...options })
        .then(res => {
          resolve(res);
        })
        .catch(err => reject(err));
    }
  });
};

export const requestProxy = <T = any>(url, params = {}, options: any = {}) => {
  options.headers = {
    'X-SIGN': true,
    'X-NOT-ENCODE': true,
    ...options.headers
  };
  return request<T>(url, params, { ...options, 'X-proxy': true });
};
