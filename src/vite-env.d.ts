/// <reference types="vite/client" />
/// <reference types="vite-plugin-pages/client-react" />

declare const DEFINE_APP_TSX_PATH: string;
interface Window {
  _clog: {
    sendLog: (
      action: string,
      log_common: {
        [key: string]: string | number;
      },
      log_data: {
        [key: string]: string | number;
      }
    ) => void;
  };
  __INITIAL_STATE__: {
    appData: {
      data: {
        proxyUrl: string;
        [key: string]: any;
      };
    };
    query: Record<string, string>;
    description: string;
    env: string;
    keywords: string;
    title: string;
  };
  jsCallNative: any;
  nativeCallback: any;
  JSBridgeCallbacks: any[];
  callAppFunction: (data: { name: string; callback?: (res: any) => any; [key: string]: any }) => void;
  JSBridge?: {
    callHandler: {
      (action: string, data: Record<string, any>, callback: (response: Record<string, any>) => void): void;
      (
        action: string,
        data: Record<string, any>,
        headers: Record<string, any>,
        callback: (response: Record<string, any>) => void
      ): void;
    };
    [key: string]: any;
  };
}
