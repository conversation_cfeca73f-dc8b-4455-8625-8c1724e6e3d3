import { SpayUnit } from '../proto/protobuf/api/pay/pay';
/**
 * 存放业务层面的配置
 */
export const DEFAULT_LOCALE = 'en';
export const DEFAULT_LOCALE_KEY: string = 'locale';
export const SUPPORTED_LOCALES = ['en', 'es', 'hi', 'bd', 'bn'] as const;
export type TypedSupportedLocale = (typeof SUPPORTED_LOCALES)[number];
export const DIRECTION: Record<TypedSupportedLocale, 'ltr' | 'rtl'> = {
  en: 'ltr',
  es: 'ltr',
  hi: 'ltr',
  bd: 'ltr',
  bn: 'ltr'
};
export const DEFAULT_TIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

export const UnitMap: Partial<Record<SpayUnit, string>> = {
  [SpayUnit.SPAY_UNIT_NONE]: '',
  [SpayUnit.SPAY_UNIT_USD]: '$',
  [SpayUnit.SPAY_UNIT_PKR]: '₨',
  [SpayUnit.SPAY_UNIT_BDT]: '৳',
  [SpayUnit.SPAY_UNIT_INR]: '₹',
  [SpayUnit.SPAY_UNIT_MXN]: 'MX$',
  [SpayUnit.SPAY_UNIT_CLP]: 'CLP',
  [SpayUnit.SPAY_UNIT_AED]: 'د.إ',
  [SpayUnit.SPAY_UNIT_EGP]: '£',
  [SpayUnit.SPAY_UNIT_SAR]: 'ر.س',
  [SpayUnit.SPAY_UNIT_KWD]: 'د.ك',
  [SpayUnit.SPAY_UNIT_QAR]: 'ر.ق',
  [SpayUnit.SPAY_UNIT_BHD]: 'د.ب',
  [SpayUnit.SPAY_UNIT_JOD]: 'د.ج',
  [SpayUnit.SPAY_UNIT_OMR]: 'ر.ع',
  [SpayUnit.SPAY_UNIT_IQD]: 'د.ع'
};
