import { AppleConfigType, FirebaseConfigType } from '../components/account-login-lagecy/login/lib/constant';
export type PageProps = {
  GradientColors: readonly [string, string];
  Name: string;
  Slogan: string;
  Features: { icon: string; title: string; desc: string }[];
  Email: string;
  pkg: string;
  subanm: string;
  baseUrl: string;
  appstoreLink?: string;
  images: {
    imgBG: string;
    imgLogo: string;
    imgHero?: string;
    imgFeatures?: string;
  };
  enableDelAcc?: boolean;
  enableStoreEntry?: boolean;
};

export type AndroidPageProps = {
  firebaseConfig: FirebaseConfigType;
} & PageProps;

export type IOSPageProps = { appleConfig: AppleConfigType } & PageProps;
