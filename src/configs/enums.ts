import { omit } from 'lodash';

/**
 * key: 值
 * value: 显示 i18nkey / 文字
 */
export class EnumView<E = string> {
  private __id__: string;
  private __map__: Record<string, string>;

  // @ts-expect-error for type inference
  private __enum__: boolean;

  constructor(id: string, map: Record<string, string>) {
    this.__id__ = id;
    this.__map__ = map;
  }

  toSelectDef(t?: (key: string) => string): { text: string; value: E }[] {
    return Object.keys(this.__map__).map(value => ({
      text: this.getLabel(value as E, t),
      value: this.__enum__ ? (Number(value) as E) : (value as E)
    }));
  }

  getLabel(value?: E, t?: (key: string) => string) {
    const keyOrLabel = this.__map__[String(value)] ?? value;
    return t ? t(`${this.__id__}.${keyOrLabel}`) : keyOrLabel;
  }

  valueOf(t?: (key: string) => string) {
    return Object.keys(this.__map__).reduce(
      (obj, value) => {
        obj[value] = this.getLabel(value as E, t);
        return obj;
      },
      {} as Record<string, string>
    );
  }

  omit(...value: E[]) {
    const map = omit({ ...this.__map__ }, ...value.map(String));
    this.__map__ = map;
    return this;
  }

  private setEnum() {
    this.__enum__ = true;
    return this;
  }

  static create(id: string, map: Record<string, string>) {
    return new EnumView(id, map);
  }

  static fromEnum<E>(id: string, e: E) {
    const map = Object.values(e as Record<string, number | string>)
      .filter(v => typeof v === 'number')
      .reduce(
        (obj, key) => {
          obj[key] = e[key];
          return obj;
        },
        {} as Record<string, string>
      );
    return new EnumView<E[keyof E]>(id, map).setEnum();
  }

  static async fromRemote(id: string, fn: () => Promise<Record<string, string>>) {
    const map = await fn();
    return new EnumView(id, map);
  }

  static default() {
    return new EnumView('', {});
  }
}
