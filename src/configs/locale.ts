import i18n, { Resource } from 'i18next';
import { initReactI18next } from 'react-i18next';
import translationEn from '@/locales/en';
import translationEs from '@/locales/es';
import translationHi from '@/locales/hi';
import { DEFAULT_LOCALE, TypedSupportedLocale, SUPPORTED_LOCALES } from '@/configs';
import { getLang } from '@/modules/utils';

type Resources = {
  [k in TypedSupportedLocale]: Resource;
};

export const resources: Resources = {
  en: {
    translation: translationEn
  },
  es: {
    translation: translationEs
  },
  hi: {
    translation: translationHi
  },
  bd: {
    translation: {}
  }
};

i18n
  .use(initReactI18next) // passes i18n down to react-i18next
  .init({
    resources,
    supportedLngs: SUPPORTED_LOCALES,
    interpolation: {
      escapeValue: false
    },
    fallbackLng: DEFAULT_LOCALE,
    returnObjects: true
  });

export { i18n };

export function useTranslation(prefix?: string) {
  return i18n.getFixedT(getLang(), 'translation', prefix);
}
