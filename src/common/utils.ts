export function sizeUnit(px: number): string {
  return `${px}px`;
}

/**
 ** 乘法函数，用来得到精确的乘法结果
 ** 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
 ** 调用：accMul(arg1,arg2)
 ** 返回值：arg1乘以 arg2的精确结果
 **/
export const accMul = (arg1: number, arg2: number) => {
  let m = 0;
  const s1 = arg1.toString(),
    s2 = arg2.toString();
  m += s1?.split?.('.')?.[1]?.length || 0;
  m += s2?.split('.')?.[1]?.length || 0;
  return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m);
};

export const formatNum = ({
  value,
  withSign = false,
  shortened = true,
  fixed = 100,
  fillZero = false // 小数点不足时是否补0，toFixed
}: {
  value: number;
  withSign?: boolean;
  shortened?: boolean;
  fixed?: number;
  fillZero?: boolean;
}): string => {
  const number = Number(value);
  const obj: Record<string, number> = {
    K: 1000,
    M: 1000000,
    B: 1000000000
  };
  let unit = '';
  let resultNum = number;
  let prefix = '';
  for (const key in obj) {
    const val = accMul(number / obj[key], fixed);
    if (number >= obj[key]) {
      unit = key;
      resultNum = Math.floor(val) / fixed;
    }
  }
  if (withSign && resultNum > 0) {
    prefix = '+';
  }
  if (!shortened) {
    return `${prefix}${fillZero ? value.toFixed(Math.log10(fixed)) : value}`;
  } else {
    return `${prefix}${fillZero ? resultNum.toFixed(Math.log10(fixed)) : resultNum}${unit}`;
  }
};
