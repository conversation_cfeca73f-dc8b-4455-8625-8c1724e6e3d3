import { getEnv } from '@/modules/utils';
import { useEffect, useState } from 'react';
import { useJsbridgeReady } from '.';
import { getLoginUser } from '../modules/bridge';

export const useUserInfo = () => {
  const [userInfo, setUserInfo] = useState<{ [key: string]: any }>();
  const isReady = useJsbridgeReady();

  useEffect(() => {
    if (!isReady) {
      return;
    }
    if ((getEnv() === 'local' && !window.JSBridge) || location.search.includes('df-02')) {
      const query = Object.fromEntries(new URLSearchParams(location.search).entries());
      setUserInfo(query);
      return;
    }
    getLoginUser().then(res => {
      setUserInfo(res);
    });
  }, [isReady]);

  return userInfo;
};
