import { getEnv, wait } from '@/modules/utils';
import { useState, useEffect } from 'react';

export const useJsbridgeReady = () => {
  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    const checkReady = () => {
      if (window?.JSBridge?.callHandler) {
        setIsReady(true);
      } else {
        wait(300).then(checkReady);
      }
    };
    if (getEnv() === 'local') {
      setIsReady(true);
    } else {
      checkReady();
    }
  }, []);

  return isReady;
};
