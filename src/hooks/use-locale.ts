import { useTranslation } from '@/configs/locale';
import i18n, { Resource } from 'i18next';
import { initReactI18next } from 'react-i18next';
import { DEFAULT_LOCALE, SUPPORTED_LOCALES } from '@/configs';
import { useState } from 'react';
import { getLang } from '@/modules/utils';

export const useLocale = (resources: Resource, prefix?: string) => {
  const [i18n] = useState(() => createLocale(resources));
  return i18n.getFixedT(getLang(), 'translation', prefix);
};

const createLocale = (resources: Resource) => {
  i18n
    .use(initReactI18next) // passes i18n down to react-i18next
    .init({
      resources: Object.fromEntries(Object.entries(resources).map(([key, value]) => [key, { translation: value }])),
      supportedLngs: SUPPORTED_LOCALES,
      fallbackLng: DEFAULT_LOCALE,
      returnObjects: true
    });

  return i18n;
};
