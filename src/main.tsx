import { useEffect } from 'react';
import ReactDOM from 'react-dom/client';
import {
  BrowserRouter as Router,
  createRoutesFromChildren,
  matchRoutes,
  useLocation,
  useNavigationType
} from 'react-router-dom';
import * as Sentry from '@sentry/react';
import './index.css';
import './assets/styles/utils.css';
import 'virtual:uno.css';
import '@unocss/reset/tailwind.css';

// vconsole start
if (import.meta.env.MODE !== 'production' || window.location.search.includes('vc=')) {
  import('vconsole').then(({ default: VConsole }) => {
    new VConsole();
  });
}
// vconsole end

// sentry start
const metaTag = document.querySelector('meta[name="sentry"]');
const dsn = metaTag ? metaTag.getAttribute('content') : null;
if (dsn) {
  const dsnArr = dsn.split('?');
  const params: { [key: string]: string } = {};
  if (dsnArr[1]) {
    const paramPairs = dsnArr[1].split('&');
    for (let i = 0; i < paramPairs.length; i++) {
      const pair = paramPairs[i].split('=');
      params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || '');
    }
  }
  Sentry.init({
    dsn: dsnArr[0],
    integrations: [
      Sentry.reactRouterV6BrowserTracingIntegration({
        useEffect,
        useLocation,
        useNavigationType,
        createRoutesFromChildren,
        matchRoutes
      }),
      Sentry.replayIntegration()
    ],
    // Performance Monitoring
    tracesSampleRate: Number(params.tsr) || 0.05, // Capture 100% of the transactions
    // Session Replay
    replaysSessionSampleRate: Number(params.rssr) || 0.05, // This sets the sample rate at 10%. You may want to change it to 100% while in development and then sample at a lower rate in production.
    replaysOnErrorSampleRate: 1.0 // If you're not already sampling the entire session, change the sample rate to 100% when sampling sessions where errors occur.
  });
}
// sentry end

// dynamic basename start
const langMetaTag = document.querySelector('meta[name="router-lang"]');
const basenameMetaTag = document.querySelector('meta[name="router-basename"]');
const basename = `${langMetaTag?.getAttribute('content') || ''}${basenameMetaTag?.getAttribute('content') || '/'}`;
// dynamic basename end

// 初始化应用
initApp();

async function initApp() {
  const importApp = await import(/* @vite-ignore */ DEFINE_APP_TSX_PATH);
  const App = importApp.default;

  ReactDOM.createRoot(document.getElementById('app')!).render(
    <Router basename={basename}>
      <App />
    </Router>
  );
}
