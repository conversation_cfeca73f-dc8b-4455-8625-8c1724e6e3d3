// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: mgr/room_mgr.proto

/* eslint-disable */
import {
  LiveStatus,
  liveStatusFromJSON,
  RoomInfo,
  RoomSettings,
  RoomStatus,
  roomStatusFromJSON,
  RoomTag,
  RoomType,
  roomTypeFromJSON
} from '../api/comm';
import { UserInfo } from '../api/user';
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'mgrroom';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/room/handlermgr */

/** 房间 */
export interface Room {
  /** 房间信息 */
  room: RoomInfo | undefined;
  /** 房间设置 */
  settings: RoomSettings | undefined;
  /** 房主信息 */
  owner: UserInfo | undefined;
  /** 房间管理员 */
  admins: UserInfo[];
  /** 房间标签 */
  tags: RoomTag[];
  /** 房间模式 */
  room_mode: string;
  /** 权重 */
  sort_weight: number;
}

/** 搜索房间列表 */
export interface SearchRoomReq {
  page: Page | undefined;
  /** 房间ID */
  room_id: number;
  /** 房主UID */
  owner_uid: number;
  /** 开播状态 */
  live_status: LiveStatus;
  /** 房间类型 */
  room_type: RoomType;
  /** 房间封禁状态 */
  room_status: RoomStatus;
}

export interface SearchRoomRsp {
  page: Page | undefined;
  /** 房间列表 */
  data: Room[];
}

/** 更新房间信息 */
export interface UpdateRoomReq {
  room: Room | undefined;
}

export interface UpdateRoomRsp {}

/** 设置房间类型 */
export interface UpdateRoomTypeReq {
  /** 房间ID列表 */
  room_ids: number[];
  /** 房间类型 */
  room_type: RoomType;
}

export interface UpdateRoomTypeRsp {}

/** 设置开播状态 */
export interface UpdateLiveStatusReq {
  /** 房间ID列表 */
  room_ids: number[];
  /** 开播状态 */
  live_status: LiveStatus;
}

export interface UpdateLiveStatusRsp {}

export interface BanRoomReq {
  /** 房间ID */
  room_id: number;
  /** 封禁原因 */
  reason: string;
  /** 封禁时长，单位秒 */
  ban_duration: number;
}

export interface BanRoomRsp {}

export interface UnbanRoomReq {
  /** 房间ID */
  room_id: number;
  /** 解封原因 */
  reason: string;
}

export interface UnbanRoomRsp {}

function createBaseRoom(): Room {
  return {
    room: undefined,
    settings: undefined,
    owner: undefined,
    admins: [],
    tags: [],
    room_mode: '',
    sort_weight: 0
  };
}

export const Room: MessageFns<Room> = {
  fromJSON(object: any): Room {
    return {
      room: isSet(object.room) ? RoomInfo.fromJSON(object.room) : undefined,
      settings: isSet(object.settings) ? RoomSettings.fromJSON(object.settings) : undefined,
      owner: isSet(object.owner) ? UserInfo.fromJSON(object.owner) : undefined,
      admins: globalThis.Array.isArray(object?.admins) ? object.admins.map((e: any) => UserInfo.fromJSON(e)) : [],
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => RoomTag.fromJSON(e)) : [],
      room_mode: isSet(object.room_mode) ? globalThis.String(object.room_mode) : '',
      sort_weight: isSet(object.sort_weight) ? globalThis.Number(object.sort_weight) : 0
    };
  },

  create<I extends Exact<DeepPartial<Room>, I>>(base?: I): Room {
    return Room.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Room>, I>>(object: I): Room {
    const message = createBaseRoom();
    message.room = object.room !== undefined && object.room !== null ? RoomInfo.fromPartial(object.room) : undefined;
    message.settings =
      object.settings !== undefined && object.settings !== null ? RoomSettings.fromPartial(object.settings) : undefined;
    message.owner =
      object.owner !== undefined && object.owner !== null ? UserInfo.fromPartial(object.owner) : undefined;
    message.admins = object.admins?.map(e => UserInfo.fromPartial(e)) || [];
    message.tags = object.tags?.map(e => RoomTag.fromPartial(e)) || [];
    message.room_mode = object.room_mode ?? '';
    message.sort_weight = object.sort_weight ?? 0;
    return message;
  }
};

function createBaseSearchRoomReq(): SearchRoomReq {
  return { page: undefined, room_id: 0, owner_uid: 0, live_status: 0, room_type: 0, room_status: 0 };
}

export const SearchRoomReq: MessageFns<SearchRoomReq> = {
  fromJSON(object: any): SearchRoomReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      live_status: isSet(object.live_status) ? liveStatusFromJSON(object.live_status) : 0,
      room_type: isSet(object.room_type) ? roomTypeFromJSON(object.room_type) : 0,
      room_status: isSet(object.room_status) ? roomStatusFromJSON(object.room_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomReq>, I>>(base?: I): SearchRoomReq {
    return SearchRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomReq>, I>>(object: I): SearchRoomReq {
    const message = createBaseSearchRoomReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    message.owner_uid = object.owner_uid ?? 0;
    message.live_status = object.live_status ?? 0;
    message.room_type = object.room_type ?? 0;
    message.room_status = object.room_status ?? 0;
    return message;
  }
};

function createBaseSearchRoomRsp(): SearchRoomRsp {
  return { page: undefined, data: [] };
}

export const SearchRoomRsp: MessageFns<SearchRoomRsp> = {
  fromJSON(object: any): SearchRoomRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => Room.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomRsp>, I>>(base?: I): SearchRoomRsp {
    return SearchRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomRsp>, I>>(object: I): SearchRoomRsp {
    const message = createBaseSearchRoomRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => Room.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUpdateRoomReq(): UpdateRoomReq {
  return { room: undefined };
}

export const UpdateRoomReq: MessageFns<UpdateRoomReq> = {
  fromJSON(object: any): UpdateRoomReq {
    return { room: isSet(object.room) ? Room.fromJSON(object.room) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateRoomReq>, I>>(base?: I): UpdateRoomReq {
    return UpdateRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomReq>, I>>(object: I): UpdateRoomReq {
    const message = createBaseUpdateRoomReq();
    message.room = object.room !== undefined && object.room !== null ? Room.fromPartial(object.room) : undefined;
    return message;
  }
};

function createBaseUpdateRoomRsp(): UpdateRoomRsp {
  return {};
}

export const UpdateRoomRsp: MessageFns<UpdateRoomRsp> = {
  fromJSON(_: any): UpdateRoomRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomRsp>, I>>(base?: I): UpdateRoomRsp {
    return UpdateRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomRsp>, I>>(_: I): UpdateRoomRsp {
    const message = createBaseUpdateRoomRsp();
    return message;
  }
};

function createBaseUpdateRoomTypeReq(): UpdateRoomTypeReq {
  return { room_ids: [], room_type: 0 };
}

export const UpdateRoomTypeReq: MessageFns<UpdateRoomTypeReq> = {
  fromJSON(object: any): UpdateRoomTypeReq {
    return {
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : [],
      room_type: isSet(object.room_type) ? roomTypeFromJSON(object.room_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomTypeReq>, I>>(base?: I): UpdateRoomTypeReq {
    return UpdateRoomTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTypeReq>, I>>(object: I): UpdateRoomTypeReq {
    const message = createBaseUpdateRoomTypeReq();
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.room_type = object.room_type ?? 0;
    return message;
  }
};

function createBaseUpdateRoomTypeRsp(): UpdateRoomTypeRsp {
  return {};
}

export const UpdateRoomTypeRsp: MessageFns<UpdateRoomTypeRsp> = {
  fromJSON(_: any): UpdateRoomTypeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomTypeRsp>, I>>(base?: I): UpdateRoomTypeRsp {
    return UpdateRoomTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTypeRsp>, I>>(_: I): UpdateRoomTypeRsp {
    const message = createBaseUpdateRoomTypeRsp();
    return message;
  }
};

function createBaseUpdateLiveStatusReq(): UpdateLiveStatusReq {
  return { room_ids: [], live_status: 0 };
}

export const UpdateLiveStatusReq: MessageFns<UpdateLiveStatusReq> = {
  fromJSON(object: any): UpdateLiveStatusReq {
    return {
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : [],
      live_status: isSet(object.live_status) ? liveStatusFromJSON(object.live_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateLiveStatusReq>, I>>(base?: I): UpdateLiveStatusReq {
    return UpdateLiveStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateLiveStatusReq>, I>>(object: I): UpdateLiveStatusReq {
    const message = createBaseUpdateLiveStatusReq();
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.live_status = object.live_status ?? 0;
    return message;
  }
};

function createBaseUpdateLiveStatusRsp(): UpdateLiveStatusRsp {
  return {};
}

export const UpdateLiveStatusRsp: MessageFns<UpdateLiveStatusRsp> = {
  fromJSON(_: any): UpdateLiveStatusRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateLiveStatusRsp>, I>>(base?: I): UpdateLiveStatusRsp {
    return UpdateLiveStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateLiveStatusRsp>, I>>(_: I): UpdateLiveStatusRsp {
    const message = createBaseUpdateLiveStatusRsp();
    return message;
  }
};

function createBaseBanRoomReq(): BanRoomReq {
  return { room_id: 0, reason: '', ban_duration: 0 };
}

export const BanRoomReq: MessageFns<BanRoomReq> = {
  fromJSON(object: any): BanRoomReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      ban_duration: isSet(object.ban_duration) ? globalThis.Number(object.ban_duration) : 0
    };
  },

  create<I extends Exact<DeepPartial<BanRoomReq>, I>>(base?: I): BanRoomReq {
    return BanRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanRoomReq>, I>>(object: I): BanRoomReq {
    const message = createBaseBanRoomReq();
    message.room_id = object.room_id ?? 0;
    message.reason = object.reason ?? '';
    message.ban_duration = object.ban_duration ?? 0;
    return message;
  }
};

function createBaseBanRoomRsp(): BanRoomRsp {
  return {};
}

export const BanRoomRsp: MessageFns<BanRoomRsp> = {
  fromJSON(_: any): BanRoomRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BanRoomRsp>, I>>(base?: I): BanRoomRsp {
    return BanRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanRoomRsp>, I>>(_: I): BanRoomRsp {
    const message = createBaseBanRoomRsp();
    return message;
  }
};

function createBaseUnbanRoomReq(): UnbanRoomReq {
  return { room_id: 0, reason: '' };
}

export const UnbanRoomReq: MessageFns<UnbanRoomReq> = {
  fromJSON(object: any): UnbanRoomReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : ''
    };
  },

  create<I extends Exact<DeepPartial<UnbanRoomReq>, I>>(base?: I): UnbanRoomReq {
    return UnbanRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnbanRoomReq>, I>>(object: I): UnbanRoomReq {
    const message = createBaseUnbanRoomReq();
    message.room_id = object.room_id ?? 0;
    message.reason = object.reason ?? '';
    return message;
  }
};

function createBaseUnbanRoomRsp(): UnbanRoomRsp {
  return {};
}

export const UnbanRoomRsp: MessageFns<UnbanRoomRsp> = {
  fromJSON(_: any): UnbanRoomRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnbanRoomRsp>, I>>(base?: I): UnbanRoomRsp {
    return UnbanRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnbanRoomRsp>, I>>(_: I): UnbanRoomRsp {
    const message = createBaseUnbanRoomRsp();
    return message;
  }
};

export type RoomMgrDefinition = typeof RoomMgrDefinition;
export const RoomMgrDefinition = {
  name: 'RoomMgr',
  fullName: 'mgrroom.RoomMgr',
  methods: {
    /** 搜索房间列表 */
    searchRoom: {
      name: 'SearchRoom',
      requestType: SearchRoomReq,
      requestStream: false,
      responseType: SearchRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 更新房间信息 */
    updateRoom: {
      name: 'UpdateRoom',
      requestType: UpdateRoomReq,
      requestStream: false,
      responseType: UpdateRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 设置房间类型(设为官方/移除官方/批量新增官方) */
    updateRoomType: {
      name: 'UpdateRoomType',
      requestType: UpdateRoomTypeReq,
      requestStream: false,
      responseType: UpdateRoomTypeRsp,
      responseStream: false,
      options: {}
    },
    /** 设置开播状态(支持批量) */
    updateLiveStatus: {
      name: 'UpdateLiveStatus',
      requestType: UpdateLiveStatusReq,
      requestStream: false,
      responseType: UpdateLiveStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 封禁房间 */
    banRoom: {
      name: 'BanRoom',
      requestType: BanRoomReq,
      requestStream: false,
      responseType: BanRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 解封房间 */
    unbanRoom: {
      name: 'UnbanRoom',
      requestType: UnbanRoomReq,
      requestStream: false,
      responseType: UnbanRoomRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
