// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: mgr/room_mode_mgr.proto

/* eslint-disable */
import { I18n, RoomModePublishDefaultSelected, roomModePublishDefaultSelectedFromJSON } from '../api/comm';
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'mgrroommode';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/roommode/handlermgr */

/** 房间模式分类-有效状态 */
export enum RoomModeCategoryActiveStatus {
  ROOM_MODE_CATEGORY_ACTIVE_STATUS_NONE = 0,
  /** ROOM_MODE_CATEGORY_ACTIVE_STATUS_ENABLE - 启用 */
  ROOM_MODE_CATEGORY_ACTIVE_STATUS_ENABLE = 1,
  /** ROOM_MODE_CATEGORY_ACTIVE_STATUS_DISABLE - 禁用 */
  ROOM_MODE_CATEGORY_ACTIVE_STATUS_DISABLE = 2,
  UNRECOGNIZED = -1
}

export function roomModeCategoryActiveStatusFromJSON(object: any): RoomModeCategoryActiveStatus {
  switch (object) {
    case 0:
    case 'ROOM_MODE_CATEGORY_ACTIVE_STATUS_NONE':
      return RoomModeCategoryActiveStatus.ROOM_MODE_CATEGORY_ACTIVE_STATUS_NONE;
    case 1:
    case 'ROOM_MODE_CATEGORY_ACTIVE_STATUS_ENABLE':
      return RoomModeCategoryActiveStatus.ROOM_MODE_CATEGORY_ACTIVE_STATUS_ENABLE;
    case 2:
    case 'ROOM_MODE_CATEGORY_ACTIVE_STATUS_DISABLE':
      return RoomModeCategoryActiveStatus.ROOM_MODE_CATEGORY_ACTIVE_STATUS_DISABLE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModeCategoryActiveStatus.UNRECOGNIZED;
  }
}

/** 房间模式分类-状态 */
export enum RoomModeCategoryStatus {
  ROOM_MODE_CATEGORY_STATUS_NONE = 0,
  /** ROOM_MODE_CATEGORY_STATUS_NORMAL - 正常 */
  ROOM_MODE_CATEGORY_STATUS_NORMAL = 1,
  /** ROOM_MODE_CATEGORY_STATUS_DELETED - 已删除 */
  ROOM_MODE_CATEGORY_STATUS_DELETED = 2,
  UNRECOGNIZED = -1
}

export function roomModeCategoryStatusFromJSON(object: any): RoomModeCategoryStatus {
  switch (object) {
    case 0:
    case 'ROOM_MODE_CATEGORY_STATUS_NONE':
      return RoomModeCategoryStatus.ROOM_MODE_CATEGORY_STATUS_NONE;
    case 1:
    case 'ROOM_MODE_CATEGORY_STATUS_NORMAL':
      return RoomModeCategoryStatus.ROOM_MODE_CATEGORY_STATUS_NORMAL;
    case 2:
    case 'ROOM_MODE_CATEGORY_STATUS_DELETED':
      return RoomModeCategoryStatus.ROOM_MODE_CATEGORY_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModeCategoryStatus.UNRECOGNIZED;
  }
}

/** 房间模式配置-状态 */
export enum RoomModeConfigStatus {
  ROOM_MODE_CONFIG_STATUS_NONE = 0,
  /** ROOM_MODE_CONFIG_STATUS_NORMAL - 正常 */
  ROOM_MODE_CONFIG_STATUS_NORMAL = 1,
  /** ROOM_MODE_CONFIG_STATUS_DELETED - 已删除 */
  ROOM_MODE_CONFIG_STATUS_DELETED = 2,
  UNRECOGNIZED = -1
}

export function roomModeConfigStatusFromJSON(object: any): RoomModeConfigStatus {
  switch (object) {
    case 0:
    case 'ROOM_MODE_CONFIG_STATUS_NONE':
      return RoomModeConfigStatus.ROOM_MODE_CONFIG_STATUS_NONE;
    case 1:
    case 'ROOM_MODE_CONFIG_STATUS_NORMAL':
      return RoomModeConfigStatus.ROOM_MODE_CONFIG_STATUS_NORMAL;
    case 2:
    case 'ROOM_MODE_CONFIG_STATUS_DELETED':
      return RoomModeConfigStatus.ROOM_MODE_CONFIG_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModeConfigStatus.UNRECOGNIZED;
  }
}

/** 房间模式上架状态枚举 */
export enum RoomModePublishStatus {
  ROOM_MODE_PUBLISH_STATUS_NONE = 0,
  /** ROOM_MODE_PUBLISH_STATUS_UNPUBLISHED - 未上架 */
  ROOM_MODE_PUBLISH_STATUS_UNPUBLISHED = 1,
  /** ROOM_MODE_PUBLISH_STATUS_PUBLISHED - 已上架 */
  ROOM_MODE_PUBLISH_STATUS_PUBLISHED = 2,
  /** ROOM_MODE_PUBLISH_STATUS_PENDING - 待生效 */
  ROOM_MODE_PUBLISH_STATUS_PENDING = 3,
  /** ROOM_MODE_PUBLISH_STATUS_DELETED - 已删除 */
  ROOM_MODE_PUBLISH_STATUS_DELETED = 4,
  UNRECOGNIZED = -1
}

export function roomModePublishStatusFromJSON(object: any): RoomModePublishStatus {
  switch (object) {
    case 0:
    case 'ROOM_MODE_PUBLISH_STATUS_NONE':
      return RoomModePublishStatus.ROOM_MODE_PUBLISH_STATUS_NONE;
    case 1:
    case 'ROOM_MODE_PUBLISH_STATUS_UNPUBLISHED':
      return RoomModePublishStatus.ROOM_MODE_PUBLISH_STATUS_UNPUBLISHED;
    case 2:
    case 'ROOM_MODE_PUBLISH_STATUS_PUBLISHED':
      return RoomModePublishStatus.ROOM_MODE_PUBLISH_STATUS_PUBLISHED;
    case 3:
    case 'ROOM_MODE_PUBLISH_STATUS_PENDING':
      return RoomModePublishStatus.ROOM_MODE_PUBLISH_STATUS_PENDING;
    case 4:
    case 'ROOM_MODE_PUBLISH_STATUS_DELETED':
      return RoomModePublishStatus.ROOM_MODE_PUBLISH_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModePublishStatus.UNRECOGNIZED;
  }
}

/** 房间模式上架-有效状态 */
export enum RoomModePublishActiveStatus {
  ROOM_MODE_PUBLISH_ACTIVE_STATUS_NONE = 0,
  /** ROOM_MODE_PUBLISH_ACTIVE_STATUS_ENABLE - 启用 */
  ROOM_MODE_PUBLISH_ACTIVE_STATUS_ENABLE = 1,
  /** ROOM_MODE_PUBLISH_ACTIVE_STATUS_DISABLE - 禁用 */
  ROOM_MODE_PUBLISH_ACTIVE_STATUS_DISABLE = 2,
  UNRECOGNIZED = -1
}

export function roomModePublishActiveStatusFromJSON(object: any): RoomModePublishActiveStatus {
  switch (object) {
    case 0:
    case 'ROOM_MODE_PUBLISH_ACTIVE_STATUS_NONE':
      return RoomModePublishActiveStatus.ROOM_MODE_PUBLISH_ACTIVE_STATUS_NONE;
    case 1:
    case 'ROOM_MODE_PUBLISH_ACTIVE_STATUS_ENABLE':
      return RoomModePublishActiveStatus.ROOM_MODE_PUBLISH_ACTIVE_STATUS_ENABLE;
    case 2:
    case 'ROOM_MODE_PUBLISH_ACTIVE_STATUS_DISABLE':
      return RoomModePublishActiveStatus.ROOM_MODE_PUBLISH_ACTIVE_STATUS_DISABLE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModePublishActiveStatus.UNRECOGNIZED;
  }
}

export interface RoomModeCategory {
  /** 房间模式分类id */
  id: number;
  /** 房间模式分类名称(缺省名称) */
  name: string;
  /** 房间模式分类名称-多语言 */
  name_i18n: I18n[];
  /** 排序 */
  position: number;
  /** 有效状态 */
  active_status: RoomModeCategoryActiveStatus;
  /** 创建时间戳 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 更新时间戳 */
  updated_at: number;
  /** 更新人 */
  updater: string;
  /** 删除时间(0:未删除,>0:已删除) */
  deleted_at: number;
}

export interface ListRoomModeCategoryReq {
  page: Page | undefined;
  /** 状态 */
  status: RoomModeCategoryStatus;
}

export interface ListRoomModeCategoryRsp {
  page: Page | undefined;
  room_mode_categories: RoomModeCategory[];
}

export interface CreateRoomModeCategoryReq {
  /** 房间模式分类名称(缺省名称) */
  name: string;
  /** 房间模式分类名称-多语言 */
  name_i18n: I18n[];
  /** 排序 */
  position: number;
  /** 有效状态 */
  active_status: RoomModeCategoryActiveStatus;
}

export interface CreateRoomModeCategoryRsp {
  /** 房间模式分类id */
  id: number;
}

export interface UpdateRoomModeCategoryReq {
  /** 房间模式分类id */
  id: number;
  /** 房间模式分类名称(缺省名称) */
  name: string;
  /** 房间模式分类名称-多语言 */
  name_i18n: I18n[];
  /** 排序 */
  position: number;
}

export interface UpdateRoomModeCategoryRsp {}

export interface DeleteRoomModeCategoryReq {
  /** 房间模式分类id */
  id: number;
}

export interface DeleteRoomModeCategoryRsp {}

export interface SetRoomModeCategoryActiveStatusReq {
  /** 房间模式分类id */
  id: number;
  /** 有效状态 */
  active_status: RoomModeCategoryActiveStatus;
}

export interface SetRoomModeCategoryActiveStatusRsp {}

/** 房间模式配置 */
export interface RoomModeConfig {
  /** 房间模式配置id */
  id: number;
  /** 名称(缺省名称) */
  name: string;
  /** 名称-多语言 */
  name_i18n: I18n[];
  /** 图标(缺省图标) */
  icon: string;
  /** 图标-多语言 */
  icon_i18n: I18n[];
  /** 房间背景图地址 */
  room_bg_img_url: string;
  /** 房间背景图类型 */
  room_bg_img_type: string;
  /** 玩法地址 */
  play_url: string;
  /** 房间模式code */
  room_mode_code: string;
  /** 麦位布局code */
  room_layout_code: string;
  /** 玩法code;eg:chat、ludo */
  play_code: string;
  /** 创建时间戳 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 更新时间戳 */
  updated_at: number;
  /** 更新人 */
  updater: string;
  /** 删除时间(0:未删除,>0:已删除) */
  deleted_at: number;
}

export interface ListRoomModeConfigReq {
  page: Page | undefined;
  /** 房间模式配置id */
  id: number;
  /** 名称 */
  name: string;
  /** 更新人 */
  updater: string;
  /** 更新时间戳 */
  updated_at_start: number;
  /** 更新时间戳 */
  updated_at_end: number;
  status: RoomModeConfigStatus;
}

export interface ListRoomModeConfigRsp {
  page: Page | undefined;
  room_mode_configs: RoomModeConfig[];
}

export interface CreateRoomModeConfigReq {
  /** 名称(缺省名称) */
  name: string;
  /** 名称-多语言 */
  name_i18n: I18n[];
  /** 图标(缺省图标) */
  icon: string;
  /** 图标-多语言 */
  icon_i18n: I18n[];
  /** 房间背景图地址 */
  room_bg_img_url: string;
  /** 房间背景图类型 ??? TODO 后缀识别 */
  room_bg_img_type: string;
  /** 玩法地址 */
  play_url: string;
  /** 房间模式code */
  room_mode_code: string;
  /** 麦位布局code */
  room_layout_code: string;
  /** 玩法code;eg:chat、ludo */
  play_code: string;
}

export interface CreateRoomModeConfigRsp {
  /** 房间模式配置id */
  id: number;
}

export interface UpdateRoomModeConfigReq {
  /** 房间模式配置id */
  id: number;
  /** 名称(缺省名称) */
  name: string;
  /** 名称-多语言 */
  name_i18n: I18n[];
  /** 图标(缺省图标) */
  icon: string;
  /** 图标-多语言 */
  icon_i18n: I18n[];
  /** 房间背景图地址 */
  room_bg_img_url: string;
  /** 房间背景图类型 ??? TODO 后缀识别 */
  room_bg_img_type: string;
  /** 玩法地址 */
  play_url: string;
  /** 房间模式code */
  room_mode_code: string;
  /** 麦位布局code */
  room_layout_code: string;
  /** 玩法code;eg:chat、ludo */
  play_code: string;
}

export interface UpdateRoomModeConfigRsp {}

export interface DeleteRoomModeConfigReq {
  /** 房间模式配置id */
  id: number;
}

export interface DeleteRoomModeConfigRsp {}

export interface RoomModePublish {
  /** 上架id */
  id: number;
  /** 房间模式配置 */
  room_mode_config: RoomModeConfig | undefined;
  /** 房间模式分类 */
  room_mode_category: RoomModeCategory | undefined;
  /** 排序 */
  position: number;
  /** 默认选中 */
  default_selected: RoomModePublishDefaultSelected;
  /** 有效状态 */
  active_status: RoomModePublishActiveStatus;
  /** 上架状态 */
  publish_status: RoomModePublishStatus;
  /** 生效-房间白名单 */
  room_id_whitelist: number[];
  /** 生效-开始时间 */
  valid_time_start: number;
  /** 创建时间戳 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 更新时间戳 */
  updated_at: number;
  /** 更新人 */
  updater: string;
  /** 删除时间(0:未删除,>0:已删除) */
  deleted_at: number;
}

export interface ListRoomModePublishReq {
  page: Page | undefined;
  /** 房间模式配置id */
  room_mode_config_id: number;
  /** 房间模式分类id */
  room_mode_category_id: number;
  /** 上架状态 */
  publish_statuses: RoomModePublishStatus[];
}

export interface ListRoomModePublishRsp {
  page: Page | undefined;
  room_mode_publishes: RoomModePublish[];
}

export interface CreateRoomModePublishReq {
  /** 房间模式配置id */
  room_mode_config_id: number;
  /** 房间模式分类id */
  room_mode_category_id: number;
  /** 排序 */
  position: number;
  /** 默认选中 */
  default_selected: RoomModePublishDefaultSelected;
  /** 生效-房间白名单 */
  room_id_whitelist: number[];
  /** 生效-开始时间 */
  valid_time_start: number;
  /** 有效状态 */
  active_status: RoomModePublishActiveStatus;
}

export interface CreateRoomModePublishRsp {
  /** 上架id */
  id: number;
}

export interface UpdateRoomModePublishReq {
  /** 上架id */
  id: number;
  /** 房间模式配置id */
  room_mode_config_id: number;
  /** 房间模式分类id */
  room_mode_category_id: number;
  /** 排序 */
  position: number;
  /** 默认选中 */
  default_selected: RoomModePublishDefaultSelected;
  /** 生效-房间白名单 */
  room_id_whitelist: number[];
  /** 生效-开始时间 */
  valid_time_start: number;
}

export interface UpdateRoomModePublishRsp {}

export interface DeleteRoomModePublishReq {
  /** 上架id */
  id: number;
}

export interface DeleteRoomModePublishRsp {}

export interface SetRoomModePublishActiveStatusReq {
  /** 上架id */
  id: number;
  /** 有效状态 */
  active_status: RoomModePublishActiveStatus;
}

export interface SetRoomModePublishActiveStatusRsp {}

/** 房间模式code */
export interface RoomModeCode {
  /** code标识 */
  code: string;
  /** 名称 */
  name: string;
}

export interface ListRoomModeCodeReq {}

export interface ListRoomModeCodeRsp {
  room_mode_codes: RoomModeCode[];
}

/** 房间布局code */
export interface RoomLayoutCode {
  /** code标识 */
  code: string;
  /** 名称 */
  name: string;
}

export interface ListRoomLayoutCodeReq {
  /** 房间布局code */
  room_mode_code: string;
}

export interface ListRoomLayoutCodeRsp {
  room_layout_codes: RoomLayoutCode[];
}

/** 房间玩法code */
export interface RoomPlayCode {
  /** code标识 */
  code: string;
  /** 名称 */
  name: string;
}

export interface ListRoomPlayCodeReq {
  /** 房间布局code */
  room_mode_code: string;
  /** 房间玩法code */
  room_layout_code: string;
}

export interface ListRoomPlayCodeRsp {
  room_play_codes: RoomPlayCode[];
}

function createBaseRoomModeCategory(): RoomModeCategory {
  return {
    id: 0,
    name: '',
    name_i18n: [],
    position: 0,
    active_status: 0,
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: '',
    deleted_at: 0
  };
}

export const RoomModeCategory: MessageFns<RoomModeCategory> = {
  fromJSON(object: any): RoomModeCategory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
      active_status: isSet(object.active_status) ? roomModeCategoryActiveStatusFromJSON(object.active_status) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      deleted_at: isSet(object.deleted_at) ? globalThis.Number(object.deleted_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomModeCategory>, I>>(base?: I): RoomModeCategory {
    return RoomModeCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeCategory>, I>>(object: I): RoomModeCategory {
    const message = createBaseRoomModeCategory();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.position = object.position ?? 0;
    message.active_status = object.active_status ?? 0;
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    message.deleted_at = object.deleted_at ?? 0;
    return message;
  }
};

function createBaseListRoomModeCategoryReq(): ListRoomModeCategoryReq {
  return { page: undefined, status: 0 };
}

export const ListRoomModeCategoryReq: MessageFns<ListRoomModeCategoryReq> = {
  fromJSON(object: any): ListRoomModeCategoryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      status: isSet(object.status) ? roomModeCategoryStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModeCategoryReq>, I>>(base?: I): ListRoomModeCategoryReq {
    return ListRoomModeCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeCategoryReq>, I>>(object: I): ListRoomModeCategoryReq {
    const message = createBaseListRoomModeCategoryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseListRoomModeCategoryRsp(): ListRoomModeCategoryRsp {
  return { page: undefined, room_mode_categories: [] };
}

export const ListRoomModeCategoryRsp: MessageFns<ListRoomModeCategoryRsp> = {
  fromJSON(object: any): ListRoomModeCategoryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_mode_categories: globalThis.Array.isArray(object?.room_mode_categories)
        ? object.room_mode_categories.map((e: any) => RoomModeCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModeCategoryRsp>, I>>(base?: I): ListRoomModeCategoryRsp {
    return ListRoomModeCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeCategoryRsp>, I>>(object: I): ListRoomModeCategoryRsp {
    const message = createBaseListRoomModeCategoryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_mode_categories = object.room_mode_categories?.map(e => RoomModeCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomModeCategoryReq(): CreateRoomModeCategoryReq {
  return { name: '', name_i18n: [], position: 0, active_status: 0 };
}

export const CreateRoomModeCategoryReq: MessageFns<CreateRoomModeCategoryReq> = {
  fromJSON(object: any): CreateRoomModeCategoryReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
      active_status: isSet(object.active_status) ? roomModeCategoryActiveStatusFromJSON(object.active_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<CreateRoomModeCategoryReq>, I>>(base?: I): CreateRoomModeCategoryReq {
    return CreateRoomModeCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModeCategoryReq>, I>>(object: I): CreateRoomModeCategoryReq {
    const message = createBaseCreateRoomModeCategoryReq();
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.position = object.position ?? 0;
    message.active_status = object.active_status ?? 0;
    return message;
  }
};

function createBaseCreateRoomModeCategoryRsp(): CreateRoomModeCategoryRsp {
  return { id: 0 };
}

export const CreateRoomModeCategoryRsp: MessageFns<CreateRoomModeCategoryRsp> = {
  fromJSON(object: any): CreateRoomModeCategoryRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRoomModeCategoryRsp>, I>>(base?: I): CreateRoomModeCategoryRsp {
    return CreateRoomModeCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModeCategoryRsp>, I>>(object: I): CreateRoomModeCategoryRsp {
    const message = createBaseCreateRoomModeCategoryRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateRoomModeCategoryReq(): UpdateRoomModeCategoryReq {
  return { id: 0, name: '', name_i18n: [], position: 0 };
}

export const UpdateRoomModeCategoryReq: MessageFns<UpdateRoomModeCategoryReq> = {
  fromJSON(object: any): UpdateRoomModeCategoryReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      position: isSet(object.position) ? globalThis.Number(object.position) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomModeCategoryReq>, I>>(base?: I): UpdateRoomModeCategoryReq {
    return UpdateRoomModeCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModeCategoryReq>, I>>(object: I): UpdateRoomModeCategoryReq {
    const message = createBaseUpdateRoomModeCategoryReq();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.position = object.position ?? 0;
    return message;
  }
};

function createBaseUpdateRoomModeCategoryRsp(): UpdateRoomModeCategoryRsp {
  return {};
}

export const UpdateRoomModeCategoryRsp: MessageFns<UpdateRoomModeCategoryRsp> = {
  fromJSON(_: any): UpdateRoomModeCategoryRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomModeCategoryRsp>, I>>(base?: I): UpdateRoomModeCategoryRsp {
    return UpdateRoomModeCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModeCategoryRsp>, I>>(_: I): UpdateRoomModeCategoryRsp {
    const message = createBaseUpdateRoomModeCategoryRsp();
    return message;
  }
};

function createBaseDeleteRoomModeCategoryReq(): DeleteRoomModeCategoryReq {
  return { id: 0 };
}

export const DeleteRoomModeCategoryReq: MessageFns<DeleteRoomModeCategoryReq> = {
  fromJSON(object: any): DeleteRoomModeCategoryReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteRoomModeCategoryReq>, I>>(base?: I): DeleteRoomModeCategoryReq {
    return DeleteRoomModeCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModeCategoryReq>, I>>(object: I): DeleteRoomModeCategoryReq {
    const message = createBaseDeleteRoomModeCategoryReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteRoomModeCategoryRsp(): DeleteRoomModeCategoryRsp {
  return {};
}

export const DeleteRoomModeCategoryRsp: MessageFns<DeleteRoomModeCategoryRsp> = {
  fromJSON(_: any): DeleteRoomModeCategoryRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomModeCategoryRsp>, I>>(base?: I): DeleteRoomModeCategoryRsp {
    return DeleteRoomModeCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModeCategoryRsp>, I>>(_: I): DeleteRoomModeCategoryRsp {
    const message = createBaseDeleteRoomModeCategoryRsp();
    return message;
  }
};

function createBaseSetRoomModeCategoryActiveStatusReq(): SetRoomModeCategoryActiveStatusReq {
  return { id: 0, active_status: 0 };
}

export const SetRoomModeCategoryActiveStatusReq: MessageFns<SetRoomModeCategoryActiveStatusReq> = {
  fromJSON(object: any): SetRoomModeCategoryActiveStatusReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      active_status: isSet(object.active_status) ? roomModeCategoryActiveStatusFromJSON(object.active_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<SetRoomModeCategoryActiveStatusReq>, I>>(
    base?: I
  ): SetRoomModeCategoryActiveStatusReq {
    return SetRoomModeCategoryActiveStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomModeCategoryActiveStatusReq>, I>>(
    object: I
  ): SetRoomModeCategoryActiveStatusReq {
    const message = createBaseSetRoomModeCategoryActiveStatusReq();
    message.id = object.id ?? 0;
    message.active_status = object.active_status ?? 0;
    return message;
  }
};

function createBaseSetRoomModeCategoryActiveStatusRsp(): SetRoomModeCategoryActiveStatusRsp {
  return {};
}

export const SetRoomModeCategoryActiveStatusRsp: MessageFns<SetRoomModeCategoryActiveStatusRsp> = {
  fromJSON(_: any): SetRoomModeCategoryActiveStatusRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SetRoomModeCategoryActiveStatusRsp>, I>>(
    base?: I
  ): SetRoomModeCategoryActiveStatusRsp {
    return SetRoomModeCategoryActiveStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomModeCategoryActiveStatusRsp>, I>>(
    _: I
  ): SetRoomModeCategoryActiveStatusRsp {
    const message = createBaseSetRoomModeCategoryActiveStatusRsp();
    return message;
  }
};

function createBaseRoomModeConfig(): RoomModeConfig {
  return {
    id: 0,
    name: '',
    name_i18n: [],
    icon: '',
    icon_i18n: [],
    room_bg_img_url: '',
    room_bg_img_type: '',
    play_url: '',
    room_mode_code: '',
    room_layout_code: '',
    play_code: '',
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: '',
    deleted_at: 0
  };
}

export const RoomModeConfig: MessageFns<RoomModeConfig> = {
  fromJSON(object: any): RoomModeConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      icon_i18n: globalThis.Array.isArray(object?.icon_i18n) ? object.icon_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      room_bg_img_url: isSet(object.room_bg_img_url) ? globalThis.String(object.room_bg_img_url) : '',
      room_bg_img_type: isSet(object.room_bg_img_type) ? globalThis.String(object.room_bg_img_type) : '',
      play_url: isSet(object.play_url) ? globalThis.String(object.play_url) : '',
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : '',
      play_code: isSet(object.play_code) ? globalThis.String(object.play_code) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      deleted_at: isSet(object.deleted_at) ? globalThis.Number(object.deleted_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomModeConfig>, I>>(base?: I): RoomModeConfig {
    return RoomModeConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeConfig>, I>>(object: I): RoomModeConfig {
    const message = createBaseRoomModeConfig();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.icon = object.icon ?? '';
    message.icon_i18n = object.icon_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.room_bg_img_url = object.room_bg_img_url ?? '';
    message.room_bg_img_type = object.room_bg_img_type ?? '';
    message.play_url = object.play_url ?? '';
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    message.play_code = object.play_code ?? '';
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    message.deleted_at = object.deleted_at ?? 0;
    return message;
  }
};

function createBaseListRoomModeConfigReq(): ListRoomModeConfigReq {
  return { page: undefined, id: 0, name: '', updater: '', updated_at_start: 0, updated_at_end: 0, status: 0 };
}

export const ListRoomModeConfigReq: MessageFns<ListRoomModeConfigReq> = {
  fromJSON(object: any): ListRoomModeConfigReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at_start: isSet(object.updated_at_start) ? globalThis.Number(object.updated_at_start) : 0,
      updated_at_end: isSet(object.updated_at_end) ? globalThis.Number(object.updated_at_end) : 0,
      status: isSet(object.status) ? roomModeConfigStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModeConfigReq>, I>>(base?: I): ListRoomModeConfigReq {
    return ListRoomModeConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeConfigReq>, I>>(object: I): ListRoomModeConfigReq {
    const message = createBaseListRoomModeConfigReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.updater = object.updater ?? '';
    message.updated_at_start = object.updated_at_start ?? 0;
    message.updated_at_end = object.updated_at_end ?? 0;
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseListRoomModeConfigRsp(): ListRoomModeConfigRsp {
  return { page: undefined, room_mode_configs: [] };
}

export const ListRoomModeConfigRsp: MessageFns<ListRoomModeConfigRsp> = {
  fromJSON(object: any): ListRoomModeConfigRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_mode_configs: globalThis.Array.isArray(object?.room_mode_configs)
        ? object.room_mode_configs.map((e: any) => RoomModeConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModeConfigRsp>, I>>(base?: I): ListRoomModeConfigRsp {
    return ListRoomModeConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeConfigRsp>, I>>(object: I): ListRoomModeConfigRsp {
    const message = createBaseListRoomModeConfigRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_mode_configs = object.room_mode_configs?.map(e => RoomModeConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomModeConfigReq(): CreateRoomModeConfigReq {
  return {
    name: '',
    name_i18n: [],
    icon: '',
    icon_i18n: [],
    room_bg_img_url: '',
    room_bg_img_type: '',
    play_url: '',
    room_mode_code: '',
    room_layout_code: '',
    play_code: ''
  };
}

export const CreateRoomModeConfigReq: MessageFns<CreateRoomModeConfigReq> = {
  fromJSON(object: any): CreateRoomModeConfigReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      icon_i18n: globalThis.Array.isArray(object?.icon_i18n) ? object.icon_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      room_bg_img_url: isSet(object.room_bg_img_url) ? globalThis.String(object.room_bg_img_url) : '',
      room_bg_img_type: isSet(object.room_bg_img_type) ? globalThis.String(object.room_bg_img_type) : '',
      play_url: isSet(object.play_url) ? globalThis.String(object.play_url) : '',
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : '',
      play_code: isSet(object.play_code) ? globalThis.String(object.play_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateRoomModeConfigReq>, I>>(base?: I): CreateRoomModeConfigReq {
    return CreateRoomModeConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModeConfigReq>, I>>(object: I): CreateRoomModeConfigReq {
    const message = createBaseCreateRoomModeConfigReq();
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.icon = object.icon ?? '';
    message.icon_i18n = object.icon_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.room_bg_img_url = object.room_bg_img_url ?? '';
    message.room_bg_img_type = object.room_bg_img_type ?? '';
    message.play_url = object.play_url ?? '';
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    message.play_code = object.play_code ?? '';
    return message;
  }
};

function createBaseCreateRoomModeConfigRsp(): CreateRoomModeConfigRsp {
  return { id: 0 };
}

export const CreateRoomModeConfigRsp: MessageFns<CreateRoomModeConfigRsp> = {
  fromJSON(object: any): CreateRoomModeConfigRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRoomModeConfigRsp>, I>>(base?: I): CreateRoomModeConfigRsp {
    return CreateRoomModeConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModeConfigRsp>, I>>(object: I): CreateRoomModeConfigRsp {
    const message = createBaseCreateRoomModeConfigRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateRoomModeConfigReq(): UpdateRoomModeConfigReq {
  return {
    id: 0,
    name: '',
    name_i18n: [],
    icon: '',
    icon_i18n: [],
    room_bg_img_url: '',
    room_bg_img_type: '',
    play_url: '',
    room_mode_code: '',
    room_layout_code: '',
    play_code: ''
  };
}

export const UpdateRoomModeConfigReq: MessageFns<UpdateRoomModeConfigReq> = {
  fromJSON(object: any): UpdateRoomModeConfigReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: globalThis.Array.isArray(object?.name_i18n) ? object.name_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      icon_i18n: globalThis.Array.isArray(object?.icon_i18n) ? object.icon_i18n.map((e: any) => I18n.fromJSON(e)) : [],
      room_bg_img_url: isSet(object.room_bg_img_url) ? globalThis.String(object.room_bg_img_url) : '',
      room_bg_img_type: isSet(object.room_bg_img_type) ? globalThis.String(object.room_bg_img_type) : '',
      play_url: isSet(object.play_url) ? globalThis.String(object.play_url) : '',
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : '',
      play_code: isSet(object.play_code) ? globalThis.String(object.play_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomModeConfigReq>, I>>(base?: I): UpdateRoomModeConfigReq {
    return UpdateRoomModeConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModeConfigReq>, I>>(object: I): UpdateRoomModeConfigReq {
    const message = createBaseUpdateRoomModeConfigReq();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.name_i18n = object.name_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.icon = object.icon ?? '';
    message.icon_i18n = object.icon_i18n?.map(e => I18n.fromPartial(e)) || [];
    message.room_bg_img_url = object.room_bg_img_url ?? '';
    message.room_bg_img_type = object.room_bg_img_type ?? '';
    message.play_url = object.play_url ?? '';
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    message.play_code = object.play_code ?? '';
    return message;
  }
};

function createBaseUpdateRoomModeConfigRsp(): UpdateRoomModeConfigRsp {
  return {};
}

export const UpdateRoomModeConfigRsp: MessageFns<UpdateRoomModeConfigRsp> = {
  fromJSON(_: any): UpdateRoomModeConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomModeConfigRsp>, I>>(base?: I): UpdateRoomModeConfigRsp {
    return UpdateRoomModeConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModeConfigRsp>, I>>(_: I): UpdateRoomModeConfigRsp {
    const message = createBaseUpdateRoomModeConfigRsp();
    return message;
  }
};

function createBaseDeleteRoomModeConfigReq(): DeleteRoomModeConfigReq {
  return { id: 0 };
}

export const DeleteRoomModeConfigReq: MessageFns<DeleteRoomModeConfigReq> = {
  fromJSON(object: any): DeleteRoomModeConfigReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteRoomModeConfigReq>, I>>(base?: I): DeleteRoomModeConfigReq {
    return DeleteRoomModeConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModeConfigReq>, I>>(object: I): DeleteRoomModeConfigReq {
    const message = createBaseDeleteRoomModeConfigReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteRoomModeConfigRsp(): DeleteRoomModeConfigRsp {
  return {};
}

export const DeleteRoomModeConfigRsp: MessageFns<DeleteRoomModeConfigRsp> = {
  fromJSON(_: any): DeleteRoomModeConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomModeConfigRsp>, I>>(base?: I): DeleteRoomModeConfigRsp {
    return DeleteRoomModeConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModeConfigRsp>, I>>(_: I): DeleteRoomModeConfigRsp {
    const message = createBaseDeleteRoomModeConfigRsp();
    return message;
  }
};

function createBaseRoomModePublish(): RoomModePublish {
  return {
    id: 0,
    room_mode_config: undefined,
    room_mode_category: undefined,
    position: 0,
    default_selected: 0,
    active_status: 0,
    publish_status: 0,
    room_id_whitelist: [],
    valid_time_start: 0,
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: '',
    deleted_at: 0
  };
}

export const RoomModePublish: MessageFns<RoomModePublish> = {
  fromJSON(object: any): RoomModePublish {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      room_mode_config: isSet(object.room_mode_config) ? RoomModeConfig.fromJSON(object.room_mode_config) : undefined,
      room_mode_category: isSet(object.room_mode_category)
        ? RoomModeCategory.fromJSON(object.room_mode_category)
        : undefined,
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
      default_selected: isSet(object.default_selected)
        ? roomModePublishDefaultSelectedFromJSON(object.default_selected)
        : 0,
      active_status: isSet(object.active_status) ? roomModePublishActiveStatusFromJSON(object.active_status) : 0,
      publish_status: isSet(object.publish_status) ? roomModePublishStatusFromJSON(object.publish_status) : 0,
      room_id_whitelist: globalThis.Array.isArray(object?.room_id_whitelist)
        ? object.room_id_whitelist.map((e: any) => globalThis.Number(e))
        : [],
      valid_time_start: isSet(object.valid_time_start) ? globalThis.Number(object.valid_time_start) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      deleted_at: isSet(object.deleted_at) ? globalThis.Number(object.deleted_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomModePublish>, I>>(base?: I): RoomModePublish {
    return RoomModePublish.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModePublish>, I>>(object: I): RoomModePublish {
    const message = createBaseRoomModePublish();
    message.id = object.id ?? 0;
    message.room_mode_config =
      object.room_mode_config !== undefined && object.room_mode_config !== null
        ? RoomModeConfig.fromPartial(object.room_mode_config)
        : undefined;
    message.room_mode_category =
      object.room_mode_category !== undefined && object.room_mode_category !== null
        ? RoomModeCategory.fromPartial(object.room_mode_category)
        : undefined;
    message.position = object.position ?? 0;
    message.default_selected = object.default_selected ?? 0;
    message.active_status = object.active_status ?? 0;
    message.publish_status = object.publish_status ?? 0;
    message.room_id_whitelist = object.room_id_whitelist?.map(e => e) || [];
    message.valid_time_start = object.valid_time_start ?? 0;
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    message.deleted_at = object.deleted_at ?? 0;
    return message;
  }
};

function createBaseListRoomModePublishReq(): ListRoomModePublishReq {
  return { page: undefined, room_mode_config_id: 0, room_mode_category_id: 0, publish_statuses: [] };
}

export const ListRoomModePublishReq: MessageFns<ListRoomModePublishReq> = {
  fromJSON(object: any): ListRoomModePublishReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_mode_config_id: isSet(object.room_mode_config_id) ? globalThis.Number(object.room_mode_config_id) : 0,
      room_mode_category_id: isSet(object.room_mode_category_id) ? globalThis.Number(object.room_mode_category_id) : 0,
      publish_statuses: globalThis.Array.isArray(object?.publish_statuses)
        ? object.publish_statuses.map((e: any) => roomModePublishStatusFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModePublishReq>, I>>(base?: I): ListRoomModePublishReq {
    return ListRoomModePublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModePublishReq>, I>>(object: I): ListRoomModePublishReq {
    const message = createBaseListRoomModePublishReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_mode_config_id = object.room_mode_config_id ?? 0;
    message.room_mode_category_id = object.room_mode_category_id ?? 0;
    message.publish_statuses = object.publish_statuses?.map(e => e) || [];
    return message;
  }
};

function createBaseListRoomModePublishRsp(): ListRoomModePublishRsp {
  return { page: undefined, room_mode_publishes: [] };
}

export const ListRoomModePublishRsp: MessageFns<ListRoomModePublishRsp> = {
  fromJSON(object: any): ListRoomModePublishRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_mode_publishes: globalThis.Array.isArray(object?.room_mode_publishes)
        ? object.room_mode_publishes.map((e: any) => RoomModePublish.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModePublishRsp>, I>>(base?: I): ListRoomModePublishRsp {
    return ListRoomModePublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModePublishRsp>, I>>(object: I): ListRoomModePublishRsp {
    const message = createBaseListRoomModePublishRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_mode_publishes = object.room_mode_publishes?.map(e => RoomModePublish.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomModePublishReq(): CreateRoomModePublishReq {
  return {
    room_mode_config_id: 0,
    room_mode_category_id: 0,
    position: 0,
    default_selected: 0,
    room_id_whitelist: [],
    valid_time_start: 0,
    active_status: 0
  };
}

export const CreateRoomModePublishReq: MessageFns<CreateRoomModePublishReq> = {
  fromJSON(object: any): CreateRoomModePublishReq {
    return {
      room_mode_config_id: isSet(object.room_mode_config_id) ? globalThis.Number(object.room_mode_config_id) : 0,
      room_mode_category_id: isSet(object.room_mode_category_id) ? globalThis.Number(object.room_mode_category_id) : 0,
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
      default_selected: isSet(object.default_selected)
        ? roomModePublishDefaultSelectedFromJSON(object.default_selected)
        : 0,
      room_id_whitelist: globalThis.Array.isArray(object?.room_id_whitelist)
        ? object.room_id_whitelist.map((e: any) => globalThis.Number(e))
        : [],
      valid_time_start: isSet(object.valid_time_start) ? globalThis.Number(object.valid_time_start) : 0,
      active_status: isSet(object.active_status) ? roomModePublishActiveStatusFromJSON(object.active_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<CreateRoomModePublishReq>, I>>(base?: I): CreateRoomModePublishReq {
    return CreateRoomModePublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModePublishReq>, I>>(object: I): CreateRoomModePublishReq {
    const message = createBaseCreateRoomModePublishReq();
    message.room_mode_config_id = object.room_mode_config_id ?? 0;
    message.room_mode_category_id = object.room_mode_category_id ?? 0;
    message.position = object.position ?? 0;
    message.default_selected = object.default_selected ?? 0;
    message.room_id_whitelist = object.room_id_whitelist?.map(e => e) || [];
    message.valid_time_start = object.valid_time_start ?? 0;
    message.active_status = object.active_status ?? 0;
    return message;
  }
};

function createBaseCreateRoomModePublishRsp(): CreateRoomModePublishRsp {
  return { id: 0 };
}

export const CreateRoomModePublishRsp: MessageFns<CreateRoomModePublishRsp> = {
  fromJSON(object: any): CreateRoomModePublishRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRoomModePublishRsp>, I>>(base?: I): CreateRoomModePublishRsp {
    return CreateRoomModePublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomModePublishRsp>, I>>(object: I): CreateRoomModePublishRsp {
    const message = createBaseCreateRoomModePublishRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateRoomModePublishReq(): UpdateRoomModePublishReq {
  return {
    id: 0,
    room_mode_config_id: 0,
    room_mode_category_id: 0,
    position: 0,
    default_selected: 0,
    room_id_whitelist: [],
    valid_time_start: 0
  };
}

export const UpdateRoomModePublishReq: MessageFns<UpdateRoomModePublishReq> = {
  fromJSON(object: any): UpdateRoomModePublishReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      room_mode_config_id: isSet(object.room_mode_config_id) ? globalThis.Number(object.room_mode_config_id) : 0,
      room_mode_category_id: isSet(object.room_mode_category_id) ? globalThis.Number(object.room_mode_category_id) : 0,
      position: isSet(object.position) ? globalThis.Number(object.position) : 0,
      default_selected: isSet(object.default_selected)
        ? roomModePublishDefaultSelectedFromJSON(object.default_selected)
        : 0,
      room_id_whitelist: globalThis.Array.isArray(object?.room_id_whitelist)
        ? object.room_id_whitelist.map((e: any) => globalThis.Number(e))
        : [],
      valid_time_start: isSet(object.valid_time_start) ? globalThis.Number(object.valid_time_start) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomModePublishReq>, I>>(base?: I): UpdateRoomModePublishReq {
    return UpdateRoomModePublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModePublishReq>, I>>(object: I): UpdateRoomModePublishReq {
    const message = createBaseUpdateRoomModePublishReq();
    message.id = object.id ?? 0;
    message.room_mode_config_id = object.room_mode_config_id ?? 0;
    message.room_mode_category_id = object.room_mode_category_id ?? 0;
    message.position = object.position ?? 0;
    message.default_selected = object.default_selected ?? 0;
    message.room_id_whitelist = object.room_id_whitelist?.map(e => e) || [];
    message.valid_time_start = object.valid_time_start ?? 0;
    return message;
  }
};

function createBaseUpdateRoomModePublishRsp(): UpdateRoomModePublishRsp {
  return {};
}

export const UpdateRoomModePublishRsp: MessageFns<UpdateRoomModePublishRsp> = {
  fromJSON(_: any): UpdateRoomModePublishRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomModePublishRsp>, I>>(base?: I): UpdateRoomModePublishRsp {
    return UpdateRoomModePublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomModePublishRsp>, I>>(_: I): UpdateRoomModePublishRsp {
    const message = createBaseUpdateRoomModePublishRsp();
    return message;
  }
};

function createBaseDeleteRoomModePublishReq(): DeleteRoomModePublishReq {
  return { id: 0 };
}

export const DeleteRoomModePublishReq: MessageFns<DeleteRoomModePublishReq> = {
  fromJSON(object: any): DeleteRoomModePublishReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteRoomModePublishReq>, I>>(base?: I): DeleteRoomModePublishReq {
    return DeleteRoomModePublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModePublishReq>, I>>(object: I): DeleteRoomModePublishReq {
    const message = createBaseDeleteRoomModePublishReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteRoomModePublishRsp(): DeleteRoomModePublishRsp {
  return {};
}

export const DeleteRoomModePublishRsp: MessageFns<DeleteRoomModePublishRsp> = {
  fromJSON(_: any): DeleteRoomModePublishRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomModePublishRsp>, I>>(base?: I): DeleteRoomModePublishRsp {
    return DeleteRoomModePublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomModePublishRsp>, I>>(_: I): DeleteRoomModePublishRsp {
    const message = createBaseDeleteRoomModePublishRsp();
    return message;
  }
};

function createBaseSetRoomModePublishActiveStatusReq(): SetRoomModePublishActiveStatusReq {
  return { id: 0, active_status: 0 };
}

export const SetRoomModePublishActiveStatusReq: MessageFns<SetRoomModePublishActiveStatusReq> = {
  fromJSON(object: any): SetRoomModePublishActiveStatusReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      active_status: isSet(object.active_status) ? roomModePublishActiveStatusFromJSON(object.active_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<SetRoomModePublishActiveStatusReq>, I>>(
    base?: I
  ): SetRoomModePublishActiveStatusReq {
    return SetRoomModePublishActiveStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomModePublishActiveStatusReq>, I>>(
    object: I
  ): SetRoomModePublishActiveStatusReq {
    const message = createBaseSetRoomModePublishActiveStatusReq();
    message.id = object.id ?? 0;
    message.active_status = object.active_status ?? 0;
    return message;
  }
};

function createBaseSetRoomModePublishActiveStatusRsp(): SetRoomModePublishActiveStatusRsp {
  return {};
}

export const SetRoomModePublishActiveStatusRsp: MessageFns<SetRoomModePublishActiveStatusRsp> = {
  fromJSON(_: any): SetRoomModePublishActiveStatusRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SetRoomModePublishActiveStatusRsp>, I>>(
    base?: I
  ): SetRoomModePublishActiveStatusRsp {
    return SetRoomModePublishActiveStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomModePublishActiveStatusRsp>, I>>(
    _: I
  ): SetRoomModePublishActiveStatusRsp {
    const message = createBaseSetRoomModePublishActiveStatusRsp();
    return message;
  }
};

function createBaseRoomModeCode(): RoomModeCode {
  return { code: '', name: '' };
}

export const RoomModeCode: MessageFns<RoomModeCode> = {
  fromJSON(object: any): RoomModeCode {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeCode>, I>>(base?: I): RoomModeCode {
    return RoomModeCode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeCode>, I>>(object: I): RoomModeCode {
    const message = createBaseRoomModeCode();
    message.code = object.code ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListRoomModeCodeReq(): ListRoomModeCodeReq {
  return {};
}

export const ListRoomModeCodeReq: MessageFns<ListRoomModeCodeReq> = {
  fromJSON(_: any): ListRoomModeCodeReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListRoomModeCodeReq>, I>>(base?: I): ListRoomModeCodeReq {
    return ListRoomModeCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeCodeReq>, I>>(_: I): ListRoomModeCodeReq {
    const message = createBaseListRoomModeCodeReq();
    return message;
  }
};

function createBaseListRoomModeCodeRsp(): ListRoomModeCodeRsp {
  return { room_mode_codes: [] };
}

export const ListRoomModeCodeRsp: MessageFns<ListRoomModeCodeRsp> = {
  fromJSON(object: any): ListRoomModeCodeRsp {
    return {
      room_mode_codes: globalThis.Array.isArray(object?.room_mode_codes)
        ? object.room_mode_codes.map((e: any) => RoomModeCode.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModeCodeRsp>, I>>(base?: I): ListRoomModeCodeRsp {
    return ListRoomModeCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModeCodeRsp>, I>>(object: I): ListRoomModeCodeRsp {
    const message = createBaseListRoomModeCodeRsp();
    message.room_mode_codes = object.room_mode_codes?.map(e => RoomModeCode.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRoomLayoutCode(): RoomLayoutCode {
  return { code: '', name: '' };
}

export const RoomLayoutCode: MessageFns<RoomLayoutCode> = {
  fromJSON(object: any): RoomLayoutCode {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomLayoutCode>, I>>(base?: I): RoomLayoutCode {
    return RoomLayoutCode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomLayoutCode>, I>>(object: I): RoomLayoutCode {
    const message = createBaseRoomLayoutCode();
    message.code = object.code ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListRoomLayoutCodeReq(): ListRoomLayoutCodeReq {
  return { room_mode_code: '' };
}

export const ListRoomLayoutCodeReq: MessageFns<ListRoomLayoutCodeReq> = {
  fromJSON(object: any): ListRoomLayoutCodeReq {
    return { room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '' };
  },

  create<I extends Exact<DeepPartial<ListRoomLayoutCodeReq>, I>>(base?: I): ListRoomLayoutCodeReq {
    return ListRoomLayoutCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomLayoutCodeReq>, I>>(object: I): ListRoomLayoutCodeReq {
    const message = createBaseListRoomLayoutCodeReq();
    message.room_mode_code = object.room_mode_code ?? '';
    return message;
  }
};

function createBaseListRoomLayoutCodeRsp(): ListRoomLayoutCodeRsp {
  return { room_layout_codes: [] };
}

export const ListRoomLayoutCodeRsp: MessageFns<ListRoomLayoutCodeRsp> = {
  fromJSON(object: any): ListRoomLayoutCodeRsp {
    return {
      room_layout_codes: globalThis.Array.isArray(object?.room_layout_codes)
        ? object.room_layout_codes.map((e: any) => RoomLayoutCode.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomLayoutCodeRsp>, I>>(base?: I): ListRoomLayoutCodeRsp {
    return ListRoomLayoutCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomLayoutCodeRsp>, I>>(object: I): ListRoomLayoutCodeRsp {
    const message = createBaseListRoomLayoutCodeRsp();
    message.room_layout_codes = object.room_layout_codes?.map(e => RoomLayoutCode.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRoomPlayCode(): RoomPlayCode {
  return { code: '', name: '' };
}

export const RoomPlayCode: MessageFns<RoomPlayCode> = {
  fromJSON(object: any): RoomPlayCode {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomPlayCode>, I>>(base?: I): RoomPlayCode {
    return RoomPlayCode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomPlayCode>, I>>(object: I): RoomPlayCode {
    const message = createBaseRoomPlayCode();
    message.code = object.code ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListRoomPlayCodeReq(): ListRoomPlayCodeReq {
  return { room_mode_code: '', room_layout_code: '' };
}

export const ListRoomPlayCodeReq: MessageFns<ListRoomPlayCodeReq> = {
  fromJSON(object: any): ListRoomPlayCodeReq {
    return {
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListRoomPlayCodeReq>, I>>(base?: I): ListRoomPlayCodeReq {
    return ListRoomPlayCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomPlayCodeReq>, I>>(object: I): ListRoomPlayCodeReq {
    const message = createBaseListRoomPlayCodeReq();
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    return message;
  }
};

function createBaseListRoomPlayCodeRsp(): ListRoomPlayCodeRsp {
  return { room_play_codes: [] };
}

export const ListRoomPlayCodeRsp: MessageFns<ListRoomPlayCodeRsp> = {
  fromJSON(object: any): ListRoomPlayCodeRsp {
    return {
      room_play_codes: globalThis.Array.isArray(object?.room_play_codes)
        ? object.room_play_codes.map((e: any) => RoomPlayCode.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomPlayCodeRsp>, I>>(base?: I): ListRoomPlayCodeRsp {
    return ListRoomPlayCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomPlayCodeRsp>, I>>(object: I): ListRoomPlayCodeRsp {
    const message = createBaseListRoomPlayCodeRsp();
    message.room_play_codes = object.room_play_codes?.map(e => RoomPlayCode.fromPartial(e)) || [];
    return message;
  }
};

/** 房间模式 */
export type RoomModeMgrDefinition = typeof RoomModeMgrDefinition;
export const RoomModeMgrDefinition = {
  name: 'RoomModeMgr',
  fullName: 'mgrroommode.RoomModeMgr',
  methods: {
    /** 房间模式分类-列表 */
    listRoomModeCategory: {
      name: 'ListRoomModeCategory',
      requestType: ListRoomModeCategoryReq,
      requestStream: false,
      responseType: ListRoomModeCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式分类-新增 */
    createRoomModeCategory: {
      name: 'CreateRoomModeCategory',
      requestType: CreateRoomModeCategoryReq,
      requestStream: false,
      responseType: CreateRoomModeCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式分类-修改 */
    updateRoomModeCategory: {
      name: 'UpdateRoomModeCategory',
      requestType: UpdateRoomModeCategoryReq,
      requestStream: false,
      responseType: UpdateRoomModeCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式分类-删除 */
    deleteRoomModeCategory: {
      name: 'DeleteRoomModeCategory',
      requestType: DeleteRoomModeCategoryReq,
      requestStream: false,
      responseType: DeleteRoomModeCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式分类-有效状态设置(启用、禁用) */
    setRoomModeCategoryActiveStatus: {
      name: 'SetRoomModeCategoryActiveStatus',
      requestType: SetRoomModeCategoryActiveStatusReq,
      requestStream: false,
      responseType: SetRoomModeCategoryActiveStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式配置-列表 */
    listRoomModeConfig: {
      name: 'ListRoomModeConfig',
      requestType: ListRoomModeConfigReq,
      requestStream: false,
      responseType: ListRoomModeConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式配置-新增 */
    createRoomModeConfig: {
      name: 'CreateRoomModeConfig',
      requestType: CreateRoomModeConfigReq,
      requestStream: false,
      responseType: CreateRoomModeConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式配置-修改 */
    updateRoomModeConfig: {
      name: 'UpdateRoomModeConfig',
      requestType: UpdateRoomModeConfigReq,
      requestStream: false,
      responseType: UpdateRoomModeConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式配置-删除 */
    deleteRoomModeConfig: {
      name: 'DeleteRoomModeConfig',
      requestType: DeleteRoomModeConfigReq,
      requestStream: false,
      responseType: DeleteRoomModeConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式上架-列表 */
    listRoomModePublish: {
      name: 'ListRoomModePublish',
      requestType: ListRoomModePublishReq,
      requestStream: false,
      responseType: ListRoomModePublishRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式上架-新增 */
    createRoomModePublish: {
      name: 'CreateRoomModePublish',
      requestType: CreateRoomModePublishReq,
      requestStream: false,
      responseType: CreateRoomModePublishRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式上架-修改 */
    updateRoomModePublish: {
      name: 'UpdateRoomModePublish',
      requestType: UpdateRoomModePublishReq,
      requestStream: false,
      responseType: UpdateRoomModePublishRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式上架-删除 */
    deleteRoomModePublish: {
      name: 'DeleteRoomModePublish',
      requestType: DeleteRoomModePublishReq,
      requestStream: false,
      responseType: DeleteRoomModePublishRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式上架-有效状态设置(启用、禁用) */
    setRoomModePublishActiveStatus: {
      name: 'SetRoomModePublishActiveStatus',
      requestType: SetRoomModePublishActiveStatusReq,
      requestStream: false,
      responseType: SetRoomModePublishActiveStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 房间模式code-列表 */
    listRoomModeCode: {
      name: 'ListRoomModeCode',
      requestType: ListRoomModeCodeReq,
      requestStream: false,
      responseType: ListRoomModeCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 房间布局code-列表 */
    listRoomLayoutCode: {
      name: 'ListRoomLayoutCode',
      requestType: ListRoomLayoutCodeReq,
      requestStream: false,
      responseType: ListRoomLayoutCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 房间玩法code-列表 */
    listRoomPlayCode: {
      name: 'ListRoomPlayCode',
      requestType: ListRoomPlayCodeReq,
      requestStream: false,
      responseType: ListRoomPlayCodeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
