// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: mgr/room_list_mgr.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'mgrroomlist';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/roomlist/handlermgr */

/** 房间分发排序状态 */
export enum RoomTabStatus {
  ROOM_TAB_STATUS_NONE = 0,
  /** ROOM_TAB_STATUS_INEFFECTIVE - 不生效 */
  ROOM_TAB_STATUS_INEFFECTIVE = 1,
  /** ROOM_TAB_STATUS_EFFECTIVE - 生效中 */
  ROOM_TAB_STATUS_EFFECTIVE = 2,
  /** ROOM_TAB_STATUS_INVALID - 已失效 */
  ROOM_TAB_STATUS_INVALID = 3,
  UNRECOGNIZED = -1
}

export function roomTabStatusFromJSON(object: any): RoomTabStatus {
  switch (object) {
    case 0:
    case 'ROOM_TAB_STATUS_NONE':
      return RoomTabStatus.ROOM_TAB_STATUS_NONE;
    case 1:
    case 'ROOM_TAB_STATUS_INEFFECTIVE':
      return RoomTabStatus.ROOM_TAB_STATUS_INEFFECTIVE;
    case 2:
    case 'ROOM_TAB_STATUS_EFFECTIVE':
      return RoomTabStatus.ROOM_TAB_STATUS_EFFECTIVE;
    case 3:
    case 'ROOM_TAB_STATUS_INVALID':
      return RoomTabStatus.ROOM_TAB_STATUS_INVALID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomTabStatus.UNRECOGNIZED;
  }
}

/** 房间分发排序方式 */
export enum RoomDistributionMode {
  ROOM_DISTRIBUTION_MODE_NONE = 0,
  /** ROOM_DISTRIBUTION_MODE_TOP - 置顶 */
  ROOM_DISTRIBUTION_MODE_TOP = 10,
  /** ROOM_DISTRIBUTION_MODE_BOTTOM - 置底 */
  ROOM_DISTRIBUTION_MODE_BOTTOM = 20,
  UNRECOGNIZED = -1
}

export function roomDistributionModeFromJSON(object: any): RoomDistributionMode {
  switch (object) {
    case 0:
    case 'ROOM_DISTRIBUTION_MODE_NONE':
      return RoomDistributionMode.ROOM_DISTRIBUTION_MODE_NONE;
    case 10:
    case 'ROOM_DISTRIBUTION_MODE_TOP':
      return RoomDistributionMode.ROOM_DISTRIBUTION_MODE_TOP;
    case 20:
    case 'ROOM_DISTRIBUTION_MODE_BOTTOM':
      return RoomDistributionMode.ROOM_DISTRIBUTION_MODE_BOTTOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomDistributionMode.UNRECOGNIZED;
  }
}

/** 房间分发排序状态 */
export enum RoomDistributionStatus {
  ROOM_DISTRIBUTION_STATUS_NONE = 0,
  /** ROOM_DISTRIBUTION_STATUS_INEFFECTIVE - 不生效 */
  ROOM_DISTRIBUTION_STATUS_INEFFECTIVE = 1,
  /** ROOM_DISTRIBUTION_STATUS_EFFECTIVE - 生效中 */
  ROOM_DISTRIBUTION_STATUS_EFFECTIVE = 2,
  /** ROOM_DISTRIBUTION_STATUS_INVALID - 已失效, 包括房间导航栏tab被删除了. */
  ROOM_DISTRIBUTION_STATUS_INVALID = 3,
  UNRECOGNIZED = -1
}

export function roomDistributionStatusFromJSON(object: any): RoomDistributionStatus {
  switch (object) {
    case 0:
    case 'ROOM_DISTRIBUTION_STATUS_NONE':
      return RoomDistributionStatus.ROOM_DISTRIBUTION_STATUS_NONE;
    case 1:
    case 'ROOM_DISTRIBUTION_STATUS_INEFFECTIVE':
      return RoomDistributionStatus.ROOM_DISTRIBUTION_STATUS_INEFFECTIVE;
    case 2:
    case 'ROOM_DISTRIBUTION_STATUS_EFFECTIVE':
      return RoomDistributionStatus.ROOM_DISTRIBUTION_STATUS_EFFECTIVE;
    case 3:
    case 'ROOM_DISTRIBUTION_STATUS_INVALID':
      return RoomDistributionStatus.ROOM_DISTRIBUTION_STATUS_INVALID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomDistributionStatus.UNRECOGNIZED;
  }
}

/** 房间分发排序状态 */
export enum RoomTagStatus {
  ROOM_TAG_STATUS_NONE = 0,
  /** ROOM_TAG_STATUS_INEFFECTIVE - 不生效 */
  ROOM_TAG_STATUS_INEFFECTIVE = 1,
  /** ROOM_TAG_STATUS_EFFECTIVE - 生效中 */
  ROOM_TAG_STATUS_EFFECTIVE = 2,
  /** ROOM_TAG_STATUS_INVALID - 已失效 */
  ROOM_TAG_STATUS_INVALID = 3,
  UNRECOGNIZED = -1
}

export function roomTagStatusFromJSON(object: any): RoomTagStatus {
  switch (object) {
    case 0:
    case 'ROOM_TAG_STATUS_NONE':
      return RoomTagStatus.ROOM_TAG_STATUS_NONE;
    case 1:
    case 'ROOM_TAG_STATUS_INEFFECTIVE':
      return RoomTagStatus.ROOM_TAG_STATUS_INEFFECTIVE;
    case 2:
    case 'ROOM_TAG_STATUS_EFFECTIVE':
      return RoomTagStatus.ROOM_TAG_STATUS_EFFECTIVE;
    case 3:
    case 'ROOM_TAG_STATUS_INVALID':
      return RoomTagStatus.ROOM_TAG_STATUS_INVALID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomTagStatus.UNRECOGNIZED;
  }
}

/** 房间tab */
export interface RoomTab {
  id: number;
  /** 国际化菜单名称, 中文: zh, 英文: en, 阿语: ar, ... */
  i18n_names: { [key: string]: string };
  /** 排序权重, 越大排序越靠前. */
  sort_weight: number;
  /** 已发布 */
  published: boolean;
  /** 生效状态 */
  status: RoomTabStatus;
  create_at: number;
  create_by: string;
  update_at: number;
  update_by: string;
  delete_at: number;
  delete_by: string;
}

export interface RoomTab_I18nNamesEntry {
  key: string;
  value: string;
}

export interface SearchRoomTabReq {
  page: Page | undefined;
  status: RoomTabStatus[];
}

export interface SearchRoomTabRsp {
  page: Page | undefined;
  data: RoomTab[];
}

export interface CreateRoomTabReq {
  room_tab: RoomTab | undefined;
}

export interface CreateRoomTabRsp {}

export interface UpdateRoomTabReq {
  room_tab: RoomTab | undefined;
}

export interface UpdateRoomTabRsp {}

export interface DeleteRoomTabReq {
  room_tab_ids: number[];
}

export interface DeleteRoomTabRsp {}

export interface UpdateRoomTabPublishedReq {
  room_tab_ids: number[];
  published: boolean;
}

export interface UpdateRoomTabPublishedRsp {}

/** 房间分发 */
export interface RoomDistribution {
  /** ID */
  id: number;
  /** 房间Tab ID */
  room_tab_id: number;
  /** 房间ID列表, 批量创建时候用. */
  room_ids: number[];
  /** 房间ID */
  room_id: number;
  /** 房间名称 */
  room_name: string;
  /** 房间分发排序方式 */
  sort_mode: RoomDistributionMode;
  /** 房间分发排序权重 */
  sort_weight: number;
  /** 房间分发排序状态 */
  status: RoomDistributionStatus;
  /** 生效时间 */
  valid_from: number;
  /** 失效时间 */
  valid_to: number;
  /** 创建时间 */
  create_at: number;
  /** 创建人 */
  create_by: string;
  update_at: number;
  update_by: string;
  delete_at: number;
  delete_by: string;
}

export interface SearchRoomDistributionReq {
  page: Page | undefined;
  room_id: number;
  room_tab_id: number;
  sort_mode: RoomDistributionMode;
  operate_by: string;
  operate_at_start: number;
  operate_at_end: number;
  status: RoomDistributionStatus[];
}

export interface SearchRoomDistributionRsp {
  page: Page | undefined;
  data: RoomDistribution[];
}

export interface CreateRoomDistributionReq {
  room_distribution: RoomDistribution | undefined;
}

export interface CreateRoomDistributionRsp {}

export interface UpdateRoomDistributionReq {
  /** 只传room_tab_id、room_id、sort_weight即可，room_tab_id默认传1 */
  room_distribution: RoomDistribution | undefined;
}

export interface UpdateRoomDistributionRsp {}

export interface DeleteRoomDistributionReq {
  room_distribution_ids: number[];
}

export interface DeleteRoomDistributionRsp {}

/** 房间标签 */
export interface RoomTag {
  id: number;
  /** 房间ID */
  room_ids: number[];
  /** 房间ID */
  room_id: number;
  /** 房间名称 */
  room_name: string;
  /** 标签名称, 用户不可见. */
  name: string;
  /** 国际化菜单图标, 中文: zh, 英文: en, 阿语: ar, ... */
  i18n_icons: { [key: string]: string };
  /** 备注, 用户不可见. */
  remark: string;
  /** 状态 */
  status: RoomTagStatus;
  /** 生效时间 */
  valid_from: number;
  /** 失效时间 */
  valid_to: number;
  create_at: number;
  create_by: string;
  update_at: number;
  update_by: string;
  delete_at: number;
  delete_by: string;
}

export interface RoomTag_I18nIconsEntry {
  key: string;
  value: string;
}

export interface SearchRoomTagReq {
  page: Page | undefined;
  room_id: number;
  operate_by: string;
  operate_at_start: number;
  operate_at_end: number;
}

export interface SearchRoomTagRsp {
  page: Page | undefined;
  data: RoomTag[];
}

export interface CreateRoomTagReq {
  room_tag: RoomTag | undefined;
}

export interface CreateRoomTagRsp {}

export interface UpdateRoomTagReq {
  room_tag: RoomTag | undefined;
}

export interface UpdateRoomTagRsp {}

export interface DeleteRoomTagReq {
  room_tag_ids: number[];
}

export interface DeleteRoomTagRsp {}

function createBaseRoomTab(): RoomTab {
  return {
    id: 0,
    i18n_names: {},
    sort_weight: 0,
    published: false,
    status: 0,
    create_at: 0,
    create_by: '',
    update_at: 0,
    update_by: '',
    delete_at: 0,
    delete_by: ''
  };
}

export const RoomTab: MessageFns<RoomTab> = {
  fromJSON(object: any): RoomTab {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      sort_weight: isSet(object.sort_weight) ? globalThis.Number(object.sort_weight) : 0,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      status: isSet(object.status) ? roomTabStatusFromJSON(object.status) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_at: isSet(object.delete_at) ? globalThis.Number(object.delete_at) : 0,
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTab>, I>>(base?: I): RoomTab {
    return RoomTab.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTab>, I>>(object: I): RoomTab {
    const message = createBaseRoomTab();
    message.id = object.id ?? 0;
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.sort_weight = object.sort_weight ?? 0;
    message.published = object.published ?? false;
    message.status = object.status ?? 0;
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.delete_at = object.delete_at ?? 0;
    message.delete_by = object.delete_by ?? '';
    return message;
  }
};

function createBaseRoomTab_I18nNamesEntry(): RoomTab_I18nNamesEntry {
  return { key: '', value: '' };
}

export const RoomTab_I18nNamesEntry: MessageFns<RoomTab_I18nNamesEntry> = {
  fromJSON(object: any): RoomTab_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTab_I18nNamesEntry>, I>>(base?: I): RoomTab_I18nNamesEntry {
    return RoomTab_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTab_I18nNamesEntry>, I>>(object: I): RoomTab_I18nNamesEntry {
    const message = createBaseRoomTab_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSearchRoomTabReq(): SearchRoomTabReq {
  return { page: undefined, status: [] };
}

export const SearchRoomTabReq: MessageFns<SearchRoomTabReq> = {
  fromJSON(object: any): SearchRoomTabReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      status: globalThis.Array.isArray(object?.status) ? object.status.map((e: any) => roomTabStatusFromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomTabReq>, I>>(base?: I): SearchRoomTabReq {
    return SearchRoomTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomTabReq>, I>>(object: I): SearchRoomTabReq {
    const message = createBaseSearchRoomTabReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.status = object.status?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRoomTabRsp(): SearchRoomTabRsp {
  return { page: undefined, data: [] };
}

export const SearchRoomTabRsp: MessageFns<SearchRoomTabRsp> = {
  fromJSON(object: any): SearchRoomTabRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RoomTab.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomTabRsp>, I>>(base?: I): SearchRoomTabRsp {
    return SearchRoomTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomTabRsp>, I>>(object: I): SearchRoomTabRsp {
    const message = createBaseSearchRoomTabRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => RoomTab.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomTabReq(): CreateRoomTabReq {
  return { room_tab: undefined };
}

export const CreateRoomTabReq: MessageFns<CreateRoomTabReq> = {
  fromJSON(object: any): CreateRoomTabReq {
    return { room_tab: isSet(object.room_tab) ? RoomTab.fromJSON(object.room_tab) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateRoomTabReq>, I>>(base?: I): CreateRoomTabReq {
    return CreateRoomTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomTabReq>, I>>(object: I): CreateRoomTabReq {
    const message = createBaseCreateRoomTabReq();
    message.room_tab =
      object.room_tab !== undefined && object.room_tab !== null ? RoomTab.fromPartial(object.room_tab) : undefined;
    return message;
  }
};

function createBaseCreateRoomTabRsp(): CreateRoomTabRsp {
  return {};
}

export const CreateRoomTabRsp: MessageFns<CreateRoomTabRsp> = {
  fromJSON(_: any): CreateRoomTabRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateRoomTabRsp>, I>>(base?: I): CreateRoomTabRsp {
    return CreateRoomTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomTabRsp>, I>>(_: I): CreateRoomTabRsp {
    const message = createBaseCreateRoomTabRsp();
    return message;
  }
};

function createBaseUpdateRoomTabReq(): UpdateRoomTabReq {
  return { room_tab: undefined };
}

export const UpdateRoomTabReq: MessageFns<UpdateRoomTabReq> = {
  fromJSON(object: any): UpdateRoomTabReq {
    return { room_tab: isSet(object.room_tab) ? RoomTab.fromJSON(object.room_tab) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateRoomTabReq>, I>>(base?: I): UpdateRoomTabReq {
    return UpdateRoomTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTabReq>, I>>(object: I): UpdateRoomTabReq {
    const message = createBaseUpdateRoomTabReq();
    message.room_tab =
      object.room_tab !== undefined && object.room_tab !== null ? RoomTab.fromPartial(object.room_tab) : undefined;
    return message;
  }
};

function createBaseUpdateRoomTabRsp(): UpdateRoomTabRsp {
  return {};
}

export const UpdateRoomTabRsp: MessageFns<UpdateRoomTabRsp> = {
  fromJSON(_: any): UpdateRoomTabRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomTabRsp>, I>>(base?: I): UpdateRoomTabRsp {
    return UpdateRoomTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTabRsp>, I>>(_: I): UpdateRoomTabRsp {
    const message = createBaseUpdateRoomTabRsp();
    return message;
  }
};

function createBaseDeleteRoomTabReq(): DeleteRoomTabReq {
  return { room_tab_ids: [] };
}

export const DeleteRoomTabReq: MessageFns<DeleteRoomTabReq> = {
  fromJSON(object: any): DeleteRoomTabReq {
    return {
      room_tab_ids: globalThis.Array.isArray(object?.room_tab_ids)
        ? object.room_tab_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteRoomTabReq>, I>>(base?: I): DeleteRoomTabReq {
    return DeleteRoomTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomTabReq>, I>>(object: I): DeleteRoomTabReq {
    const message = createBaseDeleteRoomTabReq();
    message.room_tab_ids = object.room_tab_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteRoomTabRsp(): DeleteRoomTabRsp {
  return {};
}

export const DeleteRoomTabRsp: MessageFns<DeleteRoomTabRsp> = {
  fromJSON(_: any): DeleteRoomTabRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomTabRsp>, I>>(base?: I): DeleteRoomTabRsp {
    return DeleteRoomTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomTabRsp>, I>>(_: I): DeleteRoomTabRsp {
    const message = createBaseDeleteRoomTabRsp();
    return message;
  }
};

function createBaseUpdateRoomTabPublishedReq(): UpdateRoomTabPublishedReq {
  return { room_tab_ids: [], published: false };
}

export const UpdateRoomTabPublishedReq: MessageFns<UpdateRoomTabPublishedReq> = {
  fromJSON(object: any): UpdateRoomTabPublishedReq {
    return {
      room_tab_ids: globalThis.Array.isArray(object?.room_tab_ids)
        ? object.room_tab_ids.map((e: any) => globalThis.Number(e))
        : [],
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomTabPublishedReq>, I>>(base?: I): UpdateRoomTabPublishedReq {
    return UpdateRoomTabPublishedReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTabPublishedReq>, I>>(object: I): UpdateRoomTabPublishedReq {
    const message = createBaseUpdateRoomTabPublishedReq();
    message.room_tab_ids = object.room_tab_ids?.map(e => e) || [];
    message.published = object.published ?? false;
    return message;
  }
};

function createBaseUpdateRoomTabPublishedRsp(): UpdateRoomTabPublishedRsp {
  return {};
}

export const UpdateRoomTabPublishedRsp: MessageFns<UpdateRoomTabPublishedRsp> = {
  fromJSON(_: any): UpdateRoomTabPublishedRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomTabPublishedRsp>, I>>(base?: I): UpdateRoomTabPublishedRsp {
    return UpdateRoomTabPublishedRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTabPublishedRsp>, I>>(_: I): UpdateRoomTabPublishedRsp {
    const message = createBaseUpdateRoomTabPublishedRsp();
    return message;
  }
};

function createBaseRoomDistribution(): RoomDistribution {
  return {
    id: 0,
    room_tab_id: 0,
    room_ids: [],
    room_id: 0,
    room_name: '',
    sort_mode: 0,
    sort_weight: 0,
    status: 0,
    valid_from: 0,
    valid_to: 0,
    create_at: 0,
    create_by: '',
    update_at: 0,
    update_by: '',
    delete_at: 0,
    delete_by: ''
  };
}

export const RoomDistribution: MessageFns<RoomDistribution> = {
  fromJSON(object: any): RoomDistribution {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      room_tab_id: isSet(object.room_tab_id) ? globalThis.Number(object.room_tab_id) : 0,
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_name: isSet(object.room_name) ? globalThis.String(object.room_name) : '',
      sort_mode: isSet(object.sort_mode) ? roomDistributionModeFromJSON(object.sort_mode) : 0,
      sort_weight: isSet(object.sort_weight) ? globalThis.Number(object.sort_weight) : 0,
      status: isSet(object.status) ? roomDistributionStatusFromJSON(object.status) : 0,
      valid_from: isSet(object.valid_from) ? globalThis.Number(object.valid_from) : 0,
      valid_to: isSet(object.valid_to) ? globalThis.Number(object.valid_to) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_at: isSet(object.delete_at) ? globalThis.Number(object.delete_at) : 0,
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomDistribution>, I>>(base?: I): RoomDistribution {
    return RoomDistribution.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomDistribution>, I>>(object: I): RoomDistribution {
    const message = createBaseRoomDistribution();
    message.id = object.id ?? 0;
    message.room_tab_id = object.room_tab_id ?? 0;
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.room_id = object.room_id ?? 0;
    message.room_name = object.room_name ?? '';
    message.sort_mode = object.sort_mode ?? 0;
    message.sort_weight = object.sort_weight ?? 0;
    message.status = object.status ?? 0;
    message.valid_from = object.valid_from ?? 0;
    message.valid_to = object.valid_to ?? 0;
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.delete_at = object.delete_at ?? 0;
    message.delete_by = object.delete_by ?? '';
    return message;
  }
};

function createBaseSearchRoomDistributionReq(): SearchRoomDistributionReq {
  return {
    page: undefined,
    room_id: 0,
    room_tab_id: 0,
    sort_mode: 0,
    operate_by: '',
    operate_at_start: 0,
    operate_at_end: 0,
    status: []
  };
}

export const SearchRoomDistributionReq: MessageFns<SearchRoomDistributionReq> = {
  fromJSON(object: any): SearchRoomDistributionReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_tab_id: isSet(object.room_tab_id) ? globalThis.Number(object.room_tab_id) : 0,
      sort_mode: isSet(object.sort_mode) ? roomDistributionModeFromJSON(object.sort_mode) : 0,
      operate_by: isSet(object.operate_by) ? globalThis.String(object.operate_by) : '',
      operate_at_start: isSet(object.operate_at_start) ? globalThis.Number(object.operate_at_start) : 0,
      operate_at_end: isSet(object.operate_at_end) ? globalThis.Number(object.operate_at_end) : 0,
      status: globalThis.Array.isArray(object?.status)
        ? object.status.map((e: any) => roomDistributionStatusFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomDistributionReq>, I>>(base?: I): SearchRoomDistributionReq {
    return SearchRoomDistributionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomDistributionReq>, I>>(object: I): SearchRoomDistributionReq {
    const message = createBaseSearchRoomDistributionReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    message.room_tab_id = object.room_tab_id ?? 0;
    message.sort_mode = object.sort_mode ?? 0;
    message.operate_by = object.operate_by ?? '';
    message.operate_at_start = object.operate_at_start ?? 0;
    message.operate_at_end = object.operate_at_end ?? 0;
    message.status = object.status?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRoomDistributionRsp(): SearchRoomDistributionRsp {
  return { page: undefined, data: [] };
}

export const SearchRoomDistributionRsp: MessageFns<SearchRoomDistributionRsp> = {
  fromJSON(object: any): SearchRoomDistributionRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RoomDistribution.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomDistributionRsp>, I>>(base?: I): SearchRoomDistributionRsp {
    return SearchRoomDistributionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomDistributionRsp>, I>>(object: I): SearchRoomDistributionRsp {
    const message = createBaseSearchRoomDistributionRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => RoomDistribution.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomDistributionReq(): CreateRoomDistributionReq {
  return { room_distribution: undefined };
}

export const CreateRoomDistributionReq: MessageFns<CreateRoomDistributionReq> = {
  fromJSON(object: any): CreateRoomDistributionReq {
    return {
      room_distribution: isSet(object.room_distribution)
        ? RoomDistribution.fromJSON(object.room_distribution)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<CreateRoomDistributionReq>, I>>(base?: I): CreateRoomDistributionReq {
    return CreateRoomDistributionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomDistributionReq>, I>>(object: I): CreateRoomDistributionReq {
    const message = createBaseCreateRoomDistributionReq();
    message.room_distribution =
      object.room_distribution !== undefined && object.room_distribution !== null
        ? RoomDistribution.fromPartial(object.room_distribution)
        : undefined;
    return message;
  }
};

function createBaseCreateRoomDistributionRsp(): CreateRoomDistributionRsp {
  return {};
}

export const CreateRoomDistributionRsp: MessageFns<CreateRoomDistributionRsp> = {
  fromJSON(_: any): CreateRoomDistributionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateRoomDistributionRsp>, I>>(base?: I): CreateRoomDistributionRsp {
    return CreateRoomDistributionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomDistributionRsp>, I>>(_: I): CreateRoomDistributionRsp {
    const message = createBaseCreateRoomDistributionRsp();
    return message;
  }
};

function createBaseUpdateRoomDistributionReq(): UpdateRoomDistributionReq {
  return { room_distribution: undefined };
}

export const UpdateRoomDistributionReq: MessageFns<UpdateRoomDistributionReq> = {
  fromJSON(object: any): UpdateRoomDistributionReq {
    return {
      room_distribution: isSet(object.room_distribution)
        ? RoomDistribution.fromJSON(object.room_distribution)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<UpdateRoomDistributionReq>, I>>(base?: I): UpdateRoomDistributionReq {
    return UpdateRoomDistributionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomDistributionReq>, I>>(object: I): UpdateRoomDistributionReq {
    const message = createBaseUpdateRoomDistributionReq();
    message.room_distribution =
      object.room_distribution !== undefined && object.room_distribution !== null
        ? RoomDistribution.fromPartial(object.room_distribution)
        : undefined;
    return message;
  }
};

function createBaseUpdateRoomDistributionRsp(): UpdateRoomDistributionRsp {
  return {};
}

export const UpdateRoomDistributionRsp: MessageFns<UpdateRoomDistributionRsp> = {
  fromJSON(_: any): UpdateRoomDistributionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomDistributionRsp>, I>>(base?: I): UpdateRoomDistributionRsp {
    return UpdateRoomDistributionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomDistributionRsp>, I>>(_: I): UpdateRoomDistributionRsp {
    const message = createBaseUpdateRoomDistributionRsp();
    return message;
  }
};

function createBaseDeleteRoomDistributionReq(): DeleteRoomDistributionReq {
  return { room_distribution_ids: [] };
}

export const DeleteRoomDistributionReq: MessageFns<DeleteRoomDistributionReq> = {
  fromJSON(object: any): DeleteRoomDistributionReq {
    return {
      room_distribution_ids: globalThis.Array.isArray(object?.room_distribution_ids)
        ? object.room_distribution_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteRoomDistributionReq>, I>>(base?: I): DeleteRoomDistributionReq {
    return DeleteRoomDistributionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomDistributionReq>, I>>(object: I): DeleteRoomDistributionReq {
    const message = createBaseDeleteRoomDistributionReq();
    message.room_distribution_ids = object.room_distribution_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteRoomDistributionRsp(): DeleteRoomDistributionRsp {
  return {};
}

export const DeleteRoomDistributionRsp: MessageFns<DeleteRoomDistributionRsp> = {
  fromJSON(_: any): DeleteRoomDistributionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomDistributionRsp>, I>>(base?: I): DeleteRoomDistributionRsp {
    return DeleteRoomDistributionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomDistributionRsp>, I>>(_: I): DeleteRoomDistributionRsp {
    const message = createBaseDeleteRoomDistributionRsp();
    return message;
  }
};

function createBaseRoomTag(): RoomTag {
  return {
    id: 0,
    room_ids: [],
    room_id: 0,
    room_name: '',
    name: '',
    i18n_icons: {},
    remark: '',
    status: 0,
    valid_from: 0,
    valid_to: 0,
    create_at: 0,
    create_by: '',
    update_at: 0,
    update_by: '',
    delete_at: 0,
    delete_by: ''
  };
}

export const RoomTag: MessageFns<RoomTag> = {
  fromJSON(object: any): RoomTag {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_name: isSet(object.room_name) ? globalThis.String(object.room_name) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_icons: isObject(object.i18n_icons)
        ? Object.entries(object.i18n_icons).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? roomTagStatusFromJSON(object.status) : 0,
      valid_from: isSet(object.valid_from) ? globalThis.Number(object.valid_from) : 0,
      valid_to: isSet(object.valid_to) ? globalThis.Number(object.valid_to) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_at: isSet(object.delete_at) ? globalThis.Number(object.delete_at) : 0,
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTag>, I>>(base?: I): RoomTag {
    return RoomTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTag>, I>>(object: I): RoomTag {
    const message = createBaseRoomTag();
    message.id = object.id ?? 0;
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.room_id = object.room_id ?? 0;
    message.room_name = object.room_name ?? '';
    message.name = object.name ?? '';
    message.i18n_icons = Object.entries(object.i18n_icons ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.remark = object.remark ?? '';
    message.status = object.status ?? 0;
    message.valid_from = object.valid_from ?? 0;
    message.valid_to = object.valid_to ?? 0;
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.delete_at = object.delete_at ?? 0;
    message.delete_by = object.delete_by ?? '';
    return message;
  }
};

function createBaseRoomTag_I18nIconsEntry(): RoomTag_I18nIconsEntry {
  return { key: '', value: '' };
}

export const RoomTag_I18nIconsEntry: MessageFns<RoomTag_I18nIconsEntry> = {
  fromJSON(object: any): RoomTag_I18nIconsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTag_I18nIconsEntry>, I>>(base?: I): RoomTag_I18nIconsEntry {
    return RoomTag_I18nIconsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTag_I18nIconsEntry>, I>>(object: I): RoomTag_I18nIconsEntry {
    const message = createBaseRoomTag_I18nIconsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSearchRoomTagReq(): SearchRoomTagReq {
  return { page: undefined, room_id: 0, operate_by: '', operate_at_start: 0, operate_at_end: 0 };
}

export const SearchRoomTagReq: MessageFns<SearchRoomTagReq> = {
  fromJSON(object: any): SearchRoomTagReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      operate_by: isSet(object.operate_by) ? globalThis.String(object.operate_by) : '',
      operate_at_start: isSet(object.operate_at_start) ? globalThis.Number(object.operate_at_start) : 0,
      operate_at_end: isSet(object.operate_at_end) ? globalThis.Number(object.operate_at_end) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomTagReq>, I>>(base?: I): SearchRoomTagReq {
    return SearchRoomTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomTagReq>, I>>(object: I): SearchRoomTagReq {
    const message = createBaseSearchRoomTagReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    message.operate_by = object.operate_by ?? '';
    message.operate_at_start = object.operate_at_start ?? 0;
    message.operate_at_end = object.operate_at_end ?? 0;
    return message;
  }
};

function createBaseSearchRoomTagRsp(): SearchRoomTagRsp {
  return { page: undefined, data: [] };
}

export const SearchRoomTagRsp: MessageFns<SearchRoomTagRsp> = {
  fromJSON(object: any): SearchRoomTagRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RoomTag.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomTagRsp>, I>>(base?: I): SearchRoomTagRsp {
    return SearchRoomTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomTagRsp>, I>>(object: I): SearchRoomTagRsp {
    const message = createBaseSearchRoomTagRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => RoomTag.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateRoomTagReq(): CreateRoomTagReq {
  return { room_tag: undefined };
}

export const CreateRoomTagReq: MessageFns<CreateRoomTagReq> = {
  fromJSON(object: any): CreateRoomTagReq {
    return { room_tag: isSet(object.room_tag) ? RoomTag.fromJSON(object.room_tag) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateRoomTagReq>, I>>(base?: I): CreateRoomTagReq {
    return CreateRoomTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomTagReq>, I>>(object: I): CreateRoomTagReq {
    const message = createBaseCreateRoomTagReq();
    message.room_tag =
      object.room_tag !== undefined && object.room_tag !== null ? RoomTag.fromPartial(object.room_tag) : undefined;
    return message;
  }
};

function createBaseCreateRoomTagRsp(): CreateRoomTagRsp {
  return {};
}

export const CreateRoomTagRsp: MessageFns<CreateRoomTagRsp> = {
  fromJSON(_: any): CreateRoomTagRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateRoomTagRsp>, I>>(base?: I): CreateRoomTagRsp {
    return CreateRoomTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomTagRsp>, I>>(_: I): CreateRoomTagRsp {
    const message = createBaseCreateRoomTagRsp();
    return message;
  }
};

function createBaseUpdateRoomTagReq(): UpdateRoomTagReq {
  return { room_tag: undefined };
}

export const UpdateRoomTagReq: MessageFns<UpdateRoomTagReq> = {
  fromJSON(object: any): UpdateRoomTagReq {
    return { room_tag: isSet(object.room_tag) ? RoomTag.fromJSON(object.room_tag) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateRoomTagReq>, I>>(base?: I): UpdateRoomTagReq {
    return UpdateRoomTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTagReq>, I>>(object: I): UpdateRoomTagReq {
    const message = createBaseUpdateRoomTagReq();
    message.room_tag =
      object.room_tag !== undefined && object.room_tag !== null ? RoomTag.fromPartial(object.room_tag) : undefined;
    return message;
  }
};

function createBaseUpdateRoomTagRsp(): UpdateRoomTagRsp {
  return {};
}

export const UpdateRoomTagRsp: MessageFns<UpdateRoomTagRsp> = {
  fromJSON(_: any): UpdateRoomTagRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRoomTagRsp>, I>>(base?: I): UpdateRoomTagRsp {
    return UpdateRoomTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRoomTagRsp>, I>>(_: I): UpdateRoomTagRsp {
    const message = createBaseUpdateRoomTagRsp();
    return message;
  }
};

function createBaseDeleteRoomTagReq(): DeleteRoomTagReq {
  return { room_tag_ids: [] };
}

export const DeleteRoomTagReq: MessageFns<DeleteRoomTagReq> = {
  fromJSON(object: any): DeleteRoomTagReq {
    return {
      room_tag_ids: globalThis.Array.isArray(object?.room_tag_ids)
        ? object.room_tag_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteRoomTagReq>, I>>(base?: I): DeleteRoomTagReq {
    return DeleteRoomTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomTagReq>, I>>(object: I): DeleteRoomTagReq {
    const message = createBaseDeleteRoomTagReq();
    message.room_tag_ids = object.room_tag_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteRoomTagRsp(): DeleteRoomTagRsp {
  return {};
}

export const DeleteRoomTagRsp: MessageFns<DeleteRoomTagRsp> = {
  fromJSON(_: any): DeleteRoomTagRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomTagRsp>, I>>(base?: I): DeleteRoomTagRsp {
    return DeleteRoomTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomTagRsp>, I>>(_: I): DeleteRoomTagRsp {
    const message = createBaseDeleteRoomTagRsp();
    return message;
  }
};

export type RoomListMgrDefinition = typeof RoomListMgrDefinition;
export const RoomListMgrDefinition = {
  name: 'RoomListMgr',
  fullName: 'mgrroomlist.RoomListMgr',
  methods: {
    /** 搜索房间tab */
    searchRoomTab: {
      name: 'SearchRoomTab',
      requestType: SearchRoomTabReq,
      requestStream: false,
      responseType: SearchRoomTabRsp,
      responseStream: false,
      options: {}
    },
    /** 创建房间tab */
    createRoomTab: {
      name: 'CreateRoomTab',
      requestType: CreateRoomTabReq,
      requestStream: false,
      responseType: CreateRoomTabRsp,
      responseStream: false,
      options: {}
    },
    /** 更新房间tab */
    updateRoomTab: {
      name: 'UpdateRoomTab',
      requestType: UpdateRoomTabReq,
      requestStream: false,
      responseType: UpdateRoomTabRsp,
      responseStream: false,
      options: {}
    },
    /** 删除房间tab */
    deleteRoomTab: {
      name: 'DeleteRoomTab',
      requestType: DeleteRoomTabReq,
      requestStream: false,
      responseType: DeleteRoomTabRsp,
      responseStream: false,
      options: {}
    },
    /** 更新房间tab发布状态 */
    updateRoomTabPublished: {
      name: 'UpdateRoomTabPublished',
      requestType: UpdateRoomTabPublishedReq,
      requestStream: false,
      responseType: UpdateRoomTabPublishedRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索房间分发（暂时不用） */
    searchRoomDistribution: {
      name: 'SearchRoomDistribution',
      requestType: SearchRoomDistributionReq,
      requestStream: false,
      responseType: SearchRoomDistributionRsp,
      responseStream: false,
      options: {}
    },
    /** 创建房间分发（暂时不用） */
    createRoomDistribution: {
      name: 'CreateRoomDistribution',
      requestType: CreateRoomDistributionReq,
      requestStream: false,
      responseType: CreateRoomDistributionRsp,
      responseStream: false,
      options: {}
    },
    /** 更新房间分发 */
    updateRoomDistribution: {
      name: 'UpdateRoomDistribution',
      requestType: UpdateRoomDistributionReq,
      requestStream: false,
      responseType: UpdateRoomDistributionRsp,
      responseStream: false,
      options: {}
    },
    /** 删除房间分发（暂时不用） */
    deleteRoomDistribution: {
      name: 'DeleteRoomDistribution',
      requestType: DeleteRoomDistributionReq,
      requestStream: false,
      responseType: DeleteRoomDistributionRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索房间tag */
    searchRoomTag: {
      name: 'SearchRoomTag',
      requestType: SearchRoomTagReq,
      requestStream: false,
      responseType: SearchRoomTagRsp,
      responseStream: false,
      options: {}
    },
    /** 创建房间tag */
    createRoomTag: {
      name: 'CreateRoomTag',
      requestType: CreateRoomTagReq,
      requestStream: false,
      responseType: CreateRoomTagRsp,
      responseStream: false,
      options: {}
    },
    /** 更新房间tag */
    updateRoomTag: {
      name: 'UpdateRoomTag',
      requestType: UpdateRoomTagReq,
      requestStream: false,
      responseType: UpdateRoomTagRsp,
      responseStream: false,
      options: {}
    },
    /** 删除房间tag */
    deleteRoomTag: {
      name: 'DeleteRoomTag',
      requestType: DeleteRoomTagReq,
      requestStream: false,
      responseType: DeleteRoomTagRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
