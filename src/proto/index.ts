/* eslint-disable @typescript-eslint/ban-types */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { DEFAULT_LOCALE, DEFAULT_LOCALE_KEY } from '@/configs';
import { i18n } from '@/configs/locale';
import { isAppWebview } from '@/modules/utils';
import * as Sentry from '@sentry/react';
import <PERSON><PERSON> from 'js-cookie';
import { Toast } from 'react-vant';
import { Key, SWRResponse, default as swr } from 'swr';
import { SWRMutationResponse, default as swrMutate } from 'swr/mutation';
import { AnchorDefinition } from './api/anchor';

import { AccountSvrDefinition } from './api/account';
import { UserDefinition } from './protobuf/api/account/user';
import { SpayDefinition } from './protobuf/api/pay/pay';
import { GoodsDefinition } from './protobuf/api/revenue/goods';
import { WalletDefinition } from './protobuf/api/revenue/wallet';
import { AppRequest, CrushURequestError, DebugRequest, RequestContext, RequestImpl, RequestOptions } from './request';

type LooseObject<T> =
  T extends globalThis.Array<infer U>
    ? globalThis.Array<LooseObject<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<LooseObject<U>>
      : T extends {}
        ? { [K in keyof T]?: LooseObject<T[K]> }
        : Partial<T>;

type InferredMethodsImpl<T extends Record<string, any>> = {
  [K in keyof T]: {
    request: (
      data: T[K]['requestType'] extends { fromJSON: (obj: any) => infer REQ } ? LooseObject<REQ> : unknown,
      options?: RequestOptions
    ) => Promise<T[K]['responseType'] extends { fromJSON: (obj: any) => infer RES } ? RES : unknown>;

    mutate: () => SWRMutationResponse<
      T[K]['responseType'] extends { fromJSON: (obj: any) => infer RES } ? RES : unknown,
      unknown,
      Key,
      T[K]['requestType'] extends { fromJSON: (obj: any) => infer REQ } ? LooseObject<REQ> : unknown
    >;

    query: (
      params: T[K]['requestType'] extends { fromJSON: (obj: any) => infer REQ } ? LooseObject<REQ> : unknown,
      isValidOrConfig?: boolean | { enabled?: boolean }
    ) => SWRResponse<T[K]['responseType'] extends { fromJSON: (obj: any) => infer RES } ? RES : unknown>;
  };
};

interface DefinitionShape {
  name: string;
  fullName: string;
  methods: Record<
    string,
    {
      name: string;
      requestType: {
        fromJSON: (obj: any) => any;
      };
      requestStream: false;
      responseType: {
        fromJSON: (obj: any) => any;
      };
      responseStream: false;
      options: {};
    }
  >;
}

function resolveErrorMessage(err: CrushURequestError) {
  const code = err.code;
  const ns = 'component.errorNetCode';
  const nsMessage = 'component.errorNetMessage';
  const t = i18n.getFixedT(i18n.language, 'translation');
  const key = `${ns}.${code.toString()}`;
  const message = t(key);

  if (message !== key) {
    return message;
  }

  if (!err.message) {
    return null;
  }

  if (err.message.includes('timeout')) {
    return t(`${nsMessage}.timeout`);
  }

  if (err.message.includes('404')) {
    return t(`${ns}.404`);
  }

  if (err.message.includes('CLEARTEXT')) {
    return t(`${nsMessage}.clearText`);
  }

  if (err.message.includes('Failed to connect to')) {
    return t(`${ns}.100009`);
  }

  return message === key ? null : message;
}

function requestFactory<T extends DefinitionShape>(def: T, reqImpl: RequestImpl): InferredMethodsImpl<T['methods']> {
  return Object.entries(def['methods']).reduce(
    (acc, [methodName, method]) => {
      const reqMethod = `${def.name}.${method.name}`;
      const request = async (data: any, options?: RequestOptions) => {
        try {
          console.log('【debug】request:', reqMethod, data, options);

          const resp = await reqImpl.request(reqMethod, method.requestType.fromJSON(data), options);
          return method.responseType.fromJSON(resp);
        } catch (e) {
          const error = CrushURequestError.from(e);
          const resolvedMessage = resolveErrorMessage(error);
          const errorContext = {
            extra: {
              method: reqMethod,
              req: data
            }
          };

          Toast({ message: resolvedMessage ?? error.message });
          if (options?.sentryCapture) {
            Sentry.captureException(error, errorContext);
          } else {
            Sentry.addBreadcrumb({ type: 'request-error' }, errorContext);
          }

          throw error;
        }
      };
      return {
        ...acc,
        [methodName]: {
          request,
          query: (params, isValidOrConfig = { enabled: true }) => {
            const normalizedConfig =
              typeof isValidOrConfig === 'boolean' ? { enabled: isValidOrConfig } : isValidOrConfig;
            return swr(normalizedConfig.enabled ? [reqMethod, params] : null, () =>
              request(params, { sentryCapture: true })
            );
          },
          mutate: () => {
            return swrMutate([reqMethod], (_, data) => {
              return request(data.arg, { sentryCapture: true });
            });
          }
        }
      };
    },
    {} as InferredMethodsImpl<T['methods']>
  );
}

const globalConfig = {
  entry: import.meta.env.VITE_BACKEND_API_ENTRY,
  wsEntry: import.meta.env.VITE_BACKEND_WS_ENTRY,
  service: '',
  publicParams: {
    lan: Cookie.get(DEFAULT_LOCALE_KEY) || DEFAULT_LOCALE
  }
};

const getRequestImpl = (reqCtx: Partial<RequestContext>) => {
  const ctx = Object.assign({}, globalConfig, reqCtx);
  return new DebugRequest(ctx);
  return isAppWebview() ? new AppRequest(ctx) : new DebugRequest(ctx);
};

// 请严格遵循目录结构写方法，未来这里会做生成代码
// 使用驼峰命名
export const Revenue = {
  revenueSpay: requestFactory(SpayDefinition, getRequestImpl({ service: 'api.micro.social.revenue' })),
  revenueGoods: requestFactory(GoodsDefinition, getRequestImpl({ service: 'api.micro.social.revenue' })),
  revenueWallet: requestFactory(WalletDefinition, getRequestImpl({ service: 'api.micro.social.revenue' }))
};
export const BZ = {
  account: requestFactory(AccountSvrDefinition, getRequestImpl({ service: 'crushu' })),
  anchor: requestFactory(AnchorDefinition, getRequestImpl({ service: 'crushu' })),
  user: requestFactory(UserDefinition, getRequestImpl({ service: '' }))
};
