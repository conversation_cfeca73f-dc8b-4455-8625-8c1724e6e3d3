// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/chat.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'chat';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/chatsrv */

export interface JudgeVideoStrategyReq {
  /** live:直播间 */
  scene: string;
  fuid: string;
  action: string;
  sid: string;
  fake_video_id: string;
  mid: string;
  live_id: string;
  room_id: string;
  no_need_agora_token: boolean;
  second_source: string;
}

export interface JudgeVideoStrategyResp {
  /** 策略，1:速配,2:假视频 */
  strategy: number;
  /** 假视频url */
  fake_video_url: string;
  /** 假视频id */
  fake_video_id: number;
}

export interface GetNormalFaceSnapPathReq {
  sid: string;
  /** 待上传:uploading,已上传:uploaded */
  action: string;
}

export interface GetNormalFaceSnapPathResp {
  path: string;
}

/** 获取通话反馈弹窗 */
export interface GetChatFeedBackReq {
  sid: string;
  /** 0尝试挂断1挂断反馈 */
  scene: number;
}

export interface GetChatFeedBackResp {
  show: boolean;
  fb_list: GetChatFeedBackItem[];
  reword_num: number;
  reword_type: number;
}

export interface GetChatFeedBackItem {
  key: string;
  val: string;
  emoji: string;
}

export interface ReportChatFeedBackReq {
  sid: string;
  key: string;
  /** 0尝试挂断1挂断反馈 */
  scene: number;
}

export interface ReportChatFeedBackResp {}

export interface GetVideoASecReq {
  sid: string;
  ossVideoPath: string;
}

export interface GetVideoASecResp {
  secs: number;
}

export interface GetVideoFreeDurationReq {
  sid: string;
}

export interface GetVideoFreeDurationResp {
  free_duration: number;
}

export interface GetChatListReq {
  /** history */
  action: string;
  page: Page | undefined;
}

export interface GetChatListResp {
  page: Page | undefined;
  /** 聊天用户uid */
  uids: string[];
}

function createBaseJudgeVideoStrategyReq(): JudgeVideoStrategyReq {
  return {
    scene: '',
    fuid: '',
    action: '',
    sid: '',
    fake_video_id: '',
    mid: '',
    live_id: '',
    room_id: '',
    no_need_agora_token: false,
    second_source: ''
  };
}

export const JudgeVideoStrategyReq: MessageFns<JudgeVideoStrategyReq> = {
  fromJSON(object: any): JudgeVideoStrategyReq {
    return {
      scene: isSet(object.scene) ? globalThis.String(object.scene) : '',
      fuid: isSet(object.fuid) ? globalThis.String(object.fuid) : '',
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      fake_video_id: isSet(object.fake_video_id) ? globalThis.String(object.fake_video_id) : '',
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      live_id: isSet(object.live_id) ? globalThis.String(object.live_id) : '',
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      no_need_agora_token: isSet(object.no_need_agora_token) ? globalThis.Boolean(object.no_need_agora_token) : false,
      second_source: isSet(object.second_source) ? globalThis.String(object.second_source) : ''
    };
  },

  create<I extends Exact<DeepPartial<JudgeVideoStrategyReq>, I>>(base?: I): JudgeVideoStrategyReq {
    return JudgeVideoStrategyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JudgeVideoStrategyReq>, I>>(object: I): JudgeVideoStrategyReq {
    const message = createBaseJudgeVideoStrategyReq();
    message.scene = object.scene ?? '';
    message.fuid = object.fuid ?? '';
    message.action = object.action ?? '';
    message.sid = object.sid ?? '';
    message.fake_video_id = object.fake_video_id ?? '';
    message.mid = object.mid ?? '';
    message.live_id = object.live_id ?? '';
    message.room_id = object.room_id ?? '';
    message.no_need_agora_token = object.no_need_agora_token ?? false;
    message.second_source = object.second_source ?? '';
    return message;
  }
};

function createBaseJudgeVideoStrategyResp(): JudgeVideoStrategyResp {
  return { strategy: 0, fake_video_url: '', fake_video_id: 0 };
}

export const JudgeVideoStrategyResp: MessageFns<JudgeVideoStrategyResp> = {
  fromJSON(object: any): JudgeVideoStrategyResp {
    return {
      strategy: isSet(object.strategy) ? globalThis.Number(object.strategy) : 0,
      fake_video_url: isSet(object.fake_video_url) ? globalThis.String(object.fake_video_url) : '',
      fake_video_id: isSet(object.fake_video_id) ? globalThis.Number(object.fake_video_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<JudgeVideoStrategyResp>, I>>(base?: I): JudgeVideoStrategyResp {
    return JudgeVideoStrategyResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JudgeVideoStrategyResp>, I>>(object: I): JudgeVideoStrategyResp {
    const message = createBaseJudgeVideoStrategyResp();
    message.strategy = object.strategy ?? 0;
    message.fake_video_url = object.fake_video_url ?? '';
    message.fake_video_id = object.fake_video_id ?? 0;
    return message;
  }
};

function createBaseGetNormalFaceSnapPathReq(): GetNormalFaceSnapPathReq {
  return { sid: '', action: '' };
}

export const GetNormalFaceSnapPathReq: MessageFns<GetNormalFaceSnapPathReq> = {
  fromJSON(object: any): GetNormalFaceSnapPathReq {
    return {
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      action: isSet(object.action) ? globalThis.String(object.action) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetNormalFaceSnapPathReq>, I>>(base?: I): GetNormalFaceSnapPathReq {
    return GetNormalFaceSnapPathReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNormalFaceSnapPathReq>, I>>(object: I): GetNormalFaceSnapPathReq {
    const message = createBaseGetNormalFaceSnapPathReq();
    message.sid = object.sid ?? '';
    message.action = object.action ?? '';
    return message;
  }
};

function createBaseGetNormalFaceSnapPathResp(): GetNormalFaceSnapPathResp {
  return { path: '' };
}

export const GetNormalFaceSnapPathResp: MessageFns<GetNormalFaceSnapPathResp> = {
  fromJSON(object: any): GetNormalFaceSnapPathResp {
    return { path: isSet(object.path) ? globalThis.String(object.path) : '' };
  },

  create<I extends Exact<DeepPartial<GetNormalFaceSnapPathResp>, I>>(base?: I): GetNormalFaceSnapPathResp {
    return GetNormalFaceSnapPathResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNormalFaceSnapPathResp>, I>>(object: I): GetNormalFaceSnapPathResp {
    const message = createBaseGetNormalFaceSnapPathResp();
    message.path = object.path ?? '';
    return message;
  }
};

function createBaseGetChatFeedBackReq(): GetChatFeedBackReq {
  return { sid: '', scene: 0 };
}

export const GetChatFeedBackReq: MessageFns<GetChatFeedBackReq> = {
  fromJSON(object: any): GetChatFeedBackReq {
    return {
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      scene: isSet(object.scene) ? globalThis.Number(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetChatFeedBackReq>, I>>(base?: I): GetChatFeedBackReq {
    return GetChatFeedBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetChatFeedBackReq>, I>>(object: I): GetChatFeedBackReq {
    const message = createBaseGetChatFeedBackReq();
    message.sid = object.sid ?? '';
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseGetChatFeedBackResp(): GetChatFeedBackResp {
  return { show: false, fb_list: [], reword_num: 0, reword_type: 0 };
}

export const GetChatFeedBackResp: MessageFns<GetChatFeedBackResp> = {
  fromJSON(object: any): GetChatFeedBackResp {
    return {
      show: isSet(object.show) ? globalThis.Boolean(object.show) : false,
      fb_list: globalThis.Array.isArray(object?.fb_list)
        ? object.fb_list.map((e: any) => GetChatFeedBackItem.fromJSON(e))
        : [],
      reword_num: isSet(object.reword_num) ? globalThis.Number(object.reword_num) : 0,
      reword_type: isSet(object.reword_type) ? globalThis.Number(object.reword_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetChatFeedBackResp>, I>>(base?: I): GetChatFeedBackResp {
    return GetChatFeedBackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetChatFeedBackResp>, I>>(object: I): GetChatFeedBackResp {
    const message = createBaseGetChatFeedBackResp();
    message.show = object.show ?? false;
    message.fb_list = object.fb_list?.map(e => GetChatFeedBackItem.fromPartial(e)) || [];
    message.reword_num = object.reword_num ?? 0;
    message.reword_type = object.reword_type ?? 0;
    return message;
  }
};

function createBaseGetChatFeedBackItem(): GetChatFeedBackItem {
  return { key: '', val: '', emoji: '' };
}

export const GetChatFeedBackItem: MessageFns<GetChatFeedBackItem> = {
  fromJSON(object: any): GetChatFeedBackItem {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      val: isSet(object.val) ? globalThis.String(object.val) : '',
      emoji: isSet(object.emoji) ? globalThis.String(object.emoji) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetChatFeedBackItem>, I>>(base?: I): GetChatFeedBackItem {
    return GetChatFeedBackItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetChatFeedBackItem>, I>>(object: I): GetChatFeedBackItem {
    const message = createBaseGetChatFeedBackItem();
    message.key = object.key ?? '';
    message.val = object.val ?? '';
    message.emoji = object.emoji ?? '';
    return message;
  }
};

function createBaseReportChatFeedBackReq(): ReportChatFeedBackReq {
  return { sid: '', key: '', scene: 0 };
}

export const ReportChatFeedBackReq: MessageFns<ReportChatFeedBackReq> = {
  fromJSON(object: any): ReportChatFeedBackReq {
    return {
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      scene: isSet(object.scene) ? globalThis.Number(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<ReportChatFeedBackReq>, I>>(base?: I): ReportChatFeedBackReq {
    return ReportChatFeedBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportChatFeedBackReq>, I>>(object: I): ReportChatFeedBackReq {
    const message = createBaseReportChatFeedBackReq();
    message.sid = object.sid ?? '';
    message.key = object.key ?? '';
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseReportChatFeedBackResp(): ReportChatFeedBackResp {
  return {};
}

export const ReportChatFeedBackResp: MessageFns<ReportChatFeedBackResp> = {
  fromJSON(_: any): ReportChatFeedBackResp {
    return {};
  },

  create<I extends Exact<DeepPartial<ReportChatFeedBackResp>, I>>(base?: I): ReportChatFeedBackResp {
    return ReportChatFeedBackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportChatFeedBackResp>, I>>(_: I): ReportChatFeedBackResp {
    const message = createBaseReportChatFeedBackResp();
    return message;
  }
};

function createBaseGetVideoASecReq(): GetVideoASecReq {
  return { sid: '', ossVideoPath: '' };
}

export const GetVideoASecReq: MessageFns<GetVideoASecReq> = {
  fromJSON(object: any): GetVideoASecReq {
    return {
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      ossVideoPath: isSet(object.ossVideoPath) ? globalThis.String(object.ossVideoPath) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetVideoASecReq>, I>>(base?: I): GetVideoASecReq {
    return GetVideoASecReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoASecReq>, I>>(object: I): GetVideoASecReq {
    const message = createBaseGetVideoASecReq();
    message.sid = object.sid ?? '';
    message.ossVideoPath = object.ossVideoPath ?? '';
    return message;
  }
};

function createBaseGetVideoASecResp(): GetVideoASecResp {
  return { secs: 0 };
}

export const GetVideoASecResp: MessageFns<GetVideoASecResp> = {
  fromJSON(object: any): GetVideoASecResp {
    return { secs: isSet(object.secs) ? globalThis.Number(object.secs) : 0 };
  },

  create<I extends Exact<DeepPartial<GetVideoASecResp>, I>>(base?: I): GetVideoASecResp {
    return GetVideoASecResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoASecResp>, I>>(object: I): GetVideoASecResp {
    const message = createBaseGetVideoASecResp();
    message.secs = object.secs ?? 0;
    return message;
  }
};

function createBaseGetVideoFreeDurationReq(): GetVideoFreeDurationReq {
  return { sid: '' };
}

export const GetVideoFreeDurationReq: MessageFns<GetVideoFreeDurationReq> = {
  fromJSON(object: any): GetVideoFreeDurationReq {
    return { sid: isSet(object.sid) ? globalThis.String(object.sid) : '' };
  },

  create<I extends Exact<DeepPartial<GetVideoFreeDurationReq>, I>>(base?: I): GetVideoFreeDurationReq {
    return GetVideoFreeDurationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoFreeDurationReq>, I>>(object: I): GetVideoFreeDurationReq {
    const message = createBaseGetVideoFreeDurationReq();
    message.sid = object.sid ?? '';
    return message;
  }
};

function createBaseGetVideoFreeDurationResp(): GetVideoFreeDurationResp {
  return { free_duration: 0 };
}

export const GetVideoFreeDurationResp: MessageFns<GetVideoFreeDurationResp> = {
  fromJSON(object: any): GetVideoFreeDurationResp {
    return { free_duration: isSet(object.free_duration) ? globalThis.Number(object.free_duration) : 0 };
  },

  create<I extends Exact<DeepPartial<GetVideoFreeDurationResp>, I>>(base?: I): GetVideoFreeDurationResp {
    return GetVideoFreeDurationResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoFreeDurationResp>, I>>(object: I): GetVideoFreeDurationResp {
    const message = createBaseGetVideoFreeDurationResp();
    message.free_duration = object.free_duration ?? 0;
    return message;
  }
};

function createBaseGetChatListReq(): GetChatListReq {
  return { action: '', page: undefined };
}

export const GetChatListReq: MessageFns<GetChatListReq> = {
  fromJSON(object: any): GetChatListReq {
    return {
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetChatListReq>, I>>(base?: I): GetChatListReq {
    return GetChatListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetChatListReq>, I>>(object: I): GetChatListReq {
    const message = createBaseGetChatListReq();
    message.action = object.action ?? '';
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetChatListResp(): GetChatListResp {
  return { page: undefined, uids: [] };
}

export const GetChatListResp: MessageFns<GetChatListResp> = {
  fromJSON(object: any): GetChatListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetChatListResp>, I>>(base?: I): GetChatListResp {
    return GetChatListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetChatListResp>, I>>(object: I): GetChatListResp {
    const message = createBaseGetChatListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

export type ChatDefinition = typeof ChatDefinition;
export const ChatDefinition = {
  name: 'Chat',
  fullName: 'chat.Chat',
  methods: {
    judgeVideoStrategy: {
      name: 'JudgeVideoStrategy',
      requestType: JudgeVideoStrategyReq,
      requestStream: false,
      responseType: JudgeVideoStrategyResp,
      responseStream: false,
      options: {}
    },
    getNormalFaceSnapPath: {
      name: 'GetNormalFaceSnapPath',
      requestType: GetNormalFaceSnapPathReq,
      requestStream: false,
      responseType: GetNormalFaceSnapPathResp,
      responseStream: false,
      options: {}
    },
    getChatFeedBack: {
      name: 'GetChatFeedBack',
      requestType: GetChatFeedBackReq,
      requestStream: false,
      responseType: GetChatFeedBackResp,
      responseStream: false,
      options: {}
    },
    reportChatFeedBack: {
      name: 'ReportChatFeedBack',
      requestType: ReportChatFeedBackReq,
      requestStream: false,
      responseType: ReportChatFeedBackResp,
      responseStream: false,
      options: {}
    },
    getVideoASec: {
      name: 'GetVideoASec',
      requestType: GetVideoASecReq,
      requestStream: false,
      responseType: GetVideoASecResp,
      responseStream: false,
      options: {}
    },
    /** 获取聊天用户 */
    getChatList: {
      name: 'GetChatList',
      requestType: GetChatListReq,
      requestStream: false,
      responseType: GetChatListResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
