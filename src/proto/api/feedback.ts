// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/feedback.proto

/* eslint-disable */

export const protobufPackage = 'feedback';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/feedback/handler */

/** 被举报对象类型 */
export enum FeedbackTargetType {
  FEEDBACK_TARGET_TYPE_NONE = 0,
  /** FEEDBACK_TARGET_TYPE_ROOM - 房间 */
  FEEDBACK_TARGET_TYPE_ROOM = 1,
  UNRECOGNIZED = -1
}

export function feedbackTargetTypeFromJSON(object: any): FeedbackTargetType {
  switch (object) {
    case 0:
    case 'FEEDBACK_TARGET_TYPE_NONE':
      return FeedbackTargetType.FEEDBACK_TARGET_TYPE_NONE;
    case 1:
    case 'FEEDBACK_TARGET_TYPE_ROOM':
      return FeedbackTargetType.FEEDBACK_TARGET_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return FeedbackTargetType.UNRECOGNIZED;
  }
}

/** 举报场景 */
export enum FeedbackSceneCommonType {
  FEEDBACK_SCENE_COMMON_NONE = 0,
  /** FEEDBACK_SCENE_TYPE_IM - 聊天 */
  FEEDBACK_SCENE_TYPE_IM = 1,
  /** FEEDBACK_SCENE_TYPE_POSTS - 帖子 */
  FEEDBACK_SCENE_TYPE_POSTS = 2,
  /** FEEDBACK_SCENE_TYPE_POSTS_COMMENT - 帖子评论 */
  FEEDBACK_SCENE_TYPE_POSTS_COMMENT = 3,
  /** FEEDBACK_SCENE_TYPE_ROOM - 房间举报 */
  FEEDBACK_SCENE_TYPE_ROOM = 4,
  /** FEEDBACK_SCENE_TYPE_USER_DETAIL - 资料页 */
  FEEDBACK_SCENE_TYPE_USER_DETAIL = 5,
  /** FEEDBACK_SCENE_TYPE_ROOM_INNER - 房间内 */
  FEEDBACK_SCENE_TYPE_ROOM_INNER = 6,
  UNRECOGNIZED = -1
}

export function feedbackSceneCommonTypeFromJSON(object: any): FeedbackSceneCommonType {
  switch (object) {
    case 0:
    case 'FEEDBACK_SCENE_COMMON_NONE':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_COMMON_NONE;
    case 1:
    case 'FEEDBACK_SCENE_TYPE_IM':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_IM;
    case 2:
    case 'FEEDBACK_SCENE_TYPE_POSTS':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_POSTS;
    case 3:
    case 'FEEDBACK_SCENE_TYPE_POSTS_COMMENT':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_POSTS_COMMENT;
    case 4:
    case 'FEEDBACK_SCENE_TYPE_ROOM':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_ROOM;
    case 5:
    case 'FEEDBACK_SCENE_TYPE_USER_DETAIL':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_USER_DETAIL;
    case 6:
    case 'FEEDBACK_SCENE_TYPE_ROOM_INNER':
      return FeedbackSceneCommonType.FEEDBACK_SCENE_TYPE_ROOM_INNER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return FeedbackSceneCommonType.UNRECOGNIZED;
  }
}

export interface FeedbackCategory {
  id: number;
  /** 举报分类名称 */
  name: string;
}

export interface ListFeedbackCategoryReq {
  /** 被举报对象类型(1:房间) */
  target_type: FeedbackTargetType;
}

export interface ListFeedbackCategoryRsp {
  /** 举报分类列表 */
  feedback_categories: FeedbackCategory[];
}

export interface CreateFeedbackReq {
  /** 被举报对象类型(1:房间) */
  target_type: FeedbackTargetType;
  /** 被举报对象Id */
  target_id: string;
  /** 举报类型Ids */
  feedback_category_ids: number[];
  /** 举报图片 */
  report_img_urls: string[];
  /** 举报描述 */
  report_description: string;
}

export interface CreateFeedbackRsp {
  id: number;
}

/** csae 需求新增举报 */
export interface ListCommonFeedBackCategoryRsp {
  list: ListCommonFeedBackCategory[];
}

export interface ListCommonFeedBackCategory {
  id: number;
  /** 通用举报类型 （Violence、Advertisements、Pornography、Race or Religion、Infringement、Child Sexual Abuse and Exploitation） */
  category_name: string;
}

export interface ListCommonFeedBackCategoryReq {}

export interface CommonFeedBackReq {
  /** 场景 */
  scene: FeedbackSceneCommonType;
  /** 分类id 上面的接口返回的 */
  category_id: number;
  /** 被举报人id */
  fuid: string;
  /** 被内容Id（可选）  聊天场景就是uid 帖子就是post_id 帖子评论就是评论id 房间场景就是房间id 资料页和房间内就是uid */
  business_id: string;
}

export interface CommonFeedBackRsp {}

function createBaseFeedbackCategory(): FeedbackCategory {
  return { id: 0, name: '' };
}

export const FeedbackCategory: MessageFns<FeedbackCategory> = {
  fromJSON(object: any): FeedbackCategory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<FeedbackCategory>, I>>(base?: I): FeedbackCategory {
    return FeedbackCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeedbackCategory>, I>>(object: I): FeedbackCategory {
    const message = createBaseFeedbackCategory();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListFeedbackCategoryReq(): ListFeedbackCategoryReq {
  return { target_type: 0 };
}

export const ListFeedbackCategoryReq: MessageFns<ListFeedbackCategoryReq> = {
  fromJSON(object: any): ListFeedbackCategoryReq {
    return { target_type: isSet(object.target_type) ? feedbackTargetTypeFromJSON(object.target_type) : 0 };
  },

  create<I extends Exact<DeepPartial<ListFeedbackCategoryReq>, I>>(base?: I): ListFeedbackCategoryReq {
    return ListFeedbackCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListFeedbackCategoryReq>, I>>(object: I): ListFeedbackCategoryReq {
    const message = createBaseListFeedbackCategoryReq();
    message.target_type = object.target_type ?? 0;
    return message;
  }
};

function createBaseListFeedbackCategoryRsp(): ListFeedbackCategoryRsp {
  return { feedback_categories: [] };
}

export const ListFeedbackCategoryRsp: MessageFns<ListFeedbackCategoryRsp> = {
  fromJSON(object: any): ListFeedbackCategoryRsp {
    return {
      feedback_categories: globalThis.Array.isArray(object?.feedback_categories)
        ? object.feedback_categories.map((e: any) => FeedbackCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListFeedbackCategoryRsp>, I>>(base?: I): ListFeedbackCategoryRsp {
    return ListFeedbackCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListFeedbackCategoryRsp>, I>>(object: I): ListFeedbackCategoryRsp {
    const message = createBaseListFeedbackCategoryRsp();
    message.feedback_categories = object.feedback_categories?.map(e => FeedbackCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateFeedbackReq(): CreateFeedbackReq {
  return { target_type: 0, target_id: '', feedback_category_ids: [], report_img_urls: [], report_description: '' };
}

export const CreateFeedbackReq: MessageFns<CreateFeedbackReq> = {
  fromJSON(object: any): CreateFeedbackReq {
    return {
      target_type: isSet(object.target_type) ? feedbackTargetTypeFromJSON(object.target_type) : 0,
      target_id: isSet(object.target_id) ? globalThis.String(object.target_id) : '',
      feedback_category_ids: globalThis.Array.isArray(object?.feedback_category_ids)
        ? object.feedback_category_ids.map((e: any) => globalThis.Number(e))
        : [],
      report_img_urls: globalThis.Array.isArray(object?.report_img_urls)
        ? object.report_img_urls.map((e: any) => globalThis.String(e))
        : [],
      report_description: isSet(object.report_description) ? globalThis.String(object.report_description) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateFeedbackReq>, I>>(base?: I): CreateFeedbackReq {
    return CreateFeedbackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFeedbackReq>, I>>(object: I): CreateFeedbackReq {
    const message = createBaseCreateFeedbackReq();
    message.target_type = object.target_type ?? 0;
    message.target_id = object.target_id ?? '';
    message.feedback_category_ids = object.feedback_category_ids?.map(e => e) || [];
    message.report_img_urls = object.report_img_urls?.map(e => e) || [];
    message.report_description = object.report_description ?? '';
    return message;
  }
};

function createBaseCreateFeedbackRsp(): CreateFeedbackRsp {
  return { id: 0 };
}

export const CreateFeedbackRsp: MessageFns<CreateFeedbackRsp> = {
  fromJSON(object: any): CreateFeedbackRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateFeedbackRsp>, I>>(base?: I): CreateFeedbackRsp {
    return CreateFeedbackRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFeedbackRsp>, I>>(object: I): CreateFeedbackRsp {
    const message = createBaseCreateFeedbackRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListCommonFeedBackCategoryRsp(): ListCommonFeedBackCategoryRsp {
  return { list: [] };
}

export const ListCommonFeedBackCategoryRsp: MessageFns<ListCommonFeedBackCategoryRsp> = {
  fromJSON(object: any): ListCommonFeedBackCategoryRsp {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => ListCommonFeedBackCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListCommonFeedBackCategoryRsp>, I>>(base?: I): ListCommonFeedBackCategoryRsp {
    return ListCommonFeedBackCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCommonFeedBackCategoryRsp>, I>>(
    object: I
  ): ListCommonFeedBackCategoryRsp {
    const message = createBaseListCommonFeedBackCategoryRsp();
    message.list = object.list?.map(e => ListCommonFeedBackCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListCommonFeedBackCategory(): ListCommonFeedBackCategory {
  return { id: 0, category_name: '' };
}

export const ListCommonFeedBackCategory: MessageFns<ListCommonFeedBackCategory> = {
  fromJSON(object: any): ListCommonFeedBackCategory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category_name: isSet(object.category_name) ? globalThis.String(object.category_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListCommonFeedBackCategory>, I>>(base?: I): ListCommonFeedBackCategory {
    return ListCommonFeedBackCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCommonFeedBackCategory>, I>>(object: I): ListCommonFeedBackCategory {
    const message = createBaseListCommonFeedBackCategory();
    message.id = object.id ?? 0;
    message.category_name = object.category_name ?? '';
    return message;
  }
};

function createBaseListCommonFeedBackCategoryReq(): ListCommonFeedBackCategoryReq {
  return {};
}

export const ListCommonFeedBackCategoryReq: MessageFns<ListCommonFeedBackCategoryReq> = {
  fromJSON(_: any): ListCommonFeedBackCategoryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListCommonFeedBackCategoryReq>, I>>(base?: I): ListCommonFeedBackCategoryReq {
    return ListCommonFeedBackCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCommonFeedBackCategoryReq>, I>>(_: I): ListCommonFeedBackCategoryReq {
    const message = createBaseListCommonFeedBackCategoryReq();
    return message;
  }
};

function createBaseCommonFeedBackReq(): CommonFeedBackReq {
  return { scene: 0, category_id: 0, fuid: '', business_id: '' };
}

export const CommonFeedBackReq: MessageFns<CommonFeedBackReq> = {
  fromJSON(object: any): CommonFeedBackReq {
    return {
      scene: isSet(object.scene) ? feedbackSceneCommonTypeFromJSON(object.scene) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      fuid: isSet(object.fuid) ? globalThis.String(object.fuid) : '',
      business_id: isSet(object.business_id) ? globalThis.String(object.business_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<CommonFeedBackReq>, I>>(base?: I): CommonFeedBackReq {
    return CommonFeedBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommonFeedBackReq>, I>>(object: I): CommonFeedBackReq {
    const message = createBaseCommonFeedBackReq();
    message.scene = object.scene ?? 0;
    message.category_id = object.category_id ?? 0;
    message.fuid = object.fuid ?? '';
    message.business_id = object.business_id ?? '';
    return message;
  }
};

function createBaseCommonFeedBackRsp(): CommonFeedBackRsp {
  return {};
}

export const CommonFeedBackRsp: MessageFns<CommonFeedBackRsp> = {
  fromJSON(_: any): CommonFeedBackRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CommonFeedBackRsp>, I>>(base?: I): CommonFeedBackRsp {
    return CommonFeedBackRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommonFeedBackRsp>, I>>(_: I): CommonFeedBackRsp {
    const message = createBaseCommonFeedBackRsp();
    return message;
  }
};

/** 举报协议 */
export type FeedbackDefinition = typeof FeedbackDefinition;
export const FeedbackDefinition = {
  name: 'Feedback',
  fullName: 'feedback.Feedback',
  methods: {
    /** 举报分类-列表 */
    listFeedbackCategory: {
      name: 'ListFeedbackCategory',
      requestType: ListFeedbackCategoryReq,
      requestStream: false,
      responseType: ListFeedbackCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 举报-新增 */
    createFeedback: {
      name: 'CreateFeedback',
      requestType: CreateFeedbackReq,
      requestStream: false,
      responseType: CreateFeedbackRsp,
      responseStream: false,
      options: {}
    },
    /** 获取通用举报分类 */
    listCommonFeedBackCategory: {
      name: 'ListCommonFeedBackCategory',
      requestType: ListCommonFeedBackCategoryReq,
      requestStream: false,
      responseType: ListCommonFeedBackCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 通用举报 */
    commonFeedBack: {
      name: 'CommonFeedBack',
      requestType: CommonFeedBackReq,
      requestStream: false,
      responseType: CommonFeedBackRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
