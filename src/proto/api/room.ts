// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/room.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';
import {
  EnterHotTopicInfo,
  RoomCategory,
  roomCategoryFromJSON,
  RoomDetail,
  RoomInfo,
  RoomLayout,
  RoomPerm,
  roomPermFromJSON,
  RoomSettings,
  RoomStateInfo,
  RoomUserInfo,
  RtcProvider,
  rtcProviderFromJSON,
  RtmProvider,
  rtmProviderFromJSON,
  SeatList,
  SeatPerm,
  seatPermFromJSON,
  SeatScoreSwitch,
  seatScoreSwitchFromJSON
} from './comm';
import { GiftWishlistRsp } from './gift';
import { GetShareInfoRsp } from './share';
import { UserInfo } from './user';

export const protobufPackage = 'room';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/room/handler/ */

export enum BannerType {
  /** BannerNone -  */
  BannerNone = 0,
  /** BannerGame - 游戏类型 */
  BannerGame = 1,
  /** BannerNormal - 普通类型 */
  BannerNormal = 2,
  UNRECOGNIZED = -1
}

export function bannerTypeFromJSON(object: any): BannerType {
  switch (object) {
    case 0:
    case 'BannerNone':
      return BannerType.BannerNone;
    case 1:
    case 'BannerGame':
      return BannerType.BannerGame;
    case 2:
    case 'BannerNormal':
      return BannerType.BannerNormal;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BannerType.UNRECOGNIZED;
  }
}

export enum StartLivePerm {
  Start_Live_None = 0,
  Start_Live_Perm_Start = 1,
  Start_Live_No_Perm_Start = 2,
  UNRECOGNIZED = -1
}

export function startLivePermFromJSON(object: any): StartLivePerm {
  switch (object) {
    case 0:
    case 'Start_Live_None':
      return StartLivePerm.Start_Live_None;
    case 1:
    case 'Start_Live_Perm_Start':
      return StartLivePerm.Start_Live_Perm_Start;
    case 2:
    case 'Start_Live_No_Perm_Start':
      return StartLivePerm.Start_Live_No_Perm_Start;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return StartLivePerm.UNRECOGNIZED;
  }
}

/** 刷新rtc token */
export interface RefreshRtcTokenReq {
  /** 房间ID */
  room_id: number;
}

export interface RefreshRtcTokenRsp {
  token: RtcToken | undefined;
}

/** 刷新rtc token */
export interface RefreshRtmTokenReq {}

export interface RefreshRtmTokenRsp {
  token: RtmToken | undefined;
}

export interface CreateRoomReq {
  /** 房间名称 */
  name: string;
  /** 房间公告 */
  intro: string;
  /** 房间权限 */
  room_perm: RoomPerm;
  /** 房间布局模式code */
  layout_code: string;
  /** 房间密码 */
  password: string;
  /** 房间模式上架id */
  room_mode_publish_id: number;
}

export interface CreateRoomRsp {
  /** 房间号 */
  room_id: number;
}

/** 批量获取房间信息 */
export interface BatchGetRoomsReq {
  /** 房间号 */
  rids: number[];
  selector: RoomInfo | undefined;
}

export interface BatchGetRoomsRsp {
  /** 房间信息 */
  rooms: { [key: number]: RoomInfo };
}

export interface BatchGetRoomsRsp_RoomsEntry {
  key: number;
  value: RoomInfo | undefined;
}

export interface MyRoomReq {}

export interface MyRoomRsp {
  room_info: RoomInfo | undefined;
}

export interface MyManageRoomsReq {
  page: Page | undefined;
}

export interface MyManageRoomsRsp {
  page: Page | undefined;
  /** 我的房间信息 */
  my_room: RoomInfo | undefined;
  /** 我的管理房间信息 */
  my_manage_rooms: RoomInfo[];
}

export interface EnterRoomReq {
  /** 房间ID */
  room_id: number;
  /**
   * enter_source
   *  - recommend_list 推荐列表
   *  - hot_list 热门列表
   *  - banner banner
   *  - push 推送
   *  - 个人资料页 user_profile
   *  - 其他动态返回的房间信息或者列表，后端都返回一个 source，进房时透传过来
   */
  enter_source: string;
  /** 如果是加密房，带上密码 */
  password: string;
  /** 通过分享邀请进入密码房时的 token. */
  invite_token: string;
}

export interface EnterRoomRsp {
  /** rtc token */
  rtc_token: RtcToken | undefined;
  /** 房间信息 */
  room_detail: RoomDetail | undefined;
  /** 用户信息 */
  room_user_info: RoomUserInfo | undefined;
  /** 房间实时状态信息 */
  room_state_info: RoomStateInfo | undefined;
  /** 平台公约 多语言显示 */
  room_convention: { [key: string]: string };
  /** 房主信息 */
  owner_user_info: RoomUserInfo | undefined;
  /** 场次ID */
  live_id: number;
}

export interface EnterRoomRsp_RoomConventionEntry {
  key: string;
  value: string;
}

export interface EnterEntranceInfo {
  /** 跳转路径 */
  deep_link: string;
  /** 资源图片 */
  pic: string;
  /** 游戏id 暂时没用先预留 */
  game_id: string;
  banner_type: BannerType;
}

export interface EnterRoomExtraReq {
  room_id: number;
}

export interface EnterRoomExtraRsp {
  /** 上边生效中的游戏列表 */
  top_entrance_list: EnterEntranceInfo[];
  /** 分享数据 */
  share_info: GetShareInfoRsp | undefined;
  /** 下边生效中的游戏列表 */
  bottom_entrance_list: EnterEntranceInfo[];
  /** 游戏币兑换数据 */
  exchange_info: GetGameExchangeRsp | undefined;
  /** 左上角收益/消费展示 */
  room_consumption_income_info: RoomConsumptionIncomeInfoRsp | undefined;
  /** 心愿榜 */
  gift_wishlist: GiftWishlistRsp | undefined;
  /** 如果进房的是审核版本 返回的是视频地址 */
  LiveLimitVideoUrl: string;
  /** 审核版本视频时长 */
  live_video_duration: number;
  /** ios审核版本才返回 */
  topic_info: EnterHotTopicInfo | undefined;
}

/** 获取心愿礼物列表和rank信息 */
export interface GiftWishlistReq {
  room_id: number;
  live_id: number;
}

export interface RoomConsumptionIncomeInfoRsp {
  /** 消费钻石 */
  consume_coins: number;
  /** 收益积分 */
  revenue_points: number;
}

export interface GetGameExchangeRsp {
  deep_link: string;
  /** 1展示 0不展示 */
  is_show: number;
}

export interface ReenterReq {
  /** 房间号 */
  room_id: number;
}

export interface ReenterRsp {
  /** rtc token */
  rtc_token: RtcToken | undefined;
  /** 房间信息 */
  room_detail: RoomDetail | undefined;
  /** 用户信息 */
  room_user_info: RoomUserInfo | undefined;
  /** 房间实时状态信息 */
  room_state_info: RoomStateInfo | undefined;
}

export interface GetRoomStateReq {
  room_id: number;
}

export interface GetRoomStateRsp {
  /** 房间信息 */
  room_detail: RoomDetail | undefined;
  /** 房间实时状态信息 */
  room_state_info: RoomStateInfo | undefined;
  /** 房主信息 */
  owner: RoomUserInfo | undefined;
}

/** 离开房间，房间id通过登录用户uid来获取 */
export interface LeaveReq {
  /** 房间号 */
  room_id: number;
  /** 是否解散，false不解散，true解散 */
  is_close: boolean;
  /** 场次ID */
  live_id: number;
}

export interface LeaveRsp {}

export interface KickOffReq {
  /** 房间ID */
  room_id: number;
  /** 被踢用户uid */
  target_uid: number;
  /** 拉黑时长 单位-秒，-1 永久 */
  block_time: number;
}

export interface KickOffRsp {}

export interface ChangeRoomPermReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 房间权限 */
  room_perm: RoomPerm;
  /** 房间密码，修改为密码房时需要 */
  password: string;
}

export interface ChangeRoomPermRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置管更 */
  room_settings: RoomSettings | undefined;
}

export interface ChangeLayoutReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 房间布局模式code */
  layout_code: string;
}

export interface ChangeLayoutRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置管更 */
  room_settings: RoomSettings | undefined;
  /** 变更后的麦位列表，这里会返回空数据，如果以后做麦位保留，会返回保留的麦位列表 */
  seat_list: SeatList | undefined;
}

export interface ChangeRoomModeReq {
  /** 房间id */
  room_id: number;
  /** 房间模式上架id */
  room_mode_publish_id: number;
}

export interface ChangeRoomModeRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置变更 */
  room_settings: RoomSettings | undefined;
  /** 房间的实时状态信息 */
  room_state_info: RoomStateInfo | undefined;
}

export interface ChangeSeatPermReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 上麦权限 */
  seat_perm: SeatPerm;
}

export interface ChangeSeatPermRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置管更 */
  room_settings: RoomSettings | undefined;
}

export interface ChangeSeatScoreSwitchReq {
  /** 房间id */
  room_id: number;
  /** 更新开关状态 */
  switch: SeatScoreSwitch;
}

export interface ChangeSeatScoreSwitchRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置管 */
  room_settings: RoomSettings | undefined;
}

export interface ListRoomAdminReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
}

export interface ListRoomAdminRsp {
  /** 管理员列表 */
  admin_list: RoomUserInfo[];
}

export interface SetRoomAdminReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 用户ID */
  target_uid: number;
}

export interface SetRoomAdminRsp {}

export interface RemoveRoomAdminReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 用户ID */
  target_uid: number;
}

/** 移除房间管理 */
export interface RemoveRoomAdminRsp {}

export interface SetRoomNameReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 房间名称 */
  name: string;
}

export interface SetRoomNameRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间信息 */
  room_info: RoomInfo | undefined;
}

export interface SetRoomIntroReq {
  /** 房间id，虽然通过uid能查到，这里带上房间id，如果以后其他人也能修改或者一个人有多个房间时可以区分 */
  room_id: number;
  /** 房间公告 */
  intro: string;
}

export interface SetRoomIntroRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间信息 */
  room_info: RoomInfo | undefined;
}

export interface SetRoomCoverReq {
  /** 房间id */
  room_id: number;
  /** 房间封面url */
  cover: string;
}

export interface SetRoomCoverRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间信息 */
  room_info: RoomInfo | undefined;
}

export interface SetRoomBackgroundReq {
  /** 房间ID */
  room_id: number;
  /** 房间背景 */
  bg_img: string;
  /** 房间 */
  bg_svga: string;
  /** 背景权益商品ID */
  goods_id: number;
}

export interface SetRoomBackgroundRsp {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间信息 */
  room_info: RoomInfo | undefined;
}

export interface BatchGetRoomUserInfoReq {
  /** 房间ID */
  room_id: number;
  /** 用户ID */
  uids: number[];
  /** 字段选择器 */
  selector: RoomUserInfo | undefined;
  /** 语聊房还是直播房 */
  category: RoomCategory;
}

export interface BatchGetRoomUserInfoRsp {
  /** 用户信息 */
  users: { [key: number]: RoomUserInfo };
}

export interface BatchGetRoomUserInfoRsp_UsersEntry {
  key: number;
  value: RoomUserInfo | undefined;
}

export interface GetUserCurrentRoomReq {
  /** 用户ID */
  uid: number;
}

export interface GetUserCurrentRoomRsp {
  /** 房间ID,值为0:没在房间里 */
  room_id: number;
}

export interface GetRoomRandomNameReq {}

export interface GetRoomRandomNameRsp {
  /** 随机昵称 */
  name: string;
}

export interface RoomTabShowReq {}

export interface RoomTabShowRsp {
  /** 是否展示 */
  is_show: boolean;
}

export interface JudgeCreateRoomPermReq {}

export interface JudgeCreateRoomPermRsp {
  /** 是否允许创建房间  根据错误码弹对应场景弹窗,目前仅有VIP弹窗场景 */
  is_allow: boolean;
}

export interface ListRoomValidMicUserReq {
  /** 房间ID */
  room_id: number;
}

export interface ListRoomValidMicUserRsp {
  /** 用户ID列表 */
  uids: number[];
}

/** rtc token */
export interface RtcToken {
  /** 供应商 */
  rtc_provider: RtcProvider;
  /** App ID */
  app_id: string;
  /** token */
  token: string;
  /** Token 的有效时长（秒） */
  token_expire: number;
}

/** rtm token */
export interface RtmToken {
  /** 供应商 */
  rtm_provider: RtmProvider;
  /** App ID */
  app_id: string;
  /** token */
  token: string;
  /** Token 的有效时长（秒） */
  token_expire: number;
}

/** 麦位列表 */
export interface ListSeatReq {
  /** 房间ID */
  room_id: number;
}

export interface ListSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 上麦 */
export interface EnterSeatReq {
  /** 房间ID */
  room_id: number;
  /** 麦位序号 */
  seat_index: number;
  /** 旧麦位序号;换麦场景 */
  seat_index_old: number;
}

export interface EnterSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 下麦 */
export interface LeaveSeatReq {
  /** 房间ID */
  room_id: number;
  /** 麦位序号 */
  seat_index: number;
}

export interface LeaveSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 麦上用户打开麦克风 */
export interface OpenMicReq {
  /** 房间ID */
  room_id: number;
  /** 麦位序号 */
  seat_index: number;
}

export interface OpenMicRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 麦上用户关闭自己的麦克风 */
export interface CloseMicReq {
  /** 房间ID */
  room_id: number;
  /** 麦位序号 */
  seat_index: number;
}

export interface CloseMicRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 申请上麦 */
export interface ApplyEnterSeatReq {
  /** 房间ID */
  room_id: number;
  /** 指定座位序号 */
  seat_index: number;
}

export interface ApplyEnterSeatRsp {}

/** 获取申请上麦状态 */
export interface GetApplyEnterSeatStatusReq {
  /** 房间ID */
  room_id: number;
}

export interface GetApplyEnterSeatStatusRsp {
  /** 是否有申请上麦 */
  applied: boolean;
}

/** 取消申请上麦 */
export interface CancelApplyEnterSeatReq {
  /** 房间ID */
  room_id: number;
}

export interface CancelApplyEnterSeatRsp {}

/** 用户接受邀请上麦 */
export interface AcceptSeatInvitationReq {
  /** 房间ID */
  room_id: number;
  /** 指定座位序号 */
  seat_index: number;
}

export interface AcceptSeatInvitationRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 用户拒绝邀请上麦 */
export interface RejectSeatInvitationReq {
  /** 房间ID */
  room_id: number;
  /** 指定座位序号 */
  seat_index: number;
}

export interface RejectSeatInvitationRsp {}

/** 麦位封锁 */
export interface LockSeatReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
}

export interface LockSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 解除麦位封锁 */
export interface UnlockSeatReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
}

export interface UnlockSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 麦位禁音 */
export interface MuteSeatReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
}

export interface MuteSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 解除麦位禁音 */
export interface UnmuteSeatReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
}

export interface UnmuteSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 用户禁音 */
export interface MuteSeatUserReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
  /** 用户id */
  uid: number;
}

export interface MuteSeatUserRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 解除用户禁音 */
export interface UnmuteSeatUserReq {
  /** 房间ID */
  room_id: number;
  /** 指定麦位序号 */
  seat_index: number;
  /** 用户id */
  uid: number;
}

export interface UnmuteSeatUserRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 踢下麦 */
export interface KickSeatReq {
  /** 房间ID */
  room_id: number;
  /** 麦位序号 */
  seat_index: number;
  /** 用户id */
  uid: number;
}

export interface KickSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 抱上麦 */
export interface PickUpSeatReq {
  /** 房间ID */
  room_id: number;
  /** 用户id */
  uid: number;
  /** 指定麦位序号 */
  seat_index: number;
}

export interface PickUpSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 申请上麦用户列表 */
export interface ApplyEnterSeatUserListReq {
  page: Page | undefined;
  /** 房间ID */
  room_id: number;
}

export interface ApplyEnterSeatUserListRsp {
  page: Page | undefined;
  /** 用户列表 */
  user_infos: UserInfo[];
}

/** 申请上麦审批-同意 */
export interface ApproveApplyEnterSeatReq {
  /** 房间ID */
  room_id: number;
  /** 申请上麦用户uid */
  uid: number;
}

export interface ApproveApplyEnterSeatRsp {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 申请上麦审批-拒绝 */
export interface RejectApplyEnterSeatReq {
  /** 房间ID */
  room_id: number;
  /** 申请上麦用户uid */
  uid: number;
}

export interface RejectApplyEnterSeatRsp {}

/** 获取在线的人列表 */
export interface ListOnlineUserReq {
  page: Page | undefined;
  /** 房间ID */
  room_id: number;
}

export interface ListOnlineUserRsp {
  page: Page | undefined;
  room_user_infos: RoomUserInfo[];
}

export interface ListLayoutReq {}

export interface ListLayoutRsp {
  /** 房间模式列表，返回第一个为默认布局方式 */
  layout_list: RoomLayout[];
}

export interface TempLeaveReq {
  /** 房间ID */
  room_id: number;
  /** 场次ID */
  live_id: number;
}

export interface TempLeaveRsp {}

export interface ComeBackReq {
  /** 房间ID */
  room_id: number;
  /** 场次ID */
  live_id: number;
}

export interface ComeBackRsp {}

export interface ReceiveFreeGameRewardReq {
  /** 领取id 弹窗消息会下发给你 */
  receive_id: number;
}

/** 返回房间im  游戏id 让用户进去 */
export interface ReceiveFreeGameRewardRsp {
  room_id: number;
  game_id: string;
}

export interface GetFreeGameInfoReq {}

export interface GetFreeGameInfoRsp {
  /** 过期时间戳 往这个时间点倒计时 */
  expired_ts: number;
  /** 免费的游戏币数量 */
  free_game_coin: number;
  /** 图标 */
  free_game_pic: string;
  /** 1展示入口 0不展示入口 */
  show_entrance: number;
}

export interface CheckLivePermReq {}

export interface CheckLivePermRsp {
  perm: StartLivePerm;
  cover: string;
  title: string;
  room_id: number;
}

export interface LiveOnReq {
  /** 是否是重连恢复开播 */
  reconnect: boolean;
  /** 开播封面 */
  cover: string;
  /** 房间title */
  title: string;
}

export interface LiveOnRsp {
  enter_info: EnterRoomRsp | undefined;
}

export interface LiveOffReq {
  /** 房间ID */
  room_id: number;
  /** 场次ID */
  live_id: number;
}

export interface LiveOffRsp {}

export interface GetSettleInfoReq {
  live_id: number;
}

export interface GetSettleInfoRsp {
  /** 直播时长 秒级别的 展示分钟 */
  live_duration: number;
  /** 本场直播 获得积分 */
  live_point: number;
  /** 送礼人数 */
  live_gift_send_num: number;
  /** 新增粉丝 */
  live_follow_num: number;
  /** 观众总数 */
  live_views_num: number;
  /** 通话次数 */
  live_chat_video_num: number;
  /** 平均通话时长 */
  live_chat_avg_time: number;
}

export interface GetLiveHomePageReq {
  page: Page | undefined;
  id: number;
  category: RoomCategory;
}

export interface GetLiveHomePageRsp {
  page: Page | undefined;
  list: RoomInfo[];
}

export interface SetUpWishGiftListReq {
  gift_list: WishGiftConfig[];
  room_id: number;
  live_id: number;
}

export interface WishGiftConfig {
  gift_id: number;
  target: number;
}

export interface SetUpWishGiftListResp {}

function createBaseRefreshRtcTokenReq(): RefreshRtcTokenReq {
  return { room_id: 0 };
}

export const RefreshRtcTokenReq: MessageFns<RefreshRtcTokenReq> = {
  fromJSON(object: any): RefreshRtcTokenReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<RefreshRtcTokenReq>, I>>(base?: I): RefreshRtcTokenReq {
    return RefreshRtcTokenReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshRtcTokenReq>, I>>(object: I): RefreshRtcTokenReq {
    const message = createBaseRefreshRtcTokenReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseRefreshRtcTokenRsp(): RefreshRtcTokenRsp {
  return { token: undefined };
}

export const RefreshRtcTokenRsp: MessageFns<RefreshRtcTokenRsp> = {
  fromJSON(object: any): RefreshRtcTokenRsp {
    return { token: isSet(object.token) ? RtcToken.fromJSON(object.token) : undefined };
  },

  create<I extends Exact<DeepPartial<RefreshRtcTokenRsp>, I>>(base?: I): RefreshRtcTokenRsp {
    return RefreshRtcTokenRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshRtcTokenRsp>, I>>(object: I): RefreshRtcTokenRsp {
    const message = createBaseRefreshRtcTokenRsp();
    message.token =
      object.token !== undefined && object.token !== null ? RtcToken.fromPartial(object.token) : undefined;
    return message;
  }
};

function createBaseRefreshRtmTokenReq(): RefreshRtmTokenReq {
  return {};
}

export const RefreshRtmTokenReq: MessageFns<RefreshRtmTokenReq> = {
  fromJSON(_: any): RefreshRtmTokenReq {
    return {};
  },

  create<I extends Exact<DeepPartial<RefreshRtmTokenReq>, I>>(base?: I): RefreshRtmTokenReq {
    return RefreshRtmTokenReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshRtmTokenReq>, I>>(_: I): RefreshRtmTokenReq {
    const message = createBaseRefreshRtmTokenReq();
    return message;
  }
};

function createBaseRefreshRtmTokenRsp(): RefreshRtmTokenRsp {
  return { token: undefined };
}

export const RefreshRtmTokenRsp: MessageFns<RefreshRtmTokenRsp> = {
  fromJSON(object: any): RefreshRtmTokenRsp {
    return { token: isSet(object.token) ? RtmToken.fromJSON(object.token) : undefined };
  },

  create<I extends Exact<DeepPartial<RefreshRtmTokenRsp>, I>>(base?: I): RefreshRtmTokenRsp {
    return RefreshRtmTokenRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshRtmTokenRsp>, I>>(object: I): RefreshRtmTokenRsp {
    const message = createBaseRefreshRtmTokenRsp();
    message.token =
      object.token !== undefined && object.token !== null ? RtmToken.fromPartial(object.token) : undefined;
    return message;
  }
};

function createBaseCreateRoomReq(): CreateRoomReq {
  return { name: '', intro: '', room_perm: 0, layout_code: '', password: '', room_mode_publish_id: 0 };
}

export const CreateRoomReq: MessageFns<CreateRoomReq> = {
  fromJSON(object: any): CreateRoomReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      intro: isSet(object.intro) ? globalThis.String(object.intro) : '',
      room_perm: isSet(object.room_perm) ? roomPermFromJSON(object.room_perm) : 0,
      layout_code: isSet(object.layout_code) ? globalThis.String(object.layout_code) : '',
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      room_mode_publish_id: isSet(object.room_mode_publish_id) ? globalThis.Number(object.room_mode_publish_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<CreateRoomReq>, I>>(base?: I): CreateRoomReq {
    return CreateRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomReq>, I>>(object: I): CreateRoomReq {
    const message = createBaseCreateRoomReq();
    message.name = object.name ?? '';
    message.intro = object.intro ?? '';
    message.room_perm = object.room_perm ?? 0;
    message.layout_code = object.layout_code ?? '';
    message.password = object.password ?? '';
    message.room_mode_publish_id = object.room_mode_publish_id ?? 0;
    return message;
  }
};

function createBaseCreateRoomRsp(): CreateRoomRsp {
  return { room_id: 0 };
}

export const CreateRoomRsp: MessageFns<CreateRoomRsp> = {
  fromJSON(object: any): CreateRoomRsp {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRoomRsp>, I>>(base?: I): CreateRoomRsp {
    return CreateRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRoomRsp>, I>>(object: I): CreateRoomRsp {
    const message = createBaseCreateRoomRsp();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseBatchGetRoomsReq(): BatchGetRoomsReq {
  return { rids: [], selector: undefined };
}

export const BatchGetRoomsReq: MessageFns<BatchGetRoomsReq> = {
  fromJSON(object: any): BatchGetRoomsReq {
    return {
      rids: globalThis.Array.isArray(object?.rids) ? object.rids.map((e: any) => globalThis.Number(e)) : [],
      selector: isSet(object.selector) ? RoomInfo.fromJSON(object.selector) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomsReq>, I>>(base?: I): BatchGetRoomsReq {
    return BatchGetRoomsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomsReq>, I>>(object: I): BatchGetRoomsReq {
    const message = createBaseBatchGetRoomsReq();
    message.rids = object.rids?.map(e => e) || [];
    message.selector =
      object.selector !== undefined && object.selector !== null ? RoomInfo.fromPartial(object.selector) : undefined;
    return message;
  }
};

function createBaseBatchGetRoomsRsp(): BatchGetRoomsRsp {
  return { rooms: {} };
}

export const BatchGetRoomsRsp: MessageFns<BatchGetRoomsRsp> = {
  fromJSON(object: any): BatchGetRoomsRsp {
    return {
      rooms: isObject(object.rooms)
        ? Object.entries(object.rooms).reduce<{ [key: number]: RoomInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = RoomInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomsRsp>, I>>(base?: I): BatchGetRoomsRsp {
    return BatchGetRoomsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomsRsp>, I>>(object: I): BatchGetRoomsRsp {
    const message = createBaseBatchGetRoomsRsp();
    message.rooms = Object.entries(object.rooms ?? {}).reduce<{ [key: number]: RoomInfo }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = RoomInfo.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetRoomsRsp_RoomsEntry(): BatchGetRoomsRsp_RoomsEntry {
  return { key: 0, value: undefined };
}

export const BatchGetRoomsRsp_RoomsEntry: MessageFns<BatchGetRoomsRsp_RoomsEntry> = {
  fromJSON(object: any): BatchGetRoomsRsp_RoomsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? RoomInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomsRsp_RoomsEntry>, I>>(base?: I): BatchGetRoomsRsp_RoomsEntry {
    return BatchGetRoomsRsp_RoomsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomsRsp_RoomsEntry>, I>>(object: I): BatchGetRoomsRsp_RoomsEntry {
    const message = createBaseBatchGetRoomsRsp_RoomsEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? RoomInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseMyRoomReq(): MyRoomReq {
  return {};
}

export const MyRoomReq: MessageFns<MyRoomReq> = {
  fromJSON(_: any): MyRoomReq {
    return {};
  },

  create<I extends Exact<DeepPartial<MyRoomReq>, I>>(base?: I): MyRoomReq {
    return MyRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyRoomReq>, I>>(_: I): MyRoomReq {
    const message = createBaseMyRoomReq();
    return message;
  }
};

function createBaseMyRoomRsp(): MyRoomRsp {
  return { room_info: undefined };
}

export const MyRoomRsp: MessageFns<MyRoomRsp> = {
  fromJSON(object: any): MyRoomRsp {
    return { room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined };
  },

  create<I extends Exact<DeepPartial<MyRoomRsp>, I>>(base?: I): MyRoomRsp {
    return MyRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyRoomRsp>, I>>(object: I): MyRoomRsp {
    const message = createBaseMyRoomRsp();
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseMyManageRoomsReq(): MyManageRoomsReq {
  return { page: undefined };
}

export const MyManageRoomsReq: MessageFns<MyManageRoomsReq> = {
  fromJSON(object: any): MyManageRoomsReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<MyManageRoomsReq>, I>>(base?: I): MyManageRoomsReq {
    return MyManageRoomsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyManageRoomsReq>, I>>(object: I): MyManageRoomsReq {
    const message = createBaseMyManageRoomsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseMyManageRoomsRsp(): MyManageRoomsRsp {
  return { page: undefined, my_room: undefined, my_manage_rooms: [] };
}

export const MyManageRoomsRsp: MessageFns<MyManageRoomsRsp> = {
  fromJSON(object: any): MyManageRoomsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      my_room: isSet(object.my_room) ? RoomInfo.fromJSON(object.my_room) : undefined,
      my_manage_rooms: globalThis.Array.isArray(object?.my_manage_rooms)
        ? object.my_manage_rooms.map((e: any) => RoomInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<MyManageRoomsRsp>, I>>(base?: I): MyManageRoomsRsp {
    return MyManageRoomsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MyManageRoomsRsp>, I>>(object: I): MyManageRoomsRsp {
    const message = createBaseMyManageRoomsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.my_room =
      object.my_room !== undefined && object.my_room !== null ? RoomInfo.fromPartial(object.my_room) : undefined;
    message.my_manage_rooms = object.my_manage_rooms?.map(e => RoomInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseEnterRoomReq(): EnterRoomReq {
  return { room_id: 0, enter_source: '', password: '', invite_token: '' };
}

export const EnterRoomReq: MessageFns<EnterRoomReq> = {
  fromJSON(object: any): EnterRoomReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      enter_source: isSet(object.enter_source) ? globalThis.String(object.enter_source) : '',
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      invite_token: isSet(object.invite_token) ? globalThis.String(object.invite_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomReq>, I>>(base?: I): EnterRoomReq {
    return EnterRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomReq>, I>>(object: I): EnterRoomReq {
    const message = createBaseEnterRoomReq();
    message.room_id = object.room_id ?? 0;
    message.enter_source = object.enter_source ?? '';
    message.password = object.password ?? '';
    message.invite_token = object.invite_token ?? '';
    return message;
  }
};

function createBaseEnterRoomRsp(): EnterRoomRsp {
  return {
    rtc_token: undefined,
    room_detail: undefined,
    room_user_info: undefined,
    room_state_info: undefined,
    room_convention: {},
    owner_user_info: undefined,
    live_id: 0
  };
}

export const EnterRoomRsp: MessageFns<EnterRoomRsp> = {
  fromJSON(object: any): EnterRoomRsp {
    return {
      rtc_token: isSet(object.rtc_token) ? RtcToken.fromJSON(object.rtc_token) : undefined,
      room_detail: isSet(object.room_detail) ? RoomDetail.fromJSON(object.room_detail) : undefined,
      room_user_info: isSet(object.room_user_info) ? RoomUserInfo.fromJSON(object.room_user_info) : undefined,
      room_state_info: isSet(object.room_state_info) ? RoomStateInfo.fromJSON(object.room_state_info) : undefined,
      room_convention: isObject(object.room_convention)
        ? Object.entries(object.room_convention).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      owner_user_info: isSet(object.owner_user_info) ? RoomUserInfo.fromJSON(object.owner_user_info) : undefined,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomRsp>, I>>(base?: I): EnterRoomRsp {
    return EnterRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomRsp>, I>>(object: I): EnterRoomRsp {
    const message = createBaseEnterRoomRsp();
    message.rtc_token =
      object.rtc_token !== undefined && object.rtc_token !== null ? RtcToken.fromPartial(object.rtc_token) : undefined;
    message.room_detail =
      object.room_detail !== undefined && object.room_detail !== null
        ? RoomDetail.fromPartial(object.room_detail)
        : undefined;
    message.room_user_info =
      object.room_user_info !== undefined && object.room_user_info !== null
        ? RoomUserInfo.fromPartial(object.room_user_info)
        : undefined;
    message.room_state_info =
      object.room_state_info !== undefined && object.room_state_info !== null
        ? RoomStateInfo.fromPartial(object.room_state_info)
        : undefined;
    message.room_convention = Object.entries(object.room_convention ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.owner_user_info =
      object.owner_user_info !== undefined && object.owner_user_info !== null
        ? RoomUserInfo.fromPartial(object.owner_user_info)
        : undefined;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseEnterRoomRsp_RoomConventionEntry(): EnterRoomRsp_RoomConventionEntry {
  return { key: '', value: '' };
}

export const EnterRoomRsp_RoomConventionEntry: MessageFns<EnterRoomRsp_RoomConventionEntry> = {
  fromJSON(object: any): EnterRoomRsp_RoomConventionEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomRsp_RoomConventionEntry>, I>>(
    base?: I
  ): EnterRoomRsp_RoomConventionEntry {
    return EnterRoomRsp_RoomConventionEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomRsp_RoomConventionEntry>, I>>(
    object: I
  ): EnterRoomRsp_RoomConventionEntry {
    const message = createBaseEnterRoomRsp_RoomConventionEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseEnterEntranceInfo(): EnterEntranceInfo {
  return { deep_link: '', pic: '', game_id: '', banner_type: 0 };
}

export const EnterEntranceInfo: MessageFns<EnterEntranceInfo> = {
  fromJSON(object: any): EnterEntranceInfo {
    return {
      deep_link: isSet(object.deep_link) ? globalThis.String(object.deep_link) : '',
      pic: isSet(object.pic) ? globalThis.String(object.pic) : '',
      game_id: isSet(object.game_id) ? globalThis.String(object.game_id) : '',
      banner_type: isSet(object.banner_type) ? bannerTypeFromJSON(object.banner_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<EnterEntranceInfo>, I>>(base?: I): EnterEntranceInfo {
    return EnterEntranceInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterEntranceInfo>, I>>(object: I): EnterEntranceInfo {
    const message = createBaseEnterEntranceInfo();
    message.deep_link = object.deep_link ?? '';
    message.pic = object.pic ?? '';
    message.game_id = object.game_id ?? '';
    message.banner_type = object.banner_type ?? 0;
    return message;
  }
};

function createBaseEnterRoomExtraReq(): EnterRoomExtraReq {
  return { room_id: 0 };
}

export const EnterRoomExtraReq: MessageFns<EnterRoomExtraReq> = {
  fromJSON(object: any): EnterRoomExtraReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<EnterRoomExtraReq>, I>>(base?: I): EnterRoomExtraReq {
    return EnterRoomExtraReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomExtraReq>, I>>(object: I): EnterRoomExtraReq {
    const message = createBaseEnterRoomExtraReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseEnterRoomExtraRsp(): EnterRoomExtraRsp {
  return {
    top_entrance_list: [],
    share_info: undefined,
    bottom_entrance_list: [],
    exchange_info: undefined,
    room_consumption_income_info: undefined,
    gift_wishlist: undefined,
    LiveLimitVideoUrl: '',
    live_video_duration: 0,
    topic_info: undefined
  };
}

export const EnterRoomExtraRsp: MessageFns<EnterRoomExtraRsp> = {
  fromJSON(object: any): EnterRoomExtraRsp {
    return {
      top_entrance_list: globalThis.Array.isArray(object?.top_entrance_list)
        ? object.top_entrance_list.map((e: any) => EnterEntranceInfo.fromJSON(e))
        : [],
      share_info: isSet(object.share_info) ? GetShareInfoRsp.fromJSON(object.share_info) : undefined,
      bottom_entrance_list: globalThis.Array.isArray(object?.bottom_entrance_list)
        ? object.bottom_entrance_list.map((e: any) => EnterEntranceInfo.fromJSON(e))
        : [],
      exchange_info: isSet(object.exchange_info) ? GetGameExchangeRsp.fromJSON(object.exchange_info) : undefined,
      room_consumption_income_info: isSet(object.room_consumption_income_info)
        ? RoomConsumptionIncomeInfoRsp.fromJSON(object.room_consumption_income_info)
        : undefined,
      gift_wishlist: isSet(object.gift_wishlist) ? GiftWishlistRsp.fromJSON(object.gift_wishlist) : undefined,
      LiveLimitVideoUrl: isSet(object.LiveLimitVideoUrl) ? globalThis.String(object.LiveLimitVideoUrl) : '',
      live_video_duration: isSet(object.live_video_duration) ? globalThis.Number(object.live_video_duration) : 0,
      topic_info: isSet(object.topic_info) ? EnterHotTopicInfo.fromJSON(object.topic_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomExtraRsp>, I>>(base?: I): EnterRoomExtraRsp {
    return EnterRoomExtraRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomExtraRsp>, I>>(object: I): EnterRoomExtraRsp {
    const message = createBaseEnterRoomExtraRsp();
    message.top_entrance_list = object.top_entrance_list?.map(e => EnterEntranceInfo.fromPartial(e)) || [];
    message.share_info =
      object.share_info !== undefined && object.share_info !== null
        ? GetShareInfoRsp.fromPartial(object.share_info)
        : undefined;
    message.bottom_entrance_list = object.bottom_entrance_list?.map(e => EnterEntranceInfo.fromPartial(e)) || [];
    message.exchange_info =
      object.exchange_info !== undefined && object.exchange_info !== null
        ? GetGameExchangeRsp.fromPartial(object.exchange_info)
        : undefined;
    message.room_consumption_income_info =
      object.room_consumption_income_info !== undefined && object.room_consumption_income_info !== null
        ? RoomConsumptionIncomeInfoRsp.fromPartial(object.room_consumption_income_info)
        : undefined;
    message.gift_wishlist =
      object.gift_wishlist !== undefined && object.gift_wishlist !== null
        ? GiftWishlistRsp.fromPartial(object.gift_wishlist)
        : undefined;
    message.LiveLimitVideoUrl = object.LiveLimitVideoUrl ?? '';
    message.live_video_duration = object.live_video_duration ?? 0;
    message.topic_info =
      object.topic_info !== undefined && object.topic_info !== null
        ? EnterHotTopicInfo.fromPartial(object.topic_info)
        : undefined;
    return message;
  }
};

function createBaseGiftWishlistReq(): GiftWishlistReq {
  return { room_id: 0, live_id: 0 };
}

export const GiftWishlistReq: MessageFns<GiftWishlistReq> = {
  fromJSON(object: any): GiftWishlistReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftWishlistReq>, I>>(base?: I): GiftWishlistReq {
    return GiftWishlistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftWishlistReq>, I>>(object: I): GiftWishlistReq {
    const message = createBaseGiftWishlistReq();
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseRoomConsumptionIncomeInfoRsp(): RoomConsumptionIncomeInfoRsp {
  return { consume_coins: 0, revenue_points: 0 };
}

export const RoomConsumptionIncomeInfoRsp: MessageFns<RoomConsumptionIncomeInfoRsp> = {
  fromJSON(object: any): RoomConsumptionIncomeInfoRsp {
    return {
      consume_coins: isSet(object.consume_coins) ? globalThis.Number(object.consume_coins) : 0,
      revenue_points: isSet(object.revenue_points) ? globalThis.Number(object.revenue_points) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomConsumptionIncomeInfoRsp>, I>>(base?: I): RoomConsumptionIncomeInfoRsp {
    return RoomConsumptionIncomeInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomConsumptionIncomeInfoRsp>, I>>(object: I): RoomConsumptionIncomeInfoRsp {
    const message = createBaseRoomConsumptionIncomeInfoRsp();
    message.consume_coins = object.consume_coins ?? 0;
    message.revenue_points = object.revenue_points ?? 0;
    return message;
  }
};

function createBaseGetGameExchangeRsp(): GetGameExchangeRsp {
  return { deep_link: '', is_show: 0 };
}

export const GetGameExchangeRsp: MessageFns<GetGameExchangeRsp> = {
  fromJSON(object: any): GetGameExchangeRsp {
    return {
      deep_link: isSet(object.deep_link) ? globalThis.String(object.deep_link) : '',
      is_show: isSet(object.is_show) ? globalThis.Number(object.is_show) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetGameExchangeRsp>, I>>(base?: I): GetGameExchangeRsp {
    return GetGameExchangeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGameExchangeRsp>, I>>(object: I): GetGameExchangeRsp {
    const message = createBaseGetGameExchangeRsp();
    message.deep_link = object.deep_link ?? '';
    message.is_show = object.is_show ?? 0;
    return message;
  }
};

function createBaseReenterReq(): ReenterReq {
  return { room_id: 0 };
}

export const ReenterReq: MessageFns<ReenterReq> = {
  fromJSON(object: any): ReenterReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ReenterReq>, I>>(base?: I): ReenterReq {
    return ReenterReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReenterReq>, I>>(object: I): ReenterReq {
    const message = createBaseReenterReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseReenterRsp(): ReenterRsp {
  return { rtc_token: undefined, room_detail: undefined, room_user_info: undefined, room_state_info: undefined };
}

export const ReenterRsp: MessageFns<ReenterRsp> = {
  fromJSON(object: any): ReenterRsp {
    return {
      rtc_token: isSet(object.rtc_token) ? RtcToken.fromJSON(object.rtc_token) : undefined,
      room_detail: isSet(object.room_detail) ? RoomDetail.fromJSON(object.room_detail) : undefined,
      room_user_info: isSet(object.room_user_info) ? RoomUserInfo.fromJSON(object.room_user_info) : undefined,
      room_state_info: isSet(object.room_state_info) ? RoomStateInfo.fromJSON(object.room_state_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ReenterRsp>, I>>(base?: I): ReenterRsp {
    return ReenterRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReenterRsp>, I>>(object: I): ReenterRsp {
    const message = createBaseReenterRsp();
    message.rtc_token =
      object.rtc_token !== undefined && object.rtc_token !== null ? RtcToken.fromPartial(object.rtc_token) : undefined;
    message.room_detail =
      object.room_detail !== undefined && object.room_detail !== null
        ? RoomDetail.fromPartial(object.room_detail)
        : undefined;
    message.room_user_info =
      object.room_user_info !== undefined && object.room_user_info !== null
        ? RoomUserInfo.fromPartial(object.room_user_info)
        : undefined;
    message.room_state_info =
      object.room_state_info !== undefined && object.room_state_info !== null
        ? RoomStateInfo.fromPartial(object.room_state_info)
        : undefined;
    return message;
  }
};

function createBaseGetRoomStateReq(): GetRoomStateReq {
  return { room_id: 0 };
}

export const GetRoomStateReq: MessageFns<GetRoomStateReq> = {
  fromJSON(object: any): GetRoomStateReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRoomStateReq>, I>>(base?: I): GetRoomStateReq {
    return GetRoomStateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomStateReq>, I>>(object: I): GetRoomStateReq {
    const message = createBaseGetRoomStateReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetRoomStateRsp(): GetRoomStateRsp {
  return { room_detail: undefined, room_state_info: undefined, owner: undefined };
}

export const GetRoomStateRsp: MessageFns<GetRoomStateRsp> = {
  fromJSON(object: any): GetRoomStateRsp {
    return {
      room_detail: isSet(object.room_detail) ? RoomDetail.fromJSON(object.room_detail) : undefined,
      room_state_info: isSet(object.room_state_info) ? RoomStateInfo.fromJSON(object.room_state_info) : undefined,
      owner: isSet(object.owner) ? RoomUserInfo.fromJSON(object.owner) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetRoomStateRsp>, I>>(base?: I): GetRoomStateRsp {
    return GetRoomStateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomStateRsp>, I>>(object: I): GetRoomStateRsp {
    const message = createBaseGetRoomStateRsp();
    message.room_detail =
      object.room_detail !== undefined && object.room_detail !== null
        ? RoomDetail.fromPartial(object.room_detail)
        : undefined;
    message.room_state_info =
      object.room_state_info !== undefined && object.room_state_info !== null
        ? RoomStateInfo.fromPartial(object.room_state_info)
        : undefined;
    message.owner =
      object.owner !== undefined && object.owner !== null ? RoomUserInfo.fromPartial(object.owner) : undefined;
    return message;
  }
};

function createBaseLeaveReq(): LeaveReq {
  return { room_id: 0, is_close: false, live_id: 0 };
}

export const LeaveReq: MessageFns<LeaveReq> = {
  fromJSON(object: any): LeaveReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      is_close: isSet(object.is_close) ? globalThis.Boolean(object.is_close) : false,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<LeaveReq>, I>>(base?: I): LeaveReq {
    return LeaveReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaveReq>, I>>(object: I): LeaveReq {
    const message = createBaseLeaveReq();
    message.room_id = object.room_id ?? 0;
    message.is_close = object.is_close ?? false;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseLeaveRsp(): LeaveRsp {
  return {};
}

export const LeaveRsp: MessageFns<LeaveRsp> = {
  fromJSON(_: any): LeaveRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<LeaveRsp>, I>>(base?: I): LeaveRsp {
    return LeaveRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaveRsp>, I>>(_: I): LeaveRsp {
    const message = createBaseLeaveRsp();
    return message;
  }
};

function createBaseKickOffReq(): KickOffReq {
  return { room_id: 0, target_uid: 0, block_time: 0 };
}

export const KickOffReq: MessageFns<KickOffReq> = {
  fromJSON(object: any): KickOffReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0,
      block_time: isSet(object.block_time) ? globalThis.Number(object.block_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<KickOffReq>, I>>(base?: I): KickOffReq {
    return KickOffReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickOffReq>, I>>(object: I): KickOffReq {
    const message = createBaseKickOffReq();
    message.room_id = object.room_id ?? 0;
    message.target_uid = object.target_uid ?? 0;
    message.block_time = object.block_time ?? 0;
    return message;
  }
};

function createBaseKickOffRsp(): KickOffRsp {
  return {};
}

export const KickOffRsp: MessageFns<KickOffRsp> = {
  fromJSON(_: any): KickOffRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<KickOffRsp>, I>>(base?: I): KickOffRsp {
    return KickOffRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickOffRsp>, I>>(_: I): KickOffRsp {
    const message = createBaseKickOffRsp();
    return message;
  }
};

function createBaseChangeRoomPermReq(): ChangeRoomPermReq {
  return { room_id: 0, room_perm: 0, password: '' };
}

export const ChangeRoomPermReq: MessageFns<ChangeRoomPermReq> = {
  fromJSON(object: any): ChangeRoomPermReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_perm: isSet(object.room_perm) ? roomPermFromJSON(object.room_perm) : 0,
      password: isSet(object.password) ? globalThis.String(object.password) : ''
    };
  },

  create<I extends Exact<DeepPartial<ChangeRoomPermReq>, I>>(base?: I): ChangeRoomPermReq {
    return ChangeRoomPermReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeRoomPermReq>, I>>(object: I): ChangeRoomPermReq {
    const message = createBaseChangeRoomPermReq();
    message.room_id = object.room_id ?? 0;
    message.room_perm = object.room_perm ?? 0;
    message.password = object.password ?? '';
    return message;
  }
};

function createBaseChangeRoomPermRsp(): ChangeRoomPermRsp {
  return { timestamp: 0, room_settings: undefined };
}

export const ChangeRoomPermRsp: MessageFns<ChangeRoomPermRsp> = {
  fromJSON(object: any): ChangeRoomPermRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ChangeRoomPermRsp>, I>>(base?: I): ChangeRoomPermRsp {
    return ChangeRoomPermRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeRoomPermRsp>, I>>(object: I): ChangeRoomPermRsp {
    const message = createBaseChangeRoomPermRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    return message;
  }
};

function createBaseChangeLayoutReq(): ChangeLayoutReq {
  return { room_id: 0, layout_code: '' };
}

export const ChangeLayoutReq: MessageFns<ChangeLayoutReq> = {
  fromJSON(object: any): ChangeLayoutReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      layout_code: isSet(object.layout_code) ? globalThis.String(object.layout_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<ChangeLayoutReq>, I>>(base?: I): ChangeLayoutReq {
    return ChangeLayoutReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeLayoutReq>, I>>(object: I): ChangeLayoutReq {
    const message = createBaseChangeLayoutReq();
    message.room_id = object.room_id ?? 0;
    message.layout_code = object.layout_code ?? '';
    return message;
  }
};

function createBaseChangeLayoutRsp(): ChangeLayoutRsp {
  return { timestamp: 0, room_settings: undefined, seat_list: undefined };
}

export const ChangeLayoutRsp: MessageFns<ChangeLayoutRsp> = {
  fromJSON(object: any): ChangeLayoutRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined,
      seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ChangeLayoutRsp>, I>>(base?: I): ChangeLayoutRsp {
    return ChangeLayoutRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeLayoutRsp>, I>>(object: I): ChangeLayoutRsp {
    const message = createBaseChangeLayoutRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseChangeRoomModeReq(): ChangeRoomModeReq {
  return { room_id: 0, room_mode_publish_id: 0 };
}

export const ChangeRoomModeReq: MessageFns<ChangeRoomModeReq> = {
  fromJSON(object: any): ChangeRoomModeReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_mode_publish_id: isSet(object.room_mode_publish_id) ? globalThis.Number(object.room_mode_publish_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ChangeRoomModeReq>, I>>(base?: I): ChangeRoomModeReq {
    return ChangeRoomModeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeRoomModeReq>, I>>(object: I): ChangeRoomModeReq {
    const message = createBaseChangeRoomModeReq();
    message.room_id = object.room_id ?? 0;
    message.room_mode_publish_id = object.room_mode_publish_id ?? 0;
    return message;
  }
};

function createBaseChangeRoomModeRsp(): ChangeRoomModeRsp {
  return { timestamp: 0, room_settings: undefined, room_state_info: undefined };
}

export const ChangeRoomModeRsp: MessageFns<ChangeRoomModeRsp> = {
  fromJSON(object: any): ChangeRoomModeRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined,
      room_state_info: isSet(object.room_state_info) ? RoomStateInfo.fromJSON(object.room_state_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ChangeRoomModeRsp>, I>>(base?: I): ChangeRoomModeRsp {
    return ChangeRoomModeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeRoomModeRsp>, I>>(object: I): ChangeRoomModeRsp {
    const message = createBaseChangeRoomModeRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    message.room_state_info =
      object.room_state_info !== undefined && object.room_state_info !== null
        ? RoomStateInfo.fromPartial(object.room_state_info)
        : undefined;
    return message;
  }
};

function createBaseChangeSeatPermReq(): ChangeSeatPermReq {
  return { room_id: 0, seat_perm: 0 };
}

export const ChangeSeatPermReq: MessageFns<ChangeSeatPermReq> = {
  fromJSON(object: any): ChangeSeatPermReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_perm: isSet(object.seat_perm) ? seatPermFromJSON(object.seat_perm) : 0
    };
  },

  create<I extends Exact<DeepPartial<ChangeSeatPermReq>, I>>(base?: I): ChangeSeatPermReq {
    return ChangeSeatPermReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeSeatPermReq>, I>>(object: I): ChangeSeatPermReq {
    const message = createBaseChangeSeatPermReq();
    message.room_id = object.room_id ?? 0;
    message.seat_perm = object.seat_perm ?? 0;
    return message;
  }
};

function createBaseChangeSeatPermRsp(): ChangeSeatPermRsp {
  return { timestamp: 0, room_settings: undefined };
}

export const ChangeSeatPermRsp: MessageFns<ChangeSeatPermRsp> = {
  fromJSON(object: any): ChangeSeatPermRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ChangeSeatPermRsp>, I>>(base?: I): ChangeSeatPermRsp {
    return ChangeSeatPermRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeSeatPermRsp>, I>>(object: I): ChangeSeatPermRsp {
    const message = createBaseChangeSeatPermRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    return message;
  }
};

function createBaseChangeSeatScoreSwitchReq(): ChangeSeatScoreSwitchReq {
  return { room_id: 0, switch: 0 };
}

export const ChangeSeatScoreSwitchReq: MessageFns<ChangeSeatScoreSwitchReq> = {
  fromJSON(object: any): ChangeSeatScoreSwitchReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      switch: isSet(object.switch) ? seatScoreSwitchFromJSON(object.switch) : 0
    };
  },

  create<I extends Exact<DeepPartial<ChangeSeatScoreSwitchReq>, I>>(base?: I): ChangeSeatScoreSwitchReq {
    return ChangeSeatScoreSwitchReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeSeatScoreSwitchReq>, I>>(object: I): ChangeSeatScoreSwitchReq {
    const message = createBaseChangeSeatScoreSwitchReq();
    message.room_id = object.room_id ?? 0;
    message.switch = object.switch ?? 0;
    return message;
  }
};

function createBaseChangeSeatScoreSwitchRsp(): ChangeSeatScoreSwitchRsp {
  return { timestamp: 0, room_settings: undefined };
}

export const ChangeSeatScoreSwitchRsp: MessageFns<ChangeSeatScoreSwitchRsp> = {
  fromJSON(object: any): ChangeSeatScoreSwitchRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ChangeSeatScoreSwitchRsp>, I>>(base?: I): ChangeSeatScoreSwitchRsp {
    return ChangeSeatScoreSwitchRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangeSeatScoreSwitchRsp>, I>>(object: I): ChangeSeatScoreSwitchRsp {
    const message = createBaseChangeSeatScoreSwitchRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    return message;
  }
};

function createBaseListRoomAdminReq(): ListRoomAdminReq {
  return { room_id: 0 };
}

export const ListRoomAdminReq: MessageFns<ListRoomAdminReq> = {
  fromJSON(object: any): ListRoomAdminReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListRoomAdminReq>, I>>(base?: I): ListRoomAdminReq {
    return ListRoomAdminReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomAdminReq>, I>>(object: I): ListRoomAdminReq {
    const message = createBaseListRoomAdminReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseListRoomAdminRsp(): ListRoomAdminRsp {
  return { admin_list: [] };
}

export const ListRoomAdminRsp: MessageFns<ListRoomAdminRsp> = {
  fromJSON(object: any): ListRoomAdminRsp {
    return {
      admin_list: globalThis.Array.isArray(object?.admin_list)
        ? object.admin_list.map((e: any) => RoomUserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomAdminRsp>, I>>(base?: I): ListRoomAdminRsp {
    return ListRoomAdminRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomAdminRsp>, I>>(object: I): ListRoomAdminRsp {
    const message = createBaseListRoomAdminRsp();
    message.admin_list = object.admin_list?.map(e => RoomUserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSetRoomAdminReq(): SetRoomAdminReq {
  return { room_id: 0, target_uid: 0 };
}

export const SetRoomAdminReq: MessageFns<SetRoomAdminReq> = {
  fromJSON(object: any): SetRoomAdminReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<SetRoomAdminReq>, I>>(base?: I): SetRoomAdminReq {
    return SetRoomAdminReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomAdminReq>, I>>(object: I): SetRoomAdminReq {
    const message = createBaseSetRoomAdminReq();
    message.room_id = object.room_id ?? 0;
    message.target_uid = object.target_uid ?? 0;
    return message;
  }
};

function createBaseSetRoomAdminRsp(): SetRoomAdminRsp {
  return {};
}

export const SetRoomAdminRsp: MessageFns<SetRoomAdminRsp> = {
  fromJSON(_: any): SetRoomAdminRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SetRoomAdminRsp>, I>>(base?: I): SetRoomAdminRsp {
    return SetRoomAdminRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomAdminRsp>, I>>(_: I): SetRoomAdminRsp {
    const message = createBaseSetRoomAdminRsp();
    return message;
  }
};

function createBaseRemoveRoomAdminReq(): RemoveRoomAdminReq {
  return { room_id: 0, target_uid: 0 };
}

export const RemoveRoomAdminReq: MessageFns<RemoveRoomAdminReq> = {
  fromJSON(object: any): RemoveRoomAdminReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<RemoveRoomAdminReq>, I>>(base?: I): RemoveRoomAdminReq {
    return RemoveRoomAdminReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveRoomAdminReq>, I>>(object: I): RemoveRoomAdminReq {
    const message = createBaseRemoveRoomAdminReq();
    message.room_id = object.room_id ?? 0;
    message.target_uid = object.target_uid ?? 0;
    return message;
  }
};

function createBaseRemoveRoomAdminRsp(): RemoveRoomAdminRsp {
  return {};
}

export const RemoveRoomAdminRsp: MessageFns<RemoveRoomAdminRsp> = {
  fromJSON(_: any): RemoveRoomAdminRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<RemoveRoomAdminRsp>, I>>(base?: I): RemoveRoomAdminRsp {
    return RemoveRoomAdminRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveRoomAdminRsp>, I>>(_: I): RemoveRoomAdminRsp {
    const message = createBaseRemoveRoomAdminRsp();
    return message;
  }
};

function createBaseSetRoomNameReq(): SetRoomNameReq {
  return { room_id: 0, name: '' };
}

export const SetRoomNameReq: MessageFns<SetRoomNameReq> = {
  fromJSON(object: any): SetRoomNameReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<SetRoomNameReq>, I>>(base?: I): SetRoomNameReq {
    return SetRoomNameReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomNameReq>, I>>(object: I): SetRoomNameReq {
    const message = createBaseSetRoomNameReq();
    message.room_id = object.room_id ?? 0;
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseSetRoomNameRsp(): SetRoomNameRsp {
  return { timestamp: 0, room_info: undefined };
}

export const SetRoomNameRsp: MessageFns<SetRoomNameRsp> = {
  fromJSON(object: any): SetRoomNameRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SetRoomNameRsp>, I>>(base?: I): SetRoomNameRsp {
    return SetRoomNameRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomNameRsp>, I>>(object: I): SetRoomNameRsp {
    const message = createBaseSetRoomNameRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseSetRoomIntroReq(): SetRoomIntroReq {
  return { room_id: 0, intro: '' };
}

export const SetRoomIntroReq: MessageFns<SetRoomIntroReq> = {
  fromJSON(object: any): SetRoomIntroReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      intro: isSet(object.intro) ? globalThis.String(object.intro) : ''
    };
  },

  create<I extends Exact<DeepPartial<SetRoomIntroReq>, I>>(base?: I): SetRoomIntroReq {
    return SetRoomIntroReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomIntroReq>, I>>(object: I): SetRoomIntroReq {
    const message = createBaseSetRoomIntroReq();
    message.room_id = object.room_id ?? 0;
    message.intro = object.intro ?? '';
    return message;
  }
};

function createBaseSetRoomIntroRsp(): SetRoomIntroRsp {
  return { timestamp: 0, room_info: undefined };
}

export const SetRoomIntroRsp: MessageFns<SetRoomIntroRsp> = {
  fromJSON(object: any): SetRoomIntroRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SetRoomIntroRsp>, I>>(base?: I): SetRoomIntroRsp {
    return SetRoomIntroRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomIntroRsp>, I>>(object: I): SetRoomIntroRsp {
    const message = createBaseSetRoomIntroRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseSetRoomCoverReq(): SetRoomCoverReq {
  return { room_id: 0, cover: '' };
}

export const SetRoomCoverReq: MessageFns<SetRoomCoverReq> = {
  fromJSON(object: any): SetRoomCoverReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      cover: isSet(object.cover) ? globalThis.String(object.cover) : ''
    };
  },

  create<I extends Exact<DeepPartial<SetRoomCoverReq>, I>>(base?: I): SetRoomCoverReq {
    return SetRoomCoverReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomCoverReq>, I>>(object: I): SetRoomCoverReq {
    const message = createBaseSetRoomCoverReq();
    message.room_id = object.room_id ?? 0;
    message.cover = object.cover ?? '';
    return message;
  }
};

function createBaseSetRoomCoverRsp(): SetRoomCoverRsp {
  return { timestamp: 0, room_info: undefined };
}

export const SetRoomCoverRsp: MessageFns<SetRoomCoverRsp> = {
  fromJSON(object: any): SetRoomCoverRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SetRoomCoverRsp>, I>>(base?: I): SetRoomCoverRsp {
    return SetRoomCoverRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomCoverRsp>, I>>(object: I): SetRoomCoverRsp {
    const message = createBaseSetRoomCoverRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseSetRoomBackgroundReq(): SetRoomBackgroundReq {
  return { room_id: 0, bg_img: '', bg_svga: '', goods_id: 0 };
}

export const SetRoomBackgroundReq: MessageFns<SetRoomBackgroundReq> = {
  fromJSON(object: any): SetRoomBackgroundReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      bg_img: isSet(object.bg_img) ? globalThis.String(object.bg_img) : '',
      bg_svga: isSet(object.bg_svga) ? globalThis.String(object.bg_svga) : '',
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<SetRoomBackgroundReq>, I>>(base?: I): SetRoomBackgroundReq {
    return SetRoomBackgroundReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomBackgroundReq>, I>>(object: I): SetRoomBackgroundReq {
    const message = createBaseSetRoomBackgroundReq();
    message.room_id = object.room_id ?? 0;
    message.bg_img = object.bg_img ?? '';
    message.bg_svga = object.bg_svga ?? '';
    message.goods_id = object.goods_id ?? 0;
    return message;
  }
};

function createBaseSetRoomBackgroundRsp(): SetRoomBackgroundRsp {
  return { timestamp: 0, room_info: undefined };
}

export const SetRoomBackgroundRsp: MessageFns<SetRoomBackgroundRsp> = {
  fromJSON(object: any): SetRoomBackgroundRsp {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SetRoomBackgroundRsp>, I>>(base?: I): SetRoomBackgroundRsp {
    return SetRoomBackgroundRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetRoomBackgroundRsp>, I>>(object: I): SetRoomBackgroundRsp {
    const message = createBaseSetRoomBackgroundRsp();
    message.timestamp = object.timestamp ?? 0;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseBatchGetRoomUserInfoReq(): BatchGetRoomUserInfoReq {
  return { room_id: 0, uids: [], selector: undefined, category: 0 };
}

export const BatchGetRoomUserInfoReq: MessageFns<BatchGetRoomUserInfoReq> = {
  fromJSON(object: any): BatchGetRoomUserInfoReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      selector: isSet(object.selector) ? RoomUserInfo.fromJSON(object.selector) : undefined,
      category: isSet(object.category) ? roomCategoryFromJSON(object.category) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomUserInfoReq>, I>>(base?: I): BatchGetRoomUserInfoReq {
    return BatchGetRoomUserInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomUserInfoReq>, I>>(object: I): BatchGetRoomUserInfoReq {
    const message = createBaseBatchGetRoomUserInfoReq();
    message.room_id = object.room_id ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.selector =
      object.selector !== undefined && object.selector !== null ? RoomUserInfo.fromPartial(object.selector) : undefined;
    message.category = object.category ?? 0;
    return message;
  }
};

function createBaseBatchGetRoomUserInfoRsp(): BatchGetRoomUserInfoRsp {
  return { users: {} };
}

export const BatchGetRoomUserInfoRsp: MessageFns<BatchGetRoomUserInfoRsp> = {
  fromJSON(object: any): BatchGetRoomUserInfoRsp {
    return {
      users: isObject(object.users)
        ? Object.entries(object.users).reduce<{ [key: number]: RoomUserInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = RoomUserInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomUserInfoRsp>, I>>(base?: I): BatchGetRoomUserInfoRsp {
    return BatchGetRoomUserInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomUserInfoRsp>, I>>(object: I): BatchGetRoomUserInfoRsp {
    const message = createBaseBatchGetRoomUserInfoRsp();
    message.users = Object.entries(object.users ?? {}).reduce<{ [key: number]: RoomUserInfo }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = RoomUserInfo.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetRoomUserInfoRsp_UsersEntry(): BatchGetRoomUserInfoRsp_UsersEntry {
  return { key: 0, value: undefined };
}

export const BatchGetRoomUserInfoRsp_UsersEntry: MessageFns<BatchGetRoomUserInfoRsp_UsersEntry> = {
  fromJSON(object: any): BatchGetRoomUserInfoRsp_UsersEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? RoomUserInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetRoomUserInfoRsp_UsersEntry>, I>>(
    base?: I
  ): BatchGetRoomUserInfoRsp_UsersEntry {
    return BatchGetRoomUserInfoRsp_UsersEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetRoomUserInfoRsp_UsersEntry>, I>>(
    object: I
  ): BatchGetRoomUserInfoRsp_UsersEntry {
    const message = createBaseBatchGetRoomUserInfoRsp_UsersEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? RoomUserInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseGetUserCurrentRoomReq(): GetUserCurrentRoomReq {
  return { uid: 0 };
}

export const GetUserCurrentRoomReq: MessageFns<GetUserCurrentRoomReq> = {
  fromJSON(object: any): GetUserCurrentRoomReq {
    return { uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0 };
  },

  create<I extends Exact<DeepPartial<GetUserCurrentRoomReq>, I>>(base?: I): GetUserCurrentRoomReq {
    return GetUserCurrentRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserCurrentRoomReq>, I>>(object: I): GetUserCurrentRoomReq {
    const message = createBaseGetUserCurrentRoomReq();
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseGetUserCurrentRoomRsp(): GetUserCurrentRoomRsp {
  return { room_id: 0 };
}

export const GetUserCurrentRoomRsp: MessageFns<GetUserCurrentRoomRsp> = {
  fromJSON(object: any): GetUserCurrentRoomRsp {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetUserCurrentRoomRsp>, I>>(base?: I): GetUserCurrentRoomRsp {
    return GetUserCurrentRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserCurrentRoomRsp>, I>>(object: I): GetUserCurrentRoomRsp {
    const message = createBaseGetUserCurrentRoomRsp();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetRoomRandomNameReq(): GetRoomRandomNameReq {
  return {};
}

export const GetRoomRandomNameReq: MessageFns<GetRoomRandomNameReq> = {
  fromJSON(_: any): GetRoomRandomNameReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetRoomRandomNameReq>, I>>(base?: I): GetRoomRandomNameReq {
    return GetRoomRandomNameReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRandomNameReq>, I>>(_: I): GetRoomRandomNameReq {
    const message = createBaseGetRoomRandomNameReq();
    return message;
  }
};

function createBaseGetRoomRandomNameRsp(): GetRoomRandomNameRsp {
  return { name: '' };
}

export const GetRoomRandomNameRsp: MessageFns<GetRoomRandomNameRsp> = {
  fromJSON(object: any): GetRoomRandomNameRsp {
    return { name: isSet(object.name) ? globalThis.String(object.name) : '' };
  },

  create<I extends Exact<DeepPartial<GetRoomRandomNameRsp>, I>>(base?: I): GetRoomRandomNameRsp {
    return GetRoomRandomNameRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRandomNameRsp>, I>>(object: I): GetRoomRandomNameRsp {
    const message = createBaseGetRoomRandomNameRsp();
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseRoomTabShowReq(): RoomTabShowReq {
  return {};
}

export const RoomTabShowReq: MessageFns<RoomTabShowReq> = {
  fromJSON(_: any): RoomTabShowReq {
    return {};
  },

  create<I extends Exact<DeepPartial<RoomTabShowReq>, I>>(base?: I): RoomTabShowReq {
    return RoomTabShowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTabShowReq>, I>>(_: I): RoomTabShowReq {
    const message = createBaseRoomTabShowReq();
    return message;
  }
};

function createBaseRoomTabShowRsp(): RoomTabShowRsp {
  return { is_show: false };
}

export const RoomTabShowRsp: MessageFns<RoomTabShowRsp> = {
  fromJSON(object: any): RoomTabShowRsp {
    return { is_show: isSet(object.is_show) ? globalThis.Boolean(object.is_show) : false };
  },

  create<I extends Exact<DeepPartial<RoomTabShowRsp>, I>>(base?: I): RoomTabShowRsp {
    return RoomTabShowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTabShowRsp>, I>>(object: I): RoomTabShowRsp {
    const message = createBaseRoomTabShowRsp();
    message.is_show = object.is_show ?? false;
    return message;
  }
};

function createBaseJudgeCreateRoomPermReq(): JudgeCreateRoomPermReq {
  return {};
}

export const JudgeCreateRoomPermReq: MessageFns<JudgeCreateRoomPermReq> = {
  fromJSON(_: any): JudgeCreateRoomPermReq {
    return {};
  },

  create<I extends Exact<DeepPartial<JudgeCreateRoomPermReq>, I>>(base?: I): JudgeCreateRoomPermReq {
    return JudgeCreateRoomPermReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JudgeCreateRoomPermReq>, I>>(_: I): JudgeCreateRoomPermReq {
    const message = createBaseJudgeCreateRoomPermReq();
    return message;
  }
};

function createBaseJudgeCreateRoomPermRsp(): JudgeCreateRoomPermRsp {
  return { is_allow: false };
}

export const JudgeCreateRoomPermRsp: MessageFns<JudgeCreateRoomPermRsp> = {
  fromJSON(object: any): JudgeCreateRoomPermRsp {
    return { is_allow: isSet(object.is_allow) ? globalThis.Boolean(object.is_allow) : false };
  },

  create<I extends Exact<DeepPartial<JudgeCreateRoomPermRsp>, I>>(base?: I): JudgeCreateRoomPermRsp {
    return JudgeCreateRoomPermRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<JudgeCreateRoomPermRsp>, I>>(object: I): JudgeCreateRoomPermRsp {
    const message = createBaseJudgeCreateRoomPermRsp();
    message.is_allow = object.is_allow ?? false;
    return message;
  }
};

function createBaseListRoomValidMicUserReq(): ListRoomValidMicUserReq {
  return { room_id: 0 };
}

export const ListRoomValidMicUserReq: MessageFns<ListRoomValidMicUserReq> = {
  fromJSON(object: any): ListRoomValidMicUserReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListRoomValidMicUserReq>, I>>(base?: I): ListRoomValidMicUserReq {
    return ListRoomValidMicUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomValidMicUserReq>, I>>(object: I): ListRoomValidMicUserReq {
    const message = createBaseListRoomValidMicUserReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseListRoomValidMicUserRsp(): ListRoomValidMicUserRsp {
  return { uids: [] };
}

export const ListRoomValidMicUserRsp: MessageFns<ListRoomValidMicUserRsp> = {
  fromJSON(object: any): ListRoomValidMicUserRsp {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListRoomValidMicUserRsp>, I>>(base?: I): ListRoomValidMicUserRsp {
    return ListRoomValidMicUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomValidMicUserRsp>, I>>(object: I): ListRoomValidMicUserRsp {
    const message = createBaseListRoomValidMicUserRsp();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseRtcToken(): RtcToken {
  return { rtc_provider: 0, app_id: '', token: '', token_expire: 0 };
}

export const RtcToken: MessageFns<RtcToken> = {
  fromJSON(object: any): RtcToken {
    return {
      rtc_provider: isSet(object.rtc_provider) ? rtcProviderFromJSON(object.rtc_provider) : 0,
      app_id: isSet(object.app_id) ? globalThis.String(object.app_id) : '',
      token: isSet(object.token) ? globalThis.String(object.token) : '',
      token_expire: isSet(object.token_expire) ? globalThis.Number(object.token_expire) : 0
    };
  },

  create<I extends Exact<DeepPartial<RtcToken>, I>>(base?: I): RtcToken {
    return RtcToken.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RtcToken>, I>>(object: I): RtcToken {
    const message = createBaseRtcToken();
    message.rtc_provider = object.rtc_provider ?? 0;
    message.app_id = object.app_id ?? '';
    message.token = object.token ?? '';
    message.token_expire = object.token_expire ?? 0;
    return message;
  }
};

function createBaseRtmToken(): RtmToken {
  return { rtm_provider: 0, app_id: '', token: '', token_expire: 0 };
}

export const RtmToken: MessageFns<RtmToken> = {
  fromJSON(object: any): RtmToken {
    return {
      rtm_provider: isSet(object.rtm_provider) ? rtmProviderFromJSON(object.rtm_provider) : 0,
      app_id: isSet(object.app_id) ? globalThis.String(object.app_id) : '',
      token: isSet(object.token) ? globalThis.String(object.token) : '',
      token_expire: isSet(object.token_expire) ? globalThis.Number(object.token_expire) : 0
    };
  },

  create<I extends Exact<DeepPartial<RtmToken>, I>>(base?: I): RtmToken {
    return RtmToken.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RtmToken>, I>>(object: I): RtmToken {
    const message = createBaseRtmToken();
    message.rtm_provider = object.rtm_provider ?? 0;
    message.app_id = object.app_id ?? '';
    message.token = object.token ?? '';
    message.token_expire = object.token_expire ?? 0;
    return message;
  }
};

function createBaseListSeatReq(): ListSeatReq {
  return { room_id: 0 };
}

export const ListSeatReq: MessageFns<ListSeatReq> = {
  fromJSON(object: any): ListSeatReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListSeatReq>, I>>(base?: I): ListSeatReq {
    return ListSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSeatReq>, I>>(object: I): ListSeatReq {
    const message = createBaseListSeatReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseListSeatRsp(): ListSeatRsp {
  return { seat_list: undefined };
}

export const ListSeatRsp: MessageFns<ListSeatRsp> = {
  fromJSON(object: any): ListSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<ListSeatRsp>, I>>(base?: I): ListSeatRsp {
    return ListSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSeatRsp>, I>>(object: I): ListSeatRsp {
    const message = createBaseListSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseEnterSeatReq(): EnterSeatReq {
  return { room_id: 0, seat_index: 0, seat_index_old: 0 };
}

export const EnterSeatReq: MessageFns<EnterSeatReq> = {
  fromJSON(object: any): EnterSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      seat_index_old: isSet(object.seat_index_old) ? globalThis.Number(object.seat_index_old) : 0
    };
  },

  create<I extends Exact<DeepPartial<EnterSeatReq>, I>>(base?: I): EnterSeatReq {
    return EnterSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterSeatReq>, I>>(object: I): EnterSeatReq {
    const message = createBaseEnterSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    message.seat_index_old = object.seat_index_old ?? 0;
    return message;
  }
};

function createBaseEnterSeatRsp(): EnterSeatRsp {
  return { seat_list: undefined };
}

export const EnterSeatRsp: MessageFns<EnterSeatRsp> = {
  fromJSON(object: any): EnterSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<EnterSeatRsp>, I>>(base?: I): EnterSeatRsp {
    return EnterSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterSeatRsp>, I>>(object: I): EnterSeatRsp {
    const message = createBaseEnterSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseLeaveSeatReq(): LeaveSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const LeaveSeatReq: MessageFns<LeaveSeatReq> = {
  fromJSON(object: any): LeaveSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<LeaveSeatReq>, I>>(base?: I): LeaveSeatReq {
    return LeaveSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaveSeatReq>, I>>(object: I): LeaveSeatReq {
    const message = createBaseLeaveSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseLeaveSeatRsp(): LeaveSeatRsp {
  return { seat_list: undefined };
}

export const LeaveSeatRsp: MessageFns<LeaveSeatRsp> = {
  fromJSON(object: any): LeaveSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<LeaveSeatRsp>, I>>(base?: I): LeaveSeatRsp {
    return LeaveSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LeaveSeatRsp>, I>>(object: I): LeaveSeatRsp {
    const message = createBaseLeaveSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseOpenMicReq(): OpenMicReq {
  return { room_id: 0, seat_index: 0 };
}

export const OpenMicReq: MessageFns<OpenMicReq> = {
  fromJSON(object: any): OpenMicReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<OpenMicReq>, I>>(base?: I): OpenMicReq {
    return OpenMicReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OpenMicReq>, I>>(object: I): OpenMicReq {
    const message = createBaseOpenMicReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseOpenMicRsp(): OpenMicRsp {
  return { seat_list: undefined };
}

export const OpenMicRsp: MessageFns<OpenMicRsp> = {
  fromJSON(object: any): OpenMicRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<OpenMicRsp>, I>>(base?: I): OpenMicRsp {
    return OpenMicRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OpenMicRsp>, I>>(object: I): OpenMicRsp {
    const message = createBaseOpenMicRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseCloseMicReq(): CloseMicReq {
  return { room_id: 0, seat_index: 0 };
}

export const CloseMicReq: MessageFns<CloseMicReq> = {
  fromJSON(object: any): CloseMicReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<CloseMicReq>, I>>(base?: I): CloseMicReq {
    return CloseMicReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CloseMicReq>, I>>(object: I): CloseMicReq {
    const message = createBaseCloseMicReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseCloseMicRsp(): CloseMicRsp {
  return { seat_list: undefined };
}

export const CloseMicRsp: MessageFns<CloseMicRsp> = {
  fromJSON(object: any): CloseMicRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<CloseMicRsp>, I>>(base?: I): CloseMicRsp {
    return CloseMicRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CloseMicRsp>, I>>(object: I): CloseMicRsp {
    const message = createBaseCloseMicRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseApplyEnterSeatReq(): ApplyEnterSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const ApplyEnterSeatReq: MessageFns<ApplyEnterSeatReq> = {
  fromJSON(object: any): ApplyEnterSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<ApplyEnterSeatReq>, I>>(base?: I): ApplyEnterSeatReq {
    return ApplyEnterSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyEnterSeatReq>, I>>(object: I): ApplyEnterSeatReq {
    const message = createBaseApplyEnterSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseApplyEnterSeatRsp(): ApplyEnterSeatRsp {
  return {};
}

export const ApplyEnterSeatRsp: MessageFns<ApplyEnterSeatRsp> = {
  fromJSON(_: any): ApplyEnterSeatRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ApplyEnterSeatRsp>, I>>(base?: I): ApplyEnterSeatRsp {
    return ApplyEnterSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyEnterSeatRsp>, I>>(_: I): ApplyEnterSeatRsp {
    const message = createBaseApplyEnterSeatRsp();
    return message;
  }
};

function createBaseGetApplyEnterSeatStatusReq(): GetApplyEnterSeatStatusReq {
  return { room_id: 0 };
}

export const GetApplyEnterSeatStatusReq: MessageFns<GetApplyEnterSeatStatusReq> = {
  fromJSON(object: any): GetApplyEnterSeatStatusReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetApplyEnterSeatStatusReq>, I>>(base?: I): GetApplyEnterSeatStatusReq {
    return GetApplyEnterSeatStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetApplyEnterSeatStatusReq>, I>>(object: I): GetApplyEnterSeatStatusReq {
    const message = createBaseGetApplyEnterSeatStatusReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetApplyEnterSeatStatusRsp(): GetApplyEnterSeatStatusRsp {
  return { applied: false };
}

export const GetApplyEnterSeatStatusRsp: MessageFns<GetApplyEnterSeatStatusRsp> = {
  fromJSON(object: any): GetApplyEnterSeatStatusRsp {
    return { applied: isSet(object.applied) ? globalThis.Boolean(object.applied) : false };
  },

  create<I extends Exact<DeepPartial<GetApplyEnterSeatStatusRsp>, I>>(base?: I): GetApplyEnterSeatStatusRsp {
    return GetApplyEnterSeatStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetApplyEnterSeatStatusRsp>, I>>(object: I): GetApplyEnterSeatStatusRsp {
    const message = createBaseGetApplyEnterSeatStatusRsp();
    message.applied = object.applied ?? false;
    return message;
  }
};

function createBaseCancelApplyEnterSeatReq(): CancelApplyEnterSeatReq {
  return { room_id: 0 };
}

export const CancelApplyEnterSeatReq: MessageFns<CancelApplyEnterSeatReq> = {
  fromJSON(object: any): CancelApplyEnterSeatReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CancelApplyEnterSeatReq>, I>>(base?: I): CancelApplyEnterSeatReq {
    return CancelApplyEnterSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelApplyEnterSeatReq>, I>>(object: I): CancelApplyEnterSeatReq {
    const message = createBaseCancelApplyEnterSeatReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseCancelApplyEnterSeatRsp(): CancelApplyEnterSeatRsp {
  return {};
}

export const CancelApplyEnterSeatRsp: MessageFns<CancelApplyEnterSeatRsp> = {
  fromJSON(_: any): CancelApplyEnterSeatRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CancelApplyEnterSeatRsp>, I>>(base?: I): CancelApplyEnterSeatRsp {
    return CancelApplyEnterSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelApplyEnterSeatRsp>, I>>(_: I): CancelApplyEnterSeatRsp {
    const message = createBaseCancelApplyEnterSeatRsp();
    return message;
  }
};

function createBaseAcceptSeatInvitationReq(): AcceptSeatInvitationReq {
  return { room_id: 0, seat_index: 0 };
}

export const AcceptSeatInvitationReq: MessageFns<AcceptSeatInvitationReq> = {
  fromJSON(object: any): AcceptSeatInvitationReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<AcceptSeatInvitationReq>, I>>(base?: I): AcceptSeatInvitationReq {
    return AcceptSeatInvitationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AcceptSeatInvitationReq>, I>>(object: I): AcceptSeatInvitationReq {
    const message = createBaseAcceptSeatInvitationReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseAcceptSeatInvitationRsp(): AcceptSeatInvitationRsp {
  return { seat_list: undefined };
}

export const AcceptSeatInvitationRsp: MessageFns<AcceptSeatInvitationRsp> = {
  fromJSON(object: any): AcceptSeatInvitationRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<AcceptSeatInvitationRsp>, I>>(base?: I): AcceptSeatInvitationRsp {
    return AcceptSeatInvitationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AcceptSeatInvitationRsp>, I>>(object: I): AcceptSeatInvitationRsp {
    const message = createBaseAcceptSeatInvitationRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseRejectSeatInvitationReq(): RejectSeatInvitationReq {
  return { room_id: 0, seat_index: 0 };
}

export const RejectSeatInvitationReq: MessageFns<RejectSeatInvitationReq> = {
  fromJSON(object: any): RejectSeatInvitationReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<RejectSeatInvitationReq>, I>>(base?: I): RejectSeatInvitationReq {
    return RejectSeatInvitationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RejectSeatInvitationReq>, I>>(object: I): RejectSeatInvitationReq {
    const message = createBaseRejectSeatInvitationReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseRejectSeatInvitationRsp(): RejectSeatInvitationRsp {
  return {};
}

export const RejectSeatInvitationRsp: MessageFns<RejectSeatInvitationRsp> = {
  fromJSON(_: any): RejectSeatInvitationRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<RejectSeatInvitationRsp>, I>>(base?: I): RejectSeatInvitationRsp {
    return RejectSeatInvitationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RejectSeatInvitationRsp>, I>>(_: I): RejectSeatInvitationRsp {
    const message = createBaseRejectSeatInvitationRsp();
    return message;
  }
};

function createBaseLockSeatReq(): LockSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const LockSeatReq: MessageFns<LockSeatReq> = {
  fromJSON(object: any): LockSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<LockSeatReq>, I>>(base?: I): LockSeatReq {
    return LockSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LockSeatReq>, I>>(object: I): LockSeatReq {
    const message = createBaseLockSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseLockSeatRsp(): LockSeatRsp {
  return { seat_list: undefined };
}

export const LockSeatRsp: MessageFns<LockSeatRsp> = {
  fromJSON(object: any): LockSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<LockSeatRsp>, I>>(base?: I): LockSeatRsp {
    return LockSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LockSeatRsp>, I>>(object: I): LockSeatRsp {
    const message = createBaseLockSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseUnlockSeatReq(): UnlockSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const UnlockSeatReq: MessageFns<UnlockSeatReq> = {
  fromJSON(object: any): UnlockSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<UnlockSeatReq>, I>>(base?: I): UnlockSeatReq {
    return UnlockSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnlockSeatReq>, I>>(object: I): UnlockSeatReq {
    const message = createBaseUnlockSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseUnlockSeatRsp(): UnlockSeatRsp {
  return { seat_list: undefined };
}

export const UnlockSeatRsp: MessageFns<UnlockSeatRsp> = {
  fromJSON(object: any): UnlockSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<UnlockSeatRsp>, I>>(base?: I): UnlockSeatRsp {
    return UnlockSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnlockSeatRsp>, I>>(object: I): UnlockSeatRsp {
    const message = createBaseUnlockSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseMuteSeatReq(): MuteSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const MuteSeatReq: MessageFns<MuteSeatReq> = {
  fromJSON(object: any): MuteSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<MuteSeatReq>, I>>(base?: I): MuteSeatReq {
    return MuteSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteSeatReq>, I>>(object: I): MuteSeatReq {
    const message = createBaseMuteSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseMuteSeatRsp(): MuteSeatRsp {
  return { seat_list: undefined };
}

export const MuteSeatRsp: MessageFns<MuteSeatRsp> = {
  fromJSON(object: any): MuteSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<MuteSeatRsp>, I>>(base?: I): MuteSeatRsp {
    return MuteSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteSeatRsp>, I>>(object: I): MuteSeatRsp {
    const message = createBaseMuteSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseUnmuteSeatReq(): UnmuteSeatReq {
  return { room_id: 0, seat_index: 0 };
}

export const UnmuteSeatReq: MessageFns<UnmuteSeatReq> = {
  fromJSON(object: any): UnmuteSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<UnmuteSeatReq>, I>>(base?: I): UnmuteSeatReq {
    return UnmuteSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnmuteSeatReq>, I>>(object: I): UnmuteSeatReq {
    const message = createBaseUnmuteSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseUnmuteSeatRsp(): UnmuteSeatRsp {
  return { seat_list: undefined };
}

export const UnmuteSeatRsp: MessageFns<UnmuteSeatRsp> = {
  fromJSON(object: any): UnmuteSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<UnmuteSeatRsp>, I>>(base?: I): UnmuteSeatRsp {
    return UnmuteSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnmuteSeatRsp>, I>>(object: I): UnmuteSeatRsp {
    const message = createBaseUnmuteSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseMuteSeatUserReq(): MuteSeatUserReq {
  return { room_id: 0, seat_index: 0, uid: 0 };
}

export const MuteSeatUserReq: MessageFns<MuteSeatUserReq> = {
  fromJSON(object: any): MuteSeatUserReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<MuteSeatUserReq>, I>>(base?: I): MuteSeatUserReq {
    return MuteSeatUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteSeatUserReq>, I>>(object: I): MuteSeatUserReq {
    const message = createBaseMuteSeatUserReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseMuteSeatUserRsp(): MuteSeatUserRsp {
  return { seat_list: undefined };
}

export const MuteSeatUserRsp: MessageFns<MuteSeatUserRsp> = {
  fromJSON(object: any): MuteSeatUserRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<MuteSeatUserRsp>, I>>(base?: I): MuteSeatUserRsp {
    return MuteSeatUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteSeatUserRsp>, I>>(object: I): MuteSeatUserRsp {
    const message = createBaseMuteSeatUserRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseUnmuteSeatUserReq(): UnmuteSeatUserReq {
  return { room_id: 0, seat_index: 0, uid: 0 };
}

export const UnmuteSeatUserReq: MessageFns<UnmuteSeatUserReq> = {
  fromJSON(object: any): UnmuteSeatUserReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<UnmuteSeatUserReq>, I>>(base?: I): UnmuteSeatUserReq {
    return UnmuteSeatUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnmuteSeatUserReq>, I>>(object: I): UnmuteSeatUserReq {
    const message = createBaseUnmuteSeatUserReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseUnmuteSeatUserRsp(): UnmuteSeatUserRsp {
  return { seat_list: undefined };
}

export const UnmuteSeatUserRsp: MessageFns<UnmuteSeatUserRsp> = {
  fromJSON(object: any): UnmuteSeatUserRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<UnmuteSeatUserRsp>, I>>(base?: I): UnmuteSeatUserRsp {
    return UnmuteSeatUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnmuteSeatUserRsp>, I>>(object: I): UnmuteSeatUserRsp {
    const message = createBaseUnmuteSeatUserRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseKickSeatReq(): KickSeatReq {
  return { room_id: 0, seat_index: 0, uid: 0 };
}

export const KickSeatReq: MessageFns<KickSeatReq> = {
  fromJSON(object: any): KickSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<KickSeatReq>, I>>(base?: I): KickSeatReq {
    return KickSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickSeatReq>, I>>(object: I): KickSeatReq {
    const message = createBaseKickSeatReq();
    message.room_id = object.room_id ?? 0;
    message.seat_index = object.seat_index ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseKickSeatRsp(): KickSeatRsp {
  return { seat_list: undefined };
}

export const KickSeatRsp: MessageFns<KickSeatRsp> = {
  fromJSON(object: any): KickSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<KickSeatRsp>, I>>(base?: I): KickSeatRsp {
    return KickSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KickSeatRsp>, I>>(object: I): KickSeatRsp {
    const message = createBaseKickSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBasePickUpSeatReq(): PickUpSeatReq {
  return { room_id: 0, uid: 0, seat_index: 0 };
}

export const PickUpSeatReq: MessageFns<PickUpSeatReq> = {
  fromJSON(object: any): PickUpSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<PickUpSeatReq>, I>>(base?: I): PickUpSeatReq {
    return PickUpSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PickUpSeatReq>, I>>(object: I): PickUpSeatReq {
    const message = createBasePickUpSeatReq();
    message.room_id = object.room_id ?? 0;
    message.uid = object.uid ?? 0;
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBasePickUpSeatRsp(): PickUpSeatRsp {
  return { seat_list: undefined };
}

export const PickUpSeatRsp: MessageFns<PickUpSeatRsp> = {
  fromJSON(object: any): PickUpSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<PickUpSeatRsp>, I>>(base?: I): PickUpSeatRsp {
    return PickUpSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PickUpSeatRsp>, I>>(object: I): PickUpSeatRsp {
    const message = createBasePickUpSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseApplyEnterSeatUserListReq(): ApplyEnterSeatUserListReq {
  return { page: undefined, room_id: 0 };
}

export const ApplyEnterSeatUserListReq: MessageFns<ApplyEnterSeatUserListReq> = {
  fromJSON(object: any): ApplyEnterSeatUserListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ApplyEnterSeatUserListReq>, I>>(base?: I): ApplyEnterSeatUserListReq {
    return ApplyEnterSeatUserListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyEnterSeatUserListReq>, I>>(object: I): ApplyEnterSeatUserListReq {
    const message = createBaseApplyEnterSeatUserListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseApplyEnterSeatUserListRsp(): ApplyEnterSeatUserListRsp {
  return { page: undefined, user_infos: [] };
}

export const ApplyEnterSeatUserListRsp: MessageFns<ApplyEnterSeatUserListRsp> = {
  fromJSON(object: any): ApplyEnterSeatUserListRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      user_infos: globalThis.Array.isArray(object?.user_infos)
        ? object.user_infos.map((e: any) => UserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ApplyEnterSeatUserListRsp>, I>>(base?: I): ApplyEnterSeatUserListRsp {
    return ApplyEnterSeatUserListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyEnterSeatUserListRsp>, I>>(object: I): ApplyEnterSeatUserListRsp {
    const message = createBaseApplyEnterSeatUserListRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.user_infos = object.user_infos?.map(e => UserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseApproveApplyEnterSeatReq(): ApproveApplyEnterSeatReq {
  return { room_id: 0, uid: 0 };
}

export const ApproveApplyEnterSeatReq: MessageFns<ApproveApplyEnterSeatReq> = {
  fromJSON(object: any): ApproveApplyEnterSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<ApproveApplyEnterSeatReq>, I>>(base?: I): ApproveApplyEnterSeatReq {
    return ApproveApplyEnterSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApproveApplyEnterSeatReq>, I>>(object: I): ApproveApplyEnterSeatReq {
    const message = createBaseApproveApplyEnterSeatReq();
    message.room_id = object.room_id ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseApproveApplyEnterSeatRsp(): ApproveApplyEnterSeatRsp {
  return { seat_list: undefined };
}

export const ApproveApplyEnterSeatRsp: MessageFns<ApproveApplyEnterSeatRsp> = {
  fromJSON(object: any): ApproveApplyEnterSeatRsp {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<ApproveApplyEnterSeatRsp>, I>>(base?: I): ApproveApplyEnterSeatRsp {
    return ApproveApplyEnterSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApproveApplyEnterSeatRsp>, I>>(object: I): ApproveApplyEnterSeatRsp {
    const message = createBaseApproveApplyEnterSeatRsp();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseRejectApplyEnterSeatReq(): RejectApplyEnterSeatReq {
  return { room_id: 0, uid: 0 };
}

export const RejectApplyEnterSeatReq: MessageFns<RejectApplyEnterSeatReq> = {
  fromJSON(object: any): RejectApplyEnterSeatReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<RejectApplyEnterSeatReq>, I>>(base?: I): RejectApplyEnterSeatReq {
    return RejectApplyEnterSeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RejectApplyEnterSeatReq>, I>>(object: I): RejectApplyEnterSeatReq {
    const message = createBaseRejectApplyEnterSeatReq();
    message.room_id = object.room_id ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseRejectApplyEnterSeatRsp(): RejectApplyEnterSeatRsp {
  return {};
}

export const RejectApplyEnterSeatRsp: MessageFns<RejectApplyEnterSeatRsp> = {
  fromJSON(_: any): RejectApplyEnterSeatRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<RejectApplyEnterSeatRsp>, I>>(base?: I): RejectApplyEnterSeatRsp {
    return RejectApplyEnterSeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RejectApplyEnterSeatRsp>, I>>(_: I): RejectApplyEnterSeatRsp {
    const message = createBaseRejectApplyEnterSeatRsp();
    return message;
  }
};

function createBaseListOnlineUserReq(): ListOnlineUserReq {
  return { page: undefined, room_id: 0 };
}

export const ListOnlineUserReq: MessageFns<ListOnlineUserReq> = {
  fromJSON(object: any): ListOnlineUserReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListOnlineUserReq>, I>>(base?: I): ListOnlineUserReq {
    return ListOnlineUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOnlineUserReq>, I>>(object: I): ListOnlineUserReq {
    const message = createBaseListOnlineUserReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseListOnlineUserRsp(): ListOnlineUserRsp {
  return { page: undefined, room_user_infos: [] };
}

export const ListOnlineUserRsp: MessageFns<ListOnlineUserRsp> = {
  fromJSON(object: any): ListOnlineUserRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_user_infos: globalThis.Array.isArray(object?.room_user_infos)
        ? object.room_user_infos.map((e: any) => RoomUserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListOnlineUserRsp>, I>>(base?: I): ListOnlineUserRsp {
    return ListOnlineUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOnlineUserRsp>, I>>(object: I): ListOnlineUserRsp {
    const message = createBaseListOnlineUserRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_user_infos = object.room_user_infos?.map(e => RoomUserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListLayoutReq(): ListLayoutReq {
  return {};
}

export const ListLayoutReq: MessageFns<ListLayoutReq> = {
  fromJSON(_: any): ListLayoutReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListLayoutReq>, I>>(base?: I): ListLayoutReq {
    return ListLayoutReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLayoutReq>, I>>(_: I): ListLayoutReq {
    const message = createBaseListLayoutReq();
    return message;
  }
};

function createBaseListLayoutRsp(): ListLayoutRsp {
  return { layout_list: [] };
}

export const ListLayoutRsp: MessageFns<ListLayoutRsp> = {
  fromJSON(object: any): ListLayoutRsp {
    return {
      layout_list: globalThis.Array.isArray(object?.layout_list)
        ? object.layout_list.map((e: any) => RoomLayout.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListLayoutRsp>, I>>(base?: I): ListLayoutRsp {
    return ListLayoutRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLayoutRsp>, I>>(object: I): ListLayoutRsp {
    const message = createBaseListLayoutRsp();
    message.layout_list = object.layout_list?.map(e => RoomLayout.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTempLeaveReq(): TempLeaveReq {
  return { room_id: 0, live_id: 0 };
}

export const TempLeaveReq: MessageFns<TempLeaveReq> = {
  fromJSON(object: any): TempLeaveReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<TempLeaveReq>, I>>(base?: I): TempLeaveReq {
    return TempLeaveReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempLeaveReq>, I>>(object: I): TempLeaveReq {
    const message = createBaseTempLeaveReq();
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseTempLeaveRsp(): TempLeaveRsp {
  return {};
}

export const TempLeaveRsp: MessageFns<TempLeaveRsp> = {
  fromJSON(_: any): TempLeaveRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<TempLeaveRsp>, I>>(base?: I): TempLeaveRsp {
    return TempLeaveRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TempLeaveRsp>, I>>(_: I): TempLeaveRsp {
    const message = createBaseTempLeaveRsp();
    return message;
  }
};

function createBaseComeBackReq(): ComeBackReq {
  return { room_id: 0, live_id: 0 };
}

export const ComeBackReq: MessageFns<ComeBackReq> = {
  fromJSON(object: any): ComeBackReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ComeBackReq>, I>>(base?: I): ComeBackReq {
    return ComeBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ComeBackReq>, I>>(object: I): ComeBackReq {
    const message = createBaseComeBackReq();
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseComeBackRsp(): ComeBackRsp {
  return {};
}

export const ComeBackRsp: MessageFns<ComeBackRsp> = {
  fromJSON(_: any): ComeBackRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ComeBackRsp>, I>>(base?: I): ComeBackRsp {
    return ComeBackRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ComeBackRsp>, I>>(_: I): ComeBackRsp {
    const message = createBaseComeBackRsp();
    return message;
  }
};

function createBaseReceiveFreeGameRewardReq(): ReceiveFreeGameRewardReq {
  return { receive_id: 0 };
}

export const ReceiveFreeGameRewardReq: MessageFns<ReceiveFreeGameRewardReq> = {
  fromJSON(object: any): ReceiveFreeGameRewardReq {
    return { receive_id: isSet(object.receive_id) ? globalThis.Number(object.receive_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ReceiveFreeGameRewardReq>, I>>(base?: I): ReceiveFreeGameRewardReq {
    return ReceiveFreeGameRewardReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReceiveFreeGameRewardReq>, I>>(object: I): ReceiveFreeGameRewardReq {
    const message = createBaseReceiveFreeGameRewardReq();
    message.receive_id = object.receive_id ?? 0;
    return message;
  }
};

function createBaseReceiveFreeGameRewardRsp(): ReceiveFreeGameRewardRsp {
  return { room_id: 0, game_id: '' };
}

export const ReceiveFreeGameRewardRsp: MessageFns<ReceiveFreeGameRewardRsp> = {
  fromJSON(object: any): ReceiveFreeGameRewardRsp {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      game_id: isSet(object.game_id) ? globalThis.String(object.game_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<ReceiveFreeGameRewardRsp>, I>>(base?: I): ReceiveFreeGameRewardRsp {
    return ReceiveFreeGameRewardRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReceiveFreeGameRewardRsp>, I>>(object: I): ReceiveFreeGameRewardRsp {
    const message = createBaseReceiveFreeGameRewardRsp();
    message.room_id = object.room_id ?? 0;
    message.game_id = object.game_id ?? '';
    return message;
  }
};

function createBaseGetFreeGameInfoReq(): GetFreeGameInfoReq {
  return {};
}

export const GetFreeGameInfoReq: MessageFns<GetFreeGameInfoReq> = {
  fromJSON(_: any): GetFreeGameInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetFreeGameInfoReq>, I>>(base?: I): GetFreeGameInfoReq {
    return GetFreeGameInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFreeGameInfoReq>, I>>(_: I): GetFreeGameInfoReq {
    const message = createBaseGetFreeGameInfoReq();
    return message;
  }
};

function createBaseGetFreeGameInfoRsp(): GetFreeGameInfoRsp {
  return { expired_ts: 0, free_game_coin: 0, free_game_pic: '', show_entrance: 0 };
}

export const GetFreeGameInfoRsp: MessageFns<GetFreeGameInfoRsp> = {
  fromJSON(object: any): GetFreeGameInfoRsp {
    return {
      expired_ts: isSet(object.expired_ts) ? globalThis.Number(object.expired_ts) : 0,
      free_game_coin: isSet(object.free_game_coin) ? globalThis.Number(object.free_game_coin) : 0,
      free_game_pic: isSet(object.free_game_pic) ? globalThis.String(object.free_game_pic) : '',
      show_entrance: isSet(object.show_entrance) ? globalThis.Number(object.show_entrance) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetFreeGameInfoRsp>, I>>(base?: I): GetFreeGameInfoRsp {
    return GetFreeGameInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFreeGameInfoRsp>, I>>(object: I): GetFreeGameInfoRsp {
    const message = createBaseGetFreeGameInfoRsp();
    message.expired_ts = object.expired_ts ?? 0;
    message.free_game_coin = object.free_game_coin ?? 0;
    message.free_game_pic = object.free_game_pic ?? '';
    message.show_entrance = object.show_entrance ?? 0;
    return message;
  }
};

function createBaseCheckLivePermReq(): CheckLivePermReq {
  return {};
}

export const CheckLivePermReq: MessageFns<CheckLivePermReq> = {
  fromJSON(_: any): CheckLivePermReq {
    return {};
  },

  create<I extends Exact<DeepPartial<CheckLivePermReq>, I>>(base?: I): CheckLivePermReq {
    return CheckLivePermReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckLivePermReq>, I>>(_: I): CheckLivePermReq {
    const message = createBaseCheckLivePermReq();
    return message;
  }
};

function createBaseCheckLivePermRsp(): CheckLivePermRsp {
  return { perm: 0, cover: '', title: '', room_id: 0 };
}

export const CheckLivePermRsp: MessageFns<CheckLivePermRsp> = {
  fromJSON(object: any): CheckLivePermRsp {
    return {
      perm: isSet(object.perm) ? startLivePermFromJSON(object.perm) : 0,
      cover: isSet(object.cover) ? globalThis.String(object.cover) : '',
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<CheckLivePermRsp>, I>>(base?: I): CheckLivePermRsp {
    return CheckLivePermRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckLivePermRsp>, I>>(object: I): CheckLivePermRsp {
    const message = createBaseCheckLivePermRsp();
    message.perm = object.perm ?? 0;
    message.cover = object.cover ?? '';
    message.title = object.title ?? '';
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseLiveOnReq(): LiveOnReq {
  return { reconnect: false, cover: '', title: '' };
}

export const LiveOnReq: MessageFns<LiveOnReq> = {
  fromJSON(object: any): LiveOnReq {
    return {
      reconnect: isSet(object.reconnect) ? globalThis.Boolean(object.reconnect) : false,
      cover: isSet(object.cover) ? globalThis.String(object.cover) : '',
      title: isSet(object.title) ? globalThis.String(object.title) : ''
    };
  },

  create<I extends Exact<DeepPartial<LiveOnReq>, I>>(base?: I): LiveOnReq {
    return LiveOnReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LiveOnReq>, I>>(object: I): LiveOnReq {
    const message = createBaseLiveOnReq();
    message.reconnect = object.reconnect ?? false;
    message.cover = object.cover ?? '';
    message.title = object.title ?? '';
    return message;
  }
};

function createBaseLiveOnRsp(): LiveOnRsp {
  return { enter_info: undefined };
}

export const LiveOnRsp: MessageFns<LiveOnRsp> = {
  fromJSON(object: any): LiveOnRsp {
    return { enter_info: isSet(object.enter_info) ? EnterRoomRsp.fromJSON(object.enter_info) : undefined };
  },

  create<I extends Exact<DeepPartial<LiveOnRsp>, I>>(base?: I): LiveOnRsp {
    return LiveOnRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LiveOnRsp>, I>>(object: I): LiveOnRsp {
    const message = createBaseLiveOnRsp();
    message.enter_info =
      object.enter_info !== undefined && object.enter_info !== null
        ? EnterRoomRsp.fromPartial(object.enter_info)
        : undefined;
    return message;
  }
};

function createBaseLiveOffReq(): LiveOffReq {
  return { room_id: 0, live_id: 0 };
}

export const LiveOffReq: MessageFns<LiveOffReq> = {
  fromJSON(object: any): LiveOffReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<LiveOffReq>, I>>(base?: I): LiveOffReq {
    return LiveOffReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LiveOffReq>, I>>(object: I): LiveOffReq {
    const message = createBaseLiveOffReq();
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseLiveOffRsp(): LiveOffRsp {
  return {};
}

export const LiveOffRsp: MessageFns<LiveOffRsp> = {
  fromJSON(_: any): LiveOffRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<LiveOffRsp>, I>>(base?: I): LiveOffRsp {
    return LiveOffRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LiveOffRsp>, I>>(_: I): LiveOffRsp {
    const message = createBaseLiveOffRsp();
    return message;
  }
};

function createBaseGetSettleInfoReq(): GetSettleInfoReq {
  return { live_id: 0 };
}

export const GetSettleInfoReq: MessageFns<GetSettleInfoReq> = {
  fromJSON(object: any): GetSettleInfoReq {
    return { live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetSettleInfoReq>, I>>(base?: I): GetSettleInfoReq {
    return GetSettleInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSettleInfoReq>, I>>(object: I): GetSettleInfoReq {
    const message = createBaseGetSettleInfoReq();
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseGetSettleInfoRsp(): GetSettleInfoRsp {
  return {
    live_duration: 0,
    live_point: 0,
    live_gift_send_num: 0,
    live_follow_num: 0,
    live_views_num: 0,
    live_chat_video_num: 0,
    live_chat_avg_time: 0
  };
}

export const GetSettleInfoRsp: MessageFns<GetSettleInfoRsp> = {
  fromJSON(object: any): GetSettleInfoRsp {
    return {
      live_duration: isSet(object.live_duration) ? globalThis.Number(object.live_duration) : 0,
      live_point: isSet(object.live_point) ? globalThis.Number(object.live_point) : 0,
      live_gift_send_num: isSet(object.live_gift_send_num) ? globalThis.Number(object.live_gift_send_num) : 0,
      live_follow_num: isSet(object.live_follow_num) ? globalThis.Number(object.live_follow_num) : 0,
      live_views_num: isSet(object.live_views_num) ? globalThis.Number(object.live_views_num) : 0,
      live_chat_video_num: isSet(object.live_chat_video_num) ? globalThis.Number(object.live_chat_video_num) : 0,
      live_chat_avg_time: isSet(object.live_chat_avg_time) ? globalThis.Number(object.live_chat_avg_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetSettleInfoRsp>, I>>(base?: I): GetSettleInfoRsp {
    return GetSettleInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSettleInfoRsp>, I>>(object: I): GetSettleInfoRsp {
    const message = createBaseGetSettleInfoRsp();
    message.live_duration = object.live_duration ?? 0;
    message.live_point = object.live_point ?? 0;
    message.live_gift_send_num = object.live_gift_send_num ?? 0;
    message.live_follow_num = object.live_follow_num ?? 0;
    message.live_views_num = object.live_views_num ?? 0;
    message.live_chat_video_num = object.live_chat_video_num ?? 0;
    message.live_chat_avg_time = object.live_chat_avg_time ?? 0;
    return message;
  }
};

function createBaseGetLiveHomePageReq(): GetLiveHomePageReq {
  return { page: undefined, id: 0, category: 0 };
}

export const GetLiveHomePageReq: MessageFns<GetLiveHomePageReq> = {
  fromJSON(object: any): GetLiveHomePageReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category: isSet(object.category) ? roomCategoryFromJSON(object.category) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetLiveHomePageReq>, I>>(base?: I): GetLiveHomePageReq {
    return GetLiveHomePageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLiveHomePageReq>, I>>(object: I): GetLiveHomePageReq {
    const message = createBaseGetLiveHomePageReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.category = object.category ?? 0;
    return message;
  }
};

function createBaseGetLiveHomePageRsp(): GetLiveHomePageRsp {
  return { page: undefined, list: [] };
}

export const GetLiveHomePageRsp: MessageFns<GetLiveHomePageRsp> = {
  fromJSON(object: any): GetLiveHomePageRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => RoomInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetLiveHomePageRsp>, I>>(base?: I): GetLiveHomePageRsp {
    return GetLiveHomePageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLiveHomePageRsp>, I>>(object: I): GetLiveHomePageRsp {
    const message = createBaseGetLiveHomePageRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => RoomInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSetUpWishGiftListReq(): SetUpWishGiftListReq {
  return { gift_list: [], room_id: 0, live_id: 0 };
}

export const SetUpWishGiftListReq: MessageFns<SetUpWishGiftListReq> = {
  fromJSON(object: any): SetUpWishGiftListReq {
    return {
      gift_list: globalThis.Array.isArray(object?.gift_list)
        ? object.gift_list.map((e: any) => WishGiftConfig.fromJSON(e))
        : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<SetUpWishGiftListReq>, I>>(base?: I): SetUpWishGiftListReq {
    return SetUpWishGiftListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetUpWishGiftListReq>, I>>(object: I): SetUpWishGiftListReq {
    const message = createBaseSetUpWishGiftListReq();
    message.gift_list = object.gift_list?.map(e => WishGiftConfig.fromPartial(e)) || [];
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseWishGiftConfig(): WishGiftConfig {
  return { gift_id: 0, target: 0 };
}

export const WishGiftConfig: MessageFns<WishGiftConfig> = {
  fromJSON(object: any): WishGiftConfig {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      target: isSet(object.target) ? globalThis.Number(object.target) : 0
    };
  },

  create<I extends Exact<DeepPartial<WishGiftConfig>, I>>(base?: I): WishGiftConfig {
    return WishGiftConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WishGiftConfig>, I>>(object: I): WishGiftConfig {
    const message = createBaseWishGiftConfig();
    message.gift_id = object.gift_id ?? 0;
    message.target = object.target ?? 0;
    return message;
  }
};

function createBaseSetUpWishGiftListResp(): SetUpWishGiftListResp {
  return {};
}

export const SetUpWishGiftListResp: MessageFns<SetUpWishGiftListResp> = {
  fromJSON(_: any): SetUpWishGiftListResp {
    return {};
  },

  create<I extends Exact<DeepPartial<SetUpWishGiftListResp>, I>>(base?: I): SetUpWishGiftListResp {
    return SetUpWishGiftListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetUpWishGiftListResp>, I>>(_: I): SetUpWishGiftListResp {
    const message = createBaseSetUpWishGiftListResp();
    return message;
  }
};

/** LiveRoom 直播间相关接口 */
export type LiveRoomDefinition = typeof LiveRoomDefinition;
export const LiveRoomDefinition = {
  name: 'LiveRoom',
  fullName: 'room.LiveRoom',
  methods: {
    /** 刷新rtc token */
    refreshRtcToken: {
      name: 'RefreshRtcToken',
      requestType: RefreshRtcTokenReq,
      requestStream: false,
      responseType: RefreshRtcTokenRsp,
      responseStream: false,
      options: {}
    },
    /** 刷新rtm token */
    refreshRtmToken: {
      name: 'RefreshRtmToken',
      requestType: RefreshRtmTokenReq,
      requestStream: false,
      responseType: RefreshRtmTokenRsp,
      responseStream: false,
      options: {}
    },
    /** 创建房间，用户第一次进自己房间，返回 code LIVE_ROOM_NOT_CREATE，然后走创建房间流程 */
    createRoom: {
      name: 'CreateRoom',
      requestType: CreateRoomReq,
      requestStream: false,
      responseType: CreateRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询房间列表 */
    batchGetRooms: {
      name: 'BatchGetRooms',
      requestType: BatchGetRoomsReq,
      requestStream: false,
      responseType: BatchGetRoomsRsp,
      responseStream: false,
      options: {}
    },
    /** 查询自己的房间信息 */
    getMyRoom: {
      name: 'GetMyRoom',
      requestType: MyRoomReq,
      requestStream: false,
      responseType: MyRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 查询我管理房间信息 */
    getMyManageRooms: {
      name: 'GetMyManageRooms',
      requestType: MyManageRoomsReq,
      requestStream: false,
      responseType: MyManageRoomsRsp,
      responseStream: false,
      options: {}
    },
    /** 进入房间 */
    enterRoom: {
      name: 'EnterRoom',
      requestType: EnterRoomReq,
      requestStream: false,
      responseType: EnterRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 重连 */
    reenter: {
      name: 'Reenter',
      requestType: ReenterReq,
      requestStream: false,
      responseType: ReenterRsp,
      responseStream: false,
      options: {}
    },
    /** 获取房间信息，包括房间信息和状态信息 */
    getRoomInfo: {
      name: 'GetRoomInfo',
      requestType: GetRoomStateReq,
      requestStream: false,
      responseType: GetRoomStateRsp,
      responseStream: false,
      options: {}
    },
    /** 离开房间 */
    leave: {
      name: 'Leave',
      requestType: LeaveReq,
      requestStream: false,
      responseType: LeaveRsp,
      responseStream: false,
      options: {}
    },
    /** 踢出房间 */
    kickOff: {
      name: 'KickOff',
      requestType: KickOffReq,
      requestStream: false,
      responseType: KickOffRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间权限 */
    changeRoomPerm: {
      name: 'ChangeRoomPerm',
      requestType: ChangeRoomPermReq,
      requestStream: false,
      responseType: ChangeRoomPermRsp,
      responseStream: false,
      options: {}
    },
    /** 查询支持的房间模式列表 */
    listLayout: {
      name: 'ListLayout',
      requestType: ListLayoutReq,
      requestStream: false,
      responseType: ListLayoutRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间布局 */
    changeLayout: {
      name: 'ChangeLayout',
      requestType: ChangeLayoutReq,
      requestStream: false,
      responseType: ChangeLayoutRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间模式 */
    changeRoomMode: {
      name: 'ChangeRoomMode',
      requestType: ChangeRoomModeReq,
      requestStream: false,
      responseType: ChangeRoomModeRsp,
      responseStream: false,
      options: {}
    },
    /** 修改上麦模式 */
    changeSeatPerm: {
      name: 'ChangeSeatPerm',
      requestType: ChangeSeatPermReq,
      requestStream: false,
      responseType: ChangeSeatPermRsp,
      responseStream: false,
      options: {}
    },
    /** 修改麦位积分开关 */
    changeSeatScoreSwitch: {
      name: 'ChangeSeatScoreSwitch',
      requestType: ChangeSeatScoreSwitchReq,
      requestStream: false,
      responseType: ChangeSeatScoreSwitchRsp,
      responseStream: false,
      options: {}
    },
    /** 房间管理员列表 */
    getRoomAdminList: {
      name: 'GetRoomAdminList',
      requestType: ListRoomAdminReq,
      requestStream: false,
      responseType: ListRoomAdminRsp,
      responseStream: false,
      options: {}
    },
    /** 设置房间管理员 */
    setRoomAdmin: {
      name: 'SetRoomAdmin',
      requestType: SetRoomAdminReq,
      requestStream: false,
      responseType: SetRoomAdminRsp,
      responseStream: false,
      options: {}
    },
    /** 移除房间管理员 */
    removeRoomAdmin: {
      name: 'RemoveRoomAdmin',
      requestType: RemoveRoomAdminReq,
      requestStream: false,
      responseType: RemoveRoomAdminRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间名称 */
    editRoomName: {
      name: 'EditRoomName',
      requestType: SetRoomNameReq,
      requestStream: false,
      responseType: SetRoomNameRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间公告 */
    editRoomIntro: {
      name: 'EditRoomIntro',
      requestType: SetRoomIntroReq,
      requestStream: false,
      responseType: SetRoomIntroRsp,
      responseStream: false,
      options: {}
    },
    /** 修改房间封面 */
    editRoomCover: {
      name: 'EditRoomCover',
      requestType: SetRoomCoverReq,
      requestStream: false,
      responseType: SetRoomCoverRsp,
      responseStream: false,
      options: {}
    },
    /** 设置房间背景 */
    setRoomBackground: {
      name: 'SetRoomBackground',
      requestType: SetRoomBackgroundReq,
      requestStream: false,
      responseType: SetRoomBackgroundRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询房间用户信息 */
    batchGetRoomUserInfo: {
      name: 'BatchGetRoomUserInfo',
      requestType: BatchGetRoomUserInfoReq,
      requestStream: false,
      responseType: BatchGetRoomUserInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 查询用户当前所在房间 */
    getUserCurrentRoom: {
      name: 'GetUserCurrentRoom',
      requestType: GetUserCurrentRoomReq,
      requestStream: false,
      responseType: GetUserCurrentRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 创房时获取随机昵称 */
    getRoomRandomName: {
      name: 'GetRoomRandomName',
      requestType: GetRoomRandomNameReq,
      requestStream: false,
      responseType: GetRoomRandomNameRsp,
      responseStream: false,
      options: {}
    },
    /** 房间底部入口展示 */
    roomTabShow: {
      name: 'RoomTabShow',
      requestType: RoomTabShowReq,
      requestStream: false,
      responseType: RoomTabShowRsp,
      responseStream: false,
      options: {}
    },
    /** 判断是否有创房权限 */
    judgeCreateRoomPerm: {
      name: 'JudgeCreateRoomPerm',
      requestType: JudgeCreateRoomPermReq,
      requestStream: false,
      responseType: JudgeCreateRoomPermRsp,
      responseStream: false,
      options: {}
    },
    /** ListRoomValidMicUser 获取有效的麦上用户 */
    listRoomValidMicUser: {
      name: 'ListRoomValidMicUser',
      requestType: ListRoomValidMicUserReq,
      requestStream: false,
      responseType: ListRoomValidMicUserRsp,
      responseStream: false,
      options: {}
    },
    /** 麦位列表 */
    listSeat: {
      name: 'ListSeat',
      requestType: ListSeatReq,
      requestStream: false,
      responseType: ListSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 上麦/换麦 */
    enterSeat: {
      name: 'EnterSeat',
      requestType: EnterSeatReq,
      requestStream: false,
      responseType: EnterSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 下麦 */
    leaveSeat: {
      name: 'LeaveSeat',
      requestType: LeaveSeatReq,
      requestStream: false,
      responseType: LeaveSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 开麦：麦上用户将自己麦位的麦克风开启，若被房主/管理员禁音，则无法开麦 */
    openMic: {
      name: 'OpenMic',
      requestType: OpenMicReq,
      requestStream: false,
      responseType: OpenMicRsp,
      responseStream: false,
      options: {}
    },
    /** 闭麦：麦上用户将自己麦位的麦克风关闭 */
    closeMic: {
      name: 'CloseMic',
      requestType: CloseMicReq,
      requestStream: false,
      responseType: CloseMicRsp,
      responseStream: false,
      options: {}
    },
    /** 申请上麦 */
    applyEnterSeat: {
      name: 'ApplyEnterSeat',
      requestType: ApplyEnterSeatReq,
      requestStream: false,
      responseType: ApplyEnterSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 获取申请上麦状态 */
    getApplyEnterSeatStatus: {
      name: 'GetApplyEnterSeatStatus',
      requestType: GetApplyEnterSeatStatusReq,
      requestStream: false,
      responseType: GetApplyEnterSeatStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 取消申请上麦 */
    cancelApplyEnterSeat: {
      name: 'CancelApplyEnterSeat',
      requestType: CancelApplyEnterSeatReq,
      requestStream: false,
      responseType: CancelApplyEnterSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 接受邀请上麦 */
    acceptSeatInvitation: {
      name: 'AcceptSeatInvitation',
      requestType: AcceptSeatInvitationReq,
      requestStream: false,
      responseType: AcceptSeatInvitationRsp,
      responseStream: false,
      options: {}
    },
    /** 拒绝邀请上麦 */
    rejectSeatInvitation: {
      name: 'RejectSeatInvitation',
      requestType: RejectSeatInvitationReq,
      requestStream: false,
      responseType: RejectSeatInvitationRsp,
      responseStream: false,
      options: {}
    },
    /** 麦位封锁：将该麦位进行封锁，封锁后用户无法点击进行上麦 */
    lockSeat: {
      name: 'LockSeat',
      requestType: LockSeatReq,
      requestStream: false,
      responseType: LockSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 解除封锁：将该麦位进行解封，解封后用户可点击进行上麦 */
    unlockSeat: {
      name: 'UnlockSeat',
      requestType: UnlockSeatReq,
      requestStream: false,
      responseType: UnlockSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 麦位禁音：将该麦位的麦克风禁音，禁音后该麦位仍可进行上下麦，但无法进行发音 */
    muteSeat: {
      name: 'MuteSeat',
      requestType: MuteSeatReq,
      requestStream: false,
      responseType: MuteSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 解除麦位禁音：将该麦位的禁音进行解除，解除后用户在该麦位可进行发音 */
    unmuteSeat: {
      name: 'UnmuteSeat',
      requestType: UnmuteSeatReq,
      requestStream: false,
      responseType: UnmuteSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 踢下麦：将麦上用户踢出麦位，但用户仍在该房间内，没有被踢出房间 */
    kickSeat: {
      name: 'KickSeat',
      requestType: KickSeatReq,
      requestStream: false,
      responseType: KickSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 抱上麦：将房间内的用户邀请上麦，支持需/无需该用户本人同意，默认为需本人同意 */
    pickUpSeat: {
      name: 'PickUpSeat',
      requestType: PickUpSeatReq,
      requestStream: false,
      responseType: PickUpSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 申请上麦用户列表 */
    applyEnterSeatUserList: {
      name: 'ApplyEnterSeatUserList',
      requestType: ApplyEnterSeatUserListReq,
      requestStream: false,
      responseType: ApplyEnterSeatUserListRsp,
      responseStream: false,
      options: {}
    },
    /** 申请上麦审批-同意 */
    approveApplyEnterSeat: {
      name: 'ApproveApplyEnterSeat',
      requestType: ApproveApplyEnterSeatReq,
      requestStream: false,
      responseType: ApproveApplyEnterSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 申请上麦审批-拒绝 */
    rejectApplyEnterSeat: {
      name: 'RejectApplyEnterSeat',
      requestType: RejectApplyEnterSeatReq,
      requestStream: false,
      responseType: RejectApplyEnterSeatRsp,
      responseStream: false,
      options: {}
    },
    /** 查询房间在线的人列表 */
    listOnlineUser: {
      name: 'ListOnlineUser',
      requestType: ListOnlineUserReq,
      requestStream: false,
      responseType: ListOnlineUserRsp,
      responseStream: false,
      options: {}
    },
    /** 进房成功才调用的接口 可以把进房后需要展示的资源信息展示在这里 */
    enterRoomExtra: {
      name: 'EnterRoomExtra',
      requestType: EnterRoomExtraReq,
      requestStream: false,
      responseType: EnterRoomExtraRsp,
      responseStream: false,
      options: {}
    },
    /** 暂时离开房间 */
    tempLeave: {
      name: 'TempLeave',
      requestType: TempLeaveReq,
      requestStream: false,
      responseType: TempLeaveRsp,
      responseStream: false,
      options: {}
    },
    /** 回到房间 */
    comeBack: {
      name: 'ComeBack',
      requestType: ComeBackReq,
      requestStream: false,
      responseType: ComeBackRsp,
      responseStream: false,
      options: {}
    },
    /** 领取奖励并调整弹窗 */
    receiveFreeGameReward: {
      name: 'ReceiveFreeGameReward',
      requestType: ReceiveFreeGameRewardReq,
      requestStream: false,
      responseType: ReceiveFreeGameRewardRsp,
      responseStream: false,
      options: {}
    },
    /** 拉取游戏弹窗调用接口 */
    getFreeGameInfo: {
      name: 'GetFreeGameInfo',
      requestType: GetFreeGameInfoReq,
      requestStream: false,
      responseType: GetFreeGameInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 校验开播权限 */
    checkLivePerm: {
      name: 'CheckLivePerm',
      requestType: CheckLivePermReq,
      requestStream: false,
      responseType: CheckLivePermRsp,
      responseStream: false,
      options: {}
    },
    /** 主播开播 */
    liveOn: {
      name: 'LiveOn',
      requestType: LiveOnReq,
      requestStream: false,
      responseType: LiveOnRsp,
      responseStream: false,
      options: {}
    },
    /** 主播下播 */
    liveOff: {
      name: 'LiveOff',
      requestType: LiveOffReq,
      requestStream: false,
      responseType: LiveOffRsp,
      responseStream: false,
      options: {}
    },
    /** 根据liveId获取场次结算数据 */
    getSettleInfoByLiveId: {
      name: 'GetSettleInfoByLiveId',
      requestType: GetSettleInfoReq,
      requestStream: false,
      responseType: GetSettleInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 首页接口 hot following history */
    getLiveHomePage: {
      name: 'GetLiveHomePage',
      requestType: GetLiveHomePageReq,
      requestStream: false,
      responseType: GetLiveHomePageRsp,
      responseStream: false,
      options: {}
    },
    /** 主播端设置心愿礼物 */
    setUpWishGiftList: {
      name: 'SetUpWishGiftList',
      requestType: SetUpWishGiftListReq,
      requestStream: false,
      responseType: SetUpWishGiftListResp,
      responseStream: false,
      options: {}
    },
    /** 获取心愿礼物统计列表 */
    getGiftWishlist: {
      name: 'GetGiftWishlist',
      requestType: GiftWishlistReq,
      requestStream: false,
      responseType: GiftWishlistRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
