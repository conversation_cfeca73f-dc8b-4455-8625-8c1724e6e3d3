// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/score_feedback.proto

/* eslint-disable */

export const protobufPackage = 'score';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/score */

export interface GetScoreFeedBackReq {
  /** 评价星级，传递1-5 */
  score: number;
}

export interface GetScoreFeedBackResp {
  /** 评分弹窗内容 */
  items: ScoreFeedBackItem[];
  /** 奖励数量 */
  reword_num: number;
  /** 奖励类型，1:视频卡，2:聊天卡，2:金币 */
  reword_type: number;
}

export interface ScoreFeedBackItem {
  key: string;
  val: string;
  emoji: string;
}

export interface ReportScoreFeedBackReq {
  /** 评分key */
  key: string;
  /** 奖励类型 */
  reward_type: number;
  /** 奖励数量 */
  reward_num: number;
}

export interface ReportScoreFeedBackResp {}

function createBaseGetScoreFeedBackReq(): GetScoreFeedBackReq {
  return { score: 0 };
}

export const GetScoreFeedBackReq: MessageFns<GetScoreFeedBackReq> = {
  fromJSON(object: any): GetScoreFeedBackReq {
    return { score: isSet(object.score) ? globalThis.Number(object.score) : 0 };
  },

  create<I extends Exact<DeepPartial<GetScoreFeedBackReq>, I>>(base?: I): GetScoreFeedBackReq {
    return GetScoreFeedBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetScoreFeedBackReq>, I>>(object: I): GetScoreFeedBackReq {
    const message = createBaseGetScoreFeedBackReq();
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseGetScoreFeedBackResp(): GetScoreFeedBackResp {
  return { items: [], reword_num: 0, reword_type: 0 };
}

export const GetScoreFeedBackResp: MessageFns<GetScoreFeedBackResp> = {
  fromJSON(object: any): GetScoreFeedBackResp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => ScoreFeedBackItem.fromJSON(e)) : [],
      reword_num: isSet(object.reword_num) ? globalThis.Number(object.reword_num) : 0,
      reword_type: isSet(object.reword_type) ? globalThis.Number(object.reword_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetScoreFeedBackResp>, I>>(base?: I): GetScoreFeedBackResp {
    return GetScoreFeedBackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetScoreFeedBackResp>, I>>(object: I): GetScoreFeedBackResp {
    const message = createBaseGetScoreFeedBackResp();
    message.items = object.items?.map(e => ScoreFeedBackItem.fromPartial(e)) || [];
    message.reword_num = object.reword_num ?? 0;
    message.reword_type = object.reword_type ?? 0;
    return message;
  }
};

function createBaseScoreFeedBackItem(): ScoreFeedBackItem {
  return { key: '', val: '', emoji: '' };
}

export const ScoreFeedBackItem: MessageFns<ScoreFeedBackItem> = {
  fromJSON(object: any): ScoreFeedBackItem {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      val: isSet(object.val) ? globalThis.String(object.val) : '',
      emoji: isSet(object.emoji) ? globalThis.String(object.emoji) : ''
    };
  },

  create<I extends Exact<DeepPartial<ScoreFeedBackItem>, I>>(base?: I): ScoreFeedBackItem {
    return ScoreFeedBackItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScoreFeedBackItem>, I>>(object: I): ScoreFeedBackItem {
    const message = createBaseScoreFeedBackItem();
    message.key = object.key ?? '';
    message.val = object.val ?? '';
    message.emoji = object.emoji ?? '';
    return message;
  }
};

function createBaseReportScoreFeedBackReq(): ReportScoreFeedBackReq {
  return { key: '', reward_type: 0, reward_num: 0 };
}

export const ReportScoreFeedBackReq: MessageFns<ReportScoreFeedBackReq> = {
  fromJSON(object: any): ReportScoreFeedBackReq {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      reward_type: isSet(object.reward_type) ? globalThis.Number(object.reward_type) : 0,
      reward_num: isSet(object.reward_num) ? globalThis.Number(object.reward_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<ReportScoreFeedBackReq>, I>>(base?: I): ReportScoreFeedBackReq {
    return ReportScoreFeedBackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportScoreFeedBackReq>, I>>(object: I): ReportScoreFeedBackReq {
    const message = createBaseReportScoreFeedBackReq();
    message.key = object.key ?? '';
    message.reward_type = object.reward_type ?? 0;
    message.reward_num = object.reward_num ?? 0;
    return message;
  }
};

function createBaseReportScoreFeedBackResp(): ReportScoreFeedBackResp {
  return {};
}

export const ReportScoreFeedBackResp: MessageFns<ReportScoreFeedBackResp> = {
  fromJSON(_: any): ReportScoreFeedBackResp {
    return {};
  },

  create<I extends Exact<DeepPartial<ReportScoreFeedBackResp>, I>>(base?: I): ReportScoreFeedBackResp {
    return ReportScoreFeedBackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportScoreFeedBackResp>, I>>(_: I): ReportScoreFeedBackResp {
    const message = createBaseReportScoreFeedBackResp();
    return message;
  }
};

export type ScoreDefinition = typeof ScoreDefinition;
export const ScoreDefinition = {
  name: 'Score',
  fullName: 'score.Score',
  methods: {
    /** 获取评分反馈弹窗 */
    getScoreFeedBack: {
      name: 'GetScoreFeedBack',
      requestType: GetScoreFeedBackReq,
      requestStream: false,
      responseType: GetScoreFeedBackResp,
      responseStream: false,
      options: {}
    },
    /** 领取反馈弹窗奖励 */
    reportScoreFeedBack: {
      name: 'ReportScoreFeedBack',
      requestType: ReportScoreFeedBackReq,
      requestStream: false,
      responseType: ReportScoreFeedBackResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
