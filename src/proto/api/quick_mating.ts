// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/quick_mating.proto

/* eslint-disable */

export const protobufPackage = 'quickmating';

export interface MatingBusyStrategyReq {}

export interface MatingBusyStrategyResp {
  strategy: number;
}

function createBaseMatingBusyStrategyReq(): MatingBusyStrategyReq {
  return {};
}

export const MatingBusyStrategyReq: MessageFns<MatingBusyStrategyReq> = {
  fromJSON(_: any): MatingBusyStrategyReq {
    return {};
  },

  create<I extends Exact<DeepPartial<MatingBusyStrategyReq>, I>>(base?: I): MatingBusyStrategyReq {
    return MatingBusyStrategyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MatingBusyStrategyReq>, I>>(_: I): MatingBusyStrategyReq {
    const message = createBaseMatingBusyStrategyReq();
    return message;
  }
};

function createBaseMatingBusyStrategyResp(): MatingBusyStrategyResp {
  return { strategy: 0 };
}

export const MatingBusyStrategyResp: MessageFns<MatingBusyStrategyResp> = {
  fromJSON(object: any): MatingBusyStrategyResp {
    return { strategy: isSet(object.strategy) ? globalThis.Number(object.strategy) : 0 };
  },

  create<I extends Exact<DeepPartial<MatingBusyStrategyResp>, I>>(base?: I): MatingBusyStrategyResp {
    return MatingBusyStrategyResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MatingBusyStrategyResp>, I>>(object: I): MatingBusyStrategyResp {
    const message = createBaseMatingBusyStrategyResp();
    message.strategy = object.strategy ?? 0;
    return message;
  }
};

export type QuickMatingDefinition = typeof QuickMatingDefinition;
export const QuickMatingDefinition = {
  name: 'QuickMating',
  fullName: 'quickmating.QuickMating',
  methods: {
    matingBusyStrategy: {
      name: 'MatingBusyStrategy',
      requestType: MatingBusyStrategyReq,
      requestStream: false,
      responseType: MatingBusyStrategyResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
