// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/search.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';
import { RoomInfo } from './comm';

export const protobufPackage = 'search';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/search/handler/ */

/** 搜索用户 */
export interface SearchUserReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 搜索关键字 */
  keyword: string;
}

/** 搜索房间 */
export interface SearchRoomReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 搜索关键字 */
  keyword: string;
}

/** 搜索房间 */
export interface SearchRoomRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 房间列表 */
  rooms: RoomInfo[];
}

function createBaseSearchUserReq(): SearchUserReq {
  return { page: undefined, keyword: '' };
}

export const SearchUserReq: MessageFns<SearchUserReq> = {
  fromJSON(object: any): SearchUserReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : ''
    };
  },

  create<I extends Exact<DeepPartial<SearchUserReq>, I>>(base?: I): SearchUserReq {
    return SearchUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchUserReq>, I>>(object: I): SearchUserReq {
    const message = createBaseSearchUserReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.keyword = object.keyword ?? '';
    return message;
  }
};

function createBaseSearchRoomReq(): SearchRoomReq {
  return { page: undefined, keyword: '' };
}

export const SearchRoomReq: MessageFns<SearchRoomReq> = {
  fromJSON(object: any): SearchRoomReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : ''
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomReq>, I>>(base?: I): SearchRoomReq {
    return SearchRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomReq>, I>>(object: I): SearchRoomReq {
    const message = createBaseSearchRoomReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.keyword = object.keyword ?? '';
    return message;
  }
};

function createBaseSearchRoomRsp(): SearchRoomRsp {
  return { page: undefined, rooms: [] };
}

export const SearchRoomRsp: MessageFns<SearchRoomRsp> = {
  fromJSON(object: any): SearchRoomRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      rooms: globalThis.Array.isArray(object?.rooms) ? object.rooms.map((e: any) => RoomInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRoomRsp>, I>>(base?: I): SearchRoomRsp {
    return SearchRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRoomRsp>, I>>(object: I): SearchRoomRsp {
    const message = createBaseSearchRoomRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.rooms = object.rooms?.map(e => RoomInfo.fromPartial(e)) || [];
    return message;
  }
};

/** 搜索服务 */
export type SearchDefinition = typeof SearchDefinition;
export const SearchDefinition = {
  name: 'Search',
  fullName: 'search.Search',
  methods: {
    /** 搜索房间 */
    searchRoom: {
      name: 'SearchRoom',
      requestType: SearchRoomReq,
      requestStream: false,
      responseType: SearchRoomRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
