// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/commsrv.proto

/* eslint-disable */
import { SquarePopUpWindowSceneType, squarePopUpWindowSceneTypeFromJSON } from './notify';

export const protobufPackage = 'commsrv';

export enum MatchVideoStrategy {
  MATCH_VIDEO_NONE = 0,
  /** MATCH_POP_UP - 旧逻辑 */
  MATCH_POP_UP = 1,
  /** MATCH_RECOMMEND - 新逻辑 直接chat action */
  MATCH_RECOMMEND = 2,
  UNRECOGNIZED = -1
}

export function matchVideoStrategyFromJSON(object: any): MatchVideoStrategy {
  switch (object) {
    case 0:
    case 'MATCH_VIDEO_NONE':
      return MatchVideoStrategy.MATCH_VIDEO_NONE;
    case 1:
    case 'MATCH_POP_UP':
      return MatchVideoStrategy.MATCH_POP_UP;
    case 2:
    case 'MATCH_RECOMMEND':
      return MatchVideoStrategy.MATCH_RECOMMEND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MatchVideoStrategy.UNRECOGNIZED;
  }
}

/** 上报场景值枚举 */
export enum ReportPopUpWindowSceneType {
  REPORT_SCENE_NONE = 0,
  /** REPORT_SCENE_RECHARGE_PAGE_CLOSE - recharge_page_close */
  REPORT_SCENE_RECHARGE_PAGE_CLOSE = 1,
  UNRECOGNIZED = -1
}

export function reportPopUpWindowSceneTypeFromJSON(object: any): ReportPopUpWindowSceneType {
  switch (object) {
    case 0:
    case 'REPORT_SCENE_NONE':
      return ReportPopUpWindowSceneType.REPORT_SCENE_NONE;
    case 1:
    case 'REPORT_SCENE_RECHARGE_PAGE_CLOSE':
      return ReportPopUpWindowSceneType.REPORT_SCENE_RECHARGE_PAGE_CLOSE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ReportPopUpWindowSceneType.UNRECOGNIZED;
  }
}

export interface CleanUserCoinReq {
  uid: string;
  all_close_user: boolean;
  all_no_active: number;
}

export interface CleanUserCoinResp {
  msg: string;
}

export interface CreateWithdrawOrderReq {
  order_no: string;
  uid: string;
  bank_account: string;
  payee_phone_number: string;
  amount: string;
  account_name: string;
  pkg: string;
}

export interface CreateWithdrawOrderResp {
  msg: string;
}

export interface WithdrawDoneReq {
  w_order_id: string;
  /** 提现状态 1-执行中 2-执行成功 3-执行失败 */
  withdraw_status: string;
}

export interface WithdrawDoneResp {
  msg: string;
}

export interface EchoReq {
  data: string;
}

export interface EchoRsp {
  data: string;
}

export interface OnlineNotifyReq {
  msg: WsMessage | undefined;
}

export interface OnlineNotifyRsp {}

export interface WsMessage {
  type: number;
  path: string;
  data: string;
}

export interface WsMessageBatch {
  msgs: WsMessage[];
}

export interface Notify {
  msgs: WsMessageBatch | undefined;
}

export interface HeartBeatReq {
  /** 是否前后台 */
  is_foreground: number;
  /** 是否在屏蔽页 */
  in_block: number;
  /** 在线时长 */
  interval: number;
  /** 通知权限 */
  notify_permission: number;
  /** 弹窗权限 */
  floating_permission: number;
  /** 相机权限 */
  camera_permission: number;
  /**  */
  mic_permission: number;
  /** 快捷方式 */
  add_shortcut: number;
  /** google广告id */
  gaid: string;
  /** 是否有悬浮小窗 */
  float_window_show: number;
  /** 当前事件 */
  state: string;
}

export interface HeartBeatResp {
  in_counting_time: number;
  is_time_change: number;
  now_sec: number;
}

export interface NotifyPopUpWindowReq {
  /** 事件id */
  event_id: string;
  /** 场景值 */
  scene: SquarePopUpWindowSceneType;
}

export interface NotifyPopUpWindowRsp {}

export interface GetMatchVideoStrategyReq {}

export interface GetMatchVideoStrategyRsp {
  strategy: MatchVideoStrategy;
}

export interface GetBottomNavigationReq {}

export interface GetBottomNavigationRsp {
  navigation_bars: string[];
}

export interface GetAnchorApproveConfigReq {}

export interface GetAnchorApproveConfigRsp {
  /** 照片好例子 */
  good_list: RegisterPicExample[];
  /** 照片反例 */
  bad_list: RegisterPicExample[];
  /** 介绍视频最低时长 */
  min_duration: number;
  /** 介绍视频最高时长 */
  max_duration: number;
  /** 介绍视频好例子 */
  video_good_list: RegisterPicExample[];
  /** 介绍视频反例 */
  video_bad_list: RegisterPicExample[];
  /** 介绍视频播放地址 */
  video_example_url: string;
  /** /照片上传限制 */
  photo_num_limit: number;
}

export interface RegisterPicExample {
  /** 反例照片地址 */
  picUrl: string;
  /** 反例文案说明 */
  intro: string;
}

export interface ReportPopUpWindowReq {
  /** 事件值 */
  business_id: string;
  /** 场景值 */
  scene: ReportPopUpWindowSceneType;
}

export interface ReportPopUpWindowRsp {}

function createBaseCleanUserCoinReq(): CleanUserCoinReq {
  return { uid: '', all_close_user: false, all_no_active: 0 };
}

export const CleanUserCoinReq: MessageFns<CleanUserCoinReq> = {
  fromJSON(object: any): CleanUserCoinReq {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      all_close_user: isSet(object.all_close_user) ? globalThis.Boolean(object.all_close_user) : false,
      all_no_active: isSet(object.all_no_active) ? globalThis.Number(object.all_no_active) : 0
    };
  },

  create<I extends Exact<DeepPartial<CleanUserCoinReq>, I>>(base?: I): CleanUserCoinReq {
    return CleanUserCoinReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CleanUserCoinReq>, I>>(object: I): CleanUserCoinReq {
    const message = createBaseCleanUserCoinReq();
    message.uid = object.uid ?? '';
    message.all_close_user = object.all_close_user ?? false;
    message.all_no_active = object.all_no_active ?? 0;
    return message;
  }
};

function createBaseCleanUserCoinResp(): CleanUserCoinResp {
  return { msg: '' };
}

export const CleanUserCoinResp: MessageFns<CleanUserCoinResp> = {
  fromJSON(object: any): CleanUserCoinResp {
    return { msg: isSet(object.msg) ? globalThis.String(object.msg) : '' };
  },

  create<I extends Exact<DeepPartial<CleanUserCoinResp>, I>>(base?: I): CleanUserCoinResp {
    return CleanUserCoinResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CleanUserCoinResp>, I>>(object: I): CleanUserCoinResp {
    const message = createBaseCleanUserCoinResp();
    message.msg = object.msg ?? '';
    return message;
  }
};

function createBaseCreateWithdrawOrderReq(): CreateWithdrawOrderReq {
  return { order_no: '', uid: '', bank_account: '', payee_phone_number: '', amount: '', account_name: '', pkg: '' };
}

export const CreateWithdrawOrderReq: MessageFns<CreateWithdrawOrderReq> = {
  fromJSON(object: any): CreateWithdrawOrderReq {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      bank_account: isSet(object.bank_account) ? globalThis.String(object.bank_account) : '',
      payee_phone_number: isSet(object.payee_phone_number) ? globalThis.String(object.payee_phone_number) : '',
      amount: isSet(object.amount) ? globalThis.String(object.amount) : '',
      account_name: isSet(object.account_name) ? globalThis.String(object.account_name) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateWithdrawOrderReq>, I>>(base?: I): CreateWithdrawOrderReq {
    return CreateWithdrawOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateWithdrawOrderReq>, I>>(object: I): CreateWithdrawOrderReq {
    const message = createBaseCreateWithdrawOrderReq();
    message.order_no = object.order_no ?? '';
    message.uid = object.uid ?? '';
    message.bank_account = object.bank_account ?? '';
    message.payee_phone_number = object.payee_phone_number ?? '';
    message.amount = object.amount ?? '';
    message.account_name = object.account_name ?? '';
    message.pkg = object.pkg ?? '';
    return message;
  }
};

function createBaseCreateWithdrawOrderResp(): CreateWithdrawOrderResp {
  return { msg: '' };
}

export const CreateWithdrawOrderResp: MessageFns<CreateWithdrawOrderResp> = {
  fromJSON(object: any): CreateWithdrawOrderResp {
    return { msg: isSet(object.msg) ? globalThis.String(object.msg) : '' };
  },

  create<I extends Exact<DeepPartial<CreateWithdrawOrderResp>, I>>(base?: I): CreateWithdrawOrderResp {
    return CreateWithdrawOrderResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateWithdrawOrderResp>, I>>(object: I): CreateWithdrawOrderResp {
    const message = createBaseCreateWithdrawOrderResp();
    message.msg = object.msg ?? '';
    return message;
  }
};

function createBaseWithdrawDoneReq(): WithdrawDoneReq {
  return { w_order_id: '', withdraw_status: '' };
}

export const WithdrawDoneReq: MessageFns<WithdrawDoneReq> = {
  fromJSON(object: any): WithdrawDoneReq {
    return {
      w_order_id: isSet(object.w_order_id) ? globalThis.String(object.w_order_id) : '',
      withdraw_status: isSet(object.withdraw_status) ? globalThis.String(object.withdraw_status) : ''
    };
  },

  create<I extends Exact<DeepPartial<WithdrawDoneReq>, I>>(base?: I): WithdrawDoneReq {
    return WithdrawDoneReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawDoneReq>, I>>(object: I): WithdrawDoneReq {
    const message = createBaseWithdrawDoneReq();
    message.w_order_id = object.w_order_id ?? '';
    message.withdraw_status = object.withdraw_status ?? '';
    return message;
  }
};

function createBaseWithdrawDoneResp(): WithdrawDoneResp {
  return { msg: '' };
}

export const WithdrawDoneResp: MessageFns<WithdrawDoneResp> = {
  fromJSON(object: any): WithdrawDoneResp {
    return { msg: isSet(object.msg) ? globalThis.String(object.msg) : '' };
  },

  create<I extends Exact<DeepPartial<WithdrawDoneResp>, I>>(base?: I): WithdrawDoneResp {
    return WithdrawDoneResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawDoneResp>, I>>(object: I): WithdrawDoneResp {
    const message = createBaseWithdrawDoneResp();
    message.msg = object.msg ?? '';
    return message;
  }
};

function createBaseEchoReq(): EchoReq {
  return { data: '' };
}

export const EchoReq: MessageFns<EchoReq> = {
  fromJSON(object: any): EchoReq {
    return { data: isSet(object.data) ? globalThis.String(object.data) : '' };
  },

  create<I extends Exact<DeepPartial<EchoReq>, I>>(base?: I): EchoReq {
    return EchoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EchoReq>, I>>(object: I): EchoReq {
    const message = createBaseEchoReq();
    message.data = object.data ?? '';
    return message;
  }
};

function createBaseEchoRsp(): EchoRsp {
  return { data: '' };
}

export const EchoRsp: MessageFns<EchoRsp> = {
  fromJSON(object: any): EchoRsp {
    return { data: isSet(object.data) ? globalThis.String(object.data) : '' };
  },

  create<I extends Exact<DeepPartial<EchoRsp>, I>>(base?: I): EchoRsp {
    return EchoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EchoRsp>, I>>(object: I): EchoRsp {
    const message = createBaseEchoRsp();
    message.data = object.data ?? '';
    return message;
  }
};

function createBaseOnlineNotifyReq(): OnlineNotifyReq {
  return { msg: undefined };
}

export const OnlineNotifyReq: MessageFns<OnlineNotifyReq> = {
  fromJSON(object: any): OnlineNotifyReq {
    return { msg: isSet(object.msg) ? WsMessage.fromJSON(object.msg) : undefined };
  },

  create<I extends Exact<DeepPartial<OnlineNotifyReq>, I>>(base?: I): OnlineNotifyReq {
    return OnlineNotifyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineNotifyReq>, I>>(object: I): OnlineNotifyReq {
    const message = createBaseOnlineNotifyReq();
    message.msg = object.msg !== undefined && object.msg !== null ? WsMessage.fromPartial(object.msg) : undefined;
    return message;
  }
};

function createBaseOnlineNotifyRsp(): OnlineNotifyRsp {
  return {};
}

export const OnlineNotifyRsp: MessageFns<OnlineNotifyRsp> = {
  fromJSON(_: any): OnlineNotifyRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<OnlineNotifyRsp>, I>>(base?: I): OnlineNotifyRsp {
    return OnlineNotifyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnlineNotifyRsp>, I>>(_: I): OnlineNotifyRsp {
    const message = createBaseOnlineNotifyRsp();
    return message;
  }
};

function createBaseWsMessage(): WsMessage {
  return { type: 0, path: '', data: '' };
}

export const WsMessage: MessageFns<WsMessage> = {
  fromJSON(object: any): WsMessage {
    return {
      type: isSet(object.type) ? globalThis.Number(object.type) : 0,
      path: isSet(object.path) ? globalThis.String(object.path) : '',
      data: isSet(object.data) ? globalThis.String(object.data) : ''
    };
  },

  create<I extends Exact<DeepPartial<WsMessage>, I>>(base?: I): WsMessage {
    return WsMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WsMessage>, I>>(object: I): WsMessage {
    const message = createBaseWsMessage();
    message.type = object.type ?? 0;
    message.path = object.path ?? '';
    message.data = object.data ?? '';
    return message;
  }
};

function createBaseWsMessageBatch(): WsMessageBatch {
  return { msgs: [] };
}

export const WsMessageBatch: MessageFns<WsMessageBatch> = {
  fromJSON(object: any): WsMessageBatch {
    return { msgs: globalThis.Array.isArray(object?.msgs) ? object.msgs.map((e: any) => WsMessage.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<WsMessageBatch>, I>>(base?: I): WsMessageBatch {
    return WsMessageBatch.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WsMessageBatch>, I>>(object: I): WsMessageBatch {
    const message = createBaseWsMessageBatch();
    message.msgs = object.msgs?.map(e => WsMessage.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotify(): Notify {
  return { msgs: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return { msgs: isSet(object.msgs) ? WsMessageBatch.fromJSON(object.msgs) : undefined };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.msgs =
      object.msgs !== undefined && object.msgs !== null ? WsMessageBatch.fromPartial(object.msgs) : undefined;
    return message;
  }
};

function createBaseHeartBeatReq(): HeartBeatReq {
  return {
    is_foreground: 0,
    in_block: 0,
    interval: 0,
    notify_permission: 0,
    floating_permission: 0,
    camera_permission: 0,
    mic_permission: 0,
    add_shortcut: 0,
    gaid: '',
    float_window_show: 0,
    state: ''
  };
}

export const HeartBeatReq: MessageFns<HeartBeatReq> = {
  fromJSON(object: any): HeartBeatReq {
    return {
      is_foreground: isSet(object.is_foreground) ? globalThis.Number(object.is_foreground) : 0,
      in_block: isSet(object.in_block) ? globalThis.Number(object.in_block) : 0,
      interval: isSet(object.interval) ? globalThis.Number(object.interval) : 0,
      notify_permission: isSet(object.notify_permission) ? globalThis.Number(object.notify_permission) : 0,
      floating_permission: isSet(object.floating_permission) ? globalThis.Number(object.floating_permission) : 0,
      camera_permission: isSet(object.camera_permission) ? globalThis.Number(object.camera_permission) : 0,
      mic_permission: isSet(object.mic_permission) ? globalThis.Number(object.mic_permission) : 0,
      add_shortcut: isSet(object.add_shortcut) ? globalThis.Number(object.add_shortcut) : 0,
      gaid: isSet(object.gaid) ? globalThis.String(object.gaid) : '',
      float_window_show: isSet(object.float_window_show) ? globalThis.Number(object.float_window_show) : 0,
      state: isSet(object.state) ? globalThis.String(object.state) : ''
    };
  },

  create<I extends Exact<DeepPartial<HeartBeatReq>, I>>(base?: I): HeartBeatReq {
    return HeartBeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeartBeatReq>, I>>(object: I): HeartBeatReq {
    const message = createBaseHeartBeatReq();
    message.is_foreground = object.is_foreground ?? 0;
    message.in_block = object.in_block ?? 0;
    message.interval = object.interval ?? 0;
    message.notify_permission = object.notify_permission ?? 0;
    message.floating_permission = object.floating_permission ?? 0;
    message.camera_permission = object.camera_permission ?? 0;
    message.mic_permission = object.mic_permission ?? 0;
    message.add_shortcut = object.add_shortcut ?? 0;
    message.gaid = object.gaid ?? '';
    message.float_window_show = object.float_window_show ?? 0;
    message.state = object.state ?? '';
    return message;
  }
};

function createBaseHeartBeatResp(): HeartBeatResp {
  return { in_counting_time: 0, is_time_change: 0, now_sec: 0 };
}

export const HeartBeatResp: MessageFns<HeartBeatResp> = {
  fromJSON(object: any): HeartBeatResp {
    return {
      in_counting_time: isSet(object.in_counting_time) ? globalThis.Number(object.in_counting_time) : 0,
      is_time_change: isSet(object.is_time_change) ? globalThis.Number(object.is_time_change) : 0,
      now_sec: isSet(object.now_sec) ? globalThis.Number(object.now_sec) : 0
    };
  },

  create<I extends Exact<DeepPartial<HeartBeatResp>, I>>(base?: I): HeartBeatResp {
    return HeartBeatResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeartBeatResp>, I>>(object: I): HeartBeatResp {
    const message = createBaseHeartBeatResp();
    message.in_counting_time = object.in_counting_time ?? 0;
    message.is_time_change = object.is_time_change ?? 0;
    message.now_sec = object.now_sec ?? 0;
    return message;
  }
};

function createBaseNotifyPopUpWindowReq(): NotifyPopUpWindowReq {
  return { event_id: '', scene: 0 };
}

export const NotifyPopUpWindowReq: MessageFns<NotifyPopUpWindowReq> = {
  fromJSON(object: any): NotifyPopUpWindowReq {
    return {
      event_id: isSet(object.event_id) ? globalThis.String(object.event_id) : '',
      scene: isSet(object.scene) ? squarePopUpWindowSceneTypeFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyPopUpWindowReq>, I>>(base?: I): NotifyPopUpWindowReq {
    return NotifyPopUpWindowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyPopUpWindowReq>, I>>(object: I): NotifyPopUpWindowReq {
    const message = createBaseNotifyPopUpWindowReq();
    message.event_id = object.event_id ?? '';
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseNotifyPopUpWindowRsp(): NotifyPopUpWindowRsp {
  return {};
}

export const NotifyPopUpWindowRsp: MessageFns<NotifyPopUpWindowRsp> = {
  fromJSON(_: any): NotifyPopUpWindowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<NotifyPopUpWindowRsp>, I>>(base?: I): NotifyPopUpWindowRsp {
    return NotifyPopUpWindowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyPopUpWindowRsp>, I>>(_: I): NotifyPopUpWindowRsp {
    const message = createBaseNotifyPopUpWindowRsp();
    return message;
  }
};

function createBaseGetMatchVideoStrategyReq(): GetMatchVideoStrategyReq {
  return {};
}

export const GetMatchVideoStrategyReq: MessageFns<GetMatchVideoStrategyReq> = {
  fromJSON(_: any): GetMatchVideoStrategyReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetMatchVideoStrategyReq>, I>>(base?: I): GetMatchVideoStrategyReq {
    return GetMatchVideoStrategyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMatchVideoStrategyReq>, I>>(_: I): GetMatchVideoStrategyReq {
    const message = createBaseGetMatchVideoStrategyReq();
    return message;
  }
};

function createBaseGetMatchVideoStrategyRsp(): GetMatchVideoStrategyRsp {
  return { strategy: 0 };
}

export const GetMatchVideoStrategyRsp: MessageFns<GetMatchVideoStrategyRsp> = {
  fromJSON(object: any): GetMatchVideoStrategyRsp {
    return { strategy: isSet(object.strategy) ? matchVideoStrategyFromJSON(object.strategy) : 0 };
  },

  create<I extends Exact<DeepPartial<GetMatchVideoStrategyRsp>, I>>(base?: I): GetMatchVideoStrategyRsp {
    return GetMatchVideoStrategyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMatchVideoStrategyRsp>, I>>(object: I): GetMatchVideoStrategyRsp {
    const message = createBaseGetMatchVideoStrategyRsp();
    message.strategy = object.strategy ?? 0;
    return message;
  }
};

function createBaseGetBottomNavigationReq(): GetBottomNavigationReq {
  return {};
}

export const GetBottomNavigationReq: MessageFns<GetBottomNavigationReq> = {
  fromJSON(_: any): GetBottomNavigationReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetBottomNavigationReq>, I>>(base?: I): GetBottomNavigationReq {
    return GetBottomNavigationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBottomNavigationReq>, I>>(_: I): GetBottomNavigationReq {
    const message = createBaseGetBottomNavigationReq();
    return message;
  }
};

function createBaseGetBottomNavigationRsp(): GetBottomNavigationRsp {
  return { navigation_bars: [] };
}

export const GetBottomNavigationRsp: MessageFns<GetBottomNavigationRsp> = {
  fromJSON(object: any): GetBottomNavigationRsp {
    return {
      navigation_bars: globalThis.Array.isArray(object?.navigation_bars)
        ? object.navigation_bars.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetBottomNavigationRsp>, I>>(base?: I): GetBottomNavigationRsp {
    return GetBottomNavigationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBottomNavigationRsp>, I>>(object: I): GetBottomNavigationRsp {
    const message = createBaseGetBottomNavigationRsp();
    message.navigation_bars = object.navigation_bars?.map(e => e) || [];
    return message;
  }
};

function createBaseGetAnchorApproveConfigReq(): GetAnchorApproveConfigReq {
  return {};
}

export const GetAnchorApproveConfigReq: MessageFns<GetAnchorApproveConfigReq> = {
  fromJSON(_: any): GetAnchorApproveConfigReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorApproveConfigReq>, I>>(base?: I): GetAnchorApproveConfigReq {
    return GetAnchorApproveConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorApproveConfigReq>, I>>(_: I): GetAnchorApproveConfigReq {
    const message = createBaseGetAnchorApproveConfigReq();
    return message;
  }
};

function createBaseGetAnchorApproveConfigRsp(): GetAnchorApproveConfigRsp {
  return {
    good_list: [],
    bad_list: [],
    min_duration: 0,
    max_duration: 0,
    video_good_list: [],
    video_bad_list: [],
    video_example_url: '',
    photo_num_limit: 0
  };
}

export const GetAnchorApproveConfigRsp: MessageFns<GetAnchorApproveConfigRsp> = {
  fromJSON(object: any): GetAnchorApproveConfigRsp {
    return {
      good_list: globalThis.Array.isArray(object?.good_list)
        ? object.good_list.map((e: any) => RegisterPicExample.fromJSON(e))
        : [],
      bad_list: globalThis.Array.isArray(object?.bad_list)
        ? object.bad_list.map((e: any) => RegisterPicExample.fromJSON(e))
        : [],
      min_duration: isSet(object.min_duration) ? globalThis.Number(object.min_duration) : 0,
      max_duration: isSet(object.max_duration) ? globalThis.Number(object.max_duration) : 0,
      video_good_list: globalThis.Array.isArray(object?.video_good_list)
        ? object.video_good_list.map((e: any) => RegisterPicExample.fromJSON(e))
        : [],
      video_bad_list: globalThis.Array.isArray(object?.video_bad_list)
        ? object.video_bad_list.map((e: any) => RegisterPicExample.fromJSON(e))
        : [],
      video_example_url: isSet(object.video_example_url) ? globalThis.String(object.video_example_url) : '',
      photo_num_limit: isSet(object.photo_num_limit) ? globalThis.Number(object.photo_num_limit) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorApproveConfigRsp>, I>>(base?: I): GetAnchorApproveConfigRsp {
    return GetAnchorApproveConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorApproveConfigRsp>, I>>(object: I): GetAnchorApproveConfigRsp {
    const message = createBaseGetAnchorApproveConfigRsp();
    message.good_list = object.good_list?.map(e => RegisterPicExample.fromPartial(e)) || [];
    message.bad_list = object.bad_list?.map(e => RegisterPicExample.fromPartial(e)) || [];
    message.min_duration = object.min_duration ?? 0;
    message.max_duration = object.max_duration ?? 0;
    message.video_good_list = object.video_good_list?.map(e => RegisterPicExample.fromPartial(e)) || [];
    message.video_bad_list = object.video_bad_list?.map(e => RegisterPicExample.fromPartial(e)) || [];
    message.video_example_url = object.video_example_url ?? '';
    message.photo_num_limit = object.photo_num_limit ?? 0;
    return message;
  }
};

function createBaseRegisterPicExample(): RegisterPicExample {
  return { picUrl: '', intro: '' };
}

export const RegisterPicExample: MessageFns<RegisterPicExample> = {
  fromJSON(object: any): RegisterPicExample {
    return {
      picUrl: isSet(object.picUrl) ? globalThis.String(object.picUrl) : '',
      intro: isSet(object.intro) ? globalThis.String(object.intro) : ''
    };
  },

  create<I extends Exact<DeepPartial<RegisterPicExample>, I>>(base?: I): RegisterPicExample {
    return RegisterPicExample.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegisterPicExample>, I>>(object: I): RegisterPicExample {
    const message = createBaseRegisterPicExample();
    message.picUrl = object.picUrl ?? '';
    message.intro = object.intro ?? '';
    return message;
  }
};

function createBaseReportPopUpWindowReq(): ReportPopUpWindowReq {
  return { business_id: '', scene: 0 };
}

export const ReportPopUpWindowReq: MessageFns<ReportPopUpWindowReq> = {
  fromJSON(object: any): ReportPopUpWindowReq {
    return {
      business_id: isSet(object.business_id) ? globalThis.String(object.business_id) : '',
      scene: isSet(object.scene) ? reportPopUpWindowSceneTypeFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<ReportPopUpWindowReq>, I>>(base?: I): ReportPopUpWindowReq {
    return ReportPopUpWindowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportPopUpWindowReq>, I>>(object: I): ReportPopUpWindowReq {
    const message = createBaseReportPopUpWindowReq();
    message.business_id = object.business_id ?? '';
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseReportPopUpWindowRsp(): ReportPopUpWindowRsp {
  return {};
}

export const ReportPopUpWindowRsp: MessageFns<ReportPopUpWindowRsp> = {
  fromJSON(_: any): ReportPopUpWindowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ReportPopUpWindowRsp>, I>>(base?: I): ReportPopUpWindowRsp {
    return ReportPopUpWindowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportPopUpWindowRsp>, I>>(_: I): ReportPopUpWindowRsp {
    const message = createBaseReportPopUpWindowRsp();
    return message;
  }
};

export type CommSrvDefinition = typeof CommSrvDefinition;
export const CommSrvDefinition = {
  name: 'CommSrv',
  fullName: 'commsrv.CommSrv',
  methods: {
    echo: {
      name: 'Echo',
      requestType: EchoReq,
      requestStream: false,
      responseType: EchoRsp,
      responseStream: false,
      options: {}
    },
    onlineNotify: {
      name: 'OnlineNotify',
      requestType: OnlineNotifyReq,
      requestStream: false,
      responseType: OnlineNotifyRsp,
      responseStream: false,
      options: {}
    },
    withdrawDone: {
      name: 'WithdrawDone',
      requestType: WithdrawDoneReq,
      requestStream: false,
      responseType: WithdrawDoneResp,
      responseStream: false,
      options: {}
    },
    createWithdrawOrder: {
      name: 'CreateWithdrawOrder',
      requestType: CreateWithdrawOrderReq,
      requestStream: false,
      responseType: CreateWithdrawOrderResp,
      responseStream: false,
      options: {}
    },
    /** app心跳 */
    heartBeat: {
      name: 'HeartBeat',
      requestType: HeartBeatReq,
      requestStream: false,
      responseType: HeartBeatResp,
      responseStream: false,
      options: {}
    },
    cleanUserCoin: {
      name: 'CleanUserCoin',
      requestType: CleanUserCoinReq,
      requestStream: false,
      responseType: CleanUserCoinResp,
      responseStream: false,
      options: {}
    },
    /** 弹窗上报（用于通用弹窗下发后上报） */
    notifyPopUpWindow: {
      name: 'NotifyPopUpWindow',
      requestType: NotifyPopUpWindowReq,
      requestStream: false,
      responseType: NotifyPopUpWindowRsp,
      responseStream: false,
      options: {}
    },
    /** 获取速配请求策略 */
    getMatchVideoStrategy: {
      name: 'GetMatchVideoStrategy',
      requestType: GetMatchVideoStrategyReq,
      requestStream: false,
      responseType: GetMatchVideoStrategyRsp,
      responseStream: false,
      options: {}
    },
    /** 获取底部导航栏 */
    getBottomNavigation: {
      name: 'GetBottomNavigation',
      requestType: GetBottomNavigationReq,
      requestStream: false,
      responseType: GetBottomNavigationRsp,
      responseStream: false,
      options: {}
    },
    /** 获取主播认证配置 */
    getAnchorApproveConfig: {
      name: 'GetAnchorApproveConfig',
      requestType: GetAnchorApproveConfigReq,
      requestStream: false,
      responseType: GetAnchorApproveConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 通用弹窗上报 */
    reportPopUpWindow: {
      name: 'ReportPopUpWindow',
      requestType: ReportPopUpWindowReq,
      requestStream: false,
      responseType: ReportPopUpWindowRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
