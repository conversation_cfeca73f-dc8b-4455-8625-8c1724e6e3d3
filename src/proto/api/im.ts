// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/im.proto

/* eslint-disable */
import { GiftInfo } from '../protobuf/api/revenue/gift';

export const protobufPackage = 'immsg';

export interface RyTokenInfo {
  /** token */
  token: string;
}

export interface GetTokenReq {}

export interface GetTokenRsp {
  /** 融云token信息 */
  token_info: RyTokenInfo | undefined;
}

export interface CustomMessageContent {
  /** 活动通知 */
  activity?: CustomMessageContent_Activity | undefined;
  /** 送礼 */
  gift?: CustomMessageContent_Gift | undefined;
  /** 系统提示-纯文本 */
  system_txt?: CustomMessageContent_SystemTxt | undefined;
  /** 房间分享消息 */
  share_room?: CustomMessageContent_ShareRoom | undefined;
  /** 资源包下发消息 */
  resource_package_distribute?: CustomMessageContent_ResourcePackageDistribute | undefined;
}

/** 活动通知 */
export interface CustomMessageContent_Activity {
  deep_link: string;
  /** 多语言消息 */
  map_msg: { [key: string]: CustomMessageContent_Activity_ActivityMsg };
}

export interface CustomMessageContent_Activity_ActivityMsg {
  title: string;
  content: string;
  image_url: string;
  btn_text: string;
  /** 图片宽度 px */
  image_width: number;
  /** 图片高度 px */
  image_height: number;
}

export interface CustomMessageContent_Activity_MapMsgEntry {
  key: string;
  value: CustomMessageContent_Activity_ActivityMsg | undefined;
}

/** 礼物 */
export interface CustomMessageContent_Gift {
  /** 对应营收的礼物信息 */
  gift_info: GiftInfo | undefined;
  /** 礼物数量 */
  quantity: number;
}

/** 系统通知-纯文本 */
export interface CustomMessageContent_SystemTxt {
  /** 多语言消息 */
  map_msg: { [key: string]: string };
}

export interface CustomMessageContent_SystemTxt_MapMsgEntry {
  key: string;
  value: string;
}

/** 分享房间消息 */
export interface CustomMessageContent_ShareRoom {
  /** 房间id */
  room_id: number;
  /** 房间封面 */
  cover: string;
  /** 房间名称 */
  name: string;
  /** 房主uid */
  owner_uid: number;
  /** 房主昵称 */
  owner_nick: string;
  /** 邀请进房token，针对处理免密进房 */
  invite_token: string;
}

/** 资源包下发 */
export interface CustomMessageContent_ResourcePackageDistribute {
  /** 标题-多语言 */
  title_i18n: { [key: string]: string };
  /** 内容-多语言 */
  content_i18n: { [key: string]: string };
  /** 资源信息 */
  resource_infos: CustomMessageContent_ResourcePackageDistribute_ResourceInfo[];
}

export interface CustomMessageContent_ResourcePackageDistribute_ResourceInfo {
  /** 资源类型 */
  resource_type: string;
  /** 资源图片url */
  icon_url: string;
  /** 有效时间-展示 */
  effective_time_show: string;
}

export interface CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry {
  key: string;
  value: string;
}

export interface CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry {
  key: string;
  value: string;
}

function createBaseRyTokenInfo(): RyTokenInfo {
  return { token: '' };
}

export const RyTokenInfo: MessageFns<RyTokenInfo> = {
  fromJSON(object: any): RyTokenInfo {
    return { token: isSet(object.token) ? globalThis.String(object.token) : '' };
  },

  create<I extends Exact<DeepPartial<RyTokenInfo>, I>>(base?: I): RyTokenInfo {
    return RyTokenInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RyTokenInfo>, I>>(object: I): RyTokenInfo {
    const message = createBaseRyTokenInfo();
    message.token = object.token ?? '';
    return message;
  }
};

function createBaseGetTokenReq(): GetTokenReq {
  return {};
}

export const GetTokenReq: MessageFns<GetTokenReq> = {
  fromJSON(_: any): GetTokenReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetTokenReq>, I>>(base?: I): GetTokenReq {
    return GetTokenReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTokenReq>, I>>(_: I): GetTokenReq {
    const message = createBaseGetTokenReq();
    return message;
  }
};

function createBaseGetTokenRsp(): GetTokenRsp {
  return { token_info: undefined };
}

export const GetTokenRsp: MessageFns<GetTokenRsp> = {
  fromJSON(object: any): GetTokenRsp {
    return { token_info: isSet(object.token_info) ? RyTokenInfo.fromJSON(object.token_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetTokenRsp>, I>>(base?: I): GetTokenRsp {
    return GetTokenRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTokenRsp>, I>>(object: I): GetTokenRsp {
    const message = createBaseGetTokenRsp();
    message.token_info =
      object.token_info !== undefined && object.token_info !== null
        ? RyTokenInfo.fromPartial(object.token_info)
        : undefined;
    return message;
  }
};

function createBaseCustomMessageContent(): CustomMessageContent {
  return {
    activity: undefined,
    gift: undefined,
    system_txt: undefined,
    share_room: undefined,
    resource_package_distribute: undefined
  };
}

export const CustomMessageContent: MessageFns<CustomMessageContent> = {
  fromJSON(object: any): CustomMessageContent {
    return {
      activity: isSet(object.activity) ? CustomMessageContent_Activity.fromJSON(object.activity) : undefined,
      gift: isSet(object.gift) ? CustomMessageContent_Gift.fromJSON(object.gift) : undefined,
      system_txt: isSet(object.system_txt) ? CustomMessageContent_SystemTxt.fromJSON(object.system_txt) : undefined,
      share_room: isSet(object.share_room) ? CustomMessageContent_ShareRoom.fromJSON(object.share_room) : undefined,
      resource_package_distribute: isSet(object.resource_package_distribute)
        ? CustomMessageContent_ResourcePackageDistribute.fromJSON(object.resource_package_distribute)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent>, I>>(base?: I): CustomMessageContent {
    return CustomMessageContent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent>, I>>(object: I): CustomMessageContent {
    const message = createBaseCustomMessageContent();
    message.activity =
      object.activity !== undefined && object.activity !== null
        ? CustomMessageContent_Activity.fromPartial(object.activity)
        : undefined;
    message.gift =
      object.gift !== undefined && object.gift !== null
        ? CustomMessageContent_Gift.fromPartial(object.gift)
        : undefined;
    message.system_txt =
      object.system_txt !== undefined && object.system_txt !== null
        ? CustomMessageContent_SystemTxt.fromPartial(object.system_txt)
        : undefined;
    message.share_room =
      object.share_room !== undefined && object.share_room !== null
        ? CustomMessageContent_ShareRoom.fromPartial(object.share_room)
        : undefined;
    message.resource_package_distribute =
      object.resource_package_distribute !== undefined && object.resource_package_distribute !== null
        ? CustomMessageContent_ResourcePackageDistribute.fromPartial(object.resource_package_distribute)
        : undefined;
    return message;
  }
};

function createBaseCustomMessageContent_Activity(): CustomMessageContent_Activity {
  return { deep_link: '', map_msg: {} };
}

export const CustomMessageContent_Activity: MessageFns<CustomMessageContent_Activity> = {
  fromJSON(object: any): CustomMessageContent_Activity {
    return {
      deep_link: isSet(object.deep_link) ? globalThis.String(object.deep_link) : '',
      map_msg: isObject(object.map_msg)
        ? Object.entries(object.map_msg).reduce<{ [key: string]: CustomMessageContent_Activity_ActivityMsg }>(
            (acc, [key, value]) => {
              acc[key] = CustomMessageContent_Activity_ActivityMsg.fromJSON(value);
              return acc;
            },
            {}
          )
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_Activity>, I>>(base?: I): CustomMessageContent_Activity {
    return CustomMessageContent_Activity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_Activity>, I>>(
    object: I
  ): CustomMessageContent_Activity {
    const message = createBaseCustomMessageContent_Activity();
    message.deep_link = object.deep_link ?? '';
    message.map_msg = Object.entries(object.map_msg ?? {}).reduce<{
      [key: string]: CustomMessageContent_Activity_ActivityMsg;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = CustomMessageContent_Activity_ActivityMsg.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseCustomMessageContent_Activity_ActivityMsg(): CustomMessageContent_Activity_ActivityMsg {
  return { title: '', content: '', image_url: '', btn_text: '', image_width: 0, image_height: 0 };
}

export const CustomMessageContent_Activity_ActivityMsg: MessageFns<CustomMessageContent_Activity_ActivityMsg> = {
  fromJSON(object: any): CustomMessageContent_Activity_ActivityMsg {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      image_url: isSet(object.image_url) ? globalThis.String(object.image_url) : '',
      btn_text: isSet(object.btn_text) ? globalThis.String(object.btn_text) : '',
      image_width: isSet(object.image_width) ? globalThis.Number(object.image_width) : 0,
      image_height: isSet(object.image_height) ? globalThis.Number(object.image_height) : 0
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_Activity_ActivityMsg>, I>>(
    base?: I
  ): CustomMessageContent_Activity_ActivityMsg {
    return CustomMessageContent_Activity_ActivityMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_Activity_ActivityMsg>, I>>(
    object: I
  ): CustomMessageContent_Activity_ActivityMsg {
    const message = createBaseCustomMessageContent_Activity_ActivityMsg();
    message.title = object.title ?? '';
    message.content = object.content ?? '';
    message.image_url = object.image_url ?? '';
    message.btn_text = object.btn_text ?? '';
    message.image_width = object.image_width ?? 0;
    message.image_height = object.image_height ?? 0;
    return message;
  }
};

function createBaseCustomMessageContent_Activity_MapMsgEntry(): CustomMessageContent_Activity_MapMsgEntry {
  return { key: '', value: undefined };
}

export const CustomMessageContent_Activity_MapMsgEntry: MessageFns<CustomMessageContent_Activity_MapMsgEntry> = {
  fromJSON(object: any): CustomMessageContent_Activity_MapMsgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? CustomMessageContent_Activity_ActivityMsg.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_Activity_MapMsgEntry>, I>>(
    base?: I
  ): CustomMessageContent_Activity_MapMsgEntry {
    return CustomMessageContent_Activity_MapMsgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_Activity_MapMsgEntry>, I>>(
    object: I
  ): CustomMessageContent_Activity_MapMsgEntry {
    const message = createBaseCustomMessageContent_Activity_MapMsgEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null
        ? CustomMessageContent_Activity_ActivityMsg.fromPartial(object.value)
        : undefined;
    return message;
  }
};

function createBaseCustomMessageContent_Gift(): CustomMessageContent_Gift {
  return { gift_info: undefined, quantity: 0 };
}

export const CustomMessageContent_Gift: MessageFns<CustomMessageContent_Gift> = {
  fromJSON(object: any): CustomMessageContent_Gift {
    return {
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_Gift>, I>>(base?: I): CustomMessageContent_Gift {
    return CustomMessageContent_Gift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_Gift>, I>>(object: I): CustomMessageContent_Gift {
    const message = createBaseCustomMessageContent_Gift();
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.quantity = object.quantity ?? 0;
    return message;
  }
};

function createBaseCustomMessageContent_SystemTxt(): CustomMessageContent_SystemTxt {
  return { map_msg: {} };
}

export const CustomMessageContent_SystemTxt: MessageFns<CustomMessageContent_SystemTxt> = {
  fromJSON(object: any): CustomMessageContent_SystemTxt {
    return {
      map_msg: isObject(object.map_msg)
        ? Object.entries(object.map_msg).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_SystemTxt>, I>>(base?: I): CustomMessageContent_SystemTxt {
    return CustomMessageContent_SystemTxt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_SystemTxt>, I>>(
    object: I
  ): CustomMessageContent_SystemTxt {
    const message = createBaseCustomMessageContent_SystemTxt();
    message.map_msg = Object.entries(object.map_msg ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseCustomMessageContent_SystemTxt_MapMsgEntry(): CustomMessageContent_SystemTxt_MapMsgEntry {
  return { key: '', value: '' };
}

export const CustomMessageContent_SystemTxt_MapMsgEntry: MessageFns<CustomMessageContent_SystemTxt_MapMsgEntry> = {
  fromJSON(object: any): CustomMessageContent_SystemTxt_MapMsgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_SystemTxt_MapMsgEntry>, I>>(
    base?: I
  ): CustomMessageContent_SystemTxt_MapMsgEntry {
    return CustomMessageContent_SystemTxt_MapMsgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_SystemTxt_MapMsgEntry>, I>>(
    object: I
  ): CustomMessageContent_SystemTxt_MapMsgEntry {
    const message = createBaseCustomMessageContent_SystemTxt_MapMsgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCustomMessageContent_ShareRoom(): CustomMessageContent_ShareRoom {
  return { room_id: 0, cover: '', name: '', owner_uid: 0, owner_nick: '', invite_token: '' };
}

export const CustomMessageContent_ShareRoom: MessageFns<CustomMessageContent_ShareRoom> = {
  fromJSON(object: any): CustomMessageContent_ShareRoom {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      cover: isSet(object.cover) ? globalThis.String(object.cover) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      owner_nick: isSet(object.owner_nick) ? globalThis.String(object.owner_nick) : '',
      invite_token: isSet(object.invite_token) ? globalThis.String(object.invite_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<CustomMessageContent_ShareRoom>, I>>(base?: I): CustomMessageContent_ShareRoom {
    return CustomMessageContent_ShareRoom.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomMessageContent_ShareRoom>, I>>(
    object: I
  ): CustomMessageContent_ShareRoom {
    const message = createBaseCustomMessageContent_ShareRoom();
    message.room_id = object.room_id ?? 0;
    message.cover = object.cover ?? '';
    message.name = object.name ?? '';
    message.owner_uid = object.owner_uid ?? 0;
    message.owner_nick = object.owner_nick ?? '';
    message.invite_token = object.invite_token ?? '';
    return message;
  }
};

function createBaseCustomMessageContent_ResourcePackageDistribute(): CustomMessageContent_ResourcePackageDistribute {
  return { title_i18n: {}, content_i18n: {}, resource_infos: [] };
}

export const CustomMessageContent_ResourcePackageDistribute: MessageFns<CustomMessageContent_ResourcePackageDistribute> =
  {
    fromJSON(object: any): CustomMessageContent_ResourcePackageDistribute {
      return {
        title_i18n: isObject(object.title_i18n)
          ? Object.entries(object.title_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
              acc[key] = String(value);
              return acc;
            }, {})
          : {},
        content_i18n: isObject(object.content_i18n)
          ? Object.entries(object.content_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
              acc[key] = String(value);
              return acc;
            }, {})
          : {},
        resource_infos: globalThis.Array.isArray(object?.resource_infos)
          ? object.resource_infos.map((e: any) =>
              CustomMessageContent_ResourcePackageDistribute_ResourceInfo.fromJSON(e)
            )
          : []
      };
    },

    create<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute>, I>>(
      base?: I
    ): CustomMessageContent_ResourcePackageDistribute {
      return CustomMessageContent_ResourcePackageDistribute.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute>, I>>(
      object: I
    ): CustomMessageContent_ResourcePackageDistribute {
      const message = createBaseCustomMessageContent_ResourcePackageDistribute();
      message.title_i18n = Object.entries(object.title_i18n ?? {}).reduce<{ [key: string]: string }>(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = globalThis.String(value);
          }
          return acc;
        },
        {}
      );
      message.content_i18n = Object.entries(object.content_i18n ?? {}).reduce<{ [key: string]: string }>(
        (acc, [key, value]) => {
          if (value !== undefined) {
            acc[key] = globalThis.String(value);
          }
          return acc;
        },
        {}
      );
      message.resource_infos =
        object.resource_infos?.map(e => CustomMessageContent_ResourcePackageDistribute_ResourceInfo.fromPartial(e)) ||
        [];
      return message;
    }
  };

function createBaseCustomMessageContent_ResourcePackageDistribute_ResourceInfo(): CustomMessageContent_ResourcePackageDistribute_ResourceInfo {
  return { resource_type: '', icon_url: '', effective_time_show: '' };
}

export const CustomMessageContent_ResourcePackageDistribute_ResourceInfo: MessageFns<CustomMessageContent_ResourcePackageDistribute_ResourceInfo> =
  {
    fromJSON(object: any): CustomMessageContent_ResourcePackageDistribute_ResourceInfo {
      return {
        resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
        icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
        effective_time_show: isSet(object.effective_time_show) ? globalThis.String(object.effective_time_show) : ''
      };
    },

    create<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_ResourceInfo>, I>>(
      base?: I
    ): CustomMessageContent_ResourcePackageDistribute_ResourceInfo {
      return CustomMessageContent_ResourcePackageDistribute_ResourceInfo.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_ResourceInfo>, I>>(
      object: I
    ): CustomMessageContent_ResourcePackageDistribute_ResourceInfo {
      const message = createBaseCustomMessageContent_ResourcePackageDistribute_ResourceInfo();
      message.resource_type = object.resource_type ?? '';
      message.icon_url = object.icon_url ?? '';
      message.effective_time_show = object.effective_time_show ?? '';
      return message;
    }
  };

function createBaseCustomMessageContent_ResourcePackageDistribute_TitleI18nEntry(): CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry {
  return { key: '', value: '' };
}

export const CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry: MessageFns<CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry> =
  {
    fromJSON(object: any): CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? globalThis.String(object.value) : ''
      };
    },

    create<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry>, I>>(
      base?: I
    ): CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry {
      return CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry>, I>>(
      object: I
    ): CustomMessageContent_ResourcePackageDistribute_TitleI18nEntry {
      const message = createBaseCustomMessageContent_ResourcePackageDistribute_TitleI18nEntry();
      message.key = object.key ?? '';
      message.value = object.value ?? '';
      return message;
    }
  };

function createBaseCustomMessageContent_ResourcePackageDistribute_ContentI18nEntry(): CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry {
  return { key: '', value: '' };
}

export const CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry: MessageFns<CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry> =
  {
    fromJSON(object: any): CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? globalThis.String(object.value) : ''
      };
    },

    create<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry>, I>>(
      base?: I
    ): CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry {
      return CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry>, I>>(
      object: I
    ): CustomMessageContent_ResourcePackageDistribute_ContentI18nEntry {
      const message = createBaseCustomMessageContent_ResourcePackageDistribute_ContentI18nEntry();
      message.key = object.key ?? '';
      message.value = object.value ?? '';
      return message;
    }
  };

export type IMDefinition = typeof IMDefinition;
export const IMDefinition = {
  name: 'IM',
  fullName: 'immsg.IM',
  methods: {
    /** 获取token */
    getToken: {
      name: 'GetToken',
      requestType: GetTokenReq,
      requestStream: false,
      responseType: GetTokenRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
