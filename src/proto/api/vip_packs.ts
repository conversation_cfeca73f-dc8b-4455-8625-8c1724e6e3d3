// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/vip_packs.proto

/* eslint-disable */

export const protobufPackage = 'vippack';

export enum VipGiftRewardType {
  COMMON_TYPE = 0,
  /** COIN_TYPE - 金币类型 */
  COIN_TYPE = 1,
  /** VIDEO_CARD_TYPE - 视频卡类型 */
  VIDEO_CARD_TYPE = 2,
  /** FREE_CHAT_TYPE - 免费聊天类型 */
  FREE_CHAT_TYPE = 3,
  /** IM_CARD_TYPE - 聊天卡类型 */
  IM_CARD_TYPE = 4,
  /** MEDAL_TYPE - 勋章类型 */
  MEDAL_TYPE = 5,
  /** AVATAR_FRAME_TYPE - 头像框类型 */
  AVATAR_FRAME_TYPE = 6,
  /** CHAT_BUBBLE_TYPE - 聊天气泡类型 */
  CHAT_BUBBLE_TYPE = 7,
  /** PAY_POST_TYPE - 付费动态 */
  PAY_POST_TYPE = 8,
  UNRECOGNIZED = -1
}

export function vipGiftRewardTypeFromJSON(object: any): VipGiftRewardType {
  switch (object) {
    case 0:
    case 'COMMON_TYPE':
      return VipGiftRewardType.COMMON_TYPE;
    case 1:
    case 'COIN_TYPE':
      return VipGiftRewardType.COIN_TYPE;
    case 2:
    case 'VIDEO_CARD_TYPE':
      return VipGiftRewardType.VIDEO_CARD_TYPE;
    case 3:
    case 'FREE_CHAT_TYPE':
      return VipGiftRewardType.FREE_CHAT_TYPE;
    case 4:
    case 'IM_CARD_TYPE':
      return VipGiftRewardType.IM_CARD_TYPE;
    case 5:
    case 'MEDAL_TYPE':
      return VipGiftRewardType.MEDAL_TYPE;
    case 6:
    case 'AVATAR_FRAME_TYPE':
      return VipGiftRewardType.AVATAR_FRAME_TYPE;
    case 7:
    case 'CHAT_BUBBLE_TYPE':
      return VipGiftRewardType.CHAT_BUBBLE_TYPE;
    case 8:
    case 'PAY_POST_TYPE':
      return VipGiftRewardType.PAY_POST_TYPE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return VipGiftRewardType.UNRECOGNIZED;
  }
}

export interface GetPackListReq {}

export interface GetPackListResp {
  /** 缓存id */
  rid: string;
  /** 礼包内容 */
  goods: GoodsItem[];
  /** 上一次成功的支付方式 */
  last_pay_type: string;
  /** 支付挽留弹窗策略 */
  retain_strategy: number;
  /** 默认选中下标 */
  selected_index: number;
}

export interface GoodsItem {
  /** sku名称 */
  sku: string;
  /** 原始价格 */
  ori_price: string;
  /** 实际价格 */
  act_price: string;
  /** 单位 */
  unit: string;
  /** 支付列表 */
  pay_type_list: string;
  /** 是否有礼包 */
  is_package: boolean;
  /** vip礼包额外信息 */
  vip_ext_info: VIPGoodsItemExtInfo | undefined;
  /** 支付资源下发 */
  pay_resource_list: PayResourceItem[];
  /** 金币折扣 */
  discount: number;
}

export interface PayResourceItem {
  name: string;
  pay_type: string;
  url: string;
}

export interface VIPGoodsItemExtInfo {
  /** 礼包礼物列表 */
  gift_data_list: PackGiftData[];
  /** 是否购买 */
  is_purchase: boolean;
  /** 过期时间 */
  expire_time: string;
  /** 特权icon */
  privilege_icon: string;
  /** 特权说明 */
  privilege_decr: string;
  /** vip icon */
  icon: string;
  /** 底部文案 */
  button_content: string;
  /** sku名称 */
  sku_name: string;
  /** vip礼包序号 */
  order: number;
}

export interface PackGiftData {
  /** 礼包礼物路径 */
  gift_path: string;
  /** 礼包礼物名称 */
  gift_name: string;
  /** 礼包礼物有效期 */
  gift_valid_time: string;
  /** 礼包礼物描述 */
  gift_decr: string;
  /** 支付页面礼包文案 */
  payment_option_text: string;
  /** 奖励类型 */
  reward_type: VipGiftRewardType;
  /** 礼包礼物名称新字段展示 */
  gift_name_show: string;
  /** 支付页面礼包文案新字段展示 */
  payment_option_text_show: string;
  /** 金币折扣 */
  discount: number;
  /** 礼包视频url */
  video_urls: string[];
}

export interface GetRewardListReq {}

export interface GetRewardListResp {
  /** vip礼包列表 */
  infos: VIPGoodsItemExtInfo[];
}

export interface Notify {
  /** vip礼包购买成功长链消息 */
  pay_success_msg: SendVipPaySuccessMessage | undefined;
}

export interface SendVipPaySuccessMessage {
  /** vip礼包列表 */
  infos: VIPGoodsItemExtInfo[];
}

export interface GetVipPackGuideReq {}

export interface GetVipPackGuideResp {
  /** 是否弹窗 */
  is_open: number;
  /** 选中序号 */
  order: number;
}

function createBaseGetPackListReq(): GetPackListReq {
  return {};
}

export const GetPackListReq: MessageFns<GetPackListReq> = {
  fromJSON(_: any): GetPackListReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetPackListReq>, I>>(base?: I): GetPackListReq {
    return GetPackListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPackListReq>, I>>(_: I): GetPackListReq {
    const message = createBaseGetPackListReq();
    return message;
  }
};

function createBaseGetPackListResp(): GetPackListResp {
  return { rid: '', goods: [], last_pay_type: '', retain_strategy: 0, selected_index: 0 };
}

export const GetPackListResp: MessageFns<GetPackListResp> = {
  fromJSON(object: any): GetPackListResp {
    return {
      rid: isSet(object.rid) ? globalThis.String(object.rid) : '',
      goods: globalThis.Array.isArray(object?.goods) ? object.goods.map((e: any) => GoodsItem.fromJSON(e)) : [],
      last_pay_type: isSet(object.last_pay_type) ? globalThis.String(object.last_pay_type) : '',
      retain_strategy: isSet(object.retain_strategy) ? globalThis.Number(object.retain_strategy) : 0,
      selected_index: isSet(object.selected_index) ? globalThis.Number(object.selected_index) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetPackListResp>, I>>(base?: I): GetPackListResp {
    return GetPackListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPackListResp>, I>>(object: I): GetPackListResp {
    const message = createBaseGetPackListResp();
    message.rid = object.rid ?? '';
    message.goods = object.goods?.map(e => GoodsItem.fromPartial(e)) || [];
    message.last_pay_type = object.last_pay_type ?? '';
    message.retain_strategy = object.retain_strategy ?? 0;
    message.selected_index = object.selected_index ?? 0;
    return message;
  }
};

function createBaseGoodsItem(): GoodsItem {
  return {
    sku: '',
    ori_price: '',
    act_price: '',
    unit: '',
    pay_type_list: '',
    is_package: false,
    vip_ext_info: undefined,
    pay_resource_list: [],
    discount: 0
  };
}

export const GoodsItem: MessageFns<GoodsItem> = {
  fromJSON(object: any): GoodsItem {
    return {
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      ori_price: isSet(object.ori_price) ? globalThis.String(object.ori_price) : '',
      act_price: isSet(object.act_price) ? globalThis.String(object.act_price) : '',
      unit: isSet(object.unit) ? globalThis.String(object.unit) : '',
      pay_type_list: isSet(object.pay_type_list) ? globalThis.String(object.pay_type_list) : '',
      is_package: isSet(object.is_package) ? globalThis.Boolean(object.is_package) : false,
      vip_ext_info: isSet(object.vip_ext_info) ? VIPGoodsItemExtInfo.fromJSON(object.vip_ext_info) : undefined,
      pay_resource_list: globalThis.Array.isArray(object?.pay_resource_list)
        ? object.pay_resource_list.map((e: any) => PayResourceItem.fromJSON(e))
        : [],
      discount: isSet(object.discount) ? globalThis.Number(object.discount) : 0
    };
  },

  create<I extends Exact<DeepPartial<GoodsItem>, I>>(base?: I): GoodsItem {
    return GoodsItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsItem>, I>>(object: I): GoodsItem {
    const message = createBaseGoodsItem();
    message.sku = object.sku ?? '';
    message.ori_price = object.ori_price ?? '';
    message.act_price = object.act_price ?? '';
    message.unit = object.unit ?? '';
    message.pay_type_list = object.pay_type_list ?? '';
    message.is_package = object.is_package ?? false;
    message.vip_ext_info =
      object.vip_ext_info !== undefined && object.vip_ext_info !== null
        ? VIPGoodsItemExtInfo.fromPartial(object.vip_ext_info)
        : undefined;
    message.pay_resource_list = object.pay_resource_list?.map(e => PayResourceItem.fromPartial(e)) || [];
    message.discount = object.discount ?? 0;
    return message;
  }
};

function createBasePayResourceItem(): PayResourceItem {
  return { name: '', pay_type: '', url: '' };
}

export const PayResourceItem: MessageFns<PayResourceItem> = {
  fromJSON(object: any): PayResourceItem {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      url: isSet(object.url) ? globalThis.String(object.url) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayResourceItem>, I>>(base?: I): PayResourceItem {
    return PayResourceItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayResourceItem>, I>>(object: I): PayResourceItem {
    const message = createBasePayResourceItem();
    message.name = object.name ?? '';
    message.pay_type = object.pay_type ?? '';
    message.url = object.url ?? '';
    return message;
  }
};

function createBaseVIPGoodsItemExtInfo(): VIPGoodsItemExtInfo {
  return {
    gift_data_list: [],
    is_purchase: false,
    expire_time: '',
    privilege_icon: '',
    privilege_decr: '',
    icon: '',
    button_content: '',
    sku_name: '',
    order: 0
  };
}

export const VIPGoodsItemExtInfo: MessageFns<VIPGoodsItemExtInfo> = {
  fromJSON(object: any): VIPGoodsItemExtInfo {
    return {
      gift_data_list: globalThis.Array.isArray(object?.gift_data_list)
        ? object.gift_data_list.map((e: any) => PackGiftData.fromJSON(e))
        : [],
      is_purchase: isSet(object.is_purchase) ? globalThis.Boolean(object.is_purchase) : false,
      expire_time: isSet(object.expire_time) ? globalThis.String(object.expire_time) : '',
      privilege_icon: isSet(object.privilege_icon) ? globalThis.String(object.privilege_icon) : '',
      privilege_decr: isSet(object.privilege_decr) ? globalThis.String(object.privilege_decr) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      button_content: isSet(object.button_content) ? globalThis.String(object.button_content) : '',
      sku_name: isSet(object.sku_name) ? globalThis.String(object.sku_name) : '',
      order: isSet(object.order) ? globalThis.Number(object.order) : 0
    };
  },

  create<I extends Exact<DeepPartial<VIPGoodsItemExtInfo>, I>>(base?: I): VIPGoodsItemExtInfo {
    return VIPGoodsItemExtInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VIPGoodsItemExtInfo>, I>>(object: I): VIPGoodsItemExtInfo {
    const message = createBaseVIPGoodsItemExtInfo();
    message.gift_data_list = object.gift_data_list?.map(e => PackGiftData.fromPartial(e)) || [];
    message.is_purchase = object.is_purchase ?? false;
    message.expire_time = object.expire_time ?? '';
    message.privilege_icon = object.privilege_icon ?? '';
    message.privilege_decr = object.privilege_decr ?? '';
    message.icon = object.icon ?? '';
    message.button_content = object.button_content ?? '';
    message.sku_name = object.sku_name ?? '';
    message.order = object.order ?? 0;
    return message;
  }
};

function createBasePackGiftData(): PackGiftData {
  return {
    gift_path: '',
    gift_name: '',
    gift_valid_time: '',
    gift_decr: '',
    payment_option_text: '',
    reward_type: 0,
    gift_name_show: '',
    payment_option_text_show: '',
    discount: 0,
    video_urls: []
  };
}

export const PackGiftData: MessageFns<PackGiftData> = {
  fromJSON(object: any): PackGiftData {
    return {
      gift_path: isSet(object.gift_path) ? globalThis.String(object.gift_path) : '',
      gift_name: isSet(object.gift_name) ? globalThis.String(object.gift_name) : '',
      gift_valid_time: isSet(object.gift_valid_time) ? globalThis.String(object.gift_valid_time) : '',
      gift_decr: isSet(object.gift_decr) ? globalThis.String(object.gift_decr) : '',
      payment_option_text: isSet(object.payment_option_text) ? globalThis.String(object.payment_option_text) : '',
      reward_type: isSet(object.reward_type) ? vipGiftRewardTypeFromJSON(object.reward_type) : 0,
      gift_name_show: isSet(object.gift_name_show) ? globalThis.String(object.gift_name_show) : '',
      payment_option_text_show: isSet(object.payment_option_text_show)
        ? globalThis.String(object.payment_option_text_show)
        : '',
      discount: isSet(object.discount) ? globalThis.Number(object.discount) : 0,
      video_urls: globalThis.Array.isArray(object?.video_urls)
        ? object.video_urls.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<PackGiftData>, I>>(base?: I): PackGiftData {
    return PackGiftData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PackGiftData>, I>>(object: I): PackGiftData {
    const message = createBasePackGiftData();
    message.gift_path = object.gift_path ?? '';
    message.gift_name = object.gift_name ?? '';
    message.gift_valid_time = object.gift_valid_time ?? '';
    message.gift_decr = object.gift_decr ?? '';
    message.payment_option_text = object.payment_option_text ?? '';
    message.reward_type = object.reward_type ?? 0;
    message.gift_name_show = object.gift_name_show ?? '';
    message.payment_option_text_show = object.payment_option_text_show ?? '';
    message.discount = object.discount ?? 0;
    message.video_urls = object.video_urls?.map(e => e) || [];
    return message;
  }
};

function createBaseGetRewardListReq(): GetRewardListReq {
  return {};
}

export const GetRewardListReq: MessageFns<GetRewardListReq> = {
  fromJSON(_: any): GetRewardListReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetRewardListReq>, I>>(base?: I): GetRewardListReq {
    return GetRewardListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRewardListReq>, I>>(_: I): GetRewardListReq {
    const message = createBaseGetRewardListReq();
    return message;
  }
};

function createBaseGetRewardListResp(): GetRewardListResp {
  return { infos: [] };
}

export const GetRewardListResp: MessageFns<GetRewardListResp> = {
  fromJSON(object: any): GetRewardListResp {
    return {
      infos: globalThis.Array.isArray(object?.infos)
        ? object.infos.map((e: any) => VIPGoodsItemExtInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetRewardListResp>, I>>(base?: I): GetRewardListResp {
    return GetRewardListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRewardListResp>, I>>(object: I): GetRewardListResp {
    const message = createBaseGetRewardListResp();
    message.infos = object.infos?.map(e => VIPGoodsItemExtInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotify(): Notify {
  return { pay_success_msg: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return {
      pay_success_msg: isSet(object.pay_success_msg)
        ? SendVipPaySuccessMessage.fromJSON(object.pay_success_msg)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.pay_success_msg =
      object.pay_success_msg !== undefined && object.pay_success_msg !== null
        ? SendVipPaySuccessMessage.fromPartial(object.pay_success_msg)
        : undefined;
    return message;
  }
};

function createBaseSendVipPaySuccessMessage(): SendVipPaySuccessMessage {
  return { infos: [] };
}

export const SendVipPaySuccessMessage: MessageFns<SendVipPaySuccessMessage> = {
  fromJSON(object: any): SendVipPaySuccessMessage {
    return {
      infos: globalThis.Array.isArray(object?.infos)
        ? object.infos.map((e: any) => VIPGoodsItemExtInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SendVipPaySuccessMessage>, I>>(base?: I): SendVipPaySuccessMessage {
    return SendVipPaySuccessMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendVipPaySuccessMessage>, I>>(object: I): SendVipPaySuccessMessage {
    const message = createBaseSendVipPaySuccessMessage();
    message.infos = object.infos?.map(e => VIPGoodsItemExtInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetVipPackGuideReq(): GetVipPackGuideReq {
  return {};
}

export const GetVipPackGuideReq: MessageFns<GetVipPackGuideReq> = {
  fromJSON(_: any): GetVipPackGuideReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetVipPackGuideReq>, I>>(base?: I): GetVipPackGuideReq {
    return GetVipPackGuideReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVipPackGuideReq>, I>>(_: I): GetVipPackGuideReq {
    const message = createBaseGetVipPackGuideReq();
    return message;
  }
};

function createBaseGetVipPackGuideResp(): GetVipPackGuideResp {
  return { is_open: 0, order: 0 };
}

export const GetVipPackGuideResp: MessageFns<GetVipPackGuideResp> = {
  fromJSON(object: any): GetVipPackGuideResp {
    return {
      is_open: isSet(object.is_open) ? globalThis.Number(object.is_open) : 0,
      order: isSet(object.order) ? globalThis.Number(object.order) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetVipPackGuideResp>, I>>(base?: I): GetVipPackGuideResp {
    return GetVipPackGuideResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVipPackGuideResp>, I>>(object: I): GetVipPackGuideResp {
    const message = createBaseGetVipPackGuideResp();
    message.is_open = object.is_open ?? 0;
    message.order = object.order ?? 0;
    return message;
  }
};

export type VipPackDefinition = typeof VipPackDefinition;
export const VipPackDefinition = {
  name: 'VipPack',
  fullName: 'vippack.VipPack',
  methods: {
    getPackList: {
      name: 'GetPackList',
      requestType: GetPackListReq,
      requestStream: false,
      responseType: GetPackListResp,
      responseStream: false,
      options: {}
    },
    getRewardList: {
      name: 'GetRewardList',
      requestType: GetRewardListReq,
      requestStream: false,
      responseType: GetRewardListResp,
      responseStream: false,
      options: {}
    },
    /** vip礼包引导充值 */
    getVipPackGuide: {
      name: 'GetVipPackGuide',
      requestType: GetVipPackGuideReq,
      requestStream: false,
      responseType: GetVipPackGuideResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
