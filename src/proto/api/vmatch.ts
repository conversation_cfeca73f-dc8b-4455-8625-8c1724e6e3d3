// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/vmatch.proto

/* eslint-disable */

export const protobufPackage = 'lucky';

export interface VmatchPopExposureReq {
  /** 主播uid */
  AnchorUID: string;
  /** 视频类型 */
  video_type: number;
  /** exposure:弹窗曝光，accept:accept点击 */
  action: string;
  /** 弹窗id */
  window_id: string;
}

export interface VmatchPopExposureResp {}

function createBaseVmatchPopExposureReq(): VmatchPopExposureReq {
  return { AnchorUID: '', video_type: 0, action: '', window_id: '' };
}

export const VmatchPopExposureReq: MessageFns<VmatchPopExposureReq> = {
  fromJSON(object: any): VmatchPopExposureReq {
    return {
      AnchorUID: isSet(object.AnchorUID) ? globalThis.String(object.AnchorUID) : '',
      video_type: isSet(object.video_type) ? globalThis.Number(object.video_type) : 0,
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      window_id: isSet(object.window_id) ? globalThis.String(object.window_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<VmatchPopExposureReq>, I>>(base?: I): VmatchPopExposureReq {
    return VmatchPopExposureReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VmatchPopExposureReq>, I>>(object: I): VmatchPopExposureReq {
    const message = createBaseVmatchPopExposureReq();
    message.AnchorUID = object.AnchorUID ?? '';
    message.video_type = object.video_type ?? 0;
    message.action = object.action ?? '';
    message.window_id = object.window_id ?? '';
    return message;
  }
};

function createBaseVmatchPopExposureResp(): VmatchPopExposureResp {
  return {};
}

export const VmatchPopExposureResp: MessageFns<VmatchPopExposureResp> = {
  fromJSON(_: any): VmatchPopExposureResp {
    return {};
  },

  create<I extends Exact<DeepPartial<VmatchPopExposureResp>, I>>(base?: I): VmatchPopExposureResp {
    return VmatchPopExposureResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VmatchPopExposureResp>, I>>(_: I): VmatchPopExposureResp {
    const message = createBaseVmatchPopExposureResp();
    return message;
  }
};

export type VmatchDefinition = typeof VmatchDefinition;
export const VmatchDefinition = {
  name: 'Vmatch',
  fullName: 'lucky.Vmatch',
  methods: {
    vmatchPopExposure: {
      name: 'VmatchPopExposure',
      requestType: VmatchPopExposureReq,
      requestStream: false,
      responseType: VmatchPopExposureResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
