// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/common.proto

/* eslint-disable */

export const protobufPackage = 'common';

export enum Code {
  CODE_NONE = 0,
  /** CODE_COMMON - 业务通用错误码 */
  CODE_COMMON = 1000,
  /** CODE_USER_NOT_EXIST - 用户不存在错误码 */
  CODE_USER_NOT_EXIST = 1001,
  UNRECOGNIZED = -1
}

export function codeFromJSON(object: any): Code {
  switch (object) {
    case 0:
    case 'CODE_NONE':
      return Code.CODE_NONE;
    case 1000:
    case 'CODE_COMMON':
      return Code.CODE_COMMON;
    case 1001:
    case 'CODE_USER_NOT_EXIST':
      return Code.CODE_USER_NOT_EXIST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Code.UNRECOGNIZED;
  }
}

export interface BizPubParams {
  click_id: string;
}

function createBaseBizPubParams(): BizPubParams {
  return { click_id: '' };
}

export const BizPubParams: MessageFns<BizPubParams> = {
  fromJSON(object: any): BizPubParams {
    return { click_id: isSet(object.click_id) ? globalThis.String(object.click_id) : '' };
  },

  create<I extends Exact<DeepPartial<BizPubParams>, I>>(base?: I): BizPubParams {
    return BizPubParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizPubParams>, I>>(object: I): BizPubParams {
    const message = createBaseBizPubParams();
    message.click_id = object.click_id ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
