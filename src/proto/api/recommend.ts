// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/recommend.proto

/* eslint-disable */
import { AvatarFrameItem } from './bag';

export const protobufPackage = 'recommend';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/recommendsrv */

export interface ListRecoMatchUsersReq {
  action: string;
  page: number;
  size: number;
}

export interface ListRecoMatchUsersResp {
  list: ListRecoMatchUsersResp_RecoMatchUserItem[];
  has_more: number;
}

export interface ListRecoMatchUsersResp_RecoMatchUserItem {
  uid: string;
  nickname: string;
  sex: string;
  avatar: string;
  age: number;
  self_introduction_video: string;
  is_online: boolean;
  verify_status: string;
  has_call_card: number;
  utype: string;
  show_type: number;
  vmatch_params: string;
  show_video_url: string;
  is_vmatch: boolean;
  video_like_tag: number;
  medal_url: string;
  price: number;
  /** online,offline,busy */
  client_status: string;
  /** 国旗地址 */
  country_url: string;
  /** 兴趣爱好 */
  interests: string;
  /** 国家编码 */
  country: string;
  /** 余额区间数 */
  balance: number;
  /** 亲密度 */
  hot: number;
  /** 注册间隔 秒级别 当前时间减去注册时间 */
  register_interval: number;
  /** 等级 */
  level: number;
  /** 头像框对象 */
  avatar_frame_item: AvatarFrameItem | undefined;
}

function createBaseListRecoMatchUsersReq(): ListRecoMatchUsersReq {
  return { action: '', page: 0, size: 0 };
}

export const ListRecoMatchUsersReq: MessageFns<ListRecoMatchUsersReq> = {
  fromJSON(object: any): ListRecoMatchUsersReq {
    return {
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRecoMatchUsersReq>, I>>(base?: I): ListRecoMatchUsersReq {
    return ListRecoMatchUsersReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRecoMatchUsersReq>, I>>(object: I): ListRecoMatchUsersReq {
    const message = createBaseListRecoMatchUsersReq();
    message.action = object.action ?? '';
    message.page = object.page ?? 0;
    message.size = object.size ?? 0;
    return message;
  }
};

function createBaseListRecoMatchUsersResp(): ListRecoMatchUsersResp {
  return { list: [], has_more: 0 };
}

export const ListRecoMatchUsersResp: MessageFns<ListRecoMatchUsersResp> = {
  fromJSON(object: any): ListRecoMatchUsersResp {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => ListRecoMatchUsersResp_RecoMatchUserItem.fromJSON(e))
        : [],
      has_more: isSet(object.has_more) ? globalThis.Number(object.has_more) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRecoMatchUsersResp>, I>>(base?: I): ListRecoMatchUsersResp {
    return ListRecoMatchUsersResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRecoMatchUsersResp>, I>>(object: I): ListRecoMatchUsersResp {
    const message = createBaseListRecoMatchUsersResp();
    message.list = object.list?.map(e => ListRecoMatchUsersResp_RecoMatchUserItem.fromPartial(e)) || [];
    message.has_more = object.has_more ?? 0;
    return message;
  }
};

function createBaseListRecoMatchUsersResp_RecoMatchUserItem(): ListRecoMatchUsersResp_RecoMatchUserItem {
  return {
    uid: '',
    nickname: '',
    sex: '',
    avatar: '',
    age: 0,
    self_introduction_video: '',
    is_online: false,
    verify_status: '',
    has_call_card: 0,
    utype: '',
    show_type: 0,
    vmatch_params: '',
    show_video_url: '',
    is_vmatch: false,
    video_like_tag: 0,
    medal_url: '',
    price: 0,
    client_status: '',
    country_url: '',
    interests: '',
    country: '',
    balance: 0,
    hot: 0,
    register_interval: 0,
    level: 0,
    avatar_frame_item: undefined
  };
}

export const ListRecoMatchUsersResp_RecoMatchUserItem: MessageFns<ListRecoMatchUsersResp_RecoMatchUserItem> = {
  fromJSON(object: any): ListRecoMatchUsersResp_RecoMatchUserItem {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
      self_introduction_video: isSet(object.self_introduction_video)
        ? globalThis.String(object.self_introduction_video)
        : '',
      is_online: isSet(object.is_online) ? globalThis.Boolean(object.is_online) : false,
      verify_status: isSet(object.verify_status) ? globalThis.String(object.verify_status) : '',
      has_call_card: isSet(object.has_call_card) ? globalThis.Number(object.has_call_card) : 0,
      utype: isSet(object.utype) ? globalThis.String(object.utype) : '',
      show_type: isSet(object.show_type) ? globalThis.Number(object.show_type) : 0,
      vmatch_params: isSet(object.vmatch_params) ? globalThis.String(object.vmatch_params) : '',
      show_video_url: isSet(object.show_video_url) ? globalThis.String(object.show_video_url) : '',
      is_vmatch: isSet(object.is_vmatch) ? globalThis.Boolean(object.is_vmatch) : false,
      video_like_tag: isSet(object.video_like_tag) ? globalThis.Number(object.video_like_tag) : 0,
      medal_url: isSet(object.medal_url) ? globalThis.String(object.medal_url) : '',
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      client_status: isSet(object.client_status) ? globalThis.String(object.client_status) : '',
      country_url: isSet(object.country_url) ? globalThis.String(object.country_url) : '',
      interests: isSet(object.interests) ? globalThis.String(object.interests) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      balance: isSet(object.balance) ? globalThis.Number(object.balance) : 0,
      hot: isSet(object.hot) ? globalThis.Number(object.hot) : 0,
      register_interval: isSet(object.register_interval) ? globalThis.Number(object.register_interval) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      avatar_frame_item: isSet(object.avatar_frame_item)
        ? AvatarFrameItem.fromJSON(object.avatar_frame_item)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListRecoMatchUsersResp_RecoMatchUserItem>, I>>(
    base?: I
  ): ListRecoMatchUsersResp_RecoMatchUserItem {
    return ListRecoMatchUsersResp_RecoMatchUserItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRecoMatchUsersResp_RecoMatchUserItem>, I>>(
    object: I
  ): ListRecoMatchUsersResp_RecoMatchUserItem {
    const message = createBaseListRecoMatchUsersResp_RecoMatchUserItem();
    message.uid = object.uid ?? '';
    message.nickname = object.nickname ?? '';
    message.sex = object.sex ?? '';
    message.avatar = object.avatar ?? '';
    message.age = object.age ?? 0;
    message.self_introduction_video = object.self_introduction_video ?? '';
    message.is_online = object.is_online ?? false;
    message.verify_status = object.verify_status ?? '';
    message.has_call_card = object.has_call_card ?? 0;
    message.utype = object.utype ?? '';
    message.show_type = object.show_type ?? 0;
    message.vmatch_params = object.vmatch_params ?? '';
    message.show_video_url = object.show_video_url ?? '';
    message.is_vmatch = object.is_vmatch ?? false;
    message.video_like_tag = object.video_like_tag ?? 0;
    message.medal_url = object.medal_url ?? '';
    message.price = object.price ?? 0;
    message.client_status = object.client_status ?? '';
    message.country_url = object.country_url ?? '';
    message.interests = object.interests ?? '';
    message.country = object.country ?? '';
    message.balance = object.balance ?? 0;
    message.hot = object.hot ?? 0;
    message.register_interval = object.register_interval ?? 0;
    message.level = object.level ?? 0;
    message.avatar_frame_item =
      object.avatar_frame_item !== undefined && object.avatar_frame_item !== null
        ? AvatarFrameItem.fromPartial(object.avatar_frame_item)
        : undefined;
    return message;
  }
};

export type RecommendDefinition = typeof RecommendDefinition;
export const RecommendDefinition = {
  name: 'Recommend',
  fullName: 'recommend.Recommend',
  methods: {
    listRecoMatchUsers: {
      name: 'ListRecoMatchUsers',
      requestType: ListRecoMatchUsersReq,
      requestStream: false,
      responseType: ListRecoMatchUsersResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
