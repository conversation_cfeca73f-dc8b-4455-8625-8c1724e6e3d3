// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/user.proto

/* eslint-disable */
import { AvatarFrameItem, ChatBubbleItem } from './bag';

export const protobufPackage = 'user';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/usersrv */

export enum UpdatePhotoScene {
  UpdatePhotoNone = 0,
  UpdatePhotoRegister = 1,
  UpdatePhotoPerson = 2,
  UNRECOGNIZED = -1
}

export function updatePhotoSceneFromJSON(object: any): UpdatePhotoScene {
  switch (object) {
    case 0:
    case 'UpdatePhotoNone':
      return UpdatePhotoScene.UpdatePhotoNone;
    case 1:
    case 'UpdatePhotoRegister':
      return UpdatePhotoScene.UpdatePhotoRegister;
    case 2:
    case 'UpdatePhotoPerson':
      return UpdatePhotoScene.UpdatePhotoPerson;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UpdatePhotoScene.UNRECOGNIZED;
  }
}

/** 用户类型 */
export enum UserType {
  /** USER_TYPE_NONE - 默认值，无意义 */
  USER_TYPE_NONE = 0,
  /** USER_TYPE_NORMAL - 普通用户 */
  USER_TYPE_NORMAL = 1,
  /** USER_TYPE_IMMORTAL - 真人认证 */
  USER_TYPE_IMMORTAL = 2,
  /** USER_TYPE_ANCHOR - 主播 */
  USER_TYPE_ANCHOR = 3,
  UNRECOGNIZED = -1
}

export function userTypeFromJSON(object: any): UserType {
  switch (object) {
    case 0:
    case 'USER_TYPE_NONE':
      return UserType.USER_TYPE_NONE;
    case 1:
    case 'USER_TYPE_NORMAL':
      return UserType.USER_TYPE_NORMAL;
    case 2:
    case 'USER_TYPE_IMMORTAL':
      return UserType.USER_TYPE_IMMORTAL;
    case 3:
    case 'USER_TYPE_ANCHOR':
      return UserType.USER_TYPE_ANCHOR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserType.UNRECOGNIZED;
  }
}

export interface GetUserResourceReq {
  uids: string[];
}

export interface GetUserResourceResp {
  items: GetUserResourceData[];
}

export interface GetUserResourceData {
  uid: string;
  /** 是否有聊天气泡 */
  is_chat_bubble: boolean;
  chat_bubble_item: ChatBubbleItem | undefined;
}

export interface MaleBasicInfoReq {}

export interface MaleBasicInfoResp {
  /** 用户名称 */
  name: string;
  /** 用户uid */
  uid: string;
  /** 年龄 */
  age: string;
  /** 认证状态 */
  verify_status: string;
  /** 用户等级 */
  level: number;
  /** 展示的vip名称 */
  vip_name: string;
  /** 用户头像地址 */
  avatar: string;
  /** 头像框对象 */
  avatar_frame_item: AvatarFrameItem | undefined;
  /** 徽章地址 */
  medal: string;
  /** 金币 */
  coin: string;
  /** 道具轮播图 */
  props_items: string[];
  /** 性别 */
  sex: string;
  /** 默认vip-sku */
  default_vip_select_name: string;
  /** 是否充值用户，1:未充值，2:已充值 */
  is_recharge: number;
  /** 金币展示字段 */
  coin_show: string;
  /** 积分 */
  point: string;
  /** 是否是币商 */
  is_coin_agent: boolean;
  /** 注册时间 */
  ctime: number;
}

export interface BlockReq {
  fuid: string;
}

export interface BlockResp {}

export interface UpdateProfileReq {
  sex: string;
  nickname: string;
  birthday: string;
  headimgurl: string;
  skip_check: string;
  lan: string;
  invite_code: string;
  phone: string;
  third_type: string;
  openid: string;
  interests: string;
  video_charge: number;
  message_charge: number;
  had_video: number;
  intro: string;
  headimgbody: string;
}

export interface UpdateProfileResp {}

export interface UpdatePhotoReq {
  action: string;
  item_ids: string;
  size: number;
  skip_check: string;
  scene: UpdatePhotoScene;
}

export interface UpdatePhotoResp {
  data: UpdatePhotoResp_PhotoData[];
}

export interface UpdatePhotoResp_PhotoData {
  item_id: string;
  put_url: string;
  show_url: string;
}

export interface InfoProfileReq {
  fuid: string;
  view: string;
  scene: string;
  /** 二级场景 */
  second_source: string;
}

export interface InfoProfileResp {
  uid: string;
  sex: string;
  nickname: string;
  birthday: string;
  country: string;
  status: number;
  lan: string;
  invite_code: string;
  phone: string;
  utype: string;
  fb_openid: string;
  gp_openid: string;
  is_delete: number;
  video_utime: number;
  had_video: number;
  had_fake_video: number;
  avatar_utime: number;
  verify_status: string;
  verify_anchor: number;
  level: number;
  exp: number;
  point_total: number;
  code_status: number;
  interests: string;
  last_login_time: number;
  intro: string;
  is_fake: number;
  is_official: boolean;
  photos: InfoProfileResp_PhotoData[];
  coin: string;
  coin_show: string;
  point: string;
  show_video: string;
  show_video_pic: string;
  video_reco: number;
  avatar: string;
  avatar_big: string;
  avatar_icon: string;
  age: string;
  client_status: string;
  is_active: number;
  front_online: number;
  has_call_card: number;
  call_card_num: number;
  video_like_tag: number;
  need_upload_video: number;
  need_upload_audio: number;
  need_verify_account: number;
  medal_url: string;
  price: number;
  chat_bubble_item: InfoProfileResp_ChatBubbleItem | undefined;
  avatar_frame_item: InfoProfileResp_AvatarFrameItem | undefined;
  ctime: number;
  had_avatar: number;
  /** 需要验证相册 */
  need_verify_photo: number;
  /** 相册审核状态 1待审核 2已通过 3已拒绝 */
  photo_verify_status: number;
  /** 是否非活跃冷账号 */
  inactive_account: boolean;
  /** 关注总数 ios才返回 */
  follow_count: number;
  /** 粉丝总数 ios才返回 */
  fans_count: number;
  /** 关注状态 1 已关注 */
  follow_state: number;
  /** 国旗 */
  country_url: string;
  /** komi包每日收入 */
  today_income: number;
  /** komi包主播等级 */
  grade: string;
  /** 头像是否审核中 */
  avatar_review: number;
  /** 介绍视频是否审核中 */
  video_review: number;
  /** 照片是否审核中 */
  photo_review: number;
}

export interface InfoProfileResp_PhotoData {
  id: number;
  uid: string;
  photo_id: string;
  like_num: number;
  ctime: number;
  utime: number;
  is_delete: number;
}

export interface InfoProfileResp_ChatBubbleItem {
  preview_url: string;
  background_bottom_frame: string;
  upper_left: string;
  upper_right: string;
  lower_left: string;
  lower_right: string;
  text_color: string;
}

export interface InfoProfileResp_AvatarFrameItem {
  preview_url: string;
  resource_url: string;
  type: string;
}

/** 用户信息 */
export interface UserInfo {
  /** 用户名称 */
  name: string;
  /** 用户uid */
  uid: string;
  /** 年龄 */
  age: string;
  /** 认证状态 */
  verify_status: string;
  /** 用户等级 */
  level: number;
  /** 展示的vip名称 */
  vip_name: string;
  /** 用户头像地址 */
  avatar: string;
  /** 头像框对象 */
  avatar_frame_item: AvatarFrameItem | undefined;
  /** 徽章地址 */
  medal: string;
  /** 性别 */
  sex: string;
  /** 用户身份类型 */
  Utype: UserType;
  /** vip图标 */
  vip_icon: string;
  /** 语言 */
  language: string;
  /** 国家 */
  country: string;
  /** 国家所对应的区域 */
  region: string;
  /** 封禁状态 */
  status: number;
  /** 替换头像 */
  replace_avatar: string;
}

export interface GetPrivilegeReq {}

export interface GetPrivilegeResp {
  /** 是否有资格观看付费动态 */
  is_pay_post: boolean;
}

function createBaseGetUserResourceReq(): GetUserResourceReq {
  return { uids: [] };
}

export const GetUserResourceReq: MessageFns<GetUserResourceReq> = {
  fromJSON(object: any): GetUserResourceReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.String(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetUserResourceReq>, I>>(base?: I): GetUserResourceReq {
    return GetUserResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserResourceReq>, I>>(object: I): GetUserResourceReq {
    const message = createBaseGetUserResourceReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetUserResourceResp(): GetUserResourceResp {
  return { items: [] };
}

export const GetUserResourceResp: MessageFns<GetUserResourceResp> = {
  fromJSON(object: any): GetUserResourceResp {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => GetUserResourceData.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserResourceResp>, I>>(base?: I): GetUserResourceResp {
    return GetUserResourceResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserResourceResp>, I>>(object: I): GetUserResourceResp {
    const message = createBaseGetUserResourceResp();
    message.items = object.items?.map(e => GetUserResourceData.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetUserResourceData(): GetUserResourceData {
  return { uid: '', is_chat_bubble: false, chat_bubble_item: undefined };
}

export const GetUserResourceData: MessageFns<GetUserResourceData> = {
  fromJSON(object: any): GetUserResourceData {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      is_chat_bubble: isSet(object.is_chat_bubble) ? globalThis.Boolean(object.is_chat_bubble) : false,
      chat_bubble_item: isSet(object.chat_bubble_item) ? ChatBubbleItem.fromJSON(object.chat_bubble_item) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetUserResourceData>, I>>(base?: I): GetUserResourceData {
    return GetUserResourceData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserResourceData>, I>>(object: I): GetUserResourceData {
    const message = createBaseGetUserResourceData();
    message.uid = object.uid ?? '';
    message.is_chat_bubble = object.is_chat_bubble ?? false;
    message.chat_bubble_item =
      object.chat_bubble_item !== undefined && object.chat_bubble_item !== null
        ? ChatBubbleItem.fromPartial(object.chat_bubble_item)
        : undefined;
    return message;
  }
};

function createBaseMaleBasicInfoReq(): MaleBasicInfoReq {
  return {};
}

export const MaleBasicInfoReq: MessageFns<MaleBasicInfoReq> = {
  fromJSON(_: any): MaleBasicInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<MaleBasicInfoReq>, I>>(base?: I): MaleBasicInfoReq {
    return MaleBasicInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MaleBasicInfoReq>, I>>(_: I): MaleBasicInfoReq {
    const message = createBaseMaleBasicInfoReq();
    return message;
  }
};

function createBaseMaleBasicInfoResp(): MaleBasicInfoResp {
  return {
    name: '',
    uid: '',
    age: '',
    verify_status: '',
    level: 0,
    vip_name: '',
    avatar: '',
    avatar_frame_item: undefined,
    medal: '',
    coin: '',
    props_items: [],
    sex: '',
    default_vip_select_name: '',
    is_recharge: 0,
    coin_show: '',
    point: '',
    is_coin_agent: false,
    ctime: 0
  };
}

export const MaleBasicInfoResp: MessageFns<MaleBasicInfoResp> = {
  fromJSON(object: any): MaleBasicInfoResp {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      age: isSet(object.age) ? globalThis.String(object.age) : '',
      verify_status: isSet(object.verify_status) ? globalThis.String(object.verify_status) : '',
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      vip_name: isSet(object.vip_name) ? globalThis.String(object.vip_name) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      avatar_frame_item: isSet(object.avatar_frame_item)
        ? AvatarFrameItem.fromJSON(object.avatar_frame_item)
        : undefined,
      medal: isSet(object.medal) ? globalThis.String(object.medal) : '',
      coin: isSet(object.coin) ? globalThis.String(object.coin) : '',
      props_items: globalThis.Array.isArray(object?.props_items)
        ? object.props_items.map((e: any) => globalThis.String(e))
        : [],
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      default_vip_select_name: isSet(object.default_vip_select_name)
        ? globalThis.String(object.default_vip_select_name)
        : '',
      is_recharge: isSet(object.is_recharge) ? globalThis.Number(object.is_recharge) : 0,
      coin_show: isSet(object.coin_show) ? globalThis.String(object.coin_show) : '',
      point: isSet(object.point) ? globalThis.String(object.point) : '',
      is_coin_agent: isSet(object.is_coin_agent) ? globalThis.Boolean(object.is_coin_agent) : false,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0
    };
  },

  create<I extends Exact<DeepPartial<MaleBasicInfoResp>, I>>(base?: I): MaleBasicInfoResp {
    return MaleBasicInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MaleBasicInfoResp>, I>>(object: I): MaleBasicInfoResp {
    const message = createBaseMaleBasicInfoResp();
    message.name = object.name ?? '';
    message.uid = object.uid ?? '';
    message.age = object.age ?? '';
    message.verify_status = object.verify_status ?? '';
    message.level = object.level ?? 0;
    message.vip_name = object.vip_name ?? '';
    message.avatar = object.avatar ?? '';
    message.avatar_frame_item =
      object.avatar_frame_item !== undefined && object.avatar_frame_item !== null
        ? AvatarFrameItem.fromPartial(object.avatar_frame_item)
        : undefined;
    message.medal = object.medal ?? '';
    message.coin = object.coin ?? '';
    message.props_items = object.props_items?.map(e => e) || [];
    message.sex = object.sex ?? '';
    message.default_vip_select_name = object.default_vip_select_name ?? '';
    message.is_recharge = object.is_recharge ?? 0;
    message.coin_show = object.coin_show ?? '';
    message.point = object.point ?? '';
    message.is_coin_agent = object.is_coin_agent ?? false;
    message.ctime = object.ctime ?? 0;
    return message;
  }
};

function createBaseBlockReq(): BlockReq {
  return { fuid: '' };
}

export const BlockReq: MessageFns<BlockReq> = {
  fromJSON(object: any): BlockReq {
    return { fuid: isSet(object.fuid) ? globalThis.String(object.fuid) : '' };
  },

  create<I extends Exact<DeepPartial<BlockReq>, I>>(base?: I): BlockReq {
    return BlockReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlockReq>, I>>(object: I): BlockReq {
    const message = createBaseBlockReq();
    message.fuid = object.fuid ?? '';
    return message;
  }
};

function createBaseBlockResp(): BlockResp {
  return {};
}

export const BlockResp: MessageFns<BlockResp> = {
  fromJSON(_: any): BlockResp {
    return {};
  },

  create<I extends Exact<DeepPartial<BlockResp>, I>>(base?: I): BlockResp {
    return BlockResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BlockResp>, I>>(_: I): BlockResp {
    const message = createBaseBlockResp();
    return message;
  }
};

function createBaseUpdateProfileReq(): UpdateProfileReq {
  return {
    sex: '',
    nickname: '',
    birthday: '',
    headimgurl: '',
    skip_check: '',
    lan: '',
    invite_code: '',
    phone: '',
    third_type: '',
    openid: '',
    interests: '',
    video_charge: 0,
    message_charge: 0,
    had_video: 0,
    intro: '',
    headimgbody: ''
  };
}

export const UpdateProfileReq: MessageFns<UpdateProfileReq> = {
  fromJSON(object: any): UpdateProfileReq {
    return {
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      birthday: isSet(object.birthday) ? globalThis.String(object.birthday) : '',
      headimgurl: isSet(object.headimgurl) ? globalThis.String(object.headimgurl) : '',
      skip_check: isSet(object.skip_check) ? globalThis.String(object.skip_check) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      invite_code: isSet(object.invite_code) ? globalThis.String(object.invite_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      third_type: isSet(object.third_type) ? globalThis.String(object.third_type) : '',
      openid: isSet(object.openid) ? globalThis.String(object.openid) : '',
      interests: isSet(object.interests) ? globalThis.String(object.interests) : '',
      video_charge: isSet(object.video_charge) ? globalThis.Number(object.video_charge) : 0,
      message_charge: isSet(object.message_charge) ? globalThis.Number(object.message_charge) : 0,
      had_video: isSet(object.had_video) ? globalThis.Number(object.had_video) : 0,
      intro: isSet(object.intro) ? globalThis.String(object.intro) : '',
      headimgbody: isSet(object.headimgbody) ? globalThis.String(object.headimgbody) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateProfileReq>, I>>(base?: I): UpdateProfileReq {
    return UpdateProfileReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateProfileReq>, I>>(object: I): UpdateProfileReq {
    const message = createBaseUpdateProfileReq();
    message.sex = object.sex ?? '';
    message.nickname = object.nickname ?? '';
    message.birthday = object.birthday ?? '';
    message.headimgurl = object.headimgurl ?? '';
    message.skip_check = object.skip_check ?? '';
    message.lan = object.lan ?? '';
    message.invite_code = object.invite_code ?? '';
    message.phone = object.phone ?? '';
    message.third_type = object.third_type ?? '';
    message.openid = object.openid ?? '';
    message.interests = object.interests ?? '';
    message.video_charge = object.video_charge ?? 0;
    message.message_charge = object.message_charge ?? 0;
    message.had_video = object.had_video ?? 0;
    message.intro = object.intro ?? '';
    message.headimgbody = object.headimgbody ?? '';
    return message;
  }
};

function createBaseUpdateProfileResp(): UpdateProfileResp {
  return {};
}

export const UpdateProfileResp: MessageFns<UpdateProfileResp> = {
  fromJSON(_: any): UpdateProfileResp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateProfileResp>, I>>(base?: I): UpdateProfileResp {
    return UpdateProfileResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateProfileResp>, I>>(_: I): UpdateProfileResp {
    const message = createBaseUpdateProfileResp();
    return message;
  }
};

function createBaseUpdatePhotoReq(): UpdatePhotoReq {
  return { action: '', item_ids: '', size: 0, skip_check: '', scene: 0 };
}

export const UpdatePhotoReq: MessageFns<UpdatePhotoReq> = {
  fromJSON(object: any): UpdatePhotoReq {
    return {
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      item_ids: isSet(object.item_ids) ? globalThis.String(object.item_ids) : '',
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      skip_check: isSet(object.skip_check) ? globalThis.String(object.skip_check) : '',
      scene: isSet(object.scene) ? updatePhotoSceneFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdatePhotoReq>, I>>(base?: I): UpdatePhotoReq {
    return UpdatePhotoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePhotoReq>, I>>(object: I): UpdatePhotoReq {
    const message = createBaseUpdatePhotoReq();
    message.action = object.action ?? '';
    message.item_ids = object.item_ids ?? '';
    message.size = object.size ?? 0;
    message.skip_check = object.skip_check ?? '';
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseUpdatePhotoResp(): UpdatePhotoResp {
  return { data: [] };
}

export const UpdatePhotoResp: MessageFns<UpdatePhotoResp> = {
  fromJSON(object: any): UpdatePhotoResp {
    return {
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => UpdatePhotoResp_PhotoData.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<UpdatePhotoResp>, I>>(base?: I): UpdatePhotoResp {
    return UpdatePhotoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePhotoResp>, I>>(object: I): UpdatePhotoResp {
    const message = createBaseUpdatePhotoResp();
    message.data = object.data?.map(e => UpdatePhotoResp_PhotoData.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUpdatePhotoResp_PhotoData(): UpdatePhotoResp_PhotoData {
  return { item_id: '', put_url: '', show_url: '' };
}

export const UpdatePhotoResp_PhotoData: MessageFns<UpdatePhotoResp_PhotoData> = {
  fromJSON(object: any): UpdatePhotoResp_PhotoData {
    return {
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      put_url: isSet(object.put_url) ? globalThis.String(object.put_url) : '',
      show_url: isSet(object.show_url) ? globalThis.String(object.show_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdatePhotoResp_PhotoData>, I>>(base?: I): UpdatePhotoResp_PhotoData {
    return UpdatePhotoResp_PhotoData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePhotoResp_PhotoData>, I>>(object: I): UpdatePhotoResp_PhotoData {
    const message = createBaseUpdatePhotoResp_PhotoData();
    message.item_id = object.item_id ?? '';
    message.put_url = object.put_url ?? '';
    message.show_url = object.show_url ?? '';
    return message;
  }
};

function createBaseInfoProfileReq(): InfoProfileReq {
  return { fuid: '', view: '', scene: '', second_source: '' };
}

export const InfoProfileReq: MessageFns<InfoProfileReq> = {
  fromJSON(object: any): InfoProfileReq {
    return {
      fuid: isSet(object.fuid) ? globalThis.String(object.fuid) : '',
      view: isSet(object.view) ? globalThis.String(object.view) : '',
      scene: isSet(object.scene) ? globalThis.String(object.scene) : '',
      second_source: isSet(object.second_source) ? globalThis.String(object.second_source) : ''
    };
  },

  create<I extends Exact<DeepPartial<InfoProfileReq>, I>>(base?: I): InfoProfileReq {
    return InfoProfileReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InfoProfileReq>, I>>(object: I): InfoProfileReq {
    const message = createBaseInfoProfileReq();
    message.fuid = object.fuid ?? '';
    message.view = object.view ?? '';
    message.scene = object.scene ?? '';
    message.second_source = object.second_source ?? '';
    return message;
  }
};

function createBaseInfoProfileResp(): InfoProfileResp {
  return {
    uid: '',
    sex: '',
    nickname: '',
    birthday: '',
    country: '',
    status: 0,
    lan: '',
    invite_code: '',
    phone: '',
    utype: '',
    fb_openid: '',
    gp_openid: '',
    is_delete: 0,
    video_utime: 0,
    had_video: 0,
    had_fake_video: 0,
    avatar_utime: 0,
    verify_status: '',
    verify_anchor: 0,
    level: 0,
    exp: 0,
    point_total: 0,
    code_status: 0,
    interests: '',
    last_login_time: 0,
    intro: '',
    is_fake: 0,
    is_official: false,
    photos: [],
    coin: '',
    coin_show: '',
    point: '',
    show_video: '',
    show_video_pic: '',
    video_reco: 0,
    avatar: '',
    avatar_big: '',
    avatar_icon: '',
    age: '',
    client_status: '',
    is_active: 0,
    front_online: 0,
    has_call_card: 0,
    call_card_num: 0,
    video_like_tag: 0,
    need_upload_video: 0,
    need_upload_audio: 0,
    need_verify_account: 0,
    medal_url: '',
    price: 0,
    chat_bubble_item: undefined,
    avatar_frame_item: undefined,
    ctime: 0,
    had_avatar: 0,
    need_verify_photo: 0,
    photo_verify_status: 0,
    inactive_account: false,
    follow_count: 0,
    fans_count: 0,
    follow_state: 0,
    country_url: '',
    today_income: 0,
    grade: '',
    avatar_review: 0,
    video_review: 0,
    photo_review: 0
  };
}

export const InfoProfileResp: MessageFns<InfoProfileResp> = {
  fromJSON(object: any): InfoProfileResp {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      birthday: isSet(object.birthday) ? globalThis.String(object.birthday) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      invite_code: isSet(object.invite_code) ? globalThis.String(object.invite_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      utype: isSet(object.utype) ? globalThis.String(object.utype) : '',
      fb_openid: isSet(object.fb_openid) ? globalThis.String(object.fb_openid) : '',
      gp_openid: isSet(object.gp_openid) ? globalThis.String(object.gp_openid) : '',
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0,
      video_utime: isSet(object.video_utime) ? globalThis.Number(object.video_utime) : 0,
      had_video: isSet(object.had_video) ? globalThis.Number(object.had_video) : 0,
      had_fake_video: isSet(object.had_fake_video) ? globalThis.Number(object.had_fake_video) : 0,
      avatar_utime: isSet(object.avatar_utime) ? globalThis.Number(object.avatar_utime) : 0,
      verify_status: isSet(object.verify_status) ? globalThis.String(object.verify_status) : '',
      verify_anchor: isSet(object.verify_anchor) ? globalThis.Number(object.verify_anchor) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      exp: isSet(object.exp) ? globalThis.Number(object.exp) : 0,
      point_total: isSet(object.point_total) ? globalThis.Number(object.point_total) : 0,
      code_status: isSet(object.code_status) ? globalThis.Number(object.code_status) : 0,
      interests: isSet(object.interests) ? globalThis.String(object.interests) : '',
      last_login_time: isSet(object.last_login_time) ? globalThis.Number(object.last_login_time) : 0,
      intro: isSet(object.intro) ? globalThis.String(object.intro) : '',
      is_fake: isSet(object.is_fake) ? globalThis.Number(object.is_fake) : 0,
      is_official: isSet(object.is_official) ? globalThis.Boolean(object.is_official) : false,
      photos: globalThis.Array.isArray(object?.photos)
        ? object.photos.map((e: any) => InfoProfileResp_PhotoData.fromJSON(e))
        : [],
      coin: isSet(object.coin) ? globalThis.String(object.coin) : '',
      coin_show: isSet(object.coin_show) ? globalThis.String(object.coin_show) : '',
      point: isSet(object.point) ? globalThis.String(object.point) : '',
      show_video: isSet(object.show_video) ? globalThis.String(object.show_video) : '',
      show_video_pic: isSet(object.show_video_pic) ? globalThis.String(object.show_video_pic) : '',
      video_reco: isSet(object.video_reco) ? globalThis.Number(object.video_reco) : 0,
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      avatar_big: isSet(object.avatar_big) ? globalThis.String(object.avatar_big) : '',
      avatar_icon: isSet(object.avatar_icon) ? globalThis.String(object.avatar_icon) : '',
      age: isSet(object.age) ? globalThis.String(object.age) : '',
      client_status: isSet(object.client_status) ? globalThis.String(object.client_status) : '',
      is_active: isSet(object.is_active) ? globalThis.Number(object.is_active) : 0,
      front_online: isSet(object.front_online) ? globalThis.Number(object.front_online) : 0,
      has_call_card: isSet(object.has_call_card) ? globalThis.Number(object.has_call_card) : 0,
      call_card_num: isSet(object.call_card_num) ? globalThis.Number(object.call_card_num) : 0,
      video_like_tag: isSet(object.video_like_tag) ? globalThis.Number(object.video_like_tag) : 0,
      need_upload_video: isSet(object.need_upload_video) ? globalThis.Number(object.need_upload_video) : 0,
      need_upload_audio: isSet(object.need_upload_audio) ? globalThis.Number(object.need_upload_audio) : 0,
      need_verify_account: isSet(object.need_verify_account) ? globalThis.Number(object.need_verify_account) : 0,
      medal_url: isSet(object.medal_url) ? globalThis.String(object.medal_url) : '',
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      chat_bubble_item: isSet(object.chat_bubble_item)
        ? InfoProfileResp_ChatBubbleItem.fromJSON(object.chat_bubble_item)
        : undefined,
      avatar_frame_item: isSet(object.avatar_frame_item)
        ? InfoProfileResp_AvatarFrameItem.fromJSON(object.avatar_frame_item)
        : undefined,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      had_avatar: isSet(object.had_avatar) ? globalThis.Number(object.had_avatar) : 0,
      need_verify_photo: isSet(object.need_verify_photo) ? globalThis.Number(object.need_verify_photo) : 0,
      photo_verify_status: isSet(object.photo_verify_status) ? globalThis.Number(object.photo_verify_status) : 0,
      inactive_account: isSet(object.inactive_account) ? globalThis.Boolean(object.inactive_account) : false,
      follow_count: isSet(object.follow_count) ? globalThis.Number(object.follow_count) : 0,
      fans_count: isSet(object.fans_count) ? globalThis.Number(object.fans_count) : 0,
      follow_state: isSet(object.follow_state) ? globalThis.Number(object.follow_state) : 0,
      country_url: isSet(object.country_url) ? globalThis.String(object.country_url) : '',
      today_income: isSet(object.today_income) ? globalThis.Number(object.today_income) : 0,
      grade: isSet(object.grade) ? globalThis.String(object.grade) : '',
      avatar_review: isSet(object.avatar_review) ? globalThis.Number(object.avatar_review) : 0,
      video_review: isSet(object.video_review) ? globalThis.Number(object.video_review) : 0,
      photo_review: isSet(object.photo_review) ? globalThis.Number(object.photo_review) : 0
    };
  },

  create<I extends Exact<DeepPartial<InfoProfileResp>, I>>(base?: I): InfoProfileResp {
    return InfoProfileResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InfoProfileResp>, I>>(object: I): InfoProfileResp {
    const message = createBaseInfoProfileResp();
    message.uid = object.uid ?? '';
    message.sex = object.sex ?? '';
    message.nickname = object.nickname ?? '';
    message.birthday = object.birthday ?? '';
    message.country = object.country ?? '';
    message.status = object.status ?? 0;
    message.lan = object.lan ?? '';
    message.invite_code = object.invite_code ?? '';
    message.phone = object.phone ?? '';
    message.utype = object.utype ?? '';
    message.fb_openid = object.fb_openid ?? '';
    message.gp_openid = object.gp_openid ?? '';
    message.is_delete = object.is_delete ?? 0;
    message.video_utime = object.video_utime ?? 0;
    message.had_video = object.had_video ?? 0;
    message.had_fake_video = object.had_fake_video ?? 0;
    message.avatar_utime = object.avatar_utime ?? 0;
    message.verify_status = object.verify_status ?? '';
    message.verify_anchor = object.verify_anchor ?? 0;
    message.level = object.level ?? 0;
    message.exp = object.exp ?? 0;
    message.point_total = object.point_total ?? 0;
    message.code_status = object.code_status ?? 0;
    message.interests = object.interests ?? '';
    message.last_login_time = object.last_login_time ?? 0;
    message.intro = object.intro ?? '';
    message.is_fake = object.is_fake ?? 0;
    message.is_official = object.is_official ?? false;
    message.photos = object.photos?.map(e => InfoProfileResp_PhotoData.fromPartial(e)) || [];
    message.coin = object.coin ?? '';
    message.coin_show = object.coin_show ?? '';
    message.point = object.point ?? '';
    message.show_video = object.show_video ?? '';
    message.show_video_pic = object.show_video_pic ?? '';
    message.video_reco = object.video_reco ?? 0;
    message.avatar = object.avatar ?? '';
    message.avatar_big = object.avatar_big ?? '';
    message.avatar_icon = object.avatar_icon ?? '';
    message.age = object.age ?? '';
    message.client_status = object.client_status ?? '';
    message.is_active = object.is_active ?? 0;
    message.front_online = object.front_online ?? 0;
    message.has_call_card = object.has_call_card ?? 0;
    message.call_card_num = object.call_card_num ?? 0;
    message.video_like_tag = object.video_like_tag ?? 0;
    message.need_upload_video = object.need_upload_video ?? 0;
    message.need_upload_audio = object.need_upload_audio ?? 0;
    message.need_verify_account = object.need_verify_account ?? 0;
    message.medal_url = object.medal_url ?? '';
    message.price = object.price ?? 0;
    message.chat_bubble_item =
      object.chat_bubble_item !== undefined && object.chat_bubble_item !== null
        ? InfoProfileResp_ChatBubbleItem.fromPartial(object.chat_bubble_item)
        : undefined;
    message.avatar_frame_item =
      object.avatar_frame_item !== undefined && object.avatar_frame_item !== null
        ? InfoProfileResp_AvatarFrameItem.fromPartial(object.avatar_frame_item)
        : undefined;
    message.ctime = object.ctime ?? 0;
    message.had_avatar = object.had_avatar ?? 0;
    message.need_verify_photo = object.need_verify_photo ?? 0;
    message.photo_verify_status = object.photo_verify_status ?? 0;
    message.inactive_account = object.inactive_account ?? false;
    message.follow_count = object.follow_count ?? 0;
    message.fans_count = object.fans_count ?? 0;
    message.follow_state = object.follow_state ?? 0;
    message.country_url = object.country_url ?? '';
    message.today_income = object.today_income ?? 0;
    message.grade = object.grade ?? '';
    message.avatar_review = object.avatar_review ?? 0;
    message.video_review = object.video_review ?? 0;
    message.photo_review = object.photo_review ?? 0;
    return message;
  }
};

function createBaseInfoProfileResp_PhotoData(): InfoProfileResp_PhotoData {
  return { id: 0, uid: '', photo_id: '', like_num: 0, ctime: 0, utime: 0, is_delete: 0 };
}

export const InfoProfileResp_PhotoData: MessageFns<InfoProfileResp_PhotoData> = {
  fromJSON(object: any): InfoProfileResp_PhotoData {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      photo_id: isSet(object.photo_id) ? globalThis.String(object.photo_id) : '',
      like_num: isSet(object.like_num) ? globalThis.Number(object.like_num) : 0,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0
    };
  },

  create<I extends Exact<DeepPartial<InfoProfileResp_PhotoData>, I>>(base?: I): InfoProfileResp_PhotoData {
    return InfoProfileResp_PhotoData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InfoProfileResp_PhotoData>, I>>(object: I): InfoProfileResp_PhotoData {
    const message = createBaseInfoProfileResp_PhotoData();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? '';
    message.photo_id = object.photo_id ?? '';
    message.like_num = object.like_num ?? 0;
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    message.is_delete = object.is_delete ?? 0;
    return message;
  }
};

function createBaseInfoProfileResp_ChatBubbleItem(): InfoProfileResp_ChatBubbleItem {
  return {
    preview_url: '',
    background_bottom_frame: '',
    upper_left: '',
    upper_right: '',
    lower_left: '',
    lower_right: '',
    text_color: ''
  };
}

export const InfoProfileResp_ChatBubbleItem: MessageFns<InfoProfileResp_ChatBubbleItem> = {
  fromJSON(object: any): InfoProfileResp_ChatBubbleItem {
    return {
      preview_url: isSet(object.preview_url) ? globalThis.String(object.preview_url) : '',
      background_bottom_frame: isSet(object.background_bottom_frame)
        ? globalThis.String(object.background_bottom_frame)
        : '',
      upper_left: isSet(object.upper_left) ? globalThis.String(object.upper_left) : '',
      upper_right: isSet(object.upper_right) ? globalThis.String(object.upper_right) : '',
      lower_left: isSet(object.lower_left) ? globalThis.String(object.lower_left) : '',
      lower_right: isSet(object.lower_right) ? globalThis.String(object.lower_right) : '',
      text_color: isSet(object.text_color) ? globalThis.String(object.text_color) : ''
    };
  },

  create<I extends Exact<DeepPartial<InfoProfileResp_ChatBubbleItem>, I>>(base?: I): InfoProfileResp_ChatBubbleItem {
    return InfoProfileResp_ChatBubbleItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InfoProfileResp_ChatBubbleItem>, I>>(
    object: I
  ): InfoProfileResp_ChatBubbleItem {
    const message = createBaseInfoProfileResp_ChatBubbleItem();
    message.preview_url = object.preview_url ?? '';
    message.background_bottom_frame = object.background_bottom_frame ?? '';
    message.upper_left = object.upper_left ?? '';
    message.upper_right = object.upper_right ?? '';
    message.lower_left = object.lower_left ?? '';
    message.lower_right = object.lower_right ?? '';
    message.text_color = object.text_color ?? '';
    return message;
  }
};

function createBaseInfoProfileResp_AvatarFrameItem(): InfoProfileResp_AvatarFrameItem {
  return { preview_url: '', resource_url: '', type: '' };
}

export const InfoProfileResp_AvatarFrameItem: MessageFns<InfoProfileResp_AvatarFrameItem> = {
  fromJSON(object: any): InfoProfileResp_AvatarFrameItem {
    return {
      preview_url: isSet(object.preview_url) ? globalThis.String(object.preview_url) : '',
      resource_url: isSet(object.resource_url) ? globalThis.String(object.resource_url) : '',
      type: isSet(object.type) ? globalThis.String(object.type) : ''
    };
  },

  create<I extends Exact<DeepPartial<InfoProfileResp_AvatarFrameItem>, I>>(base?: I): InfoProfileResp_AvatarFrameItem {
    return InfoProfileResp_AvatarFrameItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InfoProfileResp_AvatarFrameItem>, I>>(
    object: I
  ): InfoProfileResp_AvatarFrameItem {
    const message = createBaseInfoProfileResp_AvatarFrameItem();
    message.preview_url = object.preview_url ?? '';
    message.resource_url = object.resource_url ?? '';
    message.type = object.type ?? '';
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return {
    name: '',
    uid: '',
    age: '',
    verify_status: '',
    level: 0,
    vip_name: '',
    avatar: '',
    avatar_frame_item: undefined,
    medal: '',
    sex: '',
    Utype: 0,
    vip_icon: '',
    language: '',
    country: '',
    region: '',
    status: 0,
    replace_avatar: ''
  };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      age: isSet(object.age) ? globalThis.String(object.age) : '',
      verify_status: isSet(object.verify_status) ? globalThis.String(object.verify_status) : '',
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      vip_name: isSet(object.vip_name) ? globalThis.String(object.vip_name) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      avatar_frame_item: isSet(object.avatar_frame_item)
        ? AvatarFrameItem.fromJSON(object.avatar_frame_item)
        : undefined,
      medal: isSet(object.medal) ? globalThis.String(object.medal) : '',
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      Utype: isSet(object.Utype) ? userTypeFromJSON(object.Utype) : 0,
      vip_icon: isSet(object.vip_icon) ? globalThis.String(object.vip_icon) : '',
      language: isSet(object.language) ? globalThis.String(object.language) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      region: isSet(object.region) ? globalThis.String(object.region) : '',
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      replace_avatar: isSet(object.replace_avatar) ? globalThis.String(object.replace_avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.name = object.name ?? '';
    message.uid = object.uid ?? '';
    message.age = object.age ?? '';
    message.verify_status = object.verify_status ?? '';
    message.level = object.level ?? 0;
    message.vip_name = object.vip_name ?? '';
    message.avatar = object.avatar ?? '';
    message.avatar_frame_item =
      object.avatar_frame_item !== undefined && object.avatar_frame_item !== null
        ? AvatarFrameItem.fromPartial(object.avatar_frame_item)
        : undefined;
    message.medal = object.medal ?? '';
    message.sex = object.sex ?? '';
    message.Utype = object.Utype ?? 0;
    message.vip_icon = object.vip_icon ?? '';
    message.language = object.language ?? '';
    message.country = object.country ?? '';
    message.region = object.region ?? '';
    message.status = object.status ?? 0;
    message.replace_avatar = object.replace_avatar ?? '';
    return message;
  }
};

function createBaseGetPrivilegeReq(): GetPrivilegeReq {
  return {};
}

export const GetPrivilegeReq: MessageFns<GetPrivilegeReq> = {
  fromJSON(_: any): GetPrivilegeReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetPrivilegeReq>, I>>(base?: I): GetPrivilegeReq {
    return GetPrivilegeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrivilegeReq>, I>>(_: I): GetPrivilegeReq {
    const message = createBaseGetPrivilegeReq();
    return message;
  }
};

function createBaseGetPrivilegeResp(): GetPrivilegeResp {
  return { is_pay_post: false };
}

export const GetPrivilegeResp: MessageFns<GetPrivilegeResp> = {
  fromJSON(object: any): GetPrivilegeResp {
    return { is_pay_post: isSet(object.is_pay_post) ? globalThis.Boolean(object.is_pay_post) : false };
  },

  create<I extends Exact<DeepPartial<GetPrivilegeResp>, I>>(base?: I): GetPrivilegeResp {
    return GetPrivilegeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrivilegeResp>, I>>(object: I): GetPrivilegeResp {
    const message = createBaseGetPrivilegeResp();
    message.is_pay_post = object.is_pay_post ?? false;
    return message;
  }
};

export type UserSrvDefinition = typeof UserSrvDefinition;
export const UserSrvDefinition = {
  name: 'UserSrv',
  fullName: 'user.UserSrv',
  methods: {
    /** 获取用户资源 */
    getUserResource: {
      name: 'GetUserResource',
      requestType: GetUserResourceReq,
      requestStream: false,
      responseType: GetUserResourceResp,
      responseStream: false,
      options: {}
    },
    /** 男用户 我的页 基础信息 */
    maleBasicInfo: {
      name: 'MaleBasicInfo',
      requestType: MaleBasicInfoReq,
      requestStream: false,
      responseType: MaleBasicInfoResp,
      responseStream: false,
      options: {}
    },
    /** 举报拉黑某个人 */
    block: {
      name: 'Block',
      requestType: BlockReq,
      requestStream: false,
      responseType: BlockResp,
      responseStream: false,
      options: {}
    },
    /** 更新个人资料 */
    updateProfile: {
      name: 'UpdateProfile',
      requestType: UpdateProfileReq,
      requestStream: false,
      responseType: UpdateProfileResp,
      responseStream: false,
      options: {}
    },
    /** 更新个人相册 */
    updatePhoto: {
      name: 'UpdatePhoto',
      requestType: UpdatePhotoReq,
      requestStream: false,
      responseType: UpdatePhotoResp,
      responseStream: false,
      options: {}
    },
    /** 查看个人资料 */
    infoProfile: {
      name: 'InfoProfile',
      requestType: InfoProfileReq,
      requestStream: false,
      responseType: InfoProfileResp,
      responseStream: false,
      options: {}
    },
    /** 获取个人权益 */
    getPrivilege: {
      name: 'GetPrivilege',
      requestType: GetPrivilegeReq,
      requestStream: false,
      responseType: GetPrivilegeResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
