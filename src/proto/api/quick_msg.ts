// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/quick_msg.proto

/* eslint-disable */

export const protobufPackage = 'quickmsg';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/quickmsgsrv */

export enum QuickMsgType {
  QUICK_MSG_TYPE_NONE = 0,
  QUICK_MSG_TYPE_SAY_HI = 1,
  QUICK_MSG_TYPE_FLIRT = 2,
  QUICK_MSG_TYPE_BIND = 3,
  QUICK_MSG_TYPE_CUSTOMIZE = 4,
  UNRECOGNIZED = -1
}

export function quickMsgTypeFromJSON(object: any): QuickMsgType {
  switch (object) {
    case 0:
    case 'QUICK_MSG_TYPE_NONE':
      return QuickMsgType.QUICK_MSG_TYPE_NONE;
    case 1:
    case 'QUICK_MSG_TYPE_SAY_HI':
      return QuickMsgType.QUICK_MSG_TYPE_SAY_HI;
    case 2:
    case 'QUICK_MSG_TYPE_FLIRT':
      return QuickMsgType.QUICK_MSG_TYPE_FLIRT;
    case 3:
    case 'QUICK_MSG_TYPE_BIND':
      return QuickMsgType.QUICK_MSG_TYPE_BIND;
    case 4:
    case 'QUICK_MSG_TYPE_CUSTOMIZE':
      return QuickMsgType.QUICK_MSG_TYPE_CUSTOMIZE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return QuickMsgType.UNRECOGNIZED;
  }
}

export enum ReportLongNotifyType {
  LONG_NOTIFY_NONE = 0,
  /** LONG_NOTIFY_USER_DETAIL - 一开始只有 */
  LONG_NOTIFY_USER_DETAIL = 1,
  UNRECOGNIZED = -1
}

export function reportLongNotifyTypeFromJSON(object: any): ReportLongNotifyType {
  switch (object) {
    case 0:
    case 'LONG_NOTIFY_NONE':
      return ReportLongNotifyType.LONG_NOTIFY_NONE;
    case 1:
    case 'LONG_NOTIFY_USER_DETAIL':
      return ReportLongNotifyType.LONG_NOTIFY_USER_DETAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ReportLongNotifyType.UNRECOGNIZED;
  }
}

export interface ListQuickMsgReq {
  /** 类型 */
  msg_type: QuickMsgType;
}

export interface ListQuickMsgResp {
  items: ListQuickMsgResp_QuickMsgData[];
}

export interface ListQuickMsgResp_QuickMsgData {
  /** 类型 */
  msg_type: QuickMsgType;
  /** 编号 */
  template_no: string;
  /** 文案 */
  content: string;
  /** 音频地址 */
  audio_path: string;
  /** 音频时长 */
  audio_duration: number;
}

export interface AddQuickMsgReq {
  /** 类型 */
  msg_type: QuickMsgType;
  /** 文案 */
  content: string;
}

export interface AddQuickMsgResp {}

export interface UpdateQuickMsgReq {
  /** 编号 */
  template_no: string;
  /** 文案 */
  content: string;
  /** 音频相对地址 */
  audio_path: string;
  /** 音频时长 */
  audio_duration: number;
  /** 类型 */
  msg_type: QuickMsgType;
}

export interface UpdateQuickMsgResp {}

export interface DeleteQuickMsgReq {
  /** 编号 */
  template_no: string;
}

export interface DeleteQuickMsgResp {}

export interface ReportLongTimeNotifyReq {
  notify_type: ReportLongNotifyType;
  fUid: string;
}

export interface ReportLongTimeNotifyResp {}

function createBaseListQuickMsgReq(): ListQuickMsgReq {
  return { msg_type: 0 };
}

export const ListQuickMsgReq: MessageFns<ListQuickMsgReq> = {
  fromJSON(object: any): ListQuickMsgReq {
    return { msg_type: isSet(object.msg_type) ? quickMsgTypeFromJSON(object.msg_type) : 0 };
  },

  create<I extends Exact<DeepPartial<ListQuickMsgReq>, I>>(base?: I): ListQuickMsgReq {
    return ListQuickMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListQuickMsgReq>, I>>(object: I): ListQuickMsgReq {
    const message = createBaseListQuickMsgReq();
    message.msg_type = object.msg_type ?? 0;
    return message;
  }
};

function createBaseListQuickMsgResp(): ListQuickMsgResp {
  return { items: [] };
}

export const ListQuickMsgResp: MessageFns<ListQuickMsgResp> = {
  fromJSON(object: any): ListQuickMsgResp {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => ListQuickMsgResp_QuickMsgData.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListQuickMsgResp>, I>>(base?: I): ListQuickMsgResp {
    return ListQuickMsgResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListQuickMsgResp>, I>>(object: I): ListQuickMsgResp {
    const message = createBaseListQuickMsgResp();
    message.items = object.items?.map(e => ListQuickMsgResp_QuickMsgData.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListQuickMsgResp_QuickMsgData(): ListQuickMsgResp_QuickMsgData {
  return { msg_type: 0, template_no: '', content: '', audio_path: '', audio_duration: 0 };
}

export const ListQuickMsgResp_QuickMsgData: MessageFns<ListQuickMsgResp_QuickMsgData> = {
  fromJSON(object: any): ListQuickMsgResp_QuickMsgData {
    return {
      msg_type: isSet(object.msg_type) ? quickMsgTypeFromJSON(object.msg_type) : 0,
      template_no: isSet(object.template_no) ? globalThis.String(object.template_no) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      audio_path: isSet(object.audio_path) ? globalThis.String(object.audio_path) : '',
      audio_duration: isSet(object.audio_duration) ? globalThis.Number(object.audio_duration) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListQuickMsgResp_QuickMsgData>, I>>(base?: I): ListQuickMsgResp_QuickMsgData {
    return ListQuickMsgResp_QuickMsgData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListQuickMsgResp_QuickMsgData>, I>>(
    object: I
  ): ListQuickMsgResp_QuickMsgData {
    const message = createBaseListQuickMsgResp_QuickMsgData();
    message.msg_type = object.msg_type ?? 0;
    message.template_no = object.template_no ?? '';
    message.content = object.content ?? '';
    message.audio_path = object.audio_path ?? '';
    message.audio_duration = object.audio_duration ?? 0;
    return message;
  }
};

function createBaseAddQuickMsgReq(): AddQuickMsgReq {
  return { msg_type: 0, content: '' };
}

export const AddQuickMsgReq: MessageFns<AddQuickMsgReq> = {
  fromJSON(object: any): AddQuickMsgReq {
    return {
      msg_type: isSet(object.msg_type) ? quickMsgTypeFromJSON(object.msg_type) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddQuickMsgReq>, I>>(base?: I): AddQuickMsgReq {
    return AddQuickMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddQuickMsgReq>, I>>(object: I): AddQuickMsgReq {
    const message = createBaseAddQuickMsgReq();
    message.msg_type = object.msg_type ?? 0;
    message.content = object.content ?? '';
    return message;
  }
};

function createBaseAddQuickMsgResp(): AddQuickMsgResp {
  return {};
}

export const AddQuickMsgResp: MessageFns<AddQuickMsgResp> = {
  fromJSON(_: any): AddQuickMsgResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddQuickMsgResp>, I>>(base?: I): AddQuickMsgResp {
    return AddQuickMsgResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddQuickMsgResp>, I>>(_: I): AddQuickMsgResp {
    const message = createBaseAddQuickMsgResp();
    return message;
  }
};

function createBaseUpdateQuickMsgReq(): UpdateQuickMsgReq {
  return { template_no: '', content: '', audio_path: '', audio_duration: 0, msg_type: 0 };
}

export const UpdateQuickMsgReq: MessageFns<UpdateQuickMsgReq> = {
  fromJSON(object: any): UpdateQuickMsgReq {
    return {
      template_no: isSet(object.template_no) ? globalThis.String(object.template_no) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      audio_path: isSet(object.audio_path) ? globalThis.String(object.audio_path) : '',
      audio_duration: isSet(object.audio_duration) ? globalThis.Number(object.audio_duration) : 0,
      msg_type: isSet(object.msg_type) ? quickMsgTypeFromJSON(object.msg_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateQuickMsgReq>, I>>(base?: I): UpdateQuickMsgReq {
    return UpdateQuickMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateQuickMsgReq>, I>>(object: I): UpdateQuickMsgReq {
    const message = createBaseUpdateQuickMsgReq();
    message.template_no = object.template_no ?? '';
    message.content = object.content ?? '';
    message.audio_path = object.audio_path ?? '';
    message.audio_duration = object.audio_duration ?? 0;
    message.msg_type = object.msg_type ?? 0;
    return message;
  }
};

function createBaseUpdateQuickMsgResp(): UpdateQuickMsgResp {
  return {};
}

export const UpdateQuickMsgResp: MessageFns<UpdateQuickMsgResp> = {
  fromJSON(_: any): UpdateQuickMsgResp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateQuickMsgResp>, I>>(base?: I): UpdateQuickMsgResp {
    return UpdateQuickMsgResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateQuickMsgResp>, I>>(_: I): UpdateQuickMsgResp {
    const message = createBaseUpdateQuickMsgResp();
    return message;
  }
};

function createBaseDeleteQuickMsgReq(): DeleteQuickMsgReq {
  return { template_no: '' };
}

export const DeleteQuickMsgReq: MessageFns<DeleteQuickMsgReq> = {
  fromJSON(object: any): DeleteQuickMsgReq {
    return { template_no: isSet(object.template_no) ? globalThis.String(object.template_no) : '' };
  },

  create<I extends Exact<DeepPartial<DeleteQuickMsgReq>, I>>(base?: I): DeleteQuickMsgReq {
    return DeleteQuickMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteQuickMsgReq>, I>>(object: I): DeleteQuickMsgReq {
    const message = createBaseDeleteQuickMsgReq();
    message.template_no = object.template_no ?? '';
    return message;
  }
};

function createBaseDeleteQuickMsgResp(): DeleteQuickMsgResp {
  return {};
}

export const DeleteQuickMsgResp: MessageFns<DeleteQuickMsgResp> = {
  fromJSON(_: any): DeleteQuickMsgResp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteQuickMsgResp>, I>>(base?: I): DeleteQuickMsgResp {
    return DeleteQuickMsgResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteQuickMsgResp>, I>>(_: I): DeleteQuickMsgResp {
    const message = createBaseDeleteQuickMsgResp();
    return message;
  }
};

function createBaseReportLongTimeNotifyReq(): ReportLongTimeNotifyReq {
  return { notify_type: 0, fUid: '' };
}

export const ReportLongTimeNotifyReq: MessageFns<ReportLongTimeNotifyReq> = {
  fromJSON(object: any): ReportLongTimeNotifyReq {
    return {
      notify_type: isSet(object.notify_type) ? reportLongNotifyTypeFromJSON(object.notify_type) : 0,
      fUid: isSet(object.fUid) ? globalThis.String(object.fUid) : ''
    };
  },

  create<I extends Exact<DeepPartial<ReportLongTimeNotifyReq>, I>>(base?: I): ReportLongTimeNotifyReq {
    return ReportLongTimeNotifyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportLongTimeNotifyReq>, I>>(object: I): ReportLongTimeNotifyReq {
    const message = createBaseReportLongTimeNotifyReq();
    message.notify_type = object.notify_type ?? 0;
    message.fUid = object.fUid ?? '';
    return message;
  }
};

function createBaseReportLongTimeNotifyResp(): ReportLongTimeNotifyResp {
  return {};
}

export const ReportLongTimeNotifyResp: MessageFns<ReportLongTimeNotifyResp> = {
  fromJSON(_: any): ReportLongTimeNotifyResp {
    return {};
  },

  create<I extends Exact<DeepPartial<ReportLongTimeNotifyResp>, I>>(base?: I): ReportLongTimeNotifyResp {
    return ReportLongTimeNotifyResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportLongTimeNotifyResp>, I>>(_: I): ReportLongTimeNotifyResp {
    const message = createBaseReportLongTimeNotifyResp();
    return message;
  }
};

export type QuickMsgDefinition = typeof QuickMsgDefinition;
export const QuickMsgDefinition = {
  name: 'QuickMsg',
  fullName: 'quickmsg.QuickMsg',
  methods: {
    /** 列出快捷消息 */
    listQuickMsg: {
      name: 'ListQuickMsg',
      requestType: ListQuickMsgReq,
      requestStream: false,
      responseType: ListQuickMsgResp,
      responseStream: false,
      options: {}
    },
    /** 新增消息（自定义） */
    addQuickMsg: {
      name: 'AddQuickMsg',
      requestType: AddQuickMsgReq,
      requestStream: false,
      responseType: AddQuickMsgResp,
      responseStream: false,
      options: {}
    },
    /** 更新消息 */
    updateQuickMsg: {
      name: 'UpdateQuickMsg',
      requestType: UpdateQuickMsgReq,
      requestStream: false,
      responseType: UpdateQuickMsgResp,
      responseStream: false,
      options: {}
    },
    /** 移除消息（自定义） */
    deleteQuickMsg: {
      name: 'DeleteQuickMsg',
      requestType: DeleteQuickMsgReq,
      requestStream: false,
      responseType: DeleteQuickMsgResp,
      responseStream: false,
      options: {}
    },
    /** 客户端待在个人主页超过多少秒 直接上报这个接口就好 */
    reportLongTimeNotify: {
      name: 'ReportLongTimeNotify',
      requestType: ReportLongTimeNotifyReq,
      requestStream: false,
      responseType: ReportLongTimeNotifyResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
