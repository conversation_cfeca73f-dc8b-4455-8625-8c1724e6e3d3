// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/follow.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'follow';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/followsrv */

export interface FollowReq {
  /** 被关注人 */
  follow_id: string;
}

export interface FollowRsp {}

export interface UnfollowReq {
  /** 被取消关注人 */
  follow_id: string;
}

export interface UnfollowRsp {}

export interface RelationUserRsp {
  /** 用户名称 */
  name: string;
  /** 用户uid */
  uid: string;
  /** 年龄 */
  age: string;
  /** 用户头像地址 */
  avatar: string;
  /** 性别 */
  sex: string;
  /** 国旗地址 */
  country_url: string;
}

export interface GetFollowListReq {
  page: Page | undefined;
}

export interface GetFollowListRsp {
  page: Page | undefined;
  list: RelationUserRsp[];
}

export interface GetFansListReq {
  page: Page | undefined;
}

export interface GetFansListRsp {
  page: Page | undefined;
  list: RelationUserRsp[];
}

function createBaseFollowReq(): FollowReq {
  return { follow_id: '' };
}

export const FollowReq: MessageFns<FollowReq> = {
  fromJSON(object: any): FollowReq {
    return { follow_id: isSet(object.follow_id) ? globalThis.String(object.follow_id) : '' };
  },

  create<I extends Exact<DeepPartial<FollowReq>, I>>(base?: I): FollowReq {
    return FollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowReq>, I>>(object: I): FollowReq {
    const message = createBaseFollowReq();
    message.follow_id = object.follow_id ?? '';
    return message;
  }
};

function createBaseFollowRsp(): FollowRsp {
  return {};
}

export const FollowRsp: MessageFns<FollowRsp> = {
  fromJSON(_: any): FollowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<FollowRsp>, I>>(base?: I): FollowRsp {
    return FollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRsp>, I>>(_: I): FollowRsp {
    const message = createBaseFollowRsp();
    return message;
  }
};

function createBaseUnfollowReq(): UnfollowReq {
  return { follow_id: '' };
}

export const UnfollowReq: MessageFns<UnfollowReq> = {
  fromJSON(object: any): UnfollowReq {
    return { follow_id: isSet(object.follow_id) ? globalThis.String(object.follow_id) : '' };
  },

  create<I extends Exact<DeepPartial<UnfollowReq>, I>>(base?: I): UnfollowReq {
    return UnfollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnfollowReq>, I>>(object: I): UnfollowReq {
    const message = createBaseUnfollowReq();
    message.follow_id = object.follow_id ?? '';
    return message;
  }
};

function createBaseUnfollowRsp(): UnfollowRsp {
  return {};
}

export const UnfollowRsp: MessageFns<UnfollowRsp> = {
  fromJSON(_: any): UnfollowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnfollowRsp>, I>>(base?: I): UnfollowRsp {
    return UnfollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnfollowRsp>, I>>(_: I): UnfollowRsp {
    const message = createBaseUnfollowRsp();
    return message;
  }
};

function createBaseRelationUserRsp(): RelationUserRsp {
  return { name: '', uid: '', age: '', avatar: '', sex: '', country_url: '' };
}

export const RelationUserRsp: MessageFns<RelationUserRsp> = {
  fromJSON(object: any): RelationUserRsp {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      age: isSet(object.age) ? globalThis.String(object.age) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      country_url: isSet(object.country_url) ? globalThis.String(object.country_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<RelationUserRsp>, I>>(base?: I): RelationUserRsp {
    return RelationUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RelationUserRsp>, I>>(object: I): RelationUserRsp {
    const message = createBaseRelationUserRsp();
    message.name = object.name ?? '';
    message.uid = object.uid ?? '';
    message.age = object.age ?? '';
    message.avatar = object.avatar ?? '';
    message.sex = object.sex ?? '';
    message.country_url = object.country_url ?? '';
    return message;
  }
};

function createBaseGetFollowListReq(): GetFollowListReq {
  return { page: undefined };
}

export const GetFollowListReq: MessageFns<GetFollowListReq> = {
  fromJSON(object: any): GetFollowListReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFollowListReq>, I>>(base?: I): GetFollowListReq {
    return GetFollowListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowListReq>, I>>(object: I): GetFollowListReq {
    const message = createBaseGetFollowListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFollowListRsp(): GetFollowListRsp {
  return { page: undefined, list: [] };
}

export const GetFollowListRsp: MessageFns<GetFollowListRsp> = {
  fromJSON(object: any): GetFollowListRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => RelationUserRsp.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetFollowListRsp>, I>>(base?: I): GetFollowListRsp {
    return GetFollowListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowListRsp>, I>>(object: I): GetFollowListRsp {
    const message = createBaseGetFollowListRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => RelationUserRsp.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetFansListReq(): GetFansListReq {
  return { page: undefined };
}

export const GetFansListReq: MessageFns<GetFansListReq> = {
  fromJSON(object: any): GetFansListReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFansListReq>, I>>(base?: I): GetFansListReq {
    return GetFansListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFansListReq>, I>>(object: I): GetFansListReq {
    const message = createBaseGetFansListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFansListRsp(): GetFansListRsp {
  return { page: undefined, list: [] };
}

export const GetFansListRsp: MessageFns<GetFansListRsp> = {
  fromJSON(object: any): GetFansListRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => RelationUserRsp.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetFansListRsp>, I>>(base?: I): GetFansListRsp {
    return GetFansListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFansListRsp>, I>>(object: I): GetFansListRsp {
    const message = createBaseGetFansListRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => RelationUserRsp.fromPartial(e)) || [];
    return message;
  }
};

export type FollowDefinition = typeof FollowDefinition;
export const FollowDefinition = {
  name: 'Follow',
  fullName: 'follow.Follow',
  methods: {
    /** 关注 */
    follow: {
      name: 'Follow',
      requestType: FollowReq,
      requestStream: false,
      responseType: FollowRsp,
      responseStream: false,
      options: {}
    },
    /** 取消关注 */
    unfollow: {
      name: 'Unfollow',
      requestType: UnfollowReq,
      requestStream: false,
      responseType: UnfollowRsp,
      responseStream: false,
      options: {}
    },
    /** 获取关注列表 */
    getFollowList: {
      name: 'GetFollowList',
      requestType: GetFollowListReq,
      requestStream: false,
      responseType: GetFollowListRsp,
      responseStream: false,
      options: {}
    },
    /** 获取粉丝列表 */
    getFansList: {
      name: 'GetFansList',
      requestType: GetFansListReq,
      requestStream: false,
      responseType: GetFansListRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
