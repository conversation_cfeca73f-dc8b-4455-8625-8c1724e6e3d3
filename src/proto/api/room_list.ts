// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/room_list.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';
import { RoomCategory as RoomCategory1, roomCategoryFromJSON, RoomInfo } from './comm';

export const protobufPackage = 'roomlist';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/roomlist/handler/ */

/** 分类显示样式 */
export enum RoomCategoryStyle {
  /** ROOM_CATEGORY_STYLE_TEXT - 纯文本 */
  ROOM_CATEGORY_STYLE_TEXT = 0,
  /** ROOM_CATEGORY_STYLE_ICON - 纯图标 */
  ROOM_CATEGORY_STYLE_ICON = 10,
  /** ROOM_CATEGORY_STYLE_ICON_TEXT - 图标 + 文本 */
  ROOM_CATEGORY_STYLE_ICON_TEXT = 20,
  /** ROOM_CATEGORY_STYLE_CUBES - hot页 豆腐块样式 */
  ROOM_CATEGORY_STYLE_CUBES = 30,
  /** ROOM_CATEGORY_STYLE_SIMPLE - follow 观看历史 简单样式 */
  ROOM_CATEGORY_STYLE_SIMPLE = 40,
  /** ROOM_CATEGORY_STYLE_CHAT - 旧的语聊房样式 */
  ROOM_CATEGORY_STYLE_CHAT = 50,
  UNRECOGNIZED = -1
}

export function roomCategoryStyleFromJSON(object: any): RoomCategoryStyle {
  switch (object) {
    case 0:
    case 'ROOM_CATEGORY_STYLE_TEXT':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_TEXT;
    case 10:
    case 'ROOM_CATEGORY_STYLE_ICON':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_ICON;
    case 20:
    case 'ROOM_CATEGORY_STYLE_ICON_TEXT':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_ICON_TEXT;
    case 30:
    case 'ROOM_CATEGORY_STYLE_CUBES':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_CUBES;
    case 40:
    case 'ROOM_CATEGORY_STYLE_SIMPLE':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_SIMPLE;
    case 50:
    case 'ROOM_CATEGORY_STYLE_CHAT':
      return RoomCategoryStyle.ROOM_CATEGORY_STYLE_CHAT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomCategoryStyle.UNRECOGNIZED;
  }
}

export enum ThemeCategoryTab {
  THEME_CATEGORY_NONE = 0,
  /** THEME_CATEGORY_ROOM - 主题 */
  THEME_CATEGORY_ROOM = 1,
  /** THEME_CATEGORY_ALL - 获取全部的  主题+推荐 */
  THEME_CATEGORY_ALL = 2,
  /** THEME_CATEGORY_RECOMMEND - 获取推荐的  不传就是只拿推荐的 安卓不会传就是只拿推荐 */
  THEME_CATEGORY_RECOMMEND = 3,
  UNRECOGNIZED = -1
}

export function themeCategoryTabFromJSON(object: any): ThemeCategoryTab {
  switch (object) {
    case 0:
    case 'THEME_CATEGORY_NONE':
      return ThemeCategoryTab.THEME_CATEGORY_NONE;
    case 1:
    case 'THEME_CATEGORY_ROOM':
      return ThemeCategoryTab.THEME_CATEGORY_ROOM;
    case 2:
    case 'THEME_CATEGORY_ALL':
      return ThemeCategoryTab.THEME_CATEGORY_ALL;
    case 3:
    case 'THEME_CATEGORY_RECOMMEND':
      return ThemeCategoryTab.THEME_CATEGORY_RECOMMEND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ThemeCategoryTab.UNRECOGNIZED;
  }
}

/** 房间分类 */
export interface RoomCategory {
  /** 房间分类ID, [0, 5] 属于程序保留的分类ID, 例如 推荐 / 热门 这种属于不在 OMS 配置的房间分类而是程序直接定义的, 推荐: 1, 热门: 2. */
  id: number;
  /** 房间分类缺省名称 */
  name: string;
  /** 国际化菜单名称, 中文: zh, 英文: en, 阿语: ar, ... */
  i18n_names: { [key: string]: string };
  /** 图标 */
  icon: string;
  /** 分类显示样式 */
  style: RoomCategoryStyle;
  /** 子分类 */
  sub_categories: SubRoomCategory[];
  /** 房间大类 */
  room_category: RoomCategory1;
  /** 场景子参 */
  second_source: string;
}

export interface RoomCategory_I18nNamesEntry {
  key: string;
  value: string;
}

export interface SubRoomCategory {
  id: string;
  /** 子房间分类缺省名称 */
  name: string;
  /** 国际化菜单名称, 中文: zh, 英文: en, 阿语: ar, ... */
  i18n_names: { [key: string]: string };
  /** 图标 */
  icon: string;
  /** 分类显示样式 */
  style: RoomCategoryStyle;
}

export interface SubRoomCategory_I18nNamesEntry {
  key: string;
  value: string;
}

export interface ListRoomCategoryReq {
  /** 需要的分类 */
  category_list: RoomCategory1[];
}

export interface ListRoomCategoryRsp {
  categories: RoomCategory[];
}

export interface ListRoomCardReq {
  page: Page | undefined;
  /** 主分类ID */
  category_id: number;
  /** 子分类ID */
  sub_category_id: string;
  /** 主题枚举 */
  tab: ThemeCategoryTab;
}

export interface ListRoomCardRsp {
  page: Page | undefined;
  /** 房间列表 */
  room_infos: RoomInfo[];
}

export interface ListRoomHotTopicReq {}

export interface ListRoomHotTopicRsp {
  topic_list: HotTopicInfo[];
}

export interface HotTopicInfo {
  /** 主题icon */
  icon: string;
  /** 主题名称 */
  name: string;
  /** 跳转的主题房间ids */
  room_ids: number[];
  /** 颜色 */
  color: string[];
  /** 主题唯一id */
  id: number;
  /** 麦位图标 */
  mic_icon: string;
}

function createBaseRoomCategory(): RoomCategory {
  return {
    id: 0,
    name: '',
    i18n_names: {},
    icon: '',
    style: 0,
    sub_categories: [],
    room_category: 0,
    second_source: ''
  };
}

export const RoomCategory: MessageFns<RoomCategory> = {
  fromJSON(object: any): RoomCategory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      style: isSet(object.style) ? roomCategoryStyleFromJSON(object.style) : 0,
      sub_categories: globalThis.Array.isArray(object?.sub_categories)
        ? object.sub_categories.map((e: any) => SubRoomCategory.fromJSON(e))
        : [],
      room_category: isSet(object.room_category) ? roomCategoryFromJSON(object.room_category) : 0,
      second_source: isSet(object.second_source) ? globalThis.String(object.second_source) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomCategory>, I>>(base?: I): RoomCategory {
    return RoomCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomCategory>, I>>(object: I): RoomCategory {
    const message = createBaseRoomCategory();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.icon = object.icon ?? '';
    message.style = object.style ?? 0;
    message.sub_categories = object.sub_categories?.map(e => SubRoomCategory.fromPartial(e)) || [];
    message.room_category = object.room_category ?? 0;
    message.second_source = object.second_source ?? '';
    return message;
  }
};

function createBaseRoomCategory_I18nNamesEntry(): RoomCategory_I18nNamesEntry {
  return { key: '', value: '' };
}

export const RoomCategory_I18nNamesEntry: MessageFns<RoomCategory_I18nNamesEntry> = {
  fromJSON(object: any): RoomCategory_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomCategory_I18nNamesEntry>, I>>(base?: I): RoomCategory_I18nNamesEntry {
    return RoomCategory_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomCategory_I18nNamesEntry>, I>>(object: I): RoomCategory_I18nNamesEntry {
    const message = createBaseRoomCategory_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSubRoomCategory(): SubRoomCategory {
  return { id: '', name: '', i18n_names: {}, icon: '', style: 0 };
}

export const SubRoomCategory: MessageFns<SubRoomCategory> = {
  fromJSON(object: any): SubRoomCategory {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      style: isSet(object.style) ? roomCategoryStyleFromJSON(object.style) : 0
    };
  },

  create<I extends Exact<DeepPartial<SubRoomCategory>, I>>(base?: I): SubRoomCategory {
    return SubRoomCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubRoomCategory>, I>>(object: I): SubRoomCategory {
    const message = createBaseSubRoomCategory();
    message.id = object.id ?? '';
    message.name = object.name ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.icon = object.icon ?? '';
    message.style = object.style ?? 0;
    return message;
  }
};

function createBaseSubRoomCategory_I18nNamesEntry(): SubRoomCategory_I18nNamesEntry {
  return { key: '', value: '' };
}

export const SubRoomCategory_I18nNamesEntry: MessageFns<SubRoomCategory_I18nNamesEntry> = {
  fromJSON(object: any): SubRoomCategory_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SubRoomCategory_I18nNamesEntry>, I>>(base?: I): SubRoomCategory_I18nNamesEntry {
    return SubRoomCategory_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubRoomCategory_I18nNamesEntry>, I>>(
    object: I
  ): SubRoomCategory_I18nNamesEntry {
    const message = createBaseSubRoomCategory_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListRoomCategoryReq(): ListRoomCategoryReq {
  return { category_list: [] };
}

export const ListRoomCategoryReq: MessageFns<ListRoomCategoryReq> = {
  fromJSON(object: any): ListRoomCategoryReq {
    return {
      category_list: globalThis.Array.isArray(object?.category_list)
        ? object.category_list.map((e: any) => roomCategoryFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomCategoryReq>, I>>(base?: I): ListRoomCategoryReq {
    return ListRoomCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomCategoryReq>, I>>(object: I): ListRoomCategoryReq {
    const message = createBaseListRoomCategoryReq();
    message.category_list = object.category_list?.map(e => e) || [];
    return message;
  }
};

function createBaseListRoomCategoryRsp(): ListRoomCategoryRsp {
  return { categories: [] };
}

export const ListRoomCategoryRsp: MessageFns<ListRoomCategoryRsp> = {
  fromJSON(object: any): ListRoomCategoryRsp {
    return {
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => RoomCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomCategoryRsp>, I>>(base?: I): ListRoomCategoryRsp {
    return ListRoomCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomCategoryRsp>, I>>(object: I): ListRoomCategoryRsp {
    const message = createBaseListRoomCategoryRsp();
    message.categories = object.categories?.map(e => RoomCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListRoomCardReq(): ListRoomCardReq {
  return { page: undefined, category_id: 0, sub_category_id: '', tab: 0 };
}

export const ListRoomCardReq: MessageFns<ListRoomCardReq> = {
  fromJSON(object: any): ListRoomCardReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.String(object.sub_category_id) : '',
      tab: isSet(object.tab) ? themeCategoryTabFromJSON(object.tab) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRoomCardReq>, I>>(base?: I): ListRoomCardReq {
    return ListRoomCardReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomCardReq>, I>>(object: I): ListRoomCardReq {
    const message = createBaseListRoomCardReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? '';
    message.tab = object.tab ?? 0;
    return message;
  }
};

function createBaseListRoomCardRsp(): ListRoomCardRsp {
  return { page: undefined, room_infos: [] };
}

export const ListRoomCardRsp: MessageFns<ListRoomCardRsp> = {
  fromJSON(object: any): ListRoomCardRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_infos: globalThis.Array.isArray(object?.room_infos)
        ? object.room_infos.map((e: any) => RoomInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomCardRsp>, I>>(base?: I): ListRoomCardRsp {
    return ListRoomCardRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomCardRsp>, I>>(object: I): ListRoomCardRsp {
    const message = createBaseListRoomCardRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_infos = object.room_infos?.map(e => RoomInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListRoomHotTopicReq(): ListRoomHotTopicReq {
  return {};
}

export const ListRoomHotTopicReq: MessageFns<ListRoomHotTopicReq> = {
  fromJSON(_: any): ListRoomHotTopicReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListRoomHotTopicReq>, I>>(base?: I): ListRoomHotTopicReq {
    return ListRoomHotTopicReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomHotTopicReq>, I>>(_: I): ListRoomHotTopicReq {
    const message = createBaseListRoomHotTopicReq();
    return message;
  }
};

function createBaseListRoomHotTopicRsp(): ListRoomHotTopicRsp {
  return { topic_list: [] };
}

export const ListRoomHotTopicRsp: MessageFns<ListRoomHotTopicRsp> = {
  fromJSON(object: any): ListRoomHotTopicRsp {
    return {
      topic_list: globalThis.Array.isArray(object?.topic_list)
        ? object.topic_list.map((e: any) => HotTopicInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomHotTopicRsp>, I>>(base?: I): ListRoomHotTopicRsp {
    return ListRoomHotTopicRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomHotTopicRsp>, I>>(object: I): ListRoomHotTopicRsp {
    const message = createBaseListRoomHotTopicRsp();
    message.topic_list = object.topic_list?.map(e => HotTopicInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseHotTopicInfo(): HotTopicInfo {
  return { icon: '', name: '', room_ids: [], color: [], id: 0, mic_icon: '' };
}

export const HotTopicInfo: MessageFns<HotTopicInfo> = {
  fromJSON(object: any): HotTopicInfo {
    return {
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : [],
      color: globalThis.Array.isArray(object?.color) ? object.color.map((e: any) => globalThis.String(e)) : [],
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      mic_icon: isSet(object.mic_icon) ? globalThis.String(object.mic_icon) : ''
    };
  },

  create<I extends Exact<DeepPartial<HotTopicInfo>, I>>(base?: I): HotTopicInfo {
    return HotTopicInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HotTopicInfo>, I>>(object: I): HotTopicInfo {
    const message = createBaseHotTopicInfo();
    message.icon = object.icon ?? '';
    message.name = object.name ?? '';
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.color = object.color?.map(e => e) || [];
    message.id = object.id ?? 0;
    message.mic_icon = object.mic_icon ?? '';
    return message;
  }
};

/** 房间列表 */
export type RoomListDefinition = typeof RoomListDefinition;
export const RoomListDefinition = {
  name: 'RoomList',
  fullName: 'roomlist.RoomList',
  methods: {
    /** 获取房间分类列表 */
    listRoomCategory: {
      name: 'ListRoomCategory',
      requestType: ListRoomCategoryReq,
      requestStream: false,
      responseType: ListRoomCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取房间列表 */
    listRoomCard: {
      name: 'ListRoomCard',
      requestType: ListRoomCardReq,
      requestStream: false,
      responseType: ListRoomCardRsp,
      responseStream: false,
      options: {}
    },
    /** 获取热门话题 */
    listRoomHotTopic: {
      name: 'ListRoomHotTopic',
      requestType: ListRoomHotTopicReq,
      requestStream: false,
      responseType: ListRoomHotTopicRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
