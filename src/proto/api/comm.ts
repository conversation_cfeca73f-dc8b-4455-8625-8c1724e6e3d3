// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/comm.proto

/* eslint-disable */
import { UsingPrivilege } from './privilege';
import { UserInfo } from './user';

export const protobufPackage = 'roomcomm';

/** 房间权限 */
export enum RoomPerm {
  /** ROOM_PERM_NONE - 默认值，无意义 */
  ROOM_PERM_NONE = 0,
  /** ROOM_PERM_PUBLIC - 开放 */
  ROOM_PERM_PUBLIC = 10,
  /** ROOM_PERM_PRIVATE - 私密 */
  ROOM_PERM_PRIVATE = 20,
  /** ROOM_PERM_PASSWORD - 密码 */
  ROOM_PERM_PASSWORD = 30,
  UNRECOGNIZED = -1
}

export function roomPermFromJSON(object: any): RoomPerm {
  switch (object) {
    case 0:
    case 'ROOM_PERM_NONE':
      return RoomPerm.ROOM_PERM_NONE;
    case 10:
    case 'ROOM_PERM_PUBLIC':
      return RoomPerm.ROOM_PERM_PUBLIC;
    case 20:
    case 'ROOM_PERM_PRIVATE':
      return RoomPerm.ROOM_PERM_PRIVATE;
    case 30:
    case 'ROOM_PERM_PASSWORD':
      return RoomPerm.ROOM_PERM_PASSWORD;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomPerm.UNRECOGNIZED;
  }
}

/** 上麦权限 */
export enum SeatPerm {
  /** SEAT_PERM_NONE - 默认值，无意义 */
  SEAT_PERM_NONE = 0,
  /** SEAT_PERM_ALL - 自由上麦 */
  SEAT_PERM_ALL = 10,
  /** SEAT_PERM_APPLY - 需申请上麦 */
  SEAT_PERM_APPLY = 20,
  UNRECOGNIZED = -1
}

export function seatPermFromJSON(object: any): SeatPerm {
  switch (object) {
    case 0:
    case 'SEAT_PERM_NONE':
      return SeatPerm.SEAT_PERM_NONE;
    case 10:
    case 'SEAT_PERM_ALL':
      return SeatPerm.SEAT_PERM_ALL;
    case 20:
    case 'SEAT_PERM_APPLY':
      return SeatPerm.SEAT_PERM_APPLY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SeatPerm.UNRECOGNIZED;
  }
}

/** 麦位积分开关 */
export enum SeatScoreSwitch {
  /** SEAT_SCORE_SWITCH - 默认值 无意义 */
  SEAT_SCORE_SWITCH = 0,
  /** SEAT_SCORE_CLOSED - 关闭 */
  SEAT_SCORE_CLOSED = 1,
  /** SEAT_SCORE_OPENED - 开启 */
  SEAT_SCORE_OPENED = 2,
  UNRECOGNIZED = -1
}

export function seatScoreSwitchFromJSON(object: any): SeatScoreSwitch {
  switch (object) {
    case 0:
    case 'SEAT_SCORE_SWITCH':
      return SeatScoreSwitch.SEAT_SCORE_SWITCH;
    case 1:
    case 'SEAT_SCORE_CLOSED':
      return SeatScoreSwitch.SEAT_SCORE_CLOSED;
    case 2:
    case 'SEAT_SCORE_OPENED':
      return SeatScoreSwitch.SEAT_SCORE_OPENED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SeatScoreSwitch.UNRECOGNIZED;
  }
}

/** 账号状态 */
export enum UserStatus {
  /** USER_STATUS_NONE - 无状态 */
  USER_STATUS_NONE = 0,
  /** USER_STATUS_NORMAL - 正常 */
  USER_STATUS_NORMAL = 1,
  /** USER_STATUS_BAN - 封禁 */
  USER_STATUS_BAN = 2,
  /** USER_STATUS_FROZEN - 冻结 */
  USER_STATUS_FROZEN = 3,
  /** USER_STATUS_WITHDRAW - 注销（注销冷静期过后更新为删除） */
  USER_STATUS_WITHDRAW = 4,
  /** USER_STATUS_DELETE - 删除 */
  USER_STATUS_DELETE = 5,
  UNRECOGNIZED = -1
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case 'USER_STATUS_NONE':
      return UserStatus.USER_STATUS_NONE;
    case 1:
    case 'USER_STATUS_NORMAL':
      return UserStatus.USER_STATUS_NORMAL;
    case 2:
    case 'USER_STATUS_BAN':
      return UserStatus.USER_STATUS_BAN;
    case 3:
    case 'USER_STATUS_FROZEN':
      return UserStatus.USER_STATUS_FROZEN;
    case 4:
    case 'USER_STATUS_WITHDRAW':
      return UserStatus.USER_STATUS_WITHDRAW;
    case 5:
    case 'USER_STATUS_DELETE':
      return UserStatus.USER_STATUS_DELETE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

/** 房间用户角色 */
export enum RoomUserRole {
  ROOM_USER_ROLE_NONE = 0,
  /** ROOM_USER_ROLE_OWNER - 房主 */
  ROOM_USER_ROLE_OWNER = 1,
  /** ROOM_USER_ROLE_VIEWER - 观众 */
  ROOM_USER_ROLE_VIEWER = 2,
  /** ROOM_USER_ROLE_ADMIN - 管理员 */
  ROOM_USER_ROLE_ADMIN = 3,
  UNRECOGNIZED = -1
}

export function roomUserRoleFromJSON(object: any): RoomUserRole {
  switch (object) {
    case 0:
    case 'ROOM_USER_ROLE_NONE':
      return RoomUserRole.ROOM_USER_ROLE_NONE;
    case 1:
    case 'ROOM_USER_ROLE_OWNER':
      return RoomUserRole.ROOM_USER_ROLE_OWNER;
    case 2:
    case 'ROOM_USER_ROLE_VIEWER':
      return RoomUserRole.ROOM_USER_ROLE_VIEWER;
    case 3:
    case 'ROOM_USER_ROLE_ADMIN':
      return RoomUserRole.ROOM_USER_ROLE_ADMIN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomUserRole.UNRECOGNIZED;
  }
}

/** rtc供应商 */
export enum RtcProvider {
  RTC_PROVIDER_NONE = 0,
  /** RTC_PROVIDER_AGORA - 声网 */
  RTC_PROVIDER_AGORA = 1,
  /** RTC_PROVIDER_DEMO - demo，虚拟测试供应商 */
  RTC_PROVIDER_DEMO = 50,
  UNRECOGNIZED = -1
}

export function rtcProviderFromJSON(object: any): RtcProvider {
  switch (object) {
    case 0:
    case 'RTC_PROVIDER_NONE':
      return RtcProvider.RTC_PROVIDER_NONE;
    case 1:
    case 'RTC_PROVIDER_AGORA':
      return RtcProvider.RTC_PROVIDER_AGORA;
    case 50:
    case 'RTC_PROVIDER_DEMO':
      return RtcProvider.RTC_PROVIDER_DEMO;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RtcProvider.UNRECOGNIZED;
  }
}

/** rtm供应商 */
export enum RtmProvider {
  RTM_PROVIDER_NONE = 0,
  /** RTM_PROVIDER_AGORA - 声网 */
  RTM_PROVIDER_AGORA = 1,
  /** RTM_PROVIDER_DEMO - demo，虚拟测试供应商 */
  RTM_PROVIDER_DEMO = 50,
  UNRECOGNIZED = -1
}

export function rtmProviderFromJSON(object: any): RtmProvider {
  switch (object) {
    case 0:
    case 'RTM_PROVIDER_NONE':
      return RtmProvider.RTM_PROVIDER_NONE;
    case 1:
    case 'RTM_PROVIDER_AGORA':
      return RtmProvider.RTM_PROVIDER_AGORA;
    case 50:
    case 'RTM_PROVIDER_DEMO':
      return RtmProvider.RTM_PROVIDER_DEMO;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RtmProvider.UNRECOGNIZED;
  }
}

/** 开播状态 */
export enum LiveStatus {
  LIVE_STATUS_NONE = 0,
  /** LIVE_STATUS_LIVING - 直播中 */
  LIVE_STATUS_LIVING = 1,
  /** LIVE_STATUS_CLOSED - 已关闭 */
  LIVE_STATUS_CLOSED = 2,
  /** LIVE_STATUS_LEAVE - 暂时离开 */
  LIVE_STATUS_LEAVE = 3,
  UNRECOGNIZED = -1
}

export function liveStatusFromJSON(object: any): LiveStatus {
  switch (object) {
    case 0:
    case 'LIVE_STATUS_NONE':
      return LiveStatus.LIVE_STATUS_NONE;
    case 1:
    case 'LIVE_STATUS_LIVING':
      return LiveStatus.LIVE_STATUS_LIVING;
    case 2:
    case 'LIVE_STATUS_CLOSED':
      return LiveStatus.LIVE_STATUS_CLOSED;
    case 3:
    case 'LIVE_STATUS_LEAVE':
      return LiveStatus.LIVE_STATUS_LEAVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LiveStatus.UNRECOGNIZED;
  }
}

/** 房间状态 */
export enum RoomStatus {
  ROOM_STATUS_NONE = 0,
  /** ROOM_STATUS_NORMAL - 正常 */
  ROOM_STATUS_NORMAL = 1,
  /** ROOM_STATUS_BAN - 封禁 */
  ROOM_STATUS_BAN = 2,
  UNRECOGNIZED = -1
}

export function roomStatusFromJSON(object: any): RoomStatus {
  switch (object) {
    case 0:
    case 'ROOM_STATUS_NONE':
      return RoomStatus.ROOM_STATUS_NONE;
    case 1:
    case 'ROOM_STATUS_NORMAL':
      return RoomStatus.ROOM_STATUS_NORMAL;
    case 2:
    case 'ROOM_STATUS_BAN':
      return RoomStatus.ROOM_STATUS_BAN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomStatus.UNRECOGNIZED;
  }
}

/** 房间大类 */
export enum RoomCategory {
  ROOM_CATEGORY_NONE = 0,
  /** ROOM_CATEGORY_CHAT - 语聊 */
  ROOM_CATEGORY_CHAT = 1,
  /** ROOM_CATEGORY_LIVE - 直播 */
  ROOM_CATEGORY_LIVE = 2,
  UNRECOGNIZED = -1
}

export function roomCategoryFromJSON(object: any): RoomCategory {
  switch (object) {
    case 0:
    case 'ROOM_CATEGORY_NONE':
      return RoomCategory.ROOM_CATEGORY_NONE;
    case 1:
    case 'ROOM_CATEGORY_CHAT':
      return RoomCategory.ROOM_CATEGORY_CHAT;
    case 2:
    case 'ROOM_CATEGORY_LIVE':
      return RoomCategory.ROOM_CATEGORY_LIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomCategory.UNRECOGNIZED;
  }
}

/** 房间类型 */
export enum RoomType {
  ROOM_TYPE_NONE = 0,
  /** ROOM_TYPE_NORMAL - 普通房间 */
  ROOM_TYPE_NORMAL = 1,
  /** ROOM_TYPE_OFFICIAL - 官方房间 */
  ROOM_TYPE_OFFICIAL = 2,
  /** ROOM_TYPE_TEST - 内部测试房间，不对外显示，只允许内部账号进入 */
  ROOM_TYPE_TEST = 3,
  UNRECOGNIZED = -1
}

export function roomTypeFromJSON(object: any): RoomType {
  switch (object) {
    case 0:
    case 'ROOM_TYPE_NONE':
      return RoomType.ROOM_TYPE_NONE;
    case 1:
    case 'ROOM_TYPE_NORMAL':
      return RoomType.ROOM_TYPE_NORMAL;
    case 2:
    case 'ROOM_TYPE_OFFICIAL':
      return RoomType.ROOM_TYPE_OFFICIAL;
    case 3:
    case 'ROOM_TYPE_TEST':
      return RoomType.ROOM_TYPE_TEST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomType.UNRECOGNIZED;
  }
}

/** 房间标签模式 */
export enum RoomTagStyle {
  ROOM_TAG_TYPE_NONE = 0,
  /** ROOM_TAG_TYPE_ICON - 仅展示图标 */
  ROOM_TAG_TYPE_ICON = 10,
  /** ROOM_TAG_TYPE_ICON_NAME - 展示图标 + 名字 */
  ROOM_TAG_TYPE_ICON_NAME = 20,
  UNRECOGNIZED = -1
}

export function roomTagStyleFromJSON(object: any): RoomTagStyle {
  switch (object) {
    case 0:
    case 'ROOM_TAG_TYPE_NONE':
      return RoomTagStyle.ROOM_TAG_TYPE_NONE;
    case 10:
    case 'ROOM_TAG_TYPE_ICON':
      return RoomTagStyle.ROOM_TAG_TYPE_ICON;
    case 20:
    case 'ROOM_TAG_TYPE_ICON_NAME':
      return RoomTagStyle.ROOM_TAG_TYPE_ICON_NAME;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomTagStyle.UNRECOGNIZED;
  }
}

/** 房间模式上架-默认选中 */
export enum RoomModePublishDefaultSelected {
  ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NONE = 0,
  /** ROOM_MODE_PUBLISH_DEFAULT_SELECTED_YES - 是 */
  ROOM_MODE_PUBLISH_DEFAULT_SELECTED_YES = 1,
  /** ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NO - 否 */
  ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NO = 2,
  UNRECOGNIZED = -1
}

export function roomModePublishDefaultSelectedFromJSON(object: any): RoomModePublishDefaultSelected {
  switch (object) {
    case 0:
    case 'ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NONE':
      return RoomModePublishDefaultSelected.ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NONE;
    case 1:
    case 'ROOM_MODE_PUBLISH_DEFAULT_SELECTED_YES':
      return RoomModePublishDefaultSelected.ROOM_MODE_PUBLISH_DEFAULT_SELECTED_YES;
    case 2:
    case 'ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NO':
      return RoomModePublishDefaultSelected.ROOM_MODE_PUBLISH_DEFAULT_SELECTED_NO;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomModePublishDefaultSelected.UNRECOGNIZED;
  }
}

/** RoomDetail 房间详细信息 */
export interface RoomDetail {
  /** 房间信息 */
  room_info: RoomInfo | undefined;
  /** 房间等级 */
  room_level: RoomLevel | undefined;
  /** 房间配置 */
  room_settings: RoomSettings | undefined;
}

/** 房间信息 :selector */
export interface RoomInfo {
  /** 房间ID */
  room_id: number;
  /** 房间靓号 */
  show_id: string;
  /** 房间 owner UID */
  owner_uid: number;
  /** 房间类型 */
  room_type: RoomType;
  /** 房间名称 */
  name: string;
  /** 房间公告 */
  intro: string;
  /** 房间封面 */
  cover: string;
  /** 静态背景图 */
  bg_img: string;
  /** 动态背景图 */
  bg_img_svga: string;
  /** 创建时间 */
  create_at: number;
  /** 房间标签 */
  tags: RoomTag[];
  /** 直播状态 */
  live_status: LiveStatus;
  /** 房间状态 */
  status: RoomStatus;
  /** 在线人数 */
  online_count: number;
  /** 房间大类 */
  category: RoomCategory;
  is_audit: boolean;
  /** 房间特权 */
  privilege: UsingPrivilege | undefined;
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 直播间信息字段 */
  live_info: LiveRoomInfo | undefined;
  /** 语聊房额外信息 */
  extra_info: RoomExtraInfo | undefined;
}

export interface RoomExtraInfo {
  /** 国家编码 比如印度是IN */
  cou: string;
  /** 国旗地址 */
  country_url: string;
  /** 房间在线用户头像 */
  avatar_list: string[];
  /** 热门主题信息 */
  topic_info: EnterHotTopicInfo | undefined;
}

export interface EnterHotTopicInfo {
  /** 主题icon */
  icon: string;
  /** 主题名称 */
  name: string;
  /** 颜色 */
  color: string[];
  /** 主题唯一id */
  id: number;
  /** 麦位图标 */
  mic_icon: string;
}

export interface LiveRoomInfo {
  /** 房主头像 */
  avatar: string;
  /** 昵称 */
  nickname: string;
  /** 国旗字段 */
  country_url: string;
  /** 年龄 */
  age: number;
  /** 是否忙碌 */
  is_busy: boolean;
  /** 性别 */
  sex: string;
  /** 直播场次id */
  live_id: number;
}

/** RoomLevel 房间等级 */
export interface RoomLevel {
  /** 房间ID */
  room_id: number;
  /** 等级 */
  level: number;
  /** 经验值 */
  exp: number;
  /** 时间戳 毫秒 */
  timestamp: number;
}

/** RoomSettings 房间设置 */
export interface RoomSettings {
  /** 房间ID */
  room_id: number;
  /** 房间布局 */
  layout: RoomLayout | undefined;
  /** 房间权限 */
  room_perm: RoomPerm;
  /** 上麦权限 */
  seat_perm: SeatPerm;
  /** 房管人数上限 */
  AdminCount: number;
  /** 麦位积分开关 */
  seat_score_switch: SeatScoreSwitch;
  /** 时间戳 毫秒 */
  timestamp: number;
}

/** 房间内用户信息 :selector */
export interface RoomUserInfo {
  /** 用户角色 */
  role: RoomUserRole;
  /** 用户信息 */
  user_info: UserInfo | undefined;
  /** 是否关注 */
  follow_status: boolean;
  /** 是否被禁言 */
  is_mute_msg: boolean;
}

/** 房间布局模式 */
export interface RoomLayout {
  /**
   * code
   *  - chat_10: chat 10 mic
   *  - partner_9: partner 9 mic
   *  - partner_10: partner 10 mic
   *  - cp: cp 2 mic
   */
  code: string;
  /** 布局模式名称 */
  name: string;
  /** 布局模式名称，多语言 */
  name_lan: { [key: string]: string };
  /** 预览示意图 */
  pre_img: string;
}

export interface RoomLayout_NameLanEntry {
  key: string;
  value: string;
}

/** 房间标签 */
export interface RoomTag {
  /** 标签 */
  id: number;
  /** 房间标签模式 */
  style: RoomTagStyle;
  /** 标签权重, 如果一个房间有多个标签时按权重从高到低排序和剔除显示不下的. */
  weight: number;
  /** 标签名称内容 */
  name: string;
  /** 标签图标 */
  icon: string;
  /** 名字颜色 */
  name_color: string;
}

/** 房间的实时状态信息 */
export interface RoomStateInfo {
  /** 版本,时间戳,毫秒 */
  timestamp: number;
  /** 在线人数 */
  online_count: number;
  /** 麦位列表 */
  seat_list: SeatList | undefined;
  /** 用户麦位积分 */
  user_seat_score_list: UserScoreList | undefined;
  /** 房间模式 */
  room_mode_info: RoomModeInfo | undefined;
}

/** 麦位信息 */
export interface SeatInfo {
  /** 麦位序号，从1开始 */
  seat_index: number;
  /** 更新时间 */
  time: number;
  /** 用户id */
  room_user_info: RoomUserInfo | undefined;
  /** 麦位是否锁定,默认:false */
  seat_locked: boolean;
  /** 麦位是否禁音,默认:false */
  seat_muted: boolean;
  /** 用户麦克风是否禁用,默认:false,uid 非0有效 */
  user_mic_disabled: boolean;
  /** 麦克风是否禁用，mic_disabled=seat_muted||user_mic_disabled */
  mic_disabled: boolean;
}

/** 麦位列表 */
export interface SeatList {
  /** 版本,值递增 */
  timestamp: number;
  /** 麦位列表 */
  seat_infos: SeatInfo[];
  /** 房间布局模式code */
  layout_code: string;
}

/** 用户麦位积分 */
export interface UserScoreInfo {
  /** 用户Id */
  uid: number;
  /** 积分 */
  score: number;
}

/** 用户麦位积分列表 */
export interface UserScoreList {
  /** 版本,值递增 */
  timestamp: number;
  /** 用户数量列表 */
  user_score_infos: UserScoreInfo[];
}

/** 多语言国际化 */
export interface I18n {
  /** 语言code;e.g., "en", "zh", "fr" */
  lang_code: string;
  /** 文本内容 */
  text: string;
}

/** 房间模式 */
export interface RoomModeInfo {
  /** 房间模式上架id */
  room_mode_publish_id: number;
  /** 房间模式配置id */
  room_mode_config_id: number;
  /** 名称(缺省名称) */
  name: string;
  /** 名称-多语言 */
  name_i18n: { [key: string]: string };
  /** 图标(缺省图标) */
  icon: string;
  /** 图标-多语言 */
  icon_i18n: { [key: string]: string };
  /** 房间背景图地址 */
  room_bg_img_url: string;
  /** 房间背景图类型 */
  room_bg_img_type: string;
  /** 玩法地址 */
  play_url: string;
  /** 房间模式code;eg:Chat、Friend、Music */
  room_mode_code: string;
  /** 麦位布局code;eg:chat_10,partner_9,partner_10,cp */
  room_layout_code: string;
  /** 玩法code;eg:ludo、桌球、狼人杀 */
  play_code: string;
}

export interface RoomModeInfo_NameI18nEntry {
  key: string;
  value: string;
}

export interface RoomModeInfo_IconI18nEntry {
  key: string;
  value: string;
}

function createBaseRoomDetail(): RoomDetail {
  return { room_info: undefined, room_level: undefined, room_settings: undefined };
}

export const RoomDetail: MessageFns<RoomDetail> = {
  fromJSON(object: any): RoomDetail {
    return {
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined,
      room_level: isSet(object.room_level) ? RoomLevel.fromJSON(object.room_level) : undefined,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomDetail>, I>>(base?: I): RoomDetail {
    return RoomDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomDetail>, I>>(object: I): RoomDetail {
    const message = createBaseRoomDetail();
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    message.room_level =
      object.room_level !== undefined && object.room_level !== null
        ? RoomLevel.fromPartial(object.room_level)
        : undefined;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    return message;
  }
};

function createBaseRoomInfo(): RoomInfo {
  return {
    room_id: 0,
    show_id: '',
    owner_uid: 0,
    room_type: 0,
    name: '',
    intro: '',
    cover: '',
    bg_img: '',
    bg_img_svga: '',
    create_at: 0,
    tags: [],
    live_status: 0,
    status: 0,
    online_count: 0,
    category: 0,
    is_audit: false,
    privilege: undefined,
    timestamp: 0,
    live_info: undefined,
    extra_info: undefined
  };
}

export const RoomInfo: MessageFns<RoomInfo> = {
  fromJSON(object: any): RoomInfo {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : '',
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      room_type: isSet(object.room_type) ? roomTypeFromJSON(object.room_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      intro: isSet(object.intro) ? globalThis.String(object.intro) : '',
      cover: isSet(object.cover) ? globalThis.String(object.cover) : '',
      bg_img: isSet(object.bg_img) ? globalThis.String(object.bg_img) : '',
      bg_img_svga: isSet(object.bg_img_svga) ? globalThis.String(object.bg_img_svga) : '',
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => RoomTag.fromJSON(e)) : [],
      live_status: isSet(object.live_status) ? liveStatusFromJSON(object.live_status) : 0,
      status: isSet(object.status) ? roomStatusFromJSON(object.status) : 0,
      online_count: isSet(object.online_count) ? globalThis.Number(object.online_count) : 0,
      category: isSet(object.category) ? roomCategoryFromJSON(object.category) : 0,
      is_audit: isSet(object.is_audit) ? globalThis.Boolean(object.is_audit) : false,
      privilege: isSet(object.privilege) ? UsingPrivilege.fromJSON(object.privilege) : undefined,
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      live_info: isSet(object.live_info) ? LiveRoomInfo.fromJSON(object.live_info) : undefined,
      extra_info: isSet(object.extra_info) ? RoomExtraInfo.fromJSON(object.extra_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomInfo>, I>>(base?: I): RoomInfo {
    return RoomInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomInfo>, I>>(object: I): RoomInfo {
    const message = createBaseRoomInfo();
    message.room_id = object.room_id ?? 0;
    message.show_id = object.show_id ?? '';
    message.owner_uid = object.owner_uid ?? 0;
    message.room_type = object.room_type ?? 0;
    message.name = object.name ?? '';
    message.intro = object.intro ?? '';
    message.cover = object.cover ?? '';
    message.bg_img = object.bg_img ?? '';
    message.bg_img_svga = object.bg_img_svga ?? '';
    message.create_at = object.create_at ?? 0;
    message.tags = object.tags?.map(e => RoomTag.fromPartial(e)) || [];
    message.live_status = object.live_status ?? 0;
    message.status = object.status ?? 0;
    message.online_count = object.online_count ?? 0;
    message.category = object.category ?? 0;
    message.is_audit = object.is_audit ?? false;
    message.privilege =
      object.privilege !== undefined && object.privilege !== null
        ? UsingPrivilege.fromPartial(object.privilege)
        : undefined;
    message.timestamp = object.timestamp ?? 0;
    message.live_info =
      object.live_info !== undefined && object.live_info !== null
        ? LiveRoomInfo.fromPartial(object.live_info)
        : undefined;
    message.extra_info =
      object.extra_info !== undefined && object.extra_info !== null
        ? RoomExtraInfo.fromPartial(object.extra_info)
        : undefined;
    return message;
  }
};

function createBaseRoomExtraInfo(): RoomExtraInfo {
  return { cou: '', country_url: '', avatar_list: [], topic_info: undefined };
}

export const RoomExtraInfo: MessageFns<RoomExtraInfo> = {
  fromJSON(object: any): RoomExtraInfo {
    return {
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      country_url: isSet(object.country_url) ? globalThis.String(object.country_url) : '',
      avatar_list: globalThis.Array.isArray(object?.avatar_list)
        ? object.avatar_list.map((e: any) => globalThis.String(e))
        : [],
      topic_info: isSet(object.topic_info) ? EnterHotTopicInfo.fromJSON(object.topic_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomExtraInfo>, I>>(base?: I): RoomExtraInfo {
    return RoomExtraInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomExtraInfo>, I>>(object: I): RoomExtraInfo {
    const message = createBaseRoomExtraInfo();
    message.cou = object.cou ?? '';
    message.country_url = object.country_url ?? '';
    message.avatar_list = object.avatar_list?.map(e => e) || [];
    message.topic_info =
      object.topic_info !== undefined && object.topic_info !== null
        ? EnterHotTopicInfo.fromPartial(object.topic_info)
        : undefined;
    return message;
  }
};

function createBaseEnterHotTopicInfo(): EnterHotTopicInfo {
  return { icon: '', name: '', color: [], id: 0, mic_icon: '' };
}

export const EnterHotTopicInfo: MessageFns<EnterHotTopicInfo> = {
  fromJSON(object: any): EnterHotTopicInfo {
    return {
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      color: globalThis.Array.isArray(object?.color) ? object.color.map((e: any) => globalThis.String(e)) : [],
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      mic_icon: isSet(object.mic_icon) ? globalThis.String(object.mic_icon) : ''
    };
  },

  create<I extends Exact<DeepPartial<EnterHotTopicInfo>, I>>(base?: I): EnterHotTopicInfo {
    return EnterHotTopicInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterHotTopicInfo>, I>>(object: I): EnterHotTopicInfo {
    const message = createBaseEnterHotTopicInfo();
    message.icon = object.icon ?? '';
    message.name = object.name ?? '';
    message.color = object.color?.map(e => e) || [];
    message.id = object.id ?? 0;
    message.mic_icon = object.mic_icon ?? '';
    return message;
  }
};

function createBaseLiveRoomInfo(): LiveRoomInfo {
  return { avatar: '', nickname: '', country_url: '', age: 0, is_busy: false, sex: '', live_id: 0 };
}

export const LiveRoomInfo: MessageFns<LiveRoomInfo> = {
  fromJSON(object: any): LiveRoomInfo {
    return {
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      country_url: isSet(object.country_url) ? globalThis.String(object.country_url) : '',
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
      is_busy: isSet(object.is_busy) ? globalThis.Boolean(object.is_busy) : false,
      sex: isSet(object.sex) ? globalThis.String(object.sex) : '',
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<LiveRoomInfo>, I>>(base?: I): LiveRoomInfo {
    return LiveRoomInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LiveRoomInfo>, I>>(object: I): LiveRoomInfo {
    const message = createBaseLiveRoomInfo();
    message.avatar = object.avatar ?? '';
    message.nickname = object.nickname ?? '';
    message.country_url = object.country_url ?? '';
    message.age = object.age ?? 0;
    message.is_busy = object.is_busy ?? false;
    message.sex = object.sex ?? '';
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseRoomLevel(): RoomLevel {
  return { room_id: 0, level: 0, exp: 0, timestamp: 0 };
}

export const RoomLevel: MessageFns<RoomLevel> = {
  fromJSON(object: any): RoomLevel {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      exp: isSet(object.exp) ? globalThis.Number(object.exp) : 0,
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomLevel>, I>>(base?: I): RoomLevel {
    return RoomLevel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomLevel>, I>>(object: I): RoomLevel {
    const message = createBaseRoomLevel();
    message.room_id = object.room_id ?? 0;
    message.level = object.level ?? 0;
    message.exp = object.exp ?? 0;
    message.timestamp = object.timestamp ?? 0;
    return message;
  }
};

function createBaseRoomSettings(): RoomSettings {
  return {
    room_id: 0,
    layout: undefined,
    room_perm: 0,
    seat_perm: 0,
    AdminCount: 0,
    seat_score_switch: 0,
    timestamp: 0
  };
}

export const RoomSettings: MessageFns<RoomSettings> = {
  fromJSON(object: any): RoomSettings {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      layout: isSet(object.layout) ? RoomLayout.fromJSON(object.layout) : undefined,
      room_perm: isSet(object.room_perm) ? roomPermFromJSON(object.room_perm) : 0,
      seat_perm: isSet(object.seat_perm) ? seatPermFromJSON(object.seat_perm) : 0,
      AdminCount: isSet(object.AdminCount) ? globalThis.Number(object.AdminCount) : 0,
      seat_score_switch: isSet(object.seat_score_switch) ? seatScoreSwitchFromJSON(object.seat_score_switch) : 0,
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomSettings>, I>>(base?: I): RoomSettings {
    return RoomSettings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomSettings>, I>>(object: I): RoomSettings {
    const message = createBaseRoomSettings();
    message.room_id = object.room_id ?? 0;
    message.layout =
      object.layout !== undefined && object.layout !== null ? RoomLayout.fromPartial(object.layout) : undefined;
    message.room_perm = object.room_perm ?? 0;
    message.seat_perm = object.seat_perm ?? 0;
    message.AdminCount = object.AdminCount ?? 0;
    message.seat_score_switch = object.seat_score_switch ?? 0;
    message.timestamp = object.timestamp ?? 0;
    return message;
  }
};

function createBaseRoomUserInfo(): RoomUserInfo {
  return { role: 0, user_info: undefined, follow_status: false, is_mute_msg: false };
}

export const RoomUserInfo: MessageFns<RoomUserInfo> = {
  fromJSON(object: any): RoomUserInfo {
    return {
      role: isSet(object.role) ? roomUserRoleFromJSON(object.role) : 0,
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      follow_status: isSet(object.follow_status) ? globalThis.Boolean(object.follow_status) : false,
      is_mute_msg: isSet(object.is_mute_msg) ? globalThis.Boolean(object.is_mute_msg) : false
    };
  },

  create<I extends Exact<DeepPartial<RoomUserInfo>, I>>(base?: I): RoomUserInfo {
    return RoomUserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomUserInfo>, I>>(object: I): RoomUserInfo {
    const message = createBaseRoomUserInfo();
    message.role = object.role ?? 0;
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.follow_status = object.follow_status ?? false;
    message.is_mute_msg = object.is_mute_msg ?? false;
    return message;
  }
};

function createBaseRoomLayout(): RoomLayout {
  return { code: '', name: '', name_lan: {}, pre_img: '' };
}

export const RoomLayout: MessageFns<RoomLayout> = {
  fromJSON(object: any): RoomLayout {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_lan: isObject(object.name_lan)
        ? Object.entries(object.name_lan).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      pre_img: isSet(object.pre_img) ? globalThis.String(object.pre_img) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomLayout>, I>>(base?: I): RoomLayout {
    return RoomLayout.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomLayout>, I>>(object: I): RoomLayout {
    const message = createBaseRoomLayout();
    message.code = object.code ?? '';
    message.name = object.name ?? '';
    message.name_lan = Object.entries(object.name_lan ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.pre_img = object.pre_img ?? '';
    return message;
  }
};

function createBaseRoomLayout_NameLanEntry(): RoomLayout_NameLanEntry {
  return { key: '', value: '' };
}

export const RoomLayout_NameLanEntry: MessageFns<RoomLayout_NameLanEntry> = {
  fromJSON(object: any): RoomLayout_NameLanEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomLayout_NameLanEntry>, I>>(base?: I): RoomLayout_NameLanEntry {
    return RoomLayout_NameLanEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomLayout_NameLanEntry>, I>>(object: I): RoomLayout_NameLanEntry {
    const message = createBaseRoomLayout_NameLanEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRoomTag(): RoomTag {
  return { id: 0, style: 0, weight: 0, name: '', icon: '', name_color: '' };
}

export const RoomTag: MessageFns<RoomTag> = {
  fromJSON(object: any): RoomTag {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      style: isSet(object.style) ? roomTagStyleFromJSON(object.style) : 0,
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      name_color: isSet(object.name_color) ? globalThis.String(object.name_color) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTag>, I>>(base?: I): RoomTag {
    return RoomTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTag>, I>>(object: I): RoomTag {
    const message = createBaseRoomTag();
    message.id = object.id ?? 0;
    message.style = object.style ?? 0;
    message.weight = object.weight ?? 0;
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    message.name_color = object.name_color ?? '';
    return message;
  }
};

function createBaseRoomStateInfo(): RoomStateInfo {
  return {
    timestamp: 0,
    online_count: 0,
    seat_list: undefined,
    user_seat_score_list: undefined,
    room_mode_info: undefined
  };
}

export const RoomStateInfo: MessageFns<RoomStateInfo> = {
  fromJSON(object: any): RoomStateInfo {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      online_count: isSet(object.online_count) ? globalThis.Number(object.online_count) : 0,
      seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined,
      user_seat_score_list: isSet(object.user_seat_score_list)
        ? UserScoreList.fromJSON(object.user_seat_score_list)
        : undefined,
      room_mode_info: isSet(object.room_mode_info) ? RoomModeInfo.fromJSON(object.room_mode_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomStateInfo>, I>>(base?: I): RoomStateInfo {
    return RoomStateInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomStateInfo>, I>>(object: I): RoomStateInfo {
    const message = createBaseRoomStateInfo();
    message.timestamp = object.timestamp ?? 0;
    message.online_count = object.online_count ?? 0;
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    message.user_seat_score_list =
      object.user_seat_score_list !== undefined && object.user_seat_score_list !== null
        ? UserScoreList.fromPartial(object.user_seat_score_list)
        : undefined;
    message.room_mode_info =
      object.room_mode_info !== undefined && object.room_mode_info !== null
        ? RoomModeInfo.fromPartial(object.room_mode_info)
        : undefined;
    return message;
  }
};

function createBaseSeatInfo(): SeatInfo {
  return {
    seat_index: 0,
    time: 0,
    room_user_info: undefined,
    seat_locked: false,
    seat_muted: false,
    user_mic_disabled: false,
    mic_disabled: false
  };
}

export const SeatInfo: MessageFns<SeatInfo> = {
  fromJSON(object: any): SeatInfo {
    return {
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      room_user_info: isSet(object.room_user_info) ? RoomUserInfo.fromJSON(object.room_user_info) : undefined,
      seat_locked: isSet(object.seat_locked) ? globalThis.Boolean(object.seat_locked) : false,
      seat_muted: isSet(object.seat_muted) ? globalThis.Boolean(object.seat_muted) : false,
      user_mic_disabled: isSet(object.user_mic_disabled) ? globalThis.Boolean(object.user_mic_disabled) : false,
      mic_disabled: isSet(object.mic_disabled) ? globalThis.Boolean(object.mic_disabled) : false
    };
  },

  create<I extends Exact<DeepPartial<SeatInfo>, I>>(base?: I): SeatInfo {
    return SeatInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SeatInfo>, I>>(object: I): SeatInfo {
    const message = createBaseSeatInfo();
    message.seat_index = object.seat_index ?? 0;
    message.time = object.time ?? 0;
    message.room_user_info =
      object.room_user_info !== undefined && object.room_user_info !== null
        ? RoomUserInfo.fromPartial(object.room_user_info)
        : undefined;
    message.seat_locked = object.seat_locked ?? false;
    message.seat_muted = object.seat_muted ?? false;
    message.user_mic_disabled = object.user_mic_disabled ?? false;
    message.mic_disabled = object.mic_disabled ?? false;
    return message;
  }
};

function createBaseSeatList(): SeatList {
  return { timestamp: 0, seat_infos: [], layout_code: '' };
}

export const SeatList: MessageFns<SeatList> = {
  fromJSON(object: any): SeatList {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      seat_infos: globalThis.Array.isArray(object?.seat_infos)
        ? object.seat_infos.map((e: any) => SeatInfo.fromJSON(e))
        : [],
      layout_code: isSet(object.layout_code) ? globalThis.String(object.layout_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<SeatList>, I>>(base?: I): SeatList {
    return SeatList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SeatList>, I>>(object: I): SeatList {
    const message = createBaseSeatList();
    message.timestamp = object.timestamp ?? 0;
    message.seat_infos = object.seat_infos?.map(e => SeatInfo.fromPartial(e)) || [];
    message.layout_code = object.layout_code ?? '';
    return message;
  }
};

function createBaseUserScoreInfo(): UserScoreInfo {
  return { uid: 0, score: 0 };
}

export const UserScoreInfo: MessageFns<UserScoreInfo> = {
  fromJSON(object: any): UserScoreInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserScoreInfo>, I>>(base?: I): UserScoreInfo {
    return UserScoreInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserScoreInfo>, I>>(object: I): UserScoreInfo {
    const message = createBaseUserScoreInfo();
    message.uid = object.uid ?? 0;
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseUserScoreList(): UserScoreList {
  return { timestamp: 0, user_score_infos: [] };
}

export const UserScoreList: MessageFns<UserScoreList> = {
  fromJSON(object: any): UserScoreList {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      user_score_infos: globalThis.Array.isArray(object?.user_score_infos)
        ? object.user_score_infos.map((e: any) => UserScoreInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<UserScoreList>, I>>(base?: I): UserScoreList {
    return UserScoreList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserScoreList>, I>>(object: I): UserScoreList {
    const message = createBaseUserScoreList();
    message.timestamp = object.timestamp ?? 0;
    message.user_score_infos = object.user_score_infos?.map(e => UserScoreInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseI18n(): I18n {
  return { lang_code: '', text: '' };
}

export const I18n: MessageFns<I18n> = {
  fromJSON(object: any): I18n {
    return {
      lang_code: isSet(object.lang_code) ? globalThis.String(object.lang_code) : '',
      text: isSet(object.text) ? globalThis.String(object.text) : ''
    };
  },

  create<I extends Exact<DeepPartial<I18n>, I>>(base?: I): I18n {
    return I18n.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<I18n>, I>>(object: I): I18n {
    const message = createBaseI18n();
    message.lang_code = object.lang_code ?? '';
    message.text = object.text ?? '';
    return message;
  }
};

function createBaseRoomModeInfo(): RoomModeInfo {
  return {
    room_mode_publish_id: 0,
    room_mode_config_id: 0,
    name: '',
    name_i18n: {},
    icon: '',
    icon_i18n: {},
    room_bg_img_url: '',
    room_bg_img_type: '',
    play_url: '',
    room_mode_code: '',
    room_layout_code: '',
    play_code: ''
  };
}

export const RoomModeInfo: MessageFns<RoomModeInfo> = {
  fromJSON(object: any): RoomModeInfo {
    return {
      room_mode_publish_id: isSet(object.room_mode_publish_id) ? globalThis.Number(object.room_mode_publish_id) : 0,
      room_mode_config_id: isSet(object.room_mode_config_id) ? globalThis.Number(object.room_mode_config_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_i18n: isObject(object.name_i18n)
        ? Object.entries(object.name_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      icon_i18n: isObject(object.icon_i18n)
        ? Object.entries(object.icon_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      room_bg_img_url: isSet(object.room_bg_img_url) ? globalThis.String(object.room_bg_img_url) : '',
      room_bg_img_type: isSet(object.room_bg_img_type) ? globalThis.String(object.room_bg_img_type) : '',
      play_url: isSet(object.play_url) ? globalThis.String(object.play_url) : '',
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : '',
      play_code: isSet(object.play_code) ? globalThis.String(object.play_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeInfo>, I>>(base?: I): RoomModeInfo {
    return RoomModeInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeInfo>, I>>(object: I): RoomModeInfo {
    const message = createBaseRoomModeInfo();
    message.room_mode_publish_id = object.room_mode_publish_id ?? 0;
    message.room_mode_config_id = object.room_mode_config_id ?? 0;
    message.name = object.name ?? '';
    message.name_i18n = Object.entries(object.name_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.icon = object.icon ?? '';
    message.icon_i18n = Object.entries(object.icon_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.room_bg_img_url = object.room_bg_img_url ?? '';
    message.room_bg_img_type = object.room_bg_img_type ?? '';
    message.play_url = object.play_url ?? '';
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    message.play_code = object.play_code ?? '';
    return message;
  }
};

function createBaseRoomModeInfo_NameI18nEntry(): RoomModeInfo_NameI18nEntry {
  return { key: '', value: '' };
}

export const RoomModeInfo_NameI18nEntry: MessageFns<RoomModeInfo_NameI18nEntry> = {
  fromJSON(object: any): RoomModeInfo_NameI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeInfo_NameI18nEntry>, I>>(base?: I): RoomModeInfo_NameI18nEntry {
    return RoomModeInfo_NameI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeInfo_NameI18nEntry>, I>>(object: I): RoomModeInfo_NameI18nEntry {
    const message = createBaseRoomModeInfo_NameI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRoomModeInfo_IconI18nEntry(): RoomModeInfo_IconI18nEntry {
  return { key: '', value: '' };
}

export const RoomModeInfo_IconI18nEntry: MessageFns<RoomModeInfo_IconI18nEntry> = {
  fromJSON(object: any): RoomModeInfo_IconI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeInfo_IconI18nEntry>, I>>(base?: I): RoomModeInfo_IconI18nEntry {
    return RoomModeInfo_IconI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeInfo_IconI18nEntry>, I>>(object: I): RoomModeInfo_IconI18nEntry {
    const message = createBaseRoomModeInfo_IconI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
