// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/bag.proto

/* eslint-disable */

export const protobufPackage = 'bag';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/bagsrv */

export interface GetBagListReq {
  /** 获取的资产类型：im_video_card（视频和聊天卡），avatar_frame(头像框)，chat_bubble（聊天气泡） */
  type: string;
}

export interface GetBagListResp {
  /** 资源合集 */
  items: BagItem[];
  /** 穿戴资源内第几个 */
  wearing_idx: number;
}

export interface BagItem {
  id: number;
  /** 预览图 */
  preview: string;
  /** 名称 */
  name: string;
  /** 资源类型: im_card,video_card,avatar_frame,chat_bubble */
  resource_type: string;
  /** 时效类型:forever - 永久 ,effective - 有时效,no_effective - 没有时效 */
  valid_type: string;
  /** 时效展示文案 */
  effective_content: string;
  /** 额外信息 */
  ext_info: { [key: string]: string };
  /** 资源id */
  resource_id: number;
}

export interface BagItem_ExtInfoEntry {
  key: string;
  value: string;
}

export interface ChatBubbleItem {
  /** 背景底框 */
  background_bottom_frame: string;
  /** 左上 */
  upper_left: string;
  /** 右上 */
  upper_right: string;
  /** 左下 */
  lower_left: string;
  /** 右下 */
  lower_right: string;
  /** 文本颜色 */
  text_color: string;
}

export interface AvatarFrameItem {
  /** 预览路径 */
  preview_url: string;
  /** 资源路径 */
  resource_url: string;
  /** 枚举: dynamic - 动态， static - 静态 */
  type: string;
}

export interface WearReq {
  /** wear - 穿戴，take_off - 脱掉 */
  action: string;
  /** 资源id，action为穿戴时必传。 */
  id: number;
}

export interface WearResp {}

/** 资源获得通知 */
export interface ResourceObtainMessage {
  /** 资源id */
  id: number;
  /** 资源类型，枚举：avatar_frame,chat_bubble */
  resource_type: string;
  /** 是否首次 */
  is_first: boolean;
  /** 预览图 */
  preview_url: string;
  /** 文案 */
  content: string;
}

export interface Notify {
  resource_obatin_msg: ResourceObtainMessage | undefined;
}

function createBaseGetBagListReq(): GetBagListReq {
  return { type: '' };
}

export const GetBagListReq: MessageFns<GetBagListReq> = {
  fromJSON(object: any): GetBagListReq {
    return { type: isSet(object.type) ? globalThis.String(object.type) : '' };
  },

  create<I extends Exact<DeepPartial<GetBagListReq>, I>>(base?: I): GetBagListReq {
    return GetBagListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBagListReq>, I>>(object: I): GetBagListReq {
    const message = createBaseGetBagListReq();
    message.type = object.type ?? '';
    return message;
  }
};

function createBaseGetBagListResp(): GetBagListResp {
  return { items: [], wearing_idx: 0 };
}

export const GetBagListResp: MessageFns<GetBagListResp> = {
  fromJSON(object: any): GetBagListResp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => BagItem.fromJSON(e)) : [],
      wearing_idx: isSet(object.wearing_idx) ? globalThis.Number(object.wearing_idx) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetBagListResp>, I>>(base?: I): GetBagListResp {
    return GetBagListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBagListResp>, I>>(object: I): GetBagListResp {
    const message = createBaseGetBagListResp();
    message.items = object.items?.map(e => BagItem.fromPartial(e)) || [];
    message.wearing_idx = object.wearing_idx ?? 0;
    return message;
  }
};

function createBaseBagItem(): BagItem {
  return {
    id: 0,
    preview: '',
    name: '',
    resource_type: '',
    valid_type: '',
    effective_content: '',
    ext_info: {},
    resource_id: 0
  };
}

export const BagItem: MessageFns<BagItem> = {
  fromJSON(object: any): BagItem {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      preview: isSet(object.preview) ? globalThis.String(object.preview) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      valid_type: isSet(object.valid_type) ? globalThis.String(object.valid_type) : '',
      effective_content: isSet(object.effective_content) ? globalThis.String(object.effective_content) : '',
      ext_info: isObject(object.ext_info)
        ? Object.entries(object.ext_info).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<BagItem>, I>>(base?: I): BagItem {
    return BagItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagItem>, I>>(object: I): BagItem {
    const message = createBaseBagItem();
    message.id = object.id ?? 0;
    message.preview = object.preview ?? '';
    message.name = object.name ?? '';
    message.resource_type = object.resource_type ?? '';
    message.valid_type = object.valid_type ?? '';
    message.effective_content = object.effective_content ?? '';
    message.ext_info = Object.entries(object.ext_info ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.resource_id = object.resource_id ?? 0;
    return message;
  }
};

function createBaseBagItem_ExtInfoEntry(): BagItem_ExtInfoEntry {
  return { key: '', value: '' };
}

export const BagItem_ExtInfoEntry: MessageFns<BagItem_ExtInfoEntry> = {
  fromJSON(object: any): BagItem_ExtInfoEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<BagItem_ExtInfoEntry>, I>>(base?: I): BagItem_ExtInfoEntry {
    return BagItem_ExtInfoEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagItem_ExtInfoEntry>, I>>(object: I): BagItem_ExtInfoEntry {
    const message = createBaseBagItem_ExtInfoEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseChatBubbleItem(): ChatBubbleItem {
  return {
    background_bottom_frame: '',
    upper_left: '',
    upper_right: '',
    lower_left: '',
    lower_right: '',
    text_color: ''
  };
}

export const ChatBubbleItem: MessageFns<ChatBubbleItem> = {
  fromJSON(object: any): ChatBubbleItem {
    return {
      background_bottom_frame: isSet(object.background_bottom_frame)
        ? globalThis.String(object.background_bottom_frame)
        : '',
      upper_left: isSet(object.upper_left) ? globalThis.String(object.upper_left) : '',
      upper_right: isSet(object.upper_right) ? globalThis.String(object.upper_right) : '',
      lower_left: isSet(object.lower_left) ? globalThis.String(object.lower_left) : '',
      lower_right: isSet(object.lower_right) ? globalThis.String(object.lower_right) : '',
      text_color: isSet(object.text_color) ? globalThis.String(object.text_color) : ''
    };
  },

  create<I extends Exact<DeepPartial<ChatBubbleItem>, I>>(base?: I): ChatBubbleItem {
    return ChatBubbleItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChatBubbleItem>, I>>(object: I): ChatBubbleItem {
    const message = createBaseChatBubbleItem();
    message.background_bottom_frame = object.background_bottom_frame ?? '';
    message.upper_left = object.upper_left ?? '';
    message.upper_right = object.upper_right ?? '';
    message.lower_left = object.lower_left ?? '';
    message.lower_right = object.lower_right ?? '';
    message.text_color = object.text_color ?? '';
    return message;
  }
};

function createBaseAvatarFrameItem(): AvatarFrameItem {
  return { preview_url: '', resource_url: '', type: '' };
}

export const AvatarFrameItem: MessageFns<AvatarFrameItem> = {
  fromJSON(object: any): AvatarFrameItem {
    return {
      preview_url: isSet(object.preview_url) ? globalThis.String(object.preview_url) : '',
      resource_url: isSet(object.resource_url) ? globalThis.String(object.resource_url) : '',
      type: isSet(object.type) ? globalThis.String(object.type) : ''
    };
  },

  create<I extends Exact<DeepPartial<AvatarFrameItem>, I>>(base?: I): AvatarFrameItem {
    return AvatarFrameItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AvatarFrameItem>, I>>(object: I): AvatarFrameItem {
    const message = createBaseAvatarFrameItem();
    message.preview_url = object.preview_url ?? '';
    message.resource_url = object.resource_url ?? '';
    message.type = object.type ?? '';
    return message;
  }
};

function createBaseWearReq(): WearReq {
  return { action: '', id: 0 };
}

export const WearReq: MessageFns<WearReq> = {
  fromJSON(object: any): WearReq {
    return {
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<WearReq>, I>>(base?: I): WearReq {
    return WearReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WearReq>, I>>(object: I): WearReq {
    const message = createBaseWearReq();
    message.action = object.action ?? '';
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseWearResp(): WearResp {
  return {};
}

export const WearResp: MessageFns<WearResp> = {
  fromJSON(_: any): WearResp {
    return {};
  },

  create<I extends Exact<DeepPartial<WearResp>, I>>(base?: I): WearResp {
    return WearResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WearResp>, I>>(_: I): WearResp {
    const message = createBaseWearResp();
    return message;
  }
};

function createBaseResourceObtainMessage(): ResourceObtainMessage {
  return { id: 0, resource_type: '', is_first: false, preview_url: '', content: '' };
}

export const ResourceObtainMessage: MessageFns<ResourceObtainMessage> = {
  fromJSON(object: any): ResourceObtainMessage {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      is_first: isSet(object.is_first) ? globalThis.Boolean(object.is_first) : false,
      preview_url: isSet(object.preview_url) ? globalThis.String(object.preview_url) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourceObtainMessage>, I>>(base?: I): ResourceObtainMessage {
    return ResourceObtainMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourceObtainMessage>, I>>(object: I): ResourceObtainMessage {
    const message = createBaseResourceObtainMessage();
    message.id = object.id ?? 0;
    message.resource_type = object.resource_type ?? '';
    message.is_first = object.is_first ?? false;
    message.preview_url = object.preview_url ?? '';
    message.content = object.content ?? '';
    return message;
  }
};

function createBaseNotify(): Notify {
  return { resource_obatin_msg: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return {
      resource_obatin_msg: isSet(object.resource_obatin_msg)
        ? ResourceObtainMessage.fromJSON(object.resource_obatin_msg)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.resource_obatin_msg =
      object.resource_obatin_msg !== undefined && object.resource_obatin_msg !== null
        ? ResourceObtainMessage.fromPartial(object.resource_obatin_msg)
        : undefined;
    return message;
  }
};

export type BagDefinition = typeof BagDefinition;
export const BagDefinition = {
  name: 'Bag',
  fullName: 'bag.Bag',
  methods: {
    getBagList: {
      name: 'GetBagList',
      requestType: GetBagListReq,
      requestStream: false,
      responseType: GetBagListResp,
      responseStream: false,
      options: {}
    },
    wear: {
      name: 'Wear',
      requestType: WearReq,
      requestStream: false,
      responseType: WearResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
