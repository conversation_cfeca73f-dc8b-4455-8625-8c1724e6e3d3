// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/share.proto

/* eslint-disable */

export const protobufPackage = 'share';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/share/handler/ */

export interface GetRoomShareInfoReq {
  room_id: number;
}

export interface GetRoomShareInfoRsp {
  /** 分享数据 */
  share_info: GetShareInfoRsp | undefined;
}

export interface GetShareInfoRsp {
  /** 重定向地址 */
  redirect_url: string;
  /** 邀请进房 token */
  invite_token: string;
}

function createBaseGetRoomShareInfoReq(): GetRoomShareInfoReq {
  return { room_id: 0 };
}

export const GetRoomShareInfoReq: MessageFns<GetRoomShareInfoReq> = {
  fromJSON(object: any): GetRoomShareInfoReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRoomShareInfoReq>, I>>(base?: I): GetRoomShareInfoReq {
    return GetRoomShareInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomShareInfoReq>, I>>(object: I): GetRoomShareInfoReq {
    const message = createBaseGetRoomShareInfoReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetRoomShareInfoRsp(): GetRoomShareInfoRsp {
  return { share_info: undefined };
}

export const GetRoomShareInfoRsp: MessageFns<GetRoomShareInfoRsp> = {
  fromJSON(object: any): GetRoomShareInfoRsp {
    return { share_info: isSet(object.share_info) ? GetShareInfoRsp.fromJSON(object.share_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetRoomShareInfoRsp>, I>>(base?: I): GetRoomShareInfoRsp {
    return GetRoomShareInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomShareInfoRsp>, I>>(object: I): GetRoomShareInfoRsp {
    const message = createBaseGetRoomShareInfoRsp();
    message.share_info =
      object.share_info !== undefined && object.share_info !== null
        ? GetShareInfoRsp.fromPartial(object.share_info)
        : undefined;
    return message;
  }
};

function createBaseGetShareInfoRsp(): GetShareInfoRsp {
  return { redirect_url: '', invite_token: '' };
}

export const GetShareInfoRsp: MessageFns<GetShareInfoRsp> = {
  fromJSON(object: any): GetShareInfoRsp {
    return {
      redirect_url: isSet(object.redirect_url) ? globalThis.String(object.redirect_url) : '',
      invite_token: isSet(object.invite_token) ? globalThis.String(object.invite_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetShareInfoRsp>, I>>(base?: I): GetShareInfoRsp {
    return GetShareInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetShareInfoRsp>, I>>(object: I): GetShareInfoRsp {
    const message = createBaseGetShareInfoRsp();
    message.redirect_url = object.redirect_url ?? '';
    message.invite_token = object.invite_token ?? '';
    return message;
  }
};

export type ShareDefinition = typeof ShareDefinition;
export const ShareDefinition = {
  name: 'Share',
  fullName: 'share.Share',
  methods: {
    getRoomShareInfo: {
      name: 'GetRoomShareInfo',
      requestType: GetRoomShareInfoReq,
      requestStream: false,
      responseType: GetRoomShareInfoRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
