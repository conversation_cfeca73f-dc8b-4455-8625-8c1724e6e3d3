// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/notify.proto

/* eslint-disable */
import {
  LiveStatus,
  liveStatusFromJSON,
  RoomInfo,
  RoomLevel,
  RoomSettings,
  RoomStateInfo,
  RoomUserInfo,
  RoomUserRole,
  roomUserRoleFromJSON,
  SeatList,
  SeatScoreSwitch,
  seatScoreSwitchFromJSON,
  UserScoreList
} from './comm';
import { GiftResourceType, giftResourceTypeFromJSON, GiftWishlistData, GiftWishlistRsp } from './gift';
import { Message } from './room_msg';
import { UserInfo } from './user';

export const protobufPackage = 'api.lucky';

export enum SquarePopUpWindowSceneType {
  SCENE_TYPE_NONE = 0,
  /** SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_WORKING - 主播激励活动加入速配弹窗 */
  SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_WORKING = 1,
  /** SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_FAKE_VIDEO_END - 主播假通话结束弹窗 */
  SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_FAKE_VIDEO_END = 2,
  /** SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_REWARD_POINT - 主播激励活动积分弹窗 */
  SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_REWARD_POINT = 3,
  /** SCENE_TYPE_USER_FREE_GAME_REWARD - 付费通话结束引导游戏弹窗 */
  SCENE_TYPE_USER_FREE_GAME_REWARD = 4,
  /** SCENE_TYPE_USER_FREE_GAME_FINISH - 结束弹窗需要跳转到H5 */
  SCENE_TYPE_USER_FREE_GAME_FINISH = 5,
  /** SCENE_TYPE_COMMON_POPUP_H5_DEEPLINK - 通用的h5弹窗 */
  SCENE_TYPE_COMMON_POPUP_H5_DEEPLINK = 6,
  UNRECOGNIZED = -1
}

export function squarePopUpWindowSceneTypeFromJSON(object: any): SquarePopUpWindowSceneType {
  switch (object) {
    case 0:
    case 'SCENE_TYPE_NONE':
      return SquarePopUpWindowSceneType.SCENE_TYPE_NONE;
    case 1:
    case 'SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_WORKING':
      return SquarePopUpWindowSceneType.SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_WORKING;
    case 2:
    case 'SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_FAKE_VIDEO_END':
      return SquarePopUpWindowSceneType.SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_FAKE_VIDEO_END;
    case 3:
    case 'SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_REWARD_POINT':
      return SquarePopUpWindowSceneType.SCENE_TYPE_ANCHOR_INCENTIVE_ACTIVITY_REWARD_POINT;
    case 4:
    case 'SCENE_TYPE_USER_FREE_GAME_REWARD':
      return SquarePopUpWindowSceneType.SCENE_TYPE_USER_FREE_GAME_REWARD;
    case 5:
    case 'SCENE_TYPE_USER_FREE_GAME_FINISH':
      return SquarePopUpWindowSceneType.SCENE_TYPE_USER_FREE_GAME_FINISH;
    case 6:
    case 'SCENE_TYPE_COMMON_POPUP_H5_DEEPLINK':
      return SquarePopUpWindowSceneType.SCENE_TYPE_COMMON_POPUP_H5_DEEPLINK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SquarePopUpWindowSceneType.UNRECOGNIZED;
  }
}

export interface Notify {
  /** 公屏消息 */
  message: NotifyRoomMessage | undefined;
  user_enter: NotifyUserEnter | undefined;
  user_leave: NotifyUserLeave | undefined;
  online_users: NotifyOnlineUsers | undefined;
  /** 房间基础信息变更 */
  room_info_modify: NotifyRoomInfoModify | undefined;
  /** 房间等级变更 */
  room_level_modify: NotifyRoomLevelModify | undefined;
  /** 房间配置变更 */
  room_settings_modify: NotifyRoomSettingsModify | undefined;
  /** 用户角色变更 */
  user_role_modify: NotifyUserRoleModify | undefined;
  /** 用户被踢 */
  kick_user: NotifyKickUser | undefined;
  /** 麦位-麦位列表变更(通知对象:所有用户) */
  seat_list_change: NotifySeatListChange | undefined;
  /** 麦位-邀请用户上麦(通知对象:被邀请用户) */
  seat_invitation: NotifySeatInvitation | undefined;
  /** 麦位-用户被踢下麦(通知对象:被踢用户) */
  kick_seat: NotifyKickSeat | undefined;
  /** 麦位-用户申请上麦(通知对象:所有管理员) */
  apply_enter_seat: NotifyApplyEnterSeat | undefined;
  /** 麦位-用户上麦申请通过(通知对象:申请上麦用户) */
  approve_apply_enter_seat: NotifyApproveApplyEnterSeat | undefined;
  /** 麦位-禁音(通知对象:禁音用户) */
  mute_mic: NotifyMuteMic | undefined;
  /** 直播结束，客户端处理退房逻辑，无需调用LiveRoom.Leave接口 */
  live_end: NotifyLiveEnd | undefined;
  /** 麦位用户积分开关 */
  user_score_switch_change: NotifyUserScoreSwitchChange | undefined;
  /** 麦上用户数值变化 */
  user_score_list_change: NotifyUserScoreListChange | undefined;
  /** 房间模式变更 */
  room_mode_modify: NotifyRoomModeModify | undefined;
  /** 充值余额成功到账通知 */
  recharge_balance_success: NotifyRechargeBalanceSuccess | undefined;
  /** 生效中的游戏列表 */
  game_list: NotifyGameInfo[];
  /** 送礼消息通知 */
  send_gift: NotifySendGift | undefined;
  /** 邀请送礼消息 */
  invite_gift: NotifyInviteSendGift | undefined;
  /** 用户公屏被禁言 */
  mute_room_msg: NotifyMuteRoomMsg | undefined;
  /** 用户公屏解除禁言 */
  unmute_room_msg: NotifyUnMuteRoomMsg | undefined;
  /** 正方形通知弹窗 */
  square_pop_up_window_msg: NotifySquarePopUpWindowMsg | undefined;
  /** 免费游戏币奖励池入口通知 */
  free_game_reward_entrance: NotifyFreeGameRewardEntrance | undefined;
  /** 语聊房弹窗 */
  voice_pop_up_window_msg: NotifyVoicePopUpWindowMsg | undefined;
  /** 左上角收益/消费展示消息 */
  room_consumption_income_info_msg: NotifyRoomConsumptionIncomeInfoMsg | undefined;
  /** 直播状态变更 */
  live_status_change: NotifyLiveStatusChange | undefined;
  /** 关注 */
  follow: NotifyFollow | undefined;
  /** 心愿礼物进度通知 不包括榜单 */
  wish_progress: NotifyWishGiftList | undefined;
  /** 心愿礼物达成通知 */
  wish_finish: NotifyGiftWishlistRsp | undefined;
}

/** 用户进房 */
export interface NotifyUserEnter {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 进入房间用户信息 */
  user: RoomUserInfo | undefined;
}

/** 用户退房 */
export interface NotifyUserLeave {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 离开房间用户信息 */
  user: RoomUserInfo | undefined;
}

/** 在线用户列表 */
export interface NotifyOnlineUsers {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 总在线用户人数 */
  oneline_count: number;
  /** 在线用户用户列表，top n */
  users: RoomUserInfo[];
}

/** 房间信息修改 */
export interface NotifyRoomInfoModify {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间信息 */
  room_info: RoomInfo | undefined;
}

export interface NotifyRoomLevelModify {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间等级变更 */
  room_level: RoomLevel | undefined;
}

export interface NotifyRoomSettingsModify {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间配置管更 */
  room_settings: RoomSettings | undefined;
}

/** 用户角色变更 */
export interface NotifyUserRoleModify {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 修改之前的角色 */
  old_role: RoomUserRole;
  /** 用户信息 */
  user: RoomUserInfo | undefined;
}

/** 用户被踢 */
export interface NotifyKickUser {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间ID */
  room_id: number;
  /** 被踢用户信息 */
  kick_user: RoomUserInfo | undefined;
}

/** 公屏消息 */
export interface NotifyRoomMessage {
  /** 房间ID */
  room_id: number;
  /** 公屏消息 */
  message: Message | undefined;
}

/** 麦位-麦位列表变更(通知对象:所有用户) */
export interface NotifySeatListChange {
  /** 麦位列表 */
  seat_list: SeatList | undefined;
}

/** 麦位-邀请用户上麦(通知对象:被邀请用户) */
export interface NotifySeatInvitation {
  /** 座位序号;用户同意或者拒绝需要带上此参数 */
  seat_index: number;
  /** 邀请人 */
  inviter: RoomUserInfo | undefined;
  /** 被邀请人 */
  invitee: RoomUserInfo | undefined;
}

/** 麦位-用户被踢下麦(通知对象:被踢用户) */
export interface NotifyKickSeat {
  /** 操作人 */
  operator: RoomUserInfo | undefined;
  /** 被踢用户 */
  target_user: RoomUserInfo | undefined;
}

/** 麦位-用户申请上麦(通知对象:所有管理员) */
export interface NotifyApplyEnterSeat {
  /** 上麦申请数量 */
  apply_count: number;
}

/** 麦位-用户上麦申请通过(通知对象:申请上麦用户) */
export interface NotifyApproveApplyEnterSeat {
  /** 座位序号 */
  seat_index: number;
}

/** 麦位-禁音(通知对象:禁音用户) */
export interface NotifyMuteMic {
  /** 操作人 */
  operator: RoomUserInfo | undefined;
  /** 禁音用户 */
  target_user: RoomUserInfo | undefined;
  /** 麦克风是否禁用 */
  mic_disabled: boolean;
}

export interface NotifyLiveEnd {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间ID */
  room_id: number;
  /** 房主uid */
  owner_uid: number;
}

export interface NotifyRoomModeModify {
  room_state_info: RoomStateInfo | undefined;
}

/** 麦位-用户计数变更(通知对象:所有用户) */
export interface NotifyUserScoreListChange {
  /** 用户礼物计数列表 */
  user_score_list: UserScoreList | undefined;
}

export interface NotifyUserScoreSwitchChange {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 开关状态 */
  switch: SeatScoreSwitch;
}

export interface NotifyRechargeBalanceSuccess {
  /** 当前余额 */
  coin: number;
}

export interface NotifyGameInfo {
  /** 跳转路径 */
  deep_link: string;
  /** 游戏图片 */
  game_pic: string;
  /** 游戏id 暂时没用先预留 */
  game_id: string;
}

export interface NotifySendGift {
  /** 送礼人基本信息 */
  from_user: UserInfo | undefined;
  /** 收礼人基本信息 */
  to_users: UserInfo[];
  /** 礼物信息 */
  gift: Gift | undefined;
  /** 礼物数量 */
  nums: number;
}

export interface Gift {
  /** 礼物id */
  gift_id: number;
  /** 礼物名称 */
  gift_name: string;
  /** 礼物图片 */
  img_url: string;
  /** 资源类型 */
  resource_type: GiftResourceType;
  /** 资源路径 */
  resource_url: string;
}

export interface NotifyInviteSendGift {
  /** 礼物信息 */
  gift: Gift | undefined;
  /** 邀请人基本信息 */
  from_user: UserInfo | undefined;
  /** 数量 */
  nums: number;
}

export interface NotifyMuteRoomMsg {
  /** 房间id */
  room_id: number;
}

export interface NotifyUnMuteRoomMsg {
  /** 房间id */
  room_id: number;
}

export interface NotifySquarePopUpWindowMsg {
  /** 标题 */
  title: string;
  /** 文案 */
  content: string;
  /** 图片 */
  image_url: string;
  /** 事件id */
  event_id: string;
  /** 场景值 */
  scene: SquarePopUpWindowSceneType;
  /** 按钮文案 */
  button_text: string;
  /** 是否显示关闭按钮 */
  is_show_close_button: boolean;
  free_game_reward_notify: NotifyFreeGameRewardEntrance | undefined;
  pop_up_h5_info: NotifyPopUpDeepLinkInfo | undefined;
}

/** copy多一份出来区别上面的 */
export interface NotifyVoicePopUpWindowMsg {
  /** 标题 */
  title: string;
  /** 文案 */
  content: string;
  /** 图片 */
  image_url: string;
  /** 事件id */
  event_id: string;
  /** 场景值 */
  scene: SquarePopUpWindowSceneType;
  /** 按钮文案 */
  button_text: string;
  /** 是否显示关闭按钮 */
  is_show_close_button: boolean;
  free_game_reward_notify: NotifyFreeGameRewardEntrance | undefined;
}

export interface NotifyFreeGameRewardEntrance {
  expired_ts: number;
  free_game_coin: number;
  free_game_pic: string;
  tip: string;
  /** 1展示入口 0不展示入口 */
  show_entrance: number;
  receive_id: number;
}

export interface NotifyPopUpDeepLinkInfo {
  width: number;
  height: number;
  deep_link: string;
}

export interface NotifyRoomConsumptionIncomeInfoMsg {
  /** 消费钻石 */
  consume_coins: number;
  /** 收益积分 */
  revenue_points: number;
}

export interface NotifyLiveStatusChange {
  /** 时间戳 毫秒 */
  timestamp: number;
  /** 房间ID */
  room_id: number;
  /** 直播场次ID */
  live_id: number;
  /** 房间状态 */
  live_status: LiveStatus;
}

export interface NotifyFollow {
  /** 用户名   公屏展示某个用户名关注了主播   这里拿用户的具体信息 包括头像 */
  user: RoomUserInfo | undefined;
}

export interface NotifyWishGiftList {
  gift_wish_list_data: GiftWishlistData[];
}

export interface NotifyGiftWishlistRsp {
  gift_wish_rsp: GiftWishlistRsp | undefined;
}

function createBaseNotify(): Notify {
  return {
    message: undefined,
    user_enter: undefined,
    user_leave: undefined,
    online_users: undefined,
    room_info_modify: undefined,
    room_level_modify: undefined,
    room_settings_modify: undefined,
    user_role_modify: undefined,
    kick_user: undefined,
    seat_list_change: undefined,
    seat_invitation: undefined,
    kick_seat: undefined,
    apply_enter_seat: undefined,
    approve_apply_enter_seat: undefined,
    mute_mic: undefined,
    live_end: undefined,
    user_score_switch_change: undefined,
    user_score_list_change: undefined,
    room_mode_modify: undefined,
    recharge_balance_success: undefined,
    game_list: [],
    send_gift: undefined,
    invite_gift: undefined,
    mute_room_msg: undefined,
    unmute_room_msg: undefined,
    square_pop_up_window_msg: undefined,
    free_game_reward_entrance: undefined,
    voice_pop_up_window_msg: undefined,
    room_consumption_income_info_msg: undefined,
    live_status_change: undefined,
    follow: undefined,
    wish_progress: undefined,
    wish_finish: undefined
  };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return {
      message: isSet(object.message) ? NotifyRoomMessage.fromJSON(object.message) : undefined,
      user_enter: isSet(object.user_enter) ? NotifyUserEnter.fromJSON(object.user_enter) : undefined,
      user_leave: isSet(object.user_leave) ? NotifyUserLeave.fromJSON(object.user_leave) : undefined,
      online_users: isSet(object.online_users) ? NotifyOnlineUsers.fromJSON(object.online_users) : undefined,
      room_info_modify: isSet(object.room_info_modify)
        ? NotifyRoomInfoModify.fromJSON(object.room_info_modify)
        : undefined,
      room_level_modify: isSet(object.room_level_modify)
        ? NotifyRoomLevelModify.fromJSON(object.room_level_modify)
        : undefined,
      room_settings_modify: isSet(object.room_settings_modify)
        ? NotifyRoomSettingsModify.fromJSON(object.room_settings_modify)
        : undefined,
      user_role_modify: isSet(object.user_role_modify)
        ? NotifyUserRoleModify.fromJSON(object.user_role_modify)
        : undefined,
      kick_user: isSet(object.kick_user) ? NotifyKickUser.fromJSON(object.kick_user) : undefined,
      seat_list_change: isSet(object.seat_list_change)
        ? NotifySeatListChange.fromJSON(object.seat_list_change)
        : undefined,
      seat_invitation: isSet(object.seat_invitation)
        ? NotifySeatInvitation.fromJSON(object.seat_invitation)
        : undefined,
      kick_seat: isSet(object.kick_seat) ? NotifyKickSeat.fromJSON(object.kick_seat) : undefined,
      apply_enter_seat: isSet(object.apply_enter_seat)
        ? NotifyApplyEnterSeat.fromJSON(object.apply_enter_seat)
        : undefined,
      approve_apply_enter_seat: isSet(object.approve_apply_enter_seat)
        ? NotifyApproveApplyEnterSeat.fromJSON(object.approve_apply_enter_seat)
        : undefined,
      mute_mic: isSet(object.mute_mic) ? NotifyMuteMic.fromJSON(object.mute_mic) : undefined,
      live_end: isSet(object.live_end) ? NotifyLiveEnd.fromJSON(object.live_end) : undefined,
      user_score_switch_change: isSet(object.user_score_switch_change)
        ? NotifyUserScoreSwitchChange.fromJSON(object.user_score_switch_change)
        : undefined,
      user_score_list_change: isSet(object.user_score_list_change)
        ? NotifyUserScoreListChange.fromJSON(object.user_score_list_change)
        : undefined,
      room_mode_modify: isSet(object.room_mode_modify)
        ? NotifyRoomModeModify.fromJSON(object.room_mode_modify)
        : undefined,
      recharge_balance_success: isSet(object.recharge_balance_success)
        ? NotifyRechargeBalanceSuccess.fromJSON(object.recharge_balance_success)
        : undefined,
      game_list: globalThis.Array.isArray(object?.game_list)
        ? object.game_list.map((e: any) => NotifyGameInfo.fromJSON(e))
        : [],
      send_gift: isSet(object.send_gift) ? NotifySendGift.fromJSON(object.send_gift) : undefined,
      invite_gift: isSet(object.invite_gift) ? NotifyInviteSendGift.fromJSON(object.invite_gift) : undefined,
      mute_room_msg: isSet(object.mute_room_msg) ? NotifyMuteRoomMsg.fromJSON(object.mute_room_msg) : undefined,
      unmute_room_msg: isSet(object.unmute_room_msg) ? NotifyUnMuteRoomMsg.fromJSON(object.unmute_room_msg) : undefined,
      square_pop_up_window_msg: isSet(object.square_pop_up_window_msg)
        ? NotifySquarePopUpWindowMsg.fromJSON(object.square_pop_up_window_msg)
        : undefined,
      free_game_reward_entrance: isSet(object.free_game_reward_entrance)
        ? NotifyFreeGameRewardEntrance.fromJSON(object.free_game_reward_entrance)
        : undefined,
      voice_pop_up_window_msg: isSet(object.voice_pop_up_window_msg)
        ? NotifyVoicePopUpWindowMsg.fromJSON(object.voice_pop_up_window_msg)
        : undefined,
      room_consumption_income_info_msg: isSet(object.room_consumption_income_info_msg)
        ? NotifyRoomConsumptionIncomeInfoMsg.fromJSON(object.room_consumption_income_info_msg)
        : undefined,
      live_status_change: isSet(object.live_status_change)
        ? NotifyLiveStatusChange.fromJSON(object.live_status_change)
        : undefined,
      follow: isSet(object.follow) ? NotifyFollow.fromJSON(object.follow) : undefined,
      wish_progress: isSet(object.wish_progress) ? NotifyWishGiftList.fromJSON(object.wish_progress) : undefined,
      wish_finish: isSet(object.wish_finish) ? NotifyGiftWishlistRsp.fromJSON(object.wish_finish) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.message =
      object.message !== undefined && object.message !== null
        ? NotifyRoomMessage.fromPartial(object.message)
        : undefined;
    message.user_enter =
      object.user_enter !== undefined && object.user_enter !== null
        ? NotifyUserEnter.fromPartial(object.user_enter)
        : undefined;
    message.user_leave =
      object.user_leave !== undefined && object.user_leave !== null
        ? NotifyUserLeave.fromPartial(object.user_leave)
        : undefined;
    message.online_users =
      object.online_users !== undefined && object.online_users !== null
        ? NotifyOnlineUsers.fromPartial(object.online_users)
        : undefined;
    message.room_info_modify =
      object.room_info_modify !== undefined && object.room_info_modify !== null
        ? NotifyRoomInfoModify.fromPartial(object.room_info_modify)
        : undefined;
    message.room_level_modify =
      object.room_level_modify !== undefined && object.room_level_modify !== null
        ? NotifyRoomLevelModify.fromPartial(object.room_level_modify)
        : undefined;
    message.room_settings_modify =
      object.room_settings_modify !== undefined && object.room_settings_modify !== null
        ? NotifyRoomSettingsModify.fromPartial(object.room_settings_modify)
        : undefined;
    message.user_role_modify =
      object.user_role_modify !== undefined && object.user_role_modify !== null
        ? NotifyUserRoleModify.fromPartial(object.user_role_modify)
        : undefined;
    message.kick_user =
      object.kick_user !== undefined && object.kick_user !== null
        ? NotifyKickUser.fromPartial(object.kick_user)
        : undefined;
    message.seat_list_change =
      object.seat_list_change !== undefined && object.seat_list_change !== null
        ? NotifySeatListChange.fromPartial(object.seat_list_change)
        : undefined;
    message.seat_invitation =
      object.seat_invitation !== undefined && object.seat_invitation !== null
        ? NotifySeatInvitation.fromPartial(object.seat_invitation)
        : undefined;
    message.kick_seat =
      object.kick_seat !== undefined && object.kick_seat !== null
        ? NotifyKickSeat.fromPartial(object.kick_seat)
        : undefined;
    message.apply_enter_seat =
      object.apply_enter_seat !== undefined && object.apply_enter_seat !== null
        ? NotifyApplyEnterSeat.fromPartial(object.apply_enter_seat)
        : undefined;
    message.approve_apply_enter_seat =
      object.approve_apply_enter_seat !== undefined && object.approve_apply_enter_seat !== null
        ? NotifyApproveApplyEnterSeat.fromPartial(object.approve_apply_enter_seat)
        : undefined;
    message.mute_mic =
      object.mute_mic !== undefined && object.mute_mic !== null
        ? NotifyMuteMic.fromPartial(object.mute_mic)
        : undefined;
    message.live_end =
      object.live_end !== undefined && object.live_end !== null
        ? NotifyLiveEnd.fromPartial(object.live_end)
        : undefined;
    message.user_score_switch_change =
      object.user_score_switch_change !== undefined && object.user_score_switch_change !== null
        ? NotifyUserScoreSwitchChange.fromPartial(object.user_score_switch_change)
        : undefined;
    message.user_score_list_change =
      object.user_score_list_change !== undefined && object.user_score_list_change !== null
        ? NotifyUserScoreListChange.fromPartial(object.user_score_list_change)
        : undefined;
    message.room_mode_modify =
      object.room_mode_modify !== undefined && object.room_mode_modify !== null
        ? NotifyRoomModeModify.fromPartial(object.room_mode_modify)
        : undefined;
    message.recharge_balance_success =
      object.recharge_balance_success !== undefined && object.recharge_balance_success !== null
        ? NotifyRechargeBalanceSuccess.fromPartial(object.recharge_balance_success)
        : undefined;
    message.game_list = object.game_list?.map(e => NotifyGameInfo.fromPartial(e)) || [];
    message.send_gift =
      object.send_gift !== undefined && object.send_gift !== null
        ? NotifySendGift.fromPartial(object.send_gift)
        : undefined;
    message.invite_gift =
      object.invite_gift !== undefined && object.invite_gift !== null
        ? NotifyInviteSendGift.fromPartial(object.invite_gift)
        : undefined;
    message.mute_room_msg =
      object.mute_room_msg !== undefined && object.mute_room_msg !== null
        ? NotifyMuteRoomMsg.fromPartial(object.mute_room_msg)
        : undefined;
    message.unmute_room_msg =
      object.unmute_room_msg !== undefined && object.unmute_room_msg !== null
        ? NotifyUnMuteRoomMsg.fromPartial(object.unmute_room_msg)
        : undefined;
    message.square_pop_up_window_msg =
      object.square_pop_up_window_msg !== undefined && object.square_pop_up_window_msg !== null
        ? NotifySquarePopUpWindowMsg.fromPartial(object.square_pop_up_window_msg)
        : undefined;
    message.free_game_reward_entrance =
      object.free_game_reward_entrance !== undefined && object.free_game_reward_entrance !== null
        ? NotifyFreeGameRewardEntrance.fromPartial(object.free_game_reward_entrance)
        : undefined;
    message.voice_pop_up_window_msg =
      object.voice_pop_up_window_msg !== undefined && object.voice_pop_up_window_msg !== null
        ? NotifyVoicePopUpWindowMsg.fromPartial(object.voice_pop_up_window_msg)
        : undefined;
    message.room_consumption_income_info_msg =
      object.room_consumption_income_info_msg !== undefined && object.room_consumption_income_info_msg !== null
        ? NotifyRoomConsumptionIncomeInfoMsg.fromPartial(object.room_consumption_income_info_msg)
        : undefined;
    message.live_status_change =
      object.live_status_change !== undefined && object.live_status_change !== null
        ? NotifyLiveStatusChange.fromPartial(object.live_status_change)
        : undefined;
    message.follow =
      object.follow !== undefined && object.follow !== null ? NotifyFollow.fromPartial(object.follow) : undefined;
    message.wish_progress =
      object.wish_progress !== undefined && object.wish_progress !== null
        ? NotifyWishGiftList.fromPartial(object.wish_progress)
        : undefined;
    message.wish_finish =
      object.wish_finish !== undefined && object.wish_finish !== null
        ? NotifyGiftWishlistRsp.fromPartial(object.wish_finish)
        : undefined;
    return message;
  }
};

function createBaseNotifyUserEnter(): NotifyUserEnter {
  return { timestamp: 0, user: undefined };
}

export const NotifyUserEnter: MessageFns<NotifyUserEnter> = {
  fromJSON(object: any): NotifyUserEnter {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      user: isSet(object.user) ? RoomUserInfo.fromJSON(object.user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyUserEnter>, I>>(base?: I): NotifyUserEnter {
    return NotifyUserEnter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUserEnter>, I>>(object: I): NotifyUserEnter {
    const message = createBaseNotifyUserEnter();
    message.timestamp = object.timestamp ?? 0;
    message.user =
      object.user !== undefined && object.user !== null ? RoomUserInfo.fromPartial(object.user) : undefined;
    return message;
  }
};

function createBaseNotifyUserLeave(): NotifyUserLeave {
  return { timestamp: 0, user: undefined };
}

export const NotifyUserLeave: MessageFns<NotifyUserLeave> = {
  fromJSON(object: any): NotifyUserLeave {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      user: isSet(object.user) ? RoomUserInfo.fromJSON(object.user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyUserLeave>, I>>(base?: I): NotifyUserLeave {
    return NotifyUserLeave.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUserLeave>, I>>(object: I): NotifyUserLeave {
    const message = createBaseNotifyUserLeave();
    message.timestamp = object.timestamp ?? 0;
    message.user =
      object.user !== undefined && object.user !== null ? RoomUserInfo.fromPartial(object.user) : undefined;
    return message;
  }
};

function createBaseNotifyOnlineUsers(): NotifyOnlineUsers {
  return { timestamp: 0, oneline_count: 0, users: [] };
}

export const NotifyOnlineUsers: MessageFns<NotifyOnlineUsers> = {
  fromJSON(object: any): NotifyOnlineUsers {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      oneline_count: isSet(object.oneline_count) ? globalThis.Number(object.oneline_count) : 0,
      users: globalThis.Array.isArray(object?.users) ? object.users.map((e: any) => RoomUserInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<NotifyOnlineUsers>, I>>(base?: I): NotifyOnlineUsers {
    return NotifyOnlineUsers.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyOnlineUsers>, I>>(object: I): NotifyOnlineUsers {
    const message = createBaseNotifyOnlineUsers();
    message.timestamp = object.timestamp ?? 0;
    message.oneline_count = object.oneline_count ?? 0;
    message.users = object.users?.map(e => RoomUserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotifyRoomInfoModify(): NotifyRoomInfoModify {
  return { timestamp: 0, room_info: undefined };
}

export const NotifyRoomInfoModify: MessageFns<NotifyRoomInfoModify> = {
  fromJSON(object: any): NotifyRoomInfoModify {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomInfoModify>, I>>(base?: I): NotifyRoomInfoModify {
    return NotifyRoomInfoModify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomInfoModify>, I>>(object: I): NotifyRoomInfoModify {
    const message = createBaseNotifyRoomInfoModify();
    message.timestamp = object.timestamp ?? 0;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBaseNotifyRoomLevelModify(): NotifyRoomLevelModify {
  return { timestamp: 0, room_level: undefined };
}

export const NotifyRoomLevelModify: MessageFns<NotifyRoomLevelModify> = {
  fromJSON(object: any): NotifyRoomLevelModify {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_level: isSet(object.room_level) ? RoomLevel.fromJSON(object.room_level) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomLevelModify>, I>>(base?: I): NotifyRoomLevelModify {
    return NotifyRoomLevelModify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomLevelModify>, I>>(object: I): NotifyRoomLevelModify {
    const message = createBaseNotifyRoomLevelModify();
    message.timestamp = object.timestamp ?? 0;
    message.room_level =
      object.room_level !== undefined && object.room_level !== null
        ? RoomLevel.fromPartial(object.room_level)
        : undefined;
    return message;
  }
};

function createBaseNotifyRoomSettingsModify(): NotifyRoomSettingsModify {
  return { timestamp: 0, room_settings: undefined };
}

export const NotifyRoomSettingsModify: MessageFns<NotifyRoomSettingsModify> = {
  fromJSON(object: any): NotifyRoomSettingsModify {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_settings: isSet(object.room_settings) ? RoomSettings.fromJSON(object.room_settings) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomSettingsModify>, I>>(base?: I): NotifyRoomSettingsModify {
    return NotifyRoomSettingsModify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomSettingsModify>, I>>(object: I): NotifyRoomSettingsModify {
    const message = createBaseNotifyRoomSettingsModify();
    message.timestamp = object.timestamp ?? 0;
    message.room_settings =
      object.room_settings !== undefined && object.room_settings !== null
        ? RoomSettings.fromPartial(object.room_settings)
        : undefined;
    return message;
  }
};

function createBaseNotifyUserRoleModify(): NotifyUserRoleModify {
  return { timestamp: 0, old_role: 0, user: undefined };
}

export const NotifyUserRoleModify: MessageFns<NotifyUserRoleModify> = {
  fromJSON(object: any): NotifyUserRoleModify {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      old_role: isSet(object.old_role) ? roomUserRoleFromJSON(object.old_role) : 0,
      user: isSet(object.user) ? RoomUserInfo.fromJSON(object.user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyUserRoleModify>, I>>(base?: I): NotifyUserRoleModify {
    return NotifyUserRoleModify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUserRoleModify>, I>>(object: I): NotifyUserRoleModify {
    const message = createBaseNotifyUserRoleModify();
    message.timestamp = object.timestamp ?? 0;
    message.old_role = object.old_role ?? 0;
    message.user =
      object.user !== undefined && object.user !== null ? RoomUserInfo.fromPartial(object.user) : undefined;
    return message;
  }
};

function createBaseNotifyKickUser(): NotifyKickUser {
  return { timestamp: 0, room_id: 0, kick_user: undefined };
}

export const NotifyKickUser: MessageFns<NotifyKickUser> = {
  fromJSON(object: any): NotifyKickUser {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      kick_user: isSet(object.kick_user) ? RoomUserInfo.fromJSON(object.kick_user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyKickUser>, I>>(base?: I): NotifyKickUser {
    return NotifyKickUser.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyKickUser>, I>>(object: I): NotifyKickUser {
    const message = createBaseNotifyKickUser();
    message.timestamp = object.timestamp ?? 0;
    message.room_id = object.room_id ?? 0;
    message.kick_user =
      object.kick_user !== undefined && object.kick_user !== null
        ? RoomUserInfo.fromPartial(object.kick_user)
        : undefined;
    return message;
  }
};

function createBaseNotifyRoomMessage(): NotifyRoomMessage {
  return { room_id: 0, message: undefined };
}

export const NotifyRoomMessage: MessageFns<NotifyRoomMessage> = {
  fromJSON(object: any): NotifyRoomMessage {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      message: isSet(object.message) ? Message.fromJSON(object.message) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomMessage>, I>>(base?: I): NotifyRoomMessage {
    return NotifyRoomMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomMessage>, I>>(object: I): NotifyRoomMessage {
    const message = createBaseNotifyRoomMessage();
    message.room_id = object.room_id ?? 0;
    message.message =
      object.message !== undefined && object.message !== null ? Message.fromPartial(object.message) : undefined;
    return message;
  }
};

function createBaseNotifySeatListChange(): NotifySeatListChange {
  return { seat_list: undefined };
}

export const NotifySeatListChange: MessageFns<NotifySeatListChange> = {
  fromJSON(object: any): NotifySeatListChange {
    return { seat_list: isSet(object.seat_list) ? SeatList.fromJSON(object.seat_list) : undefined };
  },

  create<I extends Exact<DeepPartial<NotifySeatListChange>, I>>(base?: I): NotifySeatListChange {
    return NotifySeatListChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySeatListChange>, I>>(object: I): NotifySeatListChange {
    const message = createBaseNotifySeatListChange();
    message.seat_list =
      object.seat_list !== undefined && object.seat_list !== null ? SeatList.fromPartial(object.seat_list) : undefined;
    return message;
  }
};

function createBaseNotifySeatInvitation(): NotifySeatInvitation {
  return { seat_index: 0, inviter: undefined, invitee: undefined };
}

export const NotifySeatInvitation: MessageFns<NotifySeatInvitation> = {
  fromJSON(object: any): NotifySeatInvitation {
    return {
      seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0,
      inviter: isSet(object.inviter) ? RoomUserInfo.fromJSON(object.inviter) : undefined,
      invitee: isSet(object.invitee) ? RoomUserInfo.fromJSON(object.invitee) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifySeatInvitation>, I>>(base?: I): NotifySeatInvitation {
    return NotifySeatInvitation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySeatInvitation>, I>>(object: I): NotifySeatInvitation {
    const message = createBaseNotifySeatInvitation();
    message.seat_index = object.seat_index ?? 0;
    message.inviter =
      object.inviter !== undefined && object.inviter !== null ? RoomUserInfo.fromPartial(object.inviter) : undefined;
    message.invitee =
      object.invitee !== undefined && object.invitee !== null ? RoomUserInfo.fromPartial(object.invitee) : undefined;
    return message;
  }
};

function createBaseNotifyKickSeat(): NotifyKickSeat {
  return { operator: undefined, target_user: undefined };
}

export const NotifyKickSeat: MessageFns<NotifyKickSeat> = {
  fromJSON(object: any): NotifyKickSeat {
    return {
      operator: isSet(object.operator) ? RoomUserInfo.fromJSON(object.operator) : undefined,
      target_user: isSet(object.target_user) ? RoomUserInfo.fromJSON(object.target_user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyKickSeat>, I>>(base?: I): NotifyKickSeat {
    return NotifyKickSeat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyKickSeat>, I>>(object: I): NotifyKickSeat {
    const message = createBaseNotifyKickSeat();
    message.operator =
      object.operator !== undefined && object.operator !== null ? RoomUserInfo.fromPartial(object.operator) : undefined;
    message.target_user =
      object.target_user !== undefined && object.target_user !== null
        ? RoomUserInfo.fromPartial(object.target_user)
        : undefined;
    return message;
  }
};

function createBaseNotifyApplyEnterSeat(): NotifyApplyEnterSeat {
  return { apply_count: 0 };
}

export const NotifyApplyEnterSeat: MessageFns<NotifyApplyEnterSeat> = {
  fromJSON(object: any): NotifyApplyEnterSeat {
    return { apply_count: isSet(object.apply_count) ? globalThis.Number(object.apply_count) : 0 };
  },

  create<I extends Exact<DeepPartial<NotifyApplyEnterSeat>, I>>(base?: I): NotifyApplyEnterSeat {
    return NotifyApplyEnterSeat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyApplyEnterSeat>, I>>(object: I): NotifyApplyEnterSeat {
    const message = createBaseNotifyApplyEnterSeat();
    message.apply_count = object.apply_count ?? 0;
    return message;
  }
};

function createBaseNotifyApproveApplyEnterSeat(): NotifyApproveApplyEnterSeat {
  return { seat_index: 0 };
}

export const NotifyApproveApplyEnterSeat: MessageFns<NotifyApproveApplyEnterSeat> = {
  fromJSON(object: any): NotifyApproveApplyEnterSeat {
    return { seat_index: isSet(object.seat_index) ? globalThis.Number(object.seat_index) : 0 };
  },

  create<I extends Exact<DeepPartial<NotifyApproveApplyEnterSeat>, I>>(base?: I): NotifyApproveApplyEnterSeat {
    return NotifyApproveApplyEnterSeat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyApproveApplyEnterSeat>, I>>(object: I): NotifyApproveApplyEnterSeat {
    const message = createBaseNotifyApproveApplyEnterSeat();
    message.seat_index = object.seat_index ?? 0;
    return message;
  }
};

function createBaseNotifyMuteMic(): NotifyMuteMic {
  return { operator: undefined, target_user: undefined, mic_disabled: false };
}

export const NotifyMuteMic: MessageFns<NotifyMuteMic> = {
  fromJSON(object: any): NotifyMuteMic {
    return {
      operator: isSet(object.operator) ? RoomUserInfo.fromJSON(object.operator) : undefined,
      target_user: isSet(object.target_user) ? RoomUserInfo.fromJSON(object.target_user) : undefined,
      mic_disabled: isSet(object.mic_disabled) ? globalThis.Boolean(object.mic_disabled) : false
    };
  },

  create<I extends Exact<DeepPartial<NotifyMuteMic>, I>>(base?: I): NotifyMuteMic {
    return NotifyMuteMic.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyMuteMic>, I>>(object: I): NotifyMuteMic {
    const message = createBaseNotifyMuteMic();
    message.operator =
      object.operator !== undefined && object.operator !== null ? RoomUserInfo.fromPartial(object.operator) : undefined;
    message.target_user =
      object.target_user !== undefined && object.target_user !== null
        ? RoomUserInfo.fromPartial(object.target_user)
        : undefined;
    message.mic_disabled = object.mic_disabled ?? false;
    return message;
  }
};

function createBaseNotifyLiveEnd(): NotifyLiveEnd {
  return { timestamp: 0, room_id: 0, owner_uid: 0 };
}

export const NotifyLiveEnd: MessageFns<NotifyLiveEnd> = {
  fromJSON(object: any): NotifyLiveEnd {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyLiveEnd>, I>>(base?: I): NotifyLiveEnd {
    return NotifyLiveEnd.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyLiveEnd>, I>>(object: I): NotifyLiveEnd {
    const message = createBaseNotifyLiveEnd();
    message.timestamp = object.timestamp ?? 0;
    message.room_id = object.room_id ?? 0;
    message.owner_uid = object.owner_uid ?? 0;
    return message;
  }
};

function createBaseNotifyRoomModeModify(): NotifyRoomModeModify {
  return { room_state_info: undefined };
}

export const NotifyRoomModeModify: MessageFns<NotifyRoomModeModify> = {
  fromJSON(object: any): NotifyRoomModeModify {
    return {
      room_state_info: isSet(object.room_state_info) ? RoomStateInfo.fromJSON(object.room_state_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomModeModify>, I>>(base?: I): NotifyRoomModeModify {
    return NotifyRoomModeModify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomModeModify>, I>>(object: I): NotifyRoomModeModify {
    const message = createBaseNotifyRoomModeModify();
    message.room_state_info =
      object.room_state_info !== undefined && object.room_state_info !== null
        ? RoomStateInfo.fromPartial(object.room_state_info)
        : undefined;
    return message;
  }
};

function createBaseNotifyUserScoreListChange(): NotifyUserScoreListChange {
  return { user_score_list: undefined };
}

export const NotifyUserScoreListChange: MessageFns<NotifyUserScoreListChange> = {
  fromJSON(object: any): NotifyUserScoreListChange {
    return {
      user_score_list: isSet(object.user_score_list) ? UserScoreList.fromJSON(object.user_score_list) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyUserScoreListChange>, I>>(base?: I): NotifyUserScoreListChange {
    return NotifyUserScoreListChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUserScoreListChange>, I>>(object: I): NotifyUserScoreListChange {
    const message = createBaseNotifyUserScoreListChange();
    message.user_score_list =
      object.user_score_list !== undefined && object.user_score_list !== null
        ? UserScoreList.fromPartial(object.user_score_list)
        : undefined;
    return message;
  }
};

function createBaseNotifyUserScoreSwitchChange(): NotifyUserScoreSwitchChange {
  return { timestamp: 0, switch: 0 };
}

export const NotifyUserScoreSwitchChange: MessageFns<NotifyUserScoreSwitchChange> = {
  fromJSON(object: any): NotifyUserScoreSwitchChange {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      switch: isSet(object.switch) ? seatScoreSwitchFromJSON(object.switch) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyUserScoreSwitchChange>, I>>(base?: I): NotifyUserScoreSwitchChange {
    return NotifyUserScoreSwitchChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUserScoreSwitchChange>, I>>(object: I): NotifyUserScoreSwitchChange {
    const message = createBaseNotifyUserScoreSwitchChange();
    message.timestamp = object.timestamp ?? 0;
    message.switch = object.switch ?? 0;
    return message;
  }
};

function createBaseNotifyRechargeBalanceSuccess(): NotifyRechargeBalanceSuccess {
  return { coin: 0 };
}

export const NotifyRechargeBalanceSuccess: MessageFns<NotifyRechargeBalanceSuccess> = {
  fromJSON(object: any): NotifyRechargeBalanceSuccess {
    return { coin: isSet(object.coin) ? globalThis.Number(object.coin) : 0 };
  },

  create<I extends Exact<DeepPartial<NotifyRechargeBalanceSuccess>, I>>(base?: I): NotifyRechargeBalanceSuccess {
    return NotifyRechargeBalanceSuccess.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRechargeBalanceSuccess>, I>>(object: I): NotifyRechargeBalanceSuccess {
    const message = createBaseNotifyRechargeBalanceSuccess();
    message.coin = object.coin ?? 0;
    return message;
  }
};

function createBaseNotifyGameInfo(): NotifyGameInfo {
  return { deep_link: '', game_pic: '', game_id: '' };
}

export const NotifyGameInfo: MessageFns<NotifyGameInfo> = {
  fromJSON(object: any): NotifyGameInfo {
    return {
      deep_link: isSet(object.deep_link) ? globalThis.String(object.deep_link) : '',
      game_pic: isSet(object.game_pic) ? globalThis.String(object.game_pic) : '',
      game_id: isSet(object.game_id) ? globalThis.String(object.game_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifyGameInfo>, I>>(base?: I): NotifyGameInfo {
    return NotifyGameInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyGameInfo>, I>>(object: I): NotifyGameInfo {
    const message = createBaseNotifyGameInfo();
    message.deep_link = object.deep_link ?? '';
    message.game_pic = object.game_pic ?? '';
    message.game_id = object.game_id ?? '';
    return message;
  }
};

function createBaseNotifySendGift(): NotifySendGift {
  return { from_user: undefined, to_users: [], gift: undefined, nums: 0 };
}

export const NotifySendGift: MessageFns<NotifySendGift> = {
  fromJSON(object: any): NotifySendGift {
    return {
      from_user: isSet(object.from_user) ? UserInfo.fromJSON(object.from_user) : undefined,
      to_users: globalThis.Array.isArray(object?.to_users) ? object.to_users.map((e: any) => UserInfo.fromJSON(e)) : [],
      gift: isSet(object.gift) ? Gift.fromJSON(object.gift) : undefined,
      nums: isSet(object.nums) ? globalThis.Number(object.nums) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifySendGift>, I>>(base?: I): NotifySendGift {
    return NotifySendGift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySendGift>, I>>(object: I): NotifySendGift {
    const message = createBaseNotifySendGift();
    message.from_user =
      object.from_user !== undefined && object.from_user !== null ? UserInfo.fromPartial(object.from_user) : undefined;
    message.to_users = object.to_users?.map(e => UserInfo.fromPartial(e)) || [];
    message.gift = object.gift !== undefined && object.gift !== null ? Gift.fromPartial(object.gift) : undefined;
    message.nums = object.nums ?? 0;
    return message;
  }
};

function createBaseGift(): Gift {
  return { gift_id: 0, gift_name: '', img_url: '', resource_type: 0, resource_url: '' };
}

export const Gift: MessageFns<Gift> = {
  fromJSON(object: any): Gift {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      gift_name: isSet(object.gift_name) ? globalThis.String(object.gift_name) : '',
      img_url: isSet(object.img_url) ? globalThis.String(object.img_url) : '',
      resource_type: isSet(object.resource_type) ? giftResourceTypeFromJSON(object.resource_type) : 0,
      resource_url: isSet(object.resource_url) ? globalThis.String(object.resource_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<Gift>, I>>(base?: I): Gift {
    return Gift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Gift>, I>>(object: I): Gift {
    const message = createBaseGift();
    message.gift_id = object.gift_id ?? 0;
    message.gift_name = object.gift_name ?? '';
    message.img_url = object.img_url ?? '';
    message.resource_type = object.resource_type ?? 0;
    message.resource_url = object.resource_url ?? '';
    return message;
  }
};

function createBaseNotifyInviteSendGift(): NotifyInviteSendGift {
  return { gift: undefined, from_user: undefined, nums: 0 };
}

export const NotifyInviteSendGift: MessageFns<NotifyInviteSendGift> = {
  fromJSON(object: any): NotifyInviteSendGift {
    return {
      gift: isSet(object.gift) ? Gift.fromJSON(object.gift) : undefined,
      from_user: isSet(object.from_user) ? UserInfo.fromJSON(object.from_user) : undefined,
      nums: isSet(object.nums) ? globalThis.Number(object.nums) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyInviteSendGift>, I>>(base?: I): NotifyInviteSendGift {
    return NotifyInviteSendGift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyInviteSendGift>, I>>(object: I): NotifyInviteSendGift {
    const message = createBaseNotifyInviteSendGift();
    message.gift = object.gift !== undefined && object.gift !== null ? Gift.fromPartial(object.gift) : undefined;
    message.from_user =
      object.from_user !== undefined && object.from_user !== null ? UserInfo.fromPartial(object.from_user) : undefined;
    message.nums = object.nums ?? 0;
    return message;
  }
};

function createBaseNotifyMuteRoomMsg(): NotifyMuteRoomMsg {
  return { room_id: 0 };
}

export const NotifyMuteRoomMsg: MessageFns<NotifyMuteRoomMsg> = {
  fromJSON(object: any): NotifyMuteRoomMsg {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<NotifyMuteRoomMsg>, I>>(base?: I): NotifyMuteRoomMsg {
    return NotifyMuteRoomMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyMuteRoomMsg>, I>>(object: I): NotifyMuteRoomMsg {
    const message = createBaseNotifyMuteRoomMsg();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseNotifyUnMuteRoomMsg(): NotifyUnMuteRoomMsg {
  return { room_id: 0 };
}

export const NotifyUnMuteRoomMsg: MessageFns<NotifyUnMuteRoomMsg> = {
  fromJSON(object: any): NotifyUnMuteRoomMsg {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<NotifyUnMuteRoomMsg>, I>>(base?: I): NotifyUnMuteRoomMsg {
    return NotifyUnMuteRoomMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyUnMuteRoomMsg>, I>>(object: I): NotifyUnMuteRoomMsg {
    const message = createBaseNotifyUnMuteRoomMsg();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseNotifySquarePopUpWindowMsg(): NotifySquarePopUpWindowMsg {
  return {
    title: '',
    content: '',
    image_url: '',
    event_id: '',
    scene: 0,
    button_text: '',
    is_show_close_button: false,
    free_game_reward_notify: undefined,
    pop_up_h5_info: undefined
  };
}

export const NotifySquarePopUpWindowMsg: MessageFns<NotifySquarePopUpWindowMsg> = {
  fromJSON(object: any): NotifySquarePopUpWindowMsg {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      image_url: isSet(object.image_url) ? globalThis.String(object.image_url) : '',
      event_id: isSet(object.event_id) ? globalThis.String(object.event_id) : '',
      scene: isSet(object.scene) ? squarePopUpWindowSceneTypeFromJSON(object.scene) : 0,
      button_text: isSet(object.button_text) ? globalThis.String(object.button_text) : '',
      is_show_close_button: isSet(object.is_show_close_button)
        ? globalThis.Boolean(object.is_show_close_button)
        : false,
      free_game_reward_notify: isSet(object.free_game_reward_notify)
        ? NotifyFreeGameRewardEntrance.fromJSON(object.free_game_reward_notify)
        : undefined,
      pop_up_h5_info: isSet(object.pop_up_h5_info) ? NotifyPopUpDeepLinkInfo.fromJSON(object.pop_up_h5_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifySquarePopUpWindowMsg>, I>>(base?: I): NotifySquarePopUpWindowMsg {
    return NotifySquarePopUpWindowMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySquarePopUpWindowMsg>, I>>(object: I): NotifySquarePopUpWindowMsg {
    const message = createBaseNotifySquarePopUpWindowMsg();
    message.title = object.title ?? '';
    message.content = object.content ?? '';
    message.image_url = object.image_url ?? '';
    message.event_id = object.event_id ?? '';
    message.scene = object.scene ?? 0;
    message.button_text = object.button_text ?? '';
    message.is_show_close_button = object.is_show_close_button ?? false;
    message.free_game_reward_notify =
      object.free_game_reward_notify !== undefined && object.free_game_reward_notify !== null
        ? NotifyFreeGameRewardEntrance.fromPartial(object.free_game_reward_notify)
        : undefined;
    message.pop_up_h5_info =
      object.pop_up_h5_info !== undefined && object.pop_up_h5_info !== null
        ? NotifyPopUpDeepLinkInfo.fromPartial(object.pop_up_h5_info)
        : undefined;
    return message;
  }
};

function createBaseNotifyVoicePopUpWindowMsg(): NotifyVoicePopUpWindowMsg {
  return {
    title: '',
    content: '',
    image_url: '',
    event_id: '',
    scene: 0,
    button_text: '',
    is_show_close_button: false,
    free_game_reward_notify: undefined
  };
}

export const NotifyVoicePopUpWindowMsg: MessageFns<NotifyVoicePopUpWindowMsg> = {
  fromJSON(object: any): NotifyVoicePopUpWindowMsg {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      image_url: isSet(object.image_url) ? globalThis.String(object.image_url) : '',
      event_id: isSet(object.event_id) ? globalThis.String(object.event_id) : '',
      scene: isSet(object.scene) ? squarePopUpWindowSceneTypeFromJSON(object.scene) : 0,
      button_text: isSet(object.button_text) ? globalThis.String(object.button_text) : '',
      is_show_close_button: isSet(object.is_show_close_button)
        ? globalThis.Boolean(object.is_show_close_button)
        : false,
      free_game_reward_notify: isSet(object.free_game_reward_notify)
        ? NotifyFreeGameRewardEntrance.fromJSON(object.free_game_reward_notify)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifyVoicePopUpWindowMsg>, I>>(base?: I): NotifyVoicePopUpWindowMsg {
    return NotifyVoicePopUpWindowMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyVoicePopUpWindowMsg>, I>>(object: I): NotifyVoicePopUpWindowMsg {
    const message = createBaseNotifyVoicePopUpWindowMsg();
    message.title = object.title ?? '';
    message.content = object.content ?? '';
    message.image_url = object.image_url ?? '';
    message.event_id = object.event_id ?? '';
    message.scene = object.scene ?? 0;
    message.button_text = object.button_text ?? '';
    message.is_show_close_button = object.is_show_close_button ?? false;
    message.free_game_reward_notify =
      object.free_game_reward_notify !== undefined && object.free_game_reward_notify !== null
        ? NotifyFreeGameRewardEntrance.fromPartial(object.free_game_reward_notify)
        : undefined;
    return message;
  }
};

function createBaseNotifyFreeGameRewardEntrance(): NotifyFreeGameRewardEntrance {
  return { expired_ts: 0, free_game_coin: 0, free_game_pic: '', tip: '', show_entrance: 0, receive_id: 0 };
}

export const NotifyFreeGameRewardEntrance: MessageFns<NotifyFreeGameRewardEntrance> = {
  fromJSON(object: any): NotifyFreeGameRewardEntrance {
    return {
      expired_ts: isSet(object.expired_ts) ? globalThis.Number(object.expired_ts) : 0,
      free_game_coin: isSet(object.free_game_coin) ? globalThis.Number(object.free_game_coin) : 0,
      free_game_pic: isSet(object.free_game_pic) ? globalThis.String(object.free_game_pic) : '',
      tip: isSet(object.tip) ? globalThis.String(object.tip) : '',
      show_entrance: isSet(object.show_entrance) ? globalThis.Number(object.show_entrance) : 0,
      receive_id: isSet(object.receive_id) ? globalThis.Number(object.receive_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyFreeGameRewardEntrance>, I>>(base?: I): NotifyFreeGameRewardEntrance {
    return NotifyFreeGameRewardEntrance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyFreeGameRewardEntrance>, I>>(object: I): NotifyFreeGameRewardEntrance {
    const message = createBaseNotifyFreeGameRewardEntrance();
    message.expired_ts = object.expired_ts ?? 0;
    message.free_game_coin = object.free_game_coin ?? 0;
    message.free_game_pic = object.free_game_pic ?? '';
    message.tip = object.tip ?? '';
    message.show_entrance = object.show_entrance ?? 0;
    message.receive_id = object.receive_id ?? 0;
    return message;
  }
};

function createBaseNotifyPopUpDeepLinkInfo(): NotifyPopUpDeepLinkInfo {
  return { width: 0, height: 0, deep_link: '' };
}

export const NotifyPopUpDeepLinkInfo: MessageFns<NotifyPopUpDeepLinkInfo> = {
  fromJSON(object: any): NotifyPopUpDeepLinkInfo {
    return {
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      deep_link: isSet(object.deep_link) ? globalThis.String(object.deep_link) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifyPopUpDeepLinkInfo>, I>>(base?: I): NotifyPopUpDeepLinkInfo {
    return NotifyPopUpDeepLinkInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyPopUpDeepLinkInfo>, I>>(object: I): NotifyPopUpDeepLinkInfo {
    const message = createBaseNotifyPopUpDeepLinkInfo();
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.deep_link = object.deep_link ?? '';
    return message;
  }
};

function createBaseNotifyRoomConsumptionIncomeInfoMsg(): NotifyRoomConsumptionIncomeInfoMsg {
  return { consume_coins: 0, revenue_points: 0 };
}

export const NotifyRoomConsumptionIncomeInfoMsg: MessageFns<NotifyRoomConsumptionIncomeInfoMsg> = {
  fromJSON(object: any): NotifyRoomConsumptionIncomeInfoMsg {
    return {
      consume_coins: isSet(object.consume_coins) ? globalThis.Number(object.consume_coins) : 0,
      revenue_points: isSet(object.revenue_points) ? globalThis.Number(object.revenue_points) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyRoomConsumptionIncomeInfoMsg>, I>>(
    base?: I
  ): NotifyRoomConsumptionIncomeInfoMsg {
    return NotifyRoomConsumptionIncomeInfoMsg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyRoomConsumptionIncomeInfoMsg>, I>>(
    object: I
  ): NotifyRoomConsumptionIncomeInfoMsg {
    const message = createBaseNotifyRoomConsumptionIncomeInfoMsg();
    message.consume_coins = object.consume_coins ?? 0;
    message.revenue_points = object.revenue_points ?? 0;
    return message;
  }
};

function createBaseNotifyLiveStatusChange(): NotifyLiveStatusChange {
  return { timestamp: 0, room_id: 0, live_id: 0, live_status: 0 };
}

export const NotifyLiveStatusChange: MessageFns<NotifyLiveStatusChange> = {
  fromJSON(object: any): NotifyLiveStatusChange {
    return {
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0,
      live_status: isSet(object.live_status) ? liveStatusFromJSON(object.live_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<NotifyLiveStatusChange>, I>>(base?: I): NotifyLiveStatusChange {
    return NotifyLiveStatusChange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyLiveStatusChange>, I>>(object: I): NotifyLiveStatusChange {
    const message = createBaseNotifyLiveStatusChange();
    message.timestamp = object.timestamp ?? 0;
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    message.live_status = object.live_status ?? 0;
    return message;
  }
};

function createBaseNotifyFollow(): NotifyFollow {
  return { user: undefined };
}

export const NotifyFollow: MessageFns<NotifyFollow> = {
  fromJSON(object: any): NotifyFollow {
    return { user: isSet(object.user) ? RoomUserInfo.fromJSON(object.user) : undefined };
  },

  create<I extends Exact<DeepPartial<NotifyFollow>, I>>(base?: I): NotifyFollow {
    return NotifyFollow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyFollow>, I>>(object: I): NotifyFollow {
    const message = createBaseNotifyFollow();
    message.user =
      object.user !== undefined && object.user !== null ? RoomUserInfo.fromPartial(object.user) : undefined;
    return message;
  }
};

function createBaseNotifyWishGiftList(): NotifyWishGiftList {
  return { gift_wish_list_data: [] };
}

export const NotifyWishGiftList: MessageFns<NotifyWishGiftList> = {
  fromJSON(object: any): NotifyWishGiftList {
    return {
      gift_wish_list_data: globalThis.Array.isArray(object?.gift_wish_list_data)
        ? object.gift_wish_list_data.map((e: any) => GiftWishlistData.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<NotifyWishGiftList>, I>>(base?: I): NotifyWishGiftList {
    return NotifyWishGiftList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyWishGiftList>, I>>(object: I): NotifyWishGiftList {
    const message = createBaseNotifyWishGiftList();
    message.gift_wish_list_data = object.gift_wish_list_data?.map(e => GiftWishlistData.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotifyGiftWishlistRsp(): NotifyGiftWishlistRsp {
  return { gift_wish_rsp: undefined };
}

export const NotifyGiftWishlistRsp: MessageFns<NotifyGiftWishlistRsp> = {
  fromJSON(object: any): NotifyGiftWishlistRsp {
    return { gift_wish_rsp: isSet(object.gift_wish_rsp) ? GiftWishlistRsp.fromJSON(object.gift_wish_rsp) : undefined };
  },

  create<I extends Exact<DeepPartial<NotifyGiftWishlistRsp>, I>>(base?: I): NotifyGiftWishlistRsp {
    return NotifyGiftWishlistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyGiftWishlistRsp>, I>>(object: I): NotifyGiftWishlistRsp {
    const message = createBaseNotifyGiftWishlistRsp();
    message.gift_wish_rsp =
      object.gift_wish_rsp !== undefined && object.gift_wish_rsp !== null
        ? GiftWishlistRsp.fromPartial(object.gift_wish_rsp)
        : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
