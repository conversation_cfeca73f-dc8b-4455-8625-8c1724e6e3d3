// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/anchor.proto

/* eslint-disable */
import { Page } from '../protobuf/api/common/common';

export const protobufPackage = 'anchor';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/anchorsrv */

export enum RegisterPopUpType {
  RegisterPopUp_None = 0,
  /** RegisterPopUp_Welcome - 欢迎页弹窗 */
  RegisterPopUp_Welcome = 1,
  /** RegisterPopUp_Verify_Account - 真人认证弹窗 */
  RegisterPopUp_Verify_Account = 2,
  /** RegisterPopUp_Code - 邀请码弹窗 */
  RegisterPopUp_Code = 3,
  /** RegisterPopUp_Anchor_Certification - 主播认证弹窗 */
  RegisterPopUp_Anchor_Certification = 4,
  UNRECOGNIZED = -1
}

export function registerPopUpTypeFromJSON(object: any): RegisterPopUpType {
  switch (object) {
    case 0:
    case 'RegisterPopUp_None':
      return RegisterPopUpType.RegisterPopUp_None;
    case 1:
    case 'RegisterPopUp_Welcome':
      return RegisterPopUpType.RegisterPopUp_Welcome;
    case 2:
    case 'RegisterPopUp_Verify_Account':
      return RegisterPopUpType.RegisterPopUp_Verify_Account;
    case 3:
    case 'RegisterPopUp_Code':
      return RegisterPopUpType.RegisterPopUp_Code;
    case 4:
    case 'RegisterPopUp_Anchor_Certification':
      return RegisterPopUpType.RegisterPopUp_Anchor_Certification;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RegisterPopUpType.UNRECOGNIZED;
  }
}

export enum IncomeListType {
  INCOME_LIST_TYPE_NONE = 0,
  /** INCOME_LIST_TYPE_ANCHOR - 主播 */
  INCOME_LIST_TYPE_ANCHOR = 1,
  /** INCOME_LIST_TYPE_SUB_AGENCY - 二级公会 */
  INCOME_LIST_TYPE_SUB_AGENCY = 2,
  UNRECOGNIZED = -1
}

export function incomeListTypeFromJSON(object: any): IncomeListType {
  switch (object) {
    case 0:
    case 'INCOME_LIST_TYPE_NONE':
      return IncomeListType.INCOME_LIST_TYPE_NONE;
    case 1:
    case 'INCOME_LIST_TYPE_ANCHOR':
      return IncomeListType.INCOME_LIST_TYPE_ANCHOR;
    case 2:
    case 'INCOME_LIST_TYPE_SUB_AGENCY':
      return IncomeListType.INCOME_LIST_TYPE_SUB_AGENCY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return IncomeListType.UNRECOGNIZED;
  }
}

export enum ViolationListType {
  VIOLATION_LIST_TYPE_NONE = 0,
  VIOLATION_LIST_TYPE_REDUCE_POINTS = 1,
  VIOLATION_LIST_TYPE_ACCOUNT_BAN = 2,
  VIOLATION_LIST_TYPE_DELETE = 3,
  UNRECOGNIZED = -1
}

export function violationListTypeFromJSON(object: any): ViolationListType {
  switch (object) {
    case 0:
    case 'VIOLATION_LIST_TYPE_NONE':
      return ViolationListType.VIOLATION_LIST_TYPE_NONE;
    case 1:
    case 'VIOLATION_LIST_TYPE_REDUCE_POINTS':
      return ViolationListType.VIOLATION_LIST_TYPE_REDUCE_POINTS;
    case 2:
    case 'VIOLATION_LIST_TYPE_ACCOUNT_BAN':
      return ViolationListType.VIOLATION_LIST_TYPE_ACCOUNT_BAN;
    case 3:
    case 'VIOLATION_LIST_TYPE_DELETE':
      return ViolationListType.VIOLATION_LIST_TYPE_DELETE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ViolationListType.UNRECOGNIZED;
  }
}

export enum AnchorIncomeTimeRange {
  Income_Range_None = 0,
  Income_Range_Today = 1,
  Income_Range_Yesterday = 2,
  Income_Range_Week = 3,
  UNRECOGNIZED = -1
}

export function anchorIncomeTimeRangeFromJSON(object: any): AnchorIncomeTimeRange {
  switch (object) {
    case 0:
    case 'Income_Range_None':
      return AnchorIncomeTimeRange.Income_Range_None;
    case 1:
    case 'Income_Range_Today':
      return AnchorIncomeTimeRange.Income_Range_Today;
    case 2:
    case 'Income_Range_Yesterday':
      return AnchorIncomeTimeRange.Income_Range_Yesterday;
    case 3:
    case 'Income_Range_Week':
      return AnchorIncomeTimeRange.Income_Range_Week;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AnchorIncomeTimeRange.UNRECOGNIZED;
  }
}

export enum EditPermission {
  EditPermission_None = 0,
  /** EditPermission_Avatar - 判断是否可以修改头像 */
  EditPermission_Avatar = 1,
  /** EditPermission_Video - 判断是否可以修改视频 */
  EditPermission_Video = 2,
  /** EditPermission_Photo - 判断是否可以修改相册 */
  EditPermission_Photo = 3,
  UNRECOGNIZED = -1
}

export function editPermissionFromJSON(object: any): EditPermission {
  switch (object) {
    case 0:
    case 'EditPermission_None':
      return EditPermission.EditPermission_None;
    case 1:
    case 'EditPermission_Avatar':
      return EditPermission.EditPermission_Avatar;
    case 2:
    case 'EditPermission_Video':
      return EditPermission.EditPermission_Video;
    case 3:
    case 'EditPermission_Photo':
      return EditPermission.EditPermission_Photo;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return EditPermission.UNRECOGNIZED;
  }
}

export interface GetAnchorInfoReq {}

export interface GetAnchorInfoResp {
  had_video: number;
  show_video: string;
  show_video_pic: string;
  /** 头像地址 */
  avatar: string;
  /** 头像是否检验通过 */
  had_avatar: number;
  nickname: string;
  /** 主播自我介绍状态，0:还未主播认证，1:待审核，2:通过，3:拒绝 */
  verify_anchor: number;
  level: number;
  point: string;
  avatar_big: string;
  avatar_icon: string;
  /** 语言 */
  lan: string;
  /** 用户经验 */
  exp: number;
  /** 用户无效积分 */
  invalid_point: number;
  subanm: string;
  video_reco: number;
  photos: string[];
  /** 主播分群等级 */
  group_level: string;
  /** 公会码状态 */
  code_status: number;
  /** 真人认证状态 */
  verify_status: string;
  /** 年龄 */
  age: string;
  /** 最近10通付费平均时长 */
  recent_chat_avg_time: number;
  /** 相册审核状态 1待审核 2已通过 3已拒绝 */
  photo_verify_status: number;
  /** chat price */
  video_income: number;
  utype: string;
  /** komi包主播等级 */
  grade: string;
}

export interface UploadAnchorAudioReq {
  /** 待上传:uploading,已上传:uploaded */
  action: string;
  /** 音频上传路径，上传后第二次调接口透传回来 */
  path: string;
  /** 音频时长 */
  duration: number;
  /** 文件路径 */
  pool_path: string;
}

export interface UploadAnchorAudioResp {
  /** 上传路径 */
  path: string;
  /** 文件路径 */
  pool_path: string;
}

export interface GetAudioTemplateReq {}

export interface GetAudioTemplateResp {
  template_en: string[];
  template_hi: string[];
}

export interface GetVideoCallIncomeReq {}

export interface GetVideoCallIncomeResp {
  current_grade: string;
  current_income: number;
  grade_info: GetVideoCallIncomeResp_GradeInfo[];
}

export interface GetVideoCallIncomeResp_GradeInfo {
  grade: string;
  income: number;
  sort: number;
}

export interface UpdateVideoCallIncomeReq {
  income: number;
}

export interface UpdateVideoCallIncomeResp {}

export interface GetAnchorLevelConfigReq {}

export interface GetAnchorLevelConfigResp {
  config: AnchorLevelConfig[];
}

export interface AnchorLevelConfig {
  grade: string;
  min_point: number;
  max_point: number;
  chat_avg_time: number;
  point_income: number;
}

export interface GetAnchorLevelInfoReq {}

export interface GetAnchorLevelInfoResp {
  uid: string;
  nickname: string;
  avatar: string;
  /** 当前主播等级 */
  current_level: string;
  /** 收益 */
  profit: number;
  /** 平均通话时长 */
  chat_avg_time: number;
}

export interface GetAnchorRegisterPopUpReq {}

export interface GetAnchorRegisterPopUpResp {
  pop_up_type: RegisterPopUpType;
  /** 欢迎页弹窗述职配置 */
  welcome_info: WelcomePopUpInfo | undefined;
}

export interface WelcomePopUpInfo {
  /** 每分钟 积分获取范围  750-1800 */
  per_min_point: string;
  /** 70% */
  gift_percent: string;
  /** 20 */
  im_message_point: string;
  /** 每日多少用户 */
  new_user_day: string;
}

export interface GetAnchorJobDetailReq {}

export interface GetAnchorJobDetailResp {
  /** 秒 */
  today_avg_pay_call_time: number;
  /** 秒 */
  today_online_time: number;
  /** 次数 */
  today_call_num: number;
  /** 错过的电话数 */
  today_miss_call_num: number;
  /** komi包每日收入 */
  today_income: number;
  /** komi包主播等级 */
  grade: string;
  /** 主播积分余额 */
  point: number;
  /** 最近一次更新时间 */
  update_time: number;
}

export interface GetGuildMasterInfoReq {}

export interface GetGuildMasterInfoResp {
  total_commission: number;
  anchor_commission: number;
  sub_agency_commission: number;
  commission_ratio: number;
  last_update_times: number;
  nickname: string;
  invite_code: string;
  next_commission_ratio: number;
  next_level_points: number;
  contact_info: string;
  current_points: number;
}

export interface GetAnchorListReq {
  page: Page | undefined;
  datetime: number;
}

export interface GetAnchorListResp {
  page: Page | undefined;
  list: GetAnchorListResp_AnchorInfo[];
}

export interface GetAnchorListResp_AnchorInfo {
  uid: string;
  avatar: string;
  nickname: string;
  contact_information: string;
  become_time: number;
  last_active_time: number;
  is_ban: boolean;
  is_delete: boolean;
}

export interface GetIncomeListReq {
  page: Page | undefined;
  income_type: IncomeListType;
  business_id: string;
  /** 开始时间 */
  start_datetime: number;
  /** 结束时间 */
  end_datetime: number;
  /** 周期的周一 (查二级公会收入) */
  week_day: number;
}

export interface GetIncomeListResp {
  page: Page | undefined;
  list: GetIncomeListResp_IncomeItem[];
  last_update_time: number;
  week_total_income: number;
}

export interface GetIncomeListResp_IncomeItem {
  id: string;
  avatar: string;
  name: string;
  total_income: number;
  today_income: number;
  this_week_income: number;
  last_week_income: number;
  is_ban: boolean;
  is_delete: boolean;
}

export interface GetAnchorViolationListReq {
  page: Page | undefined;
}

export interface GetAnchorViolationListResp {
  page: Page | undefined;
  list: GetAnchorViolationListResp_ViolationItem[];
}

export interface GetAnchorViolationListResp_ViolationItem {
  uid: string;
  name: string;
  avatar: string;
  punish_status: ViolationListType;
  this_week_violation: number;
  punish_time: number;
  is_ban: boolean;
  is_delete: boolean;
}

export interface GetSubAgencyListReq {
  page: Page | undefined;
  datetime: number;
}

export interface GetSubAgencyListResp {
  page: Page | undefined;
  list: GetSubAgencyListResp_SubAgencyItem[];
}

export interface GetSubAgencyListResp_SubAgencyItem {
  id: string;
  name: string;
  whatsapp: string;
  join_time: number;
  total_anchor: number;
  active_anchor: number;
  is_expired: boolean;
}

export interface GetMyTotalIncomeReq {
  page: Page | undefined;
}

export interface GetMyTotalIncomeResp {
  list: GetMyTotalIncomeResp_TotalIncomeItem[];
  page: Page | undefined;
}

export interface GetMyTotalIncomeResp_TotalIncomeItem {
  /** 周期 */
  day_week: number;
  total_commission: number;
  anchor_commission: number;
  sub_agency_commission: number;
  commision_ratio: number;
  number_of_earning_anchor: number;
  anchor_paid_income: number;
}

export interface GetAnchorPointFlowsReq {
  start_time: number;
  end_time: number;
  category: string;
  page: number;
  size: number;
}

export interface GetAnchorPointFlowsResp {
  list: PointFlowInfo[];
}

export interface PointFlowInfo {
  category: string;
  ctime: number;
  chg: number;
}

export interface GetAnchorIncomeInfoReq {
  time_range: AnchorIncomeTimeRange;
}

export interface GetAnchorIncomeInfoResp {
  /** 总收入 */
  total_income: number;
  /** 视频收入 */
  video_income: number;
  /** 聊天收入 */
  chat_income: number;
  /** 礼物收入 */
  gift_income: number;
  /** 最近一次更新时间 */
  update_time: number;
}

export interface CheckAnchorEditPermissionReq {
  permission: EditPermission;
}

export interface CheckAnchorEditPermissionResp {}

export interface GetMyGuildInfoReq {}

export interface GetMyGuildInfoResp {
  guild_name: string;
  guild_master_name: string;
  guild_uid: string;
  guild_contact_info: string;
  guild_avatar: string;
}

export interface GetMyContactInfoReq {}

export interface GetMyContactInfoResp {
  whatsapp: string;
  google_mail: string;
}

export interface AuthChangeAvatarReq {}

export interface AuthChangeAvatarResp {}

function createBaseGetAnchorInfoReq(): GetAnchorInfoReq {
  return {};
}

export const GetAnchorInfoReq: MessageFns<GetAnchorInfoReq> = {
  fromJSON(_: any): GetAnchorInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorInfoReq>, I>>(base?: I): GetAnchorInfoReq {
    return GetAnchorInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorInfoReq>, I>>(_: I): GetAnchorInfoReq {
    const message = createBaseGetAnchorInfoReq();
    return message;
  }
};

function createBaseGetAnchorInfoResp(): GetAnchorInfoResp {
  return {
    had_video: 0,
    show_video: '',
    show_video_pic: '',
    avatar: '',
    had_avatar: 0,
    nickname: '',
    verify_anchor: 0,
    level: 0,
    point: '',
    avatar_big: '',
    avatar_icon: '',
    lan: '',
    exp: 0,
    invalid_point: 0,
    subanm: '',
    video_reco: 0,
    photos: [],
    group_level: '',
    code_status: 0,
    verify_status: '',
    age: '',
    recent_chat_avg_time: 0,
    photo_verify_status: 0,
    video_income: 0,
    utype: '',
    grade: ''
  };
}

export const GetAnchorInfoResp: MessageFns<GetAnchorInfoResp> = {
  fromJSON(object: any): GetAnchorInfoResp {
    return {
      had_video: isSet(object.had_video) ? globalThis.Number(object.had_video) : 0,
      show_video: isSet(object.show_video) ? globalThis.String(object.show_video) : '',
      show_video_pic: isSet(object.show_video_pic) ? globalThis.String(object.show_video_pic) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      had_avatar: isSet(object.had_avatar) ? globalThis.Number(object.had_avatar) : 0,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      verify_anchor: isSet(object.verify_anchor) ? globalThis.Number(object.verify_anchor) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      point: isSet(object.point) ? globalThis.String(object.point) : '',
      avatar_big: isSet(object.avatar_big) ? globalThis.String(object.avatar_big) : '',
      avatar_icon: isSet(object.avatar_icon) ? globalThis.String(object.avatar_icon) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      exp: isSet(object.exp) ? globalThis.Number(object.exp) : 0,
      invalid_point: isSet(object.invalid_point) ? globalThis.Number(object.invalid_point) : 0,
      subanm: isSet(object.subanm) ? globalThis.String(object.subanm) : '',
      video_reco: isSet(object.video_reco) ? globalThis.Number(object.video_reco) : 0,
      photos: globalThis.Array.isArray(object?.photos) ? object.photos.map((e: any) => globalThis.String(e)) : [],
      group_level: isSet(object.group_level) ? globalThis.String(object.group_level) : '',
      code_status: isSet(object.code_status) ? globalThis.Number(object.code_status) : 0,
      verify_status: isSet(object.verify_status) ? globalThis.String(object.verify_status) : '',
      age: isSet(object.age) ? globalThis.String(object.age) : '',
      recent_chat_avg_time: isSet(object.recent_chat_avg_time) ? globalThis.Number(object.recent_chat_avg_time) : 0,
      photo_verify_status: isSet(object.photo_verify_status) ? globalThis.Number(object.photo_verify_status) : 0,
      video_income: isSet(object.video_income) ? globalThis.Number(object.video_income) : 0,
      utype: isSet(object.utype) ? globalThis.String(object.utype) : '',
      grade: isSet(object.grade) ? globalThis.String(object.grade) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorInfoResp>, I>>(base?: I): GetAnchorInfoResp {
    return GetAnchorInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorInfoResp>, I>>(object: I): GetAnchorInfoResp {
    const message = createBaseGetAnchorInfoResp();
    message.had_video = object.had_video ?? 0;
    message.show_video = object.show_video ?? '';
    message.show_video_pic = object.show_video_pic ?? '';
    message.avatar = object.avatar ?? '';
    message.had_avatar = object.had_avatar ?? 0;
    message.nickname = object.nickname ?? '';
    message.verify_anchor = object.verify_anchor ?? 0;
    message.level = object.level ?? 0;
    message.point = object.point ?? '';
    message.avatar_big = object.avatar_big ?? '';
    message.avatar_icon = object.avatar_icon ?? '';
    message.lan = object.lan ?? '';
    message.exp = object.exp ?? 0;
    message.invalid_point = object.invalid_point ?? 0;
    message.subanm = object.subanm ?? '';
    message.video_reco = object.video_reco ?? 0;
    message.photos = object.photos?.map(e => e) || [];
    message.group_level = object.group_level ?? '';
    message.code_status = object.code_status ?? 0;
    message.verify_status = object.verify_status ?? '';
    message.age = object.age ?? '';
    message.recent_chat_avg_time = object.recent_chat_avg_time ?? 0;
    message.photo_verify_status = object.photo_verify_status ?? 0;
    message.video_income = object.video_income ?? 0;
    message.utype = object.utype ?? '';
    message.grade = object.grade ?? '';
    return message;
  }
};

function createBaseUploadAnchorAudioReq(): UploadAnchorAudioReq {
  return { action: '', path: '', duration: 0, pool_path: '' };
}

export const UploadAnchorAudioReq: MessageFns<UploadAnchorAudioReq> = {
  fromJSON(object: any): UploadAnchorAudioReq {
    return {
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      path: isSet(object.path) ? globalThis.String(object.path) : '',
      duration: isSet(object.duration) ? globalThis.Number(object.duration) : 0,
      pool_path: isSet(object.pool_path) ? globalThis.String(object.pool_path) : ''
    };
  },

  create<I extends Exact<DeepPartial<UploadAnchorAudioReq>, I>>(base?: I): UploadAnchorAudioReq {
    return UploadAnchorAudioReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UploadAnchorAudioReq>, I>>(object: I): UploadAnchorAudioReq {
    const message = createBaseUploadAnchorAudioReq();
    message.action = object.action ?? '';
    message.path = object.path ?? '';
    message.duration = object.duration ?? 0;
    message.pool_path = object.pool_path ?? '';
    return message;
  }
};

function createBaseUploadAnchorAudioResp(): UploadAnchorAudioResp {
  return { path: '', pool_path: '' };
}

export const UploadAnchorAudioResp: MessageFns<UploadAnchorAudioResp> = {
  fromJSON(object: any): UploadAnchorAudioResp {
    return {
      path: isSet(object.path) ? globalThis.String(object.path) : '',
      pool_path: isSet(object.pool_path) ? globalThis.String(object.pool_path) : ''
    };
  },

  create<I extends Exact<DeepPartial<UploadAnchorAudioResp>, I>>(base?: I): UploadAnchorAudioResp {
    return UploadAnchorAudioResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UploadAnchorAudioResp>, I>>(object: I): UploadAnchorAudioResp {
    const message = createBaseUploadAnchorAudioResp();
    message.path = object.path ?? '';
    message.pool_path = object.pool_path ?? '';
    return message;
  }
};

function createBaseGetAudioTemplateReq(): GetAudioTemplateReq {
  return {};
}

export const GetAudioTemplateReq: MessageFns<GetAudioTemplateReq> = {
  fromJSON(_: any): GetAudioTemplateReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAudioTemplateReq>, I>>(base?: I): GetAudioTemplateReq {
    return GetAudioTemplateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAudioTemplateReq>, I>>(_: I): GetAudioTemplateReq {
    const message = createBaseGetAudioTemplateReq();
    return message;
  }
};

function createBaseGetAudioTemplateResp(): GetAudioTemplateResp {
  return { template_en: [], template_hi: [] };
}

export const GetAudioTemplateResp: MessageFns<GetAudioTemplateResp> = {
  fromJSON(object: any): GetAudioTemplateResp {
    return {
      template_en: globalThis.Array.isArray(object?.template_en)
        ? object.template_en.map((e: any) => globalThis.String(e))
        : [],
      template_hi: globalThis.Array.isArray(object?.template_hi)
        ? object.template_hi.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetAudioTemplateResp>, I>>(base?: I): GetAudioTemplateResp {
    return GetAudioTemplateResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAudioTemplateResp>, I>>(object: I): GetAudioTemplateResp {
    const message = createBaseGetAudioTemplateResp();
    message.template_en = object.template_en?.map(e => e) || [];
    message.template_hi = object.template_hi?.map(e => e) || [];
    return message;
  }
};

function createBaseGetVideoCallIncomeReq(): GetVideoCallIncomeReq {
  return {};
}

export const GetVideoCallIncomeReq: MessageFns<GetVideoCallIncomeReq> = {
  fromJSON(_: any): GetVideoCallIncomeReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetVideoCallIncomeReq>, I>>(base?: I): GetVideoCallIncomeReq {
    return GetVideoCallIncomeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoCallIncomeReq>, I>>(_: I): GetVideoCallIncomeReq {
    const message = createBaseGetVideoCallIncomeReq();
    return message;
  }
};

function createBaseGetVideoCallIncomeResp(): GetVideoCallIncomeResp {
  return { current_grade: '', current_income: 0, grade_info: [] };
}

export const GetVideoCallIncomeResp: MessageFns<GetVideoCallIncomeResp> = {
  fromJSON(object: any): GetVideoCallIncomeResp {
    return {
      current_grade: isSet(object.current_grade) ? globalThis.String(object.current_grade) : '',
      current_income: isSet(object.current_income) ? globalThis.Number(object.current_income) : 0,
      grade_info: globalThis.Array.isArray(object?.grade_info)
        ? object.grade_info.map((e: any) => GetVideoCallIncomeResp_GradeInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetVideoCallIncomeResp>, I>>(base?: I): GetVideoCallIncomeResp {
    return GetVideoCallIncomeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoCallIncomeResp>, I>>(object: I): GetVideoCallIncomeResp {
    const message = createBaseGetVideoCallIncomeResp();
    message.current_grade = object.current_grade ?? '';
    message.current_income = object.current_income ?? 0;
    message.grade_info = object.grade_info?.map(e => GetVideoCallIncomeResp_GradeInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetVideoCallIncomeResp_GradeInfo(): GetVideoCallIncomeResp_GradeInfo {
  return { grade: '', income: 0, sort: 0 };
}

export const GetVideoCallIncomeResp_GradeInfo: MessageFns<GetVideoCallIncomeResp_GradeInfo> = {
  fromJSON(object: any): GetVideoCallIncomeResp_GradeInfo {
    return {
      grade: isSet(object.grade) ? globalThis.String(object.grade) : '',
      income: isSet(object.income) ? globalThis.Number(object.income) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetVideoCallIncomeResp_GradeInfo>, I>>(
    base?: I
  ): GetVideoCallIncomeResp_GradeInfo {
    return GetVideoCallIncomeResp_GradeInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetVideoCallIncomeResp_GradeInfo>, I>>(
    object: I
  ): GetVideoCallIncomeResp_GradeInfo {
    const message = createBaseGetVideoCallIncomeResp_GradeInfo();
    message.grade = object.grade ?? '';
    message.income = object.income ?? 0;
    message.sort = object.sort ?? 0;
    return message;
  }
};

function createBaseUpdateVideoCallIncomeReq(): UpdateVideoCallIncomeReq {
  return { income: 0 };
}

export const UpdateVideoCallIncomeReq: MessageFns<UpdateVideoCallIncomeReq> = {
  fromJSON(object: any): UpdateVideoCallIncomeReq {
    return { income: isSet(object.income) ? globalThis.Number(object.income) : 0 };
  },

  create<I extends Exact<DeepPartial<UpdateVideoCallIncomeReq>, I>>(base?: I): UpdateVideoCallIncomeReq {
    return UpdateVideoCallIncomeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateVideoCallIncomeReq>, I>>(object: I): UpdateVideoCallIncomeReq {
    const message = createBaseUpdateVideoCallIncomeReq();
    message.income = object.income ?? 0;
    return message;
  }
};

function createBaseUpdateVideoCallIncomeResp(): UpdateVideoCallIncomeResp {
  return {};
}

export const UpdateVideoCallIncomeResp: MessageFns<UpdateVideoCallIncomeResp> = {
  fromJSON(_: any): UpdateVideoCallIncomeResp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateVideoCallIncomeResp>, I>>(base?: I): UpdateVideoCallIncomeResp {
    return UpdateVideoCallIncomeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateVideoCallIncomeResp>, I>>(_: I): UpdateVideoCallIncomeResp {
    const message = createBaseUpdateVideoCallIncomeResp();
    return message;
  }
};

function createBaseGetAnchorLevelConfigReq(): GetAnchorLevelConfigReq {
  return {};
}

export const GetAnchorLevelConfigReq: MessageFns<GetAnchorLevelConfigReq> = {
  fromJSON(_: any): GetAnchorLevelConfigReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorLevelConfigReq>, I>>(base?: I): GetAnchorLevelConfigReq {
    return GetAnchorLevelConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorLevelConfigReq>, I>>(_: I): GetAnchorLevelConfigReq {
    const message = createBaseGetAnchorLevelConfigReq();
    return message;
  }
};

function createBaseGetAnchorLevelConfigResp(): GetAnchorLevelConfigResp {
  return { config: [] };
}

export const GetAnchorLevelConfigResp: MessageFns<GetAnchorLevelConfigResp> = {
  fromJSON(object: any): GetAnchorLevelConfigResp {
    return {
      config: globalThis.Array.isArray(object?.config)
        ? object.config.map((e: any) => AnchorLevelConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorLevelConfigResp>, I>>(base?: I): GetAnchorLevelConfigResp {
    return GetAnchorLevelConfigResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorLevelConfigResp>, I>>(object: I): GetAnchorLevelConfigResp {
    const message = createBaseGetAnchorLevelConfigResp();
    message.config = object.config?.map(e => AnchorLevelConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAnchorLevelConfig(): AnchorLevelConfig {
  return { grade: '', min_point: 0, max_point: 0, chat_avg_time: 0, point_income: 0 };
}

export const AnchorLevelConfig: MessageFns<AnchorLevelConfig> = {
  fromJSON(object: any): AnchorLevelConfig {
    return {
      grade: isSet(object.grade) ? globalThis.String(object.grade) : '',
      min_point: isSet(object.min_point) ? globalThis.Number(object.min_point) : 0,
      max_point: isSet(object.max_point) ? globalThis.Number(object.max_point) : 0,
      chat_avg_time: isSet(object.chat_avg_time) ? globalThis.Number(object.chat_avg_time) : 0,
      point_income: isSet(object.point_income) ? globalThis.Number(object.point_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<AnchorLevelConfig>, I>>(base?: I): AnchorLevelConfig {
    return AnchorLevelConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AnchorLevelConfig>, I>>(object: I): AnchorLevelConfig {
    const message = createBaseAnchorLevelConfig();
    message.grade = object.grade ?? '';
    message.min_point = object.min_point ?? 0;
    message.max_point = object.max_point ?? 0;
    message.chat_avg_time = object.chat_avg_time ?? 0;
    message.point_income = object.point_income ?? 0;
    return message;
  }
};

function createBaseGetAnchorLevelInfoReq(): GetAnchorLevelInfoReq {
  return {};
}

export const GetAnchorLevelInfoReq: MessageFns<GetAnchorLevelInfoReq> = {
  fromJSON(_: any): GetAnchorLevelInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorLevelInfoReq>, I>>(base?: I): GetAnchorLevelInfoReq {
    return GetAnchorLevelInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorLevelInfoReq>, I>>(_: I): GetAnchorLevelInfoReq {
    const message = createBaseGetAnchorLevelInfoReq();
    return message;
  }
};

function createBaseGetAnchorLevelInfoResp(): GetAnchorLevelInfoResp {
  return { uid: '', nickname: '', avatar: '', current_level: '', profit: 0, chat_avg_time: 0 };
}

export const GetAnchorLevelInfoResp: MessageFns<GetAnchorLevelInfoResp> = {
  fromJSON(object: any): GetAnchorLevelInfoResp {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      current_level: isSet(object.current_level) ? globalThis.String(object.current_level) : '',
      profit: isSet(object.profit) ? globalThis.Number(object.profit) : 0,
      chat_avg_time: isSet(object.chat_avg_time) ? globalThis.Number(object.chat_avg_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorLevelInfoResp>, I>>(base?: I): GetAnchorLevelInfoResp {
    return GetAnchorLevelInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorLevelInfoResp>, I>>(object: I): GetAnchorLevelInfoResp {
    const message = createBaseGetAnchorLevelInfoResp();
    message.uid = object.uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.current_level = object.current_level ?? '';
    message.profit = object.profit ?? 0;
    message.chat_avg_time = object.chat_avg_time ?? 0;
    return message;
  }
};

function createBaseGetAnchorRegisterPopUpReq(): GetAnchorRegisterPopUpReq {
  return {};
}

export const GetAnchorRegisterPopUpReq: MessageFns<GetAnchorRegisterPopUpReq> = {
  fromJSON(_: any): GetAnchorRegisterPopUpReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorRegisterPopUpReq>, I>>(base?: I): GetAnchorRegisterPopUpReq {
    return GetAnchorRegisterPopUpReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorRegisterPopUpReq>, I>>(_: I): GetAnchorRegisterPopUpReq {
    const message = createBaseGetAnchorRegisterPopUpReq();
    return message;
  }
};

function createBaseGetAnchorRegisterPopUpResp(): GetAnchorRegisterPopUpResp {
  return { pop_up_type: 0, welcome_info: undefined };
}

export const GetAnchorRegisterPopUpResp: MessageFns<GetAnchorRegisterPopUpResp> = {
  fromJSON(object: any): GetAnchorRegisterPopUpResp {
    return {
      pop_up_type: isSet(object.pop_up_type) ? registerPopUpTypeFromJSON(object.pop_up_type) : 0,
      welcome_info: isSet(object.welcome_info) ? WelcomePopUpInfo.fromJSON(object.welcome_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorRegisterPopUpResp>, I>>(base?: I): GetAnchorRegisterPopUpResp {
    return GetAnchorRegisterPopUpResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorRegisterPopUpResp>, I>>(object: I): GetAnchorRegisterPopUpResp {
    const message = createBaseGetAnchorRegisterPopUpResp();
    message.pop_up_type = object.pop_up_type ?? 0;
    message.welcome_info =
      object.welcome_info !== undefined && object.welcome_info !== null
        ? WelcomePopUpInfo.fromPartial(object.welcome_info)
        : undefined;
    return message;
  }
};

function createBaseWelcomePopUpInfo(): WelcomePopUpInfo {
  return { per_min_point: '', gift_percent: '', im_message_point: '', new_user_day: '' };
}

export const WelcomePopUpInfo: MessageFns<WelcomePopUpInfo> = {
  fromJSON(object: any): WelcomePopUpInfo {
    return {
      per_min_point: isSet(object.per_min_point) ? globalThis.String(object.per_min_point) : '',
      gift_percent: isSet(object.gift_percent) ? globalThis.String(object.gift_percent) : '',
      im_message_point: isSet(object.im_message_point) ? globalThis.String(object.im_message_point) : '',
      new_user_day: isSet(object.new_user_day) ? globalThis.String(object.new_user_day) : ''
    };
  },

  create<I extends Exact<DeepPartial<WelcomePopUpInfo>, I>>(base?: I): WelcomePopUpInfo {
    return WelcomePopUpInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WelcomePopUpInfo>, I>>(object: I): WelcomePopUpInfo {
    const message = createBaseWelcomePopUpInfo();
    message.per_min_point = object.per_min_point ?? '';
    message.gift_percent = object.gift_percent ?? '';
    message.im_message_point = object.im_message_point ?? '';
    message.new_user_day = object.new_user_day ?? '';
    return message;
  }
};

function createBaseGetAnchorJobDetailReq(): GetAnchorJobDetailReq {
  return {};
}

export const GetAnchorJobDetailReq: MessageFns<GetAnchorJobDetailReq> = {
  fromJSON(_: any): GetAnchorJobDetailReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetAnchorJobDetailReq>, I>>(base?: I): GetAnchorJobDetailReq {
    return GetAnchorJobDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorJobDetailReq>, I>>(_: I): GetAnchorJobDetailReq {
    const message = createBaseGetAnchorJobDetailReq();
    return message;
  }
};

function createBaseGetAnchorJobDetailResp(): GetAnchorJobDetailResp {
  return {
    today_avg_pay_call_time: 0,
    today_online_time: 0,
    today_call_num: 0,
    today_miss_call_num: 0,
    today_income: 0,
    grade: '',
    point: 0,
    update_time: 0
  };
}

export const GetAnchorJobDetailResp: MessageFns<GetAnchorJobDetailResp> = {
  fromJSON(object: any): GetAnchorJobDetailResp {
    return {
      today_avg_pay_call_time: isSet(object.today_avg_pay_call_time)
        ? globalThis.Number(object.today_avg_pay_call_time)
        : 0,
      today_online_time: isSet(object.today_online_time) ? globalThis.Number(object.today_online_time) : 0,
      today_call_num: isSet(object.today_call_num) ? globalThis.Number(object.today_call_num) : 0,
      today_miss_call_num: isSet(object.today_miss_call_num) ? globalThis.Number(object.today_miss_call_num) : 0,
      today_income: isSet(object.today_income) ? globalThis.Number(object.today_income) : 0,
      grade: isSet(object.grade) ? globalThis.String(object.grade) : '',
      point: isSet(object.point) ? globalThis.Number(object.point) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorJobDetailResp>, I>>(base?: I): GetAnchorJobDetailResp {
    return GetAnchorJobDetailResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorJobDetailResp>, I>>(object: I): GetAnchorJobDetailResp {
    const message = createBaseGetAnchorJobDetailResp();
    message.today_avg_pay_call_time = object.today_avg_pay_call_time ?? 0;
    message.today_online_time = object.today_online_time ?? 0;
    message.today_call_num = object.today_call_num ?? 0;
    message.today_miss_call_num = object.today_miss_call_num ?? 0;
    message.today_income = object.today_income ?? 0;
    message.grade = object.grade ?? '';
    message.point = object.point ?? 0;
    message.update_time = object.update_time ?? 0;
    return message;
  }
};

function createBaseGetGuildMasterInfoReq(): GetGuildMasterInfoReq {
  return {};
}

export const GetGuildMasterInfoReq: MessageFns<GetGuildMasterInfoReq> = {
  fromJSON(_: any): GetGuildMasterInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetGuildMasterInfoReq>, I>>(base?: I): GetGuildMasterInfoReq {
    return GetGuildMasterInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGuildMasterInfoReq>, I>>(_: I): GetGuildMasterInfoReq {
    const message = createBaseGetGuildMasterInfoReq();
    return message;
  }
};

function createBaseGetGuildMasterInfoResp(): GetGuildMasterInfoResp {
  return {
    total_commission: 0,
    anchor_commission: 0,
    sub_agency_commission: 0,
    commission_ratio: 0,
    last_update_times: 0,
    nickname: '',
    invite_code: '',
    next_commission_ratio: 0,
    next_level_points: 0,
    contact_info: '',
    current_points: 0
  };
}

export const GetGuildMasterInfoResp: MessageFns<GetGuildMasterInfoResp> = {
  fromJSON(object: any): GetGuildMasterInfoResp {
    return {
      total_commission: isSet(object.total_commission) ? globalThis.Number(object.total_commission) : 0,
      anchor_commission: isSet(object.anchor_commission) ? globalThis.Number(object.anchor_commission) : 0,
      sub_agency_commission: isSet(object.sub_agency_commission) ? globalThis.Number(object.sub_agency_commission) : 0,
      commission_ratio: isSet(object.commission_ratio) ? globalThis.Number(object.commission_ratio) : 0,
      last_update_times: isSet(object.last_update_times) ? globalThis.Number(object.last_update_times) : 0,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      invite_code: isSet(object.invite_code) ? globalThis.String(object.invite_code) : '',
      next_commission_ratio: isSet(object.next_commission_ratio) ? globalThis.Number(object.next_commission_ratio) : 0,
      next_level_points: isSet(object.next_level_points) ? globalThis.Number(object.next_level_points) : 0,
      contact_info: isSet(object.contact_info) ? globalThis.String(object.contact_info) : '',
      current_points: isSet(object.current_points) ? globalThis.Number(object.current_points) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetGuildMasterInfoResp>, I>>(base?: I): GetGuildMasterInfoResp {
    return GetGuildMasterInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGuildMasterInfoResp>, I>>(object: I): GetGuildMasterInfoResp {
    const message = createBaseGetGuildMasterInfoResp();
    message.total_commission = object.total_commission ?? 0;
    message.anchor_commission = object.anchor_commission ?? 0;
    message.sub_agency_commission = object.sub_agency_commission ?? 0;
    message.commission_ratio = object.commission_ratio ?? 0;
    message.last_update_times = object.last_update_times ?? 0;
    message.nickname = object.nickname ?? '';
    message.invite_code = object.invite_code ?? '';
    message.next_commission_ratio = object.next_commission_ratio ?? 0;
    message.next_level_points = object.next_level_points ?? 0;
    message.contact_info = object.contact_info ?? '';
    message.current_points = object.current_points ?? 0;
    return message;
  }
};

function createBaseGetAnchorListReq(): GetAnchorListReq {
  return { page: undefined, datetime: 0 };
}

export const GetAnchorListReq: MessageFns<GetAnchorListReq> = {
  fromJSON(object: any): GetAnchorListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      datetime: isSet(object.datetime) ? globalThis.Number(object.datetime) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorListReq>, I>>(base?: I): GetAnchorListReq {
    return GetAnchorListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorListReq>, I>>(object: I): GetAnchorListReq {
    const message = createBaseGetAnchorListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.datetime = object.datetime ?? 0;
    return message;
  }
};

function createBaseGetAnchorListResp(): GetAnchorListResp {
  return { page: undefined, list: [] };
}

export const GetAnchorListResp: MessageFns<GetAnchorListResp> = {
  fromJSON(object: any): GetAnchorListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GetAnchorListResp_AnchorInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorListResp>, I>>(base?: I): GetAnchorListResp {
    return GetAnchorListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorListResp>, I>>(object: I): GetAnchorListResp {
    const message = createBaseGetAnchorListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GetAnchorListResp_AnchorInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetAnchorListResp_AnchorInfo(): GetAnchorListResp_AnchorInfo {
  return {
    uid: '',
    avatar: '',
    nickname: '',
    contact_information: '',
    become_time: 0,
    last_active_time: 0,
    is_ban: false,
    is_delete: false
  };
}

export const GetAnchorListResp_AnchorInfo: MessageFns<GetAnchorListResp_AnchorInfo> = {
  fromJSON(object: any): GetAnchorListResp_AnchorInfo {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      contact_information: isSet(object.contact_information) ? globalThis.String(object.contact_information) : '',
      become_time: isSet(object.become_time) ? globalThis.Number(object.become_time) : 0,
      last_active_time: isSet(object.last_active_time) ? globalThis.Number(object.last_active_time) : 0,
      is_ban: isSet(object.is_ban) ? globalThis.Boolean(object.is_ban) : false,
      is_delete: isSet(object.is_delete) ? globalThis.Boolean(object.is_delete) : false
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorListResp_AnchorInfo>, I>>(base?: I): GetAnchorListResp_AnchorInfo {
    return GetAnchorListResp_AnchorInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorListResp_AnchorInfo>, I>>(object: I): GetAnchorListResp_AnchorInfo {
    const message = createBaseGetAnchorListResp_AnchorInfo();
    message.uid = object.uid ?? '';
    message.avatar = object.avatar ?? '';
    message.nickname = object.nickname ?? '';
    message.contact_information = object.contact_information ?? '';
    message.become_time = object.become_time ?? 0;
    message.last_active_time = object.last_active_time ?? 0;
    message.is_ban = object.is_ban ?? false;
    message.is_delete = object.is_delete ?? false;
    return message;
  }
};

function createBaseGetIncomeListReq(): GetIncomeListReq {
  return { page: undefined, income_type: 0, business_id: '', start_datetime: 0, end_datetime: 0, week_day: 0 };
}

export const GetIncomeListReq: MessageFns<GetIncomeListReq> = {
  fromJSON(object: any): GetIncomeListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      income_type: isSet(object.income_type) ? incomeListTypeFromJSON(object.income_type) : 0,
      business_id: isSet(object.business_id) ? globalThis.String(object.business_id) : '',
      start_datetime: isSet(object.start_datetime) ? globalThis.Number(object.start_datetime) : 0,
      end_datetime: isSet(object.end_datetime) ? globalThis.Number(object.end_datetime) : 0,
      week_day: isSet(object.week_day) ? globalThis.Number(object.week_day) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetIncomeListReq>, I>>(base?: I): GetIncomeListReq {
    return GetIncomeListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetIncomeListReq>, I>>(object: I): GetIncomeListReq {
    const message = createBaseGetIncomeListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.income_type = object.income_type ?? 0;
    message.business_id = object.business_id ?? '';
    message.start_datetime = object.start_datetime ?? 0;
    message.end_datetime = object.end_datetime ?? 0;
    message.week_day = object.week_day ?? 0;
    return message;
  }
};

function createBaseGetIncomeListResp(): GetIncomeListResp {
  return { page: undefined, list: [], last_update_time: 0, week_total_income: 0 };
}

export const GetIncomeListResp: MessageFns<GetIncomeListResp> = {
  fromJSON(object: any): GetIncomeListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GetIncomeListResp_IncomeItem.fromJSON(e))
        : [],
      last_update_time: isSet(object.last_update_time) ? globalThis.Number(object.last_update_time) : 0,
      week_total_income: isSet(object.week_total_income) ? globalThis.Number(object.week_total_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetIncomeListResp>, I>>(base?: I): GetIncomeListResp {
    return GetIncomeListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetIncomeListResp>, I>>(object: I): GetIncomeListResp {
    const message = createBaseGetIncomeListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GetIncomeListResp_IncomeItem.fromPartial(e)) || [];
    message.last_update_time = object.last_update_time ?? 0;
    message.week_total_income = object.week_total_income ?? 0;
    return message;
  }
};

function createBaseGetIncomeListResp_IncomeItem(): GetIncomeListResp_IncomeItem {
  return {
    id: '',
    avatar: '',
    name: '',
    total_income: 0,
    today_income: 0,
    this_week_income: 0,
    last_week_income: 0,
    is_ban: false,
    is_delete: false
  };
}

export const GetIncomeListResp_IncomeItem: MessageFns<GetIncomeListResp_IncomeItem> = {
  fromJSON(object: any): GetIncomeListResp_IncomeItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      total_income: isSet(object.total_income) ? globalThis.Number(object.total_income) : 0,
      today_income: isSet(object.today_income) ? globalThis.Number(object.today_income) : 0,
      this_week_income: isSet(object.this_week_income) ? globalThis.Number(object.this_week_income) : 0,
      last_week_income: isSet(object.last_week_income) ? globalThis.Number(object.last_week_income) : 0,
      is_ban: isSet(object.is_ban) ? globalThis.Boolean(object.is_ban) : false,
      is_delete: isSet(object.is_delete) ? globalThis.Boolean(object.is_delete) : false
    };
  },

  create<I extends Exact<DeepPartial<GetIncomeListResp_IncomeItem>, I>>(base?: I): GetIncomeListResp_IncomeItem {
    return GetIncomeListResp_IncomeItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetIncomeListResp_IncomeItem>, I>>(object: I): GetIncomeListResp_IncomeItem {
    const message = createBaseGetIncomeListResp_IncomeItem();
    message.id = object.id ?? '';
    message.avatar = object.avatar ?? '';
    message.name = object.name ?? '';
    message.total_income = object.total_income ?? 0;
    message.today_income = object.today_income ?? 0;
    message.this_week_income = object.this_week_income ?? 0;
    message.last_week_income = object.last_week_income ?? 0;
    message.is_ban = object.is_ban ?? false;
    message.is_delete = object.is_delete ?? false;
    return message;
  }
};

function createBaseGetAnchorViolationListReq(): GetAnchorViolationListReq {
  return { page: undefined };
}

export const GetAnchorViolationListReq: MessageFns<GetAnchorViolationListReq> = {
  fromJSON(object: any): GetAnchorViolationListReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetAnchorViolationListReq>, I>>(base?: I): GetAnchorViolationListReq {
    return GetAnchorViolationListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorViolationListReq>, I>>(object: I): GetAnchorViolationListReq {
    const message = createBaseGetAnchorViolationListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetAnchorViolationListResp(): GetAnchorViolationListResp {
  return { page: undefined, list: [] };
}

export const GetAnchorViolationListResp: MessageFns<GetAnchorViolationListResp> = {
  fromJSON(object: any): GetAnchorViolationListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GetAnchorViolationListResp_ViolationItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorViolationListResp>, I>>(base?: I): GetAnchorViolationListResp {
    return GetAnchorViolationListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorViolationListResp>, I>>(object: I): GetAnchorViolationListResp {
    const message = createBaseGetAnchorViolationListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GetAnchorViolationListResp_ViolationItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetAnchorViolationListResp_ViolationItem(): GetAnchorViolationListResp_ViolationItem {
  return {
    uid: '',
    name: '',
    avatar: '',
    punish_status: 0,
    this_week_violation: 0,
    punish_time: 0,
    is_ban: false,
    is_delete: false
  };
}

export const GetAnchorViolationListResp_ViolationItem: MessageFns<GetAnchorViolationListResp_ViolationItem> = {
  fromJSON(object: any): GetAnchorViolationListResp_ViolationItem {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      punish_status: isSet(object.punish_status) ? violationListTypeFromJSON(object.punish_status) : 0,
      this_week_violation: isSet(object.this_week_violation) ? globalThis.Number(object.this_week_violation) : 0,
      punish_time: isSet(object.punish_time) ? globalThis.Number(object.punish_time) : 0,
      is_ban: isSet(object.is_ban) ? globalThis.Boolean(object.is_ban) : false,
      is_delete: isSet(object.is_delete) ? globalThis.Boolean(object.is_delete) : false
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorViolationListResp_ViolationItem>, I>>(
    base?: I
  ): GetAnchorViolationListResp_ViolationItem {
    return GetAnchorViolationListResp_ViolationItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorViolationListResp_ViolationItem>, I>>(
    object: I
  ): GetAnchorViolationListResp_ViolationItem {
    const message = createBaseGetAnchorViolationListResp_ViolationItem();
    message.uid = object.uid ?? '';
    message.name = object.name ?? '';
    message.avatar = object.avatar ?? '';
    message.punish_status = object.punish_status ?? 0;
    message.this_week_violation = object.this_week_violation ?? 0;
    message.punish_time = object.punish_time ?? 0;
    message.is_ban = object.is_ban ?? false;
    message.is_delete = object.is_delete ?? false;
    return message;
  }
};

function createBaseGetSubAgencyListReq(): GetSubAgencyListReq {
  return { page: undefined, datetime: 0 };
}

export const GetSubAgencyListReq: MessageFns<GetSubAgencyListReq> = {
  fromJSON(object: any): GetSubAgencyListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      datetime: isSet(object.datetime) ? globalThis.Number(object.datetime) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetSubAgencyListReq>, I>>(base?: I): GetSubAgencyListReq {
    return GetSubAgencyListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubAgencyListReq>, I>>(object: I): GetSubAgencyListReq {
    const message = createBaseGetSubAgencyListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.datetime = object.datetime ?? 0;
    return message;
  }
};

function createBaseGetSubAgencyListResp(): GetSubAgencyListResp {
  return { page: undefined, list: [] };
}

export const GetSubAgencyListResp: MessageFns<GetSubAgencyListResp> = {
  fromJSON(object: any): GetSubAgencyListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GetSubAgencyListResp_SubAgencyItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetSubAgencyListResp>, I>>(base?: I): GetSubAgencyListResp {
    return GetSubAgencyListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubAgencyListResp>, I>>(object: I): GetSubAgencyListResp {
    const message = createBaseGetSubAgencyListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GetSubAgencyListResp_SubAgencyItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetSubAgencyListResp_SubAgencyItem(): GetSubAgencyListResp_SubAgencyItem {
  return { id: '', name: '', whatsapp: '', join_time: 0, total_anchor: 0, active_anchor: 0, is_expired: false };
}

export const GetSubAgencyListResp_SubAgencyItem: MessageFns<GetSubAgencyListResp_SubAgencyItem> = {
  fromJSON(object: any): GetSubAgencyListResp_SubAgencyItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      whatsapp: isSet(object.whatsapp) ? globalThis.String(object.whatsapp) : '',
      join_time: isSet(object.join_time) ? globalThis.Number(object.join_time) : 0,
      total_anchor: isSet(object.total_anchor) ? globalThis.Number(object.total_anchor) : 0,
      active_anchor: isSet(object.active_anchor) ? globalThis.Number(object.active_anchor) : 0,
      is_expired: isSet(object.is_expired) ? globalThis.Boolean(object.is_expired) : false
    };
  },

  create<I extends Exact<DeepPartial<GetSubAgencyListResp_SubAgencyItem>, I>>(
    base?: I
  ): GetSubAgencyListResp_SubAgencyItem {
    return GetSubAgencyListResp_SubAgencyItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubAgencyListResp_SubAgencyItem>, I>>(
    object: I
  ): GetSubAgencyListResp_SubAgencyItem {
    const message = createBaseGetSubAgencyListResp_SubAgencyItem();
    message.id = object.id ?? '';
    message.name = object.name ?? '';
    message.whatsapp = object.whatsapp ?? '';
    message.join_time = object.join_time ?? 0;
    message.total_anchor = object.total_anchor ?? 0;
    message.active_anchor = object.active_anchor ?? 0;
    message.is_expired = object.is_expired ?? false;
    return message;
  }
};

function createBaseGetMyTotalIncomeReq(): GetMyTotalIncomeReq {
  return { page: undefined };
}

export const GetMyTotalIncomeReq: MessageFns<GetMyTotalIncomeReq> = {
  fromJSON(object: any): GetMyTotalIncomeReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetMyTotalIncomeReq>, I>>(base?: I): GetMyTotalIncomeReq {
    return GetMyTotalIncomeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyTotalIncomeReq>, I>>(object: I): GetMyTotalIncomeReq {
    const message = createBaseGetMyTotalIncomeReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetMyTotalIncomeResp(): GetMyTotalIncomeResp {
  return { list: [], page: undefined };
}

export const GetMyTotalIncomeResp: MessageFns<GetMyTotalIncomeResp> = {
  fromJSON(object: any): GetMyTotalIncomeResp {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GetMyTotalIncomeResp_TotalIncomeItem.fromJSON(e))
        : [],
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetMyTotalIncomeResp>, I>>(base?: I): GetMyTotalIncomeResp {
    return GetMyTotalIncomeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyTotalIncomeResp>, I>>(object: I): GetMyTotalIncomeResp {
    const message = createBaseGetMyTotalIncomeResp();
    message.list = object.list?.map(e => GetMyTotalIncomeResp_TotalIncomeItem.fromPartial(e)) || [];
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetMyTotalIncomeResp_TotalIncomeItem(): GetMyTotalIncomeResp_TotalIncomeItem {
  return {
    day_week: 0,
    total_commission: 0,
    anchor_commission: 0,
    sub_agency_commission: 0,
    commision_ratio: 0,
    number_of_earning_anchor: 0,
    anchor_paid_income: 0
  };
}

export const GetMyTotalIncomeResp_TotalIncomeItem: MessageFns<GetMyTotalIncomeResp_TotalIncomeItem> = {
  fromJSON(object: any): GetMyTotalIncomeResp_TotalIncomeItem {
    return {
      day_week: isSet(object.day_week) ? globalThis.Number(object.day_week) : 0,
      total_commission: isSet(object.total_commission) ? globalThis.Number(object.total_commission) : 0,
      anchor_commission: isSet(object.anchor_commission) ? globalThis.Number(object.anchor_commission) : 0,
      sub_agency_commission: isSet(object.sub_agency_commission) ? globalThis.Number(object.sub_agency_commission) : 0,
      commision_ratio: isSet(object.commision_ratio) ? globalThis.Number(object.commision_ratio) : 0,
      number_of_earning_anchor: isSet(object.number_of_earning_anchor)
        ? globalThis.Number(object.number_of_earning_anchor)
        : 0,
      anchor_paid_income: isSet(object.anchor_paid_income) ? globalThis.Number(object.anchor_paid_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetMyTotalIncomeResp_TotalIncomeItem>, I>>(
    base?: I
  ): GetMyTotalIncomeResp_TotalIncomeItem {
    return GetMyTotalIncomeResp_TotalIncomeItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyTotalIncomeResp_TotalIncomeItem>, I>>(
    object: I
  ): GetMyTotalIncomeResp_TotalIncomeItem {
    const message = createBaseGetMyTotalIncomeResp_TotalIncomeItem();
    message.day_week = object.day_week ?? 0;
    message.total_commission = object.total_commission ?? 0;
    message.anchor_commission = object.anchor_commission ?? 0;
    message.sub_agency_commission = object.sub_agency_commission ?? 0;
    message.commision_ratio = object.commision_ratio ?? 0;
    message.number_of_earning_anchor = object.number_of_earning_anchor ?? 0;
    message.anchor_paid_income = object.anchor_paid_income ?? 0;
    return message;
  }
};

function createBaseGetAnchorPointFlowsReq(): GetAnchorPointFlowsReq {
  return { start_time: 0, end_time: 0, category: '', page: 0, size: 0 };
}

export const GetAnchorPointFlowsReq: MessageFns<GetAnchorPointFlowsReq> = {
  fromJSON(object: any): GetAnchorPointFlowsReq {
    return {
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      size: isSet(object.size) ? globalThis.Number(object.size) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorPointFlowsReq>, I>>(base?: I): GetAnchorPointFlowsReq {
    return GetAnchorPointFlowsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorPointFlowsReq>, I>>(object: I): GetAnchorPointFlowsReq {
    const message = createBaseGetAnchorPointFlowsReq();
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.category = object.category ?? '';
    message.page = object.page ?? 0;
    message.size = object.size ?? 0;
    return message;
  }
};

function createBaseGetAnchorPointFlowsResp(): GetAnchorPointFlowsResp {
  return { list: [] };
}

export const GetAnchorPointFlowsResp: MessageFns<GetAnchorPointFlowsResp> = {
  fromJSON(object: any): GetAnchorPointFlowsResp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => PointFlowInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorPointFlowsResp>, I>>(base?: I): GetAnchorPointFlowsResp {
    return GetAnchorPointFlowsResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorPointFlowsResp>, I>>(object: I): GetAnchorPointFlowsResp {
    const message = createBaseGetAnchorPointFlowsResp();
    message.list = object.list?.map(e => PointFlowInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBasePointFlowInfo(): PointFlowInfo {
  return { category: '', ctime: 0, chg: 0 };
}

export const PointFlowInfo: MessageFns<PointFlowInfo> = {
  fromJSON(object: any): PointFlowInfo {
    return {
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      chg: isSet(object.chg) ? globalThis.Number(object.chg) : 0
    };
  },

  create<I extends Exact<DeepPartial<PointFlowInfo>, I>>(base?: I): PointFlowInfo {
    return PointFlowInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PointFlowInfo>, I>>(object: I): PointFlowInfo {
    const message = createBasePointFlowInfo();
    message.category = object.category ?? '';
    message.ctime = object.ctime ?? 0;
    message.chg = object.chg ?? 0;
    return message;
  }
};

function createBaseGetAnchorIncomeInfoReq(): GetAnchorIncomeInfoReq {
  return { time_range: 0 };
}

export const GetAnchorIncomeInfoReq: MessageFns<GetAnchorIncomeInfoReq> = {
  fromJSON(object: any): GetAnchorIncomeInfoReq {
    return { time_range: isSet(object.time_range) ? anchorIncomeTimeRangeFromJSON(object.time_range) : 0 };
  },

  create<I extends Exact<DeepPartial<GetAnchorIncomeInfoReq>, I>>(base?: I): GetAnchorIncomeInfoReq {
    return GetAnchorIncomeInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorIncomeInfoReq>, I>>(object: I): GetAnchorIncomeInfoReq {
    const message = createBaseGetAnchorIncomeInfoReq();
    message.time_range = object.time_range ?? 0;
    return message;
  }
};

function createBaseGetAnchorIncomeInfoResp(): GetAnchorIncomeInfoResp {
  return { total_income: 0, video_income: 0, chat_income: 0, gift_income: 0, update_time: 0 };
}

export const GetAnchorIncomeInfoResp: MessageFns<GetAnchorIncomeInfoResp> = {
  fromJSON(object: any): GetAnchorIncomeInfoResp {
    return {
      total_income: isSet(object.total_income) ? globalThis.Number(object.total_income) : 0,
      video_income: isSet(object.video_income) ? globalThis.Number(object.video_income) : 0,
      chat_income: isSet(object.chat_income) ? globalThis.Number(object.chat_income) : 0,
      gift_income: isSet(object.gift_income) ? globalThis.Number(object.gift_income) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAnchorIncomeInfoResp>, I>>(base?: I): GetAnchorIncomeInfoResp {
    return GetAnchorIncomeInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAnchorIncomeInfoResp>, I>>(object: I): GetAnchorIncomeInfoResp {
    const message = createBaseGetAnchorIncomeInfoResp();
    message.total_income = object.total_income ?? 0;
    message.video_income = object.video_income ?? 0;
    message.chat_income = object.chat_income ?? 0;
    message.gift_income = object.gift_income ?? 0;
    message.update_time = object.update_time ?? 0;
    return message;
  }
};

function createBaseCheckAnchorEditPermissionReq(): CheckAnchorEditPermissionReq {
  return { permission: 0 };
}

export const CheckAnchorEditPermissionReq: MessageFns<CheckAnchorEditPermissionReq> = {
  fromJSON(object: any): CheckAnchorEditPermissionReq {
    return { permission: isSet(object.permission) ? editPermissionFromJSON(object.permission) : 0 };
  },

  create<I extends Exact<DeepPartial<CheckAnchorEditPermissionReq>, I>>(base?: I): CheckAnchorEditPermissionReq {
    return CheckAnchorEditPermissionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckAnchorEditPermissionReq>, I>>(object: I): CheckAnchorEditPermissionReq {
    const message = createBaseCheckAnchorEditPermissionReq();
    message.permission = object.permission ?? 0;
    return message;
  }
};

function createBaseCheckAnchorEditPermissionResp(): CheckAnchorEditPermissionResp {
  return {};
}

export const CheckAnchorEditPermissionResp: MessageFns<CheckAnchorEditPermissionResp> = {
  fromJSON(_: any): CheckAnchorEditPermissionResp {
    return {};
  },

  create<I extends Exact<DeepPartial<CheckAnchorEditPermissionResp>, I>>(base?: I): CheckAnchorEditPermissionResp {
    return CheckAnchorEditPermissionResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckAnchorEditPermissionResp>, I>>(_: I): CheckAnchorEditPermissionResp {
    const message = createBaseCheckAnchorEditPermissionResp();
    return message;
  }
};

function createBaseGetMyGuildInfoReq(): GetMyGuildInfoReq {
  return {};
}

export const GetMyGuildInfoReq: MessageFns<GetMyGuildInfoReq> = {
  fromJSON(_: any): GetMyGuildInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetMyGuildInfoReq>, I>>(base?: I): GetMyGuildInfoReq {
    return GetMyGuildInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyGuildInfoReq>, I>>(_: I): GetMyGuildInfoReq {
    const message = createBaseGetMyGuildInfoReq();
    return message;
  }
};

function createBaseGetMyGuildInfoResp(): GetMyGuildInfoResp {
  return { guild_name: '', guild_master_name: '', guild_uid: '', guild_contact_info: '', guild_avatar: '' };
}

export const GetMyGuildInfoResp: MessageFns<GetMyGuildInfoResp> = {
  fromJSON(object: any): GetMyGuildInfoResp {
    return {
      guild_name: isSet(object.guild_name) ? globalThis.String(object.guild_name) : '',
      guild_master_name: isSet(object.guild_master_name) ? globalThis.String(object.guild_master_name) : '',
      guild_uid: isSet(object.guild_uid) ? globalThis.String(object.guild_uid) : '',
      guild_contact_info: isSet(object.guild_contact_info) ? globalThis.String(object.guild_contact_info) : '',
      guild_avatar: isSet(object.guild_avatar) ? globalThis.String(object.guild_avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetMyGuildInfoResp>, I>>(base?: I): GetMyGuildInfoResp {
    return GetMyGuildInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyGuildInfoResp>, I>>(object: I): GetMyGuildInfoResp {
    const message = createBaseGetMyGuildInfoResp();
    message.guild_name = object.guild_name ?? '';
    message.guild_master_name = object.guild_master_name ?? '';
    message.guild_uid = object.guild_uid ?? '';
    message.guild_contact_info = object.guild_contact_info ?? '';
    message.guild_avatar = object.guild_avatar ?? '';
    return message;
  }
};

function createBaseGetMyContactInfoReq(): GetMyContactInfoReq {
  return {};
}

export const GetMyContactInfoReq: MessageFns<GetMyContactInfoReq> = {
  fromJSON(_: any): GetMyContactInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetMyContactInfoReq>, I>>(base?: I): GetMyContactInfoReq {
    return GetMyContactInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyContactInfoReq>, I>>(_: I): GetMyContactInfoReq {
    const message = createBaseGetMyContactInfoReq();
    return message;
  }
};

function createBaseGetMyContactInfoResp(): GetMyContactInfoResp {
  return { whatsapp: '', google_mail: '' };
}

export const GetMyContactInfoResp: MessageFns<GetMyContactInfoResp> = {
  fromJSON(object: any): GetMyContactInfoResp {
    return {
      whatsapp: isSet(object.whatsapp) ? globalThis.String(object.whatsapp) : '',
      google_mail: isSet(object.google_mail) ? globalThis.String(object.google_mail) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetMyContactInfoResp>, I>>(base?: I): GetMyContactInfoResp {
    return GetMyContactInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetMyContactInfoResp>, I>>(object: I): GetMyContactInfoResp {
    const message = createBaseGetMyContactInfoResp();
    message.whatsapp = object.whatsapp ?? '';
    message.google_mail = object.google_mail ?? '';
    return message;
  }
};

function createBaseAuthChangeAvatarReq(): AuthChangeAvatarReq {
  return {};
}

export const AuthChangeAvatarReq: MessageFns<AuthChangeAvatarReq> = {
  fromJSON(_: any): AuthChangeAvatarReq {
    return {};
  },

  create<I extends Exact<DeepPartial<AuthChangeAvatarReq>, I>>(base?: I): AuthChangeAvatarReq {
    return AuthChangeAvatarReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthChangeAvatarReq>, I>>(_: I): AuthChangeAvatarReq {
    const message = createBaseAuthChangeAvatarReq();
    return message;
  }
};

function createBaseAuthChangeAvatarResp(): AuthChangeAvatarResp {
  return {};
}

export const AuthChangeAvatarResp: MessageFns<AuthChangeAvatarResp> = {
  fromJSON(_: any): AuthChangeAvatarResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AuthChangeAvatarResp>, I>>(base?: I): AuthChangeAvatarResp {
    return AuthChangeAvatarResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuthChangeAvatarResp>, I>>(_: I): AuthChangeAvatarResp {
    const message = createBaseAuthChangeAvatarResp();
    return message;
  }
};

export type AnchorDefinition = typeof AnchorDefinition;
export const AnchorDefinition = {
  name: 'Anchor',
  fullName: 'anchor.Anchor',
  methods: {
    getAnchorInfo: {
      name: 'GetAnchorInfo',
      requestType: GetAnchorInfoReq,
      requestStream: false,
      responseType: GetAnchorInfoResp,
      responseStream: false,
      options: {}
    },
    uploadAnchorAudio: {
      name: 'UploadAnchorAudio',
      requestType: UploadAnchorAudioReq,
      requestStream: false,
      responseType: UploadAnchorAudioResp,
      responseStream: false,
      options: {}
    },
    getAudioTemplate: {
      name: 'GetAudioTemplate',
      requestType: GetAudioTemplateReq,
      requestStream: false,
      responseType: GetAudioTemplateResp,
      responseStream: false,
      options: {}
    },
    /** h5 获取主播设置的收入 */
    getVideoCallIncome: {
      name: 'GetVideoCallIncome',
      requestType: GetVideoCallIncomeReq,
      requestStream: false,
      responseType: GetVideoCallIncomeResp,
      responseStream: false,
      options: {}
    },
    updateVideoCallIncome: {
      name: 'UpdateVideoCallIncome',
      requestType: UpdateVideoCallIncomeReq,
      requestStream: false,
      responseType: UpdateVideoCallIncomeResp,
      responseStream: false,
      options: {}
    },
    /** h5获取主播等级配置信息 */
    getAnchorLevelConfig: {
      name: 'GetAnchorLevelConfig',
      requestType: GetAnchorLevelConfigReq,
      requestStream: false,
      responseType: GetAnchorLevelConfigResp,
      responseStream: false,
      options: {}
    },
    /** h5获取主播等级用户信息 */
    getAnchorLevelInfo: {
      name: 'GetAnchorLevelInfo',
      requestType: GetAnchorLevelInfoReq,
      requestStream: false,
      responseType: GetAnchorLevelInfoResp,
      responseStream: false,
      options: {}
    },
    /** 新主播包首页获取弹窗类型 */
    getAnchorRegisterPopUp: {
      name: 'GetAnchorRegisterPopUp',
      requestType: GetAnchorRegisterPopUpReq,
      requestStream: false,
      responseType: GetAnchorRegisterPopUpResp,
      responseStream: false,
      options: {}
    },
    /** 获取主播工作详情列表 */
    getAnchorJobDetail: {
      name: 'GetAnchorJobDetail',
      requestType: GetAnchorJobDetailReq,
      requestStream: false,
      responseType: GetAnchorJobDetailResp,
      responseStream: false,
      options: {}
    },
    /** h5 获取积分流水 */
    getAnchorPointFlows: {
      name: 'GetAnchorPointFlows',
      requestType: GetAnchorPointFlowsReq,
      requestStream: false,
      responseType: GetAnchorPointFlowsResp,
      responseStream: false,
      options: {}
    },
    /** h5 获取我的数据  今日收入 昨日收入 本周收入 */
    getAnchorIncomeInfo: {
      name: 'GetAnchorIncomeInfo',
      requestType: GetAnchorIncomeInfoReq,
      requestStream: false,
      responseType: GetAnchorIncomeInfoResp,
      responseStream: false,
      options: {}
    },
    /** 检查是否允许修改头像 */
    checkAnchorEditPermission: {
      name: 'CheckAnchorEditPermission',
      requestType: CheckAnchorEditPermissionReq,
      requestStream: false,
      responseType: CheckAnchorEditPermissionResp,
      responseStream: false,
      options: {}
    },
    /**
     * h5 公会中心
     * ---公会长相关---
     * 首页
     */
    getGuildMasterInfo: {
      name: 'GetGuildMasterInfo',
      requestType: GetGuildMasterInfoReq,
      requestStream: false,
      responseType: GetGuildMasterInfoResp,
      responseStream: false,
      options: {}
    },
    /** 主播列表 */
    getAnchorList: {
      name: 'GetAnchorList',
      requestType: GetAnchorListReq,
      requestStream: false,
      responseType: GetAnchorListResp,
      responseStream: false,
      options: {}
    },
    /** 收入列表(主播/二级公会) */
    getIncomeList: {
      name: 'GetIncomeList',
      requestType: GetIncomeListReq,
      requestStream: false,
      responseType: GetIncomeListResp,
      responseStream: false,
      options: {}
    },
    /** 主播处罚列表 */
    getAnchorViolationList: {
      name: 'GetAnchorViolationList',
      requestType: GetAnchorViolationListReq,
      requestStream: false,
      responseType: GetAnchorViolationListResp,
      responseStream: false,
      options: {}
    },
    /** 二级公会列表 */
    getSubAgencyList: {
      name: 'GetSubAgencyList',
      requestType: GetSubAgencyListReq,
      requestStream: false,
      responseType: GetSubAgencyListResp,
      responseStream: false,
      options: {}
    },
    /** 我的总收入 */
    getMyTotalIncome: {
      name: 'GetMyTotalIncome',
      requestType: GetMyTotalIncomeReq,
      requestStream: false,
      responseType: GetMyTotalIncomeResp,
      responseStream: false,
      options: {}
    },
    /** h5 主播获取我的公会信息页面 */
    getMyGuildInfo: {
      name: 'GetMyGuildInfo',
      requestType: GetMyGuildInfoReq,
      requestStream: false,
      responseType: GetMyGuildInfoResp,
      responseStream: false,
      options: {}
    },
    /** h5 主播页获取我的联系方式 */
    getMyContactInfo: {
      name: 'GetMyContactInfo',
      requestType: GetMyContactInfoReq,
      requestStream: false,
      responseType: GetMyContactInfoResp,
      responseStream: false,
      options: {}
    },
    /** c端 真人认证失败点击change avatar按钮 */
    authChangeAvatar: {
      name: 'AuthChangeAvatar',
      requestType: AuthChangeAvatarReq,
      requestStream: false,
      responseType: AuthChangeAvatarResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
