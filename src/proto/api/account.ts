// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/account.proto

/* eslint-disable */

export const protobufPackage = 'account';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/accountsrv */

/** 账号绑定操作类型 */
export enum AccountBindOptType {
  ACCOUNT_BIND_OPT_TYPE_NONE = 0,
  /** ACCOUNT_BIND_OPT_TYPE_BIND - 绑定 */
  ACCOUNT_BIND_OPT_TYPE_BIND = 1,
  /** ACCOUNT_BIND_OPT_TYPE_UNBIND - 解绑 */
  ACCOUNT_BIND_OPT_TYPE_UNBIND = 2,
  UNRECOGNIZED = -1
}

export function accountBindOptTypeFromJSON(object: any): AccountBindOptType {
  switch (object) {
    case 0:
    case 'ACCOUNT_BIND_OPT_TYPE_NONE':
      return AccountBindOptType.ACCOUNT_BIND_OPT_TYPE_NONE;
    case 1:
    case 'ACCOUNT_BIND_OPT_TYPE_BIND':
      return AccountBindOptType.ACCOUNT_BIND_OPT_TYPE_BIND;
    case 2:
    case 'ACCOUNT_BIND_OPT_TYPE_UNBIND':
      return AccountBindOptType.ACCOUNT_BIND_OPT_TYPE_UNBIND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountBindOptType.UNRECOGNIZED;
  }
}

/** 账号封禁操作类型 */
export enum AccountBanOptType {
  ACCOUNT_BAN_OPT_NONE = 0,
  /** ACCOUNT_BAN_OPT_BAN - 封禁 */
  ACCOUNT_BAN_OPT_BAN = 1,
  /** ACCOUNT_BAN_OPT_UNBAN - 解封 */
  ACCOUNT_BAN_OPT_UNBAN = 2,
  UNRECOGNIZED = -1
}

export function accountBanOptTypeFromJSON(object: any): AccountBanOptType {
  switch (object) {
    case 0:
    case 'ACCOUNT_BAN_OPT_NONE':
      return AccountBanOptType.ACCOUNT_BAN_OPT_NONE;
    case 1:
    case 'ACCOUNT_BAN_OPT_BAN':
      return AccountBanOptType.ACCOUNT_BAN_OPT_BAN;
    case 2:
    case 'ACCOUNT_BAN_OPT_UNBAN':
      return AccountBanOptType.ACCOUNT_BAN_OPT_UNBAN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountBanOptType.UNRECOGNIZED;
  }
}

export enum AccountLoginType {
  LOGIN_NONE = 0,
  /** LOGIN_PHONE - 手机登录 */
  LOGIN_PHONE = 1,
  /** LOGIN_GOOGLE - google登录 */
  LOGIN_GOOGLE = 2,
  /** LOGIN_FAST - 快速登录 */
  LOGIN_FAST = 3,
  /** LOGIN_APPLE - 苹果登陆 */
  LOGIN_APPLE = 4,
  UNRECOGNIZED = -1
}

export function accountLoginTypeFromJSON(object: any): AccountLoginType {
  switch (object) {
    case 0:
    case 'LOGIN_NONE':
      return AccountLoginType.LOGIN_NONE;
    case 1:
    case 'LOGIN_PHONE':
      return AccountLoginType.LOGIN_PHONE;
    case 2:
    case 'LOGIN_GOOGLE':
      return AccountLoginType.LOGIN_GOOGLE;
    case 3:
    case 'LOGIN_FAST':
      return AccountLoginType.LOGIN_FAST;
    case 4:
    case 'LOGIN_APPLE':
      return AccountLoginType.LOGIN_APPLE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountLoginType.UNRECOGNIZED;
  }
}

export interface BindAccountReq {
  /** 操作类型 */
  bind_opt_type: AccountBindOptType;
}

export interface BindAccountRsp {}

export interface BanAccountReq {
  /** 操作类型 */
  ban_opt_type: AccountBanOptType;
  uid: number;
  /** 截止日期 */
  expired_at: number;
  /** 原因 */
  reason: string;
  /** 操作人 */
  operator: string;
}

export interface BanAccountRsp {}

export interface CheckTokenReq {
  /** 用户Token */
  token: string;
  /** app */
  app: string;
}

export interface CheckTokenRsp {
  /** token结构体 */
  token: Token | undefined;
}

export interface Token {
  /** 用户ID */
  uid: number;
  /** 状态 */
  status: number;
  /** 国家 */
  cou: string;
  /** 设备ID */
  did: string;
  /** 手机系统 */
  os: string;
}

export interface LoginReq {
  phone: string;
  code: string;
  third_type: string;
  openid: string;
  third_code: string;
  token: string;
  vpn: string;
  sim_cou: string;
  check_account: number;
  login_type: string;
  account: string;
  password: string;
  subanm: string;
  pf: string;
  brd: string;
  os: string;
  cha: string;
  mod: string;
  sub: string;
  cpu: string;
  cpuf: string;
  ram: string;
  gaid: string;
  is_transfer: number;
  email: string;
  apple_token: string;
}

export interface LoginRsp {
  uid: string;
  token: string;
  info: { [key: string]: string };
  type: string;
}

export interface LoginRsp_InfoEntry {
  key: string;
  value: string;
}

export interface GetPhoneCountryReq {}

export interface GetPhoneCountryRsp {
  data: GetPhoneCountryRsp_PhoneCountryItem[];
}

export interface GetPhoneCountryRsp_PhoneCountryItem {
  id: number;
  en_name: string;
  initial: string;
  country_code: number;
  flag: string;
  ar_name: string;
  nation_flag: string;
  is_delete: number;
  is_selected: boolean;
}

export interface CheckAccountReq {
  phone: string;
}

export interface CheckAccountRsp {
  code: number;
}

export interface SendPhoneCodeReq {
  phone: string;
  phone_without_code: string;
}

export interface SendPhoneCodeRsp {}

export interface VerifyPhoneCodeReq {
  phone: string;
  code: string;
}

export interface VerifyPhoneCodeRsp {
  phone: string;
  code: string;
}

export interface RegisterReq {
  phone: string;
  phone_without_code: string;
  code: string;
  password: string;
  third_type: string;
  openid: string;
  vpn: string;
  sim_cou: string;
  subanm: string;
  pf: string;
  brd: string;
  os: string;
  cha: string;
  mod: string;
  sub: string;
  cpu: string;
  cpuf: string;
  ram: string;
  gaid: string;
  is_transfer: number;
}

export interface RegisterRsp {
  uid: string;
  token: string;
}

export interface ValidateAccountTransferReq {
  login: LoginReq | undefined;
}

export interface ValidateAccountTransferRsp {
  list: ValidateAccountTransferRsp_AccountTransfer[];
}

export interface ValidateAccountTransferRsp_AccountTransfer {
  uid: string;
  nickname: string;
  avatar: string;
  coin: number;
}

export interface UserBindTransferReq {
  /** 本来原来的登录参数都给下 */
  login: LoginReq | undefined;
  /** 要转换那个uid */
  transfer_uid: string;
}

export interface UserBindTransferRsp {
  /** 返回值 */
  login_rsp: LoginRsp | undefined;
}

export interface FastLoginInfoReq {}

export interface FastLoginInfoRsp {
  /** 上次登录类型 */
  last_login_type: AccountLoginType;
  /** 是否展示快速登录 0不展示  1展示 */
  show_fast_login: number;
  /** 是否展示手机登陆 0不展示  1展示 */
  show_phone_login: number;
}

export interface CloseReq {}

export interface CloseRsp {}

function createBaseBindAccountReq(): BindAccountReq {
  return { bind_opt_type: 0 };
}

export const BindAccountReq: MessageFns<BindAccountReq> = {
  fromJSON(object: any): BindAccountReq {
    return { bind_opt_type: isSet(object.bind_opt_type) ? accountBindOptTypeFromJSON(object.bind_opt_type) : 0 };
  },

  create<I extends Exact<DeepPartial<BindAccountReq>, I>>(base?: I): BindAccountReq {
    return BindAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindAccountReq>, I>>(object: I): BindAccountReq {
    const message = createBaseBindAccountReq();
    message.bind_opt_type = object.bind_opt_type ?? 0;
    return message;
  }
};

function createBaseBindAccountRsp(): BindAccountRsp {
  return {};
}

export const BindAccountRsp: MessageFns<BindAccountRsp> = {
  fromJSON(_: any): BindAccountRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BindAccountRsp>, I>>(base?: I): BindAccountRsp {
    return BindAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindAccountRsp>, I>>(_: I): BindAccountRsp {
    const message = createBaseBindAccountRsp();
    return message;
  }
};

function createBaseBanAccountReq(): BanAccountReq {
  return { ban_opt_type: 0, uid: 0, expired_at: 0, reason: '', operator: '' };
}

export const BanAccountReq: MessageFns<BanAccountReq> = {
  fromJSON(object: any): BanAccountReq {
    return {
      ban_opt_type: isSet(object.ban_opt_type) ? accountBanOptTypeFromJSON(object.ban_opt_type) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      expired_at: isSet(object.expired_at) ? globalThis.Number(object.expired_at) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<BanAccountReq>, I>>(base?: I): BanAccountReq {
    return BanAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanAccountReq>, I>>(object: I): BanAccountReq {
    const message = createBaseBanAccountReq();
    message.ban_opt_type = object.ban_opt_type ?? 0;
    message.uid = object.uid ?? 0;
    message.expired_at = object.expired_at ?? 0;
    message.reason = object.reason ?? '';
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseBanAccountRsp(): BanAccountRsp {
  return {};
}

export const BanAccountRsp: MessageFns<BanAccountRsp> = {
  fromJSON(_: any): BanAccountRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BanAccountRsp>, I>>(base?: I): BanAccountRsp {
    return BanAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanAccountRsp>, I>>(_: I): BanAccountRsp {
    const message = createBaseBanAccountRsp();
    return message;
  }
};

function createBaseCheckTokenReq(): CheckTokenReq {
  return { token: '', app: '' };
}

export const CheckTokenReq: MessageFns<CheckTokenReq> = {
  fromJSON(object: any): CheckTokenReq {
    return {
      token: isSet(object.token) ? globalThis.String(object.token) : '',
      app: isSet(object.app) ? globalThis.String(object.app) : ''
    };
  },

  create<I extends Exact<DeepPartial<CheckTokenReq>, I>>(base?: I): CheckTokenReq {
    return CheckTokenReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckTokenReq>, I>>(object: I): CheckTokenReq {
    const message = createBaseCheckTokenReq();
    message.token = object.token ?? '';
    message.app = object.app ?? '';
    return message;
  }
};

function createBaseCheckTokenRsp(): CheckTokenRsp {
  return { token: undefined };
}

export const CheckTokenRsp: MessageFns<CheckTokenRsp> = {
  fromJSON(object: any): CheckTokenRsp {
    return { token: isSet(object.token) ? Token.fromJSON(object.token) : undefined };
  },

  create<I extends Exact<DeepPartial<CheckTokenRsp>, I>>(base?: I): CheckTokenRsp {
    return CheckTokenRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckTokenRsp>, I>>(object: I): CheckTokenRsp {
    const message = createBaseCheckTokenRsp();
    message.token = object.token !== undefined && object.token !== null ? Token.fromPartial(object.token) : undefined;
    return message;
  }
};

function createBaseToken(): Token {
  return { uid: 0, status: 0, cou: '', did: '', os: '' };
}

export const Token: MessageFns<Token> = {
  fromJSON(object: any): Token {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      os: isSet(object.os) ? globalThis.String(object.os) : ''
    };
  },

  create<I extends Exact<DeepPartial<Token>, I>>(base?: I): Token {
    return Token.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Token>, I>>(object: I): Token {
    const message = createBaseToken();
    message.uid = object.uid ?? 0;
    message.status = object.status ?? 0;
    message.cou = object.cou ?? '';
    message.did = object.did ?? '';
    message.os = object.os ?? '';
    return message;
  }
};

function createBaseLoginReq(): LoginReq {
  return {
    phone: '',
    code: '',
    third_type: '',
    openid: '',
    third_code: '',
    token: '',
    vpn: '',
    sim_cou: '',
    check_account: 0,
    login_type: '',
    account: '',
    password: '',
    subanm: '',
    pf: '',
    brd: '',
    os: '',
    cha: '',
    mod: '',
    sub: '',
    cpu: '',
    cpuf: '',
    ram: '',
    gaid: '',
    is_transfer: 0,
    email: '',
    apple_token: ''
  };
}

export const LoginReq: MessageFns<LoginReq> = {
  fromJSON(object: any): LoginReq {
    return {
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      third_type: isSet(object.third_type) ? globalThis.String(object.third_type) : '',
      openid: isSet(object.openid) ? globalThis.String(object.openid) : '',
      third_code: isSet(object.third_code) ? globalThis.String(object.third_code) : '',
      token: isSet(object.token) ? globalThis.String(object.token) : '',
      vpn: isSet(object.vpn) ? globalThis.String(object.vpn) : '',
      sim_cou: isSet(object.sim_cou) ? globalThis.String(object.sim_cou) : '',
      check_account: isSet(object.check_account) ? globalThis.Number(object.check_account) : 0,
      login_type: isSet(object.login_type) ? globalThis.String(object.login_type) : '',
      account: isSet(object.account) ? globalThis.String(object.account) : '',
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      subanm: isSet(object.subanm) ? globalThis.String(object.subanm) : '',
      pf: isSet(object.pf) ? globalThis.String(object.pf) : '',
      brd: isSet(object.brd) ? globalThis.String(object.brd) : '',
      os: isSet(object.os) ? globalThis.String(object.os) : '',
      cha: isSet(object.cha) ? globalThis.String(object.cha) : '',
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      sub: isSet(object.sub) ? globalThis.String(object.sub) : '',
      cpu: isSet(object.cpu) ? globalThis.String(object.cpu) : '',
      cpuf: isSet(object.cpuf) ? globalThis.String(object.cpuf) : '',
      ram: isSet(object.ram) ? globalThis.String(object.ram) : '',
      gaid: isSet(object.gaid) ? globalThis.String(object.gaid) : '',
      is_transfer: isSet(object.is_transfer) ? globalThis.Number(object.is_transfer) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      apple_token: isSet(object.apple_token) ? globalThis.String(object.apple_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<LoginReq>, I>>(base?: I): LoginReq {
    return LoginReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginReq>, I>>(object: I): LoginReq {
    const message = createBaseLoginReq();
    message.phone = object.phone ?? '';
    message.code = object.code ?? '';
    message.third_type = object.third_type ?? '';
    message.openid = object.openid ?? '';
    message.third_code = object.third_code ?? '';
    message.token = object.token ?? '';
    message.vpn = object.vpn ?? '';
    message.sim_cou = object.sim_cou ?? '';
    message.check_account = object.check_account ?? 0;
    message.login_type = object.login_type ?? '';
    message.account = object.account ?? '';
    message.password = object.password ?? '';
    message.subanm = object.subanm ?? '';
    message.pf = object.pf ?? '';
    message.brd = object.brd ?? '';
    message.os = object.os ?? '';
    message.cha = object.cha ?? '';
    message.mod = object.mod ?? '';
    message.sub = object.sub ?? '';
    message.cpu = object.cpu ?? '';
    message.cpuf = object.cpuf ?? '';
    message.ram = object.ram ?? '';
    message.gaid = object.gaid ?? '';
    message.is_transfer = object.is_transfer ?? 0;
    message.email = object.email ?? '';
    message.apple_token = object.apple_token ?? '';
    return message;
  }
};

function createBaseLoginRsp(): LoginRsp {
  return { uid: '', token: '', info: {}, type: '' };
}

export const LoginRsp: MessageFns<LoginRsp> = {
  fromJSON(object: any): LoginRsp {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      token: isSet(object.token) ? globalThis.String(object.token) : '',
      info: isObject(object.info)
        ? Object.entries(object.info).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      type: isSet(object.type) ? globalThis.String(object.type) : ''
    };
  },

  create<I extends Exact<DeepPartial<LoginRsp>, I>>(base?: I): LoginRsp {
    return LoginRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginRsp>, I>>(object: I): LoginRsp {
    const message = createBaseLoginRsp();
    message.uid = object.uid ?? '';
    message.token = object.token ?? '';
    message.info = Object.entries(object.info ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.type = object.type ?? '';
    return message;
  }
};

function createBaseLoginRsp_InfoEntry(): LoginRsp_InfoEntry {
  return { key: '', value: '' };
}

export const LoginRsp_InfoEntry: MessageFns<LoginRsp_InfoEntry> = {
  fromJSON(object: any): LoginRsp_InfoEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<LoginRsp_InfoEntry>, I>>(base?: I): LoginRsp_InfoEntry {
    return LoginRsp_InfoEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginRsp_InfoEntry>, I>>(object: I): LoginRsp_InfoEntry {
    const message = createBaseLoginRsp_InfoEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetPhoneCountryReq(): GetPhoneCountryReq {
  return {};
}

export const GetPhoneCountryReq: MessageFns<GetPhoneCountryReq> = {
  fromJSON(_: any): GetPhoneCountryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetPhoneCountryReq>, I>>(base?: I): GetPhoneCountryReq {
    return GetPhoneCountryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPhoneCountryReq>, I>>(_: I): GetPhoneCountryReq {
    const message = createBaseGetPhoneCountryReq();
    return message;
  }
};

function createBaseGetPhoneCountryRsp(): GetPhoneCountryRsp {
  return { data: [] };
}

export const GetPhoneCountryRsp: MessageFns<GetPhoneCountryRsp> = {
  fromJSON(object: any): GetPhoneCountryRsp {
    return {
      data: globalThis.Array.isArray(object?.data)
        ? object.data.map((e: any) => GetPhoneCountryRsp_PhoneCountryItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetPhoneCountryRsp>, I>>(base?: I): GetPhoneCountryRsp {
    return GetPhoneCountryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPhoneCountryRsp>, I>>(object: I): GetPhoneCountryRsp {
    const message = createBaseGetPhoneCountryRsp();
    message.data = object.data?.map(e => GetPhoneCountryRsp_PhoneCountryItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetPhoneCountryRsp_PhoneCountryItem(): GetPhoneCountryRsp_PhoneCountryItem {
  return {
    id: 0,
    en_name: '',
    initial: '',
    country_code: 0,
    flag: '',
    ar_name: '',
    nation_flag: '',
    is_delete: 0,
    is_selected: false
  };
}

export const GetPhoneCountryRsp_PhoneCountryItem: MessageFns<GetPhoneCountryRsp_PhoneCountryItem> = {
  fromJSON(object: any): GetPhoneCountryRsp_PhoneCountryItem {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      en_name: isSet(object.en_name) ? globalThis.String(object.en_name) : '',
      initial: isSet(object.initial) ? globalThis.String(object.initial) : '',
      country_code: isSet(object.country_code) ? globalThis.Number(object.country_code) : 0,
      flag: isSet(object.flag) ? globalThis.String(object.flag) : '',
      ar_name: isSet(object.ar_name) ? globalThis.String(object.ar_name) : '',
      nation_flag: isSet(object.nation_flag) ? globalThis.String(object.nation_flag) : '',
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0,
      is_selected: isSet(object.is_selected) ? globalThis.Boolean(object.is_selected) : false
    };
  },

  create<I extends Exact<DeepPartial<GetPhoneCountryRsp_PhoneCountryItem>, I>>(
    base?: I
  ): GetPhoneCountryRsp_PhoneCountryItem {
    return GetPhoneCountryRsp_PhoneCountryItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPhoneCountryRsp_PhoneCountryItem>, I>>(
    object: I
  ): GetPhoneCountryRsp_PhoneCountryItem {
    const message = createBaseGetPhoneCountryRsp_PhoneCountryItem();
    message.id = object.id ?? 0;
    message.en_name = object.en_name ?? '';
    message.initial = object.initial ?? '';
    message.country_code = object.country_code ?? 0;
    message.flag = object.flag ?? '';
    message.ar_name = object.ar_name ?? '';
    message.nation_flag = object.nation_flag ?? '';
    message.is_delete = object.is_delete ?? 0;
    message.is_selected = object.is_selected ?? false;
    return message;
  }
};

function createBaseCheckAccountReq(): CheckAccountReq {
  return { phone: '' };
}

export const CheckAccountReq: MessageFns<CheckAccountReq> = {
  fromJSON(object: any): CheckAccountReq {
    return { phone: isSet(object.phone) ? globalThis.String(object.phone) : '' };
  },

  create<I extends Exact<DeepPartial<CheckAccountReq>, I>>(base?: I): CheckAccountReq {
    return CheckAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckAccountReq>, I>>(object: I): CheckAccountReq {
    const message = createBaseCheckAccountReq();
    message.phone = object.phone ?? '';
    return message;
  }
};

function createBaseCheckAccountRsp(): CheckAccountRsp {
  return { code: 0 };
}

export const CheckAccountRsp: MessageFns<CheckAccountRsp> = {
  fromJSON(object: any): CheckAccountRsp {
    return { code: isSet(object.code) ? globalThis.Number(object.code) : 0 };
  },

  create<I extends Exact<DeepPartial<CheckAccountRsp>, I>>(base?: I): CheckAccountRsp {
    return CheckAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckAccountRsp>, I>>(object: I): CheckAccountRsp {
    const message = createBaseCheckAccountRsp();
    message.code = object.code ?? 0;
    return message;
  }
};

function createBaseSendPhoneCodeReq(): SendPhoneCodeReq {
  return { phone: '', phone_without_code: '' };
}

export const SendPhoneCodeReq: MessageFns<SendPhoneCodeReq> = {
  fromJSON(object: any): SendPhoneCodeReq {
    return {
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      phone_without_code: isSet(object.phone_without_code) ? globalThis.String(object.phone_without_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendPhoneCodeReq>, I>>(base?: I): SendPhoneCodeReq {
    return SendPhoneCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendPhoneCodeReq>, I>>(object: I): SendPhoneCodeReq {
    const message = createBaseSendPhoneCodeReq();
    message.phone = object.phone ?? '';
    message.phone_without_code = object.phone_without_code ?? '';
    return message;
  }
};

function createBaseSendPhoneCodeRsp(): SendPhoneCodeRsp {
  return {};
}

export const SendPhoneCodeRsp: MessageFns<SendPhoneCodeRsp> = {
  fromJSON(_: any): SendPhoneCodeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SendPhoneCodeRsp>, I>>(base?: I): SendPhoneCodeRsp {
    return SendPhoneCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendPhoneCodeRsp>, I>>(_: I): SendPhoneCodeRsp {
    const message = createBaseSendPhoneCodeRsp();
    return message;
  }
};

function createBaseVerifyPhoneCodeReq(): VerifyPhoneCodeReq {
  return { phone: '', code: '' };
}

export const VerifyPhoneCodeReq: MessageFns<VerifyPhoneCodeReq> = {
  fromJSON(object: any): VerifyPhoneCodeReq {
    return {
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      code: isSet(object.code) ? globalThis.String(object.code) : ''
    };
  },

  create<I extends Exact<DeepPartial<VerifyPhoneCodeReq>, I>>(base?: I): VerifyPhoneCodeReq {
    return VerifyPhoneCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyPhoneCodeReq>, I>>(object: I): VerifyPhoneCodeReq {
    const message = createBaseVerifyPhoneCodeReq();
    message.phone = object.phone ?? '';
    message.code = object.code ?? '';
    return message;
  }
};

function createBaseVerifyPhoneCodeRsp(): VerifyPhoneCodeRsp {
  return { phone: '', code: '' };
}

export const VerifyPhoneCodeRsp: MessageFns<VerifyPhoneCodeRsp> = {
  fromJSON(object: any): VerifyPhoneCodeRsp {
    return {
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      code: isSet(object.code) ? globalThis.String(object.code) : ''
    };
  },

  create<I extends Exact<DeepPartial<VerifyPhoneCodeRsp>, I>>(base?: I): VerifyPhoneCodeRsp {
    return VerifyPhoneCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VerifyPhoneCodeRsp>, I>>(object: I): VerifyPhoneCodeRsp {
    const message = createBaseVerifyPhoneCodeRsp();
    message.phone = object.phone ?? '';
    message.code = object.code ?? '';
    return message;
  }
};

function createBaseRegisterReq(): RegisterReq {
  return {
    phone: '',
    phone_without_code: '',
    code: '',
    password: '',
    third_type: '',
    openid: '',
    vpn: '',
    sim_cou: '',
    subanm: '',
    pf: '',
    brd: '',
    os: '',
    cha: '',
    mod: '',
    sub: '',
    cpu: '',
    cpuf: '',
    ram: '',
    gaid: '',
    is_transfer: 0
  };
}

export const RegisterReq: MessageFns<RegisterReq> = {
  fromJSON(object: any): RegisterReq {
    return {
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      phone_without_code: isSet(object.phone_without_code) ? globalThis.String(object.phone_without_code) : '',
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      third_type: isSet(object.third_type) ? globalThis.String(object.third_type) : '',
      openid: isSet(object.openid) ? globalThis.String(object.openid) : '',
      vpn: isSet(object.vpn) ? globalThis.String(object.vpn) : '',
      sim_cou: isSet(object.sim_cou) ? globalThis.String(object.sim_cou) : '',
      subanm: isSet(object.subanm) ? globalThis.String(object.subanm) : '',
      pf: isSet(object.pf) ? globalThis.String(object.pf) : '',
      brd: isSet(object.brd) ? globalThis.String(object.brd) : '',
      os: isSet(object.os) ? globalThis.String(object.os) : '',
      cha: isSet(object.cha) ? globalThis.String(object.cha) : '',
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      sub: isSet(object.sub) ? globalThis.String(object.sub) : '',
      cpu: isSet(object.cpu) ? globalThis.String(object.cpu) : '',
      cpuf: isSet(object.cpuf) ? globalThis.String(object.cpuf) : '',
      ram: isSet(object.ram) ? globalThis.String(object.ram) : '',
      gaid: isSet(object.gaid) ? globalThis.String(object.gaid) : '',
      is_transfer: isSet(object.is_transfer) ? globalThis.Number(object.is_transfer) : 0
    };
  },

  create<I extends Exact<DeepPartial<RegisterReq>, I>>(base?: I): RegisterReq {
    return RegisterReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegisterReq>, I>>(object: I): RegisterReq {
    const message = createBaseRegisterReq();
    message.phone = object.phone ?? '';
    message.phone_without_code = object.phone_without_code ?? '';
    message.code = object.code ?? '';
    message.password = object.password ?? '';
    message.third_type = object.third_type ?? '';
    message.openid = object.openid ?? '';
    message.vpn = object.vpn ?? '';
    message.sim_cou = object.sim_cou ?? '';
    message.subanm = object.subanm ?? '';
    message.pf = object.pf ?? '';
    message.brd = object.brd ?? '';
    message.os = object.os ?? '';
    message.cha = object.cha ?? '';
    message.mod = object.mod ?? '';
    message.sub = object.sub ?? '';
    message.cpu = object.cpu ?? '';
    message.cpuf = object.cpuf ?? '';
    message.ram = object.ram ?? '';
    message.gaid = object.gaid ?? '';
    message.is_transfer = object.is_transfer ?? 0;
    return message;
  }
};

function createBaseRegisterRsp(): RegisterRsp {
  return { uid: '', token: '' };
}

export const RegisterRsp: MessageFns<RegisterRsp> = {
  fromJSON(object: any): RegisterRsp {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      token: isSet(object.token) ? globalThis.String(object.token) : ''
    };
  },

  create<I extends Exact<DeepPartial<RegisterRsp>, I>>(base?: I): RegisterRsp {
    return RegisterRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegisterRsp>, I>>(object: I): RegisterRsp {
    const message = createBaseRegisterRsp();
    message.uid = object.uid ?? '';
    message.token = object.token ?? '';
    return message;
  }
};

function createBaseValidateAccountTransferReq(): ValidateAccountTransferReq {
  return { login: undefined };
}

export const ValidateAccountTransferReq: MessageFns<ValidateAccountTransferReq> = {
  fromJSON(object: any): ValidateAccountTransferReq {
    return { login: isSet(object.login) ? LoginReq.fromJSON(object.login) : undefined };
  },

  create<I extends Exact<DeepPartial<ValidateAccountTransferReq>, I>>(base?: I): ValidateAccountTransferReq {
    return ValidateAccountTransferReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ValidateAccountTransferReq>, I>>(object: I): ValidateAccountTransferReq {
    const message = createBaseValidateAccountTransferReq();
    message.login =
      object.login !== undefined && object.login !== null ? LoginReq.fromPartial(object.login) : undefined;
    return message;
  }
};

function createBaseValidateAccountTransferRsp(): ValidateAccountTransferRsp {
  return { list: [] };
}

export const ValidateAccountTransferRsp: MessageFns<ValidateAccountTransferRsp> = {
  fromJSON(object: any): ValidateAccountTransferRsp {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => ValidateAccountTransferRsp_AccountTransfer.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ValidateAccountTransferRsp>, I>>(base?: I): ValidateAccountTransferRsp {
    return ValidateAccountTransferRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ValidateAccountTransferRsp>, I>>(object: I): ValidateAccountTransferRsp {
    const message = createBaseValidateAccountTransferRsp();
    message.list = object.list?.map(e => ValidateAccountTransferRsp_AccountTransfer.fromPartial(e)) || [];
    return message;
  }
};

function createBaseValidateAccountTransferRsp_AccountTransfer(): ValidateAccountTransferRsp_AccountTransfer {
  return { uid: '', nickname: '', avatar: '', coin: 0 };
}

export const ValidateAccountTransferRsp_AccountTransfer: MessageFns<ValidateAccountTransferRsp_AccountTransfer> = {
  fromJSON(object: any): ValidateAccountTransferRsp_AccountTransfer {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      coin: isSet(object.coin) ? globalThis.Number(object.coin) : 0
    };
  },

  create<I extends Exact<DeepPartial<ValidateAccountTransferRsp_AccountTransfer>, I>>(
    base?: I
  ): ValidateAccountTransferRsp_AccountTransfer {
    return ValidateAccountTransferRsp_AccountTransfer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ValidateAccountTransferRsp_AccountTransfer>, I>>(
    object: I
  ): ValidateAccountTransferRsp_AccountTransfer {
    const message = createBaseValidateAccountTransferRsp_AccountTransfer();
    message.uid = object.uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.coin = object.coin ?? 0;
    return message;
  }
};

function createBaseUserBindTransferReq(): UserBindTransferReq {
  return { login: undefined, transfer_uid: '' };
}

export const UserBindTransferReq: MessageFns<UserBindTransferReq> = {
  fromJSON(object: any): UserBindTransferReq {
    return {
      login: isSet(object.login) ? LoginReq.fromJSON(object.login) : undefined,
      transfer_uid: isSet(object.transfer_uid) ? globalThis.String(object.transfer_uid) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserBindTransferReq>, I>>(base?: I): UserBindTransferReq {
    return UserBindTransferReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBindTransferReq>, I>>(object: I): UserBindTransferReq {
    const message = createBaseUserBindTransferReq();
    message.login =
      object.login !== undefined && object.login !== null ? LoginReq.fromPartial(object.login) : undefined;
    message.transfer_uid = object.transfer_uid ?? '';
    return message;
  }
};

function createBaseUserBindTransferRsp(): UserBindTransferRsp {
  return { login_rsp: undefined };
}

export const UserBindTransferRsp: MessageFns<UserBindTransferRsp> = {
  fromJSON(object: any): UserBindTransferRsp {
    return { login_rsp: isSet(object.login_rsp) ? LoginRsp.fromJSON(object.login_rsp) : undefined };
  },

  create<I extends Exact<DeepPartial<UserBindTransferRsp>, I>>(base?: I): UserBindTransferRsp {
    return UserBindTransferRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserBindTransferRsp>, I>>(object: I): UserBindTransferRsp {
    const message = createBaseUserBindTransferRsp();
    message.login_rsp =
      object.login_rsp !== undefined && object.login_rsp !== null ? LoginRsp.fromPartial(object.login_rsp) : undefined;
    return message;
  }
};

function createBaseFastLoginInfoReq(): FastLoginInfoReq {
  return {};
}

export const FastLoginInfoReq: MessageFns<FastLoginInfoReq> = {
  fromJSON(_: any): FastLoginInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<FastLoginInfoReq>, I>>(base?: I): FastLoginInfoReq {
    return FastLoginInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FastLoginInfoReq>, I>>(_: I): FastLoginInfoReq {
    const message = createBaseFastLoginInfoReq();
    return message;
  }
};

function createBaseFastLoginInfoRsp(): FastLoginInfoRsp {
  return { last_login_type: 0, show_fast_login: 0, show_phone_login: 0 };
}

export const FastLoginInfoRsp: MessageFns<FastLoginInfoRsp> = {
  fromJSON(object: any): FastLoginInfoRsp {
    return {
      last_login_type: isSet(object.last_login_type) ? accountLoginTypeFromJSON(object.last_login_type) : 0,
      show_fast_login: isSet(object.show_fast_login) ? globalThis.Number(object.show_fast_login) : 0,
      show_phone_login: isSet(object.show_phone_login) ? globalThis.Number(object.show_phone_login) : 0
    };
  },

  create<I extends Exact<DeepPartial<FastLoginInfoRsp>, I>>(base?: I): FastLoginInfoRsp {
    return FastLoginInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FastLoginInfoRsp>, I>>(object: I): FastLoginInfoRsp {
    const message = createBaseFastLoginInfoRsp();
    message.last_login_type = object.last_login_type ?? 0;
    message.show_fast_login = object.show_fast_login ?? 0;
    message.show_phone_login = object.show_phone_login ?? 0;
    return message;
  }
};

function createBaseCloseReq(): CloseReq {
  return {};
}

export const CloseReq: MessageFns<CloseReq> = {
  fromJSON(_: any): CloseReq {
    return {};
  },

  create<I extends Exact<DeepPartial<CloseReq>, I>>(base?: I): CloseReq {
    return CloseReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CloseReq>, I>>(_: I): CloseReq {
    const message = createBaseCloseReq();
    return message;
  }
};

function createBaseCloseRsp(): CloseRsp {
  return {};
}

export const CloseRsp: MessageFns<CloseRsp> = {
  fromJSON(_: any): CloseRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CloseRsp>, I>>(base?: I): CloseRsp {
    return CloseRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CloseRsp>, I>>(_: I): CloseRsp {
    const message = createBaseCloseRsp();
    return message;
  }
};

export type AccountSvrDefinition = typeof AccountSvrDefinition;
export const AccountSvrDefinition = {
  name: 'AccountSvr',
  fullName: 'account.AccountSvr',
  methods: {
    /** 绑定用户操作 */
    bindAccount: {
      name: 'BindAccount',
      requestType: BindAccountReq,
      requestStream: false,
      responseType: BindAccountRsp,
      responseStream: false,
      options: {}
    },
    /** 封禁用户操作 */
    banAccount: {
      name: 'BanAccount',
      requestType: BanAccountReq,
      requestStream: false,
      responseType: BanAccountRsp,
      responseStream: false,
      options: {}
    },
    /** Token校验 */
    checkToken: {
      name: 'CheckToken',
      requestType: CheckTokenReq,
      requestStream: false,
      responseType: CheckTokenRsp,
      responseStream: false,
      options: {}
    },
    /** 登录 */
    login: {
      name: 'Login',
      requestType: LoginReq,
      requestStream: false,
      responseType: LoginRsp,
      responseStream: false,
      options: {}
    },
    /** 获取国家手机区号 */
    getPhoneCountry: {
      name: 'GetPhoneCountry',
      requestType: GetPhoneCountryReq,
      requestStream: false,
      responseType: GetPhoneCountryRsp,
      responseStream: false,
      options: {}
    },
    /** 查询手机号是否注册过 */
    checkAccount: {
      name: 'CheckAccount',
      requestType: CheckAccountReq,
      requestStream: false,
      responseType: CheckAccountRsp,
      responseStream: false,
      options: {}
    },
    /** 发送验证码 */
    sendPhoneCode: {
      name: 'SendPhoneCode',
      requestType: SendPhoneCodeReq,
      requestStream: false,
      responseType: SendPhoneCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 验证验证码 */
    verifyPhoneCode: {
      name: 'VerifyPhoneCode',
      requestType: VerifyPhoneCodeReq,
      requestStream: false,
      responseType: VerifyPhoneCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 注册 */
    register: {
      name: 'Register',
      requestType: RegisterReq,
      requestStream: false,
      responseType: RegisterRsp,
      responseStream: false,
      options: {}
    },
    /** 判断是否需要转换 */
    validateAccountTransfer: {
      name: 'ValidateAccountTransfer',
      requestType: ValidateAccountTransferReq,
      requestStream: false,
      responseType: ValidateAccountTransferRsp,
      responseStream: false,
      options: {}
    },
    /** 进行转换接口 */
    userBindTransfer: {
      name: 'UserBindTransfer',
      requestType: UserBindTransferReq,
      requestStream: false,
      responseType: UserBindTransferRsp,
      responseStream: false,
      options: {}
    },
    /** 拉去快速登录信息 */
    fastLoginInfo: {
      name: 'FastLoginInfo',
      requestType: FastLoginInfoReq,
      requestStream: false,
      responseType: FastLoginInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 删除账号 */
    close: {
      name: 'Close',
      requestType: CloseReq,
      requestStream: false,
      responseType: CloseRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
