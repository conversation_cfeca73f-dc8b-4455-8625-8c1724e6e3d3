// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/recharge.proto

/* eslint-disable */

export const protobufPackage = 'recharge';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/rechargesrv */

export interface SimulateRechargeReq {
  order_no: string;
  /** 不传递，默认quarkpay */
  pay_type: string;
  /** sku，必传 */
  sku: string;
  /** 手机号码，不传递，默认7777777777 */
  phone_number: string;
  /** 是否谷歌支付，传递1:pay_type都是google_play, 传递0，pay_type是PHONEPE、GOOGLEPAY、flycat、PAYTM */
  is_by_sdk: string;
}

export interface SimulateRechargeResp {}

export interface ReportReq {
  pay_type: string[];
}

export interface ReportResp {}

function createBaseSimulateRechargeReq(): SimulateRechargeReq {
  return { order_no: '', pay_type: '', sku: '', phone_number: '', is_by_sdk: '' };
}

export const SimulateRechargeReq: MessageFns<SimulateRechargeReq> = {
  fromJSON(object: any): SimulateRechargeReq {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      phone_number: isSet(object.phone_number) ? globalThis.String(object.phone_number) : '',
      is_by_sdk: isSet(object.is_by_sdk) ? globalThis.String(object.is_by_sdk) : ''
    };
  },

  create<I extends Exact<DeepPartial<SimulateRechargeReq>, I>>(base?: I): SimulateRechargeReq {
    return SimulateRechargeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimulateRechargeReq>, I>>(object: I): SimulateRechargeReq {
    const message = createBaseSimulateRechargeReq();
    message.order_no = object.order_no ?? '';
    message.pay_type = object.pay_type ?? '';
    message.sku = object.sku ?? '';
    message.phone_number = object.phone_number ?? '';
    message.is_by_sdk = object.is_by_sdk ?? '';
    return message;
  }
};

function createBaseSimulateRechargeResp(): SimulateRechargeResp {
  return {};
}

export const SimulateRechargeResp: MessageFns<SimulateRechargeResp> = {
  fromJSON(_: any): SimulateRechargeResp {
    return {};
  },

  create<I extends Exact<DeepPartial<SimulateRechargeResp>, I>>(base?: I): SimulateRechargeResp {
    return SimulateRechargeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SimulateRechargeResp>, I>>(_: I): SimulateRechargeResp {
    const message = createBaseSimulateRechargeResp();
    return message;
  }
};

function createBaseReportReq(): ReportReq {
  return { pay_type: [] };
}

export const ReportReq: MessageFns<ReportReq> = {
  fromJSON(object: any): ReportReq {
    return {
      pay_type: globalThis.Array.isArray(object?.pay_type) ? object.pay_type.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ReportReq>, I>>(base?: I): ReportReq {
    return ReportReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportReq>, I>>(object: I): ReportReq {
    const message = createBaseReportReq();
    message.pay_type = object.pay_type?.map(e => e) || [];
    return message;
  }
};

function createBaseReportResp(): ReportResp {
  return {};
}

export const ReportResp: MessageFns<ReportResp> = {
  fromJSON(_: any): ReportResp {
    return {};
  },

  create<I extends Exact<DeepPartial<ReportResp>, I>>(base?: I): ReportResp {
    return ReportResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReportResp>, I>>(_: I): ReportResp {
    const message = createBaseReportResp();
    return message;
  }
};

export type RechargeDefinition = typeof RechargeDefinition;
export const RechargeDefinition = {
  name: 'Recharge',
  fullName: 'recharge.Recharge',
  methods: {
    simulateRecharge: {
      name: 'SimulateRecharge',
      requestType: SimulateRechargeReq,
      requestStream: false,
      responseType: SimulateRechargeResp,
      responseStream: false,
      options: {}
    },
    /** 上报相关支付方式 */
    report: {
      name: 'Report',
      requestType: ReportReq,
      requestStream: false,
      responseType: ReportResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
