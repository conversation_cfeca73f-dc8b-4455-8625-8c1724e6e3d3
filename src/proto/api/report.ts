// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/report.proto

/* eslint-disable */

export const protobufPackage = 'core';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/core/handler */

/** 心跳场景 */
export enum HeartbeatScene {
  HEARTBEAT_SCENE_NONE = 0,
  /** HEARTBEAT_SCENE_ROOM - 语聊房心跳 */
  HEARTBEAT_SCENE_ROOM = 1,
  /** HEARTBEAT_SCENE_LIVE - 直播间心跳 */
  HEARTBEAT_SCENE_LIVE = 2,
  UNRECOGNIZED = -1
}

export function heartbeatSceneFromJSON(object: any): HeartbeatScene {
  switch (object) {
    case 0:
    case 'HEARTBEAT_SCENE_NONE':
      return HeartbeatScene.HEARTBEAT_SCENE_NONE;
    case 1:
    case 'HEARTBEAT_SCENE_ROOM':
      return HeartbeatScene.HEARTBEAT_SCENE_ROOM;
    case 2:
    case 'HEARTBEAT_SCENE_LIVE':
      return HeartbeatScene.HEARTBEAT_SCENE_LIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return HeartbeatScene.UNRECOGNIZED;
  }
}

/** 心跳参数 */
export interface HeartbeatReq {
  /** 场景 */
  scene: HeartbeatScene;
  /** 非必须，房间号，在房间内携带，其他页面不需要 */
  room_id: number;
  /** 非必须，场次号，在直播间需要携带，其他页面不需要 */
  live_id: number;
}

export interface HeartbeatRsp {
  /** 下一次心跳的时间间隔，单位秒，如：10 表示10秒后再次出发心跳上报 */
  next_period: number;
}

function createBaseHeartbeatReq(): HeartbeatReq {
  return { scene: 0, room_id: 0, live_id: 0 };
}

export const HeartbeatReq: MessageFns<HeartbeatReq> = {
  fromJSON(object: any): HeartbeatReq {
    return {
      scene: isSet(object.scene) ? heartbeatSceneFromJSON(object.scene) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<HeartbeatReq>, I>>(base?: I): HeartbeatReq {
    return HeartbeatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeartbeatReq>, I>>(object: I): HeartbeatReq {
    const message = createBaseHeartbeatReq();
    message.scene = object.scene ?? 0;
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseHeartbeatRsp(): HeartbeatRsp {
  return { next_period: 0 };
}

export const HeartbeatRsp: MessageFns<HeartbeatRsp> = {
  fromJSON(object: any): HeartbeatRsp {
    return { next_period: isSet(object.next_period) ? globalThis.Number(object.next_period) : 0 };
  },

  create<I extends Exact<DeepPartial<HeartbeatRsp>, I>>(base?: I): HeartbeatRsp {
    return HeartbeatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HeartbeatRsp>, I>>(object: I): HeartbeatRsp {
    const message = createBaseHeartbeatRsp();
    message.next_period = object.next_period ?? 0;
    return message;
  }
};

/** 上报协议 */
export type ReportDefinition = typeof ReportDefinition;
export const ReportDefinition = {
  name: 'Report',
  fullName: 'core.Report',
  methods: {
    /** 心跳上报 */
    heartbeat: {
      name: 'Heartbeat',
      requestType: HeartbeatReq,
      requestStream: false,
      responseType: HeartbeatRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
