// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/guild.proto

/* eslint-disable */

export const protobufPackage = 'guild';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/guildsrv */

export interface GetGuildByCodeReq {
  code: string;
}

export interface GetGuildByCodeResp {}

function createBaseGetGuildByCodeReq(): GetGuildByCodeReq {
  return { code: '' };
}

export const GetGuildByCodeReq: MessageFns<GetGuildByCodeReq> = {
  fromJSON(object: any): GetGuildByCodeReq {
    return { code: isSet(object.code) ? globalThis.String(object.code) : '' };
  },

  create<I extends Exact<DeepPartial<GetGuildByCodeReq>, I>>(base?: I): GetGuildByCodeReq {
    return GetGuildByCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGuildByCodeReq>, I>>(object: I): GetGuildByCodeReq {
    const message = createBaseGetGuildByCodeReq();
    message.code = object.code ?? '';
    return message;
  }
};

function createBaseGetGuildByCodeResp(): GetGuildByCodeResp {
  return {};
}

export const GetGuildByCodeResp: MessageFns<GetGuildByCodeResp> = {
  fromJSON(_: any): GetGuildByCodeResp {
    return {};
  },

  create<I extends Exact<DeepPartial<GetGuildByCodeResp>, I>>(base?: I): GetGuildByCodeResp {
    return GetGuildByCodeResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGuildByCodeResp>, I>>(_: I): GetGuildByCodeResp {
    const message = createBaseGetGuildByCodeResp();
    return message;
  }
};

export type GuildDefinition = typeof GuildDefinition;
export const GuildDefinition = {
  name: 'Guild',
  fullName: 'guild.Guild',
  methods: {
    getGuildByCode: {
      name: 'GetGuildByCode',
      requestType: GetGuildByCodeReq,
      requestStream: false,
      responseType: GetGuildByCodeResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
