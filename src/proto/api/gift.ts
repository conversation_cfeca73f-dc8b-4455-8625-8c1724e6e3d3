// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/gift.proto

/* eslint-disable */

export const protobufPackage = 'gift';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/handler/giftsrv */

export enum SendScene {
  SEND_GIFT_SCENE_NONE = 0,
  /** SEND_GIFT_SCENE_CHAT - 语聊房场景 */
  SEND_GIFT_SCENE_CHAT = 1,
  /** SEND_GIFT_SCENE_LIVE - 直播场景 */
  SEND_GIFT_SCENE_LIVE = 2,
  UNRECOGNIZED = -1
}

export function sendSceneFromJSON(object: any): SendScene {
  switch (object) {
    case 0:
    case 'SEND_GIFT_SCENE_NONE':
      return SendScene.SEND_GIFT_SCENE_NONE;
    case 1:
    case 'SEND_GIFT_SCENE_CHAT':
      return SendScene.SEND_GIFT_SCENE_CHAT;
    case 2:
    case 'SEND_GIFT_SCENE_LIVE':
      return SendScene.SEND_GIFT_SCENE_LIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SendScene.UNRECOGNIZED;
  }
}

export enum GiftResourceType {
  TYPE_NONE = 0,
  /** TYPE_IMG - 图片 */
  TYPE_IMG = 1,
  /** TYPE_SVGA - svga */
  TYPE_SVGA = 2,
  UNRECOGNIZED = -1
}

export function giftResourceTypeFromJSON(object: any): GiftResourceType {
  switch (object) {
    case 0:
    case 'TYPE_NONE':
      return GiftResourceType.TYPE_NONE;
    case 1:
    case 'TYPE_IMG':
      return GiftResourceType.TYPE_IMG;
    case 2:
    case 'TYPE_SVGA':
      return GiftResourceType.TYPE_SVGA;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftResourceType.UNRECOGNIZED;
  }
}

export interface SendGuidanceGiftMessage {
  /** 男用户uid */
  uid: string;
  /** 配置id */
  conf_id: number;
  /** 展示时长 */
  duration: number;
  /** 场景，私信：im，通话：video */
  scene: string;
  /** 礼物信息 */
  gift_info: SendGuidanceGiftMessage_GiftInfo | undefined;
}

export interface SendGuidanceGiftMessage_GiftInfo {
  /** 礼物id */
  id: number;
  /** 礼物名称 */
  name: string;
  /** 礼物金币 */
  coin: number;
  /** 礼物顺序 */
  order: number;
  /** 礼物类型 */
  type: string;
  /** 图片url */
  img_url: string;
  materials_type: number;
  materials_url: string;
  ext_info: string;
  /** 礼物金币展示字段 */
  coin_show: number;
}

export interface Notify {
  msg: SendGuidanceGiftMessage | undefined;
}

export interface SendReq {
  /** 礼物id */
  gift_id: number;
  /** 收礼用户id 列表 */
  fuids: string[];
  /** 场景 */
  scene: SendScene;
  /** 数量 */
  nums: number;
  /** 额外信息 */
  extra: SendReq_Extra | undefined;
}

export interface SendReq_Extra {
  /** 房间id */
  room_id: number;
  /** 场次id */
  live_id: number;
}

export interface SendResp {
  /** 金币余额 */
  coin: number;
  /** 金币展示字段 */
  coin_show: number;
}

export interface InviteSendReq {
  /** 房间id */
  room_id: number;
  /** 礼物id */
  gift_id: number;
  /** 数量 */
  nums: number;
}

export interface InviteSendResp {}

/** 心愿礼物 */
export interface GiftWishlistRsp {
  wish_list: GiftWishlistData[];
  /** 这个接口 开播和进房不返回 单独给的时候才返回 */
  wishlist_rank: GiftWishlistRankData[];
}

export interface GiftWishlistData {
  gift_id: number;
  gift_img_url: string;
  progress: number;
  target: number;
  gift_name: string;
  point: number;
}

export interface GiftWishlistRankData {
  uid: string;
  avatar_url: string;
  /** 消费钻石 */
  consume_coins: number;
  /** 收益积分 */
  revenue_points: number;
}

function createBaseSendGuidanceGiftMessage(): SendGuidanceGiftMessage {
  return { uid: '', conf_id: 0, duration: 0, scene: '', gift_info: undefined };
}

export const SendGuidanceGiftMessage: MessageFns<SendGuidanceGiftMessage> = {
  fromJSON(object: any): SendGuidanceGiftMessage {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      conf_id: isSet(object.conf_id) ? globalThis.Number(object.conf_id) : 0,
      duration: isSet(object.duration) ? globalThis.Number(object.duration) : 0,
      scene: isSet(object.scene) ? globalThis.String(object.scene) : '',
      gift_info: isSet(object.gift_info) ? SendGuidanceGiftMessage_GiftInfo.fromJSON(object.gift_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SendGuidanceGiftMessage>, I>>(base?: I): SendGuidanceGiftMessage {
    return SendGuidanceGiftMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGuidanceGiftMessage>, I>>(object: I): SendGuidanceGiftMessage {
    const message = createBaseSendGuidanceGiftMessage();
    message.uid = object.uid ?? '';
    message.conf_id = object.conf_id ?? 0;
    message.duration = object.duration ?? 0;
    message.scene = object.scene ?? '';
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null
        ? SendGuidanceGiftMessage_GiftInfo.fromPartial(object.gift_info)
        : undefined;
    return message;
  }
};

function createBaseSendGuidanceGiftMessage_GiftInfo(): SendGuidanceGiftMessage_GiftInfo {
  return {
    id: 0,
    name: '',
    coin: 0,
    order: 0,
    type: '',
    img_url: '',
    materials_type: 0,
    materials_url: '',
    ext_info: '',
    coin_show: 0
  };
}

export const SendGuidanceGiftMessage_GiftInfo: MessageFns<SendGuidanceGiftMessage_GiftInfo> = {
  fromJSON(object: any): SendGuidanceGiftMessage_GiftInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      coin: isSet(object.coin) ? globalThis.Number(object.coin) : 0,
      order: isSet(object.order) ? globalThis.Number(object.order) : 0,
      type: isSet(object.type) ? globalThis.String(object.type) : '',
      img_url: isSet(object.img_url) ? globalThis.String(object.img_url) : '',
      materials_type: isSet(object.materials_type) ? globalThis.Number(object.materials_type) : 0,
      materials_url: isSet(object.materials_url) ? globalThis.String(object.materials_url) : '',
      ext_info: isSet(object.ext_info) ? globalThis.String(object.ext_info) : '',
      coin_show: isSet(object.coin_show) ? globalThis.Number(object.coin_show) : 0
    };
  },

  create<I extends Exact<DeepPartial<SendGuidanceGiftMessage_GiftInfo>, I>>(
    base?: I
  ): SendGuidanceGiftMessage_GiftInfo {
    return SendGuidanceGiftMessage_GiftInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGuidanceGiftMessage_GiftInfo>, I>>(
    object: I
  ): SendGuidanceGiftMessage_GiftInfo {
    const message = createBaseSendGuidanceGiftMessage_GiftInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.coin = object.coin ?? 0;
    message.order = object.order ?? 0;
    message.type = object.type ?? '';
    message.img_url = object.img_url ?? '';
    message.materials_type = object.materials_type ?? 0;
    message.materials_url = object.materials_url ?? '';
    message.ext_info = object.ext_info ?? '';
    message.coin_show = object.coin_show ?? 0;
    return message;
  }
};

function createBaseNotify(): Notify {
  return { msg: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return { msg: isSet(object.msg) ? SendGuidanceGiftMessage.fromJSON(object.msg) : undefined };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.msg =
      object.msg !== undefined && object.msg !== null ? SendGuidanceGiftMessage.fromPartial(object.msg) : undefined;
    return message;
  }
};

function createBaseSendReq(): SendReq {
  return { gift_id: 0, fuids: [], scene: 0, nums: 0, extra: undefined };
}

export const SendReq: MessageFns<SendReq> = {
  fromJSON(object: any): SendReq {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      fuids: globalThis.Array.isArray(object?.fuids) ? object.fuids.map((e: any) => globalThis.String(e)) : [],
      scene: isSet(object.scene) ? sendSceneFromJSON(object.scene) : 0,
      nums: isSet(object.nums) ? globalThis.Number(object.nums) : 0,
      extra: isSet(object.extra) ? SendReq_Extra.fromJSON(object.extra) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SendReq>, I>>(base?: I): SendReq {
    return SendReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendReq>, I>>(object: I): SendReq {
    const message = createBaseSendReq();
    message.gift_id = object.gift_id ?? 0;
    message.fuids = object.fuids?.map(e => e) || [];
    message.scene = object.scene ?? 0;
    message.nums = object.nums ?? 0;
    message.extra =
      object.extra !== undefined && object.extra !== null ? SendReq_Extra.fromPartial(object.extra) : undefined;
    return message;
  }
};

function createBaseSendReq_Extra(): SendReq_Extra {
  return { room_id: 0, live_id: 0 };
}

export const SendReq_Extra: MessageFns<SendReq_Extra> = {
  fromJSON(object: any): SendReq_Extra {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      live_id: isSet(object.live_id) ? globalThis.Number(object.live_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<SendReq_Extra>, I>>(base?: I): SendReq_Extra {
    return SendReq_Extra.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendReq_Extra>, I>>(object: I): SendReq_Extra {
    const message = createBaseSendReq_Extra();
    message.room_id = object.room_id ?? 0;
    message.live_id = object.live_id ?? 0;
    return message;
  }
};

function createBaseSendResp(): SendResp {
  return { coin: 0, coin_show: 0 };
}

export const SendResp: MessageFns<SendResp> = {
  fromJSON(object: any): SendResp {
    return {
      coin: isSet(object.coin) ? globalThis.Number(object.coin) : 0,
      coin_show: isSet(object.coin_show) ? globalThis.Number(object.coin_show) : 0
    };
  },

  create<I extends Exact<DeepPartial<SendResp>, I>>(base?: I): SendResp {
    return SendResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendResp>, I>>(object: I): SendResp {
    const message = createBaseSendResp();
    message.coin = object.coin ?? 0;
    message.coin_show = object.coin_show ?? 0;
    return message;
  }
};

function createBaseInviteSendReq(): InviteSendReq {
  return { room_id: 0, gift_id: 0, nums: 0 };
}

export const InviteSendReq: MessageFns<InviteSendReq> = {
  fromJSON(object: any): InviteSendReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      nums: isSet(object.nums) ? globalThis.Number(object.nums) : 0
    };
  },

  create<I extends Exact<DeepPartial<InviteSendReq>, I>>(base?: I): InviteSendReq {
    return InviteSendReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InviteSendReq>, I>>(object: I): InviteSendReq {
    const message = createBaseInviteSendReq();
    message.room_id = object.room_id ?? 0;
    message.gift_id = object.gift_id ?? 0;
    message.nums = object.nums ?? 0;
    return message;
  }
};

function createBaseInviteSendResp(): InviteSendResp {
  return {};
}

export const InviteSendResp: MessageFns<InviteSendResp> = {
  fromJSON(_: any): InviteSendResp {
    return {};
  },

  create<I extends Exact<DeepPartial<InviteSendResp>, I>>(base?: I): InviteSendResp {
    return InviteSendResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InviteSendResp>, I>>(_: I): InviteSendResp {
    const message = createBaseInviteSendResp();
    return message;
  }
};

function createBaseGiftWishlistRsp(): GiftWishlistRsp {
  return { wish_list: [], wishlist_rank: [] };
}

export const GiftWishlistRsp: MessageFns<GiftWishlistRsp> = {
  fromJSON(object: any): GiftWishlistRsp {
    return {
      wish_list: globalThis.Array.isArray(object?.wish_list)
        ? object.wish_list.map((e: any) => GiftWishlistData.fromJSON(e))
        : [],
      wishlist_rank: globalThis.Array.isArray(object?.wishlist_rank)
        ? object.wishlist_rank.map((e: any) => GiftWishlistRankData.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GiftWishlistRsp>, I>>(base?: I): GiftWishlistRsp {
    return GiftWishlistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftWishlistRsp>, I>>(object: I): GiftWishlistRsp {
    const message = createBaseGiftWishlistRsp();
    message.wish_list = object.wish_list?.map(e => GiftWishlistData.fromPartial(e)) || [];
    message.wishlist_rank = object.wishlist_rank?.map(e => GiftWishlistRankData.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGiftWishlistData(): GiftWishlistData {
  return { gift_id: 0, gift_img_url: '', progress: 0, target: 0, gift_name: '', point: 0 };
}

export const GiftWishlistData: MessageFns<GiftWishlistData> = {
  fromJSON(object: any): GiftWishlistData {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      gift_img_url: isSet(object.gift_img_url) ? globalThis.String(object.gift_img_url) : '',
      progress: isSet(object.progress) ? globalThis.Number(object.progress) : 0,
      target: isSet(object.target) ? globalThis.Number(object.target) : 0,
      gift_name: isSet(object.gift_name) ? globalThis.String(object.gift_name) : '',
      point: isSet(object.point) ? globalThis.Number(object.point) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftWishlistData>, I>>(base?: I): GiftWishlistData {
    return GiftWishlistData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftWishlistData>, I>>(object: I): GiftWishlistData {
    const message = createBaseGiftWishlistData();
    message.gift_id = object.gift_id ?? 0;
    message.gift_img_url = object.gift_img_url ?? '';
    message.progress = object.progress ?? 0;
    message.target = object.target ?? 0;
    message.gift_name = object.gift_name ?? '';
    message.point = object.point ?? 0;
    return message;
  }
};

function createBaseGiftWishlistRankData(): GiftWishlistRankData {
  return { uid: '', avatar_url: '', consume_coins: 0, revenue_points: 0 };
}

export const GiftWishlistRankData: MessageFns<GiftWishlistRankData> = {
  fromJSON(object: any): GiftWishlistRankData {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      avatar_url: isSet(object.avatar_url) ? globalThis.String(object.avatar_url) : '',
      consume_coins: isSet(object.consume_coins) ? globalThis.Number(object.consume_coins) : 0,
      revenue_points: isSet(object.revenue_points) ? globalThis.Number(object.revenue_points) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftWishlistRankData>, I>>(base?: I): GiftWishlistRankData {
    return GiftWishlistRankData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftWishlistRankData>, I>>(object: I): GiftWishlistRankData {
    const message = createBaseGiftWishlistRankData();
    message.uid = object.uid ?? '';
    message.avatar_url = object.avatar_url ?? '';
    message.consume_coins = object.consume_coins ?? 0;
    message.revenue_points = object.revenue_points ?? 0;
    return message;
  }
};

/** 礼物相关 */
export type GiftDefinition = typeof GiftDefinition;
export const GiftDefinition = {
  name: 'Gift',
  fullName: 'gift.Gift',
  methods: {
    /** 发送礼物 */
    send: {
      name: 'Send',
      requestType: SendReq,
      requestStream: false,
      responseType: SendResp,
      responseStream: false,
      options: {}
    },
    /** 房主邀请送礼 */
    inviteSend: {
      name: 'InviteSend',
      requestType: InviteSendReq,
      requestStream: false,
      responseType: InviteSendResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
