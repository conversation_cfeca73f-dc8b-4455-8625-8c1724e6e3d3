// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/room_mode.proto

/* eslint-disable */
import { RoomModePublishDefaultSelected, roomModePublishDefaultSelectedFromJSON } from './comm';

export const protobufPackage = 'roommode';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/roommode/handler */

export interface RoomModeCategoryAndConfig {
  /** 房间模式分类 */
  room_mode_category: RoomModeCategory | undefined;
  /** 房间模式配置 */
  room_mode_configs: RoomModeConfig[];
}

/** 房间模式分类 */
export interface RoomModeCategory {
  /** 模式分类id */
  id: number;
  /** 模式分类名称 */
  name: string;
}

/** 房间模式配置 */
export interface RoomModeConfig {
  /** 房间模式上架id(选择后提交此id) */
  room_mode_publish_id: number;
  /** 房间模式配置id */
  room_mode_config_id: number;
  /** 名称 */
  name: string;
  /** 图标 */
  icon: string;
  /** 默认选中 */
  default_selected: RoomModePublishDefaultSelected;
  /** 房间背景图地址 */
  room_bg_img_url: string;
  /** 房间背景图类型 */
  room_bg_img_type: string;
  /** 玩法地址 */
  play_url: string;
  /** 房间模式code;eg:Chat、Friend、Music */
  room_mode_code: string;
  /** 麦位布局code;eg:chat_10,partner_9,partner_10,cp */
  room_layout_code: string;
  /** 玩法code;eg:ludo、桌球、狼人杀 */
  play_code: string;
}

export interface ListRoomModesByCategoryReq {
  /** 房间id */
  room_id: number;
}

export interface ListRoomModesByCategoryRsp {
  items: RoomModeCategoryAndConfig[];
}

function createBaseRoomModeCategoryAndConfig(): RoomModeCategoryAndConfig {
  return { room_mode_category: undefined, room_mode_configs: [] };
}

export const RoomModeCategoryAndConfig: MessageFns<RoomModeCategoryAndConfig> = {
  fromJSON(object: any): RoomModeCategoryAndConfig {
    return {
      room_mode_category: isSet(object.room_mode_category)
        ? RoomModeCategory.fromJSON(object.room_mode_category)
        : undefined,
      room_mode_configs: globalThis.Array.isArray(object?.room_mode_configs)
        ? object.room_mode_configs.map((e: any) => RoomModeConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RoomModeCategoryAndConfig>, I>>(base?: I): RoomModeCategoryAndConfig {
    return RoomModeCategoryAndConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeCategoryAndConfig>, I>>(object: I): RoomModeCategoryAndConfig {
    const message = createBaseRoomModeCategoryAndConfig();
    message.room_mode_category =
      object.room_mode_category !== undefined && object.room_mode_category !== null
        ? RoomModeCategory.fromPartial(object.room_mode_category)
        : undefined;
    message.room_mode_configs = object.room_mode_configs?.map(e => RoomModeConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRoomModeCategory(): RoomModeCategory {
  return { id: 0, name: '' };
}

export const RoomModeCategory: MessageFns<RoomModeCategory> = {
  fromJSON(object: any): RoomModeCategory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeCategory>, I>>(base?: I): RoomModeCategory {
    return RoomModeCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeCategory>, I>>(object: I): RoomModeCategory {
    const message = createBaseRoomModeCategory();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseRoomModeConfig(): RoomModeConfig {
  return {
    room_mode_publish_id: 0,
    room_mode_config_id: 0,
    name: '',
    icon: '',
    default_selected: 0,
    room_bg_img_url: '',
    room_bg_img_type: '',
    play_url: '',
    room_mode_code: '',
    room_layout_code: '',
    play_code: ''
  };
}

export const RoomModeConfig: MessageFns<RoomModeConfig> = {
  fromJSON(object: any): RoomModeConfig {
    return {
      room_mode_publish_id: isSet(object.room_mode_publish_id) ? globalThis.Number(object.room_mode_publish_id) : 0,
      room_mode_config_id: isSet(object.room_mode_config_id) ? globalThis.Number(object.room_mode_config_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      default_selected: isSet(object.default_selected)
        ? roomModePublishDefaultSelectedFromJSON(object.default_selected)
        : 0,
      room_bg_img_url: isSet(object.room_bg_img_url) ? globalThis.String(object.room_bg_img_url) : '',
      room_bg_img_type: isSet(object.room_bg_img_type) ? globalThis.String(object.room_bg_img_type) : '',
      play_url: isSet(object.play_url) ? globalThis.String(object.play_url) : '',
      room_mode_code: isSet(object.room_mode_code) ? globalThis.String(object.room_mode_code) : '',
      room_layout_code: isSet(object.room_layout_code) ? globalThis.String(object.room_layout_code) : '',
      play_code: isSet(object.play_code) ? globalThis.String(object.play_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomModeConfig>, I>>(base?: I): RoomModeConfig {
    return RoomModeConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomModeConfig>, I>>(object: I): RoomModeConfig {
    const message = createBaseRoomModeConfig();
    message.room_mode_publish_id = object.room_mode_publish_id ?? 0;
    message.room_mode_config_id = object.room_mode_config_id ?? 0;
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    message.default_selected = object.default_selected ?? 0;
    message.room_bg_img_url = object.room_bg_img_url ?? '';
    message.room_bg_img_type = object.room_bg_img_type ?? '';
    message.play_url = object.play_url ?? '';
    message.room_mode_code = object.room_mode_code ?? '';
    message.room_layout_code = object.room_layout_code ?? '';
    message.play_code = object.play_code ?? '';
    return message;
  }
};

function createBaseListRoomModesByCategoryReq(): ListRoomModesByCategoryReq {
  return { room_id: 0 };
}

export const ListRoomModesByCategoryReq: MessageFns<ListRoomModesByCategoryReq> = {
  fromJSON(object: any): ListRoomModesByCategoryReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListRoomModesByCategoryReq>, I>>(base?: I): ListRoomModesByCategoryReq {
    return ListRoomModesByCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModesByCategoryReq>, I>>(object: I): ListRoomModesByCategoryReq {
    const message = createBaseListRoomModesByCategoryReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseListRoomModesByCategoryRsp(): ListRoomModesByCategoryRsp {
  return { items: [] };
}

export const ListRoomModesByCategoryRsp: MessageFns<ListRoomModesByCategoryRsp> = {
  fromJSON(object: any): ListRoomModesByCategoryRsp {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => RoomModeCategoryAndConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomModesByCategoryRsp>, I>>(base?: I): ListRoomModesByCategoryRsp {
    return ListRoomModesByCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomModesByCategoryRsp>, I>>(object: I): ListRoomModesByCategoryRsp {
    const message = createBaseListRoomModesByCategoryRsp();
    message.items = object.items?.map(e => RoomModeCategoryAndConfig.fromPartial(e)) || [];
    return message;
  }
};

/** 房间模式 */
export type RoomModeDefinition = typeof RoomModeDefinition;
export const RoomModeDefinition = {
  name: 'RoomMode',
  fullName: 'roommode.RoomMode',
  methods: {
    /** 模式分类&&模式配置-列表 */
    listRoomModesByCategory: {
      name: 'ListRoomModesByCategory',
      requestType: ListRoomModesByCategoryReq,
      requestStream: false,
      responseType: ListRoomModesByCategoryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
