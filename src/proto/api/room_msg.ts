// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/room_msg.proto

/* eslint-disable */
import { UserInfo } from './user';

export const protobufPackage = 'roommsg';

/** smicro:spath=gitit.cc/social/lucky/lucky-api/biz/room/handler */

/** 公屏消息类型 */
export enum MsgType {
  MSG_TYPE_NONE = 0,
  /** MSG_TYPE_USER_BEGIN - 用户消息 */
  MSG_TYPE_USER_BEGIN = 1,
  /** MSG_TYPE_USER_TEXT - 用户发送公屏 */
  MSG_TYPE_USER_TEXT = 2,
  /** MSG_TYPE_USER_EMOJI - emoji 表情 */
  MSG_TYPE_USER_EMOJI = 3,
  MSG_TYPE_USER_END = 10000,
  /** MSG_TYPE_SYS_BEGIN - 系统公屏 */
  MSG_TYPE_SYS_BEGIN = 10001,
  /** MSG_TYPE_ENTER - 进房消息 */
  MSG_TYPE_ENTER = 10002,
  /** MSG_TYPE_GIFT - 送礼公屏 */
  MSG_TYPE_GIFT = 10003,
  /** MSG_TYPE_ROOM_ANNOUNCEMENT - 房间公告 */
  MSG_TYPE_ROOM_ANNOUNCEMENT = 10004,
  /** MSG_TYPE_WELCOME - 进房欢迎消息 */
  MSG_TYPE_WELCOME = 10005,
  /** MSG_TYPE_MUTE_MSG_TIPS - 禁言提示公屏 */
  MSG_TYPE_MUTE_MSG_TIPS = 10006,
  /** MSG_TYPE_SET_ROOM_ADMIN - 设置管理员公屏 */
  MSG_TYPE_SET_ROOM_ADMIN = 10007,
  /** MSG_TYPE_AT_MODEL_MSG - 单条艾特消息(@xxx content content)，之后类似都用这个消息类型 */
  MSG_TYPE_AT_MODEL_MSG = 10008,
  /** MSG_TYPE_SYS_TEXT - 系统文本公屏 */
  MSG_TYPE_SYS_TEXT = 10009,
  /** MSG_TYPE_ROOM_PK_START - 开始房内PK */
  MSG_TYPE_ROOM_PK_START = 10010,
  /** MSG_TYPE_ROOM_PK_END - 结束房内PK */
  MSG_TYPE_ROOM_PK_END = 10011,
  MSG_TYPE_SYS_END = 20000,
  UNRECOGNIZED = -1
}

export function msgTypeFromJSON(object: any): MsgType {
  switch (object) {
    case 0:
    case 'MSG_TYPE_NONE':
      return MsgType.MSG_TYPE_NONE;
    case 1:
    case 'MSG_TYPE_USER_BEGIN':
      return MsgType.MSG_TYPE_USER_BEGIN;
    case 2:
    case 'MSG_TYPE_USER_TEXT':
      return MsgType.MSG_TYPE_USER_TEXT;
    case 3:
    case 'MSG_TYPE_USER_EMOJI':
      return MsgType.MSG_TYPE_USER_EMOJI;
    case 10000:
    case 'MSG_TYPE_USER_END':
      return MsgType.MSG_TYPE_USER_END;
    case 10001:
    case 'MSG_TYPE_SYS_BEGIN':
      return MsgType.MSG_TYPE_SYS_BEGIN;
    case 10002:
    case 'MSG_TYPE_ENTER':
      return MsgType.MSG_TYPE_ENTER;
    case 10003:
    case 'MSG_TYPE_GIFT':
      return MsgType.MSG_TYPE_GIFT;
    case 10004:
    case 'MSG_TYPE_ROOM_ANNOUNCEMENT':
      return MsgType.MSG_TYPE_ROOM_ANNOUNCEMENT;
    case 10005:
    case 'MSG_TYPE_WELCOME':
      return MsgType.MSG_TYPE_WELCOME;
    case 10006:
    case 'MSG_TYPE_MUTE_MSG_TIPS':
      return MsgType.MSG_TYPE_MUTE_MSG_TIPS;
    case 10007:
    case 'MSG_TYPE_SET_ROOM_ADMIN':
      return MsgType.MSG_TYPE_SET_ROOM_ADMIN;
    case 10008:
    case 'MSG_TYPE_AT_MODEL_MSG':
      return MsgType.MSG_TYPE_AT_MODEL_MSG;
    case 10009:
    case 'MSG_TYPE_SYS_TEXT':
      return MsgType.MSG_TYPE_SYS_TEXT;
    case 10010:
    case 'MSG_TYPE_ROOM_PK_START':
      return MsgType.MSG_TYPE_ROOM_PK_START;
    case 10011:
    case 'MSG_TYPE_ROOM_PK_END':
      return MsgType.MSG_TYPE_ROOM_PK_END;
    case 20000:
    case 'MSG_TYPE_SYS_END':
      return MsgType.MSG_TYPE_SYS_END;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MsgType.UNRECOGNIZED;
  }
}

export enum SectionType {
  SECTION_TYPE_NONE = 0,
  SECTION_TYPE_TEXT = 1,
  SECTION_TYPE_IMAGE = 2,
  SECTION_TYPE_PLAY = 3,
  SECTION_TYPE_DYNAMIC = 4,
  UNRECOGNIZED = -1
}

export function sectionTypeFromJSON(object: any): SectionType {
  switch (object) {
    case 0:
    case 'SECTION_TYPE_NONE':
      return SectionType.SECTION_TYPE_NONE;
    case 1:
    case 'SECTION_TYPE_TEXT':
      return SectionType.SECTION_TYPE_TEXT;
    case 2:
    case 'SECTION_TYPE_IMAGE':
      return SectionType.SECTION_TYPE_IMAGE;
    case 3:
    case 'SECTION_TYPE_PLAY':
      return SectionType.SECTION_TYPE_PLAY;
    case 4:
    case 'SECTION_TYPE_DYNAMIC':
      return SectionType.SECTION_TYPE_DYNAMIC;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SectionType.UNRECOGNIZED;
  }
}

export enum MessageStrategyType {
  MESSAGE_STRATEGY_TYPE_IGNORE_NONE = 0,
  /** MESSAGE_STRATEGY_TYPE_IGNORE_ALL - 不认识的，整个消息不展示 */
  MESSAGE_STRATEGY_TYPE_IGNORE_ALL = 1,
  /** MESSAGE_STRATEGY_TYPE_IGNORE_ONLY - 只是不展示不认识的 */
  MESSAGE_STRATEGY_TYPE_IGNORE_ONLY = 2,
  /** MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ALL - 整个消息用通用文案代替 */
  MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ALL = 3,
  /** MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ONLY - 不认识的用通用文案代替 */
  MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ONLY = 4,
  UNRECOGNIZED = -1
}

export function messageStrategyTypeFromJSON(object: any): MessageStrategyType {
  switch (object) {
    case 0:
    case 'MESSAGE_STRATEGY_TYPE_IGNORE_NONE':
      return MessageStrategyType.MESSAGE_STRATEGY_TYPE_IGNORE_NONE;
    case 1:
    case 'MESSAGE_STRATEGY_TYPE_IGNORE_ALL':
      return MessageStrategyType.MESSAGE_STRATEGY_TYPE_IGNORE_ALL;
    case 2:
    case 'MESSAGE_STRATEGY_TYPE_IGNORE_ONLY':
      return MessageStrategyType.MESSAGE_STRATEGY_TYPE_IGNORE_ONLY;
    case 3:
    case 'MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ALL':
      return MessageStrategyType.MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ALL;
    case 4:
    case 'MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ONLY':
      return MessageStrategyType.MESSAGE_Strategy_TYPE_NOTICE_REPLACE_ONLY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MessageStrategyType.UNRECOGNIZED;
  }
}

/** 文本类型 */
export interface TextSection {
  /** 内容 */
  content: string;
  /** 颜色 */
  color: string;
  /** 背景色 */
  bg_color: string;
  /** 点击的行为（包含跳转等） */
  action: string[];
  /** 字体，没填就用默认字体 */
  font: string;
  /** 字体大小，没填就用默认大小 */
  font_size: number;
  /** 拓展json */
  ext_json: string;
  /** 多语言提示 */
  multi_lan_content: { [key: string]: string };
}

export interface TextSection_MultiLanContentEntry {
  key: string;
  value: string;
}

export interface ImageSection {
  /** 图片的链接 */
  url: string;
  /** 宽 */
  width: number;
  /** 高 */
  height: number;
  /** 点击的行为 */
  action: string[];
  /** 拓展json */
  ext_json: string;
}

export interface PlaySection {
  /** 图片的链接 */
  url: string;
  /** 宽 */
  width: number;
  /** 高 */
  height: number;
  /** 点击的行为 */
  action: string[];
  /** 玩法表情ID */
  id: number;
  /** 拓展json */
  ext_json: string;
  /** 结果 */
  result: string;
}

export interface DynamicSection {
  /** 图片的链接 */
  url: string;
  /** 宽 */
  width: number;
  /** 高 */
  height: number;
  /** 点击的行为 */
  action: string[];
  /** 动态表情的静态图片 */
  icon: string;
  /** 拓展json */
  ext_json: string;
}

/** 公屏消息 */
export interface Message {
  /** 用户信息 */
  from_user: UserInfo | undefined;
  /** 整个消息点击的行为 */
  action: string[];
  /** 公屏消息类型 */
  msg_type: MsgType;
  /** 不认识的type处理策略 */
  strategy_type: MessageStrategyType;
  types: SectionType[];
  texts: TextSection[];
  images: ImageSection[];
  dynamics: DynamicSection[];
  plays: PlaySection[];
}

export interface SendRoomMessageReq {
  /** 房间ID */
  room_id: number;
  /** 消息 */
  message: Message | undefined;
}

export interface SendRoomMessageRsp {
  /** 消息ID, 后续用于撤回. */
  message_id: string;
}

/** 禁言(公屏)，包含对某个用户和全房间禁言 */
export interface MuteMsgReq {
  /** 房间号 */
  room_id: number;
  /** 禁言的用户，0表示全房间禁言 */
  target_uid: string;
  /** 禁言时间，单位秒，-1 表示永久，0: 默认时长（服务端控制） */
  second: number;
}

export interface MuteMsgRsp {}

/** 解除禁言(公屏)，对某个用户 */
export interface UnMuteMsgReq {
  /** 房间号 */
  room_id: number;
  /** 禁言的用户 */
  target_uid: string;
}

export interface UnMuteMsgRsp {}

function createBaseTextSection(): TextSection {
  return {
    content: '',
    color: '',
    bg_color: '',
    action: [],
    font: '',
    font_size: 0,
    ext_json: '',
    multi_lan_content: {}
  };
}

export const TextSection: MessageFns<TextSection> = {
  fromJSON(object: any): TextSection {
    return {
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      color: isSet(object.color) ? globalThis.String(object.color) : '',
      bg_color: isSet(object.bg_color) ? globalThis.String(object.bg_color) : '',
      action: globalThis.Array.isArray(object?.action) ? object.action.map((e: any) => globalThis.String(e)) : [],
      font: isSet(object.font) ? globalThis.String(object.font) : '',
      font_size: isSet(object.font_size) ? globalThis.Number(object.font_size) : 0,
      ext_json: isSet(object.ext_json) ? globalThis.String(object.ext_json) : '',
      multi_lan_content: isObject(object.multi_lan_content)
        ? Object.entries(object.multi_lan_content).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<TextSection>, I>>(base?: I): TextSection {
    return TextSection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextSection>, I>>(object: I): TextSection {
    const message = createBaseTextSection();
    message.content = object.content ?? '';
    message.color = object.color ?? '';
    message.bg_color = object.bg_color ?? '';
    message.action = object.action?.map(e => e) || [];
    message.font = object.font ?? '';
    message.font_size = object.font_size ?? 0;
    message.ext_json = object.ext_json ?? '';
    message.multi_lan_content = Object.entries(object.multi_lan_content ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseTextSection_MultiLanContentEntry(): TextSection_MultiLanContentEntry {
  return { key: '', value: '' };
}

export const TextSection_MultiLanContentEntry: MessageFns<TextSection_MultiLanContentEntry> = {
  fromJSON(object: any): TextSection_MultiLanContentEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<TextSection_MultiLanContentEntry>, I>>(
    base?: I
  ): TextSection_MultiLanContentEntry {
    return TextSection_MultiLanContentEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TextSection_MultiLanContentEntry>, I>>(
    object: I
  ): TextSection_MultiLanContentEntry {
    const message = createBaseTextSection_MultiLanContentEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseImageSection(): ImageSection {
  return { url: '', width: 0, height: 0, action: [], ext_json: '' };
}

export const ImageSection: MessageFns<ImageSection> = {
  fromJSON(object: any): ImageSection {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      action: globalThis.Array.isArray(object?.action) ? object.action.map((e: any) => globalThis.String(e)) : [],
      ext_json: isSet(object.ext_json) ? globalThis.String(object.ext_json) : ''
    };
  },

  create<I extends Exact<DeepPartial<ImageSection>, I>>(base?: I): ImageSection {
    return ImageSection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImageSection>, I>>(object: I): ImageSection {
    const message = createBaseImageSection();
    message.url = object.url ?? '';
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.action = object.action?.map(e => e) || [];
    message.ext_json = object.ext_json ?? '';
    return message;
  }
};

function createBasePlaySection(): PlaySection {
  return { url: '', width: 0, height: 0, action: [], id: 0, ext_json: '', result: '' };
}

export const PlaySection: MessageFns<PlaySection> = {
  fromJSON(object: any): PlaySection {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      action: globalThis.Array.isArray(object?.action) ? object.action.map((e: any) => globalThis.String(e)) : [],
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      ext_json: isSet(object.ext_json) ? globalThis.String(object.ext_json) : '',
      result: isSet(object.result) ? globalThis.String(object.result) : ''
    };
  },

  create<I extends Exact<DeepPartial<PlaySection>, I>>(base?: I): PlaySection {
    return PlaySection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PlaySection>, I>>(object: I): PlaySection {
    const message = createBasePlaySection();
    message.url = object.url ?? '';
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.action = object.action?.map(e => e) || [];
    message.id = object.id ?? 0;
    message.ext_json = object.ext_json ?? '';
    message.result = object.result ?? '';
    return message;
  }
};

function createBaseDynamicSection(): DynamicSection {
  return { url: '', width: 0, height: 0, action: [], icon: '', ext_json: '' };
}

export const DynamicSection: MessageFns<DynamicSection> = {
  fromJSON(object: any): DynamicSection {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      action: globalThis.Array.isArray(object?.action) ? object.action.map((e: any) => globalThis.String(e)) : [],
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      ext_json: isSet(object.ext_json) ? globalThis.String(object.ext_json) : ''
    };
  },

  create<I extends Exact<DeepPartial<DynamicSection>, I>>(base?: I): DynamicSection {
    return DynamicSection.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DynamicSection>, I>>(object: I): DynamicSection {
    const message = createBaseDynamicSection();
    message.url = object.url ?? '';
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.action = object.action?.map(e => e) || [];
    message.icon = object.icon ?? '';
    message.ext_json = object.ext_json ?? '';
    return message;
  }
};

function createBaseMessage(): Message {
  return {
    from_user: undefined,
    action: [],
    msg_type: 0,
    strategy_type: 0,
    types: [],
    texts: [],
    images: [],
    dynamics: [],
    plays: []
  };
}

export const Message: MessageFns<Message> = {
  fromJSON(object: any): Message {
    return {
      from_user: isSet(object.from_user) ? UserInfo.fromJSON(object.from_user) : undefined,
      action: globalThis.Array.isArray(object?.action) ? object.action.map((e: any) => globalThis.String(e)) : [],
      msg_type: isSet(object.msg_type) ? msgTypeFromJSON(object.msg_type) : 0,
      strategy_type: isSet(object.strategy_type) ? messageStrategyTypeFromJSON(object.strategy_type) : 0,
      types: globalThis.Array.isArray(object?.types) ? object.types.map((e: any) => sectionTypeFromJSON(e)) : [],
      texts: globalThis.Array.isArray(object?.texts) ? object.texts.map((e: any) => TextSection.fromJSON(e)) : [],
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => ImageSection.fromJSON(e)) : [],
      dynamics: globalThis.Array.isArray(object?.dynamics)
        ? object.dynamics.map((e: any) => DynamicSection.fromJSON(e))
        : [],
      plays: globalThis.Array.isArray(object?.plays) ? object.plays.map((e: any) => PlaySection.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Message>, I>>(base?: I): Message {
    return Message.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Message>, I>>(object: I): Message {
    const message = createBaseMessage();
    message.from_user =
      object.from_user !== undefined && object.from_user !== null ? UserInfo.fromPartial(object.from_user) : undefined;
    message.action = object.action?.map(e => e) || [];
    message.msg_type = object.msg_type ?? 0;
    message.strategy_type = object.strategy_type ?? 0;
    message.types = object.types?.map(e => e) || [];
    message.texts = object.texts?.map(e => TextSection.fromPartial(e)) || [];
    message.images = object.images?.map(e => ImageSection.fromPartial(e)) || [];
    message.dynamics = object.dynamics?.map(e => DynamicSection.fromPartial(e)) || [];
    message.plays = object.plays?.map(e => PlaySection.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSendRoomMessageReq(): SendRoomMessageReq {
  return { room_id: 0, message: undefined };
}

export const SendRoomMessageReq: MessageFns<SendRoomMessageReq> = {
  fromJSON(object: any): SendRoomMessageReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      message: isSet(object.message) ? Message.fromJSON(object.message) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SendRoomMessageReq>, I>>(base?: I): SendRoomMessageReq {
    return SendRoomMessageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendRoomMessageReq>, I>>(object: I): SendRoomMessageReq {
    const message = createBaseSendRoomMessageReq();
    message.room_id = object.room_id ?? 0;
    message.message =
      object.message !== undefined && object.message !== null ? Message.fromPartial(object.message) : undefined;
    return message;
  }
};

function createBaseSendRoomMessageRsp(): SendRoomMessageRsp {
  return { message_id: '' };
}

export const SendRoomMessageRsp: MessageFns<SendRoomMessageRsp> = {
  fromJSON(object: any): SendRoomMessageRsp {
    return { message_id: isSet(object.message_id) ? globalThis.String(object.message_id) : '' };
  },

  create<I extends Exact<DeepPartial<SendRoomMessageRsp>, I>>(base?: I): SendRoomMessageRsp {
    return SendRoomMessageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendRoomMessageRsp>, I>>(object: I): SendRoomMessageRsp {
    const message = createBaseSendRoomMessageRsp();
    message.message_id = object.message_id ?? '';
    return message;
  }
};

function createBaseMuteMsgReq(): MuteMsgReq {
  return { room_id: 0, target_uid: '', second: 0 };
}

export const MuteMsgReq: MessageFns<MuteMsgReq> = {
  fromJSON(object: any): MuteMsgReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.String(object.target_uid) : '',
      second: isSet(object.second) ? globalThis.Number(object.second) : 0
    };
  },

  create<I extends Exact<DeepPartial<MuteMsgReq>, I>>(base?: I): MuteMsgReq {
    return MuteMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteMsgReq>, I>>(object: I): MuteMsgReq {
    const message = createBaseMuteMsgReq();
    message.room_id = object.room_id ?? 0;
    message.target_uid = object.target_uid ?? '';
    message.second = object.second ?? 0;
    return message;
  }
};

function createBaseMuteMsgRsp(): MuteMsgRsp {
  return {};
}

export const MuteMsgRsp: MessageFns<MuteMsgRsp> = {
  fromJSON(_: any): MuteMsgRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<MuteMsgRsp>, I>>(base?: I): MuteMsgRsp {
    return MuteMsgRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MuteMsgRsp>, I>>(_: I): MuteMsgRsp {
    const message = createBaseMuteMsgRsp();
    return message;
  }
};

function createBaseUnMuteMsgReq(): UnMuteMsgReq {
  return { room_id: 0, target_uid: '' };
}

export const UnMuteMsgReq: MessageFns<UnMuteMsgReq> = {
  fromJSON(object: any): UnMuteMsgReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.String(object.target_uid) : ''
    };
  },

  create<I extends Exact<DeepPartial<UnMuteMsgReq>, I>>(base?: I): UnMuteMsgReq {
    return UnMuteMsgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnMuteMsgReq>, I>>(object: I): UnMuteMsgReq {
    const message = createBaseUnMuteMsgReq();
    message.room_id = object.room_id ?? 0;
    message.target_uid = object.target_uid ?? '';
    return message;
  }
};

function createBaseUnMuteMsgRsp(): UnMuteMsgRsp {
  return {};
}

export const UnMuteMsgRsp: MessageFns<UnMuteMsgRsp> = {
  fromJSON(_: any): UnMuteMsgRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnMuteMsgRsp>, I>>(base?: I): UnMuteMsgRsp {
    return UnMuteMsgRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnMuteMsgRsp>, I>>(_: I): UnMuteMsgRsp {
    const message = createBaseUnMuteMsgRsp();
    return message;
  }
};

/** 房间公屏消息 */
export type RoomMessageDefinition = typeof RoomMessageDefinition;
export const RoomMessageDefinition = {
  name: 'RoomMessage',
  fullName: 'roommsg.RoomMessage',
  methods: {
    /** 公屏消息 */
    sendRoomMessage: {
      name: 'SendRoomMessage',
      requestType: SendRoomMessageReq,
      requestStream: false,
      responseType: SendRoomMessageRsp,
      responseStream: false,
      options: {}
    },
    /** 禁言(公屏) */
    muteMsg: {
      name: 'MuteMsg',
      requestType: MuteMsgReq,
      requestStream: false,
      responseType: MuteMsgRsp,
      responseStream: false,
      options: {}
    },
    /** 解除禁言(公屏)，对某个用户 */
    unMuteMsg: {
      name: 'UnMuteMsg',
      requestType: UnMuteMsgReq,
      requestStream: false,
      responseType: UnMuteMsgRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
