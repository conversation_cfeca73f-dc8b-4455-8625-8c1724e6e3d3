// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/transfer.proto

/* eslint-disable */

export const protobufPackage = 'lucky';

export interface AnchorOfflineMessage {
  uid: string[];
}

export interface Notify {
  msg: AnchorOfflineMessage | undefined;
}

function createBaseAnchorOfflineMessage(): AnchorOfflineMessage {
  return { uid: [] };
}

export const AnchorOfflineMessage: MessageFns<AnchorOfflineMessage> = {
  fromJSON(object: any): AnchorOfflineMessage {
    return { uid: globalThis.Array.isArray(object?.uid) ? object.uid.map((e: any) => globalThis.String(e)) : [] };
  },

  create<I extends Exact<DeepPartial<AnchorOfflineMessage>, I>>(base?: I): AnchorOfflineMessage {
    return AnchorOfflineMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AnchorOfflineMessage>, I>>(object: I): AnchorOfflineMessage {
    const message = createBaseAnchorOfflineMessage();
    message.uid = object.uid?.map(e => e) || [];
    return message;
  }
};

function createBaseNotify(): Notify {
  return { msg: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return { msg: isSet(object.msg) ? AnchorOfflineMessage.fromJSON(object.msg) : undefined };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.msg =
      object.msg !== undefined && object.msg !== null ? AnchorOfflineMessage.fromPartial(object.msg) : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
