// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/privilege.proto

/* eslint-disable */

export const protobufPackage = 'privilege';

/** 素材类型 */
export enum AssetType {
  /** ASSET_TYPE_NONE - 无意义 */
  ASSET_TYPE_NONE = 0,
  /** ASSET_TYPE_IMG - 图片 png/jpg/webp... */
  ASSET_TYPE_IMG = 2,
  /** ASSET_TYPE_MP4 - mp4 */
  ASSET_TYPE_MP4 = 4,
  /** ASSET_TYPE_SVGA - svga */
  ASSET_TYPE_SVGA = 5,
  /** ASSET_TYPE_GIF - gif */
  ASSET_TYPE_GIF = 6,
  UNRECOGNIZED = -1
}

export function assetTypeFromJSON(object: any): AssetType {
  switch (object) {
    case 0:
    case 'ASSET_TYPE_NONE':
      return AssetType.ASSET_TYPE_NONE;
    case 2:
    case 'ASSET_TYPE_IMG':
      return AssetType.ASSET_TYPE_IMG;
    case 4:
    case 'ASSET_TYPE_MP4':
      return AssetType.ASSET_TYPE_MP4;
    case 5:
    case 'ASSET_TYPE_SVGA':
      return AssetType.ASSET_TYPE_SVGA;
    case 6:
    case 'ASSET_TYPE_GIF':
      return AssetType.ASSET_TYPE_GIF;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AssetType.UNRECOGNIZED;
  }
}

/** 素材显示区域 */
export enum AssetArea {
  /** ASSET_AREA_NONE - 无意义 */
  ASSET_AREA_NONE = 0,
  /** ASSET_AREA_HALF - 半屏 */
  ASSET_AREA_HALF = 1,
  /** ASSET_AREA_FULL - 全屏 */
  ASSET_AREA_FULL = 2,
  UNRECOGNIZED = -1
}

export function assetAreaFromJSON(object: any): AssetArea {
  switch (object) {
    case 0:
    case 'ASSET_AREA_NONE':
      return AssetArea.ASSET_AREA_NONE;
    case 1:
    case 'ASSET_AREA_HALF':
      return AssetArea.ASSET_AREA_HALF;
    case 2:
    case 'ASSET_AREA_FULL':
      return AssetArea.ASSET_AREA_FULL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AssetArea.UNRECOGNIZED;
  }
}

/** 生效类型 */
export enum EffectType {
  /** EFFECT_TYPE_NONE - 无意义 */
  EFFECT_TYPE_NONE = 0,
  /** EFFECT_TYPE_BY_DURATION - 按时长 */
  EFFECT_TYPE_BY_DURATION = 1,
  /** EFFECT_TYPE_BY_NUM - 按数量 */
  EFFECT_TYPE_BY_NUM = 2,
  UNRECOGNIZED = -1
}

export function effectTypeFromJSON(object: any): EffectType {
  switch (object) {
    case 0:
    case 'EFFECT_TYPE_NONE':
      return EffectType.EFFECT_TYPE_NONE;
    case 1:
    case 'EFFECT_TYPE_BY_DURATION':
      return EffectType.EFFECT_TYPE_BY_DURATION;
    case 2:
    case 'EFFECT_TYPE_BY_NUM':
      return EffectType.EFFECT_TYPE_BY_NUM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return EffectType.UNRECOGNIZED;
  }
}

/** 归属类型, 例如: 用户 / 房间 / ... */
export enum TargetType {
  /** TARGET_TYPE_NONE - 无意义 */
  TARGET_TYPE_NONE = 0,
  /** TARGET_TYPE_USER - 用户 */
  TARGET_TYPE_USER = 1,
  /** TARGET_TYPE_ROOM - 房间 */
  TARGET_TYPE_ROOM = 2,
  UNRECOGNIZED = -1
}

export function targetTypeFromJSON(object: any): TargetType {
  switch (object) {
    case 0:
    case 'TARGET_TYPE_NONE':
      return TargetType.TARGET_TYPE_NONE;
    case 1:
    case 'TARGET_TYPE_USER':
      return TargetType.TARGET_TYPE_USER;
    case 2:
    case 'TARGET_TYPE_ROOM':
      return TargetType.TARGET_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TargetType.UNRECOGNIZED;
  }
}

/** 特权分类标识 */
export enum CategoryID {
  /** CATEGORY_ID_NONE - 无意义 */
  CATEGORY_ID_NONE = 0,
  /** CATEGORY_ID_HEADWEAR - 一级分类 */
  CATEGORY_ID_HEADWEAR = 10,
  UNRECOGNIZED = -1
}

export function categoryIDFromJSON(object: any): CategoryID {
  switch (object) {
    case 0:
    case 'CATEGORY_ID_NONE':
      return CategoryID.CATEGORY_ID_NONE;
    case 10:
    case 'CATEGORY_ID_HEADWEAR':
      return CategoryID.CATEGORY_ID_HEADWEAR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryID.UNRECOGNIZED;
  }
}

/** 特权分类状态 */
export enum CategoryStatus {
  /** CATEGORY_STATUS_NONE - 无意义 */
  CATEGORY_STATUS_NONE = 0,
  /** CATEGORY_STATUS_NORMAL - 正常 */
  CATEGORY_STATUS_NORMAL = 1,
  /** CATEGORY_STATUS_DISABLED - 禁用 */
  CATEGORY_STATUS_DISABLED = 2,
  UNRECOGNIZED = -1
}

export function categoryStatusFromJSON(object: any): CategoryStatus {
  switch (object) {
    case 0:
    case 'CATEGORY_STATUS_NONE':
      return CategoryStatus.CATEGORY_STATUS_NONE;
    case 1:
    case 'CATEGORY_STATUS_NORMAL':
      return CategoryStatus.CATEGORY_STATUS_NORMAL;
    case 2:
    case 'CATEGORY_STATUS_DISABLED':
      return CategoryStatus.CATEGORY_STATUS_DISABLED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryStatus.UNRECOGNIZED;
  }
}

/** 特权资源状态 */
export enum ResourceStatus {
  /** RESOURCE_STATUS_NONE - 无意义 */
  RESOURCE_STATUS_NONE = 0,
  /** RESOURCE_STATUS_ON_SALE - 上架 */
  RESOURCE_STATUS_ON_SALE = 1,
  /** RESOURCE_STATUS_OFF_SALE - 下架 */
  RESOURCE_STATUS_OFF_SALE = 2,
  /** RESOURCE_STATUS_DELETED - 已删除 */
  RESOURCE_STATUS_DELETED = 3,
  UNRECOGNIZED = -1
}

export function resourceStatusFromJSON(object: any): ResourceStatus {
  switch (object) {
    case 0:
    case 'RESOURCE_STATUS_NONE':
      return ResourceStatus.RESOURCE_STATUS_NONE;
    case 1:
    case 'RESOURCE_STATUS_ON_SALE':
      return ResourceStatus.RESOURCE_STATUS_ON_SALE;
    case 2:
    case 'RESOURCE_STATUS_OFF_SALE':
      return ResourceStatus.RESOURCE_STATUS_OFF_SALE;
    case 3:
    case 'RESOURCE_STATUS_DELETED':
      return ResourceStatus.RESOURCE_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourceStatus.UNRECOGNIZED;
  }
}

/** 素材信息 */
export interface AssetInfo {
  /** 资源地址 */
  url: string;
  /** 资源大小(单位: bytes) */
  size: number;
  /** 资源类型 */
  type: AssetType;
  /** 显示区域 */
  area: AssetArea;
}

/** 特权分类 */
export interface Category {
  /** 用户语言对应的资源 */
  asset: Category_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Category_Asset };
  /** 归属类型 */
  target_type: TargetType;
  /** 分类标识 */
  category_id: CategoryID;
}

export interface Category_Asset {
  /** 名称 */
  name: string;
  /** 图标 */
  icon: AssetInfo | undefined;
}

export interface Category_AssetsEntry {
  key: string;
  value: Category_Asset | undefined;
}

/** 特权资源 */
export interface Resource {
  /** 头像框 */
  headwear?: Headwear | undefined;
}

/** 头像框 */
export interface Headwear {
  /** 用户语言对应的资源 */
  asset: Headwear_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Headwear_Asset };
  /** 资源ID */
  id: number;
}

export interface Headwear_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 缩略图 */
  thumb: AssetInfo | undefined;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface Headwear_AssetsEntry {
  key: string;
  value: Headwear_Asset | undefined;
}

/** 使用中的特权 */
export interface UsingPrivilege {
  /** 头像框 */
  headwear: Headwear | undefined;
}

function createBaseAssetInfo(): AssetInfo {
  return { url: '', size: 0, type: 0, area: 0 };
}

export const AssetInfo: MessageFns<AssetInfo> = {
  fromJSON(object: any): AssetInfo {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      type: isSet(object.type) ? assetTypeFromJSON(object.type) : 0,
      area: isSet(object.area) ? assetAreaFromJSON(object.area) : 0
    };
  },

  create<I extends Exact<DeepPartial<AssetInfo>, I>>(base?: I): AssetInfo {
    return AssetInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssetInfo>, I>>(object: I): AssetInfo {
    const message = createBaseAssetInfo();
    message.url = object.url ?? '';
    message.size = object.size ?? 0;
    message.type = object.type ?? 0;
    message.area = object.area ?? 0;
    return message;
  }
};

function createBaseCategory(): Category {
  return { asset: undefined, assets: {}, target_type: 0, category_id: 0 };
}

export const Category: MessageFns<Category> = {
  fromJSON(object: any): Category {
    return {
      asset: isSet(object.asset) ? Category_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Category_Asset }>((acc, [key, value]) => {
            acc[key] = Category_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Category>, I>>(base?: I): Category {
    return Category.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category>, I>>(object: I): Category {
    const message = createBaseCategory();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Category_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Category_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Category_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.target_type = object.target_type ?? 0;
    message.category_id = object.category_id ?? 0;
    return message;
  }
};

function createBaseCategory_Asset(): Category_Asset {
  return { name: '', icon: undefined };
}

export const Category_Asset: MessageFns<Category_Asset> = {
  fromJSON(object: any): Category_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? AssetInfo.fromJSON(object.icon) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Category_Asset>, I>>(base?: I): Category_Asset {
    return Category_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category_Asset>, I>>(object: I): Category_Asset {
    const message = createBaseCategory_Asset();
    message.name = object.name ?? '';
    message.icon = object.icon !== undefined && object.icon !== null ? AssetInfo.fromPartial(object.icon) : undefined;
    return message;
  }
};

function createBaseCategory_AssetsEntry(): Category_AssetsEntry {
  return { key: '', value: undefined };
}

export const Category_AssetsEntry: MessageFns<Category_AssetsEntry> = {
  fromJSON(object: any): Category_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Category_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Category_AssetsEntry>, I>>(base?: I): Category_AssetsEntry {
    return Category_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category_AssetsEntry>, I>>(object: I): Category_AssetsEntry {
    const message = createBaseCategory_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Category_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseResource(): Resource {
  return { headwear: undefined };
}

export const Resource: MessageFns<Resource> = {
  fromJSON(object: any): Resource {
    return { headwear: isSet(object.headwear) ? Headwear.fromJSON(object.headwear) : undefined };
  },

  create<I extends Exact<DeepPartial<Resource>, I>>(base?: I): Resource {
    return Resource.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Resource>, I>>(object: I): Resource {
    const message = createBaseResource();
    message.headwear =
      object.headwear !== undefined && object.headwear !== null ? Headwear.fromPartial(object.headwear) : undefined;
    return message;
  }
};

function createBaseHeadwear(): Headwear {
  return { asset: undefined, assets: {}, id: 0 };
}

export const Headwear: MessageFns<Headwear> = {
  fromJSON(object: any): Headwear {
    return {
      asset: isSet(object.asset) ? Headwear_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Headwear_Asset }>((acc, [key, value]) => {
            acc[key] = Headwear_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Headwear>, I>>(base?: I): Headwear {
    return Headwear.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear>, I>>(object: I): Headwear {
    const message = createBaseHeadwear();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Headwear_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Headwear_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Headwear_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseHeadwear_Asset(): Headwear_Asset {
  return { name: '', brief: '', thumb: undefined, image: undefined, animation: undefined };
}

export const Headwear_Asset: MessageFns<Headwear_Asset> = {
  fromJSON(object: any): Headwear_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      thumb: isSet(object.thumb) ? AssetInfo.fromJSON(object.thumb) : undefined,
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Headwear_Asset>, I>>(base?: I): Headwear_Asset {
    return Headwear_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear_Asset>, I>>(object: I): Headwear_Asset {
    const message = createBaseHeadwear_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.thumb =
      object.thumb !== undefined && object.thumb !== null ? AssetInfo.fromPartial(object.thumb) : undefined;
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseHeadwear_AssetsEntry(): Headwear_AssetsEntry {
  return { key: '', value: undefined };
}

export const Headwear_AssetsEntry: MessageFns<Headwear_AssetsEntry> = {
  fromJSON(object: any): Headwear_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Headwear_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Headwear_AssetsEntry>, I>>(base?: I): Headwear_AssetsEntry {
    return Headwear_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear_AssetsEntry>, I>>(object: I): Headwear_AssetsEntry {
    const message = createBaseHeadwear_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Headwear_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseUsingPrivilege(): UsingPrivilege {
  return { headwear: undefined };
}

export const UsingPrivilege: MessageFns<UsingPrivilege> = {
  fromJSON(object: any): UsingPrivilege {
    return { headwear: isSet(object.headwear) ? Headwear.fromJSON(object.headwear) : undefined };
  },

  create<I extends Exact<DeepPartial<UsingPrivilege>, I>>(base?: I): UsingPrivilege {
    return UsingPrivilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UsingPrivilege>, I>>(object: I): UsingPrivilege {
    const message = createBaseUsingPrivilege();
    message.headwear =
      object.headwear !== undefined && object.headwear !== null ? Headwear.fromPartial(object.headwear) : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
