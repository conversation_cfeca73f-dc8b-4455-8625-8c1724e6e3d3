// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: api/code.proto

/* eslint-disable */

export const protobufPackage = 'errcode';

/** 错误码 */
export enum ErrCode {
  ERR_CODE_OK = 0,
  /** LIVE_ROOM_NOT_CREATE - 房间直播号段 21000 - 22000 */
  LIVE_ROOM_NOT_CREATE = 21000,
  /** LIVE_ROOM_PERMISSION_PASSWORD - 没有权限，需要密码 */
  LIVE_ROOM_PERMISSION_PASSWORD = 21001,
  /** LOVE_ROOM_PERMISSION_PRIVATE - 没有权限，私密房间不允许进入 */
  LOVE_ROOM_PERMISSION_PRIVATE = 21002,
  /** LIVE_ROOM_PERMISSION_WRONG_PASSWORD - 密码错误 */
  LIVE_ROOM_PERMISSION_WRONG_PASSWORD = 21003,
  /** LIVE_ROOM_PERMISSION_FORBIDDEN - 没有权限访问，其他通用没有权限判断，例如：测试房间不允许非测试账号进入，且无需告知具体原因 */
  LIVE_ROOM_PERMISSION_FORBIDDEN = 21004,
  /** LIVE_ROOM_DISCONNECTED - 连接已断开，需要重新进房 */
  LIVE_ROOM_DISCONNECTED = 21006,
  /** LIVE_ROOM_LIVE_OFF - 房间未开播 */
  LIVE_ROOM_LIVE_OFF = 21007,
  /** LIVE_ROOM_OWNER_ADMIN_CANNOT_KICK_OFF - 房主和管理员不能被踢 */
  LIVE_ROOM_OWNER_ADMIN_CANNOT_KICK_OFF = 21008,
  /** LIVE_ROOM_KICK_OFF_NO_PERMISSION - 踢人权限不够 */
  LIVE_ROOM_KICK_OFF_NO_PERMISSION = 21009,
  /** LIVE_ROOM_REENTER_FAIL - 重连失败，长时间掉线或者其他原因，这情况需要用户退出房间，重新走 Enter 进房 */
  LIVE_ROOM_REENTER_FAIL = 21010,
  /** LIVE_ROOM_PUBLISH_CONFIG_ID_INVALID - 房间创建、房间模式切换时，传入的参数无效或者当前场景不能使用 */
  LIVE_ROOM_PUBLISH_CONFIG_ID_INVALID = 21011,
  /** LIVE_ROOM_NO_LIVE_PERM - 直播间错误 */
  LIVE_ROOM_NO_LIVE_PERM = 21012,
  /** LIVE_ROOM_MSG_MUTE - 公屏禁言 */
  LIVE_ROOM_MSG_MUTE = 21013,
  /** Follow_Relation_NotExist - 关注错误 */
  Follow_Relation_NotExist = 21014,
  /** User_Relation_StaticErr - 关注汇总信息不存在或者小于等于0 无法取消关注 */
  User_Relation_StaticErr = 21015,
  /** Follow_Relation_Repeat - 关注关系已存在 无法重复关注 */
  Follow_Relation_Repeat = 21016,
  /** LIVE_ROOM_LIVING_STATUS - 直播状态中 无法继续开播 */
  LIVE_ROOM_LIVING_STATUS = 21017,
  /** WishGiftSameConfig - 心愿礼物 */
  WishGiftSameConfig = 21018,
  /** ROOM_NOT_EXIST - 房间不存在 */
  ROOM_NOT_EXIST = 22000,
  /** ROOM_EXISTED - 房间已存在 */
  ROOM_EXISTED = 22001,
  /** ROOM_LAYOUT_NOT_SUPPORT - 布局类型不存在或不支持 */
  ROOM_LAYOUT_NOT_SUPPORT = 22002,
  /** ROOM_NAME_BLANK - 房间名是空白 */
  ROOM_NAME_BLANK = 22003,
  /** ROOM_ADMIN_EXCEED - 房间管理员数量超出限制 */
  ROOM_ADMIN_EXCEED = 22004,
  /** ROOM_ONLY_VIP_CAN_CREATE - 仅 vip 可创建房间 */
  ROOM_ONLY_VIP_CAN_CREATE = 22005,
  /** ROOM_ONLY_PAY_CAN_ENTER - 仅付费可进入房间 */
  ROOM_ONLY_PAY_CAN_ENTER = 22006,
  /** ROOM_INFO_VIOLATION - 房间相关信息违规 */
  ROOM_INFO_VIOLATION = 22007,
  /** USER_NOT_IN_ROOM - 用户不在房间内 */
  USER_NOT_IN_ROOM = 23000,
  /** SEAT_NOT_EXIST - 麦位不存在 */
  SEAT_NOT_EXIST = 23001,
  /** SEAT_NOT_EXIST_USER - 麦位上不存在用户 */
  SEAT_NOT_EXIST_USER = 23002,
  /** USER_NOT_ON_SEAT - 用户不在麦位上 */
  USER_NOT_ON_SEAT = 23003,
  /** USER_ALREADY_ON_SEAT - 用户已在麦位上 */
  USER_ALREADY_ON_SEAT = 23004,
  /** SEAT_OCCUPIED - 麦位已经有用户 */
  SEAT_OCCUPIED = 23005,
  /** SEAT_LOCKED - 麦位已锁定 */
  SEAT_LOCKED = 23006,
  /** SEAT_VERSION_CONFLICT - 麦位变更版本冲突 */
  SEAT_VERSION_CONFLICT = 23007,
  /** SEAT_NO_NEED_APPLY - 麦位不需要申请上麦 */
  SEAT_NO_NEED_APPLY = 23008,
  /** SEAT_APPLY_EXIST - 麦位申请已存在 */
  SEAT_APPLY_EXIST = 23009,
  /** SEAT_KICK_NOT_ALLOW - 踢下麦操作不允许 */
  SEAT_KICK_NOT_ALLOW = 23010,
  /** SEAT_NO_PERMISSION - 没有权限 */
  SEAT_NO_PERMISSION = 23011,
  /** SEAT_APPLY_HAS_PROCESSED - 麦位申请已处理 */
  SEAT_APPLY_HAS_PROCESSED = 23012,
  /** SEAT_ALL_OCCUPIED - 麦位都被占用 */
  SEAT_ALL_OCCUPIED = 23013,
  /** USER_NOT_EXIST - 用户资料相关错误码 */
  USER_NOT_EXIST = 24000,
  /** USER_ILLEGAL_NICKNAME - 非法昵称 */
  USER_ILLEGAL_NICKNAME = 24001,
  /** USER_ILLEGAL_INTRODUCTION - 非法个人介绍 */
  USER_ILLEGAL_INTRODUCTION = 24002,
  /** USER_ILLEGAL_GENDER - 非法性别 */
  USER_ILLEGAL_GENDER = 24003,
  /** USER_ILLEGAL_AVATAR - 非法头像 */
  USER_ILLEGAL_AVATAR = 24004,
  /** USER_ILLEGAL_COUNTRY - 非法国家 */
  USER_ILLEGAL_COUNTRY = 24005,
  /** USER_ILLEGAL_BIRTHDAY - 非法生日 */
  USER_ILLEGAL_BIRTHDAY = 24006,
  /** USER_INSUFFICIENT_BALANCE - 用户余额错误码 */
  USER_INSUFFICIENT_BALANCE = 25001,
  /** GIFT_NOT_EXIST - 礼物相关错误码 */
  GIFT_NOT_EXIST = 26001,
  /** GIFT_NOT_ALLOW_SEND_GIFT_MYSELF - 不能给自己发礼物 */
  GIFT_NOT_ALLOW_SEND_GIFT_MYSELF = 26002,
  /** ROOM_MSG_BANNED - 公屏消息错误码 */
  ROOM_MSG_BANNED = 27001,
  /** ROOM_MSG_VIOLATION - 公屏消息违规 */
  ROOM_MSG_VIOLATION = 27002,
  /** REWARD_PACKAGE_ERROR - 奖励包发奖错误码 */
  REWARD_PACKAGE_ERROR = 28001,
  /** REJECT_LIMIT_ERROR - komi主播包 */
  REJECT_LIMIT_ERROR = 29001,
  /** Repeat_BIND_GUILD_ERROR - 重复绑定公会码报错 */
  Repeat_BIND_GUILD_ERROR = 29002,
  /** MSG_CONTENT_VIOLATION - 文案/文字违规 */
  MSG_CONTENT_VIOLATION = 30001,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ERR_CODE_OK':
      return ErrCode.ERR_CODE_OK;
    case 21000:
    case 'LIVE_ROOM_NOT_CREATE':
      return ErrCode.LIVE_ROOM_NOT_CREATE;
    case 21001:
    case 'LIVE_ROOM_PERMISSION_PASSWORD':
      return ErrCode.LIVE_ROOM_PERMISSION_PASSWORD;
    case 21002:
    case 'LOVE_ROOM_PERMISSION_PRIVATE':
      return ErrCode.LOVE_ROOM_PERMISSION_PRIVATE;
    case 21003:
    case 'LIVE_ROOM_PERMISSION_WRONG_PASSWORD':
      return ErrCode.LIVE_ROOM_PERMISSION_WRONG_PASSWORD;
    case 21004:
    case 'LIVE_ROOM_PERMISSION_FORBIDDEN':
      return ErrCode.LIVE_ROOM_PERMISSION_FORBIDDEN;
    case 21006:
    case 'LIVE_ROOM_DISCONNECTED':
      return ErrCode.LIVE_ROOM_DISCONNECTED;
    case 21007:
    case 'LIVE_ROOM_LIVE_OFF':
      return ErrCode.LIVE_ROOM_LIVE_OFF;
    case 21008:
    case 'LIVE_ROOM_OWNER_ADMIN_CANNOT_KICK_OFF':
      return ErrCode.LIVE_ROOM_OWNER_ADMIN_CANNOT_KICK_OFF;
    case 21009:
    case 'LIVE_ROOM_KICK_OFF_NO_PERMISSION':
      return ErrCode.LIVE_ROOM_KICK_OFF_NO_PERMISSION;
    case 21010:
    case 'LIVE_ROOM_REENTER_FAIL':
      return ErrCode.LIVE_ROOM_REENTER_FAIL;
    case 21011:
    case 'LIVE_ROOM_PUBLISH_CONFIG_ID_INVALID':
      return ErrCode.LIVE_ROOM_PUBLISH_CONFIG_ID_INVALID;
    case 21012:
    case 'LIVE_ROOM_NO_LIVE_PERM':
      return ErrCode.LIVE_ROOM_NO_LIVE_PERM;
    case 21013:
    case 'LIVE_ROOM_MSG_MUTE':
      return ErrCode.LIVE_ROOM_MSG_MUTE;
    case 21014:
    case 'Follow_Relation_NotExist':
      return ErrCode.Follow_Relation_NotExist;
    case 21015:
    case 'User_Relation_StaticErr':
      return ErrCode.User_Relation_StaticErr;
    case 21016:
    case 'Follow_Relation_Repeat':
      return ErrCode.Follow_Relation_Repeat;
    case 21017:
    case 'LIVE_ROOM_LIVING_STATUS':
      return ErrCode.LIVE_ROOM_LIVING_STATUS;
    case 21018:
    case 'WishGiftSameConfig':
      return ErrCode.WishGiftSameConfig;
    case 22000:
    case 'ROOM_NOT_EXIST':
      return ErrCode.ROOM_NOT_EXIST;
    case 22001:
    case 'ROOM_EXISTED':
      return ErrCode.ROOM_EXISTED;
    case 22002:
    case 'ROOM_LAYOUT_NOT_SUPPORT':
      return ErrCode.ROOM_LAYOUT_NOT_SUPPORT;
    case 22003:
    case 'ROOM_NAME_BLANK':
      return ErrCode.ROOM_NAME_BLANK;
    case 22004:
    case 'ROOM_ADMIN_EXCEED':
      return ErrCode.ROOM_ADMIN_EXCEED;
    case 22005:
    case 'ROOM_ONLY_VIP_CAN_CREATE':
      return ErrCode.ROOM_ONLY_VIP_CAN_CREATE;
    case 22006:
    case 'ROOM_ONLY_PAY_CAN_ENTER':
      return ErrCode.ROOM_ONLY_PAY_CAN_ENTER;
    case 22007:
    case 'ROOM_INFO_VIOLATION':
      return ErrCode.ROOM_INFO_VIOLATION;
    case 23000:
    case 'USER_NOT_IN_ROOM':
      return ErrCode.USER_NOT_IN_ROOM;
    case 23001:
    case 'SEAT_NOT_EXIST':
      return ErrCode.SEAT_NOT_EXIST;
    case 23002:
    case 'SEAT_NOT_EXIST_USER':
      return ErrCode.SEAT_NOT_EXIST_USER;
    case 23003:
    case 'USER_NOT_ON_SEAT':
      return ErrCode.USER_NOT_ON_SEAT;
    case 23004:
    case 'USER_ALREADY_ON_SEAT':
      return ErrCode.USER_ALREADY_ON_SEAT;
    case 23005:
    case 'SEAT_OCCUPIED':
      return ErrCode.SEAT_OCCUPIED;
    case 23006:
    case 'SEAT_LOCKED':
      return ErrCode.SEAT_LOCKED;
    case 23007:
    case 'SEAT_VERSION_CONFLICT':
      return ErrCode.SEAT_VERSION_CONFLICT;
    case 23008:
    case 'SEAT_NO_NEED_APPLY':
      return ErrCode.SEAT_NO_NEED_APPLY;
    case 23009:
    case 'SEAT_APPLY_EXIST':
      return ErrCode.SEAT_APPLY_EXIST;
    case 23010:
    case 'SEAT_KICK_NOT_ALLOW':
      return ErrCode.SEAT_KICK_NOT_ALLOW;
    case 23011:
    case 'SEAT_NO_PERMISSION':
      return ErrCode.SEAT_NO_PERMISSION;
    case 23012:
    case 'SEAT_APPLY_HAS_PROCESSED':
      return ErrCode.SEAT_APPLY_HAS_PROCESSED;
    case 23013:
    case 'SEAT_ALL_OCCUPIED':
      return ErrCode.SEAT_ALL_OCCUPIED;
    case 24000:
    case 'USER_NOT_EXIST':
      return ErrCode.USER_NOT_EXIST;
    case 24001:
    case 'USER_ILLEGAL_NICKNAME':
      return ErrCode.USER_ILLEGAL_NICKNAME;
    case 24002:
    case 'USER_ILLEGAL_INTRODUCTION':
      return ErrCode.USER_ILLEGAL_INTRODUCTION;
    case 24003:
    case 'USER_ILLEGAL_GENDER':
      return ErrCode.USER_ILLEGAL_GENDER;
    case 24004:
    case 'USER_ILLEGAL_AVATAR':
      return ErrCode.USER_ILLEGAL_AVATAR;
    case 24005:
    case 'USER_ILLEGAL_COUNTRY':
      return ErrCode.USER_ILLEGAL_COUNTRY;
    case 24006:
    case 'USER_ILLEGAL_BIRTHDAY':
      return ErrCode.USER_ILLEGAL_BIRTHDAY;
    case 25001:
    case 'USER_INSUFFICIENT_BALANCE':
      return ErrCode.USER_INSUFFICIENT_BALANCE;
    case 26001:
    case 'GIFT_NOT_EXIST':
      return ErrCode.GIFT_NOT_EXIST;
    case 26002:
    case 'GIFT_NOT_ALLOW_SEND_GIFT_MYSELF':
      return ErrCode.GIFT_NOT_ALLOW_SEND_GIFT_MYSELF;
    case 27001:
    case 'ROOM_MSG_BANNED':
      return ErrCode.ROOM_MSG_BANNED;
    case 27002:
    case 'ROOM_MSG_VIOLATION':
      return ErrCode.ROOM_MSG_VIOLATION;
    case 28001:
    case 'REWARD_PACKAGE_ERROR':
      return ErrCode.REWARD_PACKAGE_ERROR;
    case 29001:
    case 'REJECT_LIMIT_ERROR':
      return ErrCode.REJECT_LIMIT_ERROR;
    case 29002:
    case 'Repeat_BIND_GUILD_ERROR':
      return ErrCode.Repeat_BIND_GUILD_ERROR;
    case 30001:
    case 'MSG_CONTENT_VIOLATION':
      return ErrCode.MSG_CONTENT_VIOLATION;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}
