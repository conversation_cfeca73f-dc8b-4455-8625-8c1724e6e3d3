// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/app/upgrade.proto

/* eslint-disable */
import { <PERSON><PERSON> } from '../api/sconfig/model';

export const protobufPackage = 'app.config.public.upgrade';

/** 强升配置 */
export interface AppUpgrade {
  pkg_name: string;
  version_name: string;
  version_code: number;
  apk_link: string;
  md5: string;
  version_code_64: number;
  apk_abi_link: string;
  abi_md5: string;
  market_link: string;
  is_force_update: boolean;
  title: Lang<PERSON> | undefined;
  content: Lang<PERSON> | undefined;
}

function createBaseAppUpgrade(): AppUpgrade {
  return {
    pkg_name: '',
    version_name: '',
    version_code: 0,
    apk_link: '',
    md5: '',
    version_code_64: 0,
    apk_abi_link: '',
    abi_md5: '',
    market_link: '',
    is_force_update: false,
    title: undefined,
    content: undefined
  };
}

export const AppUpgrade: MessageFns<AppUpgrade> = {
  fromJSON(object: any): AppUpgrade {
    return {
      pkg_name: isSet(object.pkg_name) ? globalThis.String(object.pkg_name) : '',
      version_name: isSet(object.version_name) ? globalThis.String(object.version_name) : '',
      version_code: isSet(object.version_code) ? globalThis.Number(object.version_code) : 0,
      apk_link: isSet(object.apk_link) ? globalThis.String(object.apk_link) : '',
      md5: isSet(object.md5) ? globalThis.String(object.md5) : '',
      version_code_64: isSet(object.version_code_64) ? globalThis.Number(object.version_code_64) : 0,
      apk_abi_link: isSet(object.apk_abi_link) ? globalThis.String(object.apk_abi_link) : '',
      abi_md5: isSet(object.abi_md5) ? globalThis.String(object.abi_md5) : '',
      market_link: isSet(object.market_link) ? globalThis.String(object.market_link) : '',
      is_force_update: isSet(object.is_force_update) ? globalThis.Boolean(object.is_force_update) : false,
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      content: isSet(object.content) ? Langs.fromJSON(object.content) : undefined
    };
  },

  create<I extends Exact<DeepPartial<AppUpgrade>, I>>(base?: I): AppUpgrade {
    return AppUpgrade.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppUpgrade>, I>>(object: I): AppUpgrade {
    const message = createBaseAppUpgrade();
    message.pkg_name = object.pkg_name ?? '';
    message.version_name = object.version_name ?? '';
    message.version_code = object.version_code ?? 0;
    message.apk_link = object.apk_link ?? '';
    message.md5 = object.md5 ?? '';
    message.version_code_64 = object.version_code_64 ?? 0;
    message.apk_abi_link = object.apk_abi_link ?? '';
    message.abi_md5 = object.abi_md5 ?? '';
    message.market_link = object.market_link ?? '';
    message.is_force_update = object.is_force_update ?? false;
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.content =
      object.content !== undefined && object.content !== null ? Langs.fromPartial(object.content) : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
