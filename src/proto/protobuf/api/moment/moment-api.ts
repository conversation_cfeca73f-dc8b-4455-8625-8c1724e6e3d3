// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/moment/moment-api.proto

/* eslint-disable */
import { Page } from '../common/common';
import { OSType, oSTypeFromJSON } from '../common/common-net';
import { Attachments, BizData, Comment, Location, Moment, Tag, Text } from './moment-comm';

export const protobufPackage = 'comm.api.moment';

export interface PublishMomentReq {
  draft: MomentDraft | undefined;
}

export interface PublishMomentRsp {
  /** 动态 */
  moment: Moment | undefined;
}

/** 待发布的动态草稿 */
export interface MomentDraft {
  /** 客户端维护版本号，如果新版本不兼容旧版本，则递增版本号，渲染时如果当前维护版本小于该版本号则过滤或提示升级 */
  version: number;
  /** 动态文案 */
  text: Text | undefined;
  /** 标签 */
  tags: Tag[];
  /** 位置 */
  location: Location | undefined;
  /** 动态附件(微信方案, 不允许一个动态里面既包含图片又包含视频.) */
  attachments: Attachments | undefined;
  /** repeated Attachment attachments = 30; // 动态附件(微博方案, 允许一个动态里面既包含图片又包含视频, 而且可以随意排列组合) */
  context: BizData | undefined;
  /** 拓展信息, 由业务后端写入, 用于业务后端填充此动态的发布者相关信息, 中台只负责存储和传输并不关心其内容及结构. */
  expand: BizData | undefined;
}

export interface DeleteMomentReq {
  /** 最多100个，超出则截断不处理 */
  moment_ids: number[];
}

export interface DeleteMomentRsp {
  /** 成功的ID列表 */
  success_ids: number[];
}

export interface ListMomentReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 用户ID */
  uid: number;
}

export interface ListMomentRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 动态列表 */
  moments: Moment[];
}

export interface ViewMomentReq {
  moment_ids: number[];
}

export interface ViewMomentRsp {}

export interface PublishCommentReq {
  draft: CommentDraft | undefined;
}

export interface PublishCommentRsp {
  comment: Comment | undefined;
}

export interface CommentDraft {
  /** 客户端维护版本号，如果新版本不兼容旧版本，则递增版本号，渲染时如果当前维护版本小于该版本号则过滤或提示升级 */
  version: number;
  /** 动态ID */
  moment_id: number;
  /** 楼中楼（即一层主评论和一层子评论）时用于控制层级，大于0表示这是在一条主评论下的子评论 */
  root_comment_id: number;
  /** 平铺（没有层级关系，评论和回复处于同一层）时用于指示回复哪个评论，0表示评论，大于0表示回复 */
  refer_comment_id: number;
  /** 平铺（没有层级关系，评论和回复处于同一层）时用于指示回复哪个评论者 */
  refer_uid: number;
  /** 评论内容 */
  text: Text | undefined;
  /** 透传上下文, 由客户端写入, 用于业务客户端之间或业务客户端和服务端进行能力拓展, 中台只负责存储和传输并不关心其内容及结构. */
  context: BizData | undefined;
  /** 拓展信息, 由业务后端写入, 用于业务后端填充此动态的发布者相关信息, 中台只负责存储和传输并不关心其内容及结构. */
  expand: BizData | undefined;
}

export interface DeleteCommentReq {
  /** 最多100个，超出则截断不处理 */
  comment_ids: number[];
}

export interface DeleteCommentRsp {
  /** 成功的ID列表 */
  success_ids: number[];
}

export interface ListCommentReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 评论样式 */
  style: ListCommentReq_Style;
  /** 动态ID */
  moment_id: number;
  /** 楼中楼时的引用评论ID, 如果不传此字段表示查动态的主评论, 否则表示查某条评论的跟评. */
  comment_id: number;
}

export enum ListCommentReq_Style {
  STYLE_NONE = 0,
  /** STYLE_FLAT - 平铺，没有嵌套 */
  STYLE_FLAT = 1,
  /** STYLE_NESTED - 楼中楼，有主评论和子评论2层嵌套 */
  STYLE_NESTED = 2,
  UNRECOGNIZED = -1
}

export function listCommentReq_StyleFromJSON(object: any): ListCommentReq_Style {
  switch (object) {
    case 0:
    case 'STYLE_NONE':
      return ListCommentReq_Style.STYLE_NONE;
    case 1:
    case 'STYLE_FLAT':
      return ListCommentReq_Style.STYLE_FLAT;
    case 2:
    case 'STYLE_NESTED':
      return ListCommentReq_Style.STYLE_NESTED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ListCommentReq_Style.UNRECOGNIZED;
  }
}

export interface ListCommentRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 评论列表 */
  comments: Comment[];
}

export interface BatchGetCommentReq {
  /** 最多100个，超出则截断不处理 */
  comment_ids: number[];
}

export interface BatchGetCommentRsp {
  comment_map: { [key: number]: Comment };
}

export interface BatchGetCommentRsp_CommentMapEntry {
  key: number;
  value: Comment | undefined;
}

export interface PublishEnv {
  pkg: string;
  verc: number;
  os_type: OSType;
  os_ver: string;
  cou: string;
}

export interface BatchGetMomentReq {
  /** 最多100个，超出则截断不处理 */
  moment_ids: number[];
}

export interface BatchGetMomentRsp {
  moment_map: { [key: number]: Moment };
}

export interface BatchGetMomentRsp_MomentMapEntry {
  key: number;
  value: Moment | undefined;
}

function createBasePublishMomentReq(): PublishMomentReq {
  return { draft: undefined };
}

export const PublishMomentReq: MessageFns<PublishMomentReq> = {
  fromJSON(object: any): PublishMomentReq {
    return { draft: isSet(object.draft) ? MomentDraft.fromJSON(object.draft) : undefined };
  },

  create<I extends Exact<DeepPartial<PublishMomentReq>, I>>(base?: I): PublishMomentReq {
    return PublishMomentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishMomentReq>, I>>(object: I): PublishMomentReq {
    const message = createBasePublishMomentReq();
    message.draft =
      object.draft !== undefined && object.draft !== null ? MomentDraft.fromPartial(object.draft) : undefined;
    return message;
  }
};

function createBasePublishMomentRsp(): PublishMomentRsp {
  return { moment: undefined };
}

export const PublishMomentRsp: MessageFns<PublishMomentRsp> = {
  fromJSON(object: any): PublishMomentRsp {
    return { moment: isSet(object.moment) ? Moment.fromJSON(object.moment) : undefined };
  },

  create<I extends Exact<DeepPartial<PublishMomentRsp>, I>>(base?: I): PublishMomentRsp {
    return PublishMomentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishMomentRsp>, I>>(object: I): PublishMomentRsp {
    const message = createBasePublishMomentRsp();
    message.moment =
      object.moment !== undefined && object.moment !== null ? Moment.fromPartial(object.moment) : undefined;
    return message;
  }
};

function createBaseMomentDraft(): MomentDraft {
  return {
    version: 0,
    text: undefined,
    tags: [],
    location: undefined,
    attachments: undefined,
    context: undefined,
    expand: undefined
  };
}

export const MomentDraft: MessageFns<MomentDraft> = {
  fromJSON(object: any): MomentDraft {
    return {
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      text: isSet(object.text) ? Text.fromJSON(object.text) : undefined,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => Tag.fromJSON(e)) : [],
      location: isSet(object.location) ? Location.fromJSON(object.location) : undefined,
      attachments: isSet(object.attachments) ? Attachments.fromJSON(object.attachments) : undefined,
      context: isSet(object.context) ? BizData.fromJSON(object.context) : undefined,
      expand: isSet(object.expand) ? BizData.fromJSON(object.expand) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MomentDraft>, I>>(base?: I): MomentDraft {
    return MomentDraft.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MomentDraft>, I>>(object: I): MomentDraft {
    const message = createBaseMomentDraft();
    message.version = object.version ?? 0;
    message.text = object.text !== undefined && object.text !== null ? Text.fromPartial(object.text) : undefined;
    message.tags = object.tags?.map(e => Tag.fromPartial(e)) || [];
    message.location =
      object.location !== undefined && object.location !== null ? Location.fromPartial(object.location) : undefined;
    message.attachments =
      object.attachments !== undefined && object.attachments !== null
        ? Attachments.fromPartial(object.attachments)
        : undefined;
    message.context =
      object.context !== undefined && object.context !== null ? BizData.fromPartial(object.context) : undefined;
    message.expand =
      object.expand !== undefined && object.expand !== null ? BizData.fromPartial(object.expand) : undefined;
    return message;
  }
};

function createBaseDeleteMomentReq(): DeleteMomentReq {
  return { moment_ids: [] };
}

export const DeleteMomentReq: MessageFns<DeleteMomentReq> = {
  fromJSON(object: any): DeleteMomentReq {
    return {
      moment_ids: globalThis.Array.isArray(object?.moment_ids)
        ? object.moment_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteMomentReq>, I>>(base?: I): DeleteMomentReq {
    return DeleteMomentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteMomentReq>, I>>(object: I): DeleteMomentReq {
    const message = createBaseDeleteMomentReq();
    message.moment_ids = object.moment_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteMomentRsp(): DeleteMomentRsp {
  return { success_ids: [] };
}

export const DeleteMomentRsp: MessageFns<DeleteMomentRsp> = {
  fromJSON(object: any): DeleteMomentRsp {
    return {
      success_ids: globalThis.Array.isArray(object?.success_ids)
        ? object.success_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteMomentRsp>, I>>(base?: I): DeleteMomentRsp {
    return DeleteMomentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteMomentRsp>, I>>(object: I): DeleteMomentRsp {
    const message = createBaseDeleteMomentRsp();
    message.success_ids = object.success_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListMomentReq(): ListMomentReq {
  return { page: undefined, uid: 0 };
}

export const ListMomentReq: MessageFns<ListMomentReq> = {
  fromJSON(object: any): ListMomentReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListMomentReq>, I>>(base?: I): ListMomentReq {
    return ListMomentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMomentReq>, I>>(object: I): ListMomentReq {
    const message = createBaseListMomentReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseListMomentRsp(): ListMomentRsp {
  return { page: undefined, moments: [] };
}

export const ListMomentRsp: MessageFns<ListMomentRsp> = {
  fromJSON(object: any): ListMomentRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      moments: globalThis.Array.isArray(object?.moments) ? object.moments.map((e: any) => Moment.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListMomentRsp>, I>>(base?: I): ListMomentRsp {
    return ListMomentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMomentRsp>, I>>(object: I): ListMomentRsp {
    const message = createBaseListMomentRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.moments = object.moments?.map(e => Moment.fromPartial(e)) || [];
    return message;
  }
};

function createBaseViewMomentReq(): ViewMomentReq {
  return { moment_ids: [] };
}

export const ViewMomentReq: MessageFns<ViewMomentReq> = {
  fromJSON(object: any): ViewMomentReq {
    return {
      moment_ids: globalThis.Array.isArray(object?.moment_ids)
        ? object.moment_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ViewMomentReq>, I>>(base?: I): ViewMomentReq {
    return ViewMomentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ViewMomentReq>, I>>(object: I): ViewMomentReq {
    const message = createBaseViewMomentReq();
    message.moment_ids = object.moment_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseViewMomentRsp(): ViewMomentRsp {
  return {};
}

export const ViewMomentRsp: MessageFns<ViewMomentRsp> = {
  fromJSON(_: any): ViewMomentRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ViewMomentRsp>, I>>(base?: I): ViewMomentRsp {
    return ViewMomentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ViewMomentRsp>, I>>(_: I): ViewMomentRsp {
    const message = createBaseViewMomentRsp();
    return message;
  }
};

function createBasePublishCommentReq(): PublishCommentReq {
  return { draft: undefined };
}

export const PublishCommentReq: MessageFns<PublishCommentReq> = {
  fromJSON(object: any): PublishCommentReq {
    return { draft: isSet(object.draft) ? CommentDraft.fromJSON(object.draft) : undefined };
  },

  create<I extends Exact<DeepPartial<PublishCommentReq>, I>>(base?: I): PublishCommentReq {
    return PublishCommentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishCommentReq>, I>>(object: I): PublishCommentReq {
    const message = createBasePublishCommentReq();
    message.draft =
      object.draft !== undefined && object.draft !== null ? CommentDraft.fromPartial(object.draft) : undefined;
    return message;
  }
};

function createBasePublishCommentRsp(): PublishCommentRsp {
  return { comment: undefined };
}

export const PublishCommentRsp: MessageFns<PublishCommentRsp> = {
  fromJSON(object: any): PublishCommentRsp {
    return { comment: isSet(object.comment) ? Comment.fromJSON(object.comment) : undefined };
  },

  create<I extends Exact<DeepPartial<PublishCommentRsp>, I>>(base?: I): PublishCommentRsp {
    return PublishCommentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishCommentRsp>, I>>(object: I): PublishCommentRsp {
    const message = createBasePublishCommentRsp();
    message.comment =
      object.comment !== undefined && object.comment !== null ? Comment.fromPartial(object.comment) : undefined;
    return message;
  }
};

function createBaseCommentDraft(): CommentDraft {
  return {
    version: 0,
    moment_id: 0,
    root_comment_id: 0,
    refer_comment_id: 0,
    refer_uid: 0,
    text: undefined,
    context: undefined,
    expand: undefined
  };
}

export const CommentDraft: MessageFns<CommentDraft> = {
  fromJSON(object: any): CommentDraft {
    return {
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      moment_id: isSet(object.moment_id) ? globalThis.Number(object.moment_id) : 0,
      root_comment_id: isSet(object.root_comment_id) ? globalThis.Number(object.root_comment_id) : 0,
      refer_comment_id: isSet(object.refer_comment_id) ? globalThis.Number(object.refer_comment_id) : 0,
      refer_uid: isSet(object.refer_uid) ? globalThis.Number(object.refer_uid) : 0,
      text: isSet(object.text) ? Text.fromJSON(object.text) : undefined,
      context: isSet(object.context) ? BizData.fromJSON(object.context) : undefined,
      expand: isSet(object.expand) ? BizData.fromJSON(object.expand) : undefined
    };
  },

  create<I extends Exact<DeepPartial<CommentDraft>, I>>(base?: I): CommentDraft {
    return CommentDraft.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommentDraft>, I>>(object: I): CommentDraft {
    const message = createBaseCommentDraft();
    message.version = object.version ?? 0;
    message.moment_id = object.moment_id ?? 0;
    message.root_comment_id = object.root_comment_id ?? 0;
    message.refer_comment_id = object.refer_comment_id ?? 0;
    message.refer_uid = object.refer_uid ?? 0;
    message.text = object.text !== undefined && object.text !== null ? Text.fromPartial(object.text) : undefined;
    message.context =
      object.context !== undefined && object.context !== null ? BizData.fromPartial(object.context) : undefined;
    message.expand =
      object.expand !== undefined && object.expand !== null ? BizData.fromPartial(object.expand) : undefined;
    return message;
  }
};

function createBaseDeleteCommentReq(): DeleteCommentReq {
  return { comment_ids: [] };
}

export const DeleteCommentReq: MessageFns<DeleteCommentReq> = {
  fromJSON(object: any): DeleteCommentReq {
    return {
      comment_ids: globalThis.Array.isArray(object?.comment_ids)
        ? object.comment_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteCommentReq>, I>>(base?: I): DeleteCommentReq {
    return DeleteCommentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCommentReq>, I>>(object: I): DeleteCommentReq {
    const message = createBaseDeleteCommentReq();
    message.comment_ids = object.comment_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteCommentRsp(): DeleteCommentRsp {
  return { success_ids: [] };
}

export const DeleteCommentRsp: MessageFns<DeleteCommentRsp> = {
  fromJSON(object: any): DeleteCommentRsp {
    return {
      success_ids: globalThis.Array.isArray(object?.success_ids)
        ? object.success_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteCommentRsp>, I>>(base?: I): DeleteCommentRsp {
    return DeleteCommentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteCommentRsp>, I>>(object: I): DeleteCommentRsp {
    const message = createBaseDeleteCommentRsp();
    message.success_ids = object.success_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListCommentReq(): ListCommentReq {
  return { page: undefined, style: 0, moment_id: 0, comment_id: 0 };
}

export const ListCommentReq: MessageFns<ListCommentReq> = {
  fromJSON(object: any): ListCommentReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      style: isSet(object.style) ? listCommentReq_StyleFromJSON(object.style) : 0,
      moment_id: isSet(object.moment_id) ? globalThis.Number(object.moment_id) : 0,
      comment_id: isSet(object.comment_id) ? globalThis.Number(object.comment_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListCommentReq>, I>>(base?: I): ListCommentReq {
    return ListCommentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCommentReq>, I>>(object: I): ListCommentReq {
    const message = createBaseListCommentReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.style = object.style ?? 0;
    message.moment_id = object.moment_id ?? 0;
    message.comment_id = object.comment_id ?? 0;
    return message;
  }
};

function createBaseListCommentRsp(): ListCommentRsp {
  return { page: undefined, comments: [] };
}

export const ListCommentRsp: MessageFns<ListCommentRsp> = {
  fromJSON(object: any): ListCommentRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      comments: globalThis.Array.isArray(object?.comments) ? object.comments.map((e: any) => Comment.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListCommentRsp>, I>>(base?: I): ListCommentRsp {
    return ListCommentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCommentRsp>, I>>(object: I): ListCommentRsp {
    const message = createBaseListCommentRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.comments = object.comments?.map(e => Comment.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetCommentReq(): BatchGetCommentReq {
  return { comment_ids: [] };
}

export const BatchGetCommentReq: MessageFns<BatchGetCommentReq> = {
  fromJSON(object: any): BatchGetCommentReq {
    return {
      comment_ids: globalThis.Array.isArray(object?.comment_ids)
        ? object.comment_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetCommentReq>, I>>(base?: I): BatchGetCommentReq {
    return BatchGetCommentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetCommentReq>, I>>(object: I): BatchGetCommentReq {
    const message = createBaseBatchGetCommentReq();
    message.comment_ids = object.comment_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetCommentRsp(): BatchGetCommentRsp {
  return { comment_map: {} };
}

export const BatchGetCommentRsp: MessageFns<BatchGetCommentRsp> = {
  fromJSON(object: any): BatchGetCommentRsp {
    return {
      comment_map: isObject(object.comment_map)
        ? Object.entries(object.comment_map).reduce<{ [key: number]: Comment }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Comment.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetCommentRsp>, I>>(base?: I): BatchGetCommentRsp {
    return BatchGetCommentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetCommentRsp>, I>>(object: I): BatchGetCommentRsp {
    const message = createBaseBatchGetCommentRsp();
    message.comment_map = Object.entries(object.comment_map ?? {}).reduce<{ [key: number]: Comment }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = Comment.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchGetCommentRsp_CommentMapEntry(): BatchGetCommentRsp_CommentMapEntry {
  return { key: 0, value: undefined };
}

export const BatchGetCommentRsp_CommentMapEntry: MessageFns<BatchGetCommentRsp_CommentMapEntry> = {
  fromJSON(object: any): BatchGetCommentRsp_CommentMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Comment.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetCommentRsp_CommentMapEntry>, I>>(
    base?: I
  ): BatchGetCommentRsp_CommentMapEntry {
    return BatchGetCommentRsp_CommentMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetCommentRsp_CommentMapEntry>, I>>(
    object: I
  ): BatchGetCommentRsp_CommentMapEntry {
    const message = createBaseBatchGetCommentRsp_CommentMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value !== undefined && object.value !== null ? Comment.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePublishEnv(): PublishEnv {
  return { pkg: '', verc: 0, os_type: 0, os_ver: '', cou: '' };
}

export const PublishEnv: MessageFns<PublishEnv> = {
  fromJSON(object: any): PublishEnv {
    return {
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      verc: isSet(object.verc) ? globalThis.Number(object.verc) : 0,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      os_ver: isSet(object.os_ver) ? globalThis.String(object.os_ver) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : ''
    };
  },

  create<I extends Exact<DeepPartial<PublishEnv>, I>>(base?: I): PublishEnv {
    return PublishEnv.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishEnv>, I>>(object: I): PublishEnv {
    const message = createBasePublishEnv();
    message.pkg = object.pkg ?? '';
    message.verc = object.verc ?? 0;
    message.os_type = object.os_type ?? 0;
    message.os_ver = object.os_ver ?? '';
    message.cou = object.cou ?? '';
    return message;
  }
};

function createBaseBatchGetMomentReq(): BatchGetMomentReq {
  return { moment_ids: [] };
}

export const BatchGetMomentReq: MessageFns<BatchGetMomentReq> = {
  fromJSON(object: any): BatchGetMomentReq {
    return {
      moment_ids: globalThis.Array.isArray(object?.moment_ids)
        ? object.moment_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetMomentReq>, I>>(base?: I): BatchGetMomentReq {
    return BatchGetMomentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetMomentReq>, I>>(object: I): BatchGetMomentReq {
    const message = createBaseBatchGetMomentReq();
    message.moment_ids = object.moment_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetMomentRsp(): BatchGetMomentRsp {
  return { moment_map: {} };
}

export const BatchGetMomentRsp: MessageFns<BatchGetMomentRsp> = {
  fromJSON(object: any): BatchGetMomentRsp {
    return {
      moment_map: isObject(object.moment_map)
        ? Object.entries(object.moment_map).reduce<{ [key: number]: Moment }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Moment.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetMomentRsp>, I>>(base?: I): BatchGetMomentRsp {
    return BatchGetMomentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetMomentRsp>, I>>(object: I): BatchGetMomentRsp {
    const message = createBaseBatchGetMomentRsp();
    message.moment_map = Object.entries(object.moment_map ?? {}).reduce<{ [key: number]: Moment }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = Moment.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchGetMomentRsp_MomentMapEntry(): BatchGetMomentRsp_MomentMapEntry {
  return { key: 0, value: undefined };
}

export const BatchGetMomentRsp_MomentMapEntry: MessageFns<BatchGetMomentRsp_MomentMapEntry> = {
  fromJSON(object: any): BatchGetMomentRsp_MomentMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Moment.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetMomentRsp_MomentMapEntry>, I>>(
    base?: I
  ): BatchGetMomentRsp_MomentMapEntry {
    return BatchGetMomentRsp_MomentMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetMomentRsp_MomentMapEntry>, I>>(
    object: I
  ): BatchGetMomentRsp_MomentMapEntry {
    const message = createBaseBatchGetMomentRsp_MomentMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value !== undefined && object.value !== null ? Moment.fromPartial(object.value) : undefined;
    return message;
  }
};

/**
 * ServiceName: social-biz
 * smicro:spath=gitit.cc/social/components-service/social-biz/biz/moment/handler
 */
export type MomentApiDefinition = typeof MomentApiDefinition;
export const MomentApiDefinition = {
  name: 'MomentApi',
  fullName: 'comm.api.moment.MomentApi',
  methods: {
    /** 发布动态 */
    publishMoment: {
      name: 'PublishMoment',
      requestType: PublishMomentReq,
      requestStream: false,
      responseType: PublishMomentRsp,
      responseStream: false,
      options: {}
    },
    /** 删除动态 */
    deleteMoment: {
      name: 'DeleteMoment',
      requestType: DeleteMomentReq,
      requestStream: false,
      responseType: DeleteMomentRsp,
      responseStream: false,
      options: {}
    },
    /** 拉取动态 */
    listMoment: {
      name: 'ListMoment',
      requestType: ListMomentReq,
      requestStream: false,
      responseType: ListMomentRsp,
      responseStream: false,
      options: {}
    },
    /** 浏览动态（打点） */
    viewMoment: {
      name: 'ViewMoment',
      requestType: ViewMomentReq,
      requestStream: false,
      responseType: ViewMomentRsp,
      responseStream: false,
      options: {}
    },
    /** 发布评论 */
    publishComment: {
      name: 'PublishComment',
      requestType: PublishCommentReq,
      requestStream: false,
      responseType: PublishCommentRsp,
      responseStream: false,
      options: {}
    },
    /** 删除评论 */
    deleteComment: {
      name: 'DeleteComment',
      requestType: DeleteCommentReq,
      requestStream: false,
      responseType: DeleteCommentRsp,
      responseStream: false,
      options: {}
    },
    /** 拉取评论 */
    listComment: {
      name: 'ListComment',
      requestType: ListCommentReq,
      requestStream: false,
      responseType: ListCommentRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID批量查询动态 */
    batchGetMoment: {
      name: 'BatchGetMoment',
      requestType: BatchGetMomentReq,
      requestStream: false,
      responseType: BatchGetMomentRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID批量查询评论 */
    batchGetComment: {
      name: 'BatchGetComment',
      requestType: BatchGetCommentReq,
      requestStream: false,
      responseType: BatchGetCommentRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
