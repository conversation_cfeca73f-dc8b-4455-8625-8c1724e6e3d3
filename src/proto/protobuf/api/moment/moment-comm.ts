// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/moment/moment-comm.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.moment';

/** 审核状态 */
export enum AuditStatus {
  /** AUDIT_STATUS_NONE - 待审核 */
  AUDIT_STATUS_NONE = 0,
  /** AUDIT_STATUS_APPROVED - 通过 */
  AUDIT_STATUS_APPROVED = 1,
  /** AUDIT_STATUS_REJECTED - 拒绝 */
  AUDIT_STATUS_REJECTED = 2,
  UNRECOGNIZED = -1
}

export function auditStatusFromJSON(object: any): AuditStatus {
  switch (object) {
    case 0:
    case 'AUDIT_STATUS_NONE':
      return AuditStatus.AUDIT_STATUS_NONE;
    case 1:
    case 'AUDIT_STATUS_APPROVED':
      return AuditStatus.AUDIT_STATUS_APPROVED;
    case 2:
    case 'AUDIT_STATUS_REJECTED':
      return AuditStatus.AUDIT_STATUS_REJECTED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AuditStatus.UNRECOGNIZED;
  }
}

/** 动态附件类型 */
export enum AttachmentType {
  /** CONTENT_TYPE_NONE - 纯文本(没有附件) */
  CONTENT_TYPE_NONE = 0,
  /** CONTENT_TYPE_TEXT_IMAGE - 纯图片 */
  CONTENT_TYPE_TEXT_IMAGE = 10,
  /** CONTENT_TYPE_TEXT_VIDEO - 纯视频 */
  CONTENT_TYPE_TEXT_VIDEO = 20,
  /** CONTENT_TYPE_TEXT_LINK - 纯链接 */
  CONTENT_TYPE_TEXT_LINK = 30,
  UNRECOGNIZED = -1
}

export function attachmentTypeFromJSON(object: any): AttachmentType {
  switch (object) {
    case 0:
    case 'CONTENT_TYPE_NONE':
      return AttachmentType.CONTENT_TYPE_NONE;
    case 10:
    case 'CONTENT_TYPE_TEXT_IMAGE':
      return AttachmentType.CONTENT_TYPE_TEXT_IMAGE;
    case 20:
    case 'CONTENT_TYPE_TEXT_VIDEO':
      return AttachmentType.CONTENT_TYPE_TEXT_VIDEO;
    case 30:
    case 'CONTENT_TYPE_TEXT_LINK':
      return AttachmentType.CONTENT_TYPE_TEXT_LINK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AttachmentType.UNRECOGNIZED;
  }
}

/** 图片类型 */
export enum ImageType {
  IMAGE_TYPE_NONE = 0,
  /** IMAGE_TYPE_GENERAL - 一般图片: jpg / png / webp / ... */
  IMAGE_TYPE_GENERAL = 10,
  /** IMAGE_TYPE_GIF - gif图片 */
  IMAGE_TYPE_GIF = 20,
  /** IMAGE_TYPE_LIVE - 实况图片 */
  IMAGE_TYPE_LIVE = 30,
  UNRECOGNIZED = -1
}

export function imageTypeFromJSON(object: any): ImageType {
  switch (object) {
    case 0:
    case 'IMAGE_TYPE_NONE':
      return ImageType.IMAGE_TYPE_NONE;
    case 10:
    case 'IMAGE_TYPE_GENERAL':
      return ImageType.IMAGE_TYPE_GENERAL;
    case 20:
    case 'IMAGE_TYPE_GIF':
      return ImageType.IMAGE_TYPE_GIF;
    case 30:
    case 'IMAGE_TYPE_LIVE':
      return ImageType.IMAGE_TYPE_LIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ImageType.UNRECOGNIZED;
  }
}

/** 视频类型 */
export enum VideoType {
  VIDEO_TYPE_NONE = 0,
  /** VIDEO_TYPE_MP4 - MP4 */
  VIDEO_TYPE_MP4 = 10,
  UNRECOGNIZED = -1
}

export function videoTypeFromJSON(object: any): VideoType {
  switch (object) {
    case 0:
    case 'VIDEO_TYPE_NONE':
      return VideoType.VIDEO_TYPE_NONE;
    case 10:
    case 'VIDEO_TYPE_MP4':
      return VideoType.VIDEO_TYPE_MP4;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return VideoType.UNRECOGNIZED;
  }
}

/** 链接类型 */
export enum LinkType {
  LINK_TYPE_NONE = 0,
  /** LINK_TYPE_GENERAL - 一般链接: http / 应用内跳转 / ... */
  LINK_TYPE_GENERAL = 10,
  UNRECOGNIZED = -1
}

export function linkTypeFromJSON(object: any): LinkType {
  switch (object) {
    case 0:
    case 'LINK_TYPE_NONE':
      return LinkType.LINK_TYPE_NONE;
    case 10:
    case 'LINK_TYPE_GENERAL':
      return LinkType.LINK_TYPE_GENERAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LinkType.UNRECOGNIZED;
  }
}

/** 动态评论类型 */
export enum CommentType {
  COMMENT_TYPE_NONE = 0,
  /** COMMENT_TYPE_TEXT - 纯文本 */
  COMMENT_TYPE_TEXT = 10,
  UNRECOGNIZED = -1
}

export function commentTypeFromJSON(object: any): CommentType {
  switch (object) {
    case 0:
    case 'COMMENT_TYPE_NONE':
      return CommentType.COMMENT_TYPE_NONE;
    case 10:
    case 'COMMENT_TYPE_TEXT':
      return CommentType.COMMENT_TYPE_TEXT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CommentType.UNRECOGNIZED;
  }
}

/** 动态 */
export interface Moment {
  /** 动态ID */
  moment_id: number;
  /** 发布时间戳 */
  publish_at: number;
  /** 发布者UID */
  uid: number;
  /** 客户端维护版本号，如果新版本不兼容旧版本，则递增版本号，渲染时如果当前维护版本小于该版本号则过滤或提示升级 */
  version: number;
  /** 审核结果 */
  audit_result: AuditResult | undefined;
  /** 动态文案 */
  text: Text | undefined;
  /** 标签 */
  tags: Tag[];
  /** 位置 */
  location: Location | undefined;
  /** 动态附件(微信方案, 不允许一个动态里面既包含图片又包含视频.) */
  attachments: Attachments | undefined;
  /** repeated Attachment attachments = 30; // 动态附件(微博方案, 允许一个动态里面既包含图片又包含视频, 而且可以随意排列组合) */
  stats: MomentStats | undefined;
  /** 透传上下文, 由客户端写入, 用于业务客户端之间或业务客户端和服务端进行能力拓展, 中台只负责存储和传输并不关心其内容及结构. */
  context: BizData | undefined;
  /** 拓展信息, 由业务后端写入, 用于业务后端填充此动态的发布者相关信息, 中台只负责存储和传输并不关心其内容及结构. */
  expand: BizData | undefined;
  /** 自定义属性, 因为这块会被中台服务端和 OMS 端读取, 所以定义成 map<string, string> 结构便于使用. */
  attribute: { [key: string]: string };
}

export interface Moment_AttributeEntry {
  key: string;
  value: string;
}

/** 审核结果 */
export interface AuditResult {
  status: AuditStatus;
  labels: string[];
}

/** 文本 */
export interface Text {
  /** 纯文本 */
  plain: string;
  /** 富文本（Markdown等） */
  rich: string;
  /** 客户端维护版本号，如果新版本不兼容旧版本，则递增版本号，渲染时如果当前维护版本小于该版本号则展示纯文本 */
  version: number;
}

/** 标签 */
export interface Tag {
  tag: string;
}

/** 附件(微信方案) */
export interface Attachments {
  /** 附件类型, 不同的内容类型决定着下面的字段是否有值, 例如 "图片" 的时候就只有 images 两字段有值. */
  type: AttachmentType;
  /** 图片列表, 客户端根据图片数量决定排列布局形式. */
  images: Image[];
  /** 视频, 设计为列表是方便后续拓展成多视频的情况. */
  videos: Video[];
  /** 链接, 设计为列表是方便后续拓展成多链接的情况. */
  links: Link[];
}

/** 附件(微博方案) */
export interface Attachment {
  /** 附件类型 */
  type: AttachmentType;
  /** 图片 */
  image?: Image | undefined;
  /** 视频 */
  video?: Video | undefined;
  /** 链接 */
  link?: Link | undefined;
}

/** 图片 */
export interface Image {
  /** 图片类型 */
  type: ImageType;
  /** 图片地址 */
  url: string;
  /** 图片宽(像素) */
  width: number;
  /** 图片高(像素) */
  height: number;
}

/** 视频 */
export interface Video {
  /** 视频类型 */
  type: VideoType;
  /** 视频地址 */
  url: string;
  /** 预览图片地址 */
  preview_url: string;
  /** 视频时长(秒) */
  duration: number;
}

/** 链接 */
export interface Link {
  /** 链接类型 */
  type: LinkType;
  /** 图标 */
  icon: string;
  /** 标题 */
  title: string;
  /** 链接地址 */
  url: string;
}

/** 统计数据 */
export interface MomentStats {
  /** 浏览量 */
  views: number;
  /** 评论数 */
  comments: number;
  /** 点赞数 */
  likes: number;
  /** 收藏数 */
  favorites: number;
  /** 分享数 */
  shares: number;
}

/** 动态评论 */
export interface Comment {
  /** 评论ID */
  comment_id: number;
  /** 评论时间戳 */
  comment_at: number;
  /** 评论者UID */
  uid: number;
  /** 动态ID */
  moment_id: number;
  /** 根评论ID，大于0表示这是在一条评论下的评论 */
  root_comment_id: number;
  /** 对评论的回复也是一条评论, 不过这条评论有一个引用评论ID, 也就是说此值大于0表示这是一条对评论的回复. */
  refer_comment_id: number;
  /** 回复某个评论者的UID, 也就是@人. */
  refer_uid: number;
  /** 客户端维护版本号，如果新版本不兼容旧版本，则递增版本号，渲染时如果当前维护版本小于该版本号则过滤或提示升级 */
  version: number;
  /** 评论内容 */
  text: Text | undefined;
  /** 统计信息 */
  stats: CommentStats | undefined;
  /** 透传上下文, 由客户端写入, 用于业务客户端之间或业务客户端和服务端进行能力拓展, 中台只负责存储和传输并不关心其内容及结构. */
  context: BizData | undefined;
  /** 拓展信息, 由业务后端写入, 用于业务后端填充此动态的发布者相关信息, 中台只负责存储和传输并不关心其内容及结构. */
  expand: BizData | undefined;
}

export interface CommentStats {
  /** 回复数 */
  replies: number;
  /** 点赞数 */
  likes: number;
}

/** 业务数据 */
export interface BizData {
  /** 数据类型 */
  type: BizData_Type;
  /** 数据内容 */
  data: Uint8Array;
}

/** 数据类型 */
export enum BizData_Type {
  BIZ_DATA_TYPE_NONE = 0,
  /** BIZ_DATA_TYPE_JSON - JSON */
  BIZ_DATA_TYPE_JSON = 10,
  /** BIZ_DATA_TYPE_PROTOBUF - Protobuf */
  BIZ_DATA_TYPE_PROTOBUF = 20,
  UNRECOGNIZED = -1
}

export function bizData_TypeFromJSON(object: any): BizData_Type {
  switch (object) {
    case 0:
    case 'BIZ_DATA_TYPE_NONE':
      return BizData_Type.BIZ_DATA_TYPE_NONE;
    case 10:
    case 'BIZ_DATA_TYPE_JSON':
      return BizData_Type.BIZ_DATA_TYPE_JSON;
    case 20:
    case 'BIZ_DATA_TYPE_PROTOBUF':
      return BizData_Type.BIZ_DATA_TYPE_PROTOBUF;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BizData_Type.UNRECOGNIZED;
  }
}

/** 位置 */
export interface Location {
  /** 国家代码，例如 CN */
  country_code: string;
  /** 外显国家名 */
  country: string;
  /** 外显城市名 */
  city: string;
}

function createBaseMoment(): Moment {
  return {
    moment_id: 0,
    publish_at: 0,
    uid: 0,
    version: 0,
    audit_result: undefined,
    text: undefined,
    tags: [],
    location: undefined,
    attachments: undefined,
    stats: undefined,
    context: undefined,
    expand: undefined,
    attribute: {}
  };
}

export const Moment: MessageFns<Moment> = {
  fromJSON(object: any): Moment {
    return {
      moment_id: isSet(object.moment_id) ? globalThis.Number(object.moment_id) : 0,
      publish_at: isSet(object.publish_at) ? globalThis.Number(object.publish_at) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      audit_result: isSet(object.audit_result) ? AuditResult.fromJSON(object.audit_result) : undefined,
      text: isSet(object.text) ? Text.fromJSON(object.text) : undefined,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => Tag.fromJSON(e)) : [],
      location: isSet(object.location) ? Location.fromJSON(object.location) : undefined,
      attachments: isSet(object.attachments) ? Attachments.fromJSON(object.attachments) : undefined,
      stats: isSet(object.stats) ? MomentStats.fromJSON(object.stats) : undefined,
      context: isSet(object.context) ? BizData.fromJSON(object.context) : undefined,
      expand: isSet(object.expand) ? BizData.fromJSON(object.expand) : undefined,
      attribute: isObject(object.attribute)
        ? Object.entries(object.attribute).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<Moment>, I>>(base?: I): Moment {
    return Moment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Moment>, I>>(object: I): Moment {
    const message = createBaseMoment();
    message.moment_id = object.moment_id ?? 0;
    message.publish_at = object.publish_at ?? 0;
    message.uid = object.uid ?? 0;
    message.version = object.version ?? 0;
    message.audit_result =
      object.audit_result !== undefined && object.audit_result !== null
        ? AuditResult.fromPartial(object.audit_result)
        : undefined;
    message.text = object.text !== undefined && object.text !== null ? Text.fromPartial(object.text) : undefined;
    message.tags = object.tags?.map(e => Tag.fromPartial(e)) || [];
    message.location =
      object.location !== undefined && object.location !== null ? Location.fromPartial(object.location) : undefined;
    message.attachments =
      object.attachments !== undefined && object.attachments !== null
        ? Attachments.fromPartial(object.attachments)
        : undefined;
    message.stats =
      object.stats !== undefined && object.stats !== null ? MomentStats.fromPartial(object.stats) : undefined;
    message.context =
      object.context !== undefined && object.context !== null ? BizData.fromPartial(object.context) : undefined;
    message.expand =
      object.expand !== undefined && object.expand !== null ? BizData.fromPartial(object.expand) : undefined;
    message.attribute = Object.entries(object.attribute ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseMoment_AttributeEntry(): Moment_AttributeEntry {
  return { key: '', value: '' };
}

export const Moment_AttributeEntry: MessageFns<Moment_AttributeEntry> = {
  fromJSON(object: any): Moment_AttributeEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Moment_AttributeEntry>, I>>(base?: I): Moment_AttributeEntry {
    return Moment_AttributeEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Moment_AttributeEntry>, I>>(object: I): Moment_AttributeEntry {
    const message = createBaseMoment_AttributeEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAuditResult(): AuditResult {
  return { status: 0, labels: [] };
}

export const AuditResult: MessageFns<AuditResult> = {
  fromJSON(object: any): AuditResult {
    return {
      status: isSet(object.status) ? auditStatusFromJSON(object.status) : 0,
      labels: globalThis.Array.isArray(object?.labels) ? object.labels.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<AuditResult>, I>>(base?: I): AuditResult {
    return AuditResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditResult>, I>>(object: I): AuditResult {
    const message = createBaseAuditResult();
    message.status = object.status ?? 0;
    message.labels = object.labels?.map(e => e) || [];
    return message;
  }
};

function createBaseText(): Text {
  return { plain: '', rich: '', version: 0 };
}

export const Text: MessageFns<Text> = {
  fromJSON(object: any): Text {
    return {
      plain: isSet(object.plain) ? globalThis.String(object.plain) : '',
      rich: isSet(object.rich) ? globalThis.String(object.rich) : '',
      version: isSet(object.version) ? globalThis.Number(object.version) : 0
    };
  },

  create<I extends Exact<DeepPartial<Text>, I>>(base?: I): Text {
    return Text.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Text>, I>>(object: I): Text {
    const message = createBaseText();
    message.plain = object.plain ?? '';
    message.rich = object.rich ?? '';
    message.version = object.version ?? 0;
    return message;
  }
};

function createBaseTag(): Tag {
  return { tag: '' };
}

export const Tag: MessageFns<Tag> = {
  fromJSON(object: any): Tag {
    return { tag: isSet(object.tag) ? globalThis.String(object.tag) : '' };
  },

  create<I extends Exact<DeepPartial<Tag>, I>>(base?: I): Tag {
    return Tag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Tag>, I>>(object: I): Tag {
    const message = createBaseTag();
    message.tag = object.tag ?? '';
    return message;
  }
};

function createBaseAttachments(): Attachments {
  return { type: 0, images: [], videos: [], links: [] };
}

export const Attachments: MessageFns<Attachments> = {
  fromJSON(object: any): Attachments {
    return {
      type: isSet(object.type) ? attachmentTypeFromJSON(object.type) : 0,
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => Image.fromJSON(e)) : [],
      videos: globalThis.Array.isArray(object?.videos) ? object.videos.map((e: any) => Video.fromJSON(e)) : [],
      links: globalThis.Array.isArray(object?.links) ? object.links.map((e: any) => Link.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Attachments>, I>>(base?: I): Attachments {
    return Attachments.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Attachments>, I>>(object: I): Attachments {
    const message = createBaseAttachments();
    message.type = object.type ?? 0;
    message.images = object.images?.map(e => Image.fromPartial(e)) || [];
    message.videos = object.videos?.map(e => Video.fromPartial(e)) || [];
    message.links = object.links?.map(e => Link.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAttachment(): Attachment {
  return { type: 0, image: undefined, video: undefined, link: undefined };
}

export const Attachment: MessageFns<Attachment> = {
  fromJSON(object: any): Attachment {
    return {
      type: isSet(object.type) ? attachmentTypeFromJSON(object.type) : 0,
      image: isSet(object.image) ? Image.fromJSON(object.image) : undefined,
      video: isSet(object.video) ? Video.fromJSON(object.video) : undefined,
      link: isSet(object.link) ? Link.fromJSON(object.link) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Attachment>, I>>(base?: I): Attachment {
    return Attachment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Attachment>, I>>(object: I): Attachment {
    const message = createBaseAttachment();
    message.type = object.type ?? 0;
    message.image = object.image !== undefined && object.image !== null ? Image.fromPartial(object.image) : undefined;
    message.video = object.video !== undefined && object.video !== null ? Video.fromPartial(object.video) : undefined;
    message.link = object.link !== undefined && object.link !== null ? Link.fromPartial(object.link) : undefined;
    return message;
  }
};

function createBaseImage(): Image {
  return { type: 0, url: '', width: 0, height: 0 };
}

export const Image: MessageFns<Image> = {
  fromJSON(object: any): Image {
    return {
      type: isSet(object.type) ? imageTypeFromJSON(object.type) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0
    };
  },

  create<I extends Exact<DeepPartial<Image>, I>>(base?: I): Image {
    return Image.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Image>, I>>(object: I): Image {
    const message = createBaseImage();
    message.type = object.type ?? 0;
    message.url = object.url ?? '';
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    return message;
  }
};

function createBaseVideo(): Video {
  return { type: 0, url: '', preview_url: '', duration: 0 };
}

export const Video: MessageFns<Video> = {
  fromJSON(object: any): Video {
    return {
      type: isSet(object.type) ? videoTypeFromJSON(object.type) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      preview_url: isSet(object.preview_url) ? globalThis.String(object.preview_url) : '',
      duration: isSet(object.duration) ? globalThis.Number(object.duration) : 0
    };
  },

  create<I extends Exact<DeepPartial<Video>, I>>(base?: I): Video {
    return Video.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Video>, I>>(object: I): Video {
    const message = createBaseVideo();
    message.type = object.type ?? 0;
    message.url = object.url ?? '';
    message.preview_url = object.preview_url ?? '';
    message.duration = object.duration ?? 0;
    return message;
  }
};

function createBaseLink(): Link {
  return { type: 0, icon: '', title: '', url: '' };
}

export const Link: MessageFns<Link> = {
  fromJSON(object: any): Link {
    return {
      type: isSet(object.type) ? linkTypeFromJSON(object.type) : 0,
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      url: isSet(object.url) ? globalThis.String(object.url) : ''
    };
  },

  create<I extends Exact<DeepPartial<Link>, I>>(base?: I): Link {
    return Link.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Link>, I>>(object: I): Link {
    const message = createBaseLink();
    message.type = object.type ?? 0;
    message.icon = object.icon ?? '';
    message.title = object.title ?? '';
    message.url = object.url ?? '';
    return message;
  }
};

function createBaseMomentStats(): MomentStats {
  return { views: 0, comments: 0, likes: 0, favorites: 0, shares: 0 };
}

export const MomentStats: MessageFns<MomentStats> = {
  fromJSON(object: any): MomentStats {
    return {
      views: isSet(object.views) ? globalThis.Number(object.views) : 0,
      comments: isSet(object.comments) ? globalThis.Number(object.comments) : 0,
      likes: isSet(object.likes) ? globalThis.Number(object.likes) : 0,
      favorites: isSet(object.favorites) ? globalThis.Number(object.favorites) : 0,
      shares: isSet(object.shares) ? globalThis.Number(object.shares) : 0
    };
  },

  create<I extends Exact<DeepPartial<MomentStats>, I>>(base?: I): MomentStats {
    return MomentStats.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MomentStats>, I>>(object: I): MomentStats {
    const message = createBaseMomentStats();
    message.views = object.views ?? 0;
    message.comments = object.comments ?? 0;
    message.likes = object.likes ?? 0;
    message.favorites = object.favorites ?? 0;
    message.shares = object.shares ?? 0;
    return message;
  }
};

function createBaseComment(): Comment {
  return {
    comment_id: 0,
    comment_at: 0,
    uid: 0,
    moment_id: 0,
    root_comment_id: 0,
    refer_comment_id: 0,
    refer_uid: 0,
    version: 0,
    text: undefined,
    stats: undefined,
    context: undefined,
    expand: undefined
  };
}

export const Comment: MessageFns<Comment> = {
  fromJSON(object: any): Comment {
    return {
      comment_id: isSet(object.comment_id) ? globalThis.Number(object.comment_id) : 0,
      comment_at: isSet(object.comment_at) ? globalThis.Number(object.comment_at) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      moment_id: isSet(object.moment_id) ? globalThis.Number(object.moment_id) : 0,
      root_comment_id: isSet(object.root_comment_id) ? globalThis.Number(object.root_comment_id) : 0,
      refer_comment_id: isSet(object.refer_comment_id) ? globalThis.Number(object.refer_comment_id) : 0,
      refer_uid: isSet(object.refer_uid) ? globalThis.Number(object.refer_uid) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      text: isSet(object.text) ? Text.fromJSON(object.text) : undefined,
      stats: isSet(object.stats) ? CommentStats.fromJSON(object.stats) : undefined,
      context: isSet(object.context) ? BizData.fromJSON(object.context) : undefined,
      expand: isSet(object.expand) ? BizData.fromJSON(object.expand) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Comment>, I>>(base?: I): Comment {
    return Comment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Comment>, I>>(object: I): Comment {
    const message = createBaseComment();
    message.comment_id = object.comment_id ?? 0;
    message.comment_at = object.comment_at ?? 0;
    message.uid = object.uid ?? 0;
    message.moment_id = object.moment_id ?? 0;
    message.root_comment_id = object.root_comment_id ?? 0;
    message.refer_comment_id = object.refer_comment_id ?? 0;
    message.refer_uid = object.refer_uid ?? 0;
    message.version = object.version ?? 0;
    message.text = object.text !== undefined && object.text !== null ? Text.fromPartial(object.text) : undefined;
    message.stats =
      object.stats !== undefined && object.stats !== null ? CommentStats.fromPartial(object.stats) : undefined;
    message.context =
      object.context !== undefined && object.context !== null ? BizData.fromPartial(object.context) : undefined;
    message.expand =
      object.expand !== undefined && object.expand !== null ? BizData.fromPartial(object.expand) : undefined;
    return message;
  }
};

function createBaseCommentStats(): CommentStats {
  return { replies: 0, likes: 0 };
}

export const CommentStats: MessageFns<CommentStats> = {
  fromJSON(object: any): CommentStats {
    return {
      replies: isSet(object.replies) ? globalThis.Number(object.replies) : 0,
      likes: isSet(object.likes) ? globalThis.Number(object.likes) : 0
    };
  },

  create<I extends Exact<DeepPartial<CommentStats>, I>>(base?: I): CommentStats {
    return CommentStats.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommentStats>, I>>(object: I): CommentStats {
    const message = createBaseCommentStats();
    message.replies = object.replies ?? 0;
    message.likes = object.likes ?? 0;
    return message;
  }
};

function createBaseBizData(): BizData {
  return { type: 0, data: new Uint8Array(0) };
}

export const BizData: MessageFns<BizData> = {
  fromJSON(object: any): BizData {
    return {
      type: isSet(object.type) ? bizData_TypeFromJSON(object.type) : 0,
      data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<BizData>, I>>(base?: I): BizData {
    return BizData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizData>, I>>(object: I): BizData {
    const message = createBaseBizData();
    message.type = object.type ?? 0;
    message.data = object.data ?? new Uint8Array(0);
    return message;
  }
};

function createBaseLocation(): Location {
  return { country_code: '', country: '', city: '' };
}

export const Location: MessageFns<Location> = {
  fromJSON(object: any): Location {
    return {
      country_code: isSet(object.country_code) ? globalThis.String(object.country_code) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      city: isSet(object.city) ? globalThis.String(object.city) : ''
    };
  },

  create<I extends Exact<DeepPartial<Location>, I>>(base?: I): Location {
    return Location.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Location>, I>>(object: I): Location {
    const message = createBaseLocation();
    message.country_code = object.country_code ?? '';
    message.country = object.country ?? '';
    message.city = object.city ?? '';
    return message;
  }
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
