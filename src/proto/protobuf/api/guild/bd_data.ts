// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/guild/bd_data.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.guild';

/** 请求：获取BD旗下公会概览数据 */
export interface GetBdGuildOverviewReq {
  /** 查询开始日期，格式：yyyyMMdd */
  start_date: number;
  /** 查询结束日期，格式：yyyyMMdd (对于日维度，start_date和end_date通常相同；对于周维度，表示周的结束日期) */
  end_date: number;
}

/** 响应：BD旗下公会概览数据 */
export interface GetBdGuildOverviewRsp {
  /** Darling 业务的详细指标 */
  darling_data?: BdGuildOverviewDarling | undefined;
}

/** Darling 业务的详细指标 */
export interface BdGuildOverviewDarling {
  /** 旗下总公会数最近更新时间戳 */
  total_guild_update_time: number;
  /** 旗下总公会数 */
  total_guild_count: number;
  /** 旗下公会总主播数 */
  total_anchor_count: number;
  /** 旗下公会总活跃主播数 */
  total_active_anchor_count: number;
}

/** 请求：获取BD旗下公会汇总数据 */
export interface GetBdGuildSummaryReq {
  /** 查询开始日期，格式：yyyyMMdd */
  start_date: number;
  /** 查询结束日期，格式：yyyyMMdd (对于日维度，start_date和end_date通常相同；对于周维度，表示周的结束日期) */
  end_date: number;
}

/** 响应：BD旗下公会汇总数据 */
export interface GetBdGuildSummaryRsp {
  /** Darling 业务的详细指标 */
  darling_data?: BdGuildSummaryDarling | undefined;
}

/** Darling 业务的详细指标 */
export interface BdGuildSummaryDarling {
  /** 最近更新时间戳 */
  update_time: number;
  /** 旗下公会的新增主播数 */
  new_anchor_cnt: number;
  /** 旗下公会的新增活跃主播数 */
  new_active_anchor_cnt: number;
  /** 旗下公会的语音房礼物总收入 (钻石数) */
  gift_income: number;
  /** 旗下公会的聊天总收入 (钻石数) */
  message_income: number;
  /** 旗下公会的视频总收入 (钻石数) */
  video_income: number;
}

/** 请求：获取BD旗下每个公会的汇总数据 */
export interface GetBdGuildListSummaryReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 查询开始日期，格式：yyyyMMdd */
  start_date: number;
  /** 查询结束日期，格式：yyyyMMdd (对于日维度，start_date和end_date通常相同；对于周维度，表示周的结束日期) */
  end_date: number;
  /** 公会ID列表 */
  guild_ids: number[];
}

/** 响应：BD旗下每个公会的汇总数据 */
export interface GetBdGuildListSummaryRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** Darling 业务的详细指标列表封装 */
  darling_data_list?: BdGuildListSummaryDarlingList | undefined;
}

/** Darling业务公会汇总数据列表的封装 */
export interface BdGuildListSummaryDarlingList {
  /** Darling 业务的详细指标列表 */
  list: BdGuildListSummaryDarling[];
}

/** darling单个公会汇总数据结构 */
export interface BdGuildListSummaryDarling {
  /** 公会ID */
  guild_id: number;
  /** 公会名称 */
  guild_name: string;
  /** 公会LOGO */
  guild_logo: string;
  /** 新增主播数 */
  new_anchor_cnt: number;
  /** 新增活跃主播数 */
  new_active_anchor_cnt: number;
  /** 语音房礼物收入（钻石数） */
  gift_income: number;
  /** 聊天总收入（钻石数） */
  message_income: number;
  /** 视频总收入（钻石数） */
  video_income: number;
}

/** 请求：获取单个公会汇总数据 */
export interface GetSingleGuildSummaryReq {
  /** 公会ID */
  guild_id: number;
  /** 查询开始日期，格式：yyyyMMdd */
  start_date: number;
  /** 查询结束日期，格式：yyyyMMdd (对于日维度，start_date和end_date通常相同；对于周维度，表示周的结束日期) */
  end_date: number;
}

/** 响应：单个公会汇总数据 */
export interface GetSingleGuildSummaryRsp {
  /** Darling 业务的详细指标 */
  darling_data?: SingleGuildSummaryDarling | undefined;
}

/** darling单个公会汇总数据结构 */
export interface SingleGuildSummaryDarling {
  /** 主播数 */
  anchor_cnt: number;
  /** 活跃主播数 */
  active_anchor_cnt: number;
  /** 语音房礼物收入（钻石数） */
  gift_income: number;
  /** 聊天总收入（钻石数） */
  message_income: number;
  /** 视频总收入（钻石数） */
  video_income: number;
}

function createBaseGetBdGuildOverviewReq(): GetBdGuildOverviewReq {
  return { start_date: 0, end_date: 0 };
}

export const GetBdGuildOverviewReq: MessageFns<GetBdGuildOverviewReq> = {
  fromJSON(object: any): GetBdGuildOverviewReq {
    return {
      start_date: isSet(object.start_date) ? globalThis.Number(object.start_date) : 0,
      end_date: isSet(object.end_date) ? globalThis.Number(object.end_date) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildOverviewReq>, I>>(base?: I): GetBdGuildOverviewReq {
    return GetBdGuildOverviewReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildOverviewReq>, I>>(object: I): GetBdGuildOverviewReq {
    const message = createBaseGetBdGuildOverviewReq();
    message.start_date = object.start_date ?? 0;
    message.end_date = object.end_date ?? 0;
    return message;
  }
};

function createBaseGetBdGuildOverviewRsp(): GetBdGuildOverviewRsp {
  return { darling_data: undefined };
}

export const GetBdGuildOverviewRsp: MessageFns<GetBdGuildOverviewRsp> = {
  fromJSON(object: any): GetBdGuildOverviewRsp {
    return {
      darling_data: isSet(object.darling_data) ? BdGuildOverviewDarling.fromJSON(object.darling_data) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildOverviewRsp>, I>>(base?: I): GetBdGuildOverviewRsp {
    return GetBdGuildOverviewRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildOverviewRsp>, I>>(object: I): GetBdGuildOverviewRsp {
    const message = createBaseGetBdGuildOverviewRsp();
    message.darling_data =
      object.darling_data !== undefined && object.darling_data !== null
        ? BdGuildOverviewDarling.fromPartial(object.darling_data)
        : undefined;
    return message;
  }
};

function createBaseBdGuildOverviewDarling(): BdGuildOverviewDarling {
  return { total_guild_update_time: 0, total_guild_count: 0, total_anchor_count: 0, total_active_anchor_count: 0 };
}

export const BdGuildOverviewDarling: MessageFns<BdGuildOverviewDarling> = {
  fromJSON(object: any): BdGuildOverviewDarling {
    return {
      total_guild_update_time: isSet(object.total_guild_update_time)
        ? globalThis.Number(object.total_guild_update_time)
        : 0,
      total_guild_count: isSet(object.total_guild_count) ? globalThis.Number(object.total_guild_count) : 0,
      total_anchor_count: isSet(object.total_anchor_count) ? globalThis.Number(object.total_anchor_count) : 0,
      total_active_anchor_count: isSet(object.total_active_anchor_count)
        ? globalThis.Number(object.total_active_anchor_count)
        : 0
    };
  },

  create<I extends Exact<DeepPartial<BdGuildOverviewDarling>, I>>(base?: I): BdGuildOverviewDarling {
    return BdGuildOverviewDarling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BdGuildOverviewDarling>, I>>(object: I): BdGuildOverviewDarling {
    const message = createBaseBdGuildOverviewDarling();
    message.total_guild_update_time = object.total_guild_update_time ?? 0;
    message.total_guild_count = object.total_guild_count ?? 0;
    message.total_anchor_count = object.total_anchor_count ?? 0;
    message.total_active_anchor_count = object.total_active_anchor_count ?? 0;
    return message;
  }
};

function createBaseGetBdGuildSummaryReq(): GetBdGuildSummaryReq {
  return { start_date: 0, end_date: 0 };
}

export const GetBdGuildSummaryReq: MessageFns<GetBdGuildSummaryReq> = {
  fromJSON(object: any): GetBdGuildSummaryReq {
    return {
      start_date: isSet(object.start_date) ? globalThis.Number(object.start_date) : 0,
      end_date: isSet(object.end_date) ? globalThis.Number(object.end_date) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildSummaryReq>, I>>(base?: I): GetBdGuildSummaryReq {
    return GetBdGuildSummaryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildSummaryReq>, I>>(object: I): GetBdGuildSummaryReq {
    const message = createBaseGetBdGuildSummaryReq();
    message.start_date = object.start_date ?? 0;
    message.end_date = object.end_date ?? 0;
    return message;
  }
};

function createBaseGetBdGuildSummaryRsp(): GetBdGuildSummaryRsp {
  return { darling_data: undefined };
}

export const GetBdGuildSummaryRsp: MessageFns<GetBdGuildSummaryRsp> = {
  fromJSON(object: any): GetBdGuildSummaryRsp {
    return {
      darling_data: isSet(object.darling_data) ? BdGuildSummaryDarling.fromJSON(object.darling_data) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildSummaryRsp>, I>>(base?: I): GetBdGuildSummaryRsp {
    return GetBdGuildSummaryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildSummaryRsp>, I>>(object: I): GetBdGuildSummaryRsp {
    const message = createBaseGetBdGuildSummaryRsp();
    message.darling_data =
      object.darling_data !== undefined && object.darling_data !== null
        ? BdGuildSummaryDarling.fromPartial(object.darling_data)
        : undefined;
    return message;
  }
};

function createBaseBdGuildSummaryDarling(): BdGuildSummaryDarling {
  return {
    update_time: 0,
    new_anchor_cnt: 0,
    new_active_anchor_cnt: 0,
    gift_income: 0,
    message_income: 0,
    video_income: 0
  };
}

export const BdGuildSummaryDarling: MessageFns<BdGuildSummaryDarling> = {
  fromJSON(object: any): BdGuildSummaryDarling {
    return {
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0,
      new_anchor_cnt: isSet(object.new_anchor_cnt) ? globalThis.Number(object.new_anchor_cnt) : 0,
      new_active_anchor_cnt: isSet(object.new_active_anchor_cnt) ? globalThis.Number(object.new_active_anchor_cnt) : 0,
      gift_income: isSet(object.gift_income) ? globalThis.Number(object.gift_income) : 0,
      message_income: isSet(object.message_income) ? globalThis.Number(object.message_income) : 0,
      video_income: isSet(object.video_income) ? globalThis.Number(object.video_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<BdGuildSummaryDarling>, I>>(base?: I): BdGuildSummaryDarling {
    return BdGuildSummaryDarling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BdGuildSummaryDarling>, I>>(object: I): BdGuildSummaryDarling {
    const message = createBaseBdGuildSummaryDarling();
    message.update_time = object.update_time ?? 0;
    message.new_anchor_cnt = object.new_anchor_cnt ?? 0;
    message.new_active_anchor_cnt = object.new_active_anchor_cnt ?? 0;
    message.gift_income = object.gift_income ?? 0;
    message.message_income = object.message_income ?? 0;
    message.video_income = object.video_income ?? 0;
    return message;
  }
};

function createBaseGetBdGuildListSummaryReq(): GetBdGuildListSummaryReq {
  return { page: undefined, start_date: 0, end_date: 0, guild_ids: [] };
}

export const GetBdGuildListSummaryReq: MessageFns<GetBdGuildListSummaryReq> = {
  fromJSON(object: any): GetBdGuildListSummaryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      start_date: isSet(object.start_date) ? globalThis.Number(object.start_date) : 0,
      end_date: isSet(object.end_date) ? globalThis.Number(object.end_date) : 0,
      guild_ids: globalThis.Array.isArray(object?.guild_ids)
        ? object.guild_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildListSummaryReq>, I>>(base?: I): GetBdGuildListSummaryReq {
    return GetBdGuildListSummaryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildListSummaryReq>, I>>(object: I): GetBdGuildListSummaryReq {
    const message = createBaseGetBdGuildListSummaryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.start_date = object.start_date ?? 0;
    message.end_date = object.end_date ?? 0;
    message.guild_ids = object.guild_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetBdGuildListSummaryRsp(): GetBdGuildListSummaryRsp {
  return { page: undefined, darling_data_list: undefined };
}

export const GetBdGuildListSummaryRsp: MessageFns<GetBdGuildListSummaryRsp> = {
  fromJSON(object: any): GetBdGuildListSummaryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      darling_data_list: isSet(object.darling_data_list)
        ? BdGuildListSummaryDarlingList.fromJSON(object.darling_data_list)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetBdGuildListSummaryRsp>, I>>(base?: I): GetBdGuildListSummaryRsp {
    return GetBdGuildListSummaryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBdGuildListSummaryRsp>, I>>(object: I): GetBdGuildListSummaryRsp {
    const message = createBaseGetBdGuildListSummaryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.darling_data_list =
      object.darling_data_list !== undefined && object.darling_data_list !== null
        ? BdGuildListSummaryDarlingList.fromPartial(object.darling_data_list)
        : undefined;
    return message;
  }
};

function createBaseBdGuildListSummaryDarlingList(): BdGuildListSummaryDarlingList {
  return { list: [] };
}

export const BdGuildListSummaryDarlingList: MessageFns<BdGuildListSummaryDarlingList> = {
  fromJSON(object: any): BdGuildListSummaryDarlingList {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => BdGuildListSummaryDarling.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BdGuildListSummaryDarlingList>, I>>(base?: I): BdGuildListSummaryDarlingList {
    return BdGuildListSummaryDarlingList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BdGuildListSummaryDarlingList>, I>>(
    object: I
  ): BdGuildListSummaryDarlingList {
    const message = createBaseBdGuildListSummaryDarlingList();
    message.list = object.list?.map(e => BdGuildListSummaryDarling.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBdGuildListSummaryDarling(): BdGuildListSummaryDarling {
  return {
    guild_id: 0,
    guild_name: '',
    guild_logo: '',
    new_anchor_cnt: 0,
    new_active_anchor_cnt: 0,
    gift_income: 0,
    message_income: 0,
    video_income: 0
  };
}

export const BdGuildListSummaryDarling: MessageFns<BdGuildListSummaryDarling> = {
  fromJSON(object: any): BdGuildListSummaryDarling {
    return {
      guild_id: isSet(object.guild_id) ? globalThis.Number(object.guild_id) : 0,
      guild_name: isSet(object.guild_name) ? globalThis.String(object.guild_name) : '',
      guild_logo: isSet(object.guild_logo) ? globalThis.String(object.guild_logo) : '',
      new_anchor_cnt: isSet(object.new_anchor_cnt) ? globalThis.Number(object.new_anchor_cnt) : 0,
      new_active_anchor_cnt: isSet(object.new_active_anchor_cnt) ? globalThis.Number(object.new_active_anchor_cnt) : 0,
      gift_income: isSet(object.gift_income) ? globalThis.Number(object.gift_income) : 0,
      message_income: isSet(object.message_income) ? globalThis.Number(object.message_income) : 0,
      video_income: isSet(object.video_income) ? globalThis.Number(object.video_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<BdGuildListSummaryDarling>, I>>(base?: I): BdGuildListSummaryDarling {
    return BdGuildListSummaryDarling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BdGuildListSummaryDarling>, I>>(object: I): BdGuildListSummaryDarling {
    const message = createBaseBdGuildListSummaryDarling();
    message.guild_id = object.guild_id ?? 0;
    message.guild_name = object.guild_name ?? '';
    message.guild_logo = object.guild_logo ?? '';
    message.new_anchor_cnt = object.new_anchor_cnt ?? 0;
    message.new_active_anchor_cnt = object.new_active_anchor_cnt ?? 0;
    message.gift_income = object.gift_income ?? 0;
    message.message_income = object.message_income ?? 0;
    message.video_income = object.video_income ?? 0;
    return message;
  }
};

function createBaseGetSingleGuildSummaryReq(): GetSingleGuildSummaryReq {
  return { guild_id: 0, start_date: 0, end_date: 0 };
}

export const GetSingleGuildSummaryReq: MessageFns<GetSingleGuildSummaryReq> = {
  fromJSON(object: any): GetSingleGuildSummaryReq {
    return {
      guild_id: isSet(object.guild_id) ? globalThis.Number(object.guild_id) : 0,
      start_date: isSet(object.start_date) ? globalThis.Number(object.start_date) : 0,
      end_date: isSet(object.end_date) ? globalThis.Number(object.end_date) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetSingleGuildSummaryReq>, I>>(base?: I): GetSingleGuildSummaryReq {
    return GetSingleGuildSummaryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSingleGuildSummaryReq>, I>>(object: I): GetSingleGuildSummaryReq {
    const message = createBaseGetSingleGuildSummaryReq();
    message.guild_id = object.guild_id ?? 0;
    message.start_date = object.start_date ?? 0;
    message.end_date = object.end_date ?? 0;
    return message;
  }
};

function createBaseGetSingleGuildSummaryRsp(): GetSingleGuildSummaryRsp {
  return { darling_data: undefined };
}

export const GetSingleGuildSummaryRsp: MessageFns<GetSingleGuildSummaryRsp> = {
  fromJSON(object: any): GetSingleGuildSummaryRsp {
    return {
      darling_data: isSet(object.darling_data) ? SingleGuildSummaryDarling.fromJSON(object.darling_data) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetSingleGuildSummaryRsp>, I>>(base?: I): GetSingleGuildSummaryRsp {
    return GetSingleGuildSummaryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSingleGuildSummaryRsp>, I>>(object: I): GetSingleGuildSummaryRsp {
    const message = createBaseGetSingleGuildSummaryRsp();
    message.darling_data =
      object.darling_data !== undefined && object.darling_data !== null
        ? SingleGuildSummaryDarling.fromPartial(object.darling_data)
        : undefined;
    return message;
  }
};

function createBaseSingleGuildSummaryDarling(): SingleGuildSummaryDarling {
  return { anchor_cnt: 0, active_anchor_cnt: 0, gift_income: 0, message_income: 0, video_income: 0 };
}

export const SingleGuildSummaryDarling: MessageFns<SingleGuildSummaryDarling> = {
  fromJSON(object: any): SingleGuildSummaryDarling {
    return {
      anchor_cnt: isSet(object.anchor_cnt) ? globalThis.Number(object.anchor_cnt) : 0,
      active_anchor_cnt: isSet(object.active_anchor_cnt) ? globalThis.Number(object.active_anchor_cnt) : 0,
      gift_income: isSet(object.gift_income) ? globalThis.Number(object.gift_income) : 0,
      message_income: isSet(object.message_income) ? globalThis.Number(object.message_income) : 0,
      video_income: isSet(object.video_income) ? globalThis.Number(object.video_income) : 0
    };
  },

  create<I extends Exact<DeepPartial<SingleGuildSummaryDarling>, I>>(base?: I): SingleGuildSummaryDarling {
    return SingleGuildSummaryDarling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SingleGuildSummaryDarling>, I>>(object: I): SingleGuildSummaryDarling {
    const message = createBaseSingleGuildSummaryDarling();
    message.anchor_cnt = object.anchor_cnt ?? 0;
    message.active_anchor_cnt = object.active_anchor_cnt ?? 0;
    message.gift_income = object.gift_income ?? 0;
    message.message_income = object.message_income ?? 0;
    message.video_income = object.video_income ?? 0;
    return message;
  }
};

/**
 * ServiceName: rpc.micro.social.guild
 * smicro:spath=gitit.cc/social/components-service/social-guild/handler-api/bddata
 */
export type BdDataApiDefinition = typeof BdDataApiDefinition;
export const BdDataApiDefinition = {
  name: 'BdDataApi',
  fullName: 'comm.api.guild.BdDataApi',
  methods: {
    /** BD查看旗下公会概览信息 */
    getBdGuildOverview: {
      name: 'GetBdGuildOverview',
      requestType: GetBdGuildOverviewReq,
      requestStream: false,
      responseType: GetBdGuildOverviewRsp,
      responseStream: false,
      options: {}
    },
    /** BD查看旗下公会的相关汇总数据 (所有公会合计) */
    getBdGuildSummary: {
      name: 'GetBdGuildSummary',
      requestType: GetBdGuildSummaryReq,
      requestStream: false,
      responseType: GetBdGuildSummaryRsp,
      responseStream: false,
      options: {}
    },
    /** BD查看旗下每个公会的汇总数据 (公会列表) */
    getBdGuildListSummary: {
      name: 'GetBdGuildListSummary',
      requestType: GetBdGuildListSummaryReq,
      requestStream: false,
      responseType: GetBdGuildListSummaryRsp,
      responseStream: false,
      options: {}
    },
    /** BD查看下单个公会的汇总数据 */
    getSingleGuildSummary: {
      name: 'GetSingleGuildSummary',
      requestType: GetSingleGuildSummaryReq,
      requestStream: false,
      responseType: GetSingleGuildSummaryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
