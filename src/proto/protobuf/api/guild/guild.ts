// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/guild/guild.proto

/* eslint-disable */
import { Country } from './common';

export const protobufPackage = 'comm.api.guild';

/** 工会信息 */
export interface Guild {
  /** 公会ID */
  id: number;
  /** 公会名称 */
  name: string;
  /** 公会LOGO */
  logo: string;
  /** 公会宣言 */
  profile: string;
  /** 公会长UID */
  owner_uid: number;
  /** 公会长姓名 */
  owner_name: string;
  /** 公会长国家 */
  owner_country: Country | undefined;
  /** 公会长 whatsapp */
  owner_whatsapp: string;
  /** 公会长手机号 */
  owner_mobile: string;
  /** 公会长邮箱 */
  owner_email: string;
  /** 是否为官方公会 */
  official: boolean;
  /** 公会是否已冻结 */
  frozen: boolean;
  /** 公会冻结时间 */
  frozen_at: number;
  /** 公会创建时间 */
  created_at: number;
  /** 公会更新时间 */
  updated_at: number;
  /** 邀请码 */
  invite_code: string;
  /** 公会业务拓展信息 */
  extra: string;
}

/** 公会拓展信息, 用于反序列化公会的拓展信息 JSON 字符串. */
export interface GuildExtra {}

/** darling 业务的 */
export interface GuildExtra_Darling {
  /** 性取向, 同性: same_sex, 异性: opposite_sex */
  sexual_orientation: string;
  /** 直播类型, 语音房: voice_room, 秀场: show_time, 语音厅: voice_room_hall */
  live_type: string;
}

function createBaseGuild(): Guild {
  return {
    id: 0,
    name: '',
    logo: '',
    profile: '',
    owner_uid: 0,
    owner_name: '',
    owner_country: undefined,
    owner_whatsapp: '',
    owner_mobile: '',
    owner_email: '',
    official: false,
    frozen: false,
    frozen_at: 0,
    created_at: 0,
    updated_at: 0,
    invite_code: '',
    extra: ''
  };
}

export const Guild: MessageFns<Guild> = {
  fromJSON(object: any): Guild {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      logo: isSet(object.logo) ? globalThis.String(object.logo) : '',
      profile: isSet(object.profile) ? globalThis.String(object.profile) : '',
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      owner_name: isSet(object.owner_name) ? globalThis.String(object.owner_name) : '',
      owner_country: isSet(object.owner_country) ? Country.fromJSON(object.owner_country) : undefined,
      owner_whatsapp: isSet(object.owner_whatsapp) ? globalThis.String(object.owner_whatsapp) : '',
      owner_mobile: isSet(object.owner_mobile) ? globalThis.String(object.owner_mobile) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      official: isSet(object.official) ? globalThis.Boolean(object.official) : false,
      frozen: isSet(object.frozen) ? globalThis.Boolean(object.frozen) : false,
      frozen_at: isSet(object.frozen_at) ? globalThis.Number(object.frozen_at) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      invite_code: isSet(object.invite_code) ? globalThis.String(object.invite_code) : '',
      extra: isSet(object.extra) ? globalThis.String(object.extra) : ''
    };
  },

  create<I extends Exact<DeepPartial<Guild>, I>>(base?: I): Guild {
    return Guild.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Guild>, I>>(object: I): Guild {
    const message = createBaseGuild();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.logo = object.logo ?? '';
    message.profile = object.profile ?? '';
    message.owner_uid = object.owner_uid ?? 0;
    message.owner_name = object.owner_name ?? '';
    message.owner_country =
      object.owner_country !== undefined && object.owner_country !== null
        ? Country.fromPartial(object.owner_country)
        : undefined;
    message.owner_whatsapp = object.owner_whatsapp ?? '';
    message.owner_mobile = object.owner_mobile ?? '';
    message.owner_email = object.owner_email ?? '';
    message.official = object.official ?? false;
    message.frozen = object.frozen ?? false;
    message.frozen_at = object.frozen_at ?? 0;
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.invite_code = object.invite_code ?? '';
    message.extra = object.extra ?? '';
    return message;
  }
};

function createBaseGuildExtra(): GuildExtra {
  return {};
}

export const GuildExtra: MessageFns<GuildExtra> = {
  fromJSON(_: any): GuildExtra {
    return {};
  },

  create<I extends Exact<DeepPartial<GuildExtra>, I>>(base?: I): GuildExtra {
    return GuildExtra.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GuildExtra>, I>>(_: I): GuildExtra {
    const message = createBaseGuildExtra();
    return message;
  }
};

function createBaseGuildExtra_Darling(): GuildExtra_Darling {
  return { sexual_orientation: '', live_type: '' };
}

export const GuildExtra_Darling: MessageFns<GuildExtra_Darling> = {
  fromJSON(object: any): GuildExtra_Darling {
    return {
      sexual_orientation: isSet(object.sexual_orientation) ? globalThis.String(object.sexual_orientation) : '',
      live_type: isSet(object.live_type) ? globalThis.String(object.live_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<GuildExtra_Darling>, I>>(base?: I): GuildExtra_Darling {
    return GuildExtra_Darling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GuildExtra_Darling>, I>>(object: I): GuildExtra_Darling {
    const message = createBaseGuildExtra_Darling();
    message.sexual_orientation = object.sexual_orientation ?? '';
    message.live_type = object.live_type ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
