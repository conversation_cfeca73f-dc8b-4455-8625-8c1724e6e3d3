// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/guild/common.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.guild';

/** 国家信息 */
export interface Country {
  /** 国家ID */
  country_id: number;
  /** 国家简码 */
  country_abbr: string;
  /** 国旗图片 */
  image: string;
  /** 国家名称, 国际化, 目前包含 AR, EN */
  country_name: { [key: string]: string };
}

export interface Country_CountryNameEntry {
  key: string;
  value: string;
}

function createBaseCountry(): Country {
  return { country_id: 0, country_abbr: '', image: '', country_name: {} };
}

export const Country: MessageFns<Country> = {
  fromJSON(object: any): Country {
    return {
      country_id: isSet(object.country_id) ? globalThis.Number(object.country_id) : 0,
      country_abbr: isSet(object.country_abbr) ? globalThis.String(object.country_abbr) : '',
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      country_name: isObject(object.country_name)
        ? Object.entries(object.country_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<Country>, I>>(base?: I): Country {
    return Country.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Country>, I>>(object: I): Country {
    const message = createBaseCountry();
    message.country_id = object.country_id ?? 0;
    message.country_abbr = object.country_abbr ?? '';
    message.image = object.image ?? '';
    message.country_name = Object.entries(object.country_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCountry_CountryNameEntry(): Country_CountryNameEntry {
  return { key: '', value: '' };
}

export const Country_CountryNameEntry: MessageFns<Country_CountryNameEntry> = {
  fromJSON(object: any): Country_CountryNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Country_CountryNameEntry>, I>>(base?: I): Country_CountryNameEntry {
    return Country_CountryNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Country_CountryNameEntry>, I>>(object: I): Country_CountryNameEntry {
    const message = createBaseCountry_CountryNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
