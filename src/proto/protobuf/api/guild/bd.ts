// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/guild/bd.proto

/* eslint-disable */
import { UserInfo } from '../adapter/model';
import { Page } from '../common/common';
import { Guild } from './guild';

export const protobufPackage = 'comm.api.guild';

/** BD身份类型 */
export enum BDType {
  BD_TYPE_NONE = 0,
  /** BD_TYPE_GUILD - 管理公会 */
  BD_TYPE_GUILD = 10,
  /** BD_TYPE_TOP_PAYER - 管理大R用户 */
  BD_TYPE_TOP_PAYER = 20,
  UNRECOGNIZED = -1
}

export function bDTypeFromJSON(object: any): BDType {
  switch (object) {
    case 0:
    case 'BD_TYPE_NONE':
      return BDType.BD_TYPE_NONE;
    case 10:
    case 'BD_TYPE_GUILD':
      return BDType.BD_TYPE_GUILD;
    case 20:
    case 'BD_TYPE_TOP_PAYER':
      return BDType.BD_TYPE_TOP_PAYER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BDType.UNRECOGNIZED;
  }
}

/** 创建公会 */
export interface CreateGuildReq {
  /** 公会名称 */
  name: string;
  /** 公会LOGO */
  logo: string;
  /** 公会长 UID/ShowID */
  owner_show_uid: string;
  /** 公会长 所属国家 */
  owner_country_id: number;
  /** 公会长 whatsapp */
  owner_whatsapp: string;
  /** 公会业务拓展信息json,对应数据库表中的 extra 字段,每个业务都有自己的结构体,结构体定义在：GuildExtra */
  extra: string;
}

export interface CreateGuildRsp {
  /** 新创建的公会 ID */
  guild_id: number;
}

export interface ListGuildReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 公会ID列表 */
  guild_ids: number[];
}

export interface ListGuildRsp {
  /** 分页参数 */
  page: Page | undefined;
  guilds: BdGuild[];
}

export interface BdGuild {
  /** 公会信息 */
  guild: Guild | undefined;
  /** 公会长信息 */
  owner_user_info: UserInfo | undefined;
}

function createBaseCreateGuildReq(): CreateGuildReq {
  return { name: '', logo: '', owner_show_uid: '', owner_country_id: 0, owner_whatsapp: '', extra: '' };
}

export const CreateGuildReq: MessageFns<CreateGuildReq> = {
  fromJSON(object: any): CreateGuildReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      logo: isSet(object.logo) ? globalThis.String(object.logo) : '',
      owner_show_uid: isSet(object.owner_show_uid) ? globalThis.String(object.owner_show_uid) : '',
      owner_country_id: isSet(object.owner_country_id) ? globalThis.Number(object.owner_country_id) : 0,
      owner_whatsapp: isSet(object.owner_whatsapp) ? globalThis.String(object.owner_whatsapp) : '',
      extra: isSet(object.extra) ? globalThis.String(object.extra) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateGuildReq>, I>>(base?: I): CreateGuildReq {
    return CreateGuildReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateGuildReq>, I>>(object: I): CreateGuildReq {
    const message = createBaseCreateGuildReq();
    message.name = object.name ?? '';
    message.logo = object.logo ?? '';
    message.owner_show_uid = object.owner_show_uid ?? '';
    message.owner_country_id = object.owner_country_id ?? 0;
    message.owner_whatsapp = object.owner_whatsapp ?? '';
    message.extra = object.extra ?? '';
    return message;
  }
};

function createBaseCreateGuildRsp(): CreateGuildRsp {
  return { guild_id: 0 };
}

export const CreateGuildRsp: MessageFns<CreateGuildRsp> = {
  fromJSON(object: any): CreateGuildRsp {
    return { guild_id: isSet(object.guild_id) ? globalThis.Number(object.guild_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateGuildRsp>, I>>(base?: I): CreateGuildRsp {
    return CreateGuildRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateGuildRsp>, I>>(object: I): CreateGuildRsp {
    const message = createBaseCreateGuildRsp();
    message.guild_id = object.guild_id ?? 0;
    return message;
  }
};

function createBaseListGuildReq(): ListGuildReq {
  return { page: undefined, guild_ids: [] };
}

export const ListGuildReq: MessageFns<ListGuildReq> = {
  fromJSON(object: any): ListGuildReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      guild_ids: globalThis.Array.isArray(object?.guild_ids)
        ? object.guild_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGuildReq>, I>>(base?: I): ListGuildReq {
    return ListGuildReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGuildReq>, I>>(object: I): ListGuildReq {
    const message = createBaseListGuildReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.guild_ids = object.guild_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListGuildRsp(): ListGuildRsp {
  return { page: undefined, guilds: [] };
}

export const ListGuildRsp: MessageFns<ListGuildRsp> = {
  fromJSON(object: any): ListGuildRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      guilds: globalThis.Array.isArray(object?.guilds) ? object.guilds.map((e: any) => BdGuild.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListGuildRsp>, I>>(base?: I): ListGuildRsp {
    return ListGuildRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGuildRsp>, I>>(object: I): ListGuildRsp {
    const message = createBaseListGuildRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.guilds = object.guilds?.map(e => BdGuild.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBdGuild(): BdGuild {
  return { guild: undefined, owner_user_info: undefined };
}

export const BdGuild: MessageFns<BdGuild> = {
  fromJSON(object: any): BdGuild {
    return {
      guild: isSet(object.guild) ? Guild.fromJSON(object.guild) : undefined,
      owner_user_info: isSet(object.owner_user_info) ? UserInfo.fromJSON(object.owner_user_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BdGuild>, I>>(base?: I): BdGuild {
    return BdGuild.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BdGuild>, I>>(object: I): BdGuild {
    const message = createBaseBdGuild();
    message.guild = object.guild !== undefined && object.guild !== null ? Guild.fromPartial(object.guild) : undefined;
    message.owner_user_info =
      object.owner_user_info !== undefined && object.owner_user_info !== null
        ? UserInfo.fromPartial(object.owner_user_info)
        : undefined;
    return message;
  }
};

/**
 * ServiceName: rpc.micro.social.guild
 * smicro:spath=gitit.cc/social/components-service/social-guild/handler-api/bd
 */
export type BdApiDefinition = typeof BdApiDefinition;
export const BdApiDefinition = {
  name: 'BdApi',
  fullName: 'comm.api.guild.BdApi',
  methods: {
    /** 创建公会 */
    createGuild: {
      name: 'CreateGuild',
      requestType: CreateGuildReq,
      requestStream: false,
      responseType: CreateGuildRsp,
      responseStream: false,
      options: {}
    },
    /** 查看公会列表 */
    listGuild: {
      name: 'ListGuild',
      requestType: ListGuildReq,
      requestStream: false,
      responseType: ListGuildRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
