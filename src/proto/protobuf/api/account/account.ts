// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/account/account.proto

/* eslint-disable */

export const protobufPackage = 'pbaccount';

/** 退登原因 */
export enum LogoutReason {
  LOGOUT_REASON_NONE = 0,
  /** LOGOUT_REASON_BANNED - 封禁【被动】 */
  LOGOUT_REASON_BANNED = 10,
  /** LOGOUT_REASON_TOKEN_EXPIRED - token 过期【被动】 */
  LOGOUT_REASON_TOKEN_EXPIRED = 20,
  /** LOGOUT_REASON_RTM_KICKED - RTM 踢出【被动】 */
  LOGOUT_REASON_RTM_KICKED = 30,
  /** LOGOUT_REASON_RONGYUN_KICKED - 融云踢出【被动】 */
  LOGOUT_REASON_RONGYUN_KICKED = 40,
  /** LOGOUT_REASON_LOGOUT_ACCOUNT - 用户退出登录【用户主动】 */
  LOGOUT_REASON_LOGOUT_ACCOUNT = 50,
  /** LOGOUT_REASON_DELETE_ACCOUNT - 用户删除账号【用户主动】 */
  LOGOUT_REASON_DELETE_ACCOUNT = 60,
  /** LOGOUT_REASON_WEB_LOGOUT - 前端退出登录？【不确定是什么场景】 */
  LOGOUT_REASON_WEB_LOGOUT = 70,
  /** LOGOUT_REASON_UNDERAGE_CONFIRM - 18岁确认弹窗退出登录【客户端主动踢出】 */
  LOGOUT_REASON_UNDERAGE_CONFIRM = 80,
  /** LOGOUT_REASON_USERINFO_EXIT - 注册用户信息的时候主动退出【用户主动】 */
  LOGOUT_REASON_USERINFO_EXIT = 90,
  UNRECOGNIZED = -1
}

export function logoutReasonFromJSON(object: any): LogoutReason {
  switch (object) {
    case 0:
    case 'LOGOUT_REASON_NONE':
      return LogoutReason.LOGOUT_REASON_NONE;
    case 10:
    case 'LOGOUT_REASON_BANNED':
      return LogoutReason.LOGOUT_REASON_BANNED;
    case 20:
    case 'LOGOUT_REASON_TOKEN_EXPIRED':
      return LogoutReason.LOGOUT_REASON_TOKEN_EXPIRED;
    case 30:
    case 'LOGOUT_REASON_RTM_KICKED':
      return LogoutReason.LOGOUT_REASON_RTM_KICKED;
    case 40:
    case 'LOGOUT_REASON_RONGYUN_KICKED':
      return LogoutReason.LOGOUT_REASON_RONGYUN_KICKED;
    case 50:
    case 'LOGOUT_REASON_LOGOUT_ACCOUNT':
      return LogoutReason.LOGOUT_REASON_LOGOUT_ACCOUNT;
    case 60:
    case 'LOGOUT_REASON_DELETE_ACCOUNT':
      return LogoutReason.LOGOUT_REASON_DELETE_ACCOUNT;
    case 70:
    case 'LOGOUT_REASON_WEB_LOGOUT':
      return LogoutReason.LOGOUT_REASON_WEB_LOGOUT;
    case 80:
    case 'LOGOUT_REASON_UNDERAGE_CONFIRM':
      return LogoutReason.LOGOUT_REASON_UNDERAGE_CONFIRM;
    case 90:
    case 'LOGOUT_REASON_USERINFO_EXIT':
      return LogoutReason.LOGOUT_REASON_USERINFO_EXIT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LogoutReason.UNRECOGNIZED;
  }
}

/** 账号状态 */
export enum AccountStatus {
  /** ACCOUNT_STATUS_NONE - 无状态 */
  ACCOUNT_STATUS_NONE = 0,
  /** ACCOUNT_STATUS_NORMAL - 正常 */
  ACCOUNT_STATUS_NORMAL = 1,
  /** ACCOUNT_STATUS_BAN - 封禁 */
  ACCOUNT_STATUS_BAN = 2,
  /** ACCOUNT_STATUS_FROZEN - 冻结 */
  ACCOUNT_STATUS_FROZEN = 3,
  /** ACCOUNT_STATUS_WITHDRAW - 注销（注销冷静期过后更新为删除） */
  ACCOUNT_STATUS_WITHDRAW = 4,
  /** ACCOUNT_STATUS_DELETE - 删除 */
  ACCOUNT_STATUS_DELETE = 5,
  UNRECOGNIZED = -1
}

export function accountStatusFromJSON(object: any): AccountStatus {
  switch (object) {
    case 0:
    case 'ACCOUNT_STATUS_NONE':
      return AccountStatus.ACCOUNT_STATUS_NONE;
    case 1:
    case 'ACCOUNT_STATUS_NORMAL':
      return AccountStatus.ACCOUNT_STATUS_NORMAL;
    case 2:
    case 'ACCOUNT_STATUS_BAN':
      return AccountStatus.ACCOUNT_STATUS_BAN;
    case 3:
    case 'ACCOUNT_STATUS_FROZEN':
      return AccountStatus.ACCOUNT_STATUS_FROZEN;
    case 4:
    case 'ACCOUNT_STATUS_WITHDRAW':
      return AccountStatus.ACCOUNT_STATUS_WITHDRAW;
    case 5:
    case 'ACCOUNT_STATUS_DELETE':
      return AccountStatus.ACCOUNT_STATUS_DELETE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountStatus.UNRECOGNIZED;
  }
}

/** 登录类型 */
export enum LoginType {
  LOGIN_TYPE_NONE = 0,
  /** LOGIN_TYPE_PHONE - 验证码登录 */
  LOGIN_TYPE_PHONE = 1,
  /** LOGIN_TYPE_FACEBOOK - facebook登录 */
  LOGIN_TYPE_FACEBOOK = 2,
  /** LOGIN_TYPE_SNAPCHAT - snapchat登录 */
  LOGIN_TYPE_SNAPCHAT = 3,
  /** LOGIN_TYPE_GOOGLE - google登录 */
  LOGIN_TYPE_GOOGLE = 4,
  /** LOGIN_TYPE_TWITTER - twitter登录 */
  LOGIN_TYPE_TWITTER = 5,
  /** LOGIN_TYPE_APPLE - apple登录 */
  LOGIN_TYPE_APPLE = 6,
  /** LOGIN_TYPE_PASSWORD - 密码登录(手机号 + 密码) */
  LOGIN_TYPE_PASSWORD = 7,
  /** LOGIN_TYPE_TIKTOK - tiktok登录 */
  LOGIN_TYPE_TIKTOK = 8,
  /** LOGIN_TYPE_DEVICE_ID - 设备信息登录(did) */
  LOGIN_TYPE_DEVICE_ID = 9,
  /** LOGIN_TYPE_ACCOUNT_PASSWORD - 密码登录(账号 + 密码) */
  LOGIN_TYPE_ACCOUNT_PASSWORD = 10,
  UNRECOGNIZED = -1
}

export function loginTypeFromJSON(object: any): LoginType {
  switch (object) {
    case 0:
    case 'LOGIN_TYPE_NONE':
      return LoginType.LOGIN_TYPE_NONE;
    case 1:
    case 'LOGIN_TYPE_PHONE':
      return LoginType.LOGIN_TYPE_PHONE;
    case 2:
    case 'LOGIN_TYPE_FACEBOOK':
      return LoginType.LOGIN_TYPE_FACEBOOK;
    case 3:
    case 'LOGIN_TYPE_SNAPCHAT':
      return LoginType.LOGIN_TYPE_SNAPCHAT;
    case 4:
    case 'LOGIN_TYPE_GOOGLE':
      return LoginType.LOGIN_TYPE_GOOGLE;
    case 5:
    case 'LOGIN_TYPE_TWITTER':
      return LoginType.LOGIN_TYPE_TWITTER;
    case 6:
    case 'LOGIN_TYPE_APPLE':
      return LoginType.LOGIN_TYPE_APPLE;
    case 7:
    case 'LOGIN_TYPE_PASSWORD':
      return LoginType.LOGIN_TYPE_PASSWORD;
    case 8:
    case 'LOGIN_TYPE_TIKTOK':
      return LoginType.LOGIN_TYPE_TIKTOK;
    case 9:
    case 'LOGIN_TYPE_DEVICE_ID':
      return LoginType.LOGIN_TYPE_DEVICE_ID;
    case 10:
    case 'LOGIN_TYPE_ACCOUNT_PASSWORD':
      return LoginType.LOGIN_TYPE_ACCOUNT_PASSWORD;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LoginType.UNRECOGNIZED;
  }
}

/** 发送验证码类型 */
export enum SendCodeType {
  SEND_CODE_TYPE_NONE = 0,
  /** SEND_CODE_TYPE_PHONE - 手机号 */
  SEND_CODE_TYPE_PHONE = 1,
  /** SEND_CODE_TYPE_WHATSAPP - Whatsapp */
  SEND_CODE_TYPE_WHATSAPP = 2,
  UNRECOGNIZED = -1
}

export function sendCodeTypeFromJSON(object: any): SendCodeType {
  switch (object) {
    case 0:
    case 'SEND_CODE_TYPE_NONE':
      return SendCodeType.SEND_CODE_TYPE_NONE;
    case 1:
    case 'SEND_CODE_TYPE_PHONE':
      return SendCodeType.SEND_CODE_TYPE_PHONE;
    case 2:
    case 'SEND_CODE_TYPE_WHATSAPP':
      return SendCodeType.SEND_CODE_TYPE_WHATSAPP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SendCodeType.UNRECOGNIZED;
  }
}

/** 验证码业务类型 */
export enum VerifyCodeBusinessType {
  BUSINESS_TYPE_NONE = 0,
  /** BUSINESS_TYPE_RESET_PASSWORD - 重置密码 */
  BUSINESS_TYPE_RESET_PASSWORD = 1,
  /** BUSINESS_TYPE_CHANGE_PASSWORD - 修改密码 */
  BUSINESS_TYPE_CHANGE_PASSWORD = 2,
  /** BUSINESS_TYPE_LOGIN - 登录 */
  BUSINESS_TYPE_LOGIN = 3,
  UNRECOGNIZED = -1
}

export function verifyCodeBusinessTypeFromJSON(object: any): VerifyCodeBusinessType {
  switch (object) {
    case 0:
    case 'BUSINESS_TYPE_NONE':
      return VerifyCodeBusinessType.BUSINESS_TYPE_NONE;
    case 1:
    case 'BUSINESS_TYPE_RESET_PASSWORD':
      return VerifyCodeBusinessType.BUSINESS_TYPE_RESET_PASSWORD;
    case 2:
    case 'BUSINESS_TYPE_CHANGE_PASSWORD':
      return VerifyCodeBusinessType.BUSINESS_TYPE_CHANGE_PASSWORD;
    case 3:
    case 'BUSINESS_TYPE_LOGIN':
      return VerifyCodeBusinessType.BUSINESS_TYPE_LOGIN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return VerifyCodeBusinessType.UNRECOGNIZED;
  }
}

/** 错误码 */
export enum ErrCode {
  ERR_CODE_NONE = 0,
  /** ERR_CODE_ACCOUNT_BAN - 账户被封禁 */
  ERR_CODE_ACCOUNT_BAN = 40001,
  /** ERR_CODE_ACCOUNT_WITHDRAW - 账号注销中 */
  ERR_CODE_ACCOUNT_WITHDRAW = 40002,
  /** ERR_CODE_ACCOUNT_DELETED - 账号已删除 */
  ERR_CODE_ACCOUNT_DELETED = 40003,
  /** ERR_CODE_ACCOUNT_LOGIN_PARAMS_INVALID - 账号登录参数错误 */
  ERR_CODE_ACCOUNT_LOGIN_PARAMS_INVALID = 40004,
  /** ERR_CODE_ACCOUNT_LOGIN_PWD_INVALID - 账号登录账号密码错误 */
  ERR_CODE_ACCOUNT_LOGIN_PWD_INVALID = 40005,
  /** ERR_CODE_ACCOUNT_LOGIN_CODE_INVALID - 账号登录验证码错误 */
  ERR_CODE_ACCOUNT_LOGIN_CODE_INVALID = 40006,
  /** ERR_CODE_DID_IS_BLANK - DID 登录时, DID 是空的 */
  ERR_CODE_DID_IS_BLANK = 40007,
  /** ERR_CODE_TOKEN_INVALID - AccessToken 和 RefreshToken 已失效或已过期 */
  ERR_CODE_TOKEN_INVALID = 100001,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ERR_CODE_NONE':
      return ErrCode.ERR_CODE_NONE;
    case 40001:
    case 'ERR_CODE_ACCOUNT_BAN':
      return ErrCode.ERR_CODE_ACCOUNT_BAN;
    case 40002:
    case 'ERR_CODE_ACCOUNT_WITHDRAW':
      return ErrCode.ERR_CODE_ACCOUNT_WITHDRAW;
    case 40003:
    case 'ERR_CODE_ACCOUNT_DELETED':
      return ErrCode.ERR_CODE_ACCOUNT_DELETED;
    case 40004:
    case 'ERR_CODE_ACCOUNT_LOGIN_PARAMS_INVALID':
      return ErrCode.ERR_CODE_ACCOUNT_LOGIN_PARAMS_INVALID;
    case 40005:
    case 'ERR_CODE_ACCOUNT_LOGIN_PWD_INVALID':
      return ErrCode.ERR_CODE_ACCOUNT_LOGIN_PWD_INVALID;
    case 40006:
    case 'ERR_CODE_ACCOUNT_LOGIN_CODE_INVALID':
      return ErrCode.ERR_CODE_ACCOUNT_LOGIN_CODE_INVALID;
    case 40007:
    case 'ERR_CODE_DID_IS_BLANK':
      return ErrCode.ERR_CODE_DID_IS_BLANK;
    case 100001:
    case 'ERR_CODE_TOKEN_INVALID':
      return ErrCode.ERR_CODE_TOKEN_INVALID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}

export interface LoginReq {
  /** 第三方Token */
  third_token: string;
  /** 手机号 */
  phone: string;
  /** 验证码 */
  verification_code: string;
  /** 密码 */
  password: string;
  /** 登录类型 */
  login_type: LoginType;
  /** 第三方账号，可为账号/邮箱，有就传 */
  third_account: string;
  /** 区号 */
  area_code: string;
  /** 发送短信类型 */
  send_code_type: SendCodeType;
  /** 验证码功能类型 */
  business_type: VerifyCodeBusinessType;
  /** 跳转链接 */
  redirect_url: string;
  /** tiktok code verifier is used to generate code challenge in PKCE authorization flow */
  code_verifier: string;
  /** 账号 */
  account: string;
  /** 设备信息 */
  device_info: DeviceInfo | undefined;
}

/** 设备信息 */
export interface DeviceInfo {
  /** 设备型号 */
  model: string;
  /** 设备品牌 */
  brand: string;
}

export interface LoginRsp {
  /** 访问token */
  access_token: string;
  /** 刷新token，访问token过期时，业务方自己决定是否用refresh_token续期 */
  refresh_token: string;
  /** 是否为首次注册 */
  is_first_register: boolean;
  /** 账号状态 */
  status: AccountStatus;
  uid: number;
  /** 账号状态对应的截止时间戳 */
  dateline: number;
}

export interface LogoutReq {
  /** 退登原因 */
  reason: LogoutReason;
  /** 一些日志, 后续排查问题可能会有些帮助， 例如请求某个接口返回了 10002 , 可以把接口的信息上报上来， 后端只是打印在日志里面. */
  log: string;
}

export interface LogoutRsp {}

export interface WithdrawReq {}

export interface WithdrawRsp {}

export interface WithdrawCancelReq {
  uid: number;
}

export interface WithdrawCancelRsp {}

export interface UsePasswordReq {
  /** 国家区号 */
  area_code: string;
  /** 手机号 */
  phone: string;
}

export interface UsePasswordRsp {
  /** 是否使用密码 true-使用 false-不使用 */
  use_password: boolean;
}

export interface SendCodeReq {
  /** 区号 */
  area_code: string;
  /** 手机号 */
  phone: string;
  /** 发送验证码方式 */
  send_code_type: SendCodeType;
  /** 验证码业务类型 */
  business_type: VerifyCodeBusinessType;
}

export interface SendCodeRsp {}

export interface ChangePasswordReq {
  /** 旧密码 */
  old_password: string;
  /** 新密码 */
  new_password: string;
  /** 是否需要重新登录 */
  is_need_login: boolean;
}

export interface ChangePasswordRsp {}

export interface ResetPasswordReq {
  /** 新密码 */
  password: string;
  /** 临时token */
  tmp_token: string;
  /** 区号 */
  area_code: string;
  /** 手机号 */
  phone: string;
  /** 发送验证码方式 */
  send_code_type: SendCodeType;
}

export interface ResetPasswordRsp {}

export interface CheckVerificationCodeReq {
  /** 区号 */
  area_code: string;
  /** 手机号 */
  phone: string;
  /** 验证码 */
  verification_code: string;
  /** 验证码业务类型 */
  business_type: VerifyCodeBusinessType;
  /** 发送验证码渠道 */
  send_code_type: SendCodeType;
}

export interface CheckVerificationCodeRsp {
  /** 临时token，重置密码时用于校验身份 */
  tmp_token: string;
}

export interface RefreshTokenReq {
  /** 刷新token */
  refresh_token: string;
  /** 访问token */
  access_token: string;
}

export interface RefreshTokenRsp {
  /** 访问token */
  access_token: string;
  /** 刷新token */
  refresh_token: string;
}

export interface ListCountryReq {}

export interface ListCountryRsp {
  /** 国家列表 */
  infos: CountryInfo[];
}

/** 国家信息 */
export interface CountryInfo {
  /** 图片URL */
  image: string;
  /** 国家缩写 */
  abbreviation: string;
  /** 国家名称 */
  name: string;
  /** 编码 */
  code: number;
  /** 国际化名称, {"en": "xxx", "ar": "yyy"}. */
  i18n_names: { [key: string]: string };
}

export interface CountryInfo_I18nNamesEntry {
  key: string;
  value: string;
}

export interface GetAccountStatusReq {
  /** 登录类型/注册类型 */
  login_type: LoginType;
  /** 区号 */
  area_code: string;
  /** 手机号 */
  phone: string;
  /** 第三方Token */
  third_token: string;
  /** 第三方账号，可为账号/邮箱，有就传 */
  third_account: string;
  /** 账号 */
  account: string;
}

export interface GetAccountStatusRsp {
  /** 用户uid */
  uid: number;
  /** 账号状态 */
  status: AccountStatus;
  /** 账号状态对应的截止时间戳 */
  dateline: number;
}

function createBaseLoginReq(): LoginReq {
  return {
    third_token: '',
    phone: '',
    verification_code: '',
    password: '',
    login_type: 0,
    third_account: '',
    area_code: '',
    send_code_type: 0,
    business_type: 0,
    redirect_url: '',
    code_verifier: '',
    account: '',
    device_info: undefined
  };
}

export const LoginReq: MessageFns<LoginReq> = {
  fromJSON(object: any): LoginReq {
    return {
      third_token: isSet(object.third_token) ? globalThis.String(object.third_token) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      verification_code: isSet(object.verification_code) ? globalThis.String(object.verification_code) : '',
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      login_type: isSet(object.login_type) ? loginTypeFromJSON(object.login_type) : 0,
      third_account: isSet(object.third_account) ? globalThis.String(object.third_account) : '',
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      send_code_type: isSet(object.send_code_type) ? sendCodeTypeFromJSON(object.send_code_type) : 0,
      business_type: isSet(object.business_type) ? verifyCodeBusinessTypeFromJSON(object.business_type) : 0,
      redirect_url: isSet(object.redirect_url) ? globalThis.String(object.redirect_url) : '',
      code_verifier: isSet(object.code_verifier) ? globalThis.String(object.code_verifier) : '',
      account: isSet(object.account) ? globalThis.String(object.account) : '',
      device_info: isSet(object.device_info) ? DeviceInfo.fromJSON(object.device_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<LoginReq>, I>>(base?: I): LoginReq {
    return LoginReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginReq>, I>>(object: I): LoginReq {
    const message = createBaseLoginReq();
    message.third_token = object.third_token ?? '';
    message.phone = object.phone ?? '';
    message.verification_code = object.verification_code ?? '';
    message.password = object.password ?? '';
    message.login_type = object.login_type ?? 0;
    message.third_account = object.third_account ?? '';
    message.area_code = object.area_code ?? '';
    message.send_code_type = object.send_code_type ?? 0;
    message.business_type = object.business_type ?? 0;
    message.redirect_url = object.redirect_url ?? '';
    message.code_verifier = object.code_verifier ?? '';
    message.account = object.account ?? '';
    message.device_info =
      object.device_info !== undefined && object.device_info !== null
        ? DeviceInfo.fromPartial(object.device_info)
        : undefined;
    return message;
  }
};

function createBaseDeviceInfo(): DeviceInfo {
  return { model: '', brand: '' };
}

export const DeviceInfo: MessageFns<DeviceInfo> = {
  fromJSON(object: any): DeviceInfo {
    return {
      model: isSet(object.model) ? globalThis.String(object.model) : '',
      brand: isSet(object.brand) ? globalThis.String(object.brand) : ''
    };
  },

  create<I extends Exact<DeepPartial<DeviceInfo>, I>>(base?: I): DeviceInfo {
    return DeviceInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeviceInfo>, I>>(object: I): DeviceInfo {
    const message = createBaseDeviceInfo();
    message.model = object.model ?? '';
    message.brand = object.brand ?? '';
    return message;
  }
};

function createBaseLoginRsp(): LoginRsp {
  return { access_token: '', refresh_token: '', is_first_register: false, status: 0, uid: 0, dateline: 0 };
}

export const LoginRsp: MessageFns<LoginRsp> = {
  fromJSON(object: any): LoginRsp {
    return {
      access_token: isSet(object.access_token) ? globalThis.String(object.access_token) : '',
      refresh_token: isSet(object.refresh_token) ? globalThis.String(object.refresh_token) : '',
      is_first_register: isSet(object.is_first_register) ? globalThis.Boolean(object.is_first_register) : false,
      status: isSet(object.status) ? accountStatusFromJSON(object.status) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      dateline: isSet(object.dateline) ? globalThis.Number(object.dateline) : 0
    };
  },

  create<I extends Exact<DeepPartial<LoginRsp>, I>>(base?: I): LoginRsp {
    return LoginRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LoginRsp>, I>>(object: I): LoginRsp {
    const message = createBaseLoginRsp();
    message.access_token = object.access_token ?? '';
    message.refresh_token = object.refresh_token ?? '';
    message.is_first_register = object.is_first_register ?? false;
    message.status = object.status ?? 0;
    message.uid = object.uid ?? 0;
    message.dateline = object.dateline ?? 0;
    return message;
  }
};

function createBaseLogoutReq(): LogoutReq {
  return { reason: 0, log: '' };
}

export const LogoutReq: MessageFns<LogoutReq> = {
  fromJSON(object: any): LogoutReq {
    return {
      reason: isSet(object.reason) ? logoutReasonFromJSON(object.reason) : 0,
      log: isSet(object.log) ? globalThis.String(object.log) : ''
    };
  },

  create<I extends Exact<DeepPartial<LogoutReq>, I>>(base?: I): LogoutReq {
    return LogoutReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogoutReq>, I>>(object: I): LogoutReq {
    const message = createBaseLogoutReq();
    message.reason = object.reason ?? 0;
    message.log = object.log ?? '';
    return message;
  }
};

function createBaseLogoutRsp(): LogoutRsp {
  return {};
}

export const LogoutRsp: MessageFns<LogoutRsp> = {
  fromJSON(_: any): LogoutRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<LogoutRsp>, I>>(base?: I): LogoutRsp {
    return LogoutRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogoutRsp>, I>>(_: I): LogoutRsp {
    const message = createBaseLogoutRsp();
    return message;
  }
};

function createBaseWithdrawReq(): WithdrawReq {
  return {};
}

export const WithdrawReq: MessageFns<WithdrawReq> = {
  fromJSON(_: any): WithdrawReq {
    return {};
  },

  create<I extends Exact<DeepPartial<WithdrawReq>, I>>(base?: I): WithdrawReq {
    return WithdrawReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawReq>, I>>(_: I): WithdrawReq {
    const message = createBaseWithdrawReq();
    return message;
  }
};

function createBaseWithdrawRsp(): WithdrawRsp {
  return {};
}

export const WithdrawRsp: MessageFns<WithdrawRsp> = {
  fromJSON(_: any): WithdrawRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<WithdrawRsp>, I>>(base?: I): WithdrawRsp {
    return WithdrawRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawRsp>, I>>(_: I): WithdrawRsp {
    const message = createBaseWithdrawRsp();
    return message;
  }
};

function createBaseWithdrawCancelReq(): WithdrawCancelReq {
  return { uid: 0 };
}

export const WithdrawCancelReq: MessageFns<WithdrawCancelReq> = {
  fromJSON(object: any): WithdrawCancelReq {
    return { uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0 };
  },

  create<I extends Exact<DeepPartial<WithdrawCancelReq>, I>>(base?: I): WithdrawCancelReq {
    return WithdrawCancelReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawCancelReq>, I>>(object: I): WithdrawCancelReq {
    const message = createBaseWithdrawCancelReq();
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseWithdrawCancelRsp(): WithdrawCancelRsp {
  return {};
}

export const WithdrawCancelRsp: MessageFns<WithdrawCancelRsp> = {
  fromJSON(_: any): WithdrawCancelRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<WithdrawCancelRsp>, I>>(base?: I): WithdrawCancelRsp {
    return WithdrawCancelRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawCancelRsp>, I>>(_: I): WithdrawCancelRsp {
    const message = createBaseWithdrawCancelRsp();
    return message;
  }
};

function createBaseUsePasswordReq(): UsePasswordReq {
  return { area_code: '', phone: '' };
}

export const UsePasswordReq: MessageFns<UsePasswordReq> = {
  fromJSON(object: any): UsePasswordReq {
    return {
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : ''
    };
  },

  create<I extends Exact<DeepPartial<UsePasswordReq>, I>>(base?: I): UsePasswordReq {
    return UsePasswordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UsePasswordReq>, I>>(object: I): UsePasswordReq {
    const message = createBaseUsePasswordReq();
    message.area_code = object.area_code ?? '';
    message.phone = object.phone ?? '';
    return message;
  }
};

function createBaseUsePasswordRsp(): UsePasswordRsp {
  return { use_password: false };
}

export const UsePasswordRsp: MessageFns<UsePasswordRsp> = {
  fromJSON(object: any): UsePasswordRsp {
    return { use_password: isSet(object.use_password) ? globalThis.Boolean(object.use_password) : false };
  },

  create<I extends Exact<DeepPartial<UsePasswordRsp>, I>>(base?: I): UsePasswordRsp {
    return UsePasswordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UsePasswordRsp>, I>>(object: I): UsePasswordRsp {
    const message = createBaseUsePasswordRsp();
    message.use_password = object.use_password ?? false;
    return message;
  }
};

function createBaseSendCodeReq(): SendCodeReq {
  return { area_code: '', phone: '', send_code_type: 0, business_type: 0 };
}

export const SendCodeReq: MessageFns<SendCodeReq> = {
  fromJSON(object: any): SendCodeReq {
    return {
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      send_code_type: isSet(object.send_code_type) ? sendCodeTypeFromJSON(object.send_code_type) : 0,
      business_type: isSet(object.business_type) ? verifyCodeBusinessTypeFromJSON(object.business_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<SendCodeReq>, I>>(base?: I): SendCodeReq {
    return SendCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendCodeReq>, I>>(object: I): SendCodeReq {
    const message = createBaseSendCodeReq();
    message.area_code = object.area_code ?? '';
    message.phone = object.phone ?? '';
    message.send_code_type = object.send_code_type ?? 0;
    message.business_type = object.business_type ?? 0;
    return message;
  }
};

function createBaseSendCodeRsp(): SendCodeRsp {
  return {};
}

export const SendCodeRsp: MessageFns<SendCodeRsp> = {
  fromJSON(_: any): SendCodeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SendCodeRsp>, I>>(base?: I): SendCodeRsp {
    return SendCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendCodeRsp>, I>>(_: I): SendCodeRsp {
    const message = createBaseSendCodeRsp();
    return message;
  }
};

function createBaseChangePasswordReq(): ChangePasswordReq {
  return { old_password: '', new_password: '', is_need_login: false };
}

export const ChangePasswordReq: MessageFns<ChangePasswordReq> = {
  fromJSON(object: any): ChangePasswordReq {
    return {
      old_password: isSet(object.old_password) ? globalThis.String(object.old_password) : '',
      new_password: isSet(object.new_password) ? globalThis.String(object.new_password) : '',
      is_need_login: isSet(object.is_need_login) ? globalThis.Boolean(object.is_need_login) : false
    };
  },

  create<I extends Exact<DeepPartial<ChangePasswordReq>, I>>(base?: I): ChangePasswordReq {
    return ChangePasswordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangePasswordReq>, I>>(object: I): ChangePasswordReq {
    const message = createBaseChangePasswordReq();
    message.old_password = object.old_password ?? '';
    message.new_password = object.new_password ?? '';
    message.is_need_login = object.is_need_login ?? false;
    return message;
  }
};

function createBaseChangePasswordRsp(): ChangePasswordRsp {
  return {};
}

export const ChangePasswordRsp: MessageFns<ChangePasswordRsp> = {
  fromJSON(_: any): ChangePasswordRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ChangePasswordRsp>, I>>(base?: I): ChangePasswordRsp {
    return ChangePasswordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ChangePasswordRsp>, I>>(_: I): ChangePasswordRsp {
    const message = createBaseChangePasswordRsp();
    return message;
  }
};

function createBaseResetPasswordReq(): ResetPasswordReq {
  return { password: '', tmp_token: '', area_code: '', phone: '', send_code_type: 0 };
}

export const ResetPasswordReq: MessageFns<ResetPasswordReq> = {
  fromJSON(object: any): ResetPasswordReq {
    return {
      password: isSet(object.password) ? globalThis.String(object.password) : '',
      tmp_token: isSet(object.tmp_token) ? globalThis.String(object.tmp_token) : '',
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      send_code_type: isSet(object.send_code_type) ? sendCodeTypeFromJSON(object.send_code_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ResetPasswordReq>, I>>(base?: I): ResetPasswordReq {
    return ResetPasswordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResetPasswordReq>, I>>(object: I): ResetPasswordReq {
    const message = createBaseResetPasswordReq();
    message.password = object.password ?? '';
    message.tmp_token = object.tmp_token ?? '';
    message.area_code = object.area_code ?? '';
    message.phone = object.phone ?? '';
    message.send_code_type = object.send_code_type ?? 0;
    return message;
  }
};

function createBaseResetPasswordRsp(): ResetPasswordRsp {
  return {};
}

export const ResetPasswordRsp: MessageFns<ResetPasswordRsp> = {
  fromJSON(_: any): ResetPasswordRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ResetPasswordRsp>, I>>(base?: I): ResetPasswordRsp {
    return ResetPasswordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResetPasswordRsp>, I>>(_: I): ResetPasswordRsp {
    const message = createBaseResetPasswordRsp();
    return message;
  }
};

function createBaseCheckVerificationCodeReq(): CheckVerificationCodeReq {
  return { area_code: '', phone: '', verification_code: '', business_type: 0, send_code_type: 0 };
}

export const CheckVerificationCodeReq: MessageFns<CheckVerificationCodeReq> = {
  fromJSON(object: any): CheckVerificationCodeReq {
    return {
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      verification_code: isSet(object.verification_code) ? globalThis.String(object.verification_code) : '',
      business_type: isSet(object.business_type) ? verifyCodeBusinessTypeFromJSON(object.business_type) : 0,
      send_code_type: isSet(object.send_code_type) ? sendCodeTypeFromJSON(object.send_code_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<CheckVerificationCodeReq>, I>>(base?: I): CheckVerificationCodeReq {
    return CheckVerificationCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckVerificationCodeReq>, I>>(object: I): CheckVerificationCodeReq {
    const message = createBaseCheckVerificationCodeReq();
    message.area_code = object.area_code ?? '';
    message.phone = object.phone ?? '';
    message.verification_code = object.verification_code ?? '';
    message.business_type = object.business_type ?? 0;
    message.send_code_type = object.send_code_type ?? 0;
    return message;
  }
};

function createBaseCheckVerificationCodeRsp(): CheckVerificationCodeRsp {
  return { tmp_token: '' };
}

export const CheckVerificationCodeRsp: MessageFns<CheckVerificationCodeRsp> = {
  fromJSON(object: any): CheckVerificationCodeRsp {
    return { tmp_token: isSet(object.tmp_token) ? globalThis.String(object.tmp_token) : '' };
  },

  create<I extends Exact<DeepPartial<CheckVerificationCodeRsp>, I>>(base?: I): CheckVerificationCodeRsp {
    return CheckVerificationCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckVerificationCodeRsp>, I>>(object: I): CheckVerificationCodeRsp {
    const message = createBaseCheckVerificationCodeRsp();
    message.tmp_token = object.tmp_token ?? '';
    return message;
  }
};

function createBaseRefreshTokenReq(): RefreshTokenReq {
  return { refresh_token: '', access_token: '' };
}

export const RefreshTokenReq: MessageFns<RefreshTokenReq> = {
  fromJSON(object: any): RefreshTokenReq {
    return {
      refresh_token: isSet(object.refresh_token) ? globalThis.String(object.refresh_token) : '',
      access_token: isSet(object.access_token) ? globalThis.String(object.access_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<RefreshTokenReq>, I>>(base?: I): RefreshTokenReq {
    return RefreshTokenReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshTokenReq>, I>>(object: I): RefreshTokenReq {
    const message = createBaseRefreshTokenReq();
    message.refresh_token = object.refresh_token ?? '';
    message.access_token = object.access_token ?? '';
    return message;
  }
};

function createBaseRefreshTokenRsp(): RefreshTokenRsp {
  return { access_token: '', refresh_token: '' };
}

export const RefreshTokenRsp: MessageFns<RefreshTokenRsp> = {
  fromJSON(object: any): RefreshTokenRsp {
    return {
      access_token: isSet(object.access_token) ? globalThis.String(object.access_token) : '',
      refresh_token: isSet(object.refresh_token) ? globalThis.String(object.refresh_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<RefreshTokenRsp>, I>>(base?: I): RefreshTokenRsp {
    return RefreshTokenRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RefreshTokenRsp>, I>>(object: I): RefreshTokenRsp {
    const message = createBaseRefreshTokenRsp();
    message.access_token = object.access_token ?? '';
    message.refresh_token = object.refresh_token ?? '';
    return message;
  }
};

function createBaseListCountryReq(): ListCountryReq {
  return {};
}

export const ListCountryReq: MessageFns<ListCountryReq> = {
  fromJSON(_: any): ListCountryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListCountryReq>, I>>(base?: I): ListCountryReq {
    return ListCountryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCountryReq>, I>>(_: I): ListCountryReq {
    const message = createBaseListCountryReq();
    return message;
  }
};

function createBaseListCountryRsp(): ListCountryRsp {
  return { infos: [] };
}

export const ListCountryRsp: MessageFns<ListCountryRsp> = {
  fromJSON(object: any): ListCountryRsp {
    return {
      infos: globalThis.Array.isArray(object?.infos) ? object.infos.map((e: any) => CountryInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListCountryRsp>, I>>(base?: I): ListCountryRsp {
    return ListCountryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCountryRsp>, I>>(object: I): ListCountryRsp {
    const message = createBaseListCountryRsp();
    message.infos = object.infos?.map(e => CountryInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCountryInfo(): CountryInfo {
  return { image: '', abbreviation: '', name: '', code: 0, i18n_names: {} };
}

export const CountryInfo: MessageFns<CountryInfo> = {
  fromJSON(object: any): CountryInfo {
    return {
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      abbreviation: isSet(object.abbreviation) ? globalThis.String(object.abbreviation) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CountryInfo>, I>>(base?: I): CountryInfo {
    return CountryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CountryInfo>, I>>(object: I): CountryInfo {
    const message = createBaseCountryInfo();
    message.image = object.image ?? '';
    message.abbreviation = object.abbreviation ?? '';
    message.name = object.name ?? '';
    message.code = object.code ?? 0;
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCountryInfo_I18nNamesEntry(): CountryInfo_I18nNamesEntry {
  return { key: '', value: '' };
}

export const CountryInfo_I18nNamesEntry: MessageFns<CountryInfo_I18nNamesEntry> = {
  fromJSON(object: any): CountryInfo_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<CountryInfo_I18nNamesEntry>, I>>(base?: I): CountryInfo_I18nNamesEntry {
    return CountryInfo_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CountryInfo_I18nNamesEntry>, I>>(object: I): CountryInfo_I18nNamesEntry {
    const message = createBaseCountryInfo_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetAccountStatusReq(): GetAccountStatusReq {
  return { login_type: 0, area_code: '', phone: '', third_token: '', third_account: '', account: '' };
}

export const GetAccountStatusReq: MessageFns<GetAccountStatusReq> = {
  fromJSON(object: any): GetAccountStatusReq {
    return {
      login_type: isSet(object.login_type) ? loginTypeFromJSON(object.login_type) : 0,
      area_code: isSet(object.area_code) ? globalThis.String(object.area_code) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      third_token: isSet(object.third_token) ? globalThis.String(object.third_token) : '',
      third_account: isSet(object.third_account) ? globalThis.String(object.third_account) : '',
      account: isSet(object.account) ? globalThis.String(object.account) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetAccountStatusReq>, I>>(base?: I): GetAccountStatusReq {
    return GetAccountStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAccountStatusReq>, I>>(object: I): GetAccountStatusReq {
    const message = createBaseGetAccountStatusReq();
    message.login_type = object.login_type ?? 0;
    message.area_code = object.area_code ?? '';
    message.phone = object.phone ?? '';
    message.third_token = object.third_token ?? '';
    message.third_account = object.third_account ?? '';
    message.account = object.account ?? '';
    return message;
  }
};

function createBaseGetAccountStatusRsp(): GetAccountStatusRsp {
  return { uid: 0, status: 0, dateline: 0 };
}

export const GetAccountStatusRsp: MessageFns<GetAccountStatusRsp> = {
  fromJSON(object: any): GetAccountStatusRsp {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      status: isSet(object.status) ? accountStatusFromJSON(object.status) : 0,
      dateline: isSet(object.dateline) ? globalThis.Number(object.dateline) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetAccountStatusRsp>, I>>(base?: I): GetAccountStatusRsp {
    return GetAccountStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAccountStatusRsp>, I>>(object: I): GetAccountStatusRsp {
    const message = createBaseGetAccountStatusRsp();
    message.uid = object.uid ?? 0;
    message.status = object.status ?? 0;
    message.dateline = object.dateline ?? 0;
    return message;
  }
};

/**
 * 账号服务
 * serviceName: rpc.social.account
 * smicro:spath=gitit.cc/social/components-service/social-account/handler/account
 */
export type AccountDefinition = typeof AccountDefinition;
export const AccountDefinition = {
  name: 'Account',
  fullName: 'pbaccount.Account',
  methods: {
    /** 登录注册 */
    login: {
      name: 'Login',
      requestType: LoginReq,
      requestStream: false,
      responseType: LoginRsp,
      responseStream: false,
      options: {}
    },
    /** 登出 */
    logout: {
      name: 'Logout',
      requestType: LogoutReq,
      requestStream: false,
      responseType: LogoutRsp,
      responseStream: false,
      options: {}
    },
    /** 注销 */
    withdraw: {
      name: 'Withdraw',
      requestType: WithdrawReq,
      requestStream: false,
      responseType: WithdrawRsp,
      responseStream: false,
      options: {}
    },
    /** 取消注销 */
    withdrawCancel: {
      name: 'WithdrawCancel',
      requestType: WithdrawCancelReq,
      requestStream: false,
      responseType: WithdrawCancelRsp,
      responseStream: false,
      options: {}
    },
    /** 是否使用密码 */
    usePassword: {
      name: 'UsePassword',
      requestType: UsePasswordReq,
      requestStream: false,
      responseType: UsePasswordRsp,
      responseStream: false,
      options: {}
    },
    /** 发送验证码 */
    sendCode: {
      name: 'SendCode',
      requestType: SendCodeReq,
      requestStream: false,
      responseType: SendCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 修改密码 */
    changePassword: {
      name: 'ChangePassword',
      requestType: ChangePasswordReq,
      requestStream: false,
      responseType: ChangePasswordRsp,
      responseStream: false,
      options: {}
    },
    /** 重置密码 */
    resetPassword: {
      name: 'ResetPassword',
      requestType: ResetPasswordReq,
      requestStream: false,
      responseType: ChangePasswordRsp,
      responseStream: false,
      options: {}
    },
    /** 校验验证码-暂时用于重置密码校验 */
    checkVerificationCode: {
      name: 'CheckVerificationCode',
      requestType: CheckVerificationCodeReq,
      requestStream: false,
      responseType: CheckVerificationCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 刷新token */
    refreshToken: {
      name: 'RefreshToken',
      requestType: RefreshTokenReq,
      requestStream: false,
      responseType: RefreshTokenRsp,
      responseStream: false,
      options: {}
    },
    /** 获取国家列表 */
    listCountry: {
      name: 'ListCountry',
      requestType: ListCountryReq,
      requestStream: false,
      responseType: ListCountryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取账号状态 */
    getAccountStatus: {
      name: 'GetAccountStatus',
      requestType: GetAccountStatusReq,
      requestStream: false,
      responseType: GetAccountStatusRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
