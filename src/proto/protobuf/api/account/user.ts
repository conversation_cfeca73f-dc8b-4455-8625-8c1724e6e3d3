// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/account/user.proto

/* eslint-disable */
import { CountryInfo } from './account';

export const protobufPackage = 'pbaccount';

/** 性别 */
export enum Gender {
  /** GENDER_NONE - 未知 */
  GENDER_NONE = 0,
  /** GENDER_MALE - 男 */
  GENDER_MALE = 1,
  /** GENDER_FEMALE - 女 */
  GENDER_FEMALE = 2,
  UNRECOGNIZED = -1
}

export function genderFromJSON(object: any): Gender {
  switch (object) {
    case 0:
    case 'GENDER_NONE':
      return Gender.GENDER_NONE;
    case 1:
    case 'GENDER_MALE':
      return Gender.GENDER_MALE;
    case 2:
    case 'GENDER_FEMALE':
      return Gender.GENDER_FEMALE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Gender.UNRECOGNIZED;
  }
}

/** 账号状态 */
export enum UserStatus {
  /** USER_STATUS_NONE - 无状态 */
  USER_STATUS_NONE = 0,
  /** USER_STATUS_NORMAL - 正常 */
  USER_STATUS_NORMAL = 1,
  /** USER_STATUS_BAN - 封禁 */
  USER_STATUS_BAN = 2,
  /** USER_STATUS_FROZEN - 冻结 */
  USER_STATUS_FROZEN = 3,
  /** USER_STATUS_WITHDRAW - 注销（注销冷静期过后更新为删除） */
  USER_STATUS_WITHDRAW = 4,
  /** USER_STATUS_DELETE - 删除 */
  USER_STATUS_DELETE = 5,
  UNRECOGNIZED = -1
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case 'USER_STATUS_NONE':
      return UserStatus.USER_STATUS_NONE;
    case 1:
    case 'USER_STATUS_NORMAL':
      return UserStatus.USER_STATUS_NORMAL;
    case 2:
    case 'USER_STATUS_BAN':
      return UserStatus.USER_STATUS_BAN;
    case 3:
    case 'USER_STATUS_FROZEN':
      return UserStatus.USER_STATUS_FROZEN;
    case 4:
    case 'USER_STATUS_WITHDRAW':
      return UserStatus.USER_STATUS_WITHDRAW;
    case 5:
    case 'USER_STATUS_DELETE':
      return UserStatus.USER_STATUS_DELETE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

/** 用户信息 */
export interface UserInfo {
  /** 用户UID */
  uid: number;
  /** 用户外显UID */
  show_uid: ShowUID | undefined;
  /** 用户昵称 */
  nickname: Nickname | undefined;
  /** 用户性别 */
  gender: Gender;
  /** 用户头像 */
  avatar: Avatar | undefined;
  /** 用户国家 */
  country_info: CountryInfo | undefined;
  /** 用户生日 */
  birthday: Birthday | undefined;
  /** 语言码 */
  language: string;
  /** 用户状态 */
  status: UserStatus;
  /** 自我介绍 */
  introduction: string;
  /** 系统类型 */
  os_type: number;
  /** 客户端版本 */
  client_version: string;
}

/** ShowUID, 这个结构是用于拓展靓号系统, 例如靓号可以有不同的颜色,字体... */
export interface ShowUID {
  /** show-uid */
  show_uid: string;
}

/** 用户昵称, 这个结构是用于拓展用户特权, 例如会员的昵称是红色的, 还有一个图标的... */
export interface Nickname {
  /** 用户昵称 */
  nickname: string;
}

/** 用户头像, 这个结构是用于拓展不同尺寸的头像, 例如缩略图, 高清图等以适应不同显示场景, 同时还可以拓展不同等级的用户头像有不同的样式. */
export interface Avatar {
  /** 小图 */
  small_url: string;
  /** 中图 */
  middle_url: string;
  /** 大图 */
  large_url: string;
}

/** 生日, 用于拓展年龄, 星座等信息 */
export interface Birthday {
  /** 生日 (yyyy-MM-dd) */
  birthday: string;
}

export interface GetUserInfoReq {
  /** 查看的目标用户UID, 如果和请求者UID一致则为查看主态信息, 否则为查看客态信息. */
  target_uid: number;
}

export interface GetUserInfoRsp {
  user_info: UserInfo | undefined;
}

export interface UpdateUserInfoReq {
  user_info: UserInfo | undefined;
}

export interface UpdateUserInfoRsp {
  user_info: UserInfo | undefined;
}

export interface UpdateIntroductionReq {
  /** 自我介绍 */
  introduction: string;
}

export interface UpdateIntroductionRsp {
  /** 自我介绍 */
  introduction: string;
}

export interface GetInitInfoReq {}

export interface GetInitInfoRsp {
  /** 默认头像列表, 用于拓展可以选择更多随机头像, 客户端显示第一个即可. */
  default_avatars: string[];
  /** 男性头像列表, 用于拓展可以选择更多随机头像, 客户端显示第一个即可. */
  male_avatars: string[];
  /** 女性头像列表, 用于拓展可以选择更多随机头像, 客户端显示第一个即可. */
  female_avatars: string[];
  /** 随机昵称列表 */
  random_nicknames: string[];
  /** 默认国家, 是否需要返回所有可选国家列表? */
  default_country: CountryInfo | undefined;
}

export interface GetRandomNicknameReq {
  /** 性别 */
  gender: Gender;
}

export interface GetRandomNicknameRsp {
  /** 随机昵称列表 */
  random_nicknames: string[];
}

export interface BatchGetUserReq {
  /** 查看的目标用户UID */
  uids: number[];
}

export interface BatchGetUserRsp {
  user_infos: UserInfo[];
}

function createBaseUserInfo(): UserInfo {
  return {
    uid: 0,
    show_uid: undefined,
    nickname: undefined,
    gender: 0,
    avatar: undefined,
    country_info: undefined,
    birthday: undefined,
    language: '',
    status: 0,
    introduction: '',
    os_type: 0,
    client_version: ''
  };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? ShowUID.fromJSON(object.show_uid) : undefined,
      nickname: isSet(object.nickname) ? Nickname.fromJSON(object.nickname) : undefined,
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      avatar: isSet(object.avatar) ? Avatar.fromJSON(object.avatar) : undefined,
      country_info: isSet(object.country_info) ? CountryInfo.fromJSON(object.country_info) : undefined,
      birthday: isSet(object.birthday) ? Birthday.fromJSON(object.birthday) : undefined,
      language: isSet(object.language) ? globalThis.String(object.language) : '',
      status: isSet(object.status) ? userStatusFromJSON(object.status) : 0,
      introduction: isSet(object.introduction) ? globalThis.String(object.introduction) : '',
      os_type: isSet(object.os_type) ? globalThis.Number(object.os_type) : 0,
      client_version: isSet(object.client_version) ? globalThis.String(object.client_version) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid =
      object.show_uid !== undefined && object.show_uid !== null ? ShowUID.fromPartial(object.show_uid) : undefined;
    message.nickname =
      object.nickname !== undefined && object.nickname !== null ? Nickname.fromPartial(object.nickname) : undefined;
    message.gender = object.gender ?? 0;
    message.avatar =
      object.avatar !== undefined && object.avatar !== null ? Avatar.fromPartial(object.avatar) : undefined;
    message.country_info =
      object.country_info !== undefined && object.country_info !== null
        ? CountryInfo.fromPartial(object.country_info)
        : undefined;
    message.birthday =
      object.birthday !== undefined && object.birthday !== null ? Birthday.fromPartial(object.birthday) : undefined;
    message.language = object.language ?? '';
    message.status = object.status ?? 0;
    message.introduction = object.introduction ?? '';
    message.os_type = object.os_type ?? 0;
    message.client_version = object.client_version ?? '';
    return message;
  }
};

function createBaseShowUID(): ShowUID {
  return { show_uid: '' };
}

export const ShowUID: MessageFns<ShowUID> = {
  fromJSON(object: any): ShowUID {
    return { show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '' };
  },

  create<I extends Exact<DeepPartial<ShowUID>, I>>(base?: I): ShowUID {
    return ShowUID.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ShowUID>, I>>(object: I): ShowUID {
    const message = createBaseShowUID();
    message.show_uid = object.show_uid ?? '';
    return message;
  }
};

function createBaseNickname(): Nickname {
  return { nickname: '' };
}

export const Nickname: MessageFns<Nickname> = {
  fromJSON(object: any): Nickname {
    return { nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '' };
  },

  create<I extends Exact<DeepPartial<Nickname>, I>>(base?: I): Nickname {
    return Nickname.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Nickname>, I>>(object: I): Nickname {
    const message = createBaseNickname();
    message.nickname = object.nickname ?? '';
    return message;
  }
};

function createBaseAvatar(): Avatar {
  return { small_url: '', middle_url: '', large_url: '' };
}

export const Avatar: MessageFns<Avatar> = {
  fromJSON(object: any): Avatar {
    return {
      small_url: isSet(object.small_url) ? globalThis.String(object.small_url) : '',
      middle_url: isSet(object.middle_url) ? globalThis.String(object.middle_url) : '',
      large_url: isSet(object.large_url) ? globalThis.String(object.large_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<Avatar>, I>>(base?: I): Avatar {
    return Avatar.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Avatar>, I>>(object: I): Avatar {
    const message = createBaseAvatar();
    message.small_url = object.small_url ?? '';
    message.middle_url = object.middle_url ?? '';
    message.large_url = object.large_url ?? '';
    return message;
  }
};

function createBaseBirthday(): Birthday {
  return { birthday: '' };
}

export const Birthday: MessageFns<Birthday> = {
  fromJSON(object: any): Birthday {
    return { birthday: isSet(object.birthday) ? globalThis.String(object.birthday) : '' };
  },

  create<I extends Exact<DeepPartial<Birthday>, I>>(base?: I): Birthday {
    return Birthday.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Birthday>, I>>(object: I): Birthday {
    const message = createBaseBirthday();
    message.birthday = object.birthday ?? '';
    return message;
  }
};

function createBaseGetUserInfoReq(): GetUserInfoReq {
  return { target_uid: 0 };
}

export const GetUserInfoReq: MessageFns<GetUserInfoReq> = {
  fromJSON(object: any): GetUserInfoReq {
    return { target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0 };
  },

  create<I extends Exact<DeepPartial<GetUserInfoReq>, I>>(base?: I): GetUserInfoReq {
    return GetUserInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserInfoReq>, I>>(object: I): GetUserInfoReq {
    const message = createBaseGetUserInfoReq();
    message.target_uid = object.target_uid ?? 0;
    return message;
  }
};

function createBaseGetUserInfoRsp(): GetUserInfoRsp {
  return { user_info: undefined };
}

export const GetUserInfoRsp: MessageFns<GetUserInfoRsp> = {
  fromJSON(object: any): GetUserInfoRsp {
    return { user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetUserInfoRsp>, I>>(base?: I): GetUserInfoRsp {
    return GetUserInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserInfoRsp>, I>>(object: I): GetUserInfoRsp {
    const message = createBaseGetUserInfoRsp();
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    return message;
  }
};

function createBaseUpdateUserInfoReq(): UpdateUserInfoReq {
  return { user_info: undefined };
}

export const UpdateUserInfoReq: MessageFns<UpdateUserInfoReq> = {
  fromJSON(object: any): UpdateUserInfoReq {
    return { user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateUserInfoReq>, I>>(base?: I): UpdateUserInfoReq {
    return UpdateUserInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateUserInfoReq>, I>>(object: I): UpdateUserInfoReq {
    const message = createBaseUpdateUserInfoReq();
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    return message;
  }
};

function createBaseUpdateUserInfoRsp(): UpdateUserInfoRsp {
  return { user_info: undefined };
}

export const UpdateUserInfoRsp: MessageFns<UpdateUserInfoRsp> = {
  fromJSON(object: any): UpdateUserInfoRsp {
    return { user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateUserInfoRsp>, I>>(base?: I): UpdateUserInfoRsp {
    return UpdateUserInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateUserInfoRsp>, I>>(object: I): UpdateUserInfoRsp {
    const message = createBaseUpdateUserInfoRsp();
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    return message;
  }
};

function createBaseUpdateIntroductionReq(): UpdateIntroductionReq {
  return { introduction: '' };
}

export const UpdateIntroductionReq: MessageFns<UpdateIntroductionReq> = {
  fromJSON(object: any): UpdateIntroductionReq {
    return { introduction: isSet(object.introduction) ? globalThis.String(object.introduction) : '' };
  },

  create<I extends Exact<DeepPartial<UpdateIntroductionReq>, I>>(base?: I): UpdateIntroductionReq {
    return UpdateIntroductionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateIntroductionReq>, I>>(object: I): UpdateIntroductionReq {
    const message = createBaseUpdateIntroductionReq();
    message.introduction = object.introduction ?? '';
    return message;
  }
};

function createBaseUpdateIntroductionRsp(): UpdateIntroductionRsp {
  return { introduction: '' };
}

export const UpdateIntroductionRsp: MessageFns<UpdateIntroductionRsp> = {
  fromJSON(object: any): UpdateIntroductionRsp {
    return { introduction: isSet(object.introduction) ? globalThis.String(object.introduction) : '' };
  },

  create<I extends Exact<DeepPartial<UpdateIntroductionRsp>, I>>(base?: I): UpdateIntroductionRsp {
    return UpdateIntroductionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateIntroductionRsp>, I>>(object: I): UpdateIntroductionRsp {
    const message = createBaseUpdateIntroductionRsp();
    message.introduction = object.introduction ?? '';
    return message;
  }
};

function createBaseGetInitInfoReq(): GetInitInfoReq {
  return {};
}

export const GetInitInfoReq: MessageFns<GetInitInfoReq> = {
  fromJSON(_: any): GetInitInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetInitInfoReq>, I>>(base?: I): GetInitInfoReq {
    return GetInitInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetInitInfoReq>, I>>(_: I): GetInitInfoReq {
    const message = createBaseGetInitInfoReq();
    return message;
  }
};

function createBaseGetInitInfoRsp(): GetInitInfoRsp {
  return {
    default_avatars: [],
    male_avatars: [],
    female_avatars: [],
    random_nicknames: [],
    default_country: undefined
  };
}

export const GetInitInfoRsp: MessageFns<GetInitInfoRsp> = {
  fromJSON(object: any): GetInitInfoRsp {
    return {
      default_avatars: globalThis.Array.isArray(object?.default_avatars)
        ? object.default_avatars.map((e: any) => globalThis.String(e))
        : [],
      male_avatars: globalThis.Array.isArray(object?.male_avatars)
        ? object.male_avatars.map((e: any) => globalThis.String(e))
        : [],
      female_avatars: globalThis.Array.isArray(object?.female_avatars)
        ? object.female_avatars.map((e: any) => globalThis.String(e))
        : [],
      random_nicknames: globalThis.Array.isArray(object?.random_nicknames)
        ? object.random_nicknames.map((e: any) => globalThis.String(e))
        : [],
      default_country: isSet(object.default_country) ? CountryInfo.fromJSON(object.default_country) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetInitInfoRsp>, I>>(base?: I): GetInitInfoRsp {
    return GetInitInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetInitInfoRsp>, I>>(object: I): GetInitInfoRsp {
    const message = createBaseGetInitInfoRsp();
    message.default_avatars = object.default_avatars?.map(e => e) || [];
    message.male_avatars = object.male_avatars?.map(e => e) || [];
    message.female_avatars = object.female_avatars?.map(e => e) || [];
    message.random_nicknames = object.random_nicknames?.map(e => e) || [];
    message.default_country =
      object.default_country !== undefined && object.default_country !== null
        ? CountryInfo.fromPartial(object.default_country)
        : undefined;
    return message;
  }
};

function createBaseGetRandomNicknameReq(): GetRandomNicknameReq {
  return { gender: 0 };
}

export const GetRandomNicknameReq: MessageFns<GetRandomNicknameReq> = {
  fromJSON(object: any): GetRandomNicknameReq {
    return { gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRandomNicknameReq>, I>>(base?: I): GetRandomNicknameReq {
    return GetRandomNicknameReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRandomNicknameReq>, I>>(object: I): GetRandomNicknameReq {
    const message = createBaseGetRandomNicknameReq();
    message.gender = object.gender ?? 0;
    return message;
  }
};

function createBaseGetRandomNicknameRsp(): GetRandomNicknameRsp {
  return { random_nicknames: [] };
}

export const GetRandomNicknameRsp: MessageFns<GetRandomNicknameRsp> = {
  fromJSON(object: any): GetRandomNicknameRsp {
    return {
      random_nicknames: globalThis.Array.isArray(object?.random_nicknames)
        ? object.random_nicknames.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetRandomNicknameRsp>, I>>(base?: I): GetRandomNicknameRsp {
    return GetRandomNicknameRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRandomNicknameRsp>, I>>(object: I): GetRandomNicknameRsp {
    const message = createBaseGetRandomNicknameRsp();
    message.random_nicknames = object.random_nicknames?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserReq(): BatchGetUserReq {
  return { uids: [] };
}

export const BatchGetUserReq: MessageFns<BatchGetUserReq> = {
  fromJSON(object: any): BatchGetUserReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetUserReq>, I>>(base?: I): BatchGetUserReq {
    return BatchGetUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserReq>, I>>(object: I): BatchGetUserReq {
    const message = createBaseBatchGetUserReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserRsp(): BatchGetUserRsp {
  return { user_infos: [] };
}

export const BatchGetUserRsp: MessageFns<BatchGetUserRsp> = {
  fromJSON(object: any): BatchGetUserRsp {
    return {
      user_infos: globalThis.Array.isArray(object?.user_infos)
        ? object.user_infos.map((e: any) => UserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserRsp>, I>>(base?: I): BatchGetUserRsp {
    return BatchGetUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserRsp>, I>>(object: I): BatchGetUserRsp {
    const message = createBaseBatchGetUserRsp();
    message.user_infos = object.user_infos?.map(e => UserInfo.fromPartial(e)) || [];
    return message;
  }
};

export type UserDefinition = typeof UserDefinition;
export const UserDefinition = {
  name: 'User',
  fullName: 'pbaccount.User',
  methods: {
    /** 获取用户信息, 通过参数 target_uid 与当前用户 uid 是否一致来区分主客态. */
    getUserInfo: {
      name: 'GetUserInfo',
      requestType: GetUserInfoReq,
      requestStream: false,
      responseType: GetUserInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 更新用户信息, 注册时首次填写用户资料也是这个接口. */
    updateUserInfo: {
      name: 'UpdateUserInfo',
      requestType: UpdateUserInfoReq,
      requestStream: false,
      responseType: UpdateUserInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 获取注册页面的信息, 客户端应当在获取失败时也不影响注册流程. */
    getInitInfo: {
      name: 'GetInitInfo',
      requestType: GetInitInfoReq,
      requestStream: false,
      responseType: GetInitInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 获取一批随机昵称 */
    getRandomNickname: {
      name: 'GetRandomNickname',
      requestType: GetRandomNicknameReq,
      requestStream: false,
      responseType: GetRandomNicknameRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取用户信息 */
    batchGetUser: {
      name: 'BatchGetUser',
      requestType: BatchGetUserReq,
      requestStream: false,
      responseType: BatchGetUserRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
