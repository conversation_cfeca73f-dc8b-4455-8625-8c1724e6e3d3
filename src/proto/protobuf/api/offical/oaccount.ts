// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/offical/oaccount.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.offical';

/** smicro:spath=gitit.cc/social/components-service/social-offical/handler-api/oaccountapi */

/** 错误码 */
export enum ErrCode {
  ERR_CODE_API_NONE = 0,
  /** ERR_CODE_ACCOUNT_TYPE_ALREADY_BINDED - 账号类型已被绑定 */
  ERR_CODE_ACCOUNT_TYPE_ALREADY_BINDED = 200000,
  /** ERR_CODE_ACCOUNT_TYPE_NOT_BINDED - 账号类型未绑定 */
  ERR_CODE_ACCOUNT_TYPE_NOT_BINDED = 200001,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ERR_CODE_API_NONE':
      return ErrCode.ERR_CODE_API_NONE;
    case 200000:
    case 'ERR_CODE_ACCOUNT_TYPE_ALREADY_BINDED':
      return ErrCode.ERR_CODE_ACCOUNT_TYPE_ALREADY_BINDED;
    case 200001:
    case 'ERR_CODE_ACCOUNT_TYPE_NOT_BINDED':
      return ErrCode.ERR_CODE_ACCOUNT_TYPE_NOT_BINDED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}

/** 账号类型 */
export enum AccountType {
  ACCOUNT_TYPE_NONE = 0,
  /** ACCOUNT_TYPE_TEST - 测试账号 */
  ACCOUNT_TYPE_TEST = 1,
  /** ACCOUNT_TYPE_OPERATE - 运营账号 */
  ACCOUNT_TYPE_OPERATE = 2,
  /** ACCOUNT_TYPE_EXP - 体验账号 */
  ACCOUNT_TYPE_EXP = 3,
  UNRECOGNIZED = -1
}

export function accountTypeFromJSON(object: any): AccountType {
  switch (object) {
    case 0:
    case 'ACCOUNT_TYPE_NONE':
      return AccountType.ACCOUNT_TYPE_NONE;
    case 1:
    case 'ACCOUNT_TYPE_TEST':
      return AccountType.ACCOUNT_TYPE_TEST;
    case 2:
    case 'ACCOUNT_TYPE_OPERATE':
      return AccountType.ACCOUNT_TYPE_OPERATE;
    case 3:
    case 'ACCOUNT_TYPE_EXP':
      return AccountType.ACCOUNT_TYPE_EXP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountType.UNRECOGNIZED;
  }
}

export interface SetAccountTypeReq {
  /** 账号类型 */
  accountType: AccountType;
  /** 邮箱 */
  email: string;
}

export interface SetAccountTypeRsp {}

export interface QueryAccountTypeReq {}

export interface QueryAccountTypeRsp {
  /** 账号类型 */
  accountType: number;
  /** 邮箱 */
  email: string;
}

export interface ApplyMoneyReq {
  amount: number;
  /** 货币类型(1:结算,2:消费,3:币商,4:美分) */
  currency_type: number;
  /** 货币名称;仅展示用 */
  currency_name: string;
}

export interface ApplyMoneyRsp {}

export interface ListCurrencyTypeReq {
  /** 业务 */
  anm: string;
}

export interface ListCurrencyTypeRsp {
  items: CurrencyType[];
}

export interface CurrencyType {
  /** 货币类型(1:结算,2:消费,3:币商,4:美分) */
  currency_type: number;
  /** 货币名称 */
  currency_name: string;
}

function createBaseSetAccountTypeReq(): SetAccountTypeReq {
  return { accountType: 0, email: '' };
}

export const SetAccountTypeReq: MessageFns<SetAccountTypeReq> = {
  fromJSON(object: any): SetAccountTypeReq {
    return {
      accountType: isSet(object.accountType) ? accountTypeFromJSON(object.accountType) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : ''
    };
  },

  create<I extends Exact<DeepPartial<SetAccountTypeReq>, I>>(base?: I): SetAccountTypeReq {
    return SetAccountTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetAccountTypeReq>, I>>(object: I): SetAccountTypeReq {
    const message = createBaseSetAccountTypeReq();
    message.accountType = object.accountType ?? 0;
    message.email = object.email ?? '';
    return message;
  }
};

function createBaseSetAccountTypeRsp(): SetAccountTypeRsp {
  return {};
}

export const SetAccountTypeRsp: MessageFns<SetAccountTypeRsp> = {
  fromJSON(_: any): SetAccountTypeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SetAccountTypeRsp>, I>>(base?: I): SetAccountTypeRsp {
    return SetAccountTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SetAccountTypeRsp>, I>>(_: I): SetAccountTypeRsp {
    const message = createBaseSetAccountTypeRsp();
    return message;
  }
};

function createBaseQueryAccountTypeReq(): QueryAccountTypeReq {
  return {};
}

export const QueryAccountTypeReq: MessageFns<QueryAccountTypeReq> = {
  fromJSON(_: any): QueryAccountTypeReq {
    return {};
  },

  create<I extends Exact<DeepPartial<QueryAccountTypeReq>, I>>(base?: I): QueryAccountTypeReq {
    return QueryAccountTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryAccountTypeReq>, I>>(_: I): QueryAccountTypeReq {
    const message = createBaseQueryAccountTypeReq();
    return message;
  }
};

function createBaseQueryAccountTypeRsp(): QueryAccountTypeRsp {
  return { accountType: 0, email: '' };
}

export const QueryAccountTypeRsp: MessageFns<QueryAccountTypeRsp> = {
  fromJSON(object: any): QueryAccountTypeRsp {
    return {
      accountType: isSet(object.accountType) ? globalThis.Number(object.accountType) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : ''
    };
  },

  create<I extends Exact<DeepPartial<QueryAccountTypeRsp>, I>>(base?: I): QueryAccountTypeRsp {
    return QueryAccountTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryAccountTypeRsp>, I>>(object: I): QueryAccountTypeRsp {
    const message = createBaseQueryAccountTypeRsp();
    message.accountType = object.accountType ?? 0;
    message.email = object.email ?? '';
    return message;
  }
};

function createBaseApplyMoneyReq(): ApplyMoneyReq {
  return { amount: 0, currency_type: 0, currency_name: '' };
}

export const ApplyMoneyReq: MessageFns<ApplyMoneyReq> = {
  fromJSON(object: any): ApplyMoneyReq {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency_type: isSet(object.currency_type) ? globalThis.Number(object.currency_type) : 0,
      currency_name: isSet(object.currency_name) ? globalThis.String(object.currency_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<ApplyMoneyReq>, I>>(base?: I): ApplyMoneyReq {
    return ApplyMoneyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyMoneyReq>, I>>(object: I): ApplyMoneyReq {
    const message = createBaseApplyMoneyReq();
    message.amount = object.amount ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.currency_name = object.currency_name ?? '';
    return message;
  }
};

function createBaseApplyMoneyRsp(): ApplyMoneyRsp {
  return {};
}

export const ApplyMoneyRsp: MessageFns<ApplyMoneyRsp> = {
  fromJSON(_: any): ApplyMoneyRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ApplyMoneyRsp>, I>>(base?: I): ApplyMoneyRsp {
    return ApplyMoneyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplyMoneyRsp>, I>>(_: I): ApplyMoneyRsp {
    const message = createBaseApplyMoneyRsp();
    return message;
  }
};

function createBaseListCurrencyTypeReq(): ListCurrencyTypeReq {
  return { anm: '' };
}

export const ListCurrencyTypeReq: MessageFns<ListCurrencyTypeReq> = {
  fromJSON(object: any): ListCurrencyTypeReq {
    return { anm: isSet(object.anm) ? globalThis.String(object.anm) : '' };
  },

  create<I extends Exact<DeepPartial<ListCurrencyTypeReq>, I>>(base?: I): ListCurrencyTypeReq {
    return ListCurrencyTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCurrencyTypeReq>, I>>(object: I): ListCurrencyTypeReq {
    const message = createBaseListCurrencyTypeReq();
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseListCurrencyTypeRsp(): ListCurrencyTypeRsp {
  return { items: [] };
}

export const ListCurrencyTypeRsp: MessageFns<ListCurrencyTypeRsp> = {
  fromJSON(object: any): ListCurrencyTypeRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => CurrencyType.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListCurrencyTypeRsp>, I>>(base?: I): ListCurrencyTypeRsp {
    return ListCurrencyTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCurrencyTypeRsp>, I>>(object: I): ListCurrencyTypeRsp {
    const message = createBaseListCurrencyTypeRsp();
    message.items = object.items?.map(e => CurrencyType.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCurrencyType(): CurrencyType {
  return { currency_type: 0, currency_name: '' };
}

export const CurrencyType: MessageFns<CurrencyType> = {
  fromJSON(object: any): CurrencyType {
    return {
      currency_type: isSet(object.currency_type) ? globalThis.Number(object.currency_type) : 0,
      currency_name: isSet(object.currency_name) ? globalThis.String(object.currency_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<CurrencyType>, I>>(base?: I): CurrencyType {
    return CurrencyType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CurrencyType>, I>>(object: I): CurrencyType {
    const message = createBaseCurrencyType();
    message.currency_type = object.currency_type ?? 0;
    message.currency_name = object.currency_name ?? '';
    return message;
  }
};

/**
 * OAccount 内部账号管理
 * serviceName: social.offical
 */
export type OAccountApiDefinition = typeof OAccountApiDefinition;
export const OAccountApiDefinition = {
  name: 'OAccountApi',
  fullName: 'comm.api.offical.OAccountApi',
  methods: {
    /** 绑定账号类型，没有解绑功能 */
    setAccountType: {
      name: 'SetAccountType',
      requestType: SetAccountTypeReq,
      requestStream: false,
      responseType: SetAccountTypeRsp,
      responseStream: false,
      options: {}
    },
    /** 查询账号类型 */
    querySetAccountType: {
      name: 'QuerySetAccountType',
      requestType: QueryAccountTypeReq,
      requestStream: false,
      responseType: QueryAccountTypeRsp,
      responseStream: false,
      options: {}
    },
    /** 申请货币 */
    applyMoney: {
      name: 'ApplyMoney',
      requestType: ApplyMoneyReq,
      requestStream: false,
      responseType: ApplyMoneyRsp,
      responseStream: false,
      options: {}
    },
    /** 查询货币类型列表 */
    listCurrencyType: {
      name: 'ListCurrencyType',
      requestType: ListCurrencyTypeReq,
      requestStream: false,
      responseType: ListCurrencyTypeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
