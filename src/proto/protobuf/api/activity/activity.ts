// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/activity/activity.proto

/* eslint-disable */
import { FollowErrCode, followErrCodeFromJSON } from '../adapter/model';
import { Page } from '../common/common';
import { BatchListTasksReq, BatchListTasksRsp, DoTaskReq, DoTaskRsp } from '../ctask/handler';
import {
  ActivityInfo,
  LotteryBarrage,
  LotteryConfig,
  LotteryRecord,
  LotteryRewardConfig,
  PKConfig,
  PKDetail,
  PresentationInfo,
  PrizePoolConfig,
  RankInfo,
  RewardItem,
  RoomInfo,
  RoomRecommendConfig,
  TaskConfig,
  TimeRange,
  UserInfo,
  UserLotteryInfo,
  UserRank
} from './common';

export const protobufPackage = 'comm.api.activity';

export enum ListRoomRecommendSortType {
  LIST_ROOM_RECOMMEND_SORT_TYPE_NONE = 0,
  /** LIST_ROOM_RECOMMEND_SORT_TYPE_RAND_LIVING - 直播中的随机排序 */
  LIST_ROOM_RECOMMEND_SORT_TYPE_RAND_LIVING = 1,
  UNRECOGNIZED = -1
}

export function listRoomRecommendSortTypeFromJSON(object: any): ListRoomRecommendSortType {
  switch (object) {
    case 0:
    case 'LIST_ROOM_RECOMMEND_SORT_TYPE_NONE':
      return ListRoomRecommendSortType.LIST_ROOM_RECOMMEND_SORT_TYPE_NONE;
    case 1:
    case 'LIST_ROOM_RECOMMEND_SORT_TYPE_RAND_LIVING':
      return ListRoomRecommendSortType.LIST_ROOM_RECOMMEND_SORT_TYPE_RAND_LIVING;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ListRoomRecommendSortType.UNRECOGNIZED;
  }
}

/** 批量根据ID获取奖励配置项信息请求 */
export interface ListRewardItemByIdReq {
  /** 奖励分类标识, 必传. */
  category_key: string;
  /** 根据奖励ID查询, 可选. */
  item_ids: string[];
}

/** 批量根据ID获取奖励配置项信息响应 */
export interface ListRewardItemByIdRsp {
  /** 奖励配置项列表 */
  items: RewardItem[];
}

export interface GetCountdownReq {
  activity_id: number;
  /** 榜单id */
  rank_id: number;
  /** 任务id */
  task_id: number;
  /** pk id */
  pk_id: number;
}

export interface GetCountdownRsp {
  /** 当前周期（没开始则下一周期）倒计时开始的时间戳 */
  cycle_begin_ts: number;
  /** 当前周期（没开始则下一周期）倒计时结束时间戳 */
  cycle_end_ts: number;
}

export interface GetPkgConfigByDomainReq {
  domain: string;
}

export interface GetPkgConfigByDomainRsp {
  /** 包名 */
  pkg_name: string;
  /** 汇率,目前只有dating用来换算展示的货币 */
  score_ratio: number;
}

export interface FollowUserReq {
  uid: number;
  target_uid: number;
}

export interface FollowUserRsp {
  /** 0: 表示成功 */
  error_code: FollowErrCode;
  error_msg: string;
}

export interface FollowRoomReq {
  uid: number;
  target_room_id: number;
}

export interface FollowRoomRsp {
  /** 0: 表示成功 */
  error_code: FollowErrCode;
  error_msg: string;
}

export interface ListRoomRecommendInfoReq {
  page: Page | undefined;
  activity_id: number;
  room_recommend_config_id: number;
  sort_type: ListRoomRecommendSortType;
}

export interface ListRoomRecommendInfoRsp {
  page: Page | undefined;
  info: RoomRecommendInfo | undefined;
}

export interface BatchGetUserLotteryInfosReq {
  lottery_ids: number[];
}

export interface BatchGetUserLotteryInfosRsp {
  list: UserLotteryInfo[];
}

export interface LotteryReq {
  lottery_id: number;
  /** 抽奖次数 */
  times: number;
}

export interface LotteryRsp {
  /** 剩余次数 */
  times: number;
  /** 中奖信息 */
  records: LotteryRecord[];
}

export interface ListLotteryBarrageReq {
  page: Page | undefined;
  lottery_id: number;
}

export interface ListLotteryBarrageRsp {
  page: Page | undefined;
  list: LotteryBarrage[];
}

export interface ListMyLotteryRecordReq {
  page: Page | undefined;
  lottery_id: number;
}

export interface ListMyLotteryRecordRsp {
  page: Page | undefined;
  records: LotteryRecord[];
}

/** 根据活动标识获取活动配置信息 */
export interface GetActivityByCodeReq {
  activity_code: string;
}

/** 根据活动标识获取活动配置信息 */
export interface GetActivityByCodeRsp {
  activity_info: ActivityInfo | undefined;
  rank_infos: RankInfo[];
  server_time: number;
}

/** 根据活动标识获取活动配置信息 */
export interface GetActivityByApplicationCodeReq {
  application_code: string;
}

/** 根据活动标识获取活动配置信息 */
export interface GetActivityByApplicationCodeRsp {
  activity_info: ActivityInfo | undefined;
  rank_infos: RankInfo[];
  server_time: number;
  task_infos: TaskConfig[];
  pk_infos: PKConfig[];
  lottery_infos: LotteryInfo[];
  room_recommend_infos: RoomRecommendInfo[];
  prize_pool_configs: PrizePoolConfig[];
}

export interface GetPKDetailsByPKIdsReq {
  pk_ids: number[];
}

export interface GetPKDetailsByPKIdsRsp {
  details: PKDetail[];
}

export interface LotteryInfo {
  config: LotteryConfig | undefined;
  reward_configs: LotteryRewardConfig[];
  relate_task_ids: number[];
}

export interface RoomRecommendInfo {
  config: RoomRecommendConfig | undefined;
  /** GetActivityByApplicationCode 的时候不返回 */
  items: RoomRecommendItem[];
}

export interface RoomRecommendItem {
  room_info: RoomInfo | undefined;
  user_info: UserInfo | undefined;
  /** 关注状态 */
  is_following: boolean;
  /** 是否在直播 */
  is_living: boolean;
  /** 房间描述 */
  room_desc: string;
}

export interface GetPresentationInfoByIdsReq {
  ids: number[];
}

export interface GetPresentationInfoByIdsRsp {
  list: PresentationInfo[];
}

export interface GetTaskHonorWallReq {
  /** 任务id */
  task_id: number;
  /** 偏移周期，取值<=0，0表示当前周期，-1表示上周期、-2表示前两个周期，以此类推 */
  cycle_offset: number;
  /** 指定查看的阶段（阶段索引值），不传则查全部 */
  stages: number[];
}

export interface GetTaskHonorWallRsp {
  /** 对应阶段的荣誉墙 */
  stage_wall: { [key: number]: UserRank };
  /** 周期 */
  cycle: TimeRange | undefined;
}

export interface GetTaskHonorWallRsp_StageWallEntry {
  key: number;
  value: UserRank | undefined;
}

/** 搜索指定奖励分类的奖励配置项请求 */
export interface SearchRewardItemReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 奖励分类标识, 必传. */
  category_key: string;
  /** 根据奖励ID模糊查询, 可选. */
  item_id: string;
  /** 根据奖励名称模糊查询, 可选. */
  item_name: string;
  /** 关键字模糊查询, 可能为英文 , 分割的多个奖励id，也可能是名称 可选. */
  keyword: string;
  /** 根据子分类的id来查 */
  sub_category_ids: string[];
  /** 按照货币类型筛选 */
  currencys: number[];
}

/** 搜索指定奖励分类的奖励配置项响应 */
export interface SearchRewardItemRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 奖励配置项列表 */
  items: RewardItem[];
}

export interface GetPrizePoolValueByIdReq {
  /** 奖池配置id */
  id: number;
  /** 类似于 GetRankReq.GetPara.cycle_key */
  cycle_key: string;
}

export interface GetPrizePoolValueByIdRsp {
  /** 奖池配置id */
  id: number;
  /** 类似于 GetRankReq.GetPara.cycle_key */
  cycle_key: string;
  /** 对应的奖池数量 */
  cur_pool_num: number;
}

function createBaseListRewardItemByIdReq(): ListRewardItemByIdReq {
  return { category_key: '', item_ids: [] };
}

export const ListRewardItemByIdReq: MessageFns<ListRewardItemByIdReq> = {
  fromJSON(object: any): ListRewardItemByIdReq {
    return {
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      item_ids: globalThis.Array.isArray(object?.item_ids) ? object.item_ids.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRewardItemByIdReq>, I>>(base?: I): ListRewardItemByIdReq {
    return ListRewardItemByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardItemByIdReq>, I>>(object: I): ListRewardItemByIdReq {
    const message = createBaseListRewardItemByIdReq();
    message.category_key = object.category_key ?? '';
    message.item_ids = object.item_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListRewardItemByIdRsp(): ListRewardItemByIdRsp {
  return { items: [] };
}

export const ListRewardItemByIdRsp: MessageFns<ListRewardItemByIdRsp> = {
  fromJSON(object: any): ListRewardItemByIdRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRewardItemByIdRsp>, I>>(base?: I): ListRewardItemByIdRsp {
    return ListRewardItemByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardItemByIdRsp>, I>>(object: I): ListRewardItemByIdRsp {
    const message = createBaseListRewardItemByIdRsp();
    message.items = object.items?.map(e => RewardItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetCountdownReq(): GetCountdownReq {
  return { activity_id: 0, rank_id: 0, task_id: 0, pk_id: 0 };
}

export const GetCountdownReq: MessageFns<GetCountdownReq> = {
  fromJSON(object: any): GetCountdownReq {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      pk_id: isSet(object.pk_id) ? globalThis.Number(object.pk_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetCountdownReq>, I>>(base?: I): GetCountdownReq {
    return GetCountdownReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCountdownReq>, I>>(object: I): GetCountdownReq {
    const message = createBaseGetCountdownReq();
    message.activity_id = object.activity_id ?? 0;
    message.rank_id = object.rank_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.pk_id = object.pk_id ?? 0;
    return message;
  }
};

function createBaseGetCountdownRsp(): GetCountdownRsp {
  return { cycle_begin_ts: 0, cycle_end_ts: 0 };
}

export const GetCountdownRsp: MessageFns<GetCountdownRsp> = {
  fromJSON(object: any): GetCountdownRsp {
    return {
      cycle_begin_ts: isSet(object.cycle_begin_ts) ? globalThis.Number(object.cycle_begin_ts) : 0,
      cycle_end_ts: isSet(object.cycle_end_ts) ? globalThis.Number(object.cycle_end_ts) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetCountdownRsp>, I>>(base?: I): GetCountdownRsp {
    return GetCountdownRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCountdownRsp>, I>>(object: I): GetCountdownRsp {
    const message = createBaseGetCountdownRsp();
    message.cycle_begin_ts = object.cycle_begin_ts ?? 0;
    message.cycle_end_ts = object.cycle_end_ts ?? 0;
    return message;
  }
};

function createBaseGetPkgConfigByDomainReq(): GetPkgConfigByDomainReq {
  return { domain: '' };
}

export const GetPkgConfigByDomainReq: MessageFns<GetPkgConfigByDomainReq> = {
  fromJSON(object: any): GetPkgConfigByDomainReq {
    return { domain: isSet(object.domain) ? globalThis.String(object.domain) : '' };
  },

  create<I extends Exact<DeepPartial<GetPkgConfigByDomainReq>, I>>(base?: I): GetPkgConfigByDomainReq {
    return GetPkgConfigByDomainReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPkgConfigByDomainReq>, I>>(object: I): GetPkgConfigByDomainReq {
    const message = createBaseGetPkgConfigByDomainReq();
    message.domain = object.domain ?? '';
    return message;
  }
};

function createBaseGetPkgConfigByDomainRsp(): GetPkgConfigByDomainRsp {
  return { pkg_name: '', score_ratio: 0 };
}

export const GetPkgConfigByDomainRsp: MessageFns<GetPkgConfigByDomainRsp> = {
  fromJSON(object: any): GetPkgConfigByDomainRsp {
    return {
      pkg_name: isSet(object.pkg_name) ? globalThis.String(object.pkg_name) : '',
      score_ratio: isSet(object.score_ratio) ? globalThis.Number(object.score_ratio) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetPkgConfigByDomainRsp>, I>>(base?: I): GetPkgConfigByDomainRsp {
    return GetPkgConfigByDomainRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPkgConfigByDomainRsp>, I>>(object: I): GetPkgConfigByDomainRsp {
    const message = createBaseGetPkgConfigByDomainRsp();
    message.pkg_name = object.pkg_name ?? '';
    message.score_ratio = object.score_ratio ?? 0;
    return message;
  }
};

function createBaseFollowUserReq(): FollowUserReq {
  return { uid: 0, target_uid: 0 };
}

export const FollowUserReq: MessageFns<FollowUserReq> = {
  fromJSON(object: any): FollowUserReq {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<FollowUserReq>, I>>(base?: I): FollowUserReq {
    return FollowUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowUserReq>, I>>(object: I): FollowUserReq {
    const message = createBaseFollowUserReq();
    message.uid = object.uid ?? 0;
    message.target_uid = object.target_uid ?? 0;
    return message;
  }
};

function createBaseFollowUserRsp(): FollowUserRsp {
  return { error_code: 0, error_msg: '' };
}

export const FollowUserRsp: MessageFns<FollowUserRsp> = {
  fromJSON(object: any): FollowUserRsp {
    return {
      error_code: isSet(object.error_code) ? followErrCodeFromJSON(object.error_code) : 0,
      error_msg: isSet(object.error_msg) ? globalThis.String(object.error_msg) : ''
    };
  },

  create<I extends Exact<DeepPartial<FollowUserRsp>, I>>(base?: I): FollowUserRsp {
    return FollowUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowUserRsp>, I>>(object: I): FollowUserRsp {
    const message = createBaseFollowUserRsp();
    message.error_code = object.error_code ?? 0;
    message.error_msg = object.error_msg ?? '';
    return message;
  }
};

function createBaseFollowRoomReq(): FollowRoomReq {
  return { uid: 0, target_room_id: 0 };
}

export const FollowRoomReq: MessageFns<FollowRoomReq> = {
  fromJSON(object: any): FollowRoomReq {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      target_room_id: isSet(object.target_room_id) ? globalThis.Number(object.target_room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<FollowRoomReq>, I>>(base?: I): FollowRoomReq {
    return FollowRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRoomReq>, I>>(object: I): FollowRoomReq {
    const message = createBaseFollowRoomReq();
    message.uid = object.uid ?? 0;
    message.target_room_id = object.target_room_id ?? 0;
    return message;
  }
};

function createBaseFollowRoomRsp(): FollowRoomRsp {
  return { error_code: 0, error_msg: '' };
}

export const FollowRoomRsp: MessageFns<FollowRoomRsp> = {
  fromJSON(object: any): FollowRoomRsp {
    return {
      error_code: isSet(object.error_code) ? followErrCodeFromJSON(object.error_code) : 0,
      error_msg: isSet(object.error_msg) ? globalThis.String(object.error_msg) : ''
    };
  },

  create<I extends Exact<DeepPartial<FollowRoomRsp>, I>>(base?: I): FollowRoomRsp {
    return FollowRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRoomRsp>, I>>(object: I): FollowRoomRsp {
    const message = createBaseFollowRoomRsp();
    message.error_code = object.error_code ?? 0;
    message.error_msg = object.error_msg ?? '';
    return message;
  }
};

function createBaseListRoomRecommendInfoReq(): ListRoomRecommendInfoReq {
  return { page: undefined, activity_id: 0, room_recommend_config_id: 0, sort_type: 0 };
}

export const ListRoomRecommendInfoReq: MessageFns<ListRoomRecommendInfoReq> = {
  fromJSON(object: any): ListRoomRecommendInfoReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      room_recommend_config_id: isSet(object.room_recommend_config_id)
        ? globalThis.Number(object.room_recommend_config_id)
        : 0,
      sort_type: isSet(object.sort_type) ? listRoomRecommendSortTypeFromJSON(object.sort_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRoomRecommendInfoReq>, I>>(base?: I): ListRoomRecommendInfoReq {
    return ListRoomRecommendInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomRecommendInfoReq>, I>>(object: I): ListRoomRecommendInfoReq {
    const message = createBaseListRoomRecommendInfoReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.activity_id = object.activity_id ?? 0;
    message.room_recommend_config_id = object.room_recommend_config_id ?? 0;
    message.sort_type = object.sort_type ?? 0;
    return message;
  }
};

function createBaseListRoomRecommendInfoRsp(): ListRoomRecommendInfoRsp {
  return { page: undefined, info: undefined };
}

export const ListRoomRecommendInfoRsp: MessageFns<ListRoomRecommendInfoRsp> = {
  fromJSON(object: any): ListRoomRecommendInfoRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      info: isSet(object.info) ? RoomRecommendInfo.fromJSON(object.info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListRoomRecommendInfoRsp>, I>>(base?: I): ListRoomRecommendInfoRsp {
    return ListRoomRecommendInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomRecommendInfoRsp>, I>>(object: I): ListRoomRecommendInfoRsp {
    const message = createBaseListRoomRecommendInfoRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.info =
      object.info !== undefined && object.info !== null ? RoomRecommendInfo.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseBatchGetUserLotteryInfosReq(): BatchGetUserLotteryInfosReq {
  return { lottery_ids: [] };
}

export const BatchGetUserLotteryInfosReq: MessageFns<BatchGetUserLotteryInfosReq> = {
  fromJSON(object: any): BatchGetUserLotteryInfosReq {
    return {
      lottery_ids: globalThis.Array.isArray(object?.lottery_ids)
        ? object.lottery_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserLotteryInfosReq>, I>>(base?: I): BatchGetUserLotteryInfosReq {
    return BatchGetUserLotteryInfosReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserLotteryInfosReq>, I>>(object: I): BatchGetUserLotteryInfosReq {
    const message = createBaseBatchGetUserLotteryInfosReq();
    message.lottery_ids = object.lottery_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserLotteryInfosRsp(): BatchGetUserLotteryInfosRsp {
  return { list: [] };
}

export const BatchGetUserLotteryInfosRsp: MessageFns<BatchGetUserLotteryInfosRsp> = {
  fromJSON(object: any): BatchGetUserLotteryInfosRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => UserLotteryInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserLotteryInfosRsp>, I>>(base?: I): BatchGetUserLotteryInfosRsp {
    return BatchGetUserLotteryInfosRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserLotteryInfosRsp>, I>>(object: I): BatchGetUserLotteryInfosRsp {
    const message = createBaseBatchGetUserLotteryInfosRsp();
    message.list = object.list?.map(e => UserLotteryInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLotteryReq(): LotteryReq {
  return { lottery_id: 0, times: 0 };
}

export const LotteryReq: MessageFns<LotteryReq> = {
  fromJSON(object: any): LotteryReq {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      times: isSet(object.times) ? globalThis.Number(object.times) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryReq>, I>>(base?: I): LotteryReq {
    return LotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryReq>, I>>(object: I): LotteryReq {
    const message = createBaseLotteryReq();
    message.lottery_id = object.lottery_id ?? 0;
    message.times = object.times ?? 0;
    return message;
  }
};

function createBaseLotteryRsp(): LotteryRsp {
  return { times: 0, records: [] };
}

export const LotteryRsp: MessageFns<LotteryRsp> = {
  fromJSON(object: any): LotteryRsp {
    return {
      times: isSet(object.times) ? globalThis.Number(object.times) : 0,
      records: globalThis.Array.isArray(object?.records)
        ? object.records.map((e: any) => LotteryRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LotteryRsp>, I>>(base?: I): LotteryRsp {
    return LotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryRsp>, I>>(object: I): LotteryRsp {
    const message = createBaseLotteryRsp();
    message.times = object.times ?? 0;
    message.records = object.records?.map(e => LotteryRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListLotteryBarrageReq(): ListLotteryBarrageReq {
  return { page: undefined, lottery_id: 0 };
}

export const ListLotteryBarrageReq: MessageFns<ListLotteryBarrageReq> = {
  fromJSON(object: any): ListLotteryBarrageReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListLotteryBarrageReq>, I>>(base?: I): ListLotteryBarrageReq {
    return ListLotteryBarrageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLotteryBarrageReq>, I>>(object: I): ListLotteryBarrageReq {
    const message = createBaseListLotteryBarrageReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseListLotteryBarrageRsp(): ListLotteryBarrageRsp {
  return { page: undefined, list: [] };
}

export const ListLotteryBarrageRsp: MessageFns<ListLotteryBarrageRsp> = {
  fromJSON(object: any): ListLotteryBarrageRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => LotteryBarrage.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListLotteryBarrageRsp>, I>>(base?: I): ListLotteryBarrageRsp {
    return ListLotteryBarrageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLotteryBarrageRsp>, I>>(object: I): ListLotteryBarrageRsp {
    const message = createBaseListLotteryBarrageRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => LotteryBarrage.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListMyLotteryRecordReq(): ListMyLotteryRecordReq {
  return { page: undefined, lottery_id: 0 };
}

export const ListMyLotteryRecordReq: MessageFns<ListMyLotteryRecordReq> = {
  fromJSON(object: any): ListMyLotteryRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListMyLotteryRecordReq>, I>>(base?: I): ListMyLotteryRecordReq {
    return ListMyLotteryRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMyLotteryRecordReq>, I>>(object: I): ListMyLotteryRecordReq {
    const message = createBaseListMyLotteryRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseListMyLotteryRecordRsp(): ListMyLotteryRecordRsp {
  return { page: undefined, records: [] };
}

export const ListMyLotteryRecordRsp: MessageFns<ListMyLotteryRecordRsp> = {
  fromJSON(object: any): ListMyLotteryRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      records: globalThis.Array.isArray(object?.records)
        ? object.records.map((e: any) => LotteryRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListMyLotteryRecordRsp>, I>>(base?: I): ListMyLotteryRecordRsp {
    return ListMyLotteryRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMyLotteryRecordRsp>, I>>(object: I): ListMyLotteryRecordRsp {
    const message = createBaseListMyLotteryRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.records = object.records?.map(e => LotteryRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetActivityByCodeReq(): GetActivityByCodeReq {
  return { activity_code: '' };
}

export const GetActivityByCodeReq: MessageFns<GetActivityByCodeReq> = {
  fromJSON(object: any): GetActivityByCodeReq {
    return { activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '' };
  },

  create<I extends Exact<DeepPartial<GetActivityByCodeReq>, I>>(base?: I): GetActivityByCodeReq {
    return GetActivityByCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByCodeReq>, I>>(object: I): GetActivityByCodeReq {
    const message = createBaseGetActivityByCodeReq();
    message.activity_code = object.activity_code ?? '';
    return message;
  }
};

function createBaseGetActivityByCodeRsp(): GetActivityByCodeRsp {
  return { activity_info: undefined, rank_infos: [], server_time: 0 };
}

export const GetActivityByCodeRsp: MessageFns<GetActivityByCodeRsp> = {
  fromJSON(object: any): GetActivityByCodeRsp {
    return {
      activity_info: isSet(object.activity_info) ? ActivityInfo.fromJSON(object.activity_info) : undefined,
      rank_infos: globalThis.Array.isArray(object?.rank_infos)
        ? object.rank_infos.map((e: any) => RankInfo.fromJSON(e))
        : [],
      server_time: isSet(object.server_time) ? globalThis.Number(object.server_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityByCodeRsp>, I>>(base?: I): GetActivityByCodeRsp {
    return GetActivityByCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByCodeRsp>, I>>(object: I): GetActivityByCodeRsp {
    const message = createBaseGetActivityByCodeRsp();
    message.activity_info =
      object.activity_info !== undefined && object.activity_info !== null
        ? ActivityInfo.fromPartial(object.activity_info)
        : undefined;
    message.rank_infos = object.rank_infos?.map(e => RankInfo.fromPartial(e)) || [];
    message.server_time = object.server_time ?? 0;
    return message;
  }
};

function createBaseGetActivityByApplicationCodeReq(): GetActivityByApplicationCodeReq {
  return { application_code: '' };
}

export const GetActivityByApplicationCodeReq: MessageFns<GetActivityByApplicationCodeReq> = {
  fromJSON(object: any): GetActivityByApplicationCodeReq {
    return { application_code: isSet(object.application_code) ? globalThis.String(object.application_code) : '' };
  },

  create<I extends Exact<DeepPartial<GetActivityByApplicationCodeReq>, I>>(base?: I): GetActivityByApplicationCodeReq {
    return GetActivityByApplicationCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByApplicationCodeReq>, I>>(
    object: I
  ): GetActivityByApplicationCodeReq {
    const message = createBaseGetActivityByApplicationCodeReq();
    message.application_code = object.application_code ?? '';
    return message;
  }
};

function createBaseGetActivityByApplicationCodeRsp(): GetActivityByApplicationCodeRsp {
  return {
    activity_info: undefined,
    rank_infos: [],
    server_time: 0,
    task_infos: [],
    pk_infos: [],
    lottery_infos: [],
    room_recommend_infos: [],
    prize_pool_configs: []
  };
}

export const GetActivityByApplicationCodeRsp: MessageFns<GetActivityByApplicationCodeRsp> = {
  fromJSON(object: any): GetActivityByApplicationCodeRsp {
    return {
      activity_info: isSet(object.activity_info) ? ActivityInfo.fromJSON(object.activity_info) : undefined,
      rank_infos: globalThis.Array.isArray(object?.rank_infos)
        ? object.rank_infos.map((e: any) => RankInfo.fromJSON(e))
        : [],
      server_time: isSet(object.server_time) ? globalThis.Number(object.server_time) : 0,
      task_infos: globalThis.Array.isArray(object?.task_infos)
        ? object.task_infos.map((e: any) => TaskConfig.fromJSON(e))
        : [],
      pk_infos: globalThis.Array.isArray(object?.pk_infos) ? object.pk_infos.map((e: any) => PKConfig.fromJSON(e)) : [],
      lottery_infos: globalThis.Array.isArray(object?.lottery_infos)
        ? object.lottery_infos.map((e: any) => LotteryInfo.fromJSON(e))
        : [],
      room_recommend_infos: globalThis.Array.isArray(object?.room_recommend_infos)
        ? object.room_recommend_infos.map((e: any) => RoomRecommendInfo.fromJSON(e))
        : [],
      prize_pool_configs: globalThis.Array.isArray(object?.prize_pool_configs)
        ? object.prize_pool_configs.map((e: any) => PrizePoolConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetActivityByApplicationCodeRsp>, I>>(base?: I): GetActivityByApplicationCodeRsp {
    return GetActivityByApplicationCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByApplicationCodeRsp>, I>>(
    object: I
  ): GetActivityByApplicationCodeRsp {
    const message = createBaseGetActivityByApplicationCodeRsp();
    message.activity_info =
      object.activity_info !== undefined && object.activity_info !== null
        ? ActivityInfo.fromPartial(object.activity_info)
        : undefined;
    message.rank_infos = object.rank_infos?.map(e => RankInfo.fromPartial(e)) || [];
    message.server_time = object.server_time ?? 0;
    message.task_infos = object.task_infos?.map(e => TaskConfig.fromPartial(e)) || [];
    message.pk_infos = object.pk_infos?.map(e => PKConfig.fromPartial(e)) || [];
    message.lottery_infos = object.lottery_infos?.map(e => LotteryInfo.fromPartial(e)) || [];
    message.room_recommend_infos = object.room_recommend_infos?.map(e => RoomRecommendInfo.fromPartial(e)) || [];
    message.prize_pool_configs = object.prize_pool_configs?.map(e => PrizePoolConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetPKDetailsByPKIdsReq(): GetPKDetailsByPKIdsReq {
  return { pk_ids: [] };
}

export const GetPKDetailsByPKIdsReq: MessageFns<GetPKDetailsByPKIdsReq> = {
  fromJSON(object: any): GetPKDetailsByPKIdsReq {
    return {
      pk_ids: globalThis.Array.isArray(object?.pk_ids) ? object.pk_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetPKDetailsByPKIdsReq>, I>>(base?: I): GetPKDetailsByPKIdsReq {
    return GetPKDetailsByPKIdsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPKDetailsByPKIdsReq>, I>>(object: I): GetPKDetailsByPKIdsReq {
    const message = createBaseGetPKDetailsByPKIdsReq();
    message.pk_ids = object.pk_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetPKDetailsByPKIdsRsp(): GetPKDetailsByPKIdsRsp {
  return { details: [] };
}

export const GetPKDetailsByPKIdsRsp: MessageFns<GetPKDetailsByPKIdsRsp> = {
  fromJSON(object: any): GetPKDetailsByPKIdsRsp {
    return {
      details: globalThis.Array.isArray(object?.details) ? object.details.map((e: any) => PKDetail.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetPKDetailsByPKIdsRsp>, I>>(base?: I): GetPKDetailsByPKIdsRsp {
    return GetPKDetailsByPKIdsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPKDetailsByPKIdsRsp>, I>>(object: I): GetPKDetailsByPKIdsRsp {
    const message = createBaseGetPKDetailsByPKIdsRsp();
    message.details = object.details?.map(e => PKDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLotteryInfo(): LotteryInfo {
  return { config: undefined, reward_configs: [], relate_task_ids: [] };
}

export const LotteryInfo: MessageFns<LotteryInfo> = {
  fromJSON(object: any): LotteryInfo {
    return {
      config: isSet(object.config) ? LotteryConfig.fromJSON(object.config) : undefined,
      reward_configs: globalThis.Array.isArray(object?.reward_configs)
        ? object.reward_configs.map((e: any) => LotteryRewardConfig.fromJSON(e))
        : [],
      relate_task_ids: globalThis.Array.isArray(object?.relate_task_ids)
        ? object.relate_task_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LotteryInfo>, I>>(base?: I): LotteryInfo {
    return LotteryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryInfo>, I>>(object: I): LotteryInfo {
    const message = createBaseLotteryInfo();
    message.config =
      object.config !== undefined && object.config !== null ? LotteryConfig.fromPartial(object.config) : undefined;
    message.reward_configs = object.reward_configs?.map(e => LotteryRewardConfig.fromPartial(e)) || [];
    message.relate_task_ids = object.relate_task_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseRoomRecommendInfo(): RoomRecommendInfo {
  return { config: undefined, items: [] };
}

export const RoomRecommendInfo: MessageFns<RoomRecommendInfo> = {
  fromJSON(object: any): RoomRecommendInfo {
    return {
      config: isSet(object.config) ? RoomRecommendConfig.fromJSON(object.config) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RoomRecommendItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RoomRecommendInfo>, I>>(base?: I): RoomRecommendInfo {
    return RoomRecommendInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendInfo>, I>>(object: I): RoomRecommendInfo {
    const message = createBaseRoomRecommendInfo();
    message.config =
      object.config !== undefined && object.config !== null
        ? RoomRecommendConfig.fromPartial(object.config)
        : undefined;
    message.items = object.items?.map(e => RoomRecommendItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRoomRecommendItem(): RoomRecommendItem {
  return { room_info: undefined, user_info: undefined, is_following: false, is_living: false, room_desc: '' };
}

export const RoomRecommendItem: MessageFns<RoomRecommendItem> = {
  fromJSON(object: any): RoomRecommendItem {
    return {
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined,
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      is_following: isSet(object.is_following) ? globalThis.Boolean(object.is_following) : false,
      is_living: isSet(object.is_living) ? globalThis.Boolean(object.is_living) : false,
      room_desc: isSet(object.room_desc) ? globalThis.String(object.room_desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomRecommendItem>, I>>(base?: I): RoomRecommendItem {
    return RoomRecommendItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendItem>, I>>(object: I): RoomRecommendItem {
    const message = createBaseRoomRecommendItem();
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.is_following = object.is_following ?? false;
    message.is_living = object.is_living ?? false;
    message.room_desc = object.room_desc ?? '';
    return message;
  }
};

function createBaseGetPresentationInfoByIdsReq(): GetPresentationInfoByIdsReq {
  return { ids: [] };
}

export const GetPresentationInfoByIdsReq: MessageFns<GetPresentationInfoByIdsReq> = {
  fromJSON(object: any): GetPresentationInfoByIdsReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetPresentationInfoByIdsReq>, I>>(base?: I): GetPresentationInfoByIdsReq {
    return GetPresentationInfoByIdsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPresentationInfoByIdsReq>, I>>(object: I): GetPresentationInfoByIdsReq {
    const message = createBaseGetPresentationInfoByIdsReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetPresentationInfoByIdsRsp(): GetPresentationInfoByIdsRsp {
  return { list: [] };
}

export const GetPresentationInfoByIdsRsp: MessageFns<GetPresentationInfoByIdsRsp> = {
  fromJSON(object: any): GetPresentationInfoByIdsRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => PresentationInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetPresentationInfoByIdsRsp>, I>>(base?: I): GetPresentationInfoByIdsRsp {
    return GetPresentationInfoByIdsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPresentationInfoByIdsRsp>, I>>(object: I): GetPresentationInfoByIdsRsp {
    const message = createBaseGetPresentationInfoByIdsRsp();
    message.list = object.list?.map(e => PresentationInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetTaskHonorWallReq(): GetTaskHonorWallReq {
  return { task_id: 0, cycle_offset: 0, stages: [] };
}

export const GetTaskHonorWallReq: MessageFns<GetTaskHonorWallReq> = {
  fromJSON(object: any): GetTaskHonorWallReq {
    return {
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      cycle_offset: isSet(object.cycle_offset) ? globalThis.Number(object.cycle_offset) : 0,
      stages: globalThis.Array.isArray(object?.stages) ? object.stages.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetTaskHonorWallReq>, I>>(base?: I): GetTaskHonorWallReq {
    return GetTaskHonorWallReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskHonorWallReq>, I>>(object: I): GetTaskHonorWallReq {
    const message = createBaseGetTaskHonorWallReq();
    message.task_id = object.task_id ?? 0;
    message.cycle_offset = object.cycle_offset ?? 0;
    message.stages = object.stages?.map(e => e) || [];
    return message;
  }
};

function createBaseGetTaskHonorWallRsp(): GetTaskHonorWallRsp {
  return { stage_wall: {}, cycle: undefined };
}

export const GetTaskHonorWallRsp: MessageFns<GetTaskHonorWallRsp> = {
  fromJSON(object: any): GetTaskHonorWallRsp {
    return {
      stage_wall: isObject(object.stage_wall)
        ? Object.entries(object.stage_wall).reduce<{ [key: number]: UserRank }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = UserRank.fromJSON(value);
            return acc;
          }, {})
        : {},
      cycle: isSet(object.cycle) ? TimeRange.fromJSON(object.cycle) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetTaskHonorWallRsp>, I>>(base?: I): GetTaskHonorWallRsp {
    return GetTaskHonorWallRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskHonorWallRsp>, I>>(object: I): GetTaskHonorWallRsp {
    const message = createBaseGetTaskHonorWallRsp();
    message.stage_wall = Object.entries(object.stage_wall ?? {}).reduce<{ [key: number]: UserRank }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = UserRank.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.cycle =
      object.cycle !== undefined && object.cycle !== null ? TimeRange.fromPartial(object.cycle) : undefined;
    return message;
  }
};

function createBaseGetTaskHonorWallRsp_StageWallEntry(): GetTaskHonorWallRsp_StageWallEntry {
  return { key: 0, value: undefined };
}

export const GetTaskHonorWallRsp_StageWallEntry: MessageFns<GetTaskHonorWallRsp_StageWallEntry> = {
  fromJSON(object: any): GetTaskHonorWallRsp_StageWallEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? UserRank.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetTaskHonorWallRsp_StageWallEntry>, I>>(
    base?: I
  ): GetTaskHonorWallRsp_StageWallEntry {
    return GetTaskHonorWallRsp_StageWallEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskHonorWallRsp_StageWallEntry>, I>>(
    object: I
  ): GetTaskHonorWallRsp_StageWallEntry {
    const message = createBaseGetTaskHonorWallRsp_StageWallEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? UserRank.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseSearchRewardItemReq(): SearchRewardItemReq {
  return {
    page: undefined,
    category_key: '',
    item_id: '',
    item_name: '',
    keyword: '',
    sub_category_ids: [],
    currencys: []
  };
}

export const SearchRewardItemReq: MessageFns<SearchRewardItemReq> = {
  fromJSON(object: any): SearchRewardItemReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : '',
      sub_category_ids: globalThis.Array.isArray(object?.sub_category_ids)
        ? object.sub_category_ids.map((e: any) => globalThis.String(e))
        : [],
      currencys: globalThis.Array.isArray(object?.currencys)
        ? object.currencys.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRewardItemReq>, I>>(base?: I): SearchRewardItemReq {
    return SearchRewardItemReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRewardItemReq>, I>>(object: I): SearchRewardItemReq {
    const message = createBaseSearchRewardItemReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_key = object.category_key ?? '';
    message.item_id = object.item_id ?? '';
    message.item_name = object.item_name ?? '';
    message.keyword = object.keyword ?? '';
    message.sub_category_ids = object.sub_category_ids?.map(e => e) || [];
    message.currencys = object.currencys?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRewardItemRsp(): SearchRewardItemRsp {
  return { page: undefined, items: [] };
}

export const SearchRewardItemRsp: MessageFns<SearchRewardItemRsp> = {
  fromJSON(object: any): SearchRewardItemRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRewardItemRsp>, I>>(base?: I): SearchRewardItemRsp {
    return SearchRewardItemRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRewardItemRsp>, I>>(object: I): SearchRewardItemRsp {
    const message = createBaseSearchRewardItemRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => RewardItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetPrizePoolValueByIdReq(): GetPrizePoolValueByIdReq {
  return { id: 0, cycle_key: '' };
}

export const GetPrizePoolValueByIdReq: MessageFns<GetPrizePoolValueByIdReq> = {
  fromJSON(object: any): GetPrizePoolValueByIdReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      cycle_key: isSet(object.cycle_key) ? globalThis.String(object.cycle_key) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetPrizePoolValueByIdReq>, I>>(base?: I): GetPrizePoolValueByIdReq {
    return GetPrizePoolValueByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrizePoolValueByIdReq>, I>>(object: I): GetPrizePoolValueByIdReq {
    const message = createBaseGetPrizePoolValueByIdReq();
    message.id = object.id ?? 0;
    message.cycle_key = object.cycle_key ?? '';
    return message;
  }
};

function createBaseGetPrizePoolValueByIdRsp(): GetPrizePoolValueByIdRsp {
  return { id: 0, cycle_key: '', cur_pool_num: 0 };
}

export const GetPrizePoolValueByIdRsp: MessageFns<GetPrizePoolValueByIdRsp> = {
  fromJSON(object: any): GetPrizePoolValueByIdRsp {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      cycle_key: isSet(object.cycle_key) ? globalThis.String(object.cycle_key) : '',
      cur_pool_num: isSet(object.cur_pool_num) ? globalThis.Number(object.cur_pool_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetPrizePoolValueByIdRsp>, I>>(base?: I): GetPrizePoolValueByIdRsp {
    return GetPrizePoolValueByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrizePoolValueByIdRsp>, I>>(object: I): GetPrizePoolValueByIdRsp {
    const message = createBaseGetPrizePoolValueByIdRsp();
    message.id = object.id ?? 0;
    message.cycle_key = object.cycle_key ?? '';
    message.cur_pool_num = object.cur_pool_num ?? 0;
    return message;
  }
};

/**
 * 活动接口
 * smicro:spath=gitit.cc/social/components-service/social-activity/biz/activity/handler
 */
export type ActivityApiDefinition = typeof ActivityApiDefinition;
export const ActivityApiDefinition = {
  name: 'ActivityApi',
  fullName: 'comm.api.activity.ActivityApi',
  methods: {
    /** 根据活动标识获取活动配置信息 */
    getActivityByCode: {
      name: 'GetActivityByCode',
      requestType: GetActivityByCodeReq,
      requestStream: false,
      responseType: GetActivityByCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 根据活动标识获取活动配置信息 */
    getActivityByApplicationCode: {
      name: 'GetActivityByApplicationCode',
      requestType: GetActivityByApplicationCodeReq,
      requestStream: false,
      responseType: GetActivityByApplicationCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 根据pk id获取pk信息 */
    getPKDetailsByPKIds: {
      name: 'GetPKDetailsByPKIds',
      requestType: GetPKDetailsByPKIdsReq,
      requestStream: false,
      responseType: GetPKDetailsByPKIdsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取抽奖记录 */
    listMyLotteryRecord: {
      name: 'ListMyLotteryRecord',
      requestType: ListMyLotteryRecordReq,
      requestStream: false,
      responseType: ListMyLotteryRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 抽奖结果弹幕，top n条中奖信息 */
    listLotteryBarrage: {
      name: 'ListLotteryBarrage',
      requestType: ListLotteryBarrageReq,
      requestStream: false,
      responseType: ListLotteryBarrageRsp,
      responseStream: false,
      options: {}
    },
    /** 抽奖 */
    lottery: {
      name: 'Lottery',
      requestType: LotteryReq,
      requestStream: false,
      responseType: LotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 根据抽奖id获取抽奖次数信息 */
    batchGetUserLotteryInfos: {
      name: 'BatchGetUserLotteryInfos',
      requestType: BatchGetUserLotteryInfosReq,
      requestStream: false,
      responseType: BatchGetUserLotteryInfosRsp,
      responseStream: false,
      options: {}
    },
    /** 根据id分页获取房间or房主信息 */
    listRoomRecommendInfo: {
      name: 'ListRoomRecommendInfo',
      requestType: ListRoomRecommendInfoReq,
      requestStream: false,
      responseType: ListRoomRecommendInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 关注用户 */
    followUser: {
      name: 'FollowUser',
      requestType: FollowUserReq,
      requestStream: false,
      responseType: FollowUserRsp,
      responseStream: false,
      options: {}
    },
    /** 关注房间 */
    followRoom: {
      name: 'FollowRoom',
      requestType: FollowRoomReq,
      requestStream: false,
      responseType: FollowRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 根据id获取展示组件信息 */
    getPresentationInfoByIds: {
      name: 'GetPresentationInfoByIds',
      requestType: GetPresentationInfoByIdsReq,
      requestStream: false,
      responseType: GetPresentationInfoByIdsRsp,
      responseStream: false,
      options: {}
    },
    /** 任务荣誉墙（类似榜单，但固定数量，不可分页） */
    getTaskHonorWall: {
      name: 'GetTaskHonorWall',
      requestType: GetTaskHonorWallReq,
      requestStream: false,
      responseType: GetTaskHonorWallRsp,
      responseStream: false,
      options: {}
    },
    /** 域名转包配置信息，目前dating需要用到 */
    getPkgConfigByDomain: {
      name: 'GetPkgConfigByDomain',
      requestType: GetPkgConfigByDomainReq,
      requestStream: false,
      responseType: GetPkgConfigByDomainRsp,
      responseStream: false,
      options: {}
    },
    /** 获取任务、榜单当前周期倒计时 */
    getCountdown: {
      name: 'GetCountdown',
      requestType: GetCountdownReq,
      requestStream: false,
      responseType: GetCountdownRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询（用于不是基于活动层次去组织任务的情况）,封装ctask.BatchListTasks */
    batchListTasksWrap: {
      name: 'BatchListTasksWrap',
      requestType: BatchListTasksReq,
      requestStream: false,
      responseType: BatchListTasksRsp,
      responseStream: false,
      options: {}
    },
    /** 批量根据ID获取奖励配置项信息 */
    listRewardItemById: {
      name: 'ListRewardItemById',
      requestType: ListRewardItemByIdReq,
      requestStream: false,
      responseType: ListRewardItemByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索指定奖励分类的奖励配置项, 实际上也是交由业务去实现, 活动中台只是中转这些请求. */
    searchRewardItem: {
      name: 'SearchRewardItem',
      requestType: SearchRewardItemReq,
      requestStream: false,
      responseType: SearchRewardItemRsp,
      responseStream: false,
      options: {}
    },
    /** 获取奖池 */
    getPrizePoolValueById: {
      name: 'GetPrizePoolValueById',
      requestType: GetPrizePoolValueByIdReq,
      requestStream: false,
      responseType: GetPrizePoolValueByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 做任务，封装ctask.DoTask */
    doTaskWrap: {
      name: 'DoTaskWrap',
      requestType: DoTaskReq,
      requestStream: false,
      responseType: DoTaskRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
