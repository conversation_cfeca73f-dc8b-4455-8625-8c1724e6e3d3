// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/activity/activity_management.proto

/* eslint-disable */
import { Lang<PERSON>, Time } from '../sconfig/model';
import { RankMajorType, rankMajorTypeFromJSON } from './common';

export const protobufPackage = 'comm.api.activity';

export enum RankNodeType {
  RANK_NODE_NONE = 0,
  RANK_NODE_CONTAINER = 1,
  RANK_NODE_RANK = 2,
  UNRECOGNIZED = -1
}

export function rankNodeTypeFromJSON(object: any): RankNodeType {
  switch (object) {
    case 0:
    case 'RANK_NODE_NONE':
      return RankNodeType.RANK_NODE_NONE;
    case 1:
    case 'RANK_NODE_CONTAINER':
      return RankNodeType.RANK_NODE_CONTAINER;
    case 2:
    case 'RANK_NODE_RANK':
      return RankNodeType.RANK_NODE_RANK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankNodeType.UNRECOGNIZED;
  }
}

/** 根据 code 获取活动配置 */
export interface GetActivityConfigByCodeReq {
  /** 活动 code */
  code: string;
}

/** 根据 code 获取活动配置 */
export interface GetActivityConfigByCodeRsp {
  /** 活动配置 */
  activity: ActivityConfig | undefined;
}

/**
 * 活动信息  rank:{code}:{key}:top1
 * 这里从 ActivityInfo 改成了 ActivityConfig 以避免和其他地方的 ActivityInfo 冲突
 */
export interface ActivityConfig {
  /** 活动code */
  code: string;
  /** 活动基础配置 */
  base_config: ActivityBaseConfig | undefined;
  /** 榜单的schema，后续还有抽奖、任务等 */
  rank_schemas: RankSchema[];
  /** IM notify的schema */
  im_notify_schemas: IMNotifySchema[];
  /** 榜单的奖励配置 */
  rank_leaf_config: { [key: string]: RankLeafConfig };
  /** 任务的配置 */
  task_schemas: TaskSchema | undefined;
}

export interface ActivityConfig_RankLeafConfigEntry {
  key: string;
  value: RankLeafConfig | undefined;
}

/** 活动基础配置信息 */
export interface ActivityBaseConfig {
  start_time: Time | undefined;
  end_time: Time | undefined;
}

export interface RankSchema {
  id: string;
  name: string;
  nodes: RankNode[];
}

export interface TaskSchema {
  task_action: string[];
  task_tag: string[];
}

export interface IMNotifySchema {
  key: string;
  name: string;
}

/** 榜单节点的schema */
export interface RankNode {
  id: string;
  key: string;
  type: RankNodeType;
  desc: string;
  rank_type: RankMajorType;
  childs: RankNode[];
}

export interface RankLeafConfig {
  /** 榜单的名称 */
  title: Langs | undefined;
  /** 榜单奖励 */
  rewards: RankReward[];
  ext: string;
}

export interface RankReward {
  /** 奖励区间的名称，比如Top1 */
  title: Langs | undefined;
  /** 开始过滤名次，0代表不过滤 */
  start_rank: number;
  /** 结束过滤名次，0代表不过滤 */
  stop_rank: number;
  /** 限制开始分数，0代表不过滤 */
  start_score: number;
  /** 限制结束分数，0代表不过滤 */
  end_score: number;
  /** 奖励 */
  reward: RewardPkg | undefined;
  /** 部分奖励要区分不同的分数，则放到这里来。 */
  rewards: ScoreReward[];
  /** 支持者榜单奖励 */
  sub_rewards: RankReward[];
  ext: string;
}

export interface ScoreReward {
  /** 限制开始分数，0代表不过滤 */
  start_score: number;
  /** 限制结束分数，0代表不过滤 */
  end_score: number;
  /** 对应的奖励包配置 */
  reward_pkg: RewardPkg | undefined;
}

export interface RewardPkg {
  /** 场景ID */
  sence_id: string;
  /** 0表示初次生成，否则表示修改 */
  pkg_id: string;
}

function createBaseGetActivityConfigByCodeReq(): GetActivityConfigByCodeReq {
  return { code: '' };
}

export const GetActivityConfigByCodeReq: MessageFns<GetActivityConfigByCodeReq> = {
  fromJSON(object: any): GetActivityConfigByCodeReq {
    return { code: isSet(object.code) ? globalThis.String(object.code) : '' };
  },

  create<I extends Exact<DeepPartial<GetActivityConfigByCodeReq>, I>>(base?: I): GetActivityConfigByCodeReq {
    return GetActivityConfigByCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityConfigByCodeReq>, I>>(object: I): GetActivityConfigByCodeReq {
    const message = createBaseGetActivityConfigByCodeReq();
    message.code = object.code ?? '';
    return message;
  }
};

function createBaseGetActivityConfigByCodeRsp(): GetActivityConfigByCodeRsp {
  return { activity: undefined };
}

export const GetActivityConfigByCodeRsp: MessageFns<GetActivityConfigByCodeRsp> = {
  fromJSON(object: any): GetActivityConfigByCodeRsp {
    return { activity: isSet(object.activity) ? ActivityConfig.fromJSON(object.activity) : undefined };
  },

  create<I extends Exact<DeepPartial<GetActivityConfigByCodeRsp>, I>>(base?: I): GetActivityConfigByCodeRsp {
    return GetActivityConfigByCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityConfigByCodeRsp>, I>>(object: I): GetActivityConfigByCodeRsp {
    const message = createBaseGetActivityConfigByCodeRsp();
    message.activity =
      object.activity !== undefined && object.activity !== null
        ? ActivityConfig.fromPartial(object.activity)
        : undefined;
    return message;
  }
};

function createBaseActivityConfig(): ActivityConfig {
  return {
    code: '',
    base_config: undefined,
    rank_schemas: [],
    im_notify_schemas: [],
    rank_leaf_config: {},
    task_schemas: undefined
  };
}

export const ActivityConfig: MessageFns<ActivityConfig> = {
  fromJSON(object: any): ActivityConfig {
    return {
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      base_config: isSet(object.base_config) ? ActivityBaseConfig.fromJSON(object.base_config) : undefined,
      rank_schemas: globalThis.Array.isArray(object?.rank_schemas)
        ? object.rank_schemas.map((e: any) => RankSchema.fromJSON(e))
        : [],
      im_notify_schemas: globalThis.Array.isArray(object?.im_notify_schemas)
        ? object.im_notify_schemas.map((e: any) => IMNotifySchema.fromJSON(e))
        : [],
      rank_leaf_config: isObject(object.rank_leaf_config)
        ? Object.entries(object.rank_leaf_config).reduce<{ [key: string]: RankLeafConfig }>((acc, [key, value]) => {
            acc[key] = RankLeafConfig.fromJSON(value);
            return acc;
          }, {})
        : {},
      task_schemas: isSet(object.task_schemas) ? TaskSchema.fromJSON(object.task_schemas) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ActivityConfig>, I>>(base?: I): ActivityConfig {
    return ActivityConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityConfig>, I>>(object: I): ActivityConfig {
    const message = createBaseActivityConfig();
    message.code = object.code ?? '';
    message.base_config =
      object.base_config !== undefined && object.base_config !== null
        ? ActivityBaseConfig.fromPartial(object.base_config)
        : undefined;
    message.rank_schemas = object.rank_schemas?.map(e => RankSchema.fromPartial(e)) || [];
    message.im_notify_schemas = object.im_notify_schemas?.map(e => IMNotifySchema.fromPartial(e)) || [];
    message.rank_leaf_config = Object.entries(object.rank_leaf_config ?? {}).reduce<{ [key: string]: RankLeafConfig }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = RankLeafConfig.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.task_schemas =
      object.task_schemas !== undefined && object.task_schemas !== null
        ? TaskSchema.fromPartial(object.task_schemas)
        : undefined;
    return message;
  }
};

function createBaseActivityConfig_RankLeafConfigEntry(): ActivityConfig_RankLeafConfigEntry {
  return { key: '', value: undefined };
}

export const ActivityConfig_RankLeafConfigEntry: MessageFns<ActivityConfig_RankLeafConfigEntry> = {
  fromJSON(object: any): ActivityConfig_RankLeafConfigEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? RankLeafConfig.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ActivityConfig_RankLeafConfigEntry>, I>>(
    base?: I
  ): ActivityConfig_RankLeafConfigEntry {
    return ActivityConfig_RankLeafConfigEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityConfig_RankLeafConfigEntry>, I>>(
    object: I
  ): ActivityConfig_RankLeafConfigEntry {
    const message = createBaseActivityConfig_RankLeafConfigEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? RankLeafConfig.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseActivityBaseConfig(): ActivityBaseConfig {
  return { start_time: undefined, end_time: undefined };
}

export const ActivityBaseConfig: MessageFns<ActivityBaseConfig> = {
  fromJSON(object: any): ActivityBaseConfig {
    return {
      start_time: isSet(object.start_time) ? Time.fromJSON(object.start_time) : undefined,
      end_time: isSet(object.end_time) ? Time.fromJSON(object.end_time) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ActivityBaseConfig>, I>>(base?: I): ActivityBaseConfig {
    return ActivityBaseConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityBaseConfig>, I>>(object: I): ActivityBaseConfig {
    const message = createBaseActivityBaseConfig();
    message.start_time =
      object.start_time !== undefined && object.start_time !== null ? Time.fromPartial(object.start_time) : undefined;
    message.end_time =
      object.end_time !== undefined && object.end_time !== null ? Time.fromPartial(object.end_time) : undefined;
    return message;
  }
};

function createBaseRankSchema(): RankSchema {
  return { id: '', name: '', nodes: [] };
}

export const RankSchema: MessageFns<RankSchema> = {
  fromJSON(object: any): RankSchema {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      nodes: globalThis.Array.isArray(object?.nodes) ? object.nodes.map((e: any) => RankNode.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RankSchema>, I>>(base?: I): RankSchema {
    return RankSchema.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankSchema>, I>>(object: I): RankSchema {
    const message = createBaseRankSchema();
    message.id = object.id ?? '';
    message.name = object.name ?? '';
    message.nodes = object.nodes?.map(e => RankNode.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTaskSchema(): TaskSchema {
  return { task_action: [], task_tag: [] };
}

export const TaskSchema: MessageFns<TaskSchema> = {
  fromJSON(object: any): TaskSchema {
    return {
      task_action: globalThis.Array.isArray(object?.task_action)
        ? object.task_action.map((e: any) => globalThis.String(e))
        : [],
      task_tag: globalThis.Array.isArray(object?.task_tag) ? object.task_tag.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<TaskSchema>, I>>(base?: I): TaskSchema {
    return TaskSchema.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskSchema>, I>>(object: I): TaskSchema {
    const message = createBaseTaskSchema();
    message.task_action = object.task_action?.map(e => e) || [];
    message.task_tag = object.task_tag?.map(e => e) || [];
    return message;
  }
};

function createBaseIMNotifySchema(): IMNotifySchema {
  return { key: '', name: '' };
}

export const IMNotifySchema: MessageFns<IMNotifySchema> = {
  fromJSON(object: any): IMNotifySchema {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<IMNotifySchema>, I>>(base?: I): IMNotifySchema {
    return IMNotifySchema.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotifySchema>, I>>(object: I): IMNotifySchema {
    const message = createBaseIMNotifySchema();
    message.key = object.key ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseRankNode(): RankNode {
  return { id: '', key: '', type: 0, desc: '', rank_type: 0, childs: [] };
}

export const RankNode: MessageFns<RankNode> = {
  fromJSON(object: any): RankNode {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      type: isSet(object.type) ? rankNodeTypeFromJSON(object.type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      rank_type: isSet(object.rank_type) ? rankMajorTypeFromJSON(object.rank_type) : 0,
      childs: globalThis.Array.isArray(object?.childs) ? object.childs.map((e: any) => RankNode.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RankNode>, I>>(base?: I): RankNode {
    return RankNode.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankNode>, I>>(object: I): RankNode {
    const message = createBaseRankNode();
    message.id = object.id ?? '';
    message.key = object.key ?? '';
    message.type = object.type ?? 0;
    message.desc = object.desc ?? '';
    message.rank_type = object.rank_type ?? 0;
    message.childs = object.childs?.map(e => RankNode.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRankLeafConfig(): RankLeafConfig {
  return { title: undefined, rewards: [], ext: '' };
}

export const RankLeafConfig: MessageFns<RankLeafConfig> = {
  fromJSON(object: any): RankLeafConfig {
    return {
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => RankReward.fromJSON(e)) : [],
      ext: isSet(object.ext) ? globalThis.String(object.ext) : ''
    };
  },

  create<I extends Exact<DeepPartial<RankLeafConfig>, I>>(base?: I): RankLeafConfig {
    return RankLeafConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankLeafConfig>, I>>(object: I): RankLeafConfig {
    const message = createBaseRankLeafConfig();
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.rewards = object.rewards?.map(e => RankReward.fromPartial(e)) || [];
    message.ext = object.ext ?? '';
    return message;
  }
};

function createBaseRankReward(): RankReward {
  return {
    title: undefined,
    start_rank: 0,
    stop_rank: 0,
    start_score: 0,
    end_score: 0,
    reward: undefined,
    rewards: [],
    sub_rewards: [],
    ext: ''
  };
}

export const RankReward: MessageFns<RankReward> = {
  fromJSON(object: any): RankReward {
    return {
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      start_rank: isSet(object.start_rank) ? globalThis.Number(object.start_rank) : 0,
      stop_rank: isSet(object.stop_rank) ? globalThis.Number(object.stop_rank) : 0,
      start_score: isSet(object.start_score) ? globalThis.Number(object.start_score) : 0,
      end_score: isSet(object.end_score) ? globalThis.Number(object.end_score) : 0,
      reward: isSet(object.reward) ? RewardPkg.fromJSON(object.reward) : undefined,
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => ScoreReward.fromJSON(e)) : [],
      sub_rewards: globalThis.Array.isArray(object?.sub_rewards)
        ? object.sub_rewards.map((e: any) => RankReward.fromJSON(e))
        : [],
      ext: isSet(object.ext) ? globalThis.String(object.ext) : ''
    };
  },

  create<I extends Exact<DeepPartial<RankReward>, I>>(base?: I): RankReward {
    return RankReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankReward>, I>>(object: I): RankReward {
    const message = createBaseRankReward();
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.start_rank = object.start_rank ?? 0;
    message.stop_rank = object.stop_rank ?? 0;
    message.start_score = object.start_score ?? 0;
    message.end_score = object.end_score ?? 0;
    message.reward =
      object.reward !== undefined && object.reward !== null ? RewardPkg.fromPartial(object.reward) : undefined;
    message.rewards = object.rewards?.map(e => ScoreReward.fromPartial(e)) || [];
    message.sub_rewards = object.sub_rewards?.map(e => RankReward.fromPartial(e)) || [];
    message.ext = object.ext ?? '';
    return message;
  }
};

function createBaseScoreReward(): ScoreReward {
  return { start_score: 0, end_score: 0, reward_pkg: undefined };
}

export const ScoreReward: MessageFns<ScoreReward> = {
  fromJSON(object: any): ScoreReward {
    return {
      start_score: isSet(object.start_score) ? globalThis.Number(object.start_score) : 0,
      end_score: isSet(object.end_score) ? globalThis.Number(object.end_score) : 0,
      reward_pkg: isSet(object.reward_pkg) ? RewardPkg.fromJSON(object.reward_pkg) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ScoreReward>, I>>(base?: I): ScoreReward {
    return ScoreReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScoreReward>, I>>(object: I): ScoreReward {
    const message = createBaseScoreReward();
    message.start_score = object.start_score ?? 0;
    message.end_score = object.end_score ?? 0;
    message.reward_pkg =
      object.reward_pkg !== undefined && object.reward_pkg !== null
        ? RewardPkg.fromPartial(object.reward_pkg)
        : undefined;
    return message;
  }
};

function createBaseRewardPkg(): RewardPkg {
  return { sence_id: '', pkg_id: '' };
}

export const RewardPkg: MessageFns<RewardPkg> = {
  fromJSON(object: any): RewardPkg {
    return {
      sence_id: isSet(object.sence_id) ? globalThis.String(object.sence_id) : '',
      pkg_id: isSet(object.pkg_id) ? globalThis.String(object.pkg_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardPkg>, I>>(base?: I): RewardPkg {
    return RewardPkg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardPkg>, I>>(object: I): RewardPkg {
    const message = createBaseRewardPkg();
    message.sence_id = object.sence_id ?? '';
    message.pkg_id = object.pkg_id ?? '';
    return message;
  }
};

/**
 * 活动管理平台对外协议
 * ServiceName: 一般是各个业务的主服务名，具体咨询业务
 * smicro:spath=gitit.cc/social/components/abase/abaseapi-handler.go
 */
export type ABaseApiDefinition = typeof ABaseApiDefinition;
export const ABaseApiDefinition = {
  name: 'ABaseApi',
  fullName: 'comm.api.activity.ABaseApi',
  methods: {
    /** 根据 code 获取活动配置 */
    getActivityConfigByCode: {
      name: 'GetActivityConfigByCode',
      requestType: GetActivityConfigByCodeReq,
      requestStream: false,
      responseType: GetActivityConfigByCodeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
