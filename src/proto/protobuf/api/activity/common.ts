// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/activity/common.proto

/* eslint-disable */
import { Currency, currencyFromJSON, RoomTag } from '../adapter/model';
import { Gender, genderFromJSON } from '../common/common-net';
import { UsingPrivilege } from '../privilege/v2/bag';
import { Rank } from './crank';

export const protobufPackage = 'comm.api.activity';

/** RankType 榜单类型 */
export enum RankType {
  /** RANK_TYPE_NONE - none */
  RANK_TYPE_NONE = 0,
  /** RANK_TYPE_USER - 送礼用户榜 */
  RANK_TYPE_USER = 10,
  /** RANK_TYPE_REC_USER - 收礼用户榜 */
  RANK_TYPE_REC_USER = 11,
  /** RANK_TYPE_ROOM - 房间榜单 */
  RANK_TYPE_ROOM = 12,
  /** RANK_TYPE_GUILD - 公会榜 */
  RANK_TYPE_GUILD = 13,
  /** RANK_TYPE_FAMILY - 家族榜 */
  RANK_TYPE_FAMILY = 14,
  /** RANK_TYPE_CP - CP榜 */
  RANK_TYPE_CP = 15,
  /** RANK_TYPE_REC_CP - CP收礼榜 */
  RANK_TYPE_REC_CP = 16,
  /** RANK_TYPE_CP_UNIQ - 去重CP榜, 这个会有问题，先去掉了 */
  RANK_TYPE_CP_UNIQ = 17,
  /** RANK_TYPE_CP_ANCHOR_USER - 主播用户cp榜 */
  RANK_TYPE_CP_ANCHOR_USER = 18,
  /** RANK_TYPE_CP_ANCHOR_USER_UNIQ - 去重主播用户cp榜 */
  RANK_TYPE_CP_ANCHOR_USER_UNIQ = 19,
  /** RANK_TYPE_GAME_USER_BET - 游戏用户投入榜 */
  RANK_TYPE_GAME_USER_BET = 20,
  /** RANK_TYPE_GAME_USER_WIN - 游戏用户获得榜 */
  RANK_TYPE_GAME_USER_WIN = 21,
  /** RANK_TYPE_TASK - 任务积分榜 */
  RANK_TYPE_TASK = 22,
  /** RANK_TYPE_NEW_FANS - 新增粉丝榜 */
  RANK_TYPE_NEW_FANS = 23,
  /** RANK_TYPE_ROOM_ACTIVITY - 房间活跃度榜 */
  RANK_TYPE_ROOM_ACTIVITY = 24,
  /** RANK_TYPE_LIVE_PROFIT - 直播收益榜 */
  RANK_TYPE_LIVE_PROFIT = 25,
  /** RANK_TYPE_RECHARGE - 充值榜 */
  RANK_TYPE_RECHARGE = 26,
  /** RANK_TYPE_ANCHOR_PROFIT - 主播个人积分收益 */
  RANK_TYPE_ANCHOR_PROFIT = 27,
  /** RANK_TYPE_GUILD_PROFIT - 公会积分收益 */
  RANK_TYPE_GUILD_PROFIT = 28,
  /** RANK_TYPE_USER_PROFIT - 用户个人收益  lucky：积分，dating：金豆 */
  RANK_TYPE_USER_PROFIT = 29,
  /** RANK_TYPE_USER_CONSUME - 用户个人消耗 dating：宝石 */
  RANK_TYPE_USER_CONSUME = 30,
  /** RANK_TYPE_USER_ONLINE_DURATION - 用户在线时长 */
  RANK_TYPE_USER_ONLINE_DURATION = 31,
  /** RANK_TYPE_USER_IN_ROOM_DURATION - 在房间内累计时长 */
  RANK_TYPE_USER_IN_ROOM_DURATION = 32,
  /** RANK_TYPE_USER_MIC_ON_DURATION - 用户上麦时长 */
  RANK_TYPE_USER_MIC_ON_DURATION = 33,
  /** RANK_TYPE_ROOM_GAME_BET - 房间游戏用户投入榜 */
  RANK_TYPE_ROOM_GAME_BET = 34,
  /** RANK_TYPE_ROOM_GAME_WIN - 房间游戏用户获得榜 */
  RANK_TYPE_ROOM_GAME_WIN = 35,
  /** RANK_TYPE_ROOM_OWNER_MIC_DURATION - 房主上麦时间 */
  RANK_TYPE_ROOM_OWNER_MIC_DURATION = 36,
  /** RANK_TYPE_ROOM_ON_MIC_UNIQ_NUM - 房间上麦人数（去重） */
  RANK_TYPE_ROOM_ON_MIC_UNIQ_NUM = 37,
  /** RANK_TYPE_ROOM_OWNER_UNIQ_SENDER - 给房主送礼人数（去重） */
  RANK_TYPE_ROOM_OWNER_UNIQ_SENDER = 38,
  /** RANK_TYPE_ROOM_PROFIT - 房间收益榜，目前只统计收礼流水 */
  RANK_TYPE_ROOM_PROFIT = 39,
  /** RANK_TYPE_SEND_FAMILY - 送礼家族榜 */
  RANK_TYPE_SEND_FAMILY = 40,
  /** RANK_TYPE_AVG_PAID_LINK_DURATION - 业务特定的榜单类型，其他业务不能复用的部分 */
  RANK_TYPE_AVG_PAID_LINK_DURATION = 101,
  /** RANK_TYPE_DATING_ANCHOR_SUPPORT - dating 应援榜 */
  RANK_TYPE_DATING_ANCHOR_SUPPORT = 102,
  /** RANK_TYPE_ECHO_2025_GUILD_MID_YEAR - echo 2025年中公会积分赛 */
  RANK_TYPE_ECHO_2025_GUILD_MID_YEAR = 103,
  /** RANK_TYPE_CUSTOMIZE_USER - 自定义用户榜 */
  RANK_TYPE_CUSTOMIZE_USER = 900,
  /** RANK_TYPE_CUSTOMIZE_USER_CURRENCY - 自定义用户榜，需要✖️汇率 */
  RANK_TYPE_CUSTOMIZE_USER_CURRENCY = 901,
  UNRECOGNIZED = -1
}

export function rankTypeFromJSON(object: any): RankType {
  switch (object) {
    case 0:
    case 'RANK_TYPE_NONE':
      return RankType.RANK_TYPE_NONE;
    case 10:
    case 'RANK_TYPE_USER':
      return RankType.RANK_TYPE_USER;
    case 11:
    case 'RANK_TYPE_REC_USER':
      return RankType.RANK_TYPE_REC_USER;
    case 12:
    case 'RANK_TYPE_ROOM':
      return RankType.RANK_TYPE_ROOM;
    case 13:
    case 'RANK_TYPE_GUILD':
      return RankType.RANK_TYPE_GUILD;
    case 14:
    case 'RANK_TYPE_FAMILY':
      return RankType.RANK_TYPE_FAMILY;
    case 15:
    case 'RANK_TYPE_CP':
      return RankType.RANK_TYPE_CP;
    case 16:
    case 'RANK_TYPE_REC_CP':
      return RankType.RANK_TYPE_REC_CP;
    case 17:
    case 'RANK_TYPE_CP_UNIQ':
      return RankType.RANK_TYPE_CP_UNIQ;
    case 18:
    case 'RANK_TYPE_CP_ANCHOR_USER':
      return RankType.RANK_TYPE_CP_ANCHOR_USER;
    case 19:
    case 'RANK_TYPE_CP_ANCHOR_USER_UNIQ':
      return RankType.RANK_TYPE_CP_ANCHOR_USER_UNIQ;
    case 20:
    case 'RANK_TYPE_GAME_USER_BET':
      return RankType.RANK_TYPE_GAME_USER_BET;
    case 21:
    case 'RANK_TYPE_GAME_USER_WIN':
      return RankType.RANK_TYPE_GAME_USER_WIN;
    case 22:
    case 'RANK_TYPE_TASK':
      return RankType.RANK_TYPE_TASK;
    case 23:
    case 'RANK_TYPE_NEW_FANS':
      return RankType.RANK_TYPE_NEW_FANS;
    case 24:
    case 'RANK_TYPE_ROOM_ACTIVITY':
      return RankType.RANK_TYPE_ROOM_ACTIVITY;
    case 25:
    case 'RANK_TYPE_LIVE_PROFIT':
      return RankType.RANK_TYPE_LIVE_PROFIT;
    case 26:
    case 'RANK_TYPE_RECHARGE':
      return RankType.RANK_TYPE_RECHARGE;
    case 27:
    case 'RANK_TYPE_ANCHOR_PROFIT':
      return RankType.RANK_TYPE_ANCHOR_PROFIT;
    case 28:
    case 'RANK_TYPE_GUILD_PROFIT':
      return RankType.RANK_TYPE_GUILD_PROFIT;
    case 29:
    case 'RANK_TYPE_USER_PROFIT':
      return RankType.RANK_TYPE_USER_PROFIT;
    case 30:
    case 'RANK_TYPE_USER_CONSUME':
      return RankType.RANK_TYPE_USER_CONSUME;
    case 31:
    case 'RANK_TYPE_USER_ONLINE_DURATION':
      return RankType.RANK_TYPE_USER_ONLINE_DURATION;
    case 32:
    case 'RANK_TYPE_USER_IN_ROOM_DURATION':
      return RankType.RANK_TYPE_USER_IN_ROOM_DURATION;
    case 33:
    case 'RANK_TYPE_USER_MIC_ON_DURATION':
      return RankType.RANK_TYPE_USER_MIC_ON_DURATION;
    case 34:
    case 'RANK_TYPE_ROOM_GAME_BET':
      return RankType.RANK_TYPE_ROOM_GAME_BET;
    case 35:
    case 'RANK_TYPE_ROOM_GAME_WIN':
      return RankType.RANK_TYPE_ROOM_GAME_WIN;
    case 36:
    case 'RANK_TYPE_ROOM_OWNER_MIC_DURATION':
      return RankType.RANK_TYPE_ROOM_OWNER_MIC_DURATION;
    case 37:
    case 'RANK_TYPE_ROOM_ON_MIC_UNIQ_NUM':
      return RankType.RANK_TYPE_ROOM_ON_MIC_UNIQ_NUM;
    case 38:
    case 'RANK_TYPE_ROOM_OWNER_UNIQ_SENDER':
      return RankType.RANK_TYPE_ROOM_OWNER_UNIQ_SENDER;
    case 39:
    case 'RANK_TYPE_ROOM_PROFIT':
      return RankType.RANK_TYPE_ROOM_PROFIT;
    case 40:
    case 'RANK_TYPE_SEND_FAMILY':
      return RankType.RANK_TYPE_SEND_FAMILY;
    case 101:
    case 'RANK_TYPE_AVG_PAID_LINK_DURATION':
      return RankType.RANK_TYPE_AVG_PAID_LINK_DURATION;
    case 102:
    case 'RANK_TYPE_DATING_ANCHOR_SUPPORT':
      return RankType.RANK_TYPE_DATING_ANCHOR_SUPPORT;
    case 103:
    case 'RANK_TYPE_ECHO_2025_GUILD_MID_YEAR':
      return RankType.RANK_TYPE_ECHO_2025_GUILD_MID_YEAR;
    case 900:
    case 'RANK_TYPE_CUSTOMIZE_USER':
      return RankType.RANK_TYPE_CUSTOMIZE_USER;
    case 901:
    case 'RANK_TYPE_CUSTOMIZE_USER_CURRENCY':
      return RankType.RANK_TYPE_CUSTOMIZE_USER_CURRENCY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankType.UNRECOGNIZED;
  }
}

/** RankSource 数据来源 */
export enum RankSource {
  /** RANK_SOURCE_NONE - none */
  RANK_SOURCE_NONE = 0,
  /** RANK_SOURCE_SEND_GIFT - 送礼 */
  RANK_SOURCE_SEND_GIFT = 10,
  /** RANK_SOURCE_GAME_CONSUME - 游戏消耗 */
  RANK_SOURCE_GAME_CONSUME = 20,
  UNRECOGNIZED = -1
}

export function rankSourceFromJSON(object: any): RankSource {
  switch (object) {
    case 0:
    case 'RANK_SOURCE_NONE':
      return RankSource.RANK_SOURCE_NONE;
    case 10:
    case 'RANK_SOURCE_SEND_GIFT':
      return RankSource.RANK_SOURCE_SEND_GIFT;
    case 20:
    case 'RANK_SOURCE_GAME_CONSUME':
      return RankSource.RANK_SOURCE_GAME_CONSUME;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankSource.UNRECOGNIZED;
  }
}

/** ScoreType 分数累加类型 */
export enum ScoreType {
  /** SCORE_TYPE_NONE - none */
  SCORE_TYPE_NONE = 0,
  /** SCORE_TYPE_SPEND - 礼物消耗价值，例如：金币，每个业务币种不同，按业务定义的消耗币种来定义 */
  SCORE_TYPE_SPEND = 10,
  /** SCORE_TYPE_INCOME - 礼物收益价值，例如：钻石、宝石，每个业务币种不同，按业务定义的消耗币种来定义 */
  SCORE_TYPE_INCOME = 11,
  /** SCORE_TYPE_GIFT_NUM - 按礼物数量累加 */
  SCORE_TYPE_GIFT_NUM = 12,
  /** SCORE_TYPE_GIFT_AMOUNT - 按礼物总额 */
  SCORE_TYPE_GIFT_AMOUNT = 13,
  UNRECOGNIZED = -1
}

export function scoreTypeFromJSON(object: any): ScoreType {
  switch (object) {
    case 0:
    case 'SCORE_TYPE_NONE':
      return ScoreType.SCORE_TYPE_NONE;
    case 10:
    case 'SCORE_TYPE_SPEND':
      return ScoreType.SCORE_TYPE_SPEND;
    case 11:
    case 'SCORE_TYPE_INCOME':
      return ScoreType.SCORE_TYPE_INCOME;
    case 12:
    case 'SCORE_TYPE_GIFT_NUM':
      return ScoreType.SCORE_TYPE_GIFT_NUM;
    case 13:
    case 'SCORE_TYPE_GIFT_AMOUNT':
      return ScoreType.SCORE_TYPE_GIFT_AMOUNT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ScoreType.UNRECOGNIZED;
  }
}

/** 榜单周期 */
export enum CycleType {
  /** CYCLE_TYPE_NONE - none */
  CYCLE_TYPE_NONE = 0,
  /** CYCLE_TYPE_HOUR - 小时榜 */
  CYCLE_TYPE_HOUR = 10,
  /** CYCLE_TYPE_DAILY - 日榜 */
  CYCLE_TYPE_DAILY = 20,
  /** CYCLE_TYPE_MONTH - 月榜 */
  CYCLE_TYPE_MONTH = 30,
  /** CYCLE_TYPE_YEAR - 年榜 */
  CYCLE_TYPE_YEAR = 40,
  /** CYCLE_TYPE_TOTAL - 总榜 */
  CYCLE_TYPE_TOTAL = 50,
  /** CYCLE_TYPE_WEEKLY_SUNDAY - 周榜-周日开始 */
  CYCLE_TYPE_WEEKLY_SUNDAY = 60,
  /** CYCLE_TYPE_WEEKLY_Monday - 周榜-周一开始 */
  CYCLE_TYPE_WEEKLY_Monday = 61,
  /** CYCLE_TYPE_WEEKLY_TUESDAY - 周榜-周二开始 */
  CYCLE_TYPE_WEEKLY_TUESDAY = 62,
  /** CYCLE_TYPE_WEEKLY_WEDNESDAY - 周榜-周三开始 */
  CYCLE_TYPE_WEEKLY_WEDNESDAY = 63,
  /** CYCLE_TYPE_WEEKLY_THURSDAY - 周榜-周四开始 */
  CYCLE_TYPE_WEEKLY_THURSDAY = 64,
  /** CYCLE_TYPE_WEEKLY_FRIDAY - 周榜-周五开始 */
  CYCLE_TYPE_WEEKLY_FRIDAY = 65,
  /** CYCLE_TYPE_WEEKLY_SATURDAY - 周榜-周六开始 */
  CYCLE_TYPE_WEEKLY_SATURDAY = 66,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_SUNDAY - 双周榜-周日开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_SUNDAY = 70,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_Monday - 双周榜-周一开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_Monday = 71,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_TUESDAY - 双周榜-周二开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_TUESDAY = 72,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_WEDNESDAY - 双周榜-周三开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_WEDNESDAY = 73,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_THURSDAY - 双周榜-周四开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_THURSDAY = 74,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_FRIDAY - 双周榜-周五开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_FRIDAY = 75,
  /** CYCLE_TYPE_DOUBLE_WEEKLY_SATURDAY - 双周榜-周六开始 */
  CYCLE_TYPE_DOUBLE_WEEKLY_SATURDAY = 76,
  UNRECOGNIZED = -1
}

export function cycleTypeFromJSON(object: any): CycleType {
  switch (object) {
    case 0:
    case 'CYCLE_TYPE_NONE':
      return CycleType.CYCLE_TYPE_NONE;
    case 10:
    case 'CYCLE_TYPE_HOUR':
      return CycleType.CYCLE_TYPE_HOUR;
    case 20:
    case 'CYCLE_TYPE_DAILY':
      return CycleType.CYCLE_TYPE_DAILY;
    case 30:
    case 'CYCLE_TYPE_MONTH':
      return CycleType.CYCLE_TYPE_MONTH;
    case 40:
    case 'CYCLE_TYPE_YEAR':
      return CycleType.CYCLE_TYPE_YEAR;
    case 50:
    case 'CYCLE_TYPE_TOTAL':
      return CycleType.CYCLE_TYPE_TOTAL;
    case 60:
    case 'CYCLE_TYPE_WEEKLY_SUNDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_SUNDAY;
    case 61:
    case 'CYCLE_TYPE_WEEKLY_Monday':
      return CycleType.CYCLE_TYPE_WEEKLY_Monday;
    case 62:
    case 'CYCLE_TYPE_WEEKLY_TUESDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_TUESDAY;
    case 63:
    case 'CYCLE_TYPE_WEEKLY_WEDNESDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_WEDNESDAY;
    case 64:
    case 'CYCLE_TYPE_WEEKLY_THURSDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_THURSDAY;
    case 65:
    case 'CYCLE_TYPE_WEEKLY_FRIDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_FRIDAY;
    case 66:
    case 'CYCLE_TYPE_WEEKLY_SATURDAY':
      return CycleType.CYCLE_TYPE_WEEKLY_SATURDAY;
    case 70:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_SUNDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_SUNDAY;
    case 71:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_Monday':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_Monday;
    case 72:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_TUESDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_TUESDAY;
    case 73:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_WEDNESDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_WEDNESDAY;
    case 74:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_THURSDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_THURSDAY;
    case 75:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_FRIDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_FRIDAY;
    case 76:
    case 'CYCLE_TYPE_DOUBLE_WEEKLY_SATURDAY':
      return CycleType.CYCLE_TYPE_DOUBLE_WEEKLY_SATURDAY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CycleType.UNRECOGNIZED;
  }
}

export enum TaskCycleSettleType {
  /** TASK_CYCLE_SETTLE_TYPE_NONE - 不结算 */
  TASK_CYCLE_SETTLE_TYPE_NONE = 0,
  /** TASK_CYCLE_SETTLE_TYPE_ONLY_MAX_STAGE - 只发放最大完成的 stage 奖励 */
  TASK_CYCLE_SETTLE_TYPE_ONLY_MAX_STAGE = 1,
  UNRECOGNIZED = -1
}

export function taskCycleSettleTypeFromJSON(object: any): TaskCycleSettleType {
  switch (object) {
    case 0:
    case 'TASK_CYCLE_SETTLE_TYPE_NONE':
      return TaskCycleSettleType.TASK_CYCLE_SETTLE_TYPE_NONE;
    case 1:
    case 'TASK_CYCLE_SETTLE_TYPE_ONLY_MAX_STAGE':
      return TaskCycleSettleType.TASK_CYCLE_SETTLE_TYPE_ONLY_MAX_STAGE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskCycleSettleType.UNRECOGNIZED;
  }
}

export enum TaskMode {
  TASK_MODE_NONE = 0,
  /** TASK_MODE_STAGE - 阶段任务 */
  TASK_MODE_STAGE = 1,
  /** TASK_MODE_CYCLE - 周期性任务 */
  TASK_MODE_CYCLE = 2,
  /** TASK_MODE_COMBINATION - 组合任务 */
  TASK_MODE_COMBINATION = 3,
  /** TASK_MODE_COMBINATION_STAGE - 组合 + 阶段任务 */
  TASK_MODE_COMBINATION_STAGE = 4,
  UNRECOGNIZED = -1
}

export function taskModeFromJSON(object: any): TaskMode {
  switch (object) {
    case 0:
    case 'TASK_MODE_NONE':
      return TaskMode.TASK_MODE_NONE;
    case 1:
    case 'TASK_MODE_STAGE':
      return TaskMode.TASK_MODE_STAGE;
    case 2:
    case 'TASK_MODE_CYCLE':
      return TaskMode.TASK_MODE_CYCLE;
    case 3:
    case 'TASK_MODE_COMBINATION':
      return TaskMode.TASK_MODE_COMBINATION;
    case 4:
    case 'TASK_MODE_COMBINATION_STAGE':
      return TaskMode.TASK_MODE_COMBINATION_STAGE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskMode.UNRECOGNIZED;
  }
}

/** TaskType 任务类型 */
export enum TaskType {
  /** TASK_TYPE_NONE - none */
  TASK_TYPE_NONE = 0,
  /** TASK_TYPE_GIFT_SEND_USER - 个人维度 */
  TASK_TYPE_GIFT_SEND_USER = 10,
  /** TASK_TYPE_GIFT_SEND_NUM - 送礼次数 */
  TASK_TYPE_GIFT_SEND_NUM = 11,
  /** TASK_TYPE_USER_OWN_ROOM_DURATION - 在自己房间内累计时长 */
  TASK_TYPE_USER_OWN_ROOM_DURATION = 12,
  /** TASK_TYPE_USER_IN_ROOM_DURATION - 在房间内累计时长 */
  TASK_TYPE_USER_IN_ROOM_DURATION = 13,
  /** TASK_TYPE_USER_ADD_FRIEND - 添加好友 */
  TASK_TYPE_USER_ADD_FRIEND = 14,
  /** TASK_TYPE_USER_SEND_PRIVATE_MESSAGE - 给好友发送信息 */
  TASK_TYPE_USER_SEND_PRIVATE_MESSAGE = 15,
  /** TASK_TYPE_OPEN_MIC_ON_DURATION - 上麦并且开麦时长 */
  TASK_TYPE_OPEN_MIC_ON_DURATION = 16,
  /** TASK_TYPE_USER_ONLINE_DURATION - 用户在线时长 */
  TASK_TYPE_USER_ONLINE_DURATION = 17,
  /** TASK_TYPE_USER_ONLINE_DAY - 用户在线天数 */
  TASK_TYPE_USER_ONLINE_DAY = 18,
  /** TASK_TYPE_USER_LIVE_PROFIT - 直播收益 */
  TASK_TYPE_USER_LIVE_PROFIT = 19,
  /** TASK_TYPE_GIFT_REC_USER - 收礼任务 */
  TASK_TYPE_GIFT_REC_USER = 20,
  /** TASK_TYPE_GIFT_REC_NUM - 收礼次数 */
  TASK_TYPE_GIFT_REC_NUM = 21,
  /** TASK_TYPE_USER_PROFIT - 用户收益 */
  TASK_TYPE_USER_PROFIT = 22,
  /** TASK_TYPE_USER_CONSUME - 用户消费 */
  TASK_TYPE_USER_CONSUME = 23,
  /** TASK_TYPE_RECHARGE_AMOUNT - 充值金额总数(累计) */
  TASK_TYPE_RECHARGE_AMOUNT = 30,
  /** TASK_TYPE_RECHARGE_DAY_SUM - 活动期间充值天数 */
  TASK_TYPE_RECHARGE_DAY_SUM = 31,
  /** TASK_TYPE_CONTINUOUS_RECHARGE_DAY - 活动期间连续充值天数 */
  TASK_TYPE_CONTINUOUS_RECHARGE_DAY = 32,
  /** TASK_TYPE_RECHARGE_TIMES - 活动期间充值次数 */
  TASK_TYPE_RECHARGE_TIMES = 33,
  /** TASK_TYPE_RECHARGE_SINGLE_AMOUNT - 单笔充值 */
  TASK_TYPE_RECHARGE_SINGLE_AMOUNT = 34,
  /** TASK_TYPE_MIC_ON_DURATION - 上麦时长 */
  TASK_TYPE_MIC_ON_DURATION = 40,
  /** TASK_TYPE_ACT_PAGE_VIEW_NUM - 活动页进入次数 */
  TASK_TYPE_ACT_PAGE_VIEW_NUM = 50,
  /** TASK_TYPE_USER_FOLLOW_ROOM_NUM - 用户关注房间数 */
  TASK_TYPE_USER_FOLLOW_ROOM_NUM = 60,
  /** TASK_TYPE_USER_CHECKIN - 签到 */
  TASK_TYPE_USER_CHECKIN = 61,
  /** TASK_TYPE_LOTTERY_TIMES - 抽奖次数 */
  TASK_TYPE_LOTTERY_TIMES = 62,
  /** TASK_TYPE_ROOM_FOLLOW_NUM - 房间维度 房主任务 */
  TASK_TYPE_ROOM_FOLLOW_NUM = 100,
  /** TASK_TYPE_ROOM_NEW_FOLLOW_NUM - 房间新增关注数 */
  TASK_TYPE_ROOM_NEW_FOLLOW_NUM = 101,
  /** TASK_TYPE_ROOM_SPEND_AMOUNT - 房间消费金额总数 */
  TASK_TYPE_ROOM_SPEND_AMOUNT = 102,
  /** TASK_TYPE_ROOM_MIC_ON_DURATION - 房间上麦总时长 */
  TASK_TYPE_ROOM_MIC_ON_DURATION = 103,
  /** TASK_TYPE_ROOM_LIVE_DURATION - 房间开播时长 */
  TASK_TYPE_ROOM_LIVE_DURATION = 104,
  /** TASK_TYPE_ROOM_EFFECTIVE_ON_MIC_NUM - 房间有效上麦人数 */
  TASK_TYPE_ROOM_EFFECTIVE_ON_MIC_NUM = 105,
  /** TASK_TYPE_ROOM_GIFT_REC_NUM - 房间收礼数量 */
  TASK_TYPE_ROOM_GIFT_REC_NUM = 106,
  /** TASK_TYPE_ROOM_GIFT_SENDER_NUM - 房间送礼人数 */
  TASK_TYPE_ROOM_GIFT_SENDER_NUM = 107,
  /** TASK_TYPE_ROOM_EFFECTIVE_CONTINUOUS_ON_MIC_NUM - 房间有效连续上麦人数，需要业务传 mic_seq */
  TASK_TYPE_ROOM_EFFECTIVE_CONTINUOUS_ON_MIC_NUM = 108,
  /** TASK_TYPE_ROOM_GIFT_RECEIVER_NUM - 房间收礼人数 */
  TASK_TYPE_ROOM_GIFT_RECEIVER_NUM = 109,
  /** TASK_TYPE_GAME_USER_BET - 游戏维度 */
  TASK_TYPE_GAME_USER_BET = 200,
  /** TASK_TYPE_GAME_USER_WIN - 游戏用户获得任务 */
  TASK_TYPE_GAME_USER_WIN = 201,
  /** TASK_TYPE_GAME_START - 游戏开始次数 */
  TASK_TYPE_GAME_START = 202,
  /** TASK_TYPE_GAME_ROUND_USER - 游戏局数,用户任务 */
  TASK_TYPE_GAME_ROUND_USER = 203,
  /** TASK_TYPE_GAME_ROUND_ROOM_OWNER - 游戏局数,房主任务 */
  TASK_TYPE_GAME_ROUND_ROOM_OWNER = 204,
  /** TASK_TYPE_GAME_BET_ROOM_OWNER - 房间游戏用户投入任务，房主任务 */
  TASK_TYPE_GAME_BET_ROOM_OWNER = 205,
  /** TASK_TYPE_GAME_WIN_ROOM_OWNER - 房间游戏用户获得任务，房主任务 */
  TASK_TYPE_GAME_WIN_ROOM_OWNER = 206,
  /** TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES - 业务个性化维度 */
  TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES = 400,
  /** TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES_CREATOR - 活动参与次数与总收入达到要求, 创建者获得奖励 */
  TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES_CREATOR = 401,
  /** TASK_TYPE_CUSTOMIZE - 自定义用户任务 */
  TASK_TYPE_CUSTOMIZE = 999,
  /** TASK_TYPE_CUSTOMIZE_CURRENCY - 自定义用户任务，需要✖️汇率，目前只有dating用到 */
  TASK_TYPE_CUSTOMIZE_CURRENCY = 1000,
  UNRECOGNIZED = -1
}

export function taskTypeFromJSON(object: any): TaskType {
  switch (object) {
    case 0:
    case 'TASK_TYPE_NONE':
      return TaskType.TASK_TYPE_NONE;
    case 10:
    case 'TASK_TYPE_GIFT_SEND_USER':
      return TaskType.TASK_TYPE_GIFT_SEND_USER;
    case 11:
    case 'TASK_TYPE_GIFT_SEND_NUM':
      return TaskType.TASK_TYPE_GIFT_SEND_NUM;
    case 12:
    case 'TASK_TYPE_USER_OWN_ROOM_DURATION':
      return TaskType.TASK_TYPE_USER_OWN_ROOM_DURATION;
    case 13:
    case 'TASK_TYPE_USER_IN_ROOM_DURATION':
      return TaskType.TASK_TYPE_USER_IN_ROOM_DURATION;
    case 14:
    case 'TASK_TYPE_USER_ADD_FRIEND':
      return TaskType.TASK_TYPE_USER_ADD_FRIEND;
    case 15:
    case 'TASK_TYPE_USER_SEND_PRIVATE_MESSAGE':
      return TaskType.TASK_TYPE_USER_SEND_PRIVATE_MESSAGE;
    case 16:
    case 'TASK_TYPE_OPEN_MIC_ON_DURATION':
      return TaskType.TASK_TYPE_OPEN_MIC_ON_DURATION;
    case 17:
    case 'TASK_TYPE_USER_ONLINE_DURATION':
      return TaskType.TASK_TYPE_USER_ONLINE_DURATION;
    case 18:
    case 'TASK_TYPE_USER_ONLINE_DAY':
      return TaskType.TASK_TYPE_USER_ONLINE_DAY;
    case 19:
    case 'TASK_TYPE_USER_LIVE_PROFIT':
      return TaskType.TASK_TYPE_USER_LIVE_PROFIT;
    case 20:
    case 'TASK_TYPE_GIFT_REC_USER':
      return TaskType.TASK_TYPE_GIFT_REC_USER;
    case 21:
    case 'TASK_TYPE_GIFT_REC_NUM':
      return TaskType.TASK_TYPE_GIFT_REC_NUM;
    case 22:
    case 'TASK_TYPE_USER_PROFIT':
      return TaskType.TASK_TYPE_USER_PROFIT;
    case 23:
    case 'TASK_TYPE_USER_CONSUME':
      return TaskType.TASK_TYPE_USER_CONSUME;
    case 30:
    case 'TASK_TYPE_RECHARGE_AMOUNT':
      return TaskType.TASK_TYPE_RECHARGE_AMOUNT;
    case 31:
    case 'TASK_TYPE_RECHARGE_DAY_SUM':
      return TaskType.TASK_TYPE_RECHARGE_DAY_SUM;
    case 32:
    case 'TASK_TYPE_CONTINUOUS_RECHARGE_DAY':
      return TaskType.TASK_TYPE_CONTINUOUS_RECHARGE_DAY;
    case 33:
    case 'TASK_TYPE_RECHARGE_TIMES':
      return TaskType.TASK_TYPE_RECHARGE_TIMES;
    case 34:
    case 'TASK_TYPE_RECHARGE_SINGLE_AMOUNT':
      return TaskType.TASK_TYPE_RECHARGE_SINGLE_AMOUNT;
    case 40:
    case 'TASK_TYPE_MIC_ON_DURATION':
      return TaskType.TASK_TYPE_MIC_ON_DURATION;
    case 50:
    case 'TASK_TYPE_ACT_PAGE_VIEW_NUM':
      return TaskType.TASK_TYPE_ACT_PAGE_VIEW_NUM;
    case 60:
    case 'TASK_TYPE_USER_FOLLOW_ROOM_NUM':
      return TaskType.TASK_TYPE_USER_FOLLOW_ROOM_NUM;
    case 61:
    case 'TASK_TYPE_USER_CHECKIN':
      return TaskType.TASK_TYPE_USER_CHECKIN;
    case 62:
    case 'TASK_TYPE_LOTTERY_TIMES':
      return TaskType.TASK_TYPE_LOTTERY_TIMES;
    case 100:
    case 'TASK_TYPE_ROOM_FOLLOW_NUM':
      return TaskType.TASK_TYPE_ROOM_FOLLOW_NUM;
    case 101:
    case 'TASK_TYPE_ROOM_NEW_FOLLOW_NUM':
      return TaskType.TASK_TYPE_ROOM_NEW_FOLLOW_NUM;
    case 102:
    case 'TASK_TYPE_ROOM_SPEND_AMOUNT':
      return TaskType.TASK_TYPE_ROOM_SPEND_AMOUNT;
    case 103:
    case 'TASK_TYPE_ROOM_MIC_ON_DURATION':
      return TaskType.TASK_TYPE_ROOM_MIC_ON_DURATION;
    case 104:
    case 'TASK_TYPE_ROOM_LIVE_DURATION':
      return TaskType.TASK_TYPE_ROOM_LIVE_DURATION;
    case 105:
    case 'TASK_TYPE_ROOM_EFFECTIVE_ON_MIC_NUM':
      return TaskType.TASK_TYPE_ROOM_EFFECTIVE_ON_MIC_NUM;
    case 106:
    case 'TASK_TYPE_ROOM_GIFT_REC_NUM':
      return TaskType.TASK_TYPE_ROOM_GIFT_REC_NUM;
    case 107:
    case 'TASK_TYPE_ROOM_GIFT_SENDER_NUM':
      return TaskType.TASK_TYPE_ROOM_GIFT_SENDER_NUM;
    case 108:
    case 'TASK_TYPE_ROOM_EFFECTIVE_CONTINUOUS_ON_MIC_NUM':
      return TaskType.TASK_TYPE_ROOM_EFFECTIVE_CONTINUOUS_ON_MIC_NUM;
    case 109:
    case 'TASK_TYPE_ROOM_GIFT_RECEIVER_NUM':
      return TaskType.TASK_TYPE_ROOM_GIFT_RECEIVER_NUM;
    case 200:
    case 'TASK_TYPE_GAME_USER_BET':
      return TaskType.TASK_TYPE_GAME_USER_BET;
    case 201:
    case 'TASK_TYPE_GAME_USER_WIN':
      return TaskType.TASK_TYPE_GAME_USER_WIN;
    case 202:
    case 'TASK_TYPE_GAME_START':
      return TaskType.TASK_TYPE_GAME_START;
    case 203:
    case 'TASK_TYPE_GAME_ROUND_USER':
      return TaskType.TASK_TYPE_GAME_ROUND_USER;
    case 204:
    case 'TASK_TYPE_GAME_ROUND_ROOM_OWNER':
      return TaskType.TASK_TYPE_GAME_ROUND_ROOM_OWNER;
    case 205:
    case 'TASK_TYPE_GAME_BET_ROOM_OWNER':
      return TaskType.TASK_TYPE_GAME_BET_ROOM_OWNER;
    case 206:
    case 'TASK_TYPE_GAME_WIN_ROOM_OWNER':
      return TaskType.TASK_TYPE_GAME_WIN_ROOM_OWNER;
    case 400:
    case 'TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES':
      return TaskType.TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES;
    case 401:
    case 'TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES_CREATOR':
      return TaskType.TASK_TYPE_BIZ_REACH_ACT_TOTAL_REVENUE_TIMES_CREATOR;
    case 999:
    case 'TASK_TYPE_CUSTOMIZE':
      return TaskType.TASK_TYPE_CUSTOMIZE;
    case 1000:
    case 'TASK_TYPE_CUSTOMIZE_CURRENCY':
      return TaskType.TASK_TYPE_CUSTOMIZE_CURRENCY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskType.UNRECOGNIZED;
  }
}

/** ScoreType 分数累加类型 */
export enum TaskScoreType {
  /** TASK_SCORE_TYPE_NONE - none */
  TASK_SCORE_TYPE_NONE = 0,
  /** TASK_SCORE_TYPE_SPEND - 礼物消耗价值，例如：金币，每个业务币种不同，按业务定义的消耗币种来定义 */
  TASK_SCORE_TYPE_SPEND = 10,
  /** TASK_SCORE_TYPE_INCOME - 礼物收益价值，例如：钻石、宝石，每个业务币种不同，按业务定义的消耗币种来定义 */
  TASK_SCORE_TYPE_INCOME = 11,
  UNRECOGNIZED = -1
}

export function taskScoreTypeFromJSON(object: any): TaskScoreType {
  switch (object) {
    case 0:
    case 'TASK_SCORE_TYPE_NONE':
      return TaskScoreType.TASK_SCORE_TYPE_NONE;
    case 10:
    case 'TASK_SCORE_TYPE_SPEND':
      return TaskScoreType.TASK_SCORE_TYPE_SPEND;
    case 11:
    case 'TASK_SCORE_TYPE_INCOME':
      return TaskScoreType.TASK_SCORE_TYPE_INCOME;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskScoreType.UNRECOGNIZED;
  }
}

/** 任务周期 */
export enum TaskCycle {
  /** TASK_CYCLE_TYPE_NONE - NONE */
  TASK_CYCLE_TYPE_NONE = 0,
  /** TASK_CYCLE_TYPE_DAILY - 日榜 */
  TASK_CYCLE_TYPE_DAILY = 20,
  /** TASK_CYCLE_TYPE_MONTH - 月榜 */
  TASK_CYCLE_TYPE_MONTH = 30,
  /** TASK_CYCLE_TYPE_TOTAL - 总榜 */
  TASK_CYCLE_TYPE_TOTAL = 50,
  /** TASK_CYCLE_TYPE_WEEKLY_SUNDAY - 周榜-周日开始 */
  TASK_CYCLE_TYPE_WEEKLY_SUNDAY = 60,
  /** TASK_CYCLE_TYPE_WEEKLY_MONDAY - 周榜-周一开始 */
  TASK_CYCLE_TYPE_WEEKLY_MONDAY = 61,
  /** TASK_CYCLE_TYPE_WEEKLY_TUESDAY - 周榜-周二开始 */
  TASK_CYCLE_TYPE_WEEKLY_TUESDAY = 62,
  /** TASK_CYCLE_TYPE_WEEKLY_WEDNESDAY - 周榜-周三开始 */
  TASK_CYCLE_TYPE_WEEKLY_WEDNESDAY = 63,
  /** TASK_CYCLE_TYPE_WEEKLY_THURSDAY - 周榜-周四开始 */
  TASK_CYCLE_TYPE_WEEKLY_THURSDAY = 64,
  /** TASK_CYCLE_TYPE_WEEKLY_FRIDAY - 周榜-周五开始 */
  TASK_CYCLE_TYPE_WEEKLY_FRIDAY = 65,
  /** TASK_CYCLE_TYPE_WEEKLY_SATURDAY - 周榜-周六开始 */
  TASK_CYCLE_TYPE_WEEKLY_SATURDAY = 66,
  UNRECOGNIZED = -1
}

export function taskCycleFromJSON(object: any): TaskCycle {
  switch (object) {
    case 0:
    case 'TASK_CYCLE_TYPE_NONE':
      return TaskCycle.TASK_CYCLE_TYPE_NONE;
    case 20:
    case 'TASK_CYCLE_TYPE_DAILY':
      return TaskCycle.TASK_CYCLE_TYPE_DAILY;
    case 30:
    case 'TASK_CYCLE_TYPE_MONTH':
      return TaskCycle.TASK_CYCLE_TYPE_MONTH;
    case 50:
    case 'TASK_CYCLE_TYPE_TOTAL':
      return TaskCycle.TASK_CYCLE_TYPE_TOTAL;
    case 60:
    case 'TASK_CYCLE_TYPE_WEEKLY_SUNDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_SUNDAY;
    case 61:
    case 'TASK_CYCLE_TYPE_WEEKLY_MONDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_MONDAY;
    case 62:
    case 'TASK_CYCLE_TYPE_WEEKLY_TUESDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_TUESDAY;
    case 63:
    case 'TASK_CYCLE_TYPE_WEEKLY_WEDNESDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_WEDNESDAY;
    case 64:
    case 'TASK_CYCLE_TYPE_WEEKLY_THURSDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_THURSDAY;
    case 65:
    case 'TASK_CYCLE_TYPE_WEEKLY_FRIDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_FRIDAY;
    case 66:
    case 'TASK_CYCLE_TYPE_WEEKLY_SATURDAY':
      return TaskCycle.TASK_CYCLE_TYPE_WEEKLY_SATURDAY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskCycle.UNRECOGNIZED;
  }
}

/** 任务奖励类型 */
export enum TaskRewardType {
  /** TASK_REWARD_TYPE_NONE - none */
  TASK_REWARD_TYPE_NONE = 0,
  /** TASK_REWARD_TYPE_EMPTY - 空奖励 */
  TASK_REWARD_TYPE_EMPTY = 1,
  /** TASK_REWARD_TYPE_PACKAGE - 奖励包 */
  TASK_REWARD_TYPE_PACKAGE = 2,
  /** TASK_REWARD_TYPE_GENERIC - 通用奖励 */
  TASK_REWARD_TYPE_GENERIC = 4,
  /** TASK_REWARD_TYPE_RANK_POINTS - 榜单积分 */
  TASK_REWARD_TYPE_RANK_POINTS = 5,
  /** TASK_REWARD_TYPE_LOTTERY_TIMES - 抽奖次数 */
  TASK_REWARD_TYPE_LOTTERY_TIMES = 6,
  UNRECOGNIZED = -1
}

export function taskRewardTypeFromJSON(object: any): TaskRewardType {
  switch (object) {
    case 0:
    case 'TASK_REWARD_TYPE_NONE':
      return TaskRewardType.TASK_REWARD_TYPE_NONE;
    case 1:
    case 'TASK_REWARD_TYPE_EMPTY':
      return TaskRewardType.TASK_REWARD_TYPE_EMPTY;
    case 2:
    case 'TASK_REWARD_TYPE_PACKAGE':
      return TaskRewardType.TASK_REWARD_TYPE_PACKAGE;
    case 4:
    case 'TASK_REWARD_TYPE_GENERIC':
      return TaskRewardType.TASK_REWARD_TYPE_GENERIC;
    case 5:
    case 'TASK_REWARD_TYPE_RANK_POINTS':
      return TaskRewardType.TASK_REWARD_TYPE_RANK_POINTS;
    case 6:
    case 'TASK_REWARD_TYPE_LOTTERY_TIMES':
      return TaskRewardType.TASK_REWARD_TYPE_LOTTERY_TIMES;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskRewardType.UNRECOGNIZED;
  }
}

export enum PKType {
  PK_TYPE_NONE = 0,
  /** PK_TYPE_GUILD_REC - 公会收礼 */
  PK_TYPE_GUILD_REC = 1,
  /** PK_TYPE_FAMILY_REC - 家族收礼 */
  PK_TYPE_FAMILY_REC = 2,
  /** PK_TYPE_USER_REC - 用户收礼 */
  PK_TYPE_USER_REC = 3,
  /** PK_TYPE_USER - 用户送礼 */
  PK_TYPE_USER = 4,
  UNRECOGNIZED = -1
}

export function pKTypeFromJSON(object: any): PKType {
  switch (object) {
    case 0:
    case 'PK_TYPE_NONE':
      return PKType.PK_TYPE_NONE;
    case 1:
    case 'PK_TYPE_GUILD_REC':
      return PKType.PK_TYPE_GUILD_REC;
    case 2:
    case 'PK_TYPE_FAMILY_REC':
      return PKType.PK_TYPE_FAMILY_REC;
    case 3:
    case 'PK_TYPE_USER_REC':
      return PKType.PK_TYPE_USER_REC;
    case 4:
    case 'PK_TYPE_USER':
      return PKType.PK_TYPE_USER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PKType.UNRECOGNIZED;
  }
}

export enum PKRoundStatus {
  PK_ROUND_STATUS_NONE = 0,
  /** PK_ROUND_STATUS_WAITING - 未开始 */
  PK_ROUND_STATUS_WAITING = 1,
  /** PK_ROUND_STATUS_RUNNING - 进行中 */
  PK_ROUND_STATUS_RUNNING = 2,
  /** PK_ROUND_STATUS_END - 已结束 */
  PK_ROUND_STATUS_END = 3,
  UNRECOGNIZED = -1
}

export function pKRoundStatusFromJSON(object: any): PKRoundStatus {
  switch (object) {
    case 0:
    case 'PK_ROUND_STATUS_NONE':
      return PKRoundStatus.PK_ROUND_STATUS_NONE;
    case 1:
    case 'PK_ROUND_STATUS_WAITING':
      return PKRoundStatus.PK_ROUND_STATUS_WAITING;
    case 2:
    case 'PK_ROUND_STATUS_RUNNING':
      return PKRoundStatus.PK_ROUND_STATUS_RUNNING;
    case 3:
    case 'PK_ROUND_STATUS_END':
      return PKRoundStatus.PK_ROUND_STATUS_END;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PKRoundStatus.UNRECOGNIZED;
  }
}

export enum PKEntityResult {
  PK_ENTITY_RESULT_NONE = 0,
  /** PK_ENTITY_RESULT_DRAW - 平 */
  PK_ENTITY_RESULT_DRAW = 1,
  /** PK_ENTITY_RESULT_WIN - 胜 */
  PK_ENTITY_RESULT_WIN = 2,
  /** PK_ENTITY_RESULT_LOST - 负 */
  PK_ENTITY_RESULT_LOST = 3,
  UNRECOGNIZED = -1
}

export function pKEntityResultFromJSON(object: any): PKEntityResult {
  switch (object) {
    case 0:
    case 'PK_ENTITY_RESULT_NONE':
      return PKEntityResult.PK_ENTITY_RESULT_NONE;
    case 1:
    case 'PK_ENTITY_RESULT_DRAW':
      return PKEntityResult.PK_ENTITY_RESULT_DRAW;
    case 2:
    case 'PK_ENTITY_RESULT_WIN':
      return PKEntityResult.PK_ENTITY_RESULT_WIN;
    case 3:
    case 'PK_ENTITY_RESULT_LOST':
      return PKEntityResult.PK_ENTITY_RESULT_LOST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PKEntityResult.UNRECOGNIZED;
  }
}

export enum LotteryType {
  LOTTERY_TYPE_NONE = 0,
  /** LOTTERY_TYPE_TASK - 任务抽奖 */
  LOTTERY_TYPE_TASK = 1,
  /** LOTTERY_TYPE_PAID - 付费抽奖，宝石抽奖 */
  LOTTERY_TYPE_PAID = 2,
  /** LOTTERY_TYPE_PAID_POINT - 积分抽奖，目前只有dating用 */
  LOTTERY_TYPE_PAID_POINT = 3,
  UNRECOGNIZED = -1
}

export function lotteryTypeFromJSON(object: any): LotteryType {
  switch (object) {
    case 0:
    case 'LOTTERY_TYPE_NONE':
      return LotteryType.LOTTERY_TYPE_NONE;
    case 1:
    case 'LOTTERY_TYPE_TASK':
      return LotteryType.LOTTERY_TYPE_TASK;
    case 2:
    case 'LOTTERY_TYPE_PAID':
      return LotteryType.LOTTERY_TYPE_PAID;
    case 3:
    case 'LOTTERY_TYPE_PAID_POINT':
      return LotteryType.LOTTERY_TYPE_PAID_POINT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LotteryType.UNRECOGNIZED;
  }
}

export enum PandantType {
  PANDANT_TYPE_NONE = 0,
  /** PANDANT_TYPE_SINGLE - 单项模版 */
  PANDANT_TYPE_SINGLE = 1,
  /** PANDANT_TYPE_CP - CP模版 */
  PANDANT_TYPE_CP = 2,
  /** PANDANT_TYPE_PK - PK模版 */
  PANDANT_TYPE_PK = 3,
  /** PANDANT_TYPE_RANK - 排行模版 */
  PANDANT_TYPE_RANK = 4,
  UNRECOGNIZED = -1
}

export function pandantTypeFromJSON(object: any): PandantType {
  switch (object) {
    case 0:
    case 'PANDANT_TYPE_NONE':
      return PandantType.PANDANT_TYPE_NONE;
    case 1:
    case 'PANDANT_TYPE_SINGLE':
      return PandantType.PANDANT_TYPE_SINGLE;
    case 2:
    case 'PANDANT_TYPE_CP':
      return PandantType.PANDANT_TYPE_CP;
    case 3:
    case 'PANDANT_TYPE_PK':
      return PandantType.PANDANT_TYPE_PK;
    case 4:
    case 'PANDANT_TYPE_RANK':
      return PandantType.PANDANT_TYPE_RANK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PandantType.UNRECOGNIZED;
  }
}

export enum RoomRecommendDisplayInfo {
  ROOM_RECOMMEND_DISPLAY_INFO_NONE = 0,
  /** ROOM_RECOMMEND_DISPLAY_INFO_ROOM - 展示房间信息 */
  ROOM_RECOMMEND_DISPLAY_INFO_ROOM = 1,
  /** ROOM_RECOMMEND_DISPLAY_INFO_ROOM_OWNER - 展示房主信息 */
  ROOM_RECOMMEND_DISPLAY_INFO_ROOM_OWNER = 2,
  UNRECOGNIZED = -1
}

export function roomRecommendDisplayInfoFromJSON(object: any): RoomRecommendDisplayInfo {
  switch (object) {
    case 0:
    case 'ROOM_RECOMMEND_DISPLAY_INFO_NONE':
      return RoomRecommendDisplayInfo.ROOM_RECOMMEND_DISPLAY_INFO_NONE;
    case 1:
    case 'ROOM_RECOMMEND_DISPLAY_INFO_ROOM':
      return RoomRecommendDisplayInfo.ROOM_RECOMMEND_DISPLAY_INFO_ROOM;
    case 2:
    case 'ROOM_RECOMMEND_DISPLAY_INFO_ROOM_OWNER':
      return RoomRecommendDisplayInfo.ROOM_RECOMMEND_DISPLAY_INFO_ROOM_OWNER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomRecommendDisplayInfo.UNRECOGNIZED;
  }
}

/** 榜单主要类型分类 */
export enum RankMajorType {
  RANK_MAJOR_TYPE_NONE = 0,
  /** RANK_MAJOR_TYPE_USER - 用户榜 */
  RANK_MAJOR_TYPE_USER = 1,
  /** RANK_MAJOR_TYPE_FAMILY - 家族榜 */
  RANK_MAJOR_TYPE_FAMILY = 2,
  /** RANK_MAJOR_TYPE_GUILD - 公会榜 */
  RANK_MAJOR_TYPE_GUILD = 3,
  /** RANK_MAJOR_TYPE_ROOM - 房间榜 */
  RANK_MAJOR_TYPE_ROOM = 4,
  /** RANK_MAJOR_TYPE_USER_GROUP - 用户组榜， 比如cp榜单 */
  RANK_MAJOR_TYPE_USER_GROUP = 5,
  UNRECOGNIZED = -1
}

export function rankMajorTypeFromJSON(object: any): RankMajorType {
  switch (object) {
    case 0:
    case 'RANK_MAJOR_TYPE_NONE':
      return RankMajorType.RANK_MAJOR_TYPE_NONE;
    case 1:
    case 'RANK_MAJOR_TYPE_USER':
      return RankMajorType.RANK_MAJOR_TYPE_USER;
    case 2:
    case 'RANK_MAJOR_TYPE_FAMILY':
      return RankMajorType.RANK_MAJOR_TYPE_FAMILY;
    case 3:
    case 'RANK_MAJOR_TYPE_GUILD':
      return RankMajorType.RANK_MAJOR_TYPE_GUILD;
    case 4:
    case 'RANK_MAJOR_TYPE_ROOM':
      return RankMajorType.RANK_MAJOR_TYPE_ROOM;
    case 5:
    case 'RANK_MAJOR_TYPE_USER_GROUP':
      return RankMajorType.RANK_MAJOR_TYPE_USER_GROUP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankMajorType.UNRECOGNIZED;
  }
}

/** 奖池计算类型 */
export enum PrizePoolCalType {
  PRIZE_POOL_CAL_TYPE_NONE = 0,
  /** PRIZE_POOL_CAL_TYPE_FIX - 固定类型 */
  PRIZE_POOL_CAL_TYPE_FIX = 1,
  /** PRIZE_POOL_CAL_TYPE_RATIO - 按照比例 */
  PRIZE_POOL_CAL_TYPE_RATIO = 2,
  UNRECOGNIZED = -1
}

export function prizePoolCalTypeFromJSON(object: any): PrizePoolCalType {
  switch (object) {
    case 0:
    case 'PRIZE_POOL_CAL_TYPE_NONE':
      return PrizePoolCalType.PRIZE_POOL_CAL_TYPE_NONE;
    case 1:
    case 'PRIZE_POOL_CAL_TYPE_FIX':
      return PrizePoolCalType.PRIZE_POOL_CAL_TYPE_FIX;
    case 2:
    case 'PRIZE_POOL_CAL_TYPE_RATIO':
      return PrizePoolCalType.PRIZE_POOL_CAL_TYPE_RATIO;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PrizePoolCalType.UNRECOGNIZED;
  }
}

/** 奖池瓜分权重类型 */
export enum PrizePoolWeightType {
  PRIZE_POOL_WEIGHT_TYPE_NONE = 0,
  /** PRIZE_POOL_WEIGHT_TYPE_FIX - 固定类型：榜单排名，配置topN或者topN-topM对应多少权重 */
  PRIZE_POOL_WEIGHT_TYPE_FIX = 1,
  /** PRIZE_POOL_WEIGHT_TYPE_RATIO - 按照比例：榜单分数段，榜单分数>=XX分 权重 */
  PRIZE_POOL_WEIGHT_TYPE_RATIO = 2,
  /** PRIZE_POOL_WEIGHT_TYPE_SCORE - 按照榜单分数：无需设置权重 */
  PRIZE_POOL_WEIGHT_TYPE_SCORE = 3,
  UNRECOGNIZED = -1
}

export function prizePoolWeightTypeFromJSON(object: any): PrizePoolWeightType {
  switch (object) {
    case 0:
    case 'PRIZE_POOL_WEIGHT_TYPE_NONE':
      return PrizePoolWeightType.PRIZE_POOL_WEIGHT_TYPE_NONE;
    case 1:
    case 'PRIZE_POOL_WEIGHT_TYPE_FIX':
      return PrizePoolWeightType.PRIZE_POOL_WEIGHT_TYPE_FIX;
    case 2:
    case 'PRIZE_POOL_WEIGHT_TYPE_RATIO':
      return PrizePoolWeightType.PRIZE_POOL_WEIGHT_TYPE_RATIO;
    case 3:
    case 'PRIZE_POOL_WEIGHT_TYPE_SCORE':
      return PrizePoolWeightType.PRIZE_POOL_WEIGHT_TYPE_SCORE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PrizePoolWeightType.UNRECOGNIZED;
  }
}

/** 主体类型分类 */
export enum SubjectType {
  SUBJECT_TYPE_NONE = 0,
  /** SUBJECT_TYPE_USER - 用户 */
  SUBJECT_TYPE_USER = 1,
  /** SUBJECT_TYPE_FAMILY - 家族 */
  SUBJECT_TYPE_FAMILY = 2,
  /** SUBJECT_TYPE_GUILD - 公会 */
  SUBJECT_TYPE_GUILD = 3,
  /** SUBJECT_TYPE_ROOM - 房间 */
  SUBJECT_TYPE_ROOM = 4,
  UNRECOGNIZED = -1
}

export function subjectTypeFromJSON(object: any): SubjectType {
  switch (object) {
    case 0:
    case 'SUBJECT_TYPE_NONE':
      return SubjectType.SUBJECT_TYPE_NONE;
    case 1:
    case 'SUBJECT_TYPE_USER':
      return SubjectType.SUBJECT_TYPE_USER;
    case 2:
    case 'SUBJECT_TYPE_FAMILY':
      return SubjectType.SUBJECT_TYPE_FAMILY;
    case 3:
    case 'SUBJECT_TYPE_GUILD':
      return SubjectType.SUBJECT_TYPE_GUILD;
    case 4:
    case 'SUBJECT_TYPE_ROOM':
      return SubjectType.SUBJECT_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SubjectType.UNRECOGNIZED;
  }
}

/** 活动信息 */
export interface ActivityInfo {
  /** 活动ID */
  id: number;
  /** 活动名称 */
  name: string;
  /** 活动开始时间 */
  start_time: number;
  /** 活动结束时间 */
  end_time: number;
  /** 展示礼物 */
  display_gifts: DisplayGift[];
  /** 展示奖励, 是 OMS 序列化好的一份 JSON 字符串, 中台只是纯保存的. */
  display_rewards: string;
  /** 备注 */
  remark: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 活动唯一code, 用于运营和开发沟通. */
  activity_code: string;
  /** 应用code, 用于记录积木平台的应用标识. */
  application_code: string;
  /** 活动国家编码 */
  cou: string;
  /** 活动时区, 服务端根据国家编码返回, 前端不需要填. */
  time_zone: string;
}

/** 活动展示礼物 */
export interface DisplayGift {
  gift_id: string;
  gift_name: string;
  i18n_gift_name: { [key: string]: string };
  gift_icon: string;
  i18n_gift_icon: { [key: string]: string };
  /** 奖励配置项动画资源 */
  item_animation: string;
}

export interface DisplayGift_I18nGiftNameEntry {
  key: string;
  value: string;
}

export interface DisplayGift_I18nGiftIconEntry {
  key: string;
  value: string;
}

export interface RankInfo {
  /** 榜单ID */
  id: number;
  /** 榜单备注 */
  remark: string;
  /** 榜单周期 */
  cycle: CycleType;
  /** 榜单类型 */
  rank_type: RankType;
  /** 上榜数量（top n） */
  show_top: number;
  /** 数据来源 */
  source: RankSource;
  /** 分数累加类型 */
  score_type: ScoreType;
  /** 分数比例 */
  ratio: number;
  /** 榜单国家编码（计算时区） */
  cou: string;
  /** 榜单时区, 服务端根据国家编码返回, 前端不需要填. */
  time_zone: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 有效时段列表，对于小时榜则表示每天有效小时列表, 例如: [8, 9, 10, 20, 21, 22] 表示上午/下午的 8/9/10 点才统计. */
  valid_periods: number[];
  /** 指定币种对应的分数比例, 1: 收礼获得的货币 2: 送礼消耗的货币 3: 币商使用的货币 4: 美金钱包 */
  currency_ratio: { [key: string]: number };
  /** 指定礼物ID */
  special_gifts: string[];
  /** 指定收礼礼物ID */
  special_rec_gifts: string[];
  /** 已经选择的子分类 */
  gift_sub_categoryids: string[];
  /** 已经选择的子分类，收到礼物 */
  rec_gift_subcategoryids: string[];
  /** 配置状态：normal disable del */
  status: string;
  /** 展示历史榜单周期 */
  show_history_period: number;
  /** 晋级数量 */
  promotion_num: number;
}

export interface RankInfo_CurrencyRatioEntry {
  key: string;
  value: number;
}

/** 活动任务配置 */
export interface TaskConfig {
  /** 任务ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务场景 */
  scenes: string[];
  /** 任务代码 */
  task_code: string;
  /** 任务名称 */
  task_name: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 任务周期 */
  task_cycle: TaskCycle;
  /** 国家编码(计算时区) */
  cou: string;
  /** 去完成任务deeplink */
  to_finish_deeplink: string;
  /** 版本，1是旧平台，2是新平台 */
  ver: number;
  /** 任务模式 */
  task_mode: TaskMode;
  /** 最大可完成次数 */
  max_complete_times: number;
  /** 子任务列表 */
  sub_configs: TaskSubConfig[];
  /** h5配置列表 */
  h5_configs: TaskH5Config[];
  /** 任务描述 */
  task_desc: string;
  /** 自动领奖 */
  auto_reward: boolean;
  /** 关联的抽奖id */
  relate_lottery_id: number;
  /** 调用领奖的时候需要传个count = 1, 表示不管完成了多少次，只领取一个奖励 */
  disable_take_reward_batch: boolean;
  /** 前端组件类型 */
  component_type: string;
  /** 周期性结算类型 */
  cycle_settle: TaskCycleSettleType;
  /** 排序 */
  sort: number;
  /** 前端组件id，仅前端用 */
  task_component_id: number;
  /** 礼物白名单 */
  gift_white_list: string[];
  /** 收礼礼物白名单 */
  rec_gift_white_list: string[];
  /** 已经选择的子分类 */
  gift_sub_categoryids: string[];
  /** 已经选择的子分类，收到礼物 */
  rec_gift_subcategoryids: string[];
}

/** 活动任务h5配置 */
export interface TaskH5Config {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 阶段性任务用 */
  stage: number;
  /** 关闭状态宝箱icon */
  icon_close: string;
  /** 打开状态宝箱icon */
  icon_open: string;
  /** 领取完毕的宝箱icon */
  icon_received: string;
  /** 主标题 */
  title: string;
  /** 副标题 */
  sub_title: string;
  /** 扩展json */
  extend_json: string;
}

/** 活动任务-子任务配置 */
export interface TaskSubConfig {
  /** 子任务ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 任务类型 */
  task_type: TaskType;
  /** 分数累加类型 */
  task_score_type: TaskScoreType;
  /** 任务名称 */
  task_name: string;
  /** 任务完成目标值 */
  task_target_value: number;
  /** 完成任务路由 */
  to_finish_deeplink: string;
  /** 扩展json */
  extend_json: string;
  /** 修改时间 */
  updated_at: number;
  /** 版本，1是旧平台，2是新平台 */
  ver: number;
  /** 指定币种对应的分数比例, 1: 收礼获得的货币 2: 送礼消耗的货币 3: 币商使用的货币 4: 美金钱包 */
  currency_ratio: { [key: string]: number };
  /** 任务描述多语言 */
  task_desc: string;
  /** 跳转按钮配置 */
  jump_button: ButtonH5Config | undefined;
}

export interface TaskSubConfig_CurrencyRatioEntry {
  key: string;
  value: number;
}

export interface StageTaskExtendInfo {
  /** 阶段目标值 */
  target_values: number[];
}

/** 活动任务奖励配置 */
export interface TaskRewardConfig {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 奖励类型(2:奖励包) */
  reward_type: TaskRewardType;
  /** 奖励内容 */
  reward_object_json: string;
  /** 扩展json */
  extend_json: string;
  /** 阶段性任务用 */
  stage: number;
}

/** 任务奖励内容-空奖励 */
export interface TaskRewardEmpty {}

/** 任务奖励内容-奖励包 */
export interface TaskRewardPackage {
  /** 奖励包Id */
  package_id: string;
}

export interface PKConfig {
  id: number;
  /** 活动Id */
  activity_id: number;
  title: string;
  /** 榜单国家编码（计算时区） */
  cou: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** pk 类型 */
  pk_type: PKType;
}

export interface PKDetail {
  id: number;
  /** 活动Id */
  activity_id: number;
  pk_id: number;
  players: PKPlayer[];
  rounds: PKRound[];
  pk_type: PKType;
}

export interface PKRound {
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 状态 */
  status: PKRoundStatus;
  /** 参赛用户信息 */
  players: PKPlayer[];
}

export interface PKPlayer {
  item: PKPlayerItem | undefined;
  /** 贡献榜 */
  contribute_list: PKPlayerEntity[];
  /** 指定房间，如果不指定，所有房间都算 */
  special_room_id: number;
  /** 指定房间 show_id，如果不指定，所有房间都算 */
  special_room_show_id: string;
}

export interface PKPlayerItem {
  id: string;
  score: number;
  rank: number;
  entities: PKPlayerEntity[];
  result: PKEntityResult;
  show_id: string;
}

export interface PKPlayerEntity {
  id: string;
  nick: string;
  avatar: string;
  show_id: string;
}

export interface LotteryConfig {
  id: number;
  activity_id: number;
  title: string;
  cou: string;
  start_time: number;
  end_time: number;
  paid_rules: LotteryPaidRule[];
  lottery_type: LotteryType;
}

export interface LotteryRewardConfig {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  lottery_id: number;
  /** 奖励类型(2:奖励包) */
  reward_type: TaskRewardType;
  /** 奖励内容 */
  reward_object_json: string;
  /** 扩展json */
  extend_json: string;
  /** 排序值，越小越前显示 */
  sort: number;
  /** 奖励自定义标题 */
  title: string;
  /** 奖励自定义图片 */
  img: string;
}

/** 付费抽奖信息 */
export interface LotteryPaidRule {
  /** 货币类型 */
  currency: Currency;
  /** 付费金额 */
  amount: number;
  /** 抽奖次数 */
  lottery_times: number;
}

/** 中奖记录 */
export interface LotteryRecord {
  record_id: number;
  lottery_id: number;
  uid: number;
  /** 时区 */
  cou: string;
  details: LotteryRecordDetail[];
  create_time: number;
}

export interface LotteryRecordDetail {
  reward_id: number;
  /** 奖励类型(2:奖励包) */
  reward_type: TaskRewardType;
  /** 奖励内容 */
  reward_object_json: string;
  /** 扩展json */
  extend_json: string;
  /** 获得数量 */
  num: number;
}

/** 弹幕信息 */
export interface LotteryBarrage {
  uid: number;
  /** 中奖信息 */
  detail: LotteryRecordDetail | undefined;
  nickname: string;
  avatar: string;
}

/** 用户抽奖次数信息 */
export interface UserLotteryInfo {
  /** 抽奖id */
  lottery_id: number;
  /** 可抽奖次数 */
  times: number;
}

export interface PandantRank {
  /** 榜单 */
  rank: Rank | undefined;
  /** 榜单开始时间 */
  start_time: number;
  /** 榜单结束时间 */
  end_time: number;
  /** 活动链接 */
  url: { [key: string]: string };
  /** 挂件背景图多语言 */
  background_images: { [key: string]: string };
  /** 挂件模版 */
  pandant_type: PandantType;
}

export interface PandantRank_UrlEntry {
  key: string;
  value: string;
}

export interface PandantRank_BackgroundImagesEntry {
  key: string;
  value: string;
}

/** 按钮配置 */
export interface ButtonH5Config {
  /** 文案 */
  text: string;
  /** 字体颜色 */
  font_color: string;
  /** 字体大小 */
  font_size: number;
  /** 背景颜色 */
  bg_color: string;
  /** 背景图片 */
  bg_image: string;
}

export interface RoomRecommendConfig {
  id: number;
  activity_id: number;
  /** 组件展示对象 */
  display_info: RoomRecommendDisplayInfo;
}

/** 房间信息 */
export interface RoomInfo {
  /** 房间号 */
  room_id: number;
  /** 房间靓号 */
  show_id: string;
  /** 房主 */
  owner_uid: number;
  /** 房间类型 */
  room_type: string;
  /** 房间名称 */
  room_name: string;
  /** 房间标题 */
  room_title: string;
  /** 房间封面 */
  cover: string;
}

export interface UserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
  /** 扩展信息 */
  extend: string;
}

/** 时间周期 */
export interface TimeRange {
  start_time: number;
  end_time: number;
}

/** 展示配置 */
export interface PresentationInfo {
  id: number;
  config: PresentationConfig | undefined;
  pairs: PresentationConfigItem[];
}

/** 展示配置 */
export interface PresentationConfig {
  id: number;
}

export interface PresentationUserInfo {
  user_info: UserInfo | undefined;
  /** 开播或者上麦中的房间信息，没返回说明不在开播或者上麦中 */
  room_info: RoomInfo | undefined;
}

export interface PresentationConfigItem {
  /** 用户信息列表 */
  user_info_list: PresentationUserInfo[];
  /** 标题 */
  title: string;
  /** 描述 */
  desc: string;
}

/** 用户榜单 */
export interface UserRank {
  items: UserRankItem[];
}

/** 用户榜单item */
export interface UserRankItem {
  /** 用户信息 */
  user: UserInfo | undefined;
  /** 排名 */
  rank: number;
  /** 分数 */
  score: number;
}

export interface UserInfoExtend {
  /** 年龄 */
  age: number;
  /** 性别 */
  gender: Gender;
  /** 背包资源 */
  using_privilege: UsingPrivilege | undefined;
  /** 财富等级 */
  wealth_level: number;
  /** 财富值 */
  wealth_value: number;
  /** 财富等级 图片，语言-》图片 */
  wealth_img: { [key: string]: string };
  /** 魅力等级 */
  charm_level: number;
  /** 魅力值 */
  charm_value: number;
  /** 魅力等级 图片，语言-》图片 */
  charm_img: { [key: string]: string };
  uid: number;
  /** 瓜分得到的奖励数量 */
  reward_num: number;
}

export interface UserInfoExtend_WealthImgEntry {
  key: string;
  value: string;
}

export interface UserInfoExtend_CharmImgEntry {
  key: string;
  value: string;
}

export interface RoomInfoExtend {
  /** 房主uid */
  room_owner_uid: number;
  /** 房主昵称 */
  room_owner_nick: string;
  room_tags: RoomTag[];
  room_id: number;
  /** 瓜分得到的奖励数量 */
  reward_num: number;
}

export interface FamilyInfoExtend {
  family_id: number;
  /** 瓜分得到的奖励数量 */
  reward_num: number;
}

/** 奖励配置项 */
export interface RewardItem {
  /** 奖励配置项ID, 为了通用这里采用字符串形式, 例如礼物ID, 头像框ID, 房间背景ID, ... */
  item_id: string;
  /** 奖励配置项名称, 例如: 礼物的名称 / 头像框的名称. 这是缺省名称, 如果 oms 需要国际化显示则需要提供 i18n_category_name 字段. */
  item_name: string;
  /** 国家化的奖励配置项名称, key 的取值有 zh / en / ar / tr ... */
  i18n_item_name: { [key: string]: string };
  /** 奖励配置项图标, 例如: 礼物的图标 / 头像框的图标. 这是缺省图标, 如果 oms 需要国际化显示则需要提供 i18n_category_icon 字段. */
  item_icon: string;
  /** 国家化的奖励配置项图标, key 的取值有 zh / en / ar / tr ... */
  i18n_item_icon: { [key: string]: string };
  /** 奖励配置项动画资源 */
  item_animation: string;
  /** 子类型,具体业务决定, 对应 goods的 goods_type \ badge 的 badge_type \ gift 的 gift_type */
  reward_sub_type: string;
  /** 拓展信息, 应当设计成一个 JSON 便于后续拓展. */
  expand: string;
  /** 根据子分类的id来查 */
  sub_category_ids: string[];
}

export interface RewardItem_I18nItemNameEntry {
  key: string;
  value: string;
}

export interface RewardItem_I18nItemIconEntry {
  key: string;
  value: string;
}

export interface PrizePoolWeight {
  /** 最小值，如果是固定类型，则对应最小榜单排名，如果是按照比例类型，则对应最小榜单分数，包含这个值 */
  min: number;
  /** 最大值，如果是固定类型，则对应最大榜单排名，如果是按照比例类型，则对应最大榜单分数，不包含这个值 */
  max: number;
  /** 权重值 */
  weight: number;
}

/** 奖池配置 */
export interface PrizePoolConfig {
  id: number;
  activity_id: number;
  /** 奖池累加类型 */
  cal_type: PrizePoolCalType;
  /** true：自动发奖，false：运营自己导出，然后发奖 */
  auto_reward: boolean;
  /** 奖励配置项key, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_category_key: string;
  /** 奖励配置项ID, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_item_id: string;
  /** 最小榜单分 */
  min_rank_score: number;
  /** 最大奖池数量 */
  max_pool_num: number;
  /** 奖池瓜分权重类型 */
  weight_type: PrizePoolWeightType;
  /** 奖池瓜分权重列表,json 存数据库 */
  weight_config_list: PrizePoolWeight[];
  /** 关联的榜单id */
  relate_rank_id: number;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 周期 */
  cycle: CycleType;
  /** 当前奖池数量 */
  cur_pool_num: number;
  /** 比例值，奖池分数：所有上榜用户的榜单总分*比例值 */
  pool_ratio: number;
  /** 瓜分奖池限制，用户榜单分数大于它，才可以参加奖池瓜分 */
  prize_rank_score_limit: number;
  /** 前端组件类型 */
  component_type: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 前端组件id，仅前端用 */
  component_id: number;
}

function createBaseActivityInfo(): ActivityInfo {
  return {
    id: 0,
    name: '',
    start_time: 0,
    end_time: 0,
    display_gifts: [],
    display_rewards: '',
    remark: '',
    status: '',
    activity_code: '',
    application_code: '',
    cou: '',
    time_zone: ''
  };
}

export const ActivityInfo: MessageFns<ActivityInfo> = {
  fromJSON(object: any): ActivityInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      display_gifts: globalThis.Array.isArray(object?.display_gifts)
        ? object.display_gifts.map((e: any) => DisplayGift.fromJSON(e))
        : [],
      display_rewards: isSet(object.display_rewards) ? globalThis.String(object.display_rewards) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      application_code: isSet(object.application_code) ? globalThis.String(object.application_code) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      time_zone: isSet(object.time_zone) ? globalThis.String(object.time_zone) : ''
    };
  },

  create<I extends Exact<DeepPartial<ActivityInfo>, I>>(base?: I): ActivityInfo {
    return ActivityInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityInfo>, I>>(object: I): ActivityInfo {
    const message = createBaseActivityInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.display_gifts = object.display_gifts?.map(e => DisplayGift.fromPartial(e)) || [];
    message.display_rewards = object.display_rewards ?? '';
    message.remark = object.remark ?? '';
    message.status = object.status ?? '';
    message.activity_code = object.activity_code ?? '';
    message.application_code = object.application_code ?? '';
    message.cou = object.cou ?? '';
    message.time_zone = object.time_zone ?? '';
    return message;
  }
};

function createBaseDisplayGift(): DisplayGift {
  return { gift_id: '', gift_name: '', i18n_gift_name: {}, gift_icon: '', i18n_gift_icon: {}, item_animation: '' };
}

export const DisplayGift: MessageFns<DisplayGift> = {
  fromJSON(object: any): DisplayGift {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.String(object.gift_id) : '',
      gift_name: isSet(object.gift_name) ? globalThis.String(object.gift_name) : '',
      i18n_gift_name: isObject(object.i18n_gift_name)
        ? Object.entries(object.i18n_gift_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      gift_icon: isSet(object.gift_icon) ? globalThis.String(object.gift_icon) : '',
      i18n_gift_icon: isObject(object.i18n_gift_icon)
        ? Object.entries(object.i18n_gift_icon).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_animation: isSet(object.item_animation) ? globalThis.String(object.item_animation) : ''
    };
  },

  create<I extends Exact<DeepPartial<DisplayGift>, I>>(base?: I): DisplayGift {
    return DisplayGift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayGift>, I>>(object: I): DisplayGift {
    const message = createBaseDisplayGift();
    message.gift_id = object.gift_id ?? '';
    message.gift_name = object.gift_name ?? '';
    message.i18n_gift_name = Object.entries(object.i18n_gift_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.gift_icon = object.gift_icon ?? '';
    message.i18n_gift_icon = Object.entries(object.i18n_gift_icon ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_animation = object.item_animation ?? '';
    return message;
  }
};

function createBaseDisplayGift_I18nGiftNameEntry(): DisplayGift_I18nGiftNameEntry {
  return { key: '', value: '' };
}

export const DisplayGift_I18nGiftNameEntry: MessageFns<DisplayGift_I18nGiftNameEntry> = {
  fromJSON(object: any): DisplayGift_I18nGiftNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<DisplayGift_I18nGiftNameEntry>, I>>(base?: I): DisplayGift_I18nGiftNameEntry {
    return DisplayGift_I18nGiftNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayGift_I18nGiftNameEntry>, I>>(
    object: I
  ): DisplayGift_I18nGiftNameEntry {
    const message = createBaseDisplayGift_I18nGiftNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseDisplayGift_I18nGiftIconEntry(): DisplayGift_I18nGiftIconEntry {
  return { key: '', value: '' };
}

export const DisplayGift_I18nGiftIconEntry: MessageFns<DisplayGift_I18nGiftIconEntry> = {
  fromJSON(object: any): DisplayGift_I18nGiftIconEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<DisplayGift_I18nGiftIconEntry>, I>>(base?: I): DisplayGift_I18nGiftIconEntry {
    return DisplayGift_I18nGiftIconEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisplayGift_I18nGiftIconEntry>, I>>(
    object: I
  ): DisplayGift_I18nGiftIconEntry {
    const message = createBaseDisplayGift_I18nGiftIconEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRankInfo(): RankInfo {
  return {
    id: 0,
    remark: '',
    cycle: 0,
    rank_type: 0,
    show_top: 0,
    source: 0,
    score_type: 0,
    ratio: 0,
    cou: '',
    time_zone: '',
    start_time: 0,
    end_time: 0,
    valid_periods: [],
    currency_ratio: {},
    special_gifts: [],
    special_rec_gifts: [],
    gift_sub_categoryids: [],
    rec_gift_subcategoryids: [],
    status: '',
    show_history_period: 0,
    promotion_num: 0
  };
}

export const RankInfo: MessageFns<RankInfo> = {
  fromJSON(object: any): RankInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      cycle: isSet(object.cycle) ? cycleTypeFromJSON(object.cycle) : 0,
      rank_type: isSet(object.rank_type) ? rankTypeFromJSON(object.rank_type) : 0,
      show_top: isSet(object.show_top) ? globalThis.Number(object.show_top) : 0,
      source: isSet(object.source) ? rankSourceFromJSON(object.source) : 0,
      score_type: isSet(object.score_type) ? scoreTypeFromJSON(object.score_type) : 0,
      ratio: isSet(object.ratio) ? globalThis.Number(object.ratio) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      time_zone: isSet(object.time_zone) ? globalThis.String(object.time_zone) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      valid_periods: globalThis.Array.isArray(object?.valid_periods)
        ? object.valid_periods.map((e: any) => globalThis.Number(e))
        : [],
      currency_ratio: isObject(object.currency_ratio)
        ? Object.entries(object.currency_ratio).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      special_gifts: globalThis.Array.isArray(object?.special_gifts)
        ? object.special_gifts.map((e: any) => globalThis.String(e))
        : [],
      special_rec_gifts: globalThis.Array.isArray(object?.special_rec_gifts)
        ? object.special_rec_gifts.map((e: any) => globalThis.String(e))
        : [],
      gift_sub_categoryids: globalThis.Array.isArray(object?.gift_sub_categoryids)
        ? object.gift_sub_categoryids.map((e: any) => globalThis.String(e))
        : [],
      rec_gift_subcategoryids: globalThis.Array.isArray(object?.rec_gift_subcategoryids)
        ? object.rec_gift_subcategoryids.map((e: any) => globalThis.String(e))
        : [],
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      show_history_period: isSet(object.show_history_period) ? globalThis.Number(object.show_history_period) : 0,
      promotion_num: isSet(object.promotion_num) ? globalThis.Number(object.promotion_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<RankInfo>, I>>(base?: I): RankInfo {
    return RankInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankInfo>, I>>(object: I): RankInfo {
    const message = createBaseRankInfo();
    message.id = object.id ?? 0;
    message.remark = object.remark ?? '';
    message.cycle = object.cycle ?? 0;
    message.rank_type = object.rank_type ?? 0;
    message.show_top = object.show_top ?? 0;
    message.source = object.source ?? 0;
    message.score_type = object.score_type ?? 0;
    message.ratio = object.ratio ?? 0;
    message.cou = object.cou ?? '';
    message.time_zone = object.time_zone ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.valid_periods = object.valid_periods?.map(e => e) || [];
    message.currency_ratio = Object.entries(object.currency_ratio ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.special_gifts = object.special_gifts?.map(e => e) || [];
    message.special_rec_gifts = object.special_rec_gifts?.map(e => e) || [];
    message.gift_sub_categoryids = object.gift_sub_categoryids?.map(e => e) || [];
    message.rec_gift_subcategoryids = object.rec_gift_subcategoryids?.map(e => e) || [];
    message.status = object.status ?? '';
    message.show_history_period = object.show_history_period ?? 0;
    message.promotion_num = object.promotion_num ?? 0;
    return message;
  }
};

function createBaseRankInfo_CurrencyRatioEntry(): RankInfo_CurrencyRatioEntry {
  return { key: '', value: 0 };
}

export const RankInfo_CurrencyRatioEntry: MessageFns<RankInfo_CurrencyRatioEntry> = {
  fromJSON(object: any): RankInfo_CurrencyRatioEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<RankInfo_CurrencyRatioEntry>, I>>(base?: I): RankInfo_CurrencyRatioEntry {
    return RankInfo_CurrencyRatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankInfo_CurrencyRatioEntry>, I>>(object: I): RankInfo_CurrencyRatioEntry {
    const message = createBaseRankInfo_CurrencyRatioEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseTaskConfig(): TaskConfig {
  return {
    id: 0,
    activity_id: 0,
    scenes: [],
    task_code: '',
    task_name: '',
    start_time: 0,
    end_time: 0,
    task_cycle: 0,
    cou: '',
    to_finish_deeplink: '',
    ver: 0,
    task_mode: 0,
    max_complete_times: 0,
    sub_configs: [],
    h5_configs: [],
    task_desc: '',
    auto_reward: false,
    relate_lottery_id: 0,
    disable_take_reward_batch: false,
    component_type: '',
    cycle_settle: 0,
    sort: 0,
    task_component_id: 0,
    gift_white_list: [],
    rec_gift_white_list: [],
    gift_sub_categoryids: [],
    rec_gift_subcategoryids: []
  };
}

export const TaskConfig: MessageFns<TaskConfig> = {
  fromJSON(object: any): TaskConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      scenes: globalThis.Array.isArray(object?.scenes) ? object.scenes.map((e: any) => globalThis.String(e)) : [],
      task_code: isSet(object.task_code) ? globalThis.String(object.task_code) : '',
      task_name: isSet(object.task_name) ? globalThis.String(object.task_name) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      task_cycle: isSet(object.task_cycle) ? taskCycleFromJSON(object.task_cycle) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      to_finish_deeplink: isSet(object.to_finish_deeplink) ? globalThis.String(object.to_finish_deeplink) : '',
      ver: isSet(object.ver) ? globalThis.Number(object.ver) : 0,
      task_mode: isSet(object.task_mode) ? taskModeFromJSON(object.task_mode) : 0,
      max_complete_times: isSet(object.max_complete_times) ? globalThis.Number(object.max_complete_times) : 0,
      sub_configs: globalThis.Array.isArray(object?.sub_configs)
        ? object.sub_configs.map((e: any) => TaskSubConfig.fromJSON(e))
        : [],
      h5_configs: globalThis.Array.isArray(object?.h5_configs)
        ? object.h5_configs.map((e: any) => TaskH5Config.fromJSON(e))
        : [],
      task_desc: isSet(object.task_desc) ? globalThis.String(object.task_desc) : '',
      auto_reward: isSet(object.auto_reward) ? globalThis.Boolean(object.auto_reward) : false,
      relate_lottery_id: isSet(object.relate_lottery_id) ? globalThis.Number(object.relate_lottery_id) : 0,
      disable_take_reward_batch: isSet(object.disable_take_reward_batch)
        ? globalThis.Boolean(object.disable_take_reward_batch)
        : false,
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      cycle_settle: isSet(object.cycle_settle) ? taskCycleSettleTypeFromJSON(object.cycle_settle) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      task_component_id: isSet(object.task_component_id) ? globalThis.Number(object.task_component_id) : 0,
      gift_white_list: globalThis.Array.isArray(object?.gift_white_list)
        ? object.gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      rec_gift_white_list: globalThis.Array.isArray(object?.rec_gift_white_list)
        ? object.rec_gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      gift_sub_categoryids: globalThis.Array.isArray(object?.gift_sub_categoryids)
        ? object.gift_sub_categoryids.map((e: any) => globalThis.String(e))
        : [],
      rec_gift_subcategoryids: globalThis.Array.isArray(object?.rec_gift_subcategoryids)
        ? object.rec_gift_subcategoryids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<TaskConfig>, I>>(base?: I): TaskConfig {
    return TaskConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskConfig>, I>>(object: I): TaskConfig {
    const message = createBaseTaskConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.scenes = object.scenes?.map(e => e) || [];
    message.task_code = object.task_code ?? '';
    message.task_name = object.task_name ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.task_cycle = object.task_cycle ?? 0;
    message.cou = object.cou ?? '';
    message.to_finish_deeplink = object.to_finish_deeplink ?? '';
    message.ver = object.ver ?? 0;
    message.task_mode = object.task_mode ?? 0;
    message.max_complete_times = object.max_complete_times ?? 0;
    message.sub_configs = object.sub_configs?.map(e => TaskSubConfig.fromPartial(e)) || [];
    message.h5_configs = object.h5_configs?.map(e => TaskH5Config.fromPartial(e)) || [];
    message.task_desc = object.task_desc ?? '';
    message.auto_reward = object.auto_reward ?? false;
    message.relate_lottery_id = object.relate_lottery_id ?? 0;
    message.disable_take_reward_batch = object.disable_take_reward_batch ?? false;
    message.component_type = object.component_type ?? '';
    message.cycle_settle = object.cycle_settle ?? 0;
    message.sort = object.sort ?? 0;
    message.task_component_id = object.task_component_id ?? 0;
    message.gift_white_list = object.gift_white_list?.map(e => e) || [];
    message.rec_gift_white_list = object.rec_gift_white_list?.map(e => e) || [];
    message.gift_sub_categoryids = object.gift_sub_categoryids?.map(e => e) || [];
    message.rec_gift_subcategoryids = object.rec_gift_subcategoryids?.map(e => e) || [];
    return message;
  }
};

function createBaseTaskH5Config(): TaskH5Config {
  return {
    id: 0,
    activity_id: 0,
    task_id: 0,
    stage: 0,
    icon_close: '',
    icon_open: '',
    icon_received: '',
    title: '',
    sub_title: '',
    extend_json: ''
  };
}

export const TaskH5Config: MessageFns<TaskH5Config> = {
  fromJSON(object: any): TaskH5Config {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      stage: isSet(object.stage) ? globalThis.Number(object.stage) : 0,
      icon_close: isSet(object.icon_close) ? globalThis.String(object.icon_close) : '',
      icon_open: isSet(object.icon_open) ? globalThis.String(object.icon_open) : '',
      icon_received: isSet(object.icon_received) ? globalThis.String(object.icon_received) : '',
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      sub_title: isSet(object.sub_title) ? globalThis.String(object.sub_title) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : ''
    };
  },

  create<I extends Exact<DeepPartial<TaskH5Config>, I>>(base?: I): TaskH5Config {
    return TaskH5Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskH5Config>, I>>(object: I): TaskH5Config {
    const message = createBaseTaskH5Config();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.stage = object.stage ?? 0;
    message.icon_close = object.icon_close ?? '';
    message.icon_open = object.icon_open ?? '';
    message.icon_received = object.icon_received ?? '';
    message.title = object.title ?? '';
    message.sub_title = object.sub_title ?? '';
    message.extend_json = object.extend_json ?? '';
    return message;
  }
};

function createBaseTaskSubConfig(): TaskSubConfig {
  return {
    id: 0,
    activity_id: 0,
    task_id: 0,
    task_type: 0,
    task_score_type: 0,
    task_name: '',
    task_target_value: 0,
    to_finish_deeplink: '',
    extend_json: '',
    updated_at: 0,
    ver: 0,
    currency_ratio: {},
    task_desc: '',
    jump_button: undefined
  };
}

export const TaskSubConfig: MessageFns<TaskSubConfig> = {
  fromJSON(object: any): TaskSubConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      task_type: isSet(object.task_type) ? taskTypeFromJSON(object.task_type) : 0,
      task_score_type: isSet(object.task_score_type) ? taskScoreTypeFromJSON(object.task_score_type) : 0,
      task_name: isSet(object.task_name) ? globalThis.String(object.task_name) : '',
      task_target_value: isSet(object.task_target_value) ? globalThis.Number(object.task_target_value) : 0,
      to_finish_deeplink: isSet(object.to_finish_deeplink) ? globalThis.String(object.to_finish_deeplink) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      ver: isSet(object.ver) ? globalThis.Number(object.ver) : 0,
      currency_ratio: isObject(object.currency_ratio)
        ? Object.entries(object.currency_ratio).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      task_desc: isSet(object.task_desc) ? globalThis.String(object.task_desc) : '',
      jump_button: isSet(object.jump_button) ? ButtonH5Config.fromJSON(object.jump_button) : undefined
    };
  },

  create<I extends Exact<DeepPartial<TaskSubConfig>, I>>(base?: I): TaskSubConfig {
    return TaskSubConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskSubConfig>, I>>(object: I): TaskSubConfig {
    const message = createBaseTaskSubConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.task_type = object.task_type ?? 0;
    message.task_score_type = object.task_score_type ?? 0;
    message.task_name = object.task_name ?? '';
    message.task_target_value = object.task_target_value ?? 0;
    message.to_finish_deeplink = object.to_finish_deeplink ?? '';
    message.extend_json = object.extend_json ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.ver = object.ver ?? 0;
    message.currency_ratio = Object.entries(object.currency_ratio ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_desc = object.task_desc ?? '';
    message.jump_button =
      object.jump_button !== undefined && object.jump_button !== null
        ? ButtonH5Config.fromPartial(object.jump_button)
        : undefined;
    return message;
  }
};

function createBaseTaskSubConfig_CurrencyRatioEntry(): TaskSubConfig_CurrencyRatioEntry {
  return { key: '', value: 0 };
}

export const TaskSubConfig_CurrencyRatioEntry: MessageFns<TaskSubConfig_CurrencyRatioEntry> = {
  fromJSON(object: any): TaskSubConfig_CurrencyRatioEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskSubConfig_CurrencyRatioEntry>, I>>(
    base?: I
  ): TaskSubConfig_CurrencyRatioEntry {
    return TaskSubConfig_CurrencyRatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskSubConfig_CurrencyRatioEntry>, I>>(
    object: I
  ): TaskSubConfig_CurrencyRatioEntry {
    const message = createBaseTaskSubConfig_CurrencyRatioEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseStageTaskExtendInfo(): StageTaskExtendInfo {
  return { target_values: [] };
}

export const StageTaskExtendInfo: MessageFns<StageTaskExtendInfo> = {
  fromJSON(object: any): StageTaskExtendInfo {
    return {
      target_values: globalThis.Array.isArray(object?.target_values)
        ? object.target_values.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<StageTaskExtendInfo>, I>>(base?: I): StageTaskExtendInfo {
    return StageTaskExtendInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StageTaskExtendInfo>, I>>(object: I): StageTaskExtendInfo {
    const message = createBaseStageTaskExtendInfo();
    message.target_values = object.target_values?.map(e => e) || [];
    return message;
  }
};

function createBaseTaskRewardConfig(): TaskRewardConfig {
  return { id: 0, activity_id: 0, task_id: 0, reward_type: 0, reward_object_json: '', extend_json: '', stage: 0 };
}

export const TaskRewardConfig: MessageFns<TaskRewardConfig> = {
  fromJSON(object: any): TaskRewardConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      reward_type: isSet(object.reward_type) ? taskRewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      stage: isSet(object.stage) ? globalThis.Number(object.stage) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardConfig>, I>>(base?: I): TaskRewardConfig {
    return TaskRewardConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardConfig>, I>>(object: I): TaskRewardConfig {
    const message = createBaseTaskRewardConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.extend_json = object.extend_json ?? '';
    message.stage = object.stage ?? 0;
    return message;
  }
};

function createBaseTaskRewardEmpty(): TaskRewardEmpty {
  return {};
}

export const TaskRewardEmpty: MessageFns<TaskRewardEmpty> = {
  fromJSON(_: any): TaskRewardEmpty {
    return {};
  },

  create<I extends Exact<DeepPartial<TaskRewardEmpty>, I>>(base?: I): TaskRewardEmpty {
    return TaskRewardEmpty.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardEmpty>, I>>(_: I): TaskRewardEmpty {
    const message = createBaseTaskRewardEmpty();
    return message;
  }
};

function createBaseTaskRewardPackage(): TaskRewardPackage {
  return { package_id: '' };
}

export const TaskRewardPackage: MessageFns<TaskRewardPackage> = {
  fromJSON(object: any): TaskRewardPackage {
    return { package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '' };
  },

  create<I extends Exact<DeepPartial<TaskRewardPackage>, I>>(base?: I): TaskRewardPackage {
    return TaskRewardPackage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardPackage>, I>>(object: I): TaskRewardPackage {
    const message = createBaseTaskRewardPackage();
    message.package_id = object.package_id ?? '';
    return message;
  }
};

function createBasePKConfig(): PKConfig {
  return { id: 0, activity_id: 0, title: '', cou: '', start_time: 0, end_time: 0, pk_type: 0 };
}

export const PKConfig: MessageFns<PKConfig> = {
  fromJSON(object: any): PKConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      pk_type: isSet(object.pk_type) ? pKTypeFromJSON(object.pk_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKConfig>, I>>(base?: I): PKConfig {
    return PKConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKConfig>, I>>(object: I): PKConfig {
    const message = createBasePKConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.title = object.title ?? '';
    message.cou = object.cou ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.pk_type = object.pk_type ?? 0;
    return message;
  }
};

function createBasePKDetail(): PKDetail {
  return { id: 0, activity_id: 0, pk_id: 0, players: [], rounds: [], pk_type: 0 };
}

export const PKDetail: MessageFns<PKDetail> = {
  fromJSON(object: any): PKDetail {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      pk_id: isSet(object.pk_id) ? globalThis.Number(object.pk_id) : 0,
      players: globalThis.Array.isArray(object?.players) ? object.players.map((e: any) => PKPlayer.fromJSON(e)) : [],
      rounds: globalThis.Array.isArray(object?.rounds) ? object.rounds.map((e: any) => PKRound.fromJSON(e)) : [],
      pk_type: isSet(object.pk_type) ? pKTypeFromJSON(object.pk_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKDetail>, I>>(base?: I): PKDetail {
    return PKDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKDetail>, I>>(object: I): PKDetail {
    const message = createBasePKDetail();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.pk_id = object.pk_id ?? 0;
    message.players = object.players?.map(e => PKPlayer.fromPartial(e)) || [];
    message.rounds = object.rounds?.map(e => PKRound.fromPartial(e)) || [];
    message.pk_type = object.pk_type ?? 0;
    return message;
  }
};

function createBasePKRound(): PKRound {
  return { start_time: 0, end_time: 0, status: 0, players: [] };
}

export const PKRound: MessageFns<PKRound> = {
  fromJSON(object: any): PKRound {
    return {
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      status: isSet(object.status) ? pKRoundStatusFromJSON(object.status) : 0,
      players: globalThis.Array.isArray(object?.players) ? object.players.map((e: any) => PKPlayer.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<PKRound>, I>>(base?: I): PKRound {
    return PKRound.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKRound>, I>>(object: I): PKRound {
    const message = createBasePKRound();
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.status = object.status ?? 0;
    message.players = object.players?.map(e => PKPlayer.fromPartial(e)) || [];
    return message;
  }
};

function createBasePKPlayer(): PKPlayer {
  return { item: undefined, contribute_list: [], special_room_id: 0, special_room_show_id: '' };
}

export const PKPlayer: MessageFns<PKPlayer> = {
  fromJSON(object: any): PKPlayer {
    return {
      item: isSet(object.item) ? PKPlayerItem.fromJSON(object.item) : undefined,
      contribute_list: globalThis.Array.isArray(object?.contribute_list)
        ? object.contribute_list.map((e: any) => PKPlayerEntity.fromJSON(e))
        : [],
      special_room_id: isSet(object.special_room_id) ? globalThis.Number(object.special_room_id) : 0,
      special_room_show_id: isSet(object.special_room_show_id) ? globalThis.String(object.special_room_show_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PKPlayer>, I>>(base?: I): PKPlayer {
    return PKPlayer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKPlayer>, I>>(object: I): PKPlayer {
    const message = createBasePKPlayer();
    message.item =
      object.item !== undefined && object.item !== null ? PKPlayerItem.fromPartial(object.item) : undefined;
    message.contribute_list = object.contribute_list?.map(e => PKPlayerEntity.fromPartial(e)) || [];
    message.special_room_id = object.special_room_id ?? 0;
    message.special_room_show_id = object.special_room_show_id ?? '';
    return message;
  }
};

function createBasePKPlayerItem(): PKPlayerItem {
  return { id: '', score: 0, rank: 0, entities: [], result: 0, show_id: '' };
}

export const PKPlayerItem: MessageFns<PKPlayerItem> = {
  fromJSON(object: any): PKPlayerItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      entities: globalThis.Array.isArray(object?.entities)
        ? object.entities.map((e: any) => PKPlayerEntity.fromJSON(e))
        : [],
      result: isSet(object.result) ? pKEntityResultFromJSON(object.result) : 0,
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PKPlayerItem>, I>>(base?: I): PKPlayerItem {
    return PKPlayerItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKPlayerItem>, I>>(object: I): PKPlayerItem {
    const message = createBasePKPlayerItem();
    message.id = object.id ?? '';
    message.score = object.score ?? 0;
    message.rank = object.rank ?? 0;
    message.entities = object.entities?.map(e => PKPlayerEntity.fromPartial(e)) || [];
    message.result = object.result ?? 0;
    message.show_id = object.show_id ?? '';
    return message;
  }
};

function createBasePKPlayerEntity(): PKPlayerEntity {
  return { id: '', nick: '', avatar: '', show_id: '' };
}

export const PKPlayerEntity: MessageFns<PKPlayerEntity> = {
  fromJSON(object: any): PKPlayerEntity {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      nick: isSet(object.nick) ? globalThis.String(object.nick) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PKPlayerEntity>, I>>(base?: I): PKPlayerEntity {
    return PKPlayerEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKPlayerEntity>, I>>(object: I): PKPlayerEntity {
    const message = createBasePKPlayerEntity();
    message.id = object.id ?? '';
    message.nick = object.nick ?? '';
    message.avatar = object.avatar ?? '';
    message.show_id = object.show_id ?? '';
    return message;
  }
};

function createBaseLotteryConfig(): LotteryConfig {
  return { id: 0, activity_id: 0, title: '', cou: '', start_time: 0, end_time: 0, paid_rules: [], lottery_type: 0 };
}

export const LotteryConfig: MessageFns<LotteryConfig> = {
  fromJSON(object: any): LotteryConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      paid_rules: globalThis.Array.isArray(object?.paid_rules)
        ? object.paid_rules.map((e: any) => LotteryPaidRule.fromJSON(e))
        : [],
      lottery_type: isSet(object.lottery_type) ? lotteryTypeFromJSON(object.lottery_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryConfig>, I>>(base?: I): LotteryConfig {
    return LotteryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryConfig>, I>>(object: I): LotteryConfig {
    const message = createBaseLotteryConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.title = object.title ?? '';
    message.cou = object.cou ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.paid_rules = object.paid_rules?.map(e => LotteryPaidRule.fromPartial(e)) || [];
    message.lottery_type = object.lottery_type ?? 0;
    return message;
  }
};

function createBaseLotteryRewardConfig(): LotteryRewardConfig {
  return {
    id: 0,
    activity_id: 0,
    lottery_id: 0,
    reward_type: 0,
    reward_object_json: '',
    extend_json: '',
    sort: 0,
    title: '',
    img: ''
  };
}

export const LotteryRewardConfig: MessageFns<LotteryRewardConfig> = {
  fromJSON(object: any): LotteryRewardConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      reward_type: isSet(object.reward_type) ? taskRewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      img: isSet(object.img) ? globalThis.String(object.img) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryRewardConfig>, I>>(base?: I): LotteryRewardConfig {
    return LotteryRewardConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryRewardConfig>, I>>(object: I): LotteryRewardConfig {
    const message = createBaseLotteryRewardConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.lottery_id = object.lottery_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.extend_json = object.extend_json ?? '';
    message.sort = object.sort ?? 0;
    message.title = object.title ?? '';
    message.img = object.img ?? '';
    return message;
  }
};

function createBaseLotteryPaidRule(): LotteryPaidRule {
  return { currency: 0, amount: 0, lottery_times: 0 };
}

export const LotteryPaidRule: MessageFns<LotteryPaidRule> = {
  fromJSON(object: any): LotteryPaidRule {
    return {
      currency: isSet(object.currency) ? currencyFromJSON(object.currency) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      lottery_times: isSet(object.lottery_times) ? globalThis.Number(object.lottery_times) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryPaidRule>, I>>(base?: I): LotteryPaidRule {
    return LotteryPaidRule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryPaidRule>, I>>(object: I): LotteryPaidRule {
    const message = createBaseLotteryPaidRule();
    message.currency = object.currency ?? 0;
    message.amount = object.amount ?? 0;
    message.lottery_times = object.lottery_times ?? 0;
    return message;
  }
};

function createBaseLotteryRecord(): LotteryRecord {
  return { record_id: 0, lottery_id: 0, uid: 0, cou: '', details: [], create_time: 0 };
}

export const LotteryRecord: MessageFns<LotteryRecord> = {
  fromJSON(object: any): LotteryRecord {
    return {
      record_id: isSet(object.record_id) ? globalThis.Number(object.record_id) : 0,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      details: globalThis.Array.isArray(object?.details)
        ? object.details.map((e: any) => LotteryRecordDetail.fromJSON(e))
        : [],
      create_time: isSet(object.create_time) ? globalThis.Number(object.create_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryRecord>, I>>(base?: I): LotteryRecord {
    return LotteryRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryRecord>, I>>(object: I): LotteryRecord {
    const message = createBaseLotteryRecord();
    message.record_id = object.record_id ?? 0;
    message.lottery_id = object.lottery_id ?? 0;
    message.uid = object.uid ?? 0;
    message.cou = object.cou ?? '';
    message.details = object.details?.map(e => LotteryRecordDetail.fromPartial(e)) || [];
    message.create_time = object.create_time ?? 0;
    return message;
  }
};

function createBaseLotteryRecordDetail(): LotteryRecordDetail {
  return { reward_id: 0, reward_type: 0, reward_object_json: '', extend_json: '', num: 0 };
}

export const LotteryRecordDetail: MessageFns<LotteryRecordDetail> = {
  fromJSON(object: any): LotteryRecordDetail {
    return {
      reward_id: isSet(object.reward_id) ? globalThis.Number(object.reward_id) : 0,
      reward_type: isSet(object.reward_type) ? taskRewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      num: isSet(object.num) ? globalThis.Number(object.num) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryRecordDetail>, I>>(base?: I): LotteryRecordDetail {
    return LotteryRecordDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryRecordDetail>, I>>(object: I): LotteryRecordDetail {
    const message = createBaseLotteryRecordDetail();
    message.reward_id = object.reward_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.extend_json = object.extend_json ?? '';
    message.num = object.num ?? 0;
    return message;
  }
};

function createBaseLotteryBarrage(): LotteryBarrage {
  return { uid: 0, detail: undefined, nickname: '', avatar: '' };
}

export const LotteryBarrage: MessageFns<LotteryBarrage> = {
  fromJSON(object: any): LotteryBarrage {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      detail: isSet(object.detail) ? LotteryRecordDetail.fromJSON(object.detail) : undefined,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryBarrage>, I>>(base?: I): LotteryBarrage {
    return LotteryBarrage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryBarrage>, I>>(object: I): LotteryBarrage {
    const message = createBaseLotteryBarrage();
    message.uid = object.uid ?? 0;
    message.detail =
      object.detail !== undefined && object.detail !== null
        ? LotteryRecordDetail.fromPartial(object.detail)
        : undefined;
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

function createBaseUserLotteryInfo(): UserLotteryInfo {
  return { lottery_id: 0, times: 0 };
}

export const UserLotteryInfo: MessageFns<UserLotteryInfo> = {
  fromJSON(object: any): UserLotteryInfo {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      times: isSet(object.times) ? globalThis.Number(object.times) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserLotteryInfo>, I>>(base?: I): UserLotteryInfo {
    return UserLotteryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserLotteryInfo>, I>>(object: I): UserLotteryInfo {
    const message = createBaseUserLotteryInfo();
    message.lottery_id = object.lottery_id ?? 0;
    message.times = object.times ?? 0;
    return message;
  }
};

function createBasePandantRank(): PandantRank {
  return { rank: undefined, start_time: 0, end_time: 0, url: {}, background_images: {}, pandant_type: 0 };
}

export const PandantRank: MessageFns<PandantRank> = {
  fromJSON(object: any): PandantRank {
    return {
      rank: isSet(object.rank) ? Rank.fromJSON(object.rank) : undefined,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      url: isObject(object.url)
        ? Object.entries(object.url).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      background_images: isObject(object.background_images)
        ? Object.entries(object.background_images).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      pandant_type: isSet(object.pandant_type) ? pandantTypeFromJSON(object.pandant_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<PandantRank>, I>>(base?: I): PandantRank {
    return PandantRank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PandantRank>, I>>(object: I): PandantRank {
    const message = createBasePandantRank();
    message.rank = object.rank !== undefined && object.rank !== null ? Rank.fromPartial(object.rank) : undefined;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.url = Object.entries(object.url ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.background_images = Object.entries(object.background_images ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.pandant_type = object.pandant_type ?? 0;
    return message;
  }
};

function createBasePandantRank_UrlEntry(): PandantRank_UrlEntry {
  return { key: '', value: '' };
}

export const PandantRank_UrlEntry: MessageFns<PandantRank_UrlEntry> = {
  fromJSON(object: any): PandantRank_UrlEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PandantRank_UrlEntry>, I>>(base?: I): PandantRank_UrlEntry {
    return PandantRank_UrlEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PandantRank_UrlEntry>, I>>(object: I): PandantRank_UrlEntry {
    const message = createBasePandantRank_UrlEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePandantRank_BackgroundImagesEntry(): PandantRank_BackgroundImagesEntry {
  return { key: '', value: '' };
}

export const PandantRank_BackgroundImagesEntry: MessageFns<PandantRank_BackgroundImagesEntry> = {
  fromJSON(object: any): PandantRank_BackgroundImagesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PandantRank_BackgroundImagesEntry>, I>>(
    base?: I
  ): PandantRank_BackgroundImagesEntry {
    return PandantRank_BackgroundImagesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PandantRank_BackgroundImagesEntry>, I>>(
    object: I
  ): PandantRank_BackgroundImagesEntry {
    const message = createBasePandantRank_BackgroundImagesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseButtonH5Config(): ButtonH5Config {
  return { text: '', font_color: '', font_size: 0, bg_color: '', bg_image: '' };
}

export const ButtonH5Config: MessageFns<ButtonH5Config> = {
  fromJSON(object: any): ButtonH5Config {
    return {
      text: isSet(object.text) ? globalThis.String(object.text) : '',
      font_color: isSet(object.font_color) ? globalThis.String(object.font_color) : '',
      font_size: isSet(object.font_size) ? globalThis.Number(object.font_size) : 0,
      bg_color: isSet(object.bg_color) ? globalThis.String(object.bg_color) : '',
      bg_image: isSet(object.bg_image) ? globalThis.String(object.bg_image) : ''
    };
  },

  create<I extends Exact<DeepPartial<ButtonH5Config>, I>>(base?: I): ButtonH5Config {
    return ButtonH5Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ButtonH5Config>, I>>(object: I): ButtonH5Config {
    const message = createBaseButtonH5Config();
    message.text = object.text ?? '';
    message.font_color = object.font_color ?? '';
    message.font_size = object.font_size ?? 0;
    message.bg_color = object.bg_color ?? '';
    message.bg_image = object.bg_image ?? '';
    return message;
  }
};

function createBaseRoomRecommendConfig(): RoomRecommendConfig {
  return { id: 0, activity_id: 0, display_info: 0 };
}

export const RoomRecommendConfig: MessageFns<RoomRecommendConfig> = {
  fromJSON(object: any): RoomRecommendConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      display_info: isSet(object.display_info) ? roomRecommendDisplayInfoFromJSON(object.display_info) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomRecommendConfig>, I>>(base?: I): RoomRecommendConfig {
    return RoomRecommendConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendConfig>, I>>(object: I): RoomRecommendConfig {
    const message = createBaseRoomRecommendConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.display_info = object.display_info ?? 0;
    return message;
  }
};

function createBaseRoomInfo(): RoomInfo {
  return { room_id: 0, show_id: '', owner_uid: 0, room_type: '', room_name: '', room_title: '', cover: '' };
}

export const RoomInfo: MessageFns<RoomInfo> = {
  fromJSON(object: any): RoomInfo {
    return {
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : '',
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      room_type: isSet(object.room_type) ? globalThis.String(object.room_type) : '',
      room_name: isSet(object.room_name) ? globalThis.String(object.room_name) : '',
      room_title: isSet(object.room_title) ? globalThis.String(object.room_title) : '',
      cover: isSet(object.cover) ? globalThis.String(object.cover) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomInfo>, I>>(base?: I): RoomInfo {
    return RoomInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomInfo>, I>>(object: I): RoomInfo {
    const message = createBaseRoomInfo();
    message.room_id = object.room_id ?? 0;
    message.show_id = object.show_id ?? '';
    message.owner_uid = object.owner_uid ?? 0;
    message.room_type = object.room_type ?? '';
    message.room_name = object.room_name ?? '';
    message.room_title = object.room_title ?? '';
    message.cover = object.cover ?? '';
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return { uid: 0, show_uid: '', nickname: '', avatar: '', extend: '' };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseTimeRange(): TimeRange {
  return { start_time: 0, end_time: 0 };
}

export const TimeRange: MessageFns<TimeRange> = {
  fromJSON(object: any): TimeRange {
    return {
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<TimeRange>, I>>(base?: I): TimeRange {
    return TimeRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeRange>, I>>(object: I): TimeRange {
    const message = createBaseTimeRange();
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBasePresentationInfo(): PresentationInfo {
  return { id: 0, config: undefined, pairs: [] };
}

export const PresentationInfo: MessageFns<PresentationInfo> = {
  fromJSON(object: any): PresentationInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      config: isSet(object.config) ? PresentationConfig.fromJSON(object.config) : undefined,
      pairs: globalThis.Array.isArray(object?.pairs)
        ? object.pairs.map((e: any) => PresentationConfigItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<PresentationInfo>, I>>(base?: I): PresentationInfo {
    return PresentationInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationInfo>, I>>(object: I): PresentationInfo {
    const message = createBasePresentationInfo();
    message.id = object.id ?? 0;
    message.config =
      object.config !== undefined && object.config !== null ? PresentationConfig.fromPartial(object.config) : undefined;
    message.pairs = object.pairs?.map(e => PresentationConfigItem.fromPartial(e)) || [];
    return message;
  }
};

function createBasePresentationConfig(): PresentationConfig {
  return { id: 0 };
}

export const PresentationConfig: MessageFns<PresentationConfig> = {
  fromJSON(object: any): PresentationConfig {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<PresentationConfig>, I>>(base?: I): PresentationConfig {
    return PresentationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationConfig>, I>>(object: I): PresentationConfig {
    const message = createBasePresentationConfig();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBasePresentationUserInfo(): PresentationUserInfo {
  return { user_info: undefined, room_info: undefined };
}

export const PresentationUserInfo: MessageFns<PresentationUserInfo> = {
  fromJSON(object: any): PresentationUserInfo {
    return {
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      room_info: isSet(object.room_info) ? RoomInfo.fromJSON(object.room_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PresentationUserInfo>, I>>(base?: I): PresentationUserInfo {
    return PresentationUserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationUserInfo>, I>>(object: I): PresentationUserInfo {
    const message = createBasePresentationUserInfo();
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.room_info =
      object.room_info !== undefined && object.room_info !== null ? RoomInfo.fromPartial(object.room_info) : undefined;
    return message;
  }
};

function createBasePresentationConfigItem(): PresentationConfigItem {
  return { user_info_list: [], title: '', desc: '' };
}

export const PresentationConfigItem: MessageFns<PresentationConfigItem> = {
  fromJSON(object: any): PresentationConfigItem {
    return {
      user_info_list: globalThis.Array.isArray(object?.user_info_list)
        ? object.user_info_list.map((e: any) => PresentationUserInfo.fromJSON(e))
        : [],
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<PresentationConfigItem>, I>>(base?: I): PresentationConfigItem {
    return PresentationConfigItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationConfigItem>, I>>(object: I): PresentationConfigItem {
    const message = createBasePresentationConfigItem();
    message.user_info_list = object.user_info_list?.map(e => PresentationUserInfo.fromPartial(e)) || [];
    message.title = object.title ?? '';
    message.desc = object.desc ?? '';
    return message;
  }
};

function createBaseUserRank(): UserRank {
  return { items: [] };
}

export const UserRank: MessageFns<UserRank> = {
  fromJSON(object: any): UserRank {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => UserRankItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<UserRank>, I>>(base?: I): UserRank {
    return UserRank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRank>, I>>(object: I): UserRank {
    const message = createBaseUserRank();
    message.items = object.items?.map(e => UserRankItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUserRankItem(): UserRankItem {
  return { user: undefined, rank: 0, score: 0 };
}

export const UserRankItem: MessageFns<UserRankItem> = {
  fromJSON(object: any): UserRankItem {
    return {
      user: isSet(object.user) ? UserInfo.fromJSON(object.user) : undefined,
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserRankItem>, I>>(base?: I): UserRankItem {
    return UserRankItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRankItem>, I>>(object: I): UserRankItem {
    const message = createBaseUserRankItem();
    message.user = object.user !== undefined && object.user !== null ? UserInfo.fromPartial(object.user) : undefined;
    message.rank = object.rank ?? 0;
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseUserInfoExtend(): UserInfoExtend {
  return {
    age: 0,
    gender: 0,
    using_privilege: undefined,
    wealth_level: 0,
    wealth_value: 0,
    wealth_img: {},
    charm_level: 0,
    charm_value: 0,
    charm_img: {},
    uid: 0,
    reward_num: 0
  };
}

export const UserInfoExtend: MessageFns<UserInfoExtend> = {
  fromJSON(object: any): UserInfoExtend {
    return {
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      using_privilege: isSet(object.using_privilege) ? UsingPrivilege.fromJSON(object.using_privilege) : undefined,
      wealth_level: isSet(object.wealth_level) ? globalThis.Number(object.wealth_level) : 0,
      wealth_value: isSet(object.wealth_value) ? globalThis.Number(object.wealth_value) : 0,
      wealth_img: isObject(object.wealth_img)
        ? Object.entries(object.wealth_img).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      charm_level: isSet(object.charm_level) ? globalThis.Number(object.charm_level) : 0,
      charm_value: isSet(object.charm_value) ? globalThis.Number(object.charm_value) : 0,
      charm_img: isObject(object.charm_img)
        ? Object.entries(object.charm_img).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      reward_num: isSet(object.reward_num) ? globalThis.Number(object.reward_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserInfoExtend>, I>>(base?: I): UserInfoExtend {
    return UserInfoExtend.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfoExtend>, I>>(object: I): UserInfoExtend {
    const message = createBaseUserInfoExtend();
    message.age = object.age ?? 0;
    message.gender = object.gender ?? 0;
    message.using_privilege =
      object.using_privilege !== undefined && object.using_privilege !== null
        ? UsingPrivilege.fromPartial(object.using_privilege)
        : undefined;
    message.wealth_level = object.wealth_level ?? 0;
    message.wealth_value = object.wealth_value ?? 0;
    message.wealth_img = Object.entries(object.wealth_img ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.charm_level = object.charm_level ?? 0;
    message.charm_value = object.charm_value ?? 0;
    message.charm_img = Object.entries(object.charm_img ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.uid = object.uid ?? 0;
    message.reward_num = object.reward_num ?? 0;
    return message;
  }
};

function createBaseUserInfoExtend_WealthImgEntry(): UserInfoExtend_WealthImgEntry {
  return { key: '', value: '' };
}

export const UserInfoExtend_WealthImgEntry: MessageFns<UserInfoExtend_WealthImgEntry> = {
  fromJSON(object: any): UserInfoExtend_WealthImgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfoExtend_WealthImgEntry>, I>>(base?: I): UserInfoExtend_WealthImgEntry {
    return UserInfoExtend_WealthImgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfoExtend_WealthImgEntry>, I>>(
    object: I
  ): UserInfoExtend_WealthImgEntry {
    const message = createBaseUserInfoExtend_WealthImgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUserInfoExtend_CharmImgEntry(): UserInfoExtend_CharmImgEntry {
  return { key: '', value: '' };
}

export const UserInfoExtend_CharmImgEntry: MessageFns<UserInfoExtend_CharmImgEntry> = {
  fromJSON(object: any): UserInfoExtend_CharmImgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfoExtend_CharmImgEntry>, I>>(base?: I): UserInfoExtend_CharmImgEntry {
    return UserInfoExtend_CharmImgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfoExtend_CharmImgEntry>, I>>(object: I): UserInfoExtend_CharmImgEntry {
    const message = createBaseUserInfoExtend_CharmImgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRoomInfoExtend(): RoomInfoExtend {
  return { room_owner_uid: 0, room_owner_nick: '', room_tags: [], room_id: 0, reward_num: 0 };
}

export const RoomInfoExtend: MessageFns<RoomInfoExtend> = {
  fromJSON(object: any): RoomInfoExtend {
    return {
      room_owner_uid: isSet(object.room_owner_uid) ? globalThis.Number(object.room_owner_uid) : 0,
      room_owner_nick: isSet(object.room_owner_nick) ? globalThis.String(object.room_owner_nick) : '',
      room_tags: globalThis.Array.isArray(object?.room_tags)
        ? object.room_tags.map((e: any) => RoomTag.fromJSON(e))
        : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      reward_num: isSet(object.reward_num) ? globalThis.Number(object.reward_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomInfoExtend>, I>>(base?: I): RoomInfoExtend {
    return RoomInfoExtend.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomInfoExtend>, I>>(object: I): RoomInfoExtend {
    const message = createBaseRoomInfoExtend();
    message.room_owner_uid = object.room_owner_uid ?? 0;
    message.room_owner_nick = object.room_owner_nick ?? '';
    message.room_tags = object.room_tags?.map(e => RoomTag.fromPartial(e)) || [];
    message.room_id = object.room_id ?? 0;
    message.reward_num = object.reward_num ?? 0;
    return message;
  }
};

function createBaseFamilyInfoExtend(): FamilyInfoExtend {
  return { family_id: 0, reward_num: 0 };
}

export const FamilyInfoExtend: MessageFns<FamilyInfoExtend> = {
  fromJSON(object: any): FamilyInfoExtend {
    return {
      family_id: isSet(object.family_id) ? globalThis.Number(object.family_id) : 0,
      reward_num: isSet(object.reward_num) ? globalThis.Number(object.reward_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<FamilyInfoExtend>, I>>(base?: I): FamilyInfoExtend {
    return FamilyInfoExtend.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FamilyInfoExtend>, I>>(object: I): FamilyInfoExtend {
    const message = createBaseFamilyInfoExtend();
    message.family_id = object.family_id ?? 0;
    message.reward_num = object.reward_num ?? 0;
    return message;
  }
};

function createBaseRewardItem(): RewardItem {
  return {
    item_id: '',
    item_name: '',
    i18n_item_name: {},
    item_icon: '',
    i18n_item_icon: {},
    item_animation: '',
    reward_sub_type: '',
    expand: '',
    sub_category_ids: []
  };
}

export const RewardItem: MessageFns<RewardItem> = {
  fromJSON(object: any): RewardItem {
    return {
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      i18n_item_name: isObject(object.i18n_item_name)
        ? Object.entries(object.i18n_item_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_icon: isSet(object.item_icon) ? globalThis.String(object.item_icon) : '',
      i18n_item_icon: isObject(object.i18n_item_icon)
        ? Object.entries(object.i18n_item_icon).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_animation: isSet(object.item_animation) ? globalThis.String(object.item_animation) : '',
      reward_sub_type: isSet(object.reward_sub_type) ? globalThis.String(object.reward_sub_type) : '',
      expand: isSet(object.expand) ? globalThis.String(object.expand) : '',
      sub_category_ids: globalThis.Array.isArray(object?.sub_category_ids)
        ? object.sub_category_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RewardItem>, I>>(base?: I): RewardItem {
    return RewardItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem>, I>>(object: I): RewardItem {
    const message = createBaseRewardItem();
    message.item_id = object.item_id ?? '';
    message.item_name = object.item_name ?? '';
    message.i18n_item_name = Object.entries(object.i18n_item_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_icon = object.item_icon ?? '';
    message.i18n_item_icon = Object.entries(object.i18n_item_icon ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_animation = object.item_animation ?? '';
    message.reward_sub_type = object.reward_sub_type ?? '';
    message.expand = object.expand ?? '';
    message.sub_category_ids = object.sub_category_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseRewardItem_I18nItemNameEntry(): RewardItem_I18nItemNameEntry {
  return { key: '', value: '' };
}

export const RewardItem_I18nItemNameEntry: MessageFns<RewardItem_I18nItemNameEntry> = {
  fromJSON(object: any): RewardItem_I18nItemNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardItem_I18nItemNameEntry>, I>>(base?: I): RewardItem_I18nItemNameEntry {
    return RewardItem_I18nItemNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem_I18nItemNameEntry>, I>>(object: I): RewardItem_I18nItemNameEntry {
    const message = createBaseRewardItem_I18nItemNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRewardItem_I18nItemIconEntry(): RewardItem_I18nItemIconEntry {
  return { key: '', value: '' };
}

export const RewardItem_I18nItemIconEntry: MessageFns<RewardItem_I18nItemIconEntry> = {
  fromJSON(object: any): RewardItem_I18nItemIconEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardItem_I18nItemIconEntry>, I>>(base?: I): RewardItem_I18nItemIconEntry {
    return RewardItem_I18nItemIconEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem_I18nItemIconEntry>, I>>(object: I): RewardItem_I18nItemIconEntry {
    const message = createBaseRewardItem_I18nItemIconEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePrizePoolWeight(): PrizePoolWeight {
  return { min: 0, max: 0, weight: 0 };
}

export const PrizePoolWeight: MessageFns<PrizePoolWeight> = {
  fromJSON(object: any): PrizePoolWeight {
    return {
      min: isSet(object.min) ? globalThis.Number(object.min) : 0,
      max: isSet(object.max) ? globalThis.Number(object.max) : 0,
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizePoolWeight>, I>>(base?: I): PrizePoolWeight {
    return PrizePoolWeight.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizePoolWeight>, I>>(object: I): PrizePoolWeight {
    const message = createBasePrizePoolWeight();
    message.min = object.min ?? 0;
    message.max = object.max ?? 0;
    message.weight = object.weight ?? 0;
    return message;
  }
};

function createBasePrizePoolConfig(): PrizePoolConfig {
  return {
    id: 0,
    activity_id: 0,
    cal_type: 0,
    auto_reward: false,
    reward_category_key: '',
    reward_item_id: '',
    min_rank_score: 0,
    max_pool_num: 0,
    weight_type: 0,
    weight_config_list: [],
    relate_rank_id: 0,
    start_time: 0,
    end_time: 0,
    cycle: 0,
    cur_pool_num: 0,
    pool_ratio: 0,
    prize_rank_score_limit: 0,
    component_type: '',
    status: '',
    component_id: 0
  };
}

export const PrizePoolConfig: MessageFns<PrizePoolConfig> = {
  fromJSON(object: any): PrizePoolConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      cal_type: isSet(object.cal_type) ? prizePoolCalTypeFromJSON(object.cal_type) : 0,
      auto_reward: isSet(object.auto_reward) ? globalThis.Boolean(object.auto_reward) : false,
      reward_category_key: isSet(object.reward_category_key) ? globalThis.String(object.reward_category_key) : '',
      reward_item_id: isSet(object.reward_item_id) ? globalThis.String(object.reward_item_id) : '',
      min_rank_score: isSet(object.min_rank_score) ? globalThis.Number(object.min_rank_score) : 0,
      max_pool_num: isSet(object.max_pool_num) ? globalThis.Number(object.max_pool_num) : 0,
      weight_type: isSet(object.weight_type) ? prizePoolWeightTypeFromJSON(object.weight_type) : 0,
      weight_config_list: globalThis.Array.isArray(object?.weight_config_list)
        ? object.weight_config_list.map((e: any) => PrizePoolWeight.fromJSON(e))
        : [],
      relate_rank_id: isSet(object.relate_rank_id) ? globalThis.Number(object.relate_rank_id) : 0,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      cycle: isSet(object.cycle) ? cycleTypeFromJSON(object.cycle) : 0,
      cur_pool_num: isSet(object.cur_pool_num) ? globalThis.Number(object.cur_pool_num) : 0,
      pool_ratio: isSet(object.pool_ratio) ? globalThis.Number(object.pool_ratio) : 0,
      prize_rank_score_limit: isSet(object.prize_rank_score_limit)
        ? globalThis.Number(object.prize_rank_score_limit)
        : 0,
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      component_id: isSet(object.component_id) ? globalThis.Number(object.component_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizePoolConfig>, I>>(base?: I): PrizePoolConfig {
    return PrizePoolConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizePoolConfig>, I>>(object: I): PrizePoolConfig {
    const message = createBasePrizePoolConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.cal_type = object.cal_type ?? 0;
    message.auto_reward = object.auto_reward ?? false;
    message.reward_category_key = object.reward_category_key ?? '';
    message.reward_item_id = object.reward_item_id ?? '';
    message.min_rank_score = object.min_rank_score ?? 0;
    message.max_pool_num = object.max_pool_num ?? 0;
    message.weight_type = object.weight_type ?? 0;
    message.weight_config_list = object.weight_config_list?.map(e => PrizePoolWeight.fromPartial(e)) || [];
    message.relate_rank_id = object.relate_rank_id ?? 0;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.cycle = object.cycle ?? 0;
    message.cur_pool_num = object.cur_pool_num ?? 0;
    message.pool_ratio = object.pool_ratio ?? 0;
    message.prize_rank_score_limit = object.prize_rank_score_limit ?? 0;
    message.component_type = object.component_type ?? '';
    message.status = object.status ?? '';
    message.component_id = object.component_id ?? 0;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
