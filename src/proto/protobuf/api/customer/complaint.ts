// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/customer/complaint.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.customer';

/** 相关分类枚举 */
export enum CategoryType {
  /** CATEGORY_TYPE_NONE - 无 */
  CATEGORY_TYPE_NONE = 0,
  /** CATEGORY_TYPE_PAY - 支付类型 */
  CATEGORY_TYPE_PAY = 1,
  /** CATEGORY_TYPE_OTHERS - 其他类型 */
  CATEGORY_TYPE_OTHERS = 2,
  /** CATEGORY_TYPE_Withdraw - 提现类型 */
  CATEGORY_TYPE_Withdraw = 3,
  UNRECOGNIZED = -1
}

export function categoryTypeFromJSON(object: any): CategoryType {
  switch (object) {
    case 0:
    case 'CATEGORY_TYPE_NONE':
      return CategoryType.CATEGORY_TYPE_NONE;
    case 1:
    case 'CATEGORY_TYPE_PAY':
      return CategoryType.CATEGORY_TYPE_PAY;
    case 2:
    case 'CATEGORY_TYPE_OTHERS':
      return CategoryType.CATEGORY_TYPE_OTHERS;
    case 3:
    case 'CATEGORY_TYPE_Withdraw':
      return CategoryType.CATEGORY_TYPE_Withdraw;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryType.UNRECOGNIZED;
  }
}

export enum State {
  /** STATE_NONE - 无 */
  STATE_NONE = 0,
  /** STATE_RESOLVED - 已通过 */
  STATE_RESOLVED = 1,
  /** STATE_PROCESSING - 处理中 */
  STATE_PROCESSING = 2,
  /** STATE_INVALID - 拒绝 */
  STATE_INVALID = 3,
  /** STATE_PENDING - 挂起 */
  STATE_PENDING = 4,
  UNRECOGNIZED = -1
}

export function stateFromJSON(object: any): State {
  switch (object) {
    case 0:
    case 'STATE_NONE':
      return State.STATE_NONE;
    case 1:
    case 'STATE_RESOLVED':
      return State.STATE_RESOLVED;
    case 2:
    case 'STATE_PROCESSING':
      return State.STATE_PROCESSING;
    case 3:
    case 'STATE_INVALID':
      return State.STATE_INVALID;
    case 4:
    case 'STATE_PENDING':
      return State.STATE_PENDING;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return State.UNRECOGNIZED;
  }
}

export interface ListCategoryReq {
  /** 申诉类型id */
  complaint_type_id: number[];
}

export interface ComplaintType {
  /** 子申诉类型ID */
  complaint_type_id: number;
  /** 相关分类 */
  complaint_category_id: number;
  /** 文案 */
  text: string;
  /** icon */
  icon: string;
  /** 跳转FAQ页面路由 */
  url: string;
  /** 黑暗模式icon */
  icon_dark: string;
  /** 相关分类 */
  category_type: CategoryType;
}

export interface ListCategoryRsp {
  /** 子申诉类型详情 */
  complaint_type: ComplaintType[];
  /** 是否有新回复 */
  new: boolean;
}

export interface ListReq {
  /** 子申诉类型ID */
  complaint_type_id: number;
}

export interface Faq {
  /** 问题 */
  issues: string;
  /** 答案 */
  answer: string;
  /** 图片 */
  images: string[];
}

export interface ListRsp {
  /** FAQ列表 */
  faq: Faq[];
}

/** 支付相关 */
export interface PayType {
  /** 支付方式ID */
  id: number;
  /** 订单号 */
  number: string;
  /** 平台订单号 */
  platform_order_id: string;
}

/** 普通相关 */
export interface CommonType {
  /** 问题描述 */
  detail: string;
  /** 系统提示文案 */
  system_prompt: string;
}

/** 提现相关 */
export interface WithdrawType {
  /** 问题描述 */
  detail: string;
  /** 订单号 */
  number: string;
}

export interface CreateReq {
  /** 子申诉类型ID */
  complaint_type_id: number;
  /** 相关分类 */
  complaint_category_id: number;
  /** 支付相关的参数 */
  pay_params: PayType | undefined;
  /** 常规相关的参数 */
  common_params: CommonType | undefined;
  /** 提现相关的参数 */
  withdraw_params: WithdrawType | undefined;
  /** pkg包名 */
  pkg: string;
  /** 出现问题时间 秒 */
  time: number;
  /** 附件(支付截图,图片,银行流水) */
  attachment: string[];
}

export interface CreateRsp {
  /** 申诉工单ID */
  id: number;
}

export interface ListPaymentReq {}

export interface Payment {
  /** 支付方式ID */
  id: number;
  /** 支付方式 */
  method: string;
  /** 支付截图副标题 */
  screenshots_text: string;
}

export interface ListPaymentRsp {
  /** 支付类型 */
  Payment: Payment[];
}

export interface ListRecordsReq {
  /** 页码 */
  page: number;
  /** 页数 */
  pageSize: number;
}

export interface Records {
  /** 子申诉类型ID */
  complaint_type_id: number;
  /** 子申诉类型标题 */
  title: string;
  /** 状态 */
  state: State;
  /** 记录ID */
  id: number;
  /** 是否有新回复 */
  new: boolean;
  /** 时间 */
  time: number;
}

export interface ListRecordsRsp {
  /** 记录列表 */
  records: Records[];
  /** 总数 */
  count: number;
}

export interface GetRecordReq {
  /** 记录ID */
  id: number;
}

export interface Reply {
  /** 回复 */
  reply: string;
  /** 回复时间 秒 */
  reply_time: number;
  /** 图片 */
  images: string[];
}

export interface GetRecordRsp {
  /** 记录ID */
  id: number;
  /** 支付相关的详情 */
  pay_detail: PayType | undefined;
  /** 常规相关的详情 */
  common_detail: CommonType | undefined;
  /** 提现相关的详情 */
  withdraw_detail: WithdrawType | undefined;
  /** 回复 */
  replys: Reply[];
  /** 相关分类 */
  category_type: CategoryType;
  /** 子申诉类型文案 */
  complaint_type_text: string;
  /** 子申诉类型ID */
  complaint_type_id: number;
  /** 类型ID */
  complaint_category_id: number;
  /** 出现问题时间 秒 */
  time: number;
  /** 附件(支付截图,图片,银行流水) */
  attachment: string[];
  /** 状态 */
  state: State;
}

export interface CustomerPubPara {
  /** 如果为m2，则为m2；其他则为m1 */
  user_business: string;
}

function createBaseListCategoryReq(): ListCategoryReq {
  return { complaint_type_id: [] };
}

export const ListCategoryReq: MessageFns<ListCategoryReq> = {
  fromJSON(object: any): ListCategoryReq {
    return {
      complaint_type_id: globalThis.Array.isArray(object?.complaint_type_id)
        ? object.complaint_type_id.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListCategoryReq>, I>>(base?: I): ListCategoryReq {
    return ListCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryReq>, I>>(object: I): ListCategoryReq {
    const message = createBaseListCategoryReq();
    message.complaint_type_id = object.complaint_type_id?.map(e => e) || [];
    return message;
  }
};

function createBaseComplaintType(): ComplaintType {
  return {
    complaint_type_id: 0,
    complaint_category_id: 0,
    text: '',
    icon: '',
    url: '',
    icon_dark: '',
    category_type: 0
  };
}

export const ComplaintType: MessageFns<ComplaintType> = {
  fromJSON(object: any): ComplaintType {
    return {
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      text: isSet(object.text) ? globalThis.String(object.text) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      icon_dark: isSet(object.icon_dark) ? globalThis.String(object.icon_dark) : '',
      category_type: isSet(object.category_type) ? categoryTypeFromJSON(object.category_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ComplaintType>, I>>(base?: I): ComplaintType {
    return ComplaintType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ComplaintType>, I>>(object: I): ComplaintType {
    const message = createBaseComplaintType();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.text = object.text ?? '';
    message.icon = object.icon ?? '';
    message.url = object.url ?? '';
    message.icon_dark = object.icon_dark ?? '';
    message.category_type = object.category_type ?? 0;
    return message;
  }
};

function createBaseListCategoryRsp(): ListCategoryRsp {
  return { complaint_type: [], new: false };
}

export const ListCategoryRsp: MessageFns<ListCategoryRsp> = {
  fromJSON(object: any): ListCategoryRsp {
    return {
      complaint_type: globalThis.Array.isArray(object?.complaint_type)
        ? object.complaint_type.map((e: any) => ComplaintType.fromJSON(e))
        : [],
      new: isSet(object.new) ? globalThis.Boolean(object.new) : false
    };
  },

  create<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(base?: I): ListCategoryRsp {
    return ListCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(object: I): ListCategoryRsp {
    const message = createBaseListCategoryRsp();
    message.complaint_type = object.complaint_type?.map(e => ComplaintType.fromPartial(e)) || [];
    message.new = object.new ?? false;
    return message;
  }
};

function createBaseListReq(): ListReq {
  return { complaint_type_id: 0 };
}

export const ListReq: MessageFns<ListReq> = {
  fromJSON(object: any): ListReq {
    return { complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListReq>, I>>(base?: I): ListReq {
    return ListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListReq>, I>>(object: I): ListReq {
    const message = createBaseListReq();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    return message;
  }
};

function createBaseFaq(): Faq {
  return { issues: '', answer: '', images: [] };
}

export const Faq: MessageFns<Faq> = {
  fromJSON(object: any): Faq {
    return {
      issues: isSet(object.issues) ? globalThis.String(object.issues) : '',
      answer: isSet(object.answer) ? globalThis.String(object.answer) : '',
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Faq>, I>>(base?: I): Faq {
    return Faq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Faq>, I>>(object: I): Faq {
    const message = createBaseFaq();
    message.issues = object.issues ?? '';
    message.answer = object.answer ?? '';
    message.images = object.images?.map(e => e) || [];
    return message;
  }
};

function createBaseListRsp(): ListRsp {
  return { faq: [] };
}

export const ListRsp: MessageFns<ListRsp> = {
  fromJSON(object: any): ListRsp {
    return { faq: globalThis.Array.isArray(object?.faq) ? object.faq.map((e: any) => Faq.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListRsp>, I>>(base?: I): ListRsp {
    return ListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRsp>, I>>(object: I): ListRsp {
    const message = createBaseListRsp();
    message.faq = object.faq?.map(e => Faq.fromPartial(e)) || [];
    return message;
  }
};

function createBasePayType(): PayType {
  return { id: 0, number: '', platform_order_id: '' };
}

export const PayType: MessageFns<PayType> = {
  fromJSON(object: any): PayType {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      number: isSet(object.number) ? globalThis.String(object.number) : '',
      platform_order_id: isSet(object.platform_order_id) ? globalThis.String(object.platform_order_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayType>, I>>(base?: I): PayType {
    return PayType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayType>, I>>(object: I): PayType {
    const message = createBasePayType();
    message.id = object.id ?? 0;
    message.number = object.number ?? '';
    message.platform_order_id = object.platform_order_id ?? '';
    return message;
  }
};

function createBaseCommonType(): CommonType {
  return { detail: '', system_prompt: '' };
}

export const CommonType: MessageFns<CommonType> = {
  fromJSON(object: any): CommonType {
    return {
      detail: isSet(object.detail) ? globalThis.String(object.detail) : '',
      system_prompt: isSet(object.system_prompt) ? globalThis.String(object.system_prompt) : ''
    };
  },

  create<I extends Exact<DeepPartial<CommonType>, I>>(base?: I): CommonType {
    return CommonType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommonType>, I>>(object: I): CommonType {
    const message = createBaseCommonType();
    message.detail = object.detail ?? '';
    message.system_prompt = object.system_prompt ?? '';
    return message;
  }
};

function createBaseWithdrawType(): WithdrawType {
  return { detail: '', number: '' };
}

export const WithdrawType: MessageFns<WithdrawType> = {
  fromJSON(object: any): WithdrawType {
    return {
      detail: isSet(object.detail) ? globalThis.String(object.detail) : '',
      number: isSet(object.number) ? globalThis.String(object.number) : ''
    };
  },

  create<I extends Exact<DeepPartial<WithdrawType>, I>>(base?: I): WithdrawType {
    return WithdrawType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawType>, I>>(object: I): WithdrawType {
    const message = createBaseWithdrawType();
    message.detail = object.detail ?? '';
    message.number = object.number ?? '';
    return message;
  }
};

function createBaseCreateReq(): CreateReq {
  return {
    complaint_type_id: 0,
    complaint_category_id: 0,
    pay_params: undefined,
    common_params: undefined,
    withdraw_params: undefined,
    pkg: '',
    time: 0,
    attachment: []
  };
}

export const CreateReq: MessageFns<CreateReq> = {
  fromJSON(object: any): CreateReq {
    return {
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      pay_params: isSet(object.pay_params) ? PayType.fromJSON(object.pay_params) : undefined,
      common_params: isSet(object.common_params) ? CommonType.fromJSON(object.common_params) : undefined,
      withdraw_params: isSet(object.withdraw_params) ? WithdrawType.fromJSON(object.withdraw_params) : undefined,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      attachment: globalThis.Array.isArray(object?.attachment)
        ? object.attachment.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CreateReq>, I>>(base?: I): CreateReq {
    return CreateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateReq>, I>>(object: I): CreateReq {
    const message = createBaseCreateReq();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.pay_params =
      object.pay_params !== undefined && object.pay_params !== null
        ? PayType.fromPartial(object.pay_params)
        : undefined;
    message.common_params =
      object.common_params !== undefined && object.common_params !== null
        ? CommonType.fromPartial(object.common_params)
        : undefined;
    message.withdraw_params =
      object.withdraw_params !== undefined && object.withdraw_params !== null
        ? WithdrawType.fromPartial(object.withdraw_params)
        : undefined;
    message.pkg = object.pkg ?? '';
    message.time = object.time ?? 0;
    message.attachment = object.attachment?.map(e => e) || [];
    return message;
  }
};

function createBaseCreateRsp(): CreateRsp {
  return { id: 0 };
}

export const CreateRsp: MessageFns<CreateRsp> = {
  fromJSON(object: any): CreateRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRsp>, I>>(base?: I): CreateRsp {
    return CreateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRsp>, I>>(object: I): CreateRsp {
    const message = createBaseCreateRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListPaymentReq(): ListPaymentReq {
  return {};
}

export const ListPaymentReq: MessageFns<ListPaymentReq> = {
  fromJSON(_: any): ListPaymentReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListPaymentReq>, I>>(base?: I): ListPaymentReq {
    return ListPaymentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPaymentReq>, I>>(_: I): ListPaymentReq {
    const message = createBaseListPaymentReq();
    return message;
  }
};

function createBasePayment(): Payment {
  return { id: 0, method: '', screenshots_text: '' };
}

export const Payment: MessageFns<Payment> = {
  fromJSON(object: any): Payment {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      method: isSet(object.method) ? globalThis.String(object.method) : '',
      screenshots_text: isSet(object.screenshots_text) ? globalThis.String(object.screenshots_text) : ''
    };
  },

  create<I extends Exact<DeepPartial<Payment>, I>>(base?: I): Payment {
    return Payment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Payment>, I>>(object: I): Payment {
    const message = createBasePayment();
    message.id = object.id ?? 0;
    message.method = object.method ?? '';
    message.screenshots_text = object.screenshots_text ?? '';
    return message;
  }
};

function createBaseListPaymentRsp(): ListPaymentRsp {
  return { Payment: [] };
}

export const ListPaymentRsp: MessageFns<ListPaymentRsp> = {
  fromJSON(object: any): ListPaymentRsp {
    return {
      Payment: globalThis.Array.isArray(object?.Payment) ? object.Payment.map((e: any) => Payment.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListPaymentRsp>, I>>(base?: I): ListPaymentRsp {
    return ListPaymentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPaymentRsp>, I>>(object: I): ListPaymentRsp {
    const message = createBaseListPaymentRsp();
    message.Payment = object.Payment?.map(e => Payment.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListRecordsReq(): ListRecordsReq {
  return { page: 0, pageSize: 0 };
}

export const ListRecordsReq: MessageFns<ListRecordsReq> = {
  fromJSON(object: any): ListRecordsReq {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRecordsReq>, I>>(base?: I): ListRecordsReq {
    return ListRecordsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRecordsReq>, I>>(object: I): ListRecordsReq {
    const message = createBaseListRecordsReq();
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    return message;
  }
};

function createBaseRecords(): Records {
  return { complaint_type_id: 0, title: '', state: 0, id: 0, new: false, time: 0 };
}

export const Records: MessageFns<Records> = {
  fromJSON(object: any): Records {
    return {
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      new: isSet(object.new) ? globalThis.Boolean(object.new) : false,
      time: isSet(object.time) ? globalThis.Number(object.time) : 0
    };
  },

  create<I extends Exact<DeepPartial<Records>, I>>(base?: I): Records {
    return Records.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Records>, I>>(object: I): Records {
    const message = createBaseRecords();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.title = object.title ?? '';
    message.state = object.state ?? 0;
    message.id = object.id ?? 0;
    message.new = object.new ?? false;
    message.time = object.time ?? 0;
    return message;
  }
};

function createBaseListRecordsRsp(): ListRecordsRsp {
  return { records: [], count: 0 };
}

export const ListRecordsRsp: MessageFns<ListRecordsRsp> = {
  fromJSON(object: any): ListRecordsRsp {
    return {
      records: globalThis.Array.isArray(object?.records) ? object.records.map((e: any) => Records.fromJSON(e)) : [],
      count: isSet(object.count) ? globalThis.Number(object.count) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListRecordsRsp>, I>>(base?: I): ListRecordsRsp {
    return ListRecordsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRecordsRsp>, I>>(object: I): ListRecordsRsp {
    const message = createBaseListRecordsRsp();
    message.records = object.records?.map(e => Records.fromPartial(e)) || [];
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseGetRecordReq(): GetRecordReq {
  return { id: 0 };
}

export const GetRecordReq: MessageFns<GetRecordReq> = {
  fromJSON(object: any): GetRecordReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRecordReq>, I>>(base?: I): GetRecordReq {
    return GetRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecordReq>, I>>(object: I): GetRecordReq {
    const message = createBaseGetRecordReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseReply(): Reply {
  return { reply: '', reply_time: 0, images: [] };
}

export const Reply: MessageFns<Reply> = {
  fromJSON(object: any): Reply {
    return {
      reply: isSet(object.reply) ? globalThis.String(object.reply) : '',
      reply_time: isSet(object.reply_time) ? globalThis.Number(object.reply_time) : 0,
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Reply>, I>>(base?: I): Reply {
    return Reply.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reply>, I>>(object: I): Reply {
    const message = createBaseReply();
    message.reply = object.reply ?? '';
    message.reply_time = object.reply_time ?? 0;
    message.images = object.images?.map(e => e) || [];
    return message;
  }
};

function createBaseGetRecordRsp(): GetRecordRsp {
  return {
    id: 0,
    pay_detail: undefined,
    common_detail: undefined,
    withdraw_detail: undefined,
    replys: [],
    category_type: 0,
    complaint_type_text: '',
    complaint_type_id: 0,
    complaint_category_id: 0,
    time: 0,
    attachment: [],
    state: 0
  };
}

export const GetRecordRsp: MessageFns<GetRecordRsp> = {
  fromJSON(object: any): GetRecordRsp {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      pay_detail: isSet(object.pay_detail) ? PayType.fromJSON(object.pay_detail) : undefined,
      common_detail: isSet(object.common_detail) ? CommonType.fromJSON(object.common_detail) : undefined,
      withdraw_detail: isSet(object.withdraw_detail) ? WithdrawType.fromJSON(object.withdraw_detail) : undefined,
      replys: globalThis.Array.isArray(object?.replys) ? object.replys.map((e: any) => Reply.fromJSON(e)) : [],
      category_type: isSet(object.category_type) ? categoryTypeFromJSON(object.category_type) : 0,
      complaint_type_text: isSet(object.complaint_type_text) ? globalThis.String(object.complaint_type_text) : '',
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      attachment: globalThis.Array.isArray(object?.attachment)
        ? object.attachment.map((e: any) => globalThis.String(e))
        : [],
      state: isSet(object.state) ? stateFromJSON(object.state) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetRecordRsp>, I>>(base?: I): GetRecordRsp {
    return GetRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRecordRsp>, I>>(object: I): GetRecordRsp {
    const message = createBaseGetRecordRsp();
    message.id = object.id ?? 0;
    message.pay_detail =
      object.pay_detail !== undefined && object.pay_detail !== null
        ? PayType.fromPartial(object.pay_detail)
        : undefined;
    message.common_detail =
      object.common_detail !== undefined && object.common_detail !== null
        ? CommonType.fromPartial(object.common_detail)
        : undefined;
    message.withdraw_detail =
      object.withdraw_detail !== undefined && object.withdraw_detail !== null
        ? WithdrawType.fromPartial(object.withdraw_detail)
        : undefined;
    message.replys = object.replys?.map(e => Reply.fromPartial(e)) || [];
    message.category_type = object.category_type ?? 0;
    message.complaint_type_text = object.complaint_type_text ?? '';
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.time = object.time ?? 0;
    message.attachment = object.attachment?.map(e => e) || [];
    message.state = object.state ?? 0;
    return message;
  }
};

function createBaseCustomerPubPara(): CustomerPubPara {
  return { user_business: '' };
}

export const CustomerPubPara: MessageFns<CustomerPubPara> = {
  fromJSON(object: any): CustomerPubPara {
    return { user_business: isSet(object.user_business) ? globalThis.String(object.user_business) : '' };
  },

  create<I extends Exact<DeepPartial<CustomerPubPara>, I>>(base?: I): CustomerPubPara {
    return CustomerPubPara.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomerPubPara>, I>>(object: I): CustomerPubPara {
    const message = createBaseCustomerPubPara();
    message.user_business = object.user_business ?? '';
    return message;
  }
};

export type ComplaintDefinition = typeof ComplaintDefinition;
export const ComplaintDefinition = {
  name: 'Complaint',
  fullName: 'comm.api.customer.Complaint',
  methods: {
    /** 获取申诉类型列表 */
    listCategory: {
      name: 'ListCategory',
      requestType: ListCategoryReq,
      requestStream: false,
      responseType: ListCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取FAQ列表 */
    listFAQ: {
      name: 'ListFAQ',
      requestType: ListReq,
      requestStream: false,
      responseType: ListRsp,
      responseStream: false,
      options: {}
    },
    /** 创建申诉工单 */
    create: {
      name: 'Create',
      requestType: CreateReq,
      requestStream: false,
      responseType: CreateRsp,
      responseStream: false,
      options: {}
    },
    /** 获取支付列表 */
    listPayment: {
      name: 'ListPayment',
      requestType: ListPaymentReq,
      requestStream: false,
      responseType: ListPaymentRsp,
      responseStream: false,
      options: {}
    },
    /** 获取申诉记录列表 */
    listRecords: {
      name: 'ListRecords',
      requestType: ListRecordsReq,
      requestStream: false,
      responseType: ListRecordsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取申诉详情 */
    getRecord: {
      name: 'GetRecord',
      requestType: GetRecordReq,
      requestStream: false,
      responseType: GetRecordRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
