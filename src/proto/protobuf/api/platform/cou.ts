// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/platform/cou.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.platform.cou';

export interface GetNewCouReq {
  /** 原国家码,格式类似SA */
  cou: string;
  /** APP Store账号对应的国家码，格式类似SA，""表示空 */
  storefront_cou: string;
  /** 获取苹果充值档位对应国家码，格式类似SA，""表示空 */
  skproduct_cou: string;
  /** 当前系统设置的国家码，格式类似SA */
  sys_cou: string;
  /** 当前系统设置的语言信息，格式类似en_US */
  sys_lan: string;
  /** 当前系统设置的时区信息，格式类似Asia/Riyadh */
  sys_timezone: string;
  /** 是否链接vpn，bool类型，true表示vpn，false表示非vpn */
  vpn: boolean;
}

export interface GetNewCouRsp {
  /** 新的国家代码, 基于策略计算出来. */
  new_cou: string;
}

function createBaseGetNewCouReq(): GetNewCouReq {
  return { cou: '', storefront_cou: '', skproduct_cou: '', sys_cou: '', sys_lan: '', sys_timezone: '', vpn: false };
}

export const GetNewCouReq: MessageFns<GetNewCouReq> = {
  fromJSON(object: any): GetNewCouReq {
    return {
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      storefront_cou: isSet(object.storefront_cou) ? globalThis.String(object.storefront_cou) : '',
      skproduct_cou: isSet(object.skproduct_cou) ? globalThis.String(object.skproduct_cou) : '',
      sys_cou: isSet(object.sys_cou) ? globalThis.String(object.sys_cou) : '',
      sys_lan: isSet(object.sys_lan) ? globalThis.String(object.sys_lan) : '',
      sys_timezone: isSet(object.sys_timezone) ? globalThis.String(object.sys_timezone) : '',
      vpn: isSet(object.vpn) ? globalThis.Boolean(object.vpn) : false
    };
  },

  create<I extends Exact<DeepPartial<GetNewCouReq>, I>>(base?: I): GetNewCouReq {
    return GetNewCouReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNewCouReq>, I>>(object: I): GetNewCouReq {
    const message = createBaseGetNewCouReq();
    message.cou = object.cou ?? '';
    message.storefront_cou = object.storefront_cou ?? '';
    message.skproduct_cou = object.skproduct_cou ?? '';
    message.sys_cou = object.sys_cou ?? '';
    message.sys_lan = object.sys_lan ?? '';
    message.sys_timezone = object.sys_timezone ?? '';
    message.vpn = object.vpn ?? false;
    return message;
  }
};

function createBaseGetNewCouRsp(): GetNewCouRsp {
  return { new_cou: '' };
}

export const GetNewCouRsp: MessageFns<GetNewCouRsp> = {
  fromJSON(object: any): GetNewCouRsp {
    return { new_cou: isSet(object.new_cou) ? globalThis.String(object.new_cou) : '' };
  },

  create<I extends Exact<DeepPartial<GetNewCouRsp>, I>>(base?: I): GetNewCouRsp {
    return GetNewCouRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNewCouRsp>, I>>(object: I): GetNewCouRsp {
    const message = createBaseGetNewCouRsp();
    message.new_cou = object.new_cou ?? '';
    return message;
  }
};

/**
 * 国家代码
 * smicro:spath=gitit.cc/social/components-service/social-platform/biz/cou/handler
 * serviceName: platform-api
 */
export type CouDefinition = typeof CouDefinition;
export const CouDefinition = {
  name: 'Cou',
  fullName: 'comm.api.platform.cou.Cou',
  methods: {
    /** 获取国家代码 */
    getNewCou: {
      name: 'GetNewCou',
      requestType: GetNewCouReq,
      requestStream: false,
      responseType: GetNewCouRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
