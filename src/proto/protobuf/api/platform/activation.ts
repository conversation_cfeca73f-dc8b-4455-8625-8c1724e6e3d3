// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/platform/activation.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.platform.activation';

/** 归因错误码 */
export enum ActivationCode {
  ACTIVATION_CODE_NONE = 0,
  /**
   * ACTIVATION_CODE_NOT_MATCH - 归因号段 70000 - 71000，后端根据现有码来定义不重复的号段，以下是举例
   * 表示没归因信息
   */
  ACTIVATION_CODE_NOT_MATCH = 70000,
  /** ACTIVATION_CODE_PARSE_TOKEN_FAIL - aid 解析失败 */
  ACTIVATION_CODE_PARSE_TOKEN_FAIL = 70001,
  /** ACTIVATION_CODE_ALREADY_USED - atrribution已经使用 */
  ACTIVATION_CODE_ALREADY_USED = 70002,
  UNRECOGNIZED = -1
}

export function activationCodeFromJSON(object: any): ActivationCode {
  switch (object) {
    case 0:
    case 'ACTIVATION_CODE_NONE':
      return ActivationCode.ACTIVATION_CODE_NONE;
    case 70000:
    case 'ACTIVATION_CODE_NOT_MATCH':
      return ActivationCode.ACTIVATION_CODE_NOT_MATCH;
    case 70001:
    case 'ACTIVATION_CODE_PARSE_TOKEN_FAIL':
      return ActivationCode.ACTIVATION_CODE_PARSE_TOKEN_FAIL;
    case 70002:
    case 'ACTIVATION_CODE_ALREADY_USED':
      return ActivationCode.ACTIVATION_CODE_ALREADY_USED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ActivationCode.UNRECOGNIZED;
  }
}

/**
 * H5推广页请求归因登记
 * 注：后端需要获取浏览器的UserAgent信息、请求IP
 */
export interface AttrSigninReq {
  /** 应用ID，保证每个App唯一即可，一般用anm的值 */
  appid: string;
  /** 归因主渠道，比如填活动ID，一般产品提供 */
  cha: string;
  /** 归因子渠道，比如填推广用户ID，http地址获取 */
  sub: string;
  /** 推广页来源，通过JS获取（document.referrer）来源 */
  referrer: string;
  /** 预留扩展信息 */
  ext: { [key: string]: string };
}

export interface AttrSigninReq_ExtEntry {
  key: string;
  value: string;
}

export interface AttrSigninRsp {
  /**
   * 后端用于映射存储的信息的Key（关联存储信息的ID），前端需要保存在剪切板
   * 存储的信息包括：渠道信息(cha、sub、referrer)、终端参数(浏览器UserAgent)、请求IP
   */
  aid: string;
}

/** App打开请求获取归因信息 */
export interface AttrTrackReq {
  /** 应用ID，保证每个App唯一即可，一般用anm的值 */
  appid: string;
  /** 终端从cookie或剪切板获取的信息，用于后端判断归因 */
  aid: string;
  /** 终端请求时，构造web获取浏览器UserAgent信息 */
  user_agent: string;
}

export interface AttrTrackRsp {
  /**
   * 归因信息
   * 目前只返回以下内容，也支持扩展：
   * {
   *    "cha": "xxx", // 主渠道
   *    "sub": "xxx", // 子渠道
   *    "referrer": "xxx", // 来源
   *    ...
   * }
   */
  attribution: { [key: string]: string };
  /**
   * 如果丢失aid，后端使用UserAgent+IP等判断，此场景定义为概率归因
   * 取值 [0-100]，比如60表示60%的归因准确度；
   * 如果有aid并能和后端存储匹配上且不重复则取值应该为100；
   */
  probability: number;
  /**
   * 是否首次安装
   * false:表示有统计过did已经安装过的，不管是否有归因信息；true则否
   */
  is_first_install: boolean;
}

export interface AttrTrackRsp_AttributionEntry {
  key: string;
  value: string;
}

function createBaseAttrSigninReq(): AttrSigninReq {
  return { appid: '', cha: '', sub: '', referrer: '', ext: {} };
}

export const AttrSigninReq: MessageFns<AttrSigninReq> = {
  fromJSON(object: any): AttrSigninReq {
    return {
      appid: isSet(object.appid) ? globalThis.String(object.appid) : '',
      cha: isSet(object.cha) ? globalThis.String(object.cha) : '',
      sub: isSet(object.sub) ? globalThis.String(object.sub) : '',
      referrer: isSet(object.referrer) ? globalThis.String(object.referrer) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<AttrSigninReq>, I>>(base?: I): AttrSigninReq {
    return AttrSigninReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrSigninReq>, I>>(object: I): AttrSigninReq {
    const message = createBaseAttrSigninReq();
    message.appid = object.appid ?? '';
    message.cha = object.cha ?? '';
    message.sub = object.sub ?? '';
    message.referrer = object.referrer ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseAttrSigninReq_ExtEntry(): AttrSigninReq_ExtEntry {
  return { key: '', value: '' };
}

export const AttrSigninReq_ExtEntry: MessageFns<AttrSigninReq_ExtEntry> = {
  fromJSON(object: any): AttrSigninReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AttrSigninReq_ExtEntry>, I>>(base?: I): AttrSigninReq_ExtEntry {
    return AttrSigninReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrSigninReq_ExtEntry>, I>>(object: I): AttrSigninReq_ExtEntry {
    const message = createBaseAttrSigninReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAttrSigninRsp(): AttrSigninRsp {
  return { aid: '' };
}

export const AttrSigninRsp: MessageFns<AttrSigninRsp> = {
  fromJSON(object: any): AttrSigninRsp {
    return { aid: isSet(object.aid) ? globalThis.String(object.aid) : '' };
  },

  create<I extends Exact<DeepPartial<AttrSigninRsp>, I>>(base?: I): AttrSigninRsp {
    return AttrSigninRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrSigninRsp>, I>>(object: I): AttrSigninRsp {
    const message = createBaseAttrSigninRsp();
    message.aid = object.aid ?? '';
    return message;
  }
};

function createBaseAttrTrackReq(): AttrTrackReq {
  return { appid: '', aid: '', user_agent: '' };
}

export const AttrTrackReq: MessageFns<AttrTrackReq> = {
  fromJSON(object: any): AttrTrackReq {
    return {
      appid: isSet(object.appid) ? globalThis.String(object.appid) : '',
      aid: isSet(object.aid) ? globalThis.String(object.aid) : '',
      user_agent: isSet(object.user_agent) ? globalThis.String(object.user_agent) : ''
    };
  },

  create<I extends Exact<DeepPartial<AttrTrackReq>, I>>(base?: I): AttrTrackReq {
    return AttrTrackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrTrackReq>, I>>(object: I): AttrTrackReq {
    const message = createBaseAttrTrackReq();
    message.appid = object.appid ?? '';
    message.aid = object.aid ?? '';
    message.user_agent = object.user_agent ?? '';
    return message;
  }
};

function createBaseAttrTrackRsp(): AttrTrackRsp {
  return { attribution: {}, probability: 0, is_first_install: false };
}

export const AttrTrackRsp: MessageFns<AttrTrackRsp> = {
  fromJSON(object: any): AttrTrackRsp {
    return {
      attribution: isObject(object.attribution)
        ? Object.entries(object.attribution).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      probability: isSet(object.probability) ? globalThis.Number(object.probability) : 0,
      is_first_install: isSet(object.is_first_install) ? globalThis.Boolean(object.is_first_install) : false
    };
  },

  create<I extends Exact<DeepPartial<AttrTrackRsp>, I>>(base?: I): AttrTrackRsp {
    return AttrTrackRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrTrackRsp>, I>>(object: I): AttrTrackRsp {
    const message = createBaseAttrTrackRsp();
    message.attribution = Object.entries(object.attribution ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.probability = object.probability ?? 0;
    message.is_first_install = object.is_first_install ?? false;
    return message;
  }
};

function createBaseAttrTrackRsp_AttributionEntry(): AttrTrackRsp_AttributionEntry {
  return { key: '', value: '' };
}

export const AttrTrackRsp_AttributionEntry: MessageFns<AttrTrackRsp_AttributionEntry> = {
  fromJSON(object: any): AttrTrackRsp_AttributionEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AttrTrackRsp_AttributionEntry>, I>>(base?: I): AttrTrackRsp_AttributionEntry {
    return AttrTrackRsp_AttributionEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AttrTrackRsp_AttributionEntry>, I>>(
    object: I
  ): AttrTrackRsp_AttributionEntry {
    const message = createBaseAttrTrackRsp_AttributionEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

/**
 * 归因协议
 * serviceName: platform-api
 */
export type ActivationDefinition = typeof ActivationDefinition;
export const ActivationDefinition = {
  name: 'Activation',
  fullName: 'comm.api.platform.activation.Activation',
  methods: {
    /** 设备归因登记，即H5推广页请求后端，记录设备信息，用于App安装后首次打开归因判断 */
    attrSignin: {
      name: 'AttrSignin',
      requestType: AttrSigninReq,
      requestStream: false,
      responseType: AttrSigninRsp,
      responseStream: false,
      options: {}
    },
    /** 获取设备归因信息，即App安装首次打开后，请求后端获取归因信息 */
    attrTrack: {
      name: 'AttrTrack',
      requestType: AttrTrackReq,
      requestStream: false,
      responseType: AttrTrackRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
