// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/platform/feature.proto

/* eslint-disable */
import { Gender, genderFromJSON, OSType, oSTypeFromJSON } from '../common/common-net';

export const protobufPackage = 'comm.api.platform.activation';

/** FeatureValues 这个信息有点多余，当时为了写测试加的，用这个信息直接替换公参信息。仅测试可用 */
export interface FeatureValues {
  /** c端 */
  ver: string;
  /** 递增的编译版本号 */
  verc: number;
  /** 设备ID */
  did: string;
  /** 系统类型 */
  os_type: OSType;
  /** 系统版本 */
  os_ver: string;
  /** 系统API版本 */
  os_api_ver: string;
  /** 语言，优先APP设置的语言，如果APP没有语言设置，用系统语言 */
  lan: string;
  /** 包名 */
  pkg: string;
  /** 当前的国家码，优先取sim卡的，没有则取系统的 */
  cou: string;
  /** 性别信息 */
  gender: Gender;
  /** ip */
  ip: string;
  /** uid */
  uid: number;
}

export interface ClientInfo {
  /** 手机型号，比如iPhone 14 */
  mod: string;
  /** 系统语言，比如 en_US */
  slan: string;
  /** 归因渠道号 */
  cha: string;
  /** 归因子渠道号 */
  sub: string;
  /** 时区，比如 Asia/Shanghai */
  time_zone: string;
  /** 移动网络代码(mcc+mnc)，比如46001 */
  isp: number;
  /** 网络类型，比如4G/5G/WIFI/nonet */
  net: string;
  /** 安装来源,最大64 */
  install_source: string;
  /** 是否开启vpn */
  is_vpn: boolean;
  /** 是否越狱 */
  is_jailbroken: boolean;
  /** 电池状态，取值0:unknown; 1:unplugged; 2:charging; 3:full */
  battery_state: number;
  /** 电量，取值(0, 100] */
  battery_level: number;
  /** 磁盘总空间(单位字节)，比如 ************ */
  disk_space_total: number;
  /** 磁盘剩余空间(单位字节)，比如 3712196608 */
  disk_space_free: number;
  /** 内存总大小(单位字节)，比如 5910118400 */
  memory_total: number;
  /** 内存剩余大小(单位字节)，比如 130252800 */
  memory_free: number;
  /** 系统启动时间(单位秒)，比如 2510019 */
  system_uptime: number;
  /** 系统用户界面风格，取值 0:unspecified; 1:light; 2:dark */
  ui_style: number;
}

/**
 * 3类特征都来自于cproxy：ip特征，c端特征，uid
 * ClientInfo：来自于客户端
 */
export interface SubmitFeaturesReq {
  /** 仅用于测试 */
  featrues: FeatureValues | undefined;
  /** 客户端补充属性 */
  client_info: ClientInfo | undefined;
}

export interface SubmitFeaturesRsp {
  id: number;
  /** 是否具备审核特征 */
  audit_toggle: boolean;
  /** 特征集名 */
  name: string;
  /** 特征标签列表 */
  labels: string[];
  /** 特征map */
  attrs: { [key: string]: string };
  /** 是否第一次发布 */
  is_first_pub: boolean;
  /** 表示访问是否来自公司内网 */
  is_intranet: boolean;
}

export interface SubmitFeaturesRsp_AttrsEntry {
  key: string;
  value: string;
}

function createBaseFeatureValues(): FeatureValues {
  return {
    ver: '',
    verc: 0,
    did: '',
    os_type: 0,
    os_ver: '',
    os_api_ver: '',
    lan: '',
    pkg: '',
    cou: '',
    gender: 0,
    ip: '',
    uid: 0
  };
}

export const FeatureValues: MessageFns<FeatureValues> = {
  fromJSON(object: any): FeatureValues {
    return {
      ver: isSet(object.ver) ? globalThis.String(object.ver) : '',
      verc: isSet(object.verc) ? globalThis.Number(object.verc) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      os_ver: isSet(object.os_ver) ? globalThis.String(object.os_ver) : '',
      os_api_ver: isSet(object.os_api_ver) ? globalThis.String(object.os_api_ver) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      ip: isSet(object.ip) ? globalThis.String(object.ip) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<FeatureValues>, I>>(base?: I): FeatureValues {
    return FeatureValues.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureValues>, I>>(object: I): FeatureValues {
    const message = createBaseFeatureValues();
    message.ver = object.ver ?? '';
    message.verc = object.verc ?? 0;
    message.did = object.did ?? '';
    message.os_type = object.os_type ?? 0;
    message.os_ver = object.os_ver ?? '';
    message.os_api_ver = object.os_api_ver ?? '';
    message.lan = object.lan ?? '';
    message.pkg = object.pkg ?? '';
    message.cou = object.cou ?? '';
    message.gender = object.gender ?? 0;
    message.ip = object.ip ?? '';
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseClientInfo(): ClientInfo {
  return {
    mod: '',
    slan: '',
    cha: '',
    sub: '',
    time_zone: '',
    isp: 0,
    net: '',
    install_source: '',
    is_vpn: false,
    is_jailbroken: false,
    battery_state: 0,
    battery_level: 0,
    disk_space_total: 0,
    disk_space_free: 0,
    memory_total: 0,
    memory_free: 0,
    system_uptime: 0,
    ui_style: 0
  };
}

export const ClientInfo: MessageFns<ClientInfo> = {
  fromJSON(object: any): ClientInfo {
    return {
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      slan: isSet(object.slan) ? globalThis.String(object.slan) : '',
      cha: isSet(object.cha) ? globalThis.String(object.cha) : '',
      sub: isSet(object.sub) ? globalThis.String(object.sub) : '',
      time_zone: isSet(object.time_zone) ? globalThis.String(object.time_zone) : '',
      isp: isSet(object.isp) ? globalThis.Number(object.isp) : 0,
      net: isSet(object.net) ? globalThis.String(object.net) : '',
      install_source: isSet(object.install_source) ? globalThis.String(object.install_source) : '',
      is_vpn: isSet(object.is_vpn) ? globalThis.Boolean(object.is_vpn) : false,
      is_jailbroken: isSet(object.is_jailbroken) ? globalThis.Boolean(object.is_jailbroken) : false,
      battery_state: isSet(object.battery_state) ? globalThis.Number(object.battery_state) : 0,
      battery_level: isSet(object.battery_level) ? globalThis.Number(object.battery_level) : 0,
      disk_space_total: isSet(object.disk_space_total) ? globalThis.Number(object.disk_space_total) : 0,
      disk_space_free: isSet(object.disk_space_free) ? globalThis.Number(object.disk_space_free) : 0,
      memory_total: isSet(object.memory_total) ? globalThis.Number(object.memory_total) : 0,
      memory_free: isSet(object.memory_free) ? globalThis.Number(object.memory_free) : 0,
      system_uptime: isSet(object.system_uptime) ? globalThis.Number(object.system_uptime) : 0,
      ui_style: isSet(object.ui_style) ? globalThis.Number(object.ui_style) : 0
    };
  },

  create<I extends Exact<DeepPartial<ClientInfo>, I>>(base?: I): ClientInfo {
    return ClientInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientInfo>, I>>(object: I): ClientInfo {
    const message = createBaseClientInfo();
    message.mod = object.mod ?? '';
    message.slan = object.slan ?? '';
    message.cha = object.cha ?? '';
    message.sub = object.sub ?? '';
    message.time_zone = object.time_zone ?? '';
    message.isp = object.isp ?? 0;
    message.net = object.net ?? '';
    message.install_source = object.install_source ?? '';
    message.is_vpn = object.is_vpn ?? false;
    message.is_jailbroken = object.is_jailbroken ?? false;
    message.battery_state = object.battery_state ?? 0;
    message.battery_level = object.battery_level ?? 0;
    message.disk_space_total = object.disk_space_total ?? 0;
    message.disk_space_free = object.disk_space_free ?? 0;
    message.memory_total = object.memory_total ?? 0;
    message.memory_free = object.memory_free ?? 0;
    message.system_uptime = object.system_uptime ?? 0;
    message.ui_style = object.ui_style ?? 0;
    return message;
  }
};

function createBaseSubmitFeaturesReq(): SubmitFeaturesReq {
  return { featrues: undefined, client_info: undefined };
}

export const SubmitFeaturesReq: MessageFns<SubmitFeaturesReq> = {
  fromJSON(object: any): SubmitFeaturesReq {
    return {
      featrues: isSet(object.featrues) ? FeatureValues.fromJSON(object.featrues) : undefined,
      client_info: isSet(object.client_info) ? ClientInfo.fromJSON(object.client_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SubmitFeaturesReq>, I>>(base?: I): SubmitFeaturesReq {
    return SubmitFeaturesReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitFeaturesReq>, I>>(object: I): SubmitFeaturesReq {
    const message = createBaseSubmitFeaturesReq();
    message.featrues =
      object.featrues !== undefined && object.featrues !== null
        ? FeatureValues.fromPartial(object.featrues)
        : undefined;
    message.client_info =
      object.client_info !== undefined && object.client_info !== null
        ? ClientInfo.fromPartial(object.client_info)
        : undefined;
    return message;
  }
};

function createBaseSubmitFeaturesRsp(): SubmitFeaturesRsp {
  return { id: 0, audit_toggle: false, name: '', labels: [], attrs: {}, is_first_pub: false, is_intranet: false };
}

export const SubmitFeaturesRsp: MessageFns<SubmitFeaturesRsp> = {
  fromJSON(object: any): SubmitFeaturesRsp {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      audit_toggle: isSet(object.audit_toggle) ? globalThis.Boolean(object.audit_toggle) : false,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      labels: globalThis.Array.isArray(object?.labels) ? object.labels.map((e: any) => globalThis.String(e)) : [],
      attrs: isObject(object.attrs)
        ? Object.entries(object.attrs).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      is_first_pub: isSet(object.is_first_pub) ? globalThis.Boolean(object.is_first_pub) : false,
      is_intranet: isSet(object.is_intranet) ? globalThis.Boolean(object.is_intranet) : false
    };
  },

  create<I extends Exact<DeepPartial<SubmitFeaturesRsp>, I>>(base?: I): SubmitFeaturesRsp {
    return SubmitFeaturesRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitFeaturesRsp>, I>>(object: I): SubmitFeaturesRsp {
    const message = createBaseSubmitFeaturesRsp();
    message.id = object.id ?? 0;
    message.audit_toggle = object.audit_toggle ?? false;
    message.name = object.name ?? '';
    message.labels = object.labels?.map(e => e) || [];
    message.attrs = Object.entries(object.attrs ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.is_first_pub = object.is_first_pub ?? false;
    message.is_intranet = object.is_intranet ?? false;
    return message;
  }
};

function createBaseSubmitFeaturesRsp_AttrsEntry(): SubmitFeaturesRsp_AttrsEntry {
  return { key: '', value: '' };
}

export const SubmitFeaturesRsp_AttrsEntry: MessageFns<SubmitFeaturesRsp_AttrsEntry> = {
  fromJSON(object: any): SubmitFeaturesRsp_AttrsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SubmitFeaturesRsp_AttrsEntry>, I>>(base?: I): SubmitFeaturesRsp_AttrsEntry {
    return SubmitFeaturesRsp_AttrsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitFeaturesRsp_AttrsEntry>, I>>(object: I): SubmitFeaturesRsp_AttrsEntry {
    const message = createBaseSubmitFeaturesRsp_AttrsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

/**
 * app特征
 * serviceName: platform-api
 */
export type FeaturesDefinition = typeof FeaturesDefinition;
export const FeaturesDefinition = {
  name: 'Features',
  fullName: 'comm.api.platform.activation.Features',
  methods: {
    /** 提交特征 */
    submitFeatures: {
      name: 'SubmitFeatures',
      requestType: SubmitFeaturesReq,
      requestStream: false,
      responseType: SubmitFeaturesRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
