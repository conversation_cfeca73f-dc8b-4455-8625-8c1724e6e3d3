// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/platform/langs.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.platform.langs';

/** 哪个端的 */
export enum Site {
  SITE_NONE = 0,
  SITE_SERVER = 1,
  SITE_APP = 2,
  SITE_WEB = 3,
  UNRECOGNIZED = -1
}

export function siteFromJSON(object: any): Site {
  switch (object) {
    case 0:
    case 'SITE_NONE':
      return Site.SITE_NONE;
    case 1:
    case 'SITE_SERVER':
      return Site.SITE_SERVER;
    case 2:
    case 'SITE_APP':
      return Site.SITE_APP;
    case 3:
    case 'SITE_WEB':
      return Site.SITE_WEB;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Site.UNRECOGNIZED;
  }
}

export enum DataType {
  DATA_TYPE_NONE = 0,
  /** DATA_TYPE_CONTENT - 多语言的内容，map<string, string>格式，客户端自行unmarshal */
  DATA_TYPE_CONTENT = 1,
  /** DATA_TYPE_PATCH - json pathch 文件 */
  DATA_TYPE_PATCH = 2,
  UNRECOGNIZED = -1
}

export function dataTypeFromJSON(object: any): DataType {
  switch (object) {
    case 0:
    case 'DATA_TYPE_NONE':
      return DataType.DATA_TYPE_NONE;
    case 1:
    case 'DATA_TYPE_CONTENT':
      return DataType.DATA_TYPE_CONTENT;
    case 2:
    case 'DATA_TYPE_PATCH':
      return DataType.DATA_TYPE_PATCH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return DataType.UNRECOGNIZED;
  }
}

export interface GetReq {
  site: Site;
  /** 根据当前用户的语言拉取对应的多语言，zh/en等，如果不存在，则返回en */
  lang: string;
  /** key是多语言的code */
  versions: { [key: string]: number };
}

export interface GetReq_VersionsEntry {
  key: string;
  value: number;
}

export interface GetRsp {
  /** code->Lang */
  langs: { [key: string]: Lang };
}

export interface GetRsp_LangsEntry {
  key: string;
  value: Lang | undefined;
}

export interface Lang {
  version: number;
  /** lang code，比如en、zh 等 */
  lang: string;
  type: DataType;
  /** 内容取决于type */
  data: string;
}

function createBaseGetReq(): GetReq {
  return { site: 0, lang: '', versions: {} };
}

export const GetReq: MessageFns<GetReq> = {
  fromJSON(object: any): GetReq {
    return {
      site: isSet(object.site) ? siteFromJSON(object.site) : 0,
      lang: isSet(object.lang) ? globalThis.String(object.lang) : '',
      versions: isObject(object.versions)
        ? Object.entries(object.versions).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetReq>, I>>(base?: I): GetReq {
    return GetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetReq>, I>>(object: I): GetReq {
    const message = createBaseGetReq();
    message.site = object.site ?? 0;
    message.lang = object.lang ?? '';
    message.versions = Object.entries(object.versions ?? {}).reduce<{ [key: string]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGetReq_VersionsEntry(): GetReq_VersionsEntry {
  return { key: '', value: 0 };
}

export const GetReq_VersionsEntry: MessageFns<GetReq_VersionsEntry> = {
  fromJSON(object: any): GetReq_VersionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetReq_VersionsEntry>, I>>(base?: I): GetReq_VersionsEntry {
    return GetReq_VersionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetReq_VersionsEntry>, I>>(object: I): GetReq_VersionsEntry {
    const message = createBaseGetReq_VersionsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseGetRsp(): GetRsp {
  return { langs: {} };
}

export const GetRsp: MessageFns<GetRsp> = {
  fromJSON(object: any): GetRsp {
    return {
      langs: isObject(object.langs)
        ? Object.entries(object.langs).reduce<{ [key: string]: Lang }>((acc, [key, value]) => {
            acc[key] = Lang.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetRsp>, I>>(base?: I): GetRsp {
    return GetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRsp>, I>>(object: I): GetRsp {
    const message = createBaseGetRsp();
    message.langs = Object.entries(object.langs ?? {}).reduce<{ [key: string]: Lang }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Lang.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGetRsp_LangsEntry(): GetRsp_LangsEntry {
  return { key: '', value: undefined };
}

export const GetRsp_LangsEntry: MessageFns<GetRsp_LangsEntry> = {
  fromJSON(object: any): GetRsp_LangsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Lang.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetRsp_LangsEntry>, I>>(base?: I): GetRsp_LangsEntry {
    return GetRsp_LangsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRsp_LangsEntry>, I>>(object: I): GetRsp_LangsEntry {
    const message = createBaseGetRsp_LangsEntry();
    message.key = object.key ?? '';
    message.value = object.value !== undefined && object.value !== null ? Lang.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseLang(): Lang {
  return { version: 0, lang: '', type: 0, data: '' };
}

export const Lang: MessageFns<Lang> = {
  fromJSON(object: any): Lang {
    return {
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      lang: isSet(object.lang) ? globalThis.String(object.lang) : '',
      type: isSet(object.type) ? dataTypeFromJSON(object.type) : 0,
      data: isSet(object.data) ? globalThis.String(object.data) : ''
    };
  },

  create<I extends Exact<DeepPartial<Lang>, I>>(base?: I): Lang {
    return Lang.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Lang>, I>>(object: I): Lang {
    const message = createBaseLang();
    message.version = object.version ?? 0;
    message.lang = object.lang ?? '';
    message.type = object.type ?? 0;
    message.data = object.data ?? '';
    return message;
  }
};

export type LangsApiDefinition = typeof LangsApiDefinition;
export const LangsApiDefinition = {
  name: 'LangsApi',
  fullName: 'comm.api.platform.langs.LangsApi',
  methods: {
    /** 拉取多语言信息 */
    get: {
      name: 'Get',
      requestType: GetReq,
      requestStream: false,
      responseType: GetRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
