// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/sconfig/model.proto

/* eslint-disable */

export const protobufPackage = 'sconfig.model';

/** 用来接收时间，前端提交上来的时间格式是 {"ts":1234567,"zone":"Asia/Shanghai"} */
export interface Time {
  ts: number;
  zone: string;
}

/** 文件上传 */
export interface File {
  url: string;
}

/** 特权/礼物选择 */
export interface Privilege {
  /** 特权类型，各个业务定 */
  type: number;
  /** 特权ID，各个业务定 */
  id: number;
  /** 是否是时长类型的特权 */
  use_time: boolean;
}

/** 奖励选择，相对于特权选择来说就是多了个数量 */
export interface Prize {
  privilege: Privilege | undefined;
  /**
   * 奖励的数量，如果是数量类型就是个数，如果是时长类型，就是秒。
   * 会有一些场景，时长类型的奖励，还需要个数，比如给公会长发10个有效期为30天的
   * 头像框。这个时候可以自己定义一个message，里面有一个Prize加一个count
   */
  number: number;
}

/** 用来接收配置的版本信息 */
export interface ConfigVersion {
  ver: number;
}

/** 员工信息 */
export interface Staff {
  union_id: string;
  name: string;
  nick_name: string;
  email: string;
  user_id: string;
}

/** 颜色选择 */
export interface Color {
  color: string;
}

/** 多语言 */
export interface Langs {
  langs: { [key: string]: string };
}

export interface Langs_LangsEntry {
  key: string;
  value: string;
}

function createBaseTime(): Time {
  return { ts: 0, zone: '' };
}

export const Time: MessageFns<Time> = {
  fromJSON(object: any): Time {
    return {
      ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0,
      zone: isSet(object.zone) ? globalThis.String(object.zone) : ''
    };
  },

  create<I extends Exact<DeepPartial<Time>, I>>(base?: I): Time {
    return Time.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Time>, I>>(object: I): Time {
    const message = createBaseTime();
    message.ts = object.ts ?? 0;
    message.zone = object.zone ?? '';
    return message;
  }
};

function createBaseFile(): File {
  return { url: '' };
}

export const File: MessageFns<File> = {
  fromJSON(object: any): File {
    return { url: isSet(object.url) ? globalThis.String(object.url) : '' };
  },

  create<I extends Exact<DeepPartial<File>, I>>(base?: I): File {
    return File.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<File>, I>>(object: I): File {
    const message = createBaseFile();
    message.url = object.url ?? '';
    return message;
  }
};

function createBasePrivilege(): Privilege {
  return { type: 0, id: 0, use_time: false };
}

export const Privilege: MessageFns<Privilege> = {
  fromJSON(object: any): Privilege {
    return {
      type: isSet(object.type) ? globalThis.Number(object.type) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      use_time: isSet(object.use_time) ? globalThis.Boolean(object.use_time) : false
    };
  },

  create<I extends Exact<DeepPartial<Privilege>, I>>(base?: I): Privilege {
    return Privilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Privilege>, I>>(object: I): Privilege {
    const message = createBasePrivilege();
    message.type = object.type ?? 0;
    message.id = object.id ?? 0;
    message.use_time = object.use_time ?? false;
    return message;
  }
};

function createBasePrize(): Prize {
  return { privilege: undefined, number: 0 };
}

export const Prize: MessageFns<Prize> = {
  fromJSON(object: any): Prize {
    return {
      privilege: isSet(object.privilege) ? Privilege.fromJSON(object.privilege) : undefined,
      number: isSet(object.number) ? globalThis.Number(object.number) : 0
    };
  },

  create<I extends Exact<DeepPartial<Prize>, I>>(base?: I): Prize {
    return Prize.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Prize>, I>>(object: I): Prize {
    const message = createBasePrize();
    message.privilege =
      object.privilege !== undefined && object.privilege !== null ? Privilege.fromPartial(object.privilege) : undefined;
    message.number = object.number ?? 0;
    return message;
  }
};

function createBaseConfigVersion(): ConfigVersion {
  return { ver: 0 };
}

export const ConfigVersion: MessageFns<ConfigVersion> = {
  fromJSON(object: any): ConfigVersion {
    return { ver: isSet(object.ver) ? globalThis.Number(object.ver) : 0 };
  },

  create<I extends Exact<DeepPartial<ConfigVersion>, I>>(base?: I): ConfigVersion {
    return ConfigVersion.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigVersion>, I>>(object: I): ConfigVersion {
    const message = createBaseConfigVersion();
    message.ver = object.ver ?? 0;
    return message;
  }
};

function createBaseStaff(): Staff {
  return { union_id: '', name: '', nick_name: '', email: '', user_id: '' };
}

export const Staff: MessageFns<Staff> = {
  fromJSON(object: any): Staff {
    return {
      union_id: isSet(object.union_id) ? globalThis.String(object.union_id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      nick_name: isSet(object.nick_name) ? globalThis.String(object.nick_name) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      user_id: isSet(object.user_id) ? globalThis.String(object.user_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<Staff>, I>>(base?: I): Staff {
    return Staff.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Staff>, I>>(object: I): Staff {
    const message = createBaseStaff();
    message.union_id = object.union_id ?? '';
    message.name = object.name ?? '';
    message.nick_name = object.nick_name ?? '';
    message.email = object.email ?? '';
    message.user_id = object.user_id ?? '';
    return message;
  }
};

function createBaseColor(): Color {
  return { color: '' };
}

export const Color: MessageFns<Color> = {
  fromJSON(object: any): Color {
    return { color: isSet(object.color) ? globalThis.String(object.color) : '' };
  },

  create<I extends Exact<DeepPartial<Color>, I>>(base?: I): Color {
    return Color.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Color>, I>>(object: I): Color {
    const message = createBaseColor();
    message.color = object.color ?? '';
    return message;
  }
};

function createBaseLangs(): Langs {
  return { langs: {} };
}

export const Langs: MessageFns<Langs> = {
  fromJSON(object: any): Langs {
    return {
      langs: isObject(object.langs)
        ? Object.entries(object.langs).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<Langs>, I>>(base?: I): Langs {
    return Langs.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Langs>, I>>(object: I): Langs {
    const message = createBaseLangs();
    message.langs = Object.entries(object.langs ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseLangs_LangsEntry(): Langs_LangsEntry {
  return { key: '', value: '' };
}

export const Langs_LangsEntry: MessageFns<Langs_LangsEntry> = {
  fromJSON(object: any): Langs_LangsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Langs_LangsEntry>, I>>(base?: I): Langs_LangsEntry {
    return Langs_LangsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Langs_LangsEntry>, I>>(object: I): Langs_LangsEntry {
    const message = createBaseLangs_LangsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
