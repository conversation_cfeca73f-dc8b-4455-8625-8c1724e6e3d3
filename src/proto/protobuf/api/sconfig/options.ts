// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/sconfig/options.proto

/* eslint-disable */

export const protobufPackage = 'sconfig.schema';

/** 枚举值扩展信息 */
export interface EnumValue {
  /** 名字，只有定义了名字的才会显示到下拉框里面 */
  name: string;
}

/** 字段的扩展信息 */
export interface InputItem {
  /** 输入框的前导，只有定义了这个才会生成 */
  name: string;
  /** 帮助信息 */
  help: string;
  /** 默认值 */
  def: string;
  /** 是否可选 */
  opt: boolean;
  /** 是否是多行输入，string类型才支持 */
  ml: boolean;
  /** 是否是表单的主键，只有table类型（repeated message)才有意义 */
  kid: boolean;
  min: string;
  max: string;
  /** 是否用表单的形式展示，只有repeated object的才支持 */
  table: boolean;
}

/** 模块信息 */
export interface ModelInfo {
  /** 模块名称 */
  name: string;
  /** 是否用表单的形式展示，只有repeated object的才支持 */
  table: boolean;
  /** 配置项的帮助信息 */
  help: string;
}

function createBaseEnumValue(): EnumValue {
  return { name: '' };
}

export const EnumValue: MessageFns<EnumValue> = {
  fromJSON(object: any): EnumValue {
    return { name: isSet(object.name) ? globalThis.String(object.name) : '' };
  },

  create<I extends Exact<DeepPartial<EnumValue>, I>>(base?: I): EnumValue {
    return EnumValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnumValue>, I>>(object: I): EnumValue {
    const message = createBaseEnumValue();
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseInputItem(): InputItem {
  return { name: '', help: '', def: '', opt: false, ml: false, kid: false, min: '', max: '', table: false };
}

export const InputItem: MessageFns<InputItem> = {
  fromJSON(object: any): InputItem {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      help: isSet(object.help) ? globalThis.String(object.help) : '',
      def: isSet(object.def) ? globalThis.String(object.def) : '',
      opt: isSet(object.opt) ? globalThis.Boolean(object.opt) : false,
      ml: isSet(object.ml) ? globalThis.Boolean(object.ml) : false,
      kid: isSet(object.kid) ? globalThis.Boolean(object.kid) : false,
      min: isSet(object.min) ? globalThis.String(object.min) : '',
      max: isSet(object.max) ? globalThis.String(object.max) : '',
      table: isSet(object.table) ? globalThis.Boolean(object.table) : false
    };
  },

  create<I extends Exact<DeepPartial<InputItem>, I>>(base?: I): InputItem {
    return InputItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<InputItem>, I>>(object: I): InputItem {
    const message = createBaseInputItem();
    message.name = object.name ?? '';
    message.help = object.help ?? '';
    message.def = object.def ?? '';
    message.opt = object.opt ?? false;
    message.ml = object.ml ?? false;
    message.kid = object.kid ?? false;
    message.min = object.min ?? '';
    message.max = object.max ?? '';
    message.table = object.table ?? false;
    return message;
  }
};

function createBaseModelInfo(): ModelInfo {
  return { name: '', table: false, help: '' };
}

export const ModelInfo: MessageFns<ModelInfo> = {
  fromJSON(object: any): ModelInfo {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      table: isSet(object.table) ? globalThis.Boolean(object.table) : false,
      help: isSet(object.help) ? globalThis.String(object.help) : ''
    };
  },

  create<I extends Exact<DeepPartial<ModelInfo>, I>>(base?: I): ModelInfo {
    return ModelInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ModelInfo>, I>>(object: I): ModelInfo {
    const message = createBaseModelInfo();
    message.name = object.name ?? '';
    message.table = object.table ?? false;
    message.help = object.help ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
