// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/adapter/model.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.adapter';

/** 家族角色类型 */
export enum FamilyRoleType {
  /** FAMILY_ROLE_TYPE_NONE - 非家族成员 */
  FAMILY_ROLE_TYPE_NONE = 0,
  /** FAMILY_ROLE_TYPE_PATRIARCH - 家族长 */
  FAMILY_ROLE_TYPE_PATRIARCH = 1,
  /** FAMILY_ROLE_TYPE_VICE_PATRIARCH - 副家族长 */
  FAMILY_ROLE_TYPE_VICE_PATRIARCH = 2,
  /** FAMILY_ROLE_TYPE_ELDER - 长老, 对应echo 的大佬 */
  FAMILY_ROLE_TYPE_ELDER = 3,
  /** FAMILY_ROLE_TYPE_ADMIN - 管理员 */
  FAMILY_ROLE_TYPE_ADMIN = 4,
  /** FAMILY_ROLE_TYPE_MASS - 普通成员 */
  FAMILY_ROLE_TYPE_MASS = 99,
  UNRECOGNIZED = -1
}

export function familyRoleTypeFromJSON(object: any): FamilyRoleType {
  switch (object) {
    case 0:
    case 'FAMILY_ROLE_TYPE_NONE':
      return FamilyRoleType.FAMILY_ROLE_TYPE_NONE;
    case 1:
    case 'FAMILY_ROLE_TYPE_PATRIARCH':
      return FamilyRoleType.FAMILY_ROLE_TYPE_PATRIARCH;
    case 2:
    case 'FAMILY_ROLE_TYPE_VICE_PATRIARCH':
      return FamilyRoleType.FAMILY_ROLE_TYPE_VICE_PATRIARCH;
    case 3:
    case 'FAMILY_ROLE_TYPE_ELDER':
      return FamilyRoleType.FAMILY_ROLE_TYPE_ELDER;
    case 4:
    case 'FAMILY_ROLE_TYPE_ADMIN':
      return FamilyRoleType.FAMILY_ROLE_TYPE_ADMIN;
    case 99:
    case 'FAMILY_ROLE_TYPE_MASS':
      return FamilyRoleType.FAMILY_ROLE_TYPE_MASS;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return FamilyRoleType.UNRECOGNIZED;
  }
}

export enum RoomStatus {
  ROOM_STATUS_NONE = 0,
  /** ROOM_STATUS_LIVING - 直播中 */
  ROOM_STATUS_LIVING = 1,
  /** ROOM_STATUS_CLOSE - 下播中 */
  ROOM_STATUS_CLOSE = 2,
  /** ROOM_STATUS_BAN - 封禁 */
  ROOM_STATUS_BAN = 3,
  UNRECOGNIZED = -1
}

export function roomStatusFromJSON(object: any): RoomStatus {
  switch (object) {
    case 0:
    case 'ROOM_STATUS_NONE':
      return RoomStatus.ROOM_STATUS_NONE;
    case 1:
    case 'ROOM_STATUS_LIVING':
      return RoomStatus.ROOM_STATUS_LIVING;
    case 2:
    case 'ROOM_STATUS_CLOSE':
      return RoomStatus.ROOM_STATUS_CLOSE;
    case 3:
    case 'ROOM_STATUS_BAN':
      return RoomStatus.ROOM_STATUS_BAN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomStatus.UNRECOGNIZED;
  }
}

export enum FollowErrCode {
  FOLLOW_ERR_CODE_NONE = 0,
  /** FOLLOW_ERR_CODE_BLOCKED - 被对方拉黑 */
  FOLLOW_ERR_CODE_BLOCKED = 1,
  /** FOLLOW_ERR_CODE_REACH_LIMIT - 到达关注人数上限 */
  FOLLOW_ERR_CODE_REACH_LIMIT = 2,
  /** FOLLOW_ERR_CODE_RISK - 被风控了 */
  FOLLOW_ERR_CODE_RISK = 3,
  /** FOLLOW_ERR_CODE_BLOCK_BY_SELF - 主动拉黑 */
  FOLLOW_ERR_CODE_BLOCK_BY_SELF = 4,
  /** FOLLOW_ERR_CODE_OTHER - 其他错误 */
  FOLLOW_ERR_CODE_OTHER = 100,
  UNRECOGNIZED = -1
}

export function followErrCodeFromJSON(object: any): FollowErrCode {
  switch (object) {
    case 0:
    case 'FOLLOW_ERR_CODE_NONE':
      return FollowErrCode.FOLLOW_ERR_CODE_NONE;
    case 1:
    case 'FOLLOW_ERR_CODE_BLOCKED':
      return FollowErrCode.FOLLOW_ERR_CODE_BLOCKED;
    case 2:
    case 'FOLLOW_ERR_CODE_REACH_LIMIT':
      return FollowErrCode.FOLLOW_ERR_CODE_REACH_LIMIT;
    case 3:
    case 'FOLLOW_ERR_CODE_RISK':
      return FollowErrCode.FOLLOW_ERR_CODE_RISK;
    case 4:
    case 'FOLLOW_ERR_CODE_BLOCK_BY_SELF':
      return FollowErrCode.FOLLOW_ERR_CODE_BLOCK_BY_SELF;
    case 100:
    case 'FOLLOW_ERR_CODE_OTHER':
      return FollowErrCode.FOLLOW_ERR_CODE_OTHER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return FollowErrCode.UNRECOGNIZED;
  }
}

export enum RewardSourceType {
  REWARD_SOURCE_TYPE_NONE = 0,
  /** REWARD_SOURCE_TYPE_RANK - 榜单 */
  REWARD_SOURCE_TYPE_RANK = 1,
  /** REWARD_SOURCE_TYPE_TASK - 任务 */
  REWARD_SOURCE_TYPE_TASK = 2,
  /** REWARD_SOURCE_TYPE_LOTTERY - 抽奖 */
  REWARD_SOURCE_TYPE_LOTTERY = 3,
  /** REWARD_SOURCE_TYPE_PRIZEPOOL - 奖池 */
  REWARD_SOURCE_TYPE_PRIZEPOOL = 4,
  UNRECOGNIZED = -1
}

export function rewardSourceTypeFromJSON(object: any): RewardSourceType {
  switch (object) {
    case 0:
    case 'REWARD_SOURCE_TYPE_NONE':
      return RewardSourceType.REWARD_SOURCE_TYPE_NONE;
    case 1:
    case 'REWARD_SOURCE_TYPE_RANK':
      return RewardSourceType.REWARD_SOURCE_TYPE_RANK;
    case 2:
    case 'REWARD_SOURCE_TYPE_TASK':
      return RewardSourceType.REWARD_SOURCE_TYPE_TASK;
    case 3:
    case 'REWARD_SOURCE_TYPE_LOTTERY':
      return RewardSourceType.REWARD_SOURCE_TYPE_LOTTERY;
    case 4:
    case 'REWARD_SOURCE_TYPE_PRIZEPOOL':
      return RewardSourceType.REWARD_SOURCE_TYPE_PRIZEPOOL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RewardSourceType.UNRECOGNIZED;
  }
}

/**
 * 因为不同业务的货币叫法不一致, 但概念是相通的:
 * Playmate:
 * 结算: 钻石-diamond
 * 消费: 星币-star
 * 币商: 星票-ticket
 * 美分: 美分-usd
 *
 * Lucky:
 * 结算: 积分-point
 * 消费: 金币-coin
 * 币商: -
 * 美分: -
 *
 * Echo:
 * 结算: 钻石-diamond
 * 消费: 金币-coin
 * 币商: 金票-ticket
 * 美分: 美分-usd
 *
 * HalaMe:
 * 结算: 宝石-gems
 * 消费: 钻石-diamond
 * 币商: 金票-ticket
 * 美分: 美分-usd
 *
 * Carne:
 * 经营货币：coin
 *
 * darling:
 * 结算：金币
 * 消费：金币
 *
 * dating:
 * 结算：金币
 * 消费：宝石
 *
 * nova：
 * 消费：钻石
 * 第二消费货币：金币
 *
 * 所以公会中台对货币进行以下抽象, 让业务各自去对应自己的货币类型.
 */
export enum Currency {
  CURRENCY_NONE = 0,
  /** CURRENCY_SETTLEMENT - 结算货币 */
  CURRENCY_SETTLEMENT = 1,
  /** CURRENCY_CONSUMPTION - 消费货币 */
  CURRENCY_CONSUMPTION = 2,
  /** CURRENCY_COIN_AGENCY - 币商货币 */
  CURRENCY_COIN_AGENCY = 3,
  /** CURRENCY_DOLLAR_CENT - 美分货币 */
  CURRENCY_DOLLAR_CENT = 4,
  /** CURRENCY_COIN_OPERATING - 经营货币 */
  CURRENCY_COIN_OPERATING = 5,
  /** CURRENCY_POINT - 积分货币，一般是做任务获得的积分，目前只有dating 用 */
  CURRENCY_POINT = 6,
  /** CURRENCY_SECONDARY_CONSUMPTION - 第二消费货币 */
  CURRENCY_SECONDARY_CONSUMPTION = 12,
  /** CURRENCY_SECONDARY_SETTLEMENT - 第二结算货币， 目前只有dating 用来做用户收益结算 */
  CURRENCY_SECONDARY_SETTLEMENT = 13,
  UNRECOGNIZED = -1
}

export function currencyFromJSON(object: any): Currency {
  switch (object) {
    case 0:
    case 'CURRENCY_NONE':
      return Currency.CURRENCY_NONE;
    case 1:
    case 'CURRENCY_SETTLEMENT':
      return Currency.CURRENCY_SETTLEMENT;
    case 2:
    case 'CURRENCY_CONSUMPTION':
      return Currency.CURRENCY_CONSUMPTION;
    case 3:
    case 'CURRENCY_COIN_AGENCY':
      return Currency.CURRENCY_COIN_AGENCY;
    case 4:
    case 'CURRENCY_DOLLAR_CENT':
      return Currency.CURRENCY_DOLLAR_CENT;
    case 5:
    case 'CURRENCY_COIN_OPERATING':
      return Currency.CURRENCY_COIN_OPERATING;
    case 6:
    case 'CURRENCY_POINT':
      return Currency.CURRENCY_POINT;
    case 12:
    case 'CURRENCY_SECONDARY_CONSUMPTION':
      return Currency.CURRENCY_SECONDARY_CONSUMPTION;
    case 13:
    case 'CURRENCY_SECONDARY_SETTLEMENT':
      return Currency.CURRENCY_SECONDARY_SETTLEMENT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Currency.UNRECOGNIZED;
  }
}

/** 房间标签模式 */
export enum RoomTagStyle {
  ROOM_TAG_TYPE_NONE = 0,
  /** ROOM_TAG_TYPE_ICON - 仅展示图标 */
  ROOM_TAG_TYPE_ICON = 10,
  /** ROOM_TAG_TYPE_ICON_NAME - 展示图标 + 名字 */
  ROOM_TAG_TYPE_ICON_NAME = 20,
  UNRECOGNIZED = -1
}

export function roomTagStyleFromJSON(object: any): RoomTagStyle {
  switch (object) {
    case 0:
    case 'ROOM_TAG_TYPE_NONE':
      return RoomTagStyle.ROOM_TAG_TYPE_NONE;
    case 10:
    case 'ROOM_TAG_TYPE_ICON':
      return RoomTagStyle.ROOM_TAG_TYPE_ICON;
    case 20:
    case 'ROOM_TAG_TYPE_ICON_NAME':
      return RoomTagStyle.ROOM_TAG_TYPE_ICON_NAME;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomTagStyle.UNRECOGNIZED;
  }
}

/** 主体类型 */
export enum SubjectType {
  /** SUBJECT_TYPE_NONE - 无意义 */
  SUBJECT_TYPE_NONE = 0,
  /** SUBJECT_TYPE_USER - 用户 */
  SUBJECT_TYPE_USER = 10,
  UNRECOGNIZED = -1
}

export function subjectTypeFromJSON(object: any): SubjectType {
  switch (object) {
    case 0:
    case 'SUBJECT_TYPE_NONE':
      return SubjectType.SUBJECT_TYPE_NONE;
    case 10:
    case 'SUBJECT_TYPE_USER':
      return SubjectType.SUBJECT_TYPE_USER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SubjectType.UNRECOGNIZED;
  }
}

/** 榜单奖励目标类型 */
export enum RankRewardTargetType {
  RANK_REWARD_TARGET_TYPE_NONE = 0,
  /** RANK_REWARD_TARGET_TYPE_USER - 用户 */
  RANK_REWARD_TARGET_TYPE_USER = 1,
  /** RANK_REWARD_TARGET_TYPE_USER_RESOURCE - 用户资源 */
  RANK_REWARD_TARGET_TYPE_USER_RESOURCE = 2,
  /** RANK_REWARD_TARGET_TYPE_FAMILY - 家族 */
  RANK_REWARD_TARGET_TYPE_FAMILY = 5,
  /** RANK_REWARD_TARGET_TYPE_FAMILY_RESOURCE - 家族资源 */
  RANK_REWARD_TARGET_TYPE_FAMILY_RESOURCE = 6,
  /** RANK_REWARD_TARGET_TYPE_ROOM - 房间 */
  RANK_REWARD_TARGET_TYPE_ROOM = 7,
  UNRECOGNIZED = -1
}

export function rankRewardTargetTypeFromJSON(object: any): RankRewardTargetType {
  switch (object) {
    case 0:
    case 'RANK_REWARD_TARGET_TYPE_NONE':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_NONE;
    case 1:
    case 'RANK_REWARD_TARGET_TYPE_USER':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_USER;
    case 2:
    case 'RANK_REWARD_TARGET_TYPE_USER_RESOURCE':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_USER_RESOURCE;
    case 5:
    case 'RANK_REWARD_TARGET_TYPE_FAMILY':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_FAMILY;
    case 6:
    case 'RANK_REWARD_TARGET_TYPE_FAMILY_RESOURCE':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_FAMILY_RESOURCE;
    case 7:
    case 'RANK_REWARD_TARGET_TYPE_ROOM':
      return RankRewardTargetType.RANK_REWARD_TARGET_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankRewardTargetType.UNRECOGNIZED;
  }
}

/** 房间标签 */
export interface RoomTag {
  /** 标签 */
  id: number;
  /** 房间标签模式 */
  style: RoomTagStyle;
  /** 标签权重, 如果一个房间有多个标签时按权重从高到低排序和剔除显示不下的. */
  weight: number;
  /** 标签名称内容 */
  name: string;
  /** 标签图标 */
  icon: string;
}

/** 用户信息 */
export interface UserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
}

function createBaseRoomTag(): RoomTag {
  return { id: 0, style: 0, weight: 0, name: '', icon: '' };
}

export const RoomTag: MessageFns<RoomTag> = {
  fromJSON(object: any): RoomTag {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      style: isSet(object.style) ? roomTagStyleFromJSON(object.style) : 0,
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomTag>, I>>(base?: I): RoomTag {
    return RoomTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomTag>, I>>(object: I): RoomTag {
    const message = createBaseRoomTag();
    message.id = object.id ?? 0;
    message.style = object.style ?? 0;
    message.weight = object.weight ?? 0;
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return { uid: 0, show_uid: '', nickname: '', avatar: '' };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
