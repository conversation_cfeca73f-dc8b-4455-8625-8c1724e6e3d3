// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/room_black.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/roomblack */

/** 添加黑名单 */
export interface AddRoomBlackUserReq {
  black_uid: number;
  room_id: number;
}

export interface AddRoomBlackUserResp {}

/** 移除黑名单 */
export interface RemoveRoomBlackUserReq {
  remove_uid: number[];
  room_id: number;
}

export interface RemoveRoomBlackUserResp {}

/** 获取黑名单列表 */
export interface GetRoomBlackListReq {
  page: Page | undefined;
  room_id: number;
}

export interface GetRoomBlackListResp {
  page: Page | undefined;
  uids: number[];
}

/** 批量检查用户是否在黑名单中 */
export interface CheckInRoomBlackReq {
  uids: number[];
  room_id: number;
}

export interface CheckInRoomBlackResp {
  black_map: { [key: number]: boolean };
}

export interface CheckInRoomBlackResp_BlackMapEntry {
  key: number;
  value: boolean;
}

function createBaseAddRoomBlackUserReq(): AddRoomBlackUserReq {
  return { black_uid: 0, room_id: 0 };
}

export const AddRoomBlackUserReq: MessageFns<AddRoomBlackUserReq> = {
  fromJSON(object: any): AddRoomBlackUserReq {
    return {
      black_uid: isSet(object.black_uid) ? globalThis.Number(object.black_uid) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<AddRoomBlackUserReq>, I>>(base?: I): AddRoomBlackUserReq {
    return AddRoomBlackUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddRoomBlackUserReq>, I>>(object: I): AddRoomBlackUserReq {
    const message = createBaseAddRoomBlackUserReq();
    message.black_uid = object.black_uid ?? 0;
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseAddRoomBlackUserResp(): AddRoomBlackUserResp {
  return {};
}

export const AddRoomBlackUserResp: MessageFns<AddRoomBlackUserResp> = {
  fromJSON(_: any): AddRoomBlackUserResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddRoomBlackUserResp>, I>>(base?: I): AddRoomBlackUserResp {
    return AddRoomBlackUserResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddRoomBlackUserResp>, I>>(_: I): AddRoomBlackUserResp {
    const message = createBaseAddRoomBlackUserResp();
    return message;
  }
};

function createBaseRemoveRoomBlackUserReq(): RemoveRoomBlackUserReq {
  return { remove_uid: [], room_id: 0 };
}

export const RemoveRoomBlackUserReq: MessageFns<RemoveRoomBlackUserReq> = {
  fromJSON(object: any): RemoveRoomBlackUserReq {
    return {
      remove_uid: globalThis.Array.isArray(object?.remove_uid)
        ? object.remove_uid.map((e: any) => globalThis.Number(e))
        : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<RemoveRoomBlackUserReq>, I>>(base?: I): RemoveRoomBlackUserReq {
    return RemoveRoomBlackUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveRoomBlackUserReq>, I>>(object: I): RemoveRoomBlackUserReq {
    const message = createBaseRemoveRoomBlackUserReq();
    message.remove_uid = object.remove_uid?.map(e => e) || [];
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseRemoveRoomBlackUserResp(): RemoveRoomBlackUserResp {
  return {};
}

export const RemoveRoomBlackUserResp: MessageFns<RemoveRoomBlackUserResp> = {
  fromJSON(_: any): RemoveRoomBlackUserResp {
    return {};
  },

  create<I extends Exact<DeepPartial<RemoveRoomBlackUserResp>, I>>(base?: I): RemoveRoomBlackUserResp {
    return RemoveRoomBlackUserResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveRoomBlackUserResp>, I>>(_: I): RemoveRoomBlackUserResp {
    const message = createBaseRemoveRoomBlackUserResp();
    return message;
  }
};

function createBaseGetRoomBlackListReq(): GetRoomBlackListReq {
  return { page: undefined, room_id: 0 };
}

export const GetRoomBlackListReq: MessageFns<GetRoomBlackListReq> = {
  fromJSON(object: any): GetRoomBlackListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetRoomBlackListReq>, I>>(base?: I): GetRoomBlackListReq {
    return GetRoomBlackListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomBlackListReq>, I>>(object: I): GetRoomBlackListReq {
    const message = createBaseGetRoomBlackListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetRoomBlackListResp(): GetRoomBlackListResp {
  return { page: undefined, uids: [] };
}

export const GetRoomBlackListResp: MessageFns<GetRoomBlackListResp> = {
  fromJSON(object: any): GetRoomBlackListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetRoomBlackListResp>, I>>(base?: I): GetRoomBlackListResp {
    return GetRoomBlackListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomBlackListResp>, I>>(object: I): GetRoomBlackListResp {
    const message = createBaseGetRoomBlackListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckInRoomBlackReq(): CheckInRoomBlackReq {
  return { uids: [], room_id: 0 };
}

export const CheckInRoomBlackReq: MessageFns<CheckInRoomBlackReq> = {
  fromJSON(object: any): CheckInRoomBlackReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<CheckInRoomBlackReq>, I>>(base?: I): CheckInRoomBlackReq {
    return CheckInRoomBlackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInRoomBlackReq>, I>>(object: I): CheckInRoomBlackReq {
    const message = createBaseCheckInRoomBlackReq();
    message.uids = object.uids?.map(e => e) || [];
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseCheckInRoomBlackResp(): CheckInRoomBlackResp {
  return { black_map: {} };
}

export const CheckInRoomBlackResp: MessageFns<CheckInRoomBlackResp> = {
  fromJSON(object: any): CheckInRoomBlackResp {
    return {
      black_map: isObject(object.black_map)
        ? Object.entries(object.black_map).reduce<{ [key: number]: boolean }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Boolean(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CheckInRoomBlackResp>, I>>(base?: I): CheckInRoomBlackResp {
    return CheckInRoomBlackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInRoomBlackResp>, I>>(object: I): CheckInRoomBlackResp {
    const message = createBaseCheckInRoomBlackResp();
    message.black_map = Object.entries(object.black_map ?? {}).reduce<{ [key: number]: boolean }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Boolean(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCheckInRoomBlackResp_BlackMapEntry(): CheckInRoomBlackResp_BlackMapEntry {
  return { key: 0, value: false };
}

export const CheckInRoomBlackResp_BlackMapEntry: MessageFns<CheckInRoomBlackResp_BlackMapEntry> = {
  fromJSON(object: any): CheckInRoomBlackResp_BlackMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false
    };
  },

  create<I extends Exact<DeepPartial<CheckInRoomBlackResp_BlackMapEntry>, I>>(
    base?: I
  ): CheckInRoomBlackResp_BlackMapEntry {
    return CheckInRoomBlackResp_BlackMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInRoomBlackResp_BlackMapEntry>, I>>(
    object: I
  ): CheckInRoomBlackResp_BlackMapEntry {
    const message = createBaseCheckInRoomBlackResp_BlackMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? false;
    return message;
  }
};

/** 房间黑名单 */
export type RoomBlackDefinition = typeof RoomBlackDefinition;
export const RoomBlackDefinition = {
  name: 'RoomBlack',
  fullName: 'comm.api.relation.RoomBlack',
  methods: {
    /** 添加用户到黑名单 */
    addRoomBlackUser: {
      name: 'AddRoomBlackUser',
      requestType: AddRoomBlackUserReq,
      requestStream: false,
      responseType: AddRoomBlackUserResp,
      responseStream: false,
      options: {}
    },
    /** 移除用户黑名单 */
    removeRoomBlackUser: {
      name: 'RemoveRoomBlackUser',
      requestType: RemoveRoomBlackUserReq,
      requestStream: false,
      responseType: RemoveRoomBlackUserResp,
      responseStream: false,
      options: {}
    },
    /** 获取黑名单列表 */
    getRoomBlackList: {
      name: 'GetRoomBlackList',
      requestType: GetRoomBlackListReq,
      requestStream: false,
      responseType: GetRoomBlackListResp,
      responseStream: false,
      options: {}
    },
    /** 批量检查用户是否在黑名单中 */
    checkInRoomBlack: {
      name: 'CheckInRoomBlack',
      requestType: CheckInRoomBlackReq,
      requestStream: false,
      responseType: CheckInRoomBlackResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
