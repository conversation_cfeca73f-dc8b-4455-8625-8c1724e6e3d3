// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/room_follow.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/roomfollow */

/** 关注房间 */
export interface FollowRoomReq {
  /** 添加关注的房间ID */
  room_id: number;
}

export interface FollowRoomRsp {}

/** 取消关注 */
export interface UnFollowRoomReq {
  /** 取消关注的房间ID */
  unFollowRoomID: number;
}

export interface UnFollowRoomRsp {}

/** 获取用户关注房间ID列表 */
export interface GetFollowRoomIDsReq {
  /** 分页 */
  page: Page | undefined;
}

export interface GetFollowRoomIDsRsp {
  /** 分页 */
  page: Page | undefined;
  /** 关注房间ID列表 */
  room_ids: number[];
}

/** 获取粉丝ID列表 */
export interface GetRoomFansIDsReq {
  /** 分页 */
  page: Page | undefined;
  /** 房间ID */
  room_id: number;
}

export interface GetRoomFansIDsRsp {
  /** 分页 */
  page: Page | undefined;
  /** 粉丝ID列表 */
  fans_ids: number[];
}

function createBaseFollowRoomReq(): FollowRoomReq {
  return { room_id: 0 };
}

export const FollowRoomReq: MessageFns<FollowRoomReq> = {
  fromJSON(object: any): FollowRoomReq {
    return { room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<FollowRoomReq>, I>>(base?: I): FollowRoomReq {
    return FollowRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRoomReq>, I>>(object: I): FollowRoomReq {
    const message = createBaseFollowRoomReq();
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseFollowRoomRsp(): FollowRoomRsp {
  return {};
}

export const FollowRoomRsp: MessageFns<FollowRoomRsp> = {
  fromJSON(_: any): FollowRoomRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<FollowRoomRsp>, I>>(base?: I): FollowRoomRsp {
    return FollowRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRoomRsp>, I>>(_: I): FollowRoomRsp {
    const message = createBaseFollowRoomRsp();
    return message;
  }
};

function createBaseUnFollowRoomReq(): UnFollowRoomReq {
  return { unFollowRoomID: 0 };
}

export const UnFollowRoomReq: MessageFns<UnFollowRoomReq> = {
  fromJSON(object: any): UnFollowRoomReq {
    return { unFollowRoomID: isSet(object.unFollowRoomID) ? globalThis.Number(object.unFollowRoomID) : 0 };
  },

  create<I extends Exact<DeepPartial<UnFollowRoomReq>, I>>(base?: I): UnFollowRoomReq {
    return UnFollowRoomReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnFollowRoomReq>, I>>(object: I): UnFollowRoomReq {
    const message = createBaseUnFollowRoomReq();
    message.unFollowRoomID = object.unFollowRoomID ?? 0;
    return message;
  }
};

function createBaseUnFollowRoomRsp(): UnFollowRoomRsp {
  return {};
}

export const UnFollowRoomRsp: MessageFns<UnFollowRoomRsp> = {
  fromJSON(_: any): UnFollowRoomRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnFollowRoomRsp>, I>>(base?: I): UnFollowRoomRsp {
    return UnFollowRoomRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnFollowRoomRsp>, I>>(_: I): UnFollowRoomRsp {
    const message = createBaseUnFollowRoomRsp();
    return message;
  }
};

function createBaseGetFollowRoomIDsReq(): GetFollowRoomIDsReq {
  return { page: undefined };
}

export const GetFollowRoomIDsReq: MessageFns<GetFollowRoomIDsReq> = {
  fromJSON(object: any): GetFollowRoomIDsReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFollowRoomIDsReq>, I>>(base?: I): GetFollowRoomIDsReq {
    return GetFollowRoomIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowRoomIDsReq>, I>>(object: I): GetFollowRoomIDsReq {
    const message = createBaseGetFollowRoomIDsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFollowRoomIDsRsp(): GetFollowRoomIDsRsp {
  return { page: undefined, room_ids: [] };
}

export const GetFollowRoomIDsRsp: MessageFns<GetFollowRoomIDsRsp> = {
  fromJSON(object: any): GetFollowRoomIDsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetFollowRoomIDsRsp>, I>>(base?: I): GetFollowRoomIDsRsp {
    return GetFollowRoomIDsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowRoomIDsRsp>, I>>(object: I): GetFollowRoomIDsRsp {
    const message = createBaseGetFollowRoomIDsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_ids = object.room_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetRoomFansIDsReq(): GetRoomFansIDsReq {
  return { page: undefined, room_id: 0 };
}

export const GetRoomFansIDsReq: MessageFns<GetRoomFansIDsReq> = {
  fromJSON(object: any): GetRoomFansIDsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetRoomFansIDsReq>, I>>(base?: I): GetRoomFansIDsReq {
    return GetRoomFansIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomFansIDsReq>, I>>(object: I): GetRoomFansIDsReq {
    const message = createBaseGetRoomFansIDsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.room_id = object.room_id ?? 0;
    return message;
  }
};

function createBaseGetRoomFansIDsRsp(): GetRoomFansIDsRsp {
  return { page: undefined, fans_ids: [] };
}

export const GetRoomFansIDsRsp: MessageFns<GetRoomFansIDsRsp> = {
  fromJSON(object: any): GetRoomFansIDsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      fans_ids: globalThis.Array.isArray(object?.fans_ids) ? object.fans_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetRoomFansIDsRsp>, I>>(base?: I): GetRoomFansIDsRsp {
    return GetRoomFansIDsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomFansIDsRsp>, I>>(object: I): GetRoomFansIDsRsp {
    const message = createBaseGetRoomFansIDsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.fans_ids = object.fans_ids?.map(e => e) || [];
    return message;
  }
};

/** 房间关注 */
export type RoomFollowDefinition = typeof RoomFollowDefinition;
export const RoomFollowDefinition = {
  name: 'RoomFollow',
  fullName: 'comm.api.relation.RoomFollow',
  methods: {
    /** 关注 */
    followRoom: {
      name: 'FollowRoom',
      requestType: FollowRoomReq,
      requestStream: false,
      responseType: FollowRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 取消关注 */
    unFollowRoom: {
      name: 'UnFollowRoom',
      requestType: UnFollowRoomReq,
      requestStream: false,
      responseType: UnFollowRoomRsp,
      responseStream: false,
      options: {}
    },
    /** 获取用户关注房间ID列表 */
    getFollowRoomIDs: {
      name: 'GetFollowRoomIDs',
      requestType: GetFollowRoomIDsReq,
      requestStream: false,
      responseType: GetFollowRoomIDsRsp,
      responseStream: false,
      options: {}
    },
    /** 粉丝列表 （关注房间的用户ID） */
    getRoomFansIDs: {
      name: 'GetRoomFansIDs',
      requestType: GetRoomFansIDsReq,
      requestStream: false,
      responseType: GetRoomFansIDsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
