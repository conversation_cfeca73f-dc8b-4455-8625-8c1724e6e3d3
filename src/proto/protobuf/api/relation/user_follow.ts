// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/user_follow.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/userfollow */

/** 关注用户时的场景 */
export enum UserFollowScene {
  User_FOLLOW_SCENE_NONE = 0,
  /** User_FOLLOW_SCENE_ROOM - 语音房内，用户之间关注 */
  User_FOLLOW_SCENE_ROOM = 10,
  /** User_FOLLOW_SCENE_IM - 私聊，用户之间关注 */
  User_FOLLOW_SCENE_IM = 20,
  UNRECOGNIZED = -1
}

export function userFollowSceneFromJSON(object: any): UserFollowScene {
  switch (object) {
    case 0:
    case 'User_FOLLOW_SCENE_NONE':
      return UserFollowScene.User_FOLLOW_SCENE_NONE;
    case 10:
    case 'User_FOLLOW_SCENE_ROOM':
      return UserFollowScene.User_FOLLOW_SCENE_ROOM;
    case 20:
    case 'User_FOLLOW_SCENE_IM':
      return UserFollowScene.User_FOLLOW_SCENE_IM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserFollowScene.UNRECOGNIZED;
  }
}

/** 关注用户 */
export interface FollowReq {
  /** 被关注用户ID */
  followUid: number;
  /** 场景 */
  scene: UserFollowScene;
}

export interface FollowRsp {}

/** 取消关注 */
export interface UnFollowReq {
  /** 取消关注用户ID */
  unFollowUid: number;
}

export interface UnFollowRsp {}

/** 获取关注用户ID列表 */
export interface GetFollowUIDsReq {
  /** 分页 */
  page: Page | undefined;
}

export interface GetFollowUIDsRsp {
  /** 分页 */
  page: Page | undefined;
  /** 关注用户ID列表 */
  followUIDs: number[];
}

/** 获取粉丝ID列表 */
export interface GetFansUIDsReq {
  /** 分页 */
  page: Page | undefined;
}

export interface GetFansUIDsRsp {
  /** 分页 */
  page: Page | undefined;
  /** 粉丝ID列表 */
  fansUIDs: number[];
}

export interface CheckIsFollowReq {
  /** 目标用户ID */
  targetUid: number;
}

export interface CheckIsFollowRsp {
  /** 是否关注 */
  isFollow: boolean;
}

/** 批量关注用户 */
export interface BatchFollowReq {
  /** 被关注用户ID */
  follow_uids: number[];
  /** 场景 */
  scene: UserFollowScene;
}

export interface BatchFollowRsp {}

function createBaseFollowReq(): FollowReq {
  return { followUid: 0, scene: 0 };
}

export const FollowReq: MessageFns<FollowReq> = {
  fromJSON(object: any): FollowReq {
    return {
      followUid: isSet(object.followUid) ? globalThis.Number(object.followUid) : 0,
      scene: isSet(object.scene) ? userFollowSceneFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<FollowReq>, I>>(base?: I): FollowReq {
    return FollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowReq>, I>>(object: I): FollowReq {
    const message = createBaseFollowReq();
    message.followUid = object.followUid ?? 0;
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseFollowRsp(): FollowRsp {
  return {};
}

export const FollowRsp: MessageFns<FollowRsp> = {
  fromJSON(_: any): FollowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<FollowRsp>, I>>(base?: I): FollowRsp {
    return FollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FollowRsp>, I>>(_: I): FollowRsp {
    const message = createBaseFollowRsp();
    return message;
  }
};

function createBaseUnFollowReq(): UnFollowReq {
  return { unFollowUid: 0 };
}

export const UnFollowReq: MessageFns<UnFollowReq> = {
  fromJSON(object: any): UnFollowReq {
    return { unFollowUid: isSet(object.unFollowUid) ? globalThis.Number(object.unFollowUid) : 0 };
  },

  create<I extends Exact<DeepPartial<UnFollowReq>, I>>(base?: I): UnFollowReq {
    return UnFollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnFollowReq>, I>>(object: I): UnFollowReq {
    const message = createBaseUnFollowReq();
    message.unFollowUid = object.unFollowUid ?? 0;
    return message;
  }
};

function createBaseUnFollowRsp(): UnFollowRsp {
  return {};
}

export const UnFollowRsp: MessageFns<UnFollowRsp> = {
  fromJSON(_: any): UnFollowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnFollowRsp>, I>>(base?: I): UnFollowRsp {
    return UnFollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnFollowRsp>, I>>(_: I): UnFollowRsp {
    const message = createBaseUnFollowRsp();
    return message;
  }
};

function createBaseGetFollowUIDsReq(): GetFollowUIDsReq {
  return { page: undefined };
}

export const GetFollowUIDsReq: MessageFns<GetFollowUIDsReq> = {
  fromJSON(object: any): GetFollowUIDsReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFollowUIDsReq>, I>>(base?: I): GetFollowUIDsReq {
    return GetFollowUIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowUIDsReq>, I>>(object: I): GetFollowUIDsReq {
    const message = createBaseGetFollowUIDsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFollowUIDsRsp(): GetFollowUIDsRsp {
  return { page: undefined, followUIDs: [] };
}

export const GetFollowUIDsRsp: MessageFns<GetFollowUIDsRsp> = {
  fromJSON(object: any): GetFollowUIDsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      followUIDs: globalThis.Array.isArray(object?.followUIDs)
        ? object.followUIDs.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetFollowUIDsRsp>, I>>(base?: I): GetFollowUIDsRsp {
    return GetFollowUIDsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFollowUIDsRsp>, I>>(object: I): GetFollowUIDsRsp {
    const message = createBaseGetFollowUIDsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.followUIDs = object.followUIDs?.map(e => e) || [];
    return message;
  }
};

function createBaseGetFansUIDsReq(): GetFansUIDsReq {
  return { page: undefined };
}

export const GetFansUIDsReq: MessageFns<GetFansUIDsReq> = {
  fromJSON(object: any): GetFansUIDsReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFansUIDsReq>, I>>(base?: I): GetFansUIDsReq {
    return GetFansUIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFansUIDsReq>, I>>(object: I): GetFansUIDsReq {
    const message = createBaseGetFansUIDsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFansUIDsRsp(): GetFansUIDsRsp {
  return { page: undefined, fansUIDs: [] };
}

export const GetFansUIDsRsp: MessageFns<GetFansUIDsRsp> = {
  fromJSON(object: any): GetFansUIDsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      fansUIDs: globalThis.Array.isArray(object?.fansUIDs) ? object.fansUIDs.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetFansUIDsRsp>, I>>(base?: I): GetFansUIDsRsp {
    return GetFansUIDsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFansUIDsRsp>, I>>(object: I): GetFansUIDsRsp {
    const message = createBaseGetFansUIDsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.fansUIDs = object.fansUIDs?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckIsFollowReq(): CheckIsFollowReq {
  return { targetUid: 0 };
}

export const CheckIsFollowReq: MessageFns<CheckIsFollowReq> = {
  fromJSON(object: any): CheckIsFollowReq {
    return { targetUid: isSet(object.targetUid) ? globalThis.Number(object.targetUid) : 0 };
  },

  create<I extends Exact<DeepPartial<CheckIsFollowReq>, I>>(base?: I): CheckIsFollowReq {
    return CheckIsFollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckIsFollowReq>, I>>(object: I): CheckIsFollowReq {
    const message = createBaseCheckIsFollowReq();
    message.targetUid = object.targetUid ?? 0;
    return message;
  }
};

function createBaseCheckIsFollowRsp(): CheckIsFollowRsp {
  return { isFollow: false };
}

export const CheckIsFollowRsp: MessageFns<CheckIsFollowRsp> = {
  fromJSON(object: any): CheckIsFollowRsp {
    return { isFollow: isSet(object.isFollow) ? globalThis.Boolean(object.isFollow) : false };
  },

  create<I extends Exact<DeepPartial<CheckIsFollowRsp>, I>>(base?: I): CheckIsFollowRsp {
    return CheckIsFollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckIsFollowRsp>, I>>(object: I): CheckIsFollowRsp {
    const message = createBaseCheckIsFollowRsp();
    message.isFollow = object.isFollow ?? false;
    return message;
  }
};

function createBaseBatchFollowReq(): BatchFollowReq {
  return { follow_uids: [], scene: 0 };
}

export const BatchFollowReq: MessageFns<BatchFollowReq> = {
  fromJSON(object: any): BatchFollowReq {
    return {
      follow_uids: globalThis.Array.isArray(object?.follow_uids)
        ? object.follow_uids.map((e: any) => globalThis.Number(e))
        : [],
      scene: isSet(object.scene) ? userFollowSceneFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchFollowReq>, I>>(base?: I): BatchFollowReq {
    return BatchFollowReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchFollowReq>, I>>(object: I): BatchFollowReq {
    const message = createBaseBatchFollowReq();
    message.follow_uids = object.follow_uids?.map(e => e) || [];
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseBatchFollowRsp(): BatchFollowRsp {
  return {};
}

export const BatchFollowRsp: MessageFns<BatchFollowRsp> = {
  fromJSON(_: any): BatchFollowRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchFollowRsp>, I>>(base?: I): BatchFollowRsp {
    return BatchFollowRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchFollowRsp>, I>>(_: I): BatchFollowRsp {
    const message = createBaseBatchFollowRsp();
    return message;
  }
};

/** 用户关注 */
export type UserFollowDefinition = typeof UserFollowDefinition;
export const UserFollowDefinition = {
  name: 'UserFollow',
  fullName: 'comm.api.relation.UserFollow',
  methods: {
    /** 关注 */
    follow: {
      name: 'Follow',
      requestType: FollowReq,
      requestStream: false,
      responseType: FollowRsp,
      responseStream: false,
      options: {}
    },
    /** 取消关注 */
    unFollow: {
      name: 'UnFollow',
      requestType: UnFollowReq,
      requestStream: false,
      responseType: UnFollowRsp,
      responseStream: false,
      options: {}
    },
    /** 关注列表 */
    getFollowUIDs: {
      name: 'GetFollowUIDs',
      requestType: GetFollowUIDsReq,
      requestStream: false,
      responseType: GetFollowUIDsRsp,
      responseStream: false,
      options: {}
    },
    /** 粉丝列表 */
    getFansIDs: {
      name: 'GetFansIDs',
      requestType: GetFansUIDsReq,
      requestStream: false,
      responseType: GetFansUIDsRsp,
      responseStream: false,
      options: {}
    },
    /** 是否关注 */
    checkIsFollow: {
      name: 'CheckIsFollow',
      requestType: CheckIsFollowReq,
      requestStream: false,
      responseType: CheckIsFollowRsp,
      responseStream: false,
      options: {}
    },
    /** 批量关注 */
    batchFollow: {
      name: 'BatchFollow',
      requestType: BatchFollowReq,
      requestStream: false,
      responseType: BatchFollowRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
