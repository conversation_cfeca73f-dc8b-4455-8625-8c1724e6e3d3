// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/code.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.relation';

/** 错误码 */
export enum ErrCode {
  /** CODE_OK - 成功 */
  CODE_OK = 0,
  /** CODE_OPERATE_FREQUENTLY - 63000 - 63999 社交关系相关 */
  CODE_OPERATE_FREQUENTLY = 63000,
  /** CODE_UNFOLLOW - 未关注 */
  CODE_UNFOLLOW = 63001,
  /** CODE_REPEATED_FOLLOW - 已关注 */
  CODE_REPEATED_FOLLOW = 63002,
  /** CODE_USER_NOT_FOUND - 用户不存在 */
  CODE_USER_NOT_FOUND = 63003,
  /** CODE_ROOM_NOT_FOUND - 房间不存在 */
  CODE_ROOM_NOT_FOUND = 63004,
  /** CODE_REPEATED_OPERATE - 重复操作 */
  CODE_REPEATED_OPERATE = 63005,
  /** CODE_RECORDE_NOT_FOUND - 记录不存在 */
  CODE_RECORDE_NOT_FOUND = 63006,
  /** CODE_IS_IN_USER_BLACK - 已被用户拉黑 */
  CODE_IS_IN_USER_BLACK = 63007,
  /** CODE_IS_IN_ROOM_BLACK - 已被房间拉黑 */
  CODE_IS_IN_ROOM_BLACK = 63008,
  /** CODE_NOT_IN_USER_BLACK - 未被用户拉黑 */
  CODE_NOT_IN_USER_BLACK = 63009,
  /** CODE_NOT_IN_ROOM_BLACK - 未被房间拉黑 */
  CODE_NOT_IN_ROOM_BLACK = 63010,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'CODE_OK':
      return ErrCode.CODE_OK;
    case 63000:
    case 'CODE_OPERATE_FREQUENTLY':
      return ErrCode.CODE_OPERATE_FREQUENTLY;
    case 63001:
    case 'CODE_UNFOLLOW':
      return ErrCode.CODE_UNFOLLOW;
    case 63002:
    case 'CODE_REPEATED_FOLLOW':
      return ErrCode.CODE_REPEATED_FOLLOW;
    case 63003:
    case 'CODE_USER_NOT_FOUND':
      return ErrCode.CODE_USER_NOT_FOUND;
    case 63004:
    case 'CODE_ROOM_NOT_FOUND':
      return ErrCode.CODE_ROOM_NOT_FOUND;
    case 63005:
    case 'CODE_REPEATED_OPERATE':
      return ErrCode.CODE_REPEATED_OPERATE;
    case 63006:
    case 'CODE_RECORDE_NOT_FOUND':
      return ErrCode.CODE_RECORDE_NOT_FOUND;
    case 63007:
    case 'CODE_IS_IN_USER_BLACK':
      return ErrCode.CODE_IS_IN_USER_BLACK;
    case 63008:
    case 'CODE_IS_IN_ROOM_BLACK':
      return ErrCode.CODE_IS_IN_ROOM_BLACK;
    case 63009:
    case 'CODE_NOT_IN_USER_BLACK':
      return ErrCode.CODE_NOT_IN_USER_BLACK;
    case 63010:
    case 'CODE_NOT_IN_ROOM_BLACK':
      return ErrCode.CODE_NOT_IN_ROOM_BLACK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}
