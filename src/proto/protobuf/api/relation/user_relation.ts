// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/user_relation.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/userrelation */

/** 关系枚举 */
export enum Relation {
  /** RELATION_NONE - 无意义 */
  RELATION_NONE = 0,
  /** RELATION_BLOCKED - 我拉黑TA */
  RELATION_BLOCKED = 1,
  /** RELATION_BE_BLOCKED - 被TA拉黑 */
  RELATION_BE_BLOCKED = 2,
  /** RELATION_BOTH_BLOCKED - 互相拉黑 */
  RELATION_BOTH_BLOCKED = 3,
  /** RELATION_STRANGER - 陌生人, 既没拉黑也没关注 */
  RELATION_STRANGER = 4,
  /** RELATION_FOLLOWED - 我关注TA */
  RELATION_FOLLOWED = 5,
  /** RELATION_BE_FOLLOWED - 被TA关注 */
  RELATION_BE_FOLLOWED = 6,
  /** RELATION_BOTH_FOLLOWED - 互相关注 */
  RELATION_BOTH_FOLLOWED = 7,
  UNRECOGNIZED = -1
}

export function relationFromJSON(object: any): Relation {
  switch (object) {
    case 0:
    case 'RELATION_NONE':
      return Relation.RELATION_NONE;
    case 1:
    case 'RELATION_BLOCKED':
      return Relation.RELATION_BLOCKED;
    case 2:
    case 'RELATION_BE_BLOCKED':
      return Relation.RELATION_BE_BLOCKED;
    case 3:
    case 'RELATION_BOTH_BLOCKED':
      return Relation.RELATION_BOTH_BLOCKED;
    case 4:
    case 'RELATION_STRANGER':
      return Relation.RELATION_STRANGER;
    case 5:
    case 'RELATION_FOLLOWED':
      return Relation.RELATION_FOLLOWED;
    case 6:
    case 'RELATION_BE_FOLLOWED':
      return Relation.RELATION_BE_FOLLOWED;
    case 7:
    case 'RELATION_BOTH_FOLLOWED':
      return Relation.RELATION_BOTH_FOLLOWED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Relation.UNRECOGNIZED;
  }
}

export interface GetUserRelationReq {
  target_uids: number[];
}

export interface GetUserRelationRsp {
  relations: { [key: number]: Relation };
}

export interface GetUserRelationRsp_RelationsEntry {
  key: number;
  value: Relation;
}

function createBaseGetUserRelationReq(): GetUserRelationReq {
  return { target_uids: [] };
}

export const GetUserRelationReq: MessageFns<GetUserRelationReq> = {
  fromJSON(object: any): GetUserRelationReq {
    return {
      target_uids: globalThis.Array.isArray(object?.target_uids)
        ? object.target_uids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationReq>, I>>(base?: I): GetUserRelationReq {
    return GetUserRelationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationReq>, I>>(object: I): GetUserRelationReq {
    const message = createBaseGetUserRelationReq();
    message.target_uids = object.target_uids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetUserRelationRsp(): GetUserRelationRsp {
  return { relations: {} };
}

export const GetUserRelationRsp: MessageFns<GetUserRelationRsp> = {
  fromJSON(object: any): GetUserRelationRsp {
    return {
      relations: isObject(object.relations)
        ? Object.entries(object.relations).reduce<{ [key: number]: Relation }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = relationFromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationRsp>, I>>(base?: I): GetUserRelationRsp {
    return GetUserRelationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationRsp>, I>>(object: I): GetUserRelationRsp {
    const message = createBaseGetUserRelationRsp();
    message.relations = Object.entries(object.relations ?? {}).reduce<{ [key: number]: Relation }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = value as Relation;
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGetUserRelationRsp_RelationsEntry(): GetUserRelationRsp_RelationsEntry {
  return { key: 0, value: 0 };
}

export const GetUserRelationRsp_RelationsEntry: MessageFns<GetUserRelationRsp_RelationsEntry> = {
  fromJSON(object: any): GetUserRelationRsp_RelationsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? relationFromJSON(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationRsp_RelationsEntry>, I>>(
    base?: I
  ): GetUserRelationRsp_RelationsEntry {
    return GetUserRelationRsp_RelationsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationRsp_RelationsEntry>, I>>(
    object: I
  ): GetUserRelationRsp_RelationsEntry {
    const message = createBaseGetUserRelationRsp_RelationsEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  }
};

export type UserRelationDefinition = typeof UserRelationDefinition;
export const UserRelationDefinition = {
  name: 'UserRelation',
  fullName: 'comm.api.relation.UserRelation',
  methods: {
    getUserRelation: {
      name: 'GetUserRelation',
      requestType: GetUserRelationReq,
      requestStream: false,
      responseType: GetUserRelationRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
