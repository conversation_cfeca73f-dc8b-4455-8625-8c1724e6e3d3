// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/user_friend.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/userfriend */

/** 获取好友列表 */
export interface GetFriendUIDsReq {
  /** 分页 */
  page: Page | undefined;
}

export interface GetFriendUIDsResp {
  /** 分页 */
  page: Page | undefined;
  /** 好友列表 */
  friend_uids: number[];
}

function createBaseGetFriendUIDsReq(): GetFriendUIDsReq {
  return { page: undefined };
}

export const GetFriendUIDsReq: MessageFns<GetFriendUIDsReq> = {
  fromJSON(object: any): GetFriendUIDsReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetFriendUIDsReq>, I>>(base?: I): GetFriendUIDsReq {
    return GetFriendUIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFriendUIDsReq>, I>>(object: I): GetFriendUIDsReq {
    const message = createBaseGetFriendUIDsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetFriendUIDsResp(): GetFriendUIDsResp {
  return { page: undefined, friend_uids: [] };
}

export const GetFriendUIDsResp: MessageFns<GetFriendUIDsResp> = {
  fromJSON(object: any): GetFriendUIDsResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      friend_uids: globalThis.Array.isArray(object?.friend_uids)
        ? object.friend_uids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetFriendUIDsResp>, I>>(base?: I): GetFriendUIDsResp {
    return GetFriendUIDsResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetFriendUIDsResp>, I>>(object: I): GetFriendUIDsResp {
    const message = createBaseGetFriendUIDsResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.friend_uids = object.friend_uids?.map(e => e) || [];
    return message;
  }
};

export type UserFriendDefinition = typeof UserFriendDefinition;
export const UserFriendDefinition = {
  name: 'UserFriend',
  fullName: 'comm.api.relation.UserFriend',
  methods: {
    getFriendUIDs: {
      name: 'GetFriendUIDs',
      requestType: GetFriendUIDsReq,
      requestStream: false,
      responseType: GetFriendUIDsResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
