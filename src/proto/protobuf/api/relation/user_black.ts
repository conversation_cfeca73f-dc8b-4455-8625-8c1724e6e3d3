// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/relation/user_black.proto

/* eslint-disable */
import { Page } from '../common/common';

export const protobufPackage = 'comm.api.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handler/userblack */

export enum UserBlackStatus {
  /** Status_None - 状态为空 */
  Status_None = 0,
  /** Status_Initiative - in.UID 主动拉黑用户 */
  Status_Initiative = 1,
  /** Status_Passive - in.UID 被拉黑 */
  Status_Passive = 2,
  /** Status_Each_Other - 相互拉黑 */
  Status_Each_Other = 3,
  UNRECOGNIZED = -1
}

export function userBlackStatusFromJSON(object: any): UserBlackStatus {
  switch (object) {
    case 0:
    case 'Status_None':
      return UserBlackStatus.Status_None;
    case 1:
    case 'Status_Initiative':
      return UserBlackStatus.Status_Initiative;
    case 2:
    case 'Status_Passive':
      return UserBlackStatus.Status_Passive;
    case 3:
    case 'Status_Each_Other':
      return UserBlackStatus.Status_Each_Other;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserBlackStatus.UNRECOGNIZED;
  }
}

/** 添加用户黑名单 */
export interface AddBlackUserReq {
  /** 黑名单用户id */
  black_uid: number;
}

export interface AddBlackUserResp {}

/** 移除用户黑名单 */
export interface RemoveBlackUserReq {
  /** 用户id */
  uids: number[];
}

export interface RemoveBlackUserResp {}

/** 获取用户黑名单列表 */
export interface GetBlackUserListReq {
  page: Page | undefined;
}

export interface GetBlackUserListResp {
  page: Page | undefined;
  /** 黑名单用户id */
  black_uids: number[];
}

/** 批量检查用户是否在黑名单中 */
export interface CheckInUserBlackReq {
  /** 用户id */
  uids: number[];
}

export interface CheckInUserBlackResp {
  black_map: { [key: number]: boolean };
}

export interface CheckInUserBlackResp_BlackMapEntry {
  key: number;
  value: boolean;
}

/** 添加房间黑名单 */
export interface AddUserRoomBlackReq {
  /** 黑名单房间id */
  black_room_id: number;
}

export interface AddUserRoomBlackResp {}

/** 移除房间黑名单 */
export interface RemoveUserRoomBlackReq {
  /** 房间id */
  room_ids: number[];
}

export interface RemoveUserRoomBlackResp {}

/** 获取房间黑名单列表 */
export interface GetUserRoomBlackListReq {
  page: Page | undefined;
}

export interface GetUserRoomBlackListResp {
  page: Page | undefined;
  /** 黑名单房间id */
  black_room_ids: number[];
}

/** 批量检查房间是否在黑名单中 */
export interface CheckInUserRoomBlackReq {
  /** 房间id */
  room_ids: number[];
}

export interface CheckInUserRoomBlackResp {
  black_map: { [key: number]: boolean };
}

export interface CheckInUserRoomBlackResp_BlackMapEntry {
  key: number;
  value: boolean;
}

/** 批量检查用户拉黑状态 */
export interface CheckUserBlackStatusReq {
  /** 用户id */
  user_ids: number[];
}

export interface CheckUserBlackStatusResp {
  status_map: { [key: number]: UserBlackStatus };
}

export interface CheckUserBlackStatusResp_StatusMapEntry {
  key: number;
  value: UserBlackStatus;
}

function createBaseAddBlackUserReq(): AddBlackUserReq {
  return { black_uid: 0 };
}

export const AddBlackUserReq: MessageFns<AddBlackUserReq> = {
  fromJSON(object: any): AddBlackUserReq {
    return { black_uid: isSet(object.black_uid) ? globalThis.Number(object.black_uid) : 0 };
  },

  create<I extends Exact<DeepPartial<AddBlackUserReq>, I>>(base?: I): AddBlackUserReq {
    return AddBlackUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddBlackUserReq>, I>>(object: I): AddBlackUserReq {
    const message = createBaseAddBlackUserReq();
    message.black_uid = object.black_uid ?? 0;
    return message;
  }
};

function createBaseAddBlackUserResp(): AddBlackUserResp {
  return {};
}

export const AddBlackUserResp: MessageFns<AddBlackUserResp> = {
  fromJSON(_: any): AddBlackUserResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddBlackUserResp>, I>>(base?: I): AddBlackUserResp {
    return AddBlackUserResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddBlackUserResp>, I>>(_: I): AddBlackUserResp {
    const message = createBaseAddBlackUserResp();
    return message;
  }
};

function createBaseRemoveBlackUserReq(): RemoveBlackUserReq {
  return { uids: [] };
}

export const RemoveBlackUserReq: MessageFns<RemoveBlackUserReq> = {
  fromJSON(object: any): RemoveBlackUserReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<RemoveBlackUserReq>, I>>(base?: I): RemoveBlackUserReq {
    return RemoveBlackUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveBlackUserReq>, I>>(object: I): RemoveBlackUserReq {
    const message = createBaseRemoveBlackUserReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseRemoveBlackUserResp(): RemoveBlackUserResp {
  return {};
}

export const RemoveBlackUserResp: MessageFns<RemoveBlackUserResp> = {
  fromJSON(_: any): RemoveBlackUserResp {
    return {};
  },

  create<I extends Exact<DeepPartial<RemoveBlackUserResp>, I>>(base?: I): RemoveBlackUserResp {
    return RemoveBlackUserResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveBlackUserResp>, I>>(_: I): RemoveBlackUserResp {
    const message = createBaseRemoveBlackUserResp();
    return message;
  }
};

function createBaseGetBlackUserListReq(): GetBlackUserListReq {
  return { page: undefined };
}

export const GetBlackUserListReq: MessageFns<GetBlackUserListReq> = {
  fromJSON(object: any): GetBlackUserListReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetBlackUserListReq>, I>>(base?: I): GetBlackUserListReq {
    return GetBlackUserListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlackUserListReq>, I>>(object: I): GetBlackUserListReq {
    const message = createBaseGetBlackUserListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetBlackUserListResp(): GetBlackUserListResp {
  return { page: undefined, black_uids: [] };
}

export const GetBlackUserListResp: MessageFns<GetBlackUserListResp> = {
  fromJSON(object: any): GetBlackUserListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      black_uids: globalThis.Array.isArray(object?.black_uids)
        ? object.black_uids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetBlackUserListResp>, I>>(base?: I): GetBlackUserListResp {
    return GetBlackUserListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBlackUserListResp>, I>>(object: I): GetBlackUserListResp {
    const message = createBaseGetBlackUserListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.black_uids = object.black_uids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckInUserBlackReq(): CheckInUserBlackReq {
  return { uids: [] };
}

export const CheckInUserBlackReq: MessageFns<CheckInUserBlackReq> = {
  fromJSON(object: any): CheckInUserBlackReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<CheckInUserBlackReq>, I>>(base?: I): CheckInUserBlackReq {
    return CheckInUserBlackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserBlackReq>, I>>(object: I): CheckInUserBlackReq {
    const message = createBaseCheckInUserBlackReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckInUserBlackResp(): CheckInUserBlackResp {
  return { black_map: {} };
}

export const CheckInUserBlackResp: MessageFns<CheckInUserBlackResp> = {
  fromJSON(object: any): CheckInUserBlackResp {
    return {
      black_map: isObject(object.black_map)
        ? Object.entries(object.black_map).reduce<{ [key: number]: boolean }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Boolean(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CheckInUserBlackResp>, I>>(base?: I): CheckInUserBlackResp {
    return CheckInUserBlackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserBlackResp>, I>>(object: I): CheckInUserBlackResp {
    const message = createBaseCheckInUserBlackResp();
    message.black_map = Object.entries(object.black_map ?? {}).reduce<{ [key: number]: boolean }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Boolean(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCheckInUserBlackResp_BlackMapEntry(): CheckInUserBlackResp_BlackMapEntry {
  return { key: 0, value: false };
}

export const CheckInUserBlackResp_BlackMapEntry: MessageFns<CheckInUserBlackResp_BlackMapEntry> = {
  fromJSON(object: any): CheckInUserBlackResp_BlackMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false
    };
  },

  create<I extends Exact<DeepPartial<CheckInUserBlackResp_BlackMapEntry>, I>>(
    base?: I
  ): CheckInUserBlackResp_BlackMapEntry {
    return CheckInUserBlackResp_BlackMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserBlackResp_BlackMapEntry>, I>>(
    object: I
  ): CheckInUserBlackResp_BlackMapEntry {
    const message = createBaseCheckInUserBlackResp_BlackMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? false;
    return message;
  }
};

function createBaseAddUserRoomBlackReq(): AddUserRoomBlackReq {
  return { black_room_id: 0 };
}

export const AddUserRoomBlackReq: MessageFns<AddUserRoomBlackReq> = {
  fromJSON(object: any): AddUserRoomBlackReq {
    return { black_room_id: isSet(object.black_room_id) ? globalThis.Number(object.black_room_id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddUserRoomBlackReq>, I>>(base?: I): AddUserRoomBlackReq {
    return AddUserRoomBlackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddUserRoomBlackReq>, I>>(object: I): AddUserRoomBlackReq {
    const message = createBaseAddUserRoomBlackReq();
    message.black_room_id = object.black_room_id ?? 0;
    return message;
  }
};

function createBaseAddUserRoomBlackResp(): AddUserRoomBlackResp {
  return {};
}

export const AddUserRoomBlackResp: MessageFns<AddUserRoomBlackResp> = {
  fromJSON(_: any): AddUserRoomBlackResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddUserRoomBlackResp>, I>>(base?: I): AddUserRoomBlackResp {
    return AddUserRoomBlackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddUserRoomBlackResp>, I>>(_: I): AddUserRoomBlackResp {
    const message = createBaseAddUserRoomBlackResp();
    return message;
  }
};

function createBaseRemoveUserRoomBlackReq(): RemoveUserRoomBlackReq {
  return { room_ids: [] };
}

export const RemoveUserRoomBlackReq: MessageFns<RemoveUserRoomBlackReq> = {
  fromJSON(object: any): RemoveUserRoomBlackReq {
    return {
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RemoveUserRoomBlackReq>, I>>(base?: I): RemoveUserRoomBlackReq {
    return RemoveUserRoomBlackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveUserRoomBlackReq>, I>>(object: I): RemoveUserRoomBlackReq {
    const message = createBaseRemoveUserRoomBlackReq();
    message.room_ids = object.room_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseRemoveUserRoomBlackResp(): RemoveUserRoomBlackResp {
  return {};
}

export const RemoveUserRoomBlackResp: MessageFns<RemoveUserRoomBlackResp> = {
  fromJSON(_: any): RemoveUserRoomBlackResp {
    return {};
  },

  create<I extends Exact<DeepPartial<RemoveUserRoomBlackResp>, I>>(base?: I): RemoveUserRoomBlackResp {
    return RemoveUserRoomBlackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveUserRoomBlackResp>, I>>(_: I): RemoveUserRoomBlackResp {
    const message = createBaseRemoveUserRoomBlackResp();
    return message;
  }
};

function createBaseGetUserRoomBlackListReq(): GetUserRoomBlackListReq {
  return { page: undefined };
}

export const GetUserRoomBlackListReq: MessageFns<GetUserRoomBlackListReq> = {
  fromJSON(object: any): GetUserRoomBlackListReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetUserRoomBlackListReq>, I>>(base?: I): GetUserRoomBlackListReq {
    return GetUserRoomBlackListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRoomBlackListReq>, I>>(object: I): GetUserRoomBlackListReq {
    const message = createBaseGetUserRoomBlackListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetUserRoomBlackListResp(): GetUserRoomBlackListResp {
  return { page: undefined, black_room_ids: [] };
}

export const GetUserRoomBlackListResp: MessageFns<GetUserRoomBlackListResp> = {
  fromJSON(object: any): GetUserRoomBlackListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      black_room_ids: globalThis.Array.isArray(object?.black_room_ids)
        ? object.black_room_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserRoomBlackListResp>, I>>(base?: I): GetUserRoomBlackListResp {
    return GetUserRoomBlackListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRoomBlackListResp>, I>>(object: I): GetUserRoomBlackListResp {
    const message = createBaseGetUserRoomBlackListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.black_room_ids = object.black_room_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckInUserRoomBlackReq(): CheckInUserRoomBlackReq {
  return { room_ids: [] };
}

export const CheckInUserRoomBlackReq: MessageFns<CheckInUserRoomBlackReq> = {
  fromJSON(object: any): CheckInUserRoomBlackReq {
    return {
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<CheckInUserRoomBlackReq>, I>>(base?: I): CheckInUserRoomBlackReq {
    return CheckInUserRoomBlackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserRoomBlackReq>, I>>(object: I): CheckInUserRoomBlackReq {
    const message = createBaseCheckInUserRoomBlackReq();
    message.room_ids = object.room_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckInUserRoomBlackResp(): CheckInUserRoomBlackResp {
  return { black_map: {} };
}

export const CheckInUserRoomBlackResp: MessageFns<CheckInUserRoomBlackResp> = {
  fromJSON(object: any): CheckInUserRoomBlackResp {
    return {
      black_map: isObject(object.black_map)
        ? Object.entries(object.black_map).reduce<{ [key: number]: boolean }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Boolean(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CheckInUserRoomBlackResp>, I>>(base?: I): CheckInUserRoomBlackResp {
    return CheckInUserRoomBlackResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserRoomBlackResp>, I>>(object: I): CheckInUserRoomBlackResp {
    const message = createBaseCheckInUserRoomBlackResp();
    message.black_map = Object.entries(object.black_map ?? {}).reduce<{ [key: number]: boolean }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Boolean(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCheckInUserRoomBlackResp_BlackMapEntry(): CheckInUserRoomBlackResp_BlackMapEntry {
  return { key: 0, value: false };
}

export const CheckInUserRoomBlackResp_BlackMapEntry: MessageFns<CheckInUserRoomBlackResp_BlackMapEntry> = {
  fromJSON(object: any): CheckInUserRoomBlackResp_BlackMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Boolean(object.value) : false
    };
  },

  create<I extends Exact<DeepPartial<CheckInUserRoomBlackResp_BlackMapEntry>, I>>(
    base?: I
  ): CheckInUserRoomBlackResp_BlackMapEntry {
    return CheckInUserRoomBlackResp_BlackMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckInUserRoomBlackResp_BlackMapEntry>, I>>(
    object: I
  ): CheckInUserRoomBlackResp_BlackMapEntry {
    const message = createBaseCheckInUserRoomBlackResp_BlackMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? false;
    return message;
  }
};

function createBaseCheckUserBlackStatusReq(): CheckUserBlackStatusReq {
  return { user_ids: [] };
}

export const CheckUserBlackStatusReq: MessageFns<CheckUserBlackStatusReq> = {
  fromJSON(object: any): CheckUserBlackStatusReq {
    return {
      user_ids: globalThis.Array.isArray(object?.user_ids) ? object.user_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<CheckUserBlackStatusReq>, I>>(base?: I): CheckUserBlackStatusReq {
    return CheckUserBlackStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckUserBlackStatusReq>, I>>(object: I): CheckUserBlackStatusReq {
    const message = createBaseCheckUserBlackStatusReq();
    message.user_ids = object.user_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseCheckUserBlackStatusResp(): CheckUserBlackStatusResp {
  return { status_map: {} };
}

export const CheckUserBlackStatusResp: MessageFns<CheckUserBlackStatusResp> = {
  fromJSON(object: any): CheckUserBlackStatusResp {
    return {
      status_map: isObject(object.status_map)
        ? Object.entries(object.status_map).reduce<{ [key: number]: UserBlackStatus }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = userBlackStatusFromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CheckUserBlackStatusResp>, I>>(base?: I): CheckUserBlackStatusResp {
    return CheckUserBlackStatusResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckUserBlackStatusResp>, I>>(object: I): CheckUserBlackStatusResp {
    const message = createBaseCheckUserBlackStatusResp();
    message.status_map = Object.entries(object.status_map ?? {}).reduce<{ [key: number]: UserBlackStatus }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = value as UserBlackStatus;
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCheckUserBlackStatusResp_StatusMapEntry(): CheckUserBlackStatusResp_StatusMapEntry {
  return { key: 0, value: 0 };
}

export const CheckUserBlackStatusResp_StatusMapEntry: MessageFns<CheckUserBlackStatusResp_StatusMapEntry> = {
  fromJSON(object: any): CheckUserBlackStatusResp_StatusMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? userBlackStatusFromJSON(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<CheckUserBlackStatusResp_StatusMapEntry>, I>>(
    base?: I
  ): CheckUserBlackStatusResp_StatusMapEntry {
    return CheckUserBlackStatusResp_StatusMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckUserBlackStatusResp_StatusMapEntry>, I>>(
    object: I
  ): CheckUserBlackStatusResp_StatusMapEntry {
    const message = createBaseCheckUserBlackStatusResp_StatusMapEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  }
};

/** 用户黑名单 */
export type UserBlackDefinition = typeof UserBlackDefinition;
export const UserBlackDefinition = {
  name: 'UserBlack',
  fullName: 'comm.api.relation.UserBlack',
  methods: {
    /** 添加用户黑名单 */
    addBlackUser: {
      name: 'AddBlackUser',
      requestType: AddBlackUserReq,
      requestStream: false,
      responseType: AddBlackUserResp,
      responseStream: false,
      options: {}
    },
    /** 移除用户黑名单 */
    removeBlackUser: {
      name: 'RemoveBlackUser',
      requestType: RemoveBlackUserReq,
      requestStream: false,
      responseType: RemoveBlackUserResp,
      responseStream: false,
      options: {}
    },
    /** 获取用户黑名单列表 */
    getBlackUserList: {
      name: 'GetBlackUserList',
      requestType: GetBlackUserListReq,
      requestStream: false,
      responseType: GetBlackUserListResp,
      responseStream: false,
      options: {}
    },
    /** 检查用户拉黑状态 */
    checkUserBlackStatus: {
      name: 'CheckUserBlackStatus',
      requestType: CheckUserBlackStatusReq,
      requestStream: false,
      responseType: CheckUserBlackStatusResp,
      responseStream: false,
      options: {}
    },
    /** 批量检查用户是否在黑名单中 */
    checkInUserBlack: {
      name: 'CheckInUserBlack',
      requestType: CheckInUserBlackReq,
      requestStream: false,
      responseType: CheckInUserBlackResp,
      responseStream: false,
      options: {}
    },
    /** 添加用户房间黑名单 */
    addUserRoomBlack: {
      name: 'AddUserRoomBlack',
      requestType: AddUserRoomBlackReq,
      requestStream: false,
      responseType: AddUserRoomBlackResp,
      responseStream: false,
      options: {}
    },
    /** 移除用户房间黑名单 */
    removeUserRoomBlack: {
      name: 'RemoveUserRoomBlack',
      requestType: RemoveUserRoomBlackReq,
      requestStream: false,
      responseType: RemoveUserRoomBlackResp,
      responseStream: false,
      options: {}
    },
    /** 获取用户房间黑名单列表 */
    getUserRoomBlackList: {
      name: 'GetUserRoomBlackList',
      requestType: GetUserRoomBlackListReq,
      requestStream: false,
      responseType: GetUserRoomBlackListResp,
      responseStream: false,
      options: {}
    },
    /** 批量检查是否在用户房间黑名单中 */
    checkInUserRoomBlack: {
      name: 'CheckInUserRoomBlack',
      requestType: CheckInUserRoomBlackReq,
      requestStream: false,
      responseType: CheckInUserRoomBlackResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
