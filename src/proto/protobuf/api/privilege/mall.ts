// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/mall.proto

/* eslint-disable */
import { Page } from '../common/common';
import { CurrencyType, currencyTypeFromJSON } from '../revenue/common';
import { CategoryAlias, categoryAliasFromJSON, GoodsScene, goodsSceneFromJSON } from './enum';

export const protobufPackage = 'api.privilege';

export interface MallGoodsPrice {
  /** 商品周期-秒 */
  period_secs: number;
  /** 商品周期展示 例如： 30 days */
  period_text: string;
  /** 货币类型 */
  currency_type: CurrencyType;
  /** 原价 */
  price: number;
  /** 折扣价 */
  discount_price: number;
}

/** 商城商品 */
export interface MallGoods {
  /** 商品ID */
  id: number;
  /** 商品分类ID */
  category_id: number;
  /** 子类ID */
  sub_category_id: number;
  /** 商品一级分类标识 */
  category_alias: CategoryAlias;
  /** 商品二级分类标识 */
  sub_category_alias: CategoryAlias;
  /** 商品名称 */
  name: string;
  /** 商品缩略图 */
  thumb: string;
  /** 商品图片 */
  image: string;
  /** 多媒体类型 */
  media_type: string;
  /** 多媒体地址 */
  media_url: string;
  /** 商品描述 */
  brief: string;
  /** 商品价格列表 */
  prices: MallGoodsPrice[];
}

/** 商城商品列表请求 */
export interface ListMallGoodsReq {
  page: Page | undefined;
  /** 商品场景 */
  scene: GoodsScene;
  /** 分类ID, 优先使用分类ID, 分类ID为空使用分类alias */
  category_id: number;
  /** 子类ID */
  sub_category_id: number;
  /** 分类标识 */
  category_alias: CategoryAlias;
  /** 二级分类标识 */
  sub_category_alias: CategoryAlias;
}

/** 商城商品列表响应 */
export interface ListMallGoodsRsp {
  page: Page | undefined;
  /** 商品列表 */
  goods: MallGoods[];
}

/** 购买请求 */
export interface BuyGoodsReq {
  /** 商品ID */
  goods_id: number;
  /** 购买数量 */
  num: number;
  /** 货币类型 */
  currency_type: CurrencyType;
  /** 支付金额 */
  amount: number;
  /** 用于业务透传 */
  extra_context: { [key: string]: string };
}

export interface BuyGoodsReq_ExtraContextEntry {
  key: string;
  value: string;
}

/** 购买响应 */
export interface BuyGoodsRsp {
  /** 背包ID */
  bag_id: number;
}

function createBaseMallGoodsPrice(): MallGoodsPrice {
  return { period_secs: 0, period_text: '', currency_type: 0, price: 0, discount_price: 0 };
}

export const MallGoodsPrice: MessageFns<MallGoodsPrice> = {
  fromJSON(object: any): MallGoodsPrice {
    return {
      period_secs: isSet(object.period_secs) ? globalThis.Number(object.period_secs) : 0,
      period_text: isSet(object.period_text) ? globalThis.String(object.period_text) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      discount_price: isSet(object.discount_price) ? globalThis.Number(object.discount_price) : 0
    };
  },

  create<I extends Exact<DeepPartial<MallGoodsPrice>, I>>(base?: I): MallGoodsPrice {
    return MallGoodsPrice.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallGoodsPrice>, I>>(object: I): MallGoodsPrice {
    const message = createBaseMallGoodsPrice();
    message.period_secs = object.period_secs ?? 0;
    message.period_text = object.period_text ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.price = object.price ?? 0;
    message.discount_price = object.discount_price ?? 0;
    return message;
  }
};

function createBaseMallGoods(): MallGoods {
  return {
    id: 0,
    category_id: 0,
    sub_category_id: 0,
    category_alias: 0,
    sub_category_alias: 0,
    name: '',
    thumb: '',
    image: '',
    media_type: '',
    media_url: '',
    brief: '',
    prices: []
  };
}

export const MallGoods: MessageFns<MallGoods> = {
  fromJSON(object: any): MallGoods {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.Number(object.sub_category_id) : 0,
      category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0,
      sub_category_alias: isSet(object.sub_category_alias) ? categoryAliasFromJSON(object.sub_category_alias) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      thumb: isSet(object.thumb) ? globalThis.String(object.thumb) : '',
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      media_type: isSet(object.media_type) ? globalThis.String(object.media_type) : '',
      media_url: isSet(object.media_url) ? globalThis.String(object.media_url) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      prices: globalThis.Array.isArray(object?.prices) ? object.prices.map((e: any) => MallGoodsPrice.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<MallGoods>, I>>(base?: I): MallGoods {
    return MallGoods.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MallGoods>, I>>(object: I): MallGoods {
    const message = createBaseMallGoods();
    message.id = object.id ?? 0;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.category_alias = object.category_alias ?? 0;
    message.sub_category_alias = object.sub_category_alias ?? 0;
    message.name = object.name ?? '';
    message.thumb = object.thumb ?? '';
    message.image = object.image ?? '';
    message.media_type = object.media_type ?? '';
    message.media_url = object.media_url ?? '';
    message.brief = object.brief ?? '';
    message.prices = object.prices?.map(e => MallGoodsPrice.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListMallGoodsReq(): ListMallGoodsReq {
  return { page: undefined, scene: 0, category_id: 0, sub_category_id: 0, category_alias: 0, sub_category_alias: 0 };
}

export const ListMallGoodsReq: MessageFns<ListMallGoodsReq> = {
  fromJSON(object: any): ListMallGoodsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      scene: isSet(object.scene) ? goodsSceneFromJSON(object.scene) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.Number(object.sub_category_id) : 0,
      category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0,
      sub_category_alias: isSet(object.sub_category_alias) ? categoryAliasFromJSON(object.sub_category_alias) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListMallGoodsReq>, I>>(base?: I): ListMallGoodsReq {
    return ListMallGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMallGoodsReq>, I>>(object: I): ListMallGoodsReq {
    const message = createBaseListMallGoodsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.scene = object.scene ?? 0;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.category_alias = object.category_alias ?? 0;
    message.sub_category_alias = object.sub_category_alias ?? 0;
    return message;
  }
};

function createBaseListMallGoodsRsp(): ListMallGoodsRsp {
  return { page: undefined, goods: [] };
}

export const ListMallGoodsRsp: MessageFns<ListMallGoodsRsp> = {
  fromJSON(object: any): ListMallGoodsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      goods: globalThis.Array.isArray(object?.goods) ? object.goods.map((e: any) => MallGoods.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListMallGoodsRsp>, I>>(base?: I): ListMallGoodsRsp {
    return ListMallGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListMallGoodsRsp>, I>>(object: I): ListMallGoodsRsp {
    const message = createBaseListMallGoodsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.goods = object.goods?.map(e => MallGoods.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBuyGoodsReq(): BuyGoodsReq {
  return { goods_id: 0, num: 0, currency_type: 0, amount: 0, extra_context: {} };
}

export const BuyGoodsReq: MessageFns<BuyGoodsReq> = {
  fromJSON(object: any): BuyGoodsReq {
    return {
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      extra_context: isObject(object.extra_context)
        ? Object.entries(object.extra_context).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BuyGoodsReq>, I>>(base?: I): BuyGoodsReq {
    return BuyGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BuyGoodsReq>, I>>(object: I): BuyGoodsReq {
    const message = createBaseBuyGoodsReq();
    message.goods_id = object.goods_id ?? 0;
    message.num = object.num ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.extra_context = Object.entries(object.extra_context ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBuyGoodsReq_ExtraContextEntry(): BuyGoodsReq_ExtraContextEntry {
  return { key: '', value: '' };
}

export const BuyGoodsReq_ExtraContextEntry: MessageFns<BuyGoodsReq_ExtraContextEntry> = {
  fromJSON(object: any): BuyGoodsReq_ExtraContextEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<BuyGoodsReq_ExtraContextEntry>, I>>(base?: I): BuyGoodsReq_ExtraContextEntry {
    return BuyGoodsReq_ExtraContextEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BuyGoodsReq_ExtraContextEntry>, I>>(
    object: I
  ): BuyGoodsReq_ExtraContextEntry {
    const message = createBaseBuyGoodsReq_ExtraContextEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseBuyGoodsRsp(): BuyGoodsRsp {
  return { bag_id: 0 };
}

export const BuyGoodsRsp: MessageFns<BuyGoodsRsp> = {
  fromJSON(object: any): BuyGoodsRsp {
    return { bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0 };
  },

  create<I extends Exact<DeepPartial<BuyGoodsRsp>, I>>(base?: I): BuyGoodsRsp {
    return BuyGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BuyGoodsRsp>, I>>(object: I): BuyGoodsRsp {
    const message = createBaseBuyGoodsRsp();
    message.bag_id = object.bag_id ?? 0;
    return message;
  }
};

/** ServiceName: privilege-api */
export type MallApiDefinition = typeof MallApiDefinition;
export const MallApiDefinition = {
  name: 'MallApi',
  fullName: 'api.privilege.MallApi',
  methods: {
    /** 商城商品列表 */
    listMallGoods: {
      name: 'ListMallGoods',
      requestType: ListMallGoodsReq,
      requestStream: false,
      responseType: ListMallGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 购买商品 */
    buyGoods: {
      name: 'BuyGoods',
      requestType: BuyGoodsReq,
      requestStream: false,
      responseType: BuyGoodsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
