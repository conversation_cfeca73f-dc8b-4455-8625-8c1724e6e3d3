// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/privilege.proto

/* eslint-disable */
import { CategoryAlias, categoryAliasFromJSON } from './enum';

export const protobufPackage = 'api.privilege';

/** BagPrivilege 背包权益 */
export interface BagPrivilege {
  /** 背包ID */
  id: number;
  /** 名称 */
  name: string;
  /** 缩略图 */
  thumb: string;
  /** 静图 */
  image: string;
  /** 动效类型 */
  media_type: string;
  /** 动效地址 */
  media_url: string;
  /** 过期时间 */
  expired_at: number;
  /** 商品ID */
  goods_id: number;
  /** 分类ID */
  category_id: number;
  /** 子分类ID */
  sub_category_id: number;
  /** 一级分类标识 */
  category_alias: CategoryAlias;
  /** 二级分类标识 */
  sub_category_alias: CategoryAlias;
  /** 用户UID */
  uid: number;
  /** 额外信息 */
  extra: { [key: string]: string };
}

export interface BagPrivilege_ExtraEntry {
  key: string;
  value: string;
}

export interface MultiBagPrivilege {
  /** 权益map */
  privileges: { [key: number]: BagPrivilege };
}

export interface MultiBagPrivilege_PrivilegesEntry {
  key: number;
  value: BagPrivilege | undefined;
}

function createBaseBagPrivilege(): BagPrivilege {
  return {
    id: 0,
    name: '',
    thumb: '',
    image: '',
    media_type: '',
    media_url: '',
    expired_at: 0,
    goods_id: 0,
    category_id: 0,
    sub_category_id: 0,
    category_alias: 0,
    sub_category_alias: 0,
    uid: 0,
    extra: {}
  };
}

export const BagPrivilege: MessageFns<BagPrivilege> = {
  fromJSON(object: any): BagPrivilege {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      thumb: isSet(object.thumb) ? globalThis.String(object.thumb) : '',
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      media_type: isSet(object.media_type) ? globalThis.String(object.media_type) : '',
      media_url: isSet(object.media_url) ? globalThis.String(object.media_url) : '',
      expired_at: isSet(object.expired_at) ? globalThis.Number(object.expired_at) : 0,
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.Number(object.sub_category_id) : 0,
      category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0,
      sub_category_alias: isSet(object.sub_category_alias) ? categoryAliasFromJSON(object.sub_category_alias) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      extra: isObject(object.extra)
        ? Object.entries(object.extra).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BagPrivilege>, I>>(base?: I): BagPrivilege {
    return BagPrivilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagPrivilege>, I>>(object: I): BagPrivilege {
    const message = createBaseBagPrivilege();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.thumb = object.thumb ?? '';
    message.image = object.image ?? '';
    message.media_type = object.media_type ?? '';
    message.media_url = object.media_url ?? '';
    message.expired_at = object.expired_at ?? 0;
    message.goods_id = object.goods_id ?? 0;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.category_alias = object.category_alias ?? 0;
    message.sub_category_alias = object.sub_category_alias ?? 0;
    message.uid = object.uid ?? 0;
    message.extra = Object.entries(object.extra ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBagPrivilege_ExtraEntry(): BagPrivilege_ExtraEntry {
  return { key: '', value: '' };
}

export const BagPrivilege_ExtraEntry: MessageFns<BagPrivilege_ExtraEntry> = {
  fromJSON(object: any): BagPrivilege_ExtraEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<BagPrivilege_ExtraEntry>, I>>(base?: I): BagPrivilege_ExtraEntry {
    return BagPrivilege_ExtraEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagPrivilege_ExtraEntry>, I>>(object: I): BagPrivilege_ExtraEntry {
    const message = createBaseBagPrivilege_ExtraEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseMultiBagPrivilege(): MultiBagPrivilege {
  return { privileges: {} };
}

export const MultiBagPrivilege: MessageFns<MultiBagPrivilege> = {
  fromJSON(object: any): MultiBagPrivilege {
    return {
      privileges: isObject(object.privileges)
        ? Object.entries(object.privileges).reduce<{ [key: number]: BagPrivilege }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = BagPrivilege.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<MultiBagPrivilege>, I>>(base?: I): MultiBagPrivilege {
    return MultiBagPrivilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiBagPrivilege>, I>>(object: I): MultiBagPrivilege {
    const message = createBaseMultiBagPrivilege();
    message.privileges = Object.entries(object.privileges ?? {}).reduce<{ [key: number]: BagPrivilege }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = BagPrivilege.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseMultiBagPrivilege_PrivilegesEntry(): MultiBagPrivilege_PrivilegesEntry {
  return { key: 0, value: undefined };
}

export const MultiBagPrivilege_PrivilegesEntry: MessageFns<MultiBagPrivilege_PrivilegesEntry> = {
  fromJSON(object: any): MultiBagPrivilege_PrivilegesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? BagPrivilege.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MultiBagPrivilege_PrivilegesEntry>, I>>(
    base?: I
  ): MultiBagPrivilege_PrivilegesEntry {
    return MultiBagPrivilege_PrivilegesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MultiBagPrivilege_PrivilegesEntry>, I>>(
    object: I
  ): MultiBagPrivilege_PrivilegesEntry {
    const message = createBaseMultiBagPrivilege_PrivilegesEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? BagPrivilege.fromPartial(object.value) : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
