// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/errcode.proto

/* eslint-disable */

export const protobufPackage = 'api.privilege';

export enum ErrCode {
  ErrCodeNone = 0,
  /** ErrCodeSingleCoinValuesNotMatch - 道具价值金币数不匹配 */
  ErrCodeSingleCoinValuesNotMatch = 40001,
  /** ErrBagGoodsNumNotEnough - 道具数量不足 */
  ErrBagGoodsNumNotEnough = 40002,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ErrCodeNone':
      return ErrCode.ErrCodeNone;
    case 40001:
    case 'ErrCodeSingleCoinValuesNotMatch':
      return ErrCode.ErrCodeSingleCoinValuesNotMatch;
    case 40002:
    case 'ErrBagGoodsNumNotEnough':
      return ErrCode.ErrBagGoodsNumNotEnough;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}
