// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/v2/resource_package.proto

/* eslint-disable */
import { GiftInfo } from '../../revenue/gift';
import { Resource } from './privilege';

export const protobufPackage = 'api.privilege.v2';

/** smicro:spath=gitit.cc/social/components-service/social-privilege/biz/respkg/handler */

export enum ResourceType {
  /** RESOURCE_TYPE_NONE - 无意义 */
  RESOURCE_TYPE_NONE = 0,
  /** RESOURCE_TYPE_COIN - 金币 */
  RESOURCE_TYPE_COIN = 1,
  /** RESOURCE_TYPE_GIFT - 礼物 */
  RESOURCE_TYPE_GIFT = 2,
  /** RESOURCE_TYPE_HEADWEAR - 头像框 */
  RESOURCE_TYPE_HEADWEAR = 3,
  /** RESOURCE_TYPE_VEHICLE - 座驾 */
  RESOURCE_TYPE_VEHICLE = 4,
  /** RESOURCE_TYPE_BUBBLE - 公屏气泡 */
  RESOURCE_TYPE_BUBBLE = 5,
  /** RESOURCE_TYPE_ROOM_BG - 房间背景 */
  RESOURCE_TYPE_ROOM_BG = 6,
  /** RESOURCE_TYPE_MIC_APERTURE - 麦位光圈 */
  RESOURCE_TYPE_MIC_APERTURE = 7,
  /** RESOURCE_TYPE_HOME_ACTION - 主页动效 */
  RESOURCE_TYPE_HOME_ACTION = 8,
  /** RESOURCE_TYPE_MEDAL - 勋章 */
  RESOURCE_TYPE_MEDAL = 9,
  /** RESOURCE_TYPE_MINI_CARD_BG - mini资料卡背景 */
  RESOURCE_TYPE_MINI_CARD_BG = 10,
  /** RESOURCE_TYPE_BAY_WINDOW - 进房飘窗 */
  RESOURCE_TYPE_BAY_WINDOW = 11,
  /** RESOURCE_TYPE_ROOM_LIST_BG - 房间列表背景 */
  RESOURCE_TYPE_ROOM_LIST_BG = 12,
  /** RESOURCE_TYPE_NICKNAME_COLOR - 昵称颜色 */
  RESOURCE_TYPE_NICKNAME_COLOR = 13,
  UNRECOGNIZED = -1
}

export function resourceTypeFromJSON(object: any): ResourceType {
  switch (object) {
    case 0:
    case 'RESOURCE_TYPE_NONE':
      return ResourceType.RESOURCE_TYPE_NONE;
    case 1:
    case 'RESOURCE_TYPE_COIN':
      return ResourceType.RESOURCE_TYPE_COIN;
    case 2:
    case 'RESOURCE_TYPE_GIFT':
      return ResourceType.RESOURCE_TYPE_GIFT;
    case 3:
    case 'RESOURCE_TYPE_HEADWEAR':
      return ResourceType.RESOURCE_TYPE_HEADWEAR;
    case 4:
    case 'RESOURCE_TYPE_VEHICLE':
      return ResourceType.RESOURCE_TYPE_VEHICLE;
    case 5:
    case 'RESOURCE_TYPE_BUBBLE':
      return ResourceType.RESOURCE_TYPE_BUBBLE;
    case 6:
    case 'RESOURCE_TYPE_ROOM_BG':
      return ResourceType.RESOURCE_TYPE_ROOM_BG;
    case 7:
    case 'RESOURCE_TYPE_MIC_APERTURE':
      return ResourceType.RESOURCE_TYPE_MIC_APERTURE;
    case 8:
    case 'RESOURCE_TYPE_HOME_ACTION':
      return ResourceType.RESOURCE_TYPE_HOME_ACTION;
    case 9:
    case 'RESOURCE_TYPE_MEDAL':
      return ResourceType.RESOURCE_TYPE_MEDAL;
    case 10:
    case 'RESOURCE_TYPE_MINI_CARD_BG':
      return ResourceType.RESOURCE_TYPE_MINI_CARD_BG;
    case 11:
    case 'RESOURCE_TYPE_BAY_WINDOW':
      return ResourceType.RESOURCE_TYPE_BAY_WINDOW;
    case 12:
    case 'RESOURCE_TYPE_ROOM_LIST_BG':
      return ResourceType.RESOURCE_TYPE_ROOM_LIST_BG;
    case 13:
    case 'RESOURCE_TYPE_NICKNAME_COLOR':
      return ResourceType.RESOURCE_TYPE_NICKNAME_COLOR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourceType.UNRECOGNIZED;
  }
}

/** 资源包 */
export interface ResourcePackageInfo {
  /** id */
  id: number;
  /** 资源包名称 */
  name: string;
  /** 资源包图标URL */
  icon_url: string;
}

/** 资源包详情 */
export interface ResourcePackageDetailInfo {
  /** id */
  id: number;
  /** 资源包id */
  package_id: number;
  /** 资源类型 */
  resource_type: ResourceType;
  /** 资源id */
  resource_id: number;
  /** 资源图片url */
  icon_url: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
  /** 资源名称，多语言, key 的取值有 zh / en / ar / tr ... */
  i18n_name: { [key: string]: string };
}

export interface ResourcePackageDetailInfo_I18nNameEntry {
  key: string;
  value: string;
}

export interface ResourcePackageAndDetail {
  resource_package_info: ResourcePackageInfo | undefined;
  resource_package_detail_infos: ResourcePackageDetailInfo[];
}

export interface BatchGetResourcePackageReq {
  /** 资源包ids */
  resource_package_ids: number[];
}

export interface BatchGetResourcePackageRsp {
  /** map<资源包id, 资源包> */
  resource_packages: { [key: number]: ResourcePackageAndDetail };
  resources: { [key: number]: Resource };
  giftInfos: { [key: number]: GiftInfo };
}

export interface BatchGetResourcePackageRsp_ResourcePackagesEntry {
  key: number;
  value: ResourcePackageAndDetail | undefined;
}

export interface BatchGetResourcePackageRsp_ResourcesEntry {
  key: number;
  value: Resource | undefined;
}

export interface BatchGetResourcePackageRsp_GiftInfosEntry {
  key: number;
  value: GiftInfo | undefined;
}

function createBaseResourcePackageInfo(): ResourcePackageInfo {
  return { id: 0, name: '', icon_url: '' };
}

export const ResourcePackageInfo: MessageFns<ResourcePackageInfo> = {
  fromJSON(object: any): ResourcePackageInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageInfo>, I>>(base?: I): ResourcePackageInfo {
    return ResourcePackageInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageInfo>, I>>(object: I): ResourcePackageInfo {
    const message = createBaseResourcePackageInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.icon_url = object.icon_url ?? '';
    return message;
  }
};

function createBaseResourcePackageDetailInfo(): ResourcePackageDetailInfo {
  return {
    id: 0,
    package_id: 0,
    resource_type: 0,
    resource_id: 0,
    icon_url: '',
    amount: 0,
    effective_time: 0,
    i18n_name: {}
  };
}

export const ResourcePackageDetailInfo: MessageFns<ResourcePackageDetailInfo> = {
  fromJSON(object: any): ResourcePackageDetailInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      resource_type: isSet(object.resource_type) ? resourceTypeFromJSON(object.resource_type) : 0,
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0,
      i18n_name: isObject(object.i18n_name)
        ? Object.entries(object.i18n_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDetailInfo>, I>>(base?: I): ResourcePackageDetailInfo {
    return ResourcePackageDetailInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDetailInfo>, I>>(object: I): ResourcePackageDetailInfo {
    const message = createBaseResourcePackageDetailInfo();
    message.id = object.id ?? 0;
    message.package_id = object.package_id ?? 0;
    message.resource_type = object.resource_type ?? 0;
    message.resource_id = object.resource_id ?? 0;
    message.icon_url = object.icon_url ?? '';
    message.amount = object.amount ?? 0;
    message.effective_time = object.effective_time ?? 0;
    message.i18n_name = Object.entries(object.i18n_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseResourcePackageDetailInfo_I18nNameEntry(): ResourcePackageDetailInfo_I18nNameEntry {
  return { key: '', value: '' };
}

export const ResourcePackageDetailInfo_I18nNameEntry: MessageFns<ResourcePackageDetailInfo_I18nNameEntry> = {
  fromJSON(object: any): ResourcePackageDetailInfo_I18nNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDetailInfo_I18nNameEntry>, I>>(
    base?: I
  ): ResourcePackageDetailInfo_I18nNameEntry {
    return ResourcePackageDetailInfo_I18nNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDetailInfo_I18nNameEntry>, I>>(
    object: I
  ): ResourcePackageDetailInfo_I18nNameEntry {
    const message = createBaseResourcePackageDetailInfo_I18nNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseResourcePackageAndDetail(): ResourcePackageAndDetail {
  return { resource_package_info: undefined, resource_package_detail_infos: [] };
}

export const ResourcePackageAndDetail: MessageFns<ResourcePackageAndDetail> = {
  fromJSON(object: any): ResourcePackageAndDetail {
    return {
      resource_package_info: isSet(object.resource_package_info)
        ? ResourcePackageInfo.fromJSON(object.resource_package_info)
        : undefined,
      resource_package_detail_infos: globalThis.Array.isArray(object?.resource_package_detail_infos)
        ? object.resource_package_detail_infos.map((e: any) => ResourcePackageDetailInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageAndDetail>, I>>(base?: I): ResourcePackageAndDetail {
    return ResourcePackageAndDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageAndDetail>, I>>(object: I): ResourcePackageAndDetail {
    const message = createBaseResourcePackageAndDetail();
    message.resource_package_info =
      object.resource_package_info !== undefined && object.resource_package_info !== null
        ? ResourcePackageInfo.fromPartial(object.resource_package_info)
        : undefined;
    message.resource_package_detail_infos =
      object.resource_package_detail_infos?.map(e => ResourcePackageDetailInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetResourcePackageReq(): BatchGetResourcePackageReq {
  return { resource_package_ids: [] };
}

export const BatchGetResourcePackageReq: MessageFns<BatchGetResourcePackageReq> = {
  fromJSON(object: any): BatchGetResourcePackageReq {
    return {
      resource_package_ids: globalThis.Array.isArray(object?.resource_package_ids)
        ? object.resource_package_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetResourcePackageReq>, I>>(base?: I): BatchGetResourcePackageReq {
    return BatchGetResourcePackageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetResourcePackageReq>, I>>(object: I): BatchGetResourcePackageReq {
    const message = createBaseBatchGetResourcePackageReq();
    message.resource_package_ids = object.resource_package_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetResourcePackageRsp(): BatchGetResourcePackageRsp {
  return { resource_packages: {}, resources: {}, giftInfos: {} };
}

export const BatchGetResourcePackageRsp: MessageFns<BatchGetResourcePackageRsp> = {
  fromJSON(object: any): BatchGetResourcePackageRsp {
    return {
      resource_packages: isObject(object.resource_packages)
        ? Object.entries(object.resource_packages).reduce<{ [key: number]: ResourcePackageAndDetail }>(
            (acc, [key, value]) => {
              acc[globalThis.Number(key)] = ResourcePackageAndDetail.fromJSON(value);
              return acc;
            },
            {}
          )
        : {},
      resources: isObject(object.resources)
        ? Object.entries(object.resources).reduce<{ [key: number]: Resource }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Resource.fromJSON(value);
            return acc;
          }, {})
        : {},
      giftInfos: isObject(object.giftInfos)
        ? Object.entries(object.giftInfos).reduce<{ [key: number]: GiftInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = GiftInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetResourcePackageRsp>, I>>(base?: I): BatchGetResourcePackageRsp {
    return BatchGetResourcePackageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetResourcePackageRsp>, I>>(object: I): BatchGetResourcePackageRsp {
    const message = createBaseBatchGetResourcePackageRsp();
    message.resource_packages = Object.entries(object.resource_packages ?? {}).reduce<{
      [key: number]: ResourcePackageAndDetail;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = ResourcePackageAndDetail.fromPartial(value);
      }
      return acc;
    }, {});
    message.resources = Object.entries(object.resources ?? {}).reduce<{ [key: number]: Resource }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = Resource.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.giftInfos = Object.entries(object.giftInfos ?? {}).reduce<{ [key: number]: GiftInfo }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = GiftInfo.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchGetResourcePackageRsp_ResourcePackagesEntry(): BatchGetResourcePackageRsp_ResourcePackagesEntry {
  return { key: 0, value: undefined };
}

export const BatchGetResourcePackageRsp_ResourcePackagesEntry: MessageFns<BatchGetResourcePackageRsp_ResourcePackagesEntry> =
  {
    fromJSON(object: any): BatchGetResourcePackageRsp_ResourcePackagesEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? ResourcePackageAndDetail.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_ResourcePackagesEntry>, I>>(
      base?: I
    ): BatchGetResourcePackageRsp_ResourcePackagesEntry {
      return BatchGetResourcePackageRsp_ResourcePackagesEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_ResourcePackagesEntry>, I>>(
      object: I
    ): BatchGetResourcePackageRsp_ResourcePackagesEntry {
      const message = createBaseBatchGetResourcePackageRsp_ResourcePackagesEntry();
      message.key = object.key ?? 0;
      message.value =
        object.value !== undefined && object.value !== null
          ? ResourcePackageAndDetail.fromPartial(object.value)
          : undefined;
      return message;
    }
  };

function createBaseBatchGetResourcePackageRsp_ResourcesEntry(): BatchGetResourcePackageRsp_ResourcesEntry {
  return { key: 0, value: undefined };
}

export const BatchGetResourcePackageRsp_ResourcesEntry: MessageFns<BatchGetResourcePackageRsp_ResourcesEntry> = {
  fromJSON(object: any): BatchGetResourcePackageRsp_ResourcesEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Resource.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_ResourcesEntry>, I>>(
    base?: I
  ): BatchGetResourcePackageRsp_ResourcesEntry {
    return BatchGetResourcePackageRsp_ResourcesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_ResourcesEntry>, I>>(
    object: I
  ): BatchGetResourcePackageRsp_ResourcesEntry {
    const message = createBaseBatchGetResourcePackageRsp_ResourcesEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? Resource.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseBatchGetResourcePackageRsp_GiftInfosEntry(): BatchGetResourcePackageRsp_GiftInfosEntry {
  return { key: 0, value: undefined };
}

export const BatchGetResourcePackageRsp_GiftInfosEntry: MessageFns<BatchGetResourcePackageRsp_GiftInfosEntry> = {
  fromJSON(object: any): BatchGetResourcePackageRsp_GiftInfosEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? GiftInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_GiftInfosEntry>, I>>(
    base?: I
  ): BatchGetResourcePackageRsp_GiftInfosEntry {
    return BatchGetResourcePackageRsp_GiftInfosEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetResourcePackageRsp_GiftInfosEntry>, I>>(
    object: I
  ): BatchGetResourcePackageRsp_GiftInfosEntry {
    const message = createBaseBatchGetResourcePackageRsp_GiftInfosEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? GiftInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

/**
 * 资源包接口
 * ServiceName: privilege-api
 */
export type ResourcePackageApiDefinition = typeof ResourcePackageApiDefinition;
export const ResourcePackageApiDefinition = {
  name: 'ResourcePackageApi',
  fullName: 'api.privilege.v2.ResourcePackageApi',
  methods: {
    /** 批量获资源包 */
    batchGetResourcePackage: {
      name: 'BatchGetResourcePackage',
      requestType: BatchGetResourcePackageReq,
      requestStream: false,
      responseType: BatchGetResourcePackageRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
