// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/v2/privilege.proto

/* eslint-disable */
import { Page } from '../../common/common';

export const protobufPackage = 'api.privilege.v2';

/** smicro:spath=gitit.cc/social/components-service/social-privilege/biz/bag/handler */

/** 素材类型 */
export enum AssetType {
  /** ASSET_TYPE_NONE - 无意义 */
  ASSET_TYPE_NONE = 0,
  /** ASSET_TYPE_IMG - 图片 png/jpg/webp... */
  ASSET_TYPE_IMG = 2,
  /** ASSET_TYPE_MP4 - mp4 */
  ASSET_TYPE_MP4 = 4,
  /** ASSET_TYPE_SVGA - svga */
  ASSET_TYPE_SVGA = 5,
  /** ASSET_TYPE_GIF - gif */
  ASSET_TYPE_GIF = 6,
  UNRECOGNIZED = -1
}

export function assetTypeFromJSON(object: any): AssetType {
  switch (object) {
    case 0:
    case 'ASSET_TYPE_NONE':
      return AssetType.ASSET_TYPE_NONE;
    case 2:
    case 'ASSET_TYPE_IMG':
      return AssetType.ASSET_TYPE_IMG;
    case 4:
    case 'ASSET_TYPE_MP4':
      return AssetType.ASSET_TYPE_MP4;
    case 5:
    case 'ASSET_TYPE_SVGA':
      return AssetType.ASSET_TYPE_SVGA;
    case 6:
    case 'ASSET_TYPE_GIF':
      return AssetType.ASSET_TYPE_GIF;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AssetType.UNRECOGNIZED;
  }
}

/** 素材显示区域 */
export enum AssetArea {
  /** ASSET_AREA_NONE - 无意义 */
  ASSET_AREA_NONE = 0,
  /** ASSET_AREA_HALF - 半屏 */
  ASSET_AREA_HALF = 1,
  /** ASSET_AREA_FULL - 全屏 */
  ASSET_AREA_FULL = 2,
  UNRECOGNIZED = -1
}

export function assetAreaFromJSON(object: any): AssetArea {
  switch (object) {
    case 0:
    case 'ASSET_AREA_NONE':
      return AssetArea.ASSET_AREA_NONE;
    case 1:
    case 'ASSET_AREA_HALF':
      return AssetArea.ASSET_AREA_HALF;
    case 2:
    case 'ASSET_AREA_FULL':
      return AssetArea.ASSET_AREA_FULL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AssetArea.UNRECOGNIZED;
  }
}

/** 生效类型 */
export enum EffectType {
  /** EFFECT_TYPE_NONE - 无意义 */
  EFFECT_TYPE_NONE = 0,
  /** EFFECT_TYPE_BY_DURATION - 按时长 */
  EFFECT_TYPE_BY_DURATION = 1,
  /** EFFECT_TYPE_BY_NUM - 按数量 */
  EFFECT_TYPE_BY_NUM = 2,
  UNRECOGNIZED = -1
}

export function effectTypeFromJSON(object: any): EffectType {
  switch (object) {
    case 0:
    case 'EFFECT_TYPE_NONE':
      return EffectType.EFFECT_TYPE_NONE;
    case 1:
    case 'EFFECT_TYPE_BY_DURATION':
      return EffectType.EFFECT_TYPE_BY_DURATION;
    case 2:
    case 'EFFECT_TYPE_BY_NUM':
      return EffectType.EFFECT_TYPE_BY_NUM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return EffectType.UNRECOGNIZED;
  }
}

/** 归属类型, 例如: 用户 / 房间 / ... */
export enum TargetType {
  /** TARGET_TYPE_NONE - 无意义 */
  TARGET_TYPE_NONE = 0,
  /** TARGET_TYPE_USER - 用户 */
  TARGET_TYPE_USER = 1,
  /** TARGET_TYPE_ROOM - 房间 */
  TARGET_TYPE_ROOM = 2,
  UNRECOGNIZED = -1
}

export function targetTypeFromJSON(object: any): TargetType {
  switch (object) {
    case 0:
    case 'TARGET_TYPE_NONE':
      return TargetType.TARGET_TYPE_NONE;
    case 1:
    case 'TARGET_TYPE_USER':
      return TargetType.TARGET_TYPE_USER;
    case 2:
    case 'TARGET_TYPE_ROOM':
      return TargetType.TARGET_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TargetType.UNRECOGNIZED;
  }
}

/** 特权资源使用策略 */
export enum UseStrategy {
  /** USE_STRATEGY_NONE - 无意义 */
  USE_STRATEGY_NONE = 0,
  /** USE_STRATEGY_UNIQUE_BY_CATEGORY - 一级分类唯一穿戴 */
  USE_STRATEGY_UNIQUE_BY_CATEGORY = 1,
  /** USE_STRATEGY_UNIQUE_BY_SUB_CATEGORY - 二级分类下唯一穿戴 */
  USE_STRATEGY_UNIQUE_BY_SUB_CATEGORY = 2,
  UNRECOGNIZED = -1
}

export function useStrategyFromJSON(object: any): UseStrategy {
  switch (object) {
    case 0:
    case 'USE_STRATEGY_NONE':
      return UseStrategy.USE_STRATEGY_NONE;
    case 1:
    case 'USE_STRATEGY_UNIQUE_BY_CATEGORY':
      return UseStrategy.USE_STRATEGY_UNIQUE_BY_CATEGORY;
    case 2:
    case 'USE_STRATEGY_UNIQUE_BY_SUB_CATEGORY':
      return UseStrategy.USE_STRATEGY_UNIQUE_BY_SUB_CATEGORY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UseStrategy.UNRECOGNIZED;
  }
}

/** 特权分类标识 */
export enum CategoryID {
  /** CATEGORY_ID_NONE - 无意义 */
  CATEGORY_ID_NONE = 0,
  /** CATEGORY_ID_HEADWEAR - 一级分类 */
  CATEGORY_ID_HEADWEAR = 10,
  /** CATEGORY_ID_VEHICLE - 座驾 */
  CATEGORY_ID_VEHICLE = 11,
  /** CATEGORY_ID_SCREEN_BUBBLE - 公屏气泡 */
  CATEGORY_ID_SCREEN_BUBBLE = 12,
  /** CATEGORY_ID_MIC_APERTURE - 麦位光圈 */
  CATEGORY_ID_MIC_APERTURE = 13,
  /** CATEGORY_ID_HOME_ACTION - 主页动效 */
  CATEGORY_ID_HOME_ACTION = 14,
  /** CATEGORY_ID_MEDAL - 勋章 */
  CATEGORY_ID_MEDAL = 15,
  /** CATEGORY_ID_ROOM_BACKGROUND - 房间背景 */
  CATEGORY_ID_ROOM_BACKGROUND = 16,
  /** CATEGORY_ID_MINI_CARD_BACKGROUND - mini资料卡背景 */
  CATEGORY_ID_MINI_CARD_BACKGROUND = 17,
  /** CATEGORY_ID_ENTER_ROOM_BAY_WINDOW - 进房飘窗 */
  CATEGORY_ID_ENTER_ROOM_BAY_WINDOW = 18,
  /** CATEGORY_ID_ROOM_LIST_BACKGROUND - 房间列表背景 */
  CATEGORY_ID_ROOM_LIST_BACKGROUND = 19,
  /** CATEGORY_ID_PRETTY_NUMBER - 用户靓号 */
  CATEGORY_ID_PRETTY_NUMBER = 20,
  /** CATEGORY_ID_PRETTY_NUMBER_ROOM - 房间靓号 */
  CATEGORY_ID_PRETTY_NUMBER_ROOM = 21,
  /** CATEGORY_ID_NICKNAME_COLOR - 昵称颜色 */
  CATEGORY_ID_NICKNAME_COLOR = 22,
  /** CATEGORY_ID_VIP - vip 等级 */
  CATEGORY_ID_VIP = 23,
  /** CATEGORY_ID_MEDAL_NORMAL - 子分类标识 */
  CATEGORY_ID_MEDAL_NORMAL = 15000,
  /** CATEGORY_ID_MEDAL_ACHIEVEMENT - 成就勋章 */
  CATEGORY_ID_MEDAL_ACHIEVEMENT = 15010,
  /** CATEGORY_ID_ROOM_BACKGROUND_OFFICIAL - 官方房间背景 */
  CATEGORY_ID_ROOM_BACKGROUND_OFFICIAL = 16000,
  /** CATEGORY_ID_ROOM_BACKGROUND_NOBLE - 贵族房间背景 */
  CATEGORY_ID_ROOM_BACKGROUND_NOBLE = 16010,
  UNRECOGNIZED = -1
}

export function categoryIDFromJSON(object: any): CategoryID {
  switch (object) {
    case 0:
    case 'CATEGORY_ID_NONE':
      return CategoryID.CATEGORY_ID_NONE;
    case 10:
    case 'CATEGORY_ID_HEADWEAR':
      return CategoryID.CATEGORY_ID_HEADWEAR;
    case 11:
    case 'CATEGORY_ID_VEHICLE':
      return CategoryID.CATEGORY_ID_VEHICLE;
    case 12:
    case 'CATEGORY_ID_SCREEN_BUBBLE':
      return CategoryID.CATEGORY_ID_SCREEN_BUBBLE;
    case 13:
    case 'CATEGORY_ID_MIC_APERTURE':
      return CategoryID.CATEGORY_ID_MIC_APERTURE;
    case 14:
    case 'CATEGORY_ID_HOME_ACTION':
      return CategoryID.CATEGORY_ID_HOME_ACTION;
    case 15:
    case 'CATEGORY_ID_MEDAL':
      return CategoryID.CATEGORY_ID_MEDAL;
    case 16:
    case 'CATEGORY_ID_ROOM_BACKGROUND':
      return CategoryID.CATEGORY_ID_ROOM_BACKGROUND;
    case 17:
    case 'CATEGORY_ID_MINI_CARD_BACKGROUND':
      return CategoryID.CATEGORY_ID_MINI_CARD_BACKGROUND;
    case 18:
    case 'CATEGORY_ID_ENTER_ROOM_BAY_WINDOW':
      return CategoryID.CATEGORY_ID_ENTER_ROOM_BAY_WINDOW;
    case 19:
    case 'CATEGORY_ID_ROOM_LIST_BACKGROUND':
      return CategoryID.CATEGORY_ID_ROOM_LIST_BACKGROUND;
    case 20:
    case 'CATEGORY_ID_PRETTY_NUMBER':
      return CategoryID.CATEGORY_ID_PRETTY_NUMBER;
    case 21:
    case 'CATEGORY_ID_PRETTY_NUMBER_ROOM':
      return CategoryID.CATEGORY_ID_PRETTY_NUMBER_ROOM;
    case 22:
    case 'CATEGORY_ID_NICKNAME_COLOR':
      return CategoryID.CATEGORY_ID_NICKNAME_COLOR;
    case 23:
    case 'CATEGORY_ID_VIP':
      return CategoryID.CATEGORY_ID_VIP;
    case 15000:
    case 'CATEGORY_ID_MEDAL_NORMAL':
      return CategoryID.CATEGORY_ID_MEDAL_NORMAL;
    case 15010:
    case 'CATEGORY_ID_MEDAL_ACHIEVEMENT':
      return CategoryID.CATEGORY_ID_MEDAL_ACHIEVEMENT;
    case 16000:
    case 'CATEGORY_ID_ROOM_BACKGROUND_OFFICIAL':
      return CategoryID.CATEGORY_ID_ROOM_BACKGROUND_OFFICIAL;
    case 16010:
    case 'CATEGORY_ID_ROOM_BACKGROUND_NOBLE':
      return CategoryID.CATEGORY_ID_ROOM_BACKGROUND_NOBLE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryID.UNRECOGNIZED;
  }
}

/** 特权分类状态 */
export enum CategoryStatus {
  /** CATEGORY_STATUS_NONE - 无意义 */
  CATEGORY_STATUS_NONE = 0,
  /** CATEGORY_STATUS_NORMAL - 正常 */
  CATEGORY_STATUS_NORMAL = 1,
  /** CATEGORY_STATUS_DISABLED - 禁用 */
  CATEGORY_STATUS_DISABLED = 2,
  UNRECOGNIZED = -1
}

export function categoryStatusFromJSON(object: any): CategoryStatus {
  switch (object) {
    case 0:
    case 'CATEGORY_STATUS_NONE':
      return CategoryStatus.CATEGORY_STATUS_NONE;
    case 1:
    case 'CATEGORY_STATUS_NORMAL':
      return CategoryStatus.CATEGORY_STATUS_NORMAL;
    case 2:
    case 'CATEGORY_STATUS_DISABLED':
      return CategoryStatus.CATEGORY_STATUS_DISABLED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryStatus.UNRECOGNIZED;
  }
}

/** 特权资源状态 */
export enum ResourceStatus {
  /** RESOURCE_STATUS_NONE - 无意义 */
  RESOURCE_STATUS_NONE = 0,
  /** RESOURCE_STATUS_ON_SALE - 上架 */
  RESOURCE_STATUS_ON_SALE = 1,
  /** RESOURCE_STATUS_OFF_SALE - 下架 */
  RESOURCE_STATUS_OFF_SALE = 2,
  /** RESOURCE_STATUS_DELETED - 已删除 */
  RESOURCE_STATUS_DELETED = 3,
  UNRECOGNIZED = -1
}

export function resourceStatusFromJSON(object: any): ResourceStatus {
  switch (object) {
    case 0:
    case 'RESOURCE_STATUS_NONE':
      return ResourceStatus.RESOURCE_STATUS_NONE;
    case 1:
    case 'RESOURCE_STATUS_ON_SALE':
      return ResourceStatus.RESOURCE_STATUS_ON_SALE;
    case 2:
    case 'RESOURCE_STATUS_OFF_SALE':
      return ResourceStatus.RESOURCE_STATUS_OFF_SALE;
    case 3:
    case 'RESOURCE_STATUS_DELETED':
      return ResourceStatus.RESOURCE_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourceStatus.UNRECOGNIZED;
  }
}

/** 靓号使用状态 */
export enum PrettyNumberUseStatus {
  /** PRETTY_NUMBER_USE_STATUS_NONE - 无意义 */
  PRETTY_NUMBER_USE_STATUS_NONE = 0,
  /** PRETTY_NUMBER_USE_STATUS_USING - 使用中 */
  PRETTY_NUMBER_USE_STATUS_USING = 1,
  /** PRETTY_NUMBER_USE_STATUS_UNUSED - 未使用 */
  PRETTY_NUMBER_USE_STATUS_UNUSED = 2,
  UNRECOGNIZED = -1
}

export function prettyNumberUseStatusFromJSON(object: any): PrettyNumberUseStatus {
  switch (object) {
    case 0:
    case 'PRETTY_NUMBER_USE_STATUS_NONE':
      return PrettyNumberUseStatus.PRETTY_NUMBER_USE_STATUS_NONE;
    case 1:
    case 'PRETTY_NUMBER_USE_STATUS_USING':
      return PrettyNumberUseStatus.PRETTY_NUMBER_USE_STATUS_USING;
    case 2:
    case 'PRETTY_NUMBER_USE_STATUS_UNUSED':
      return PrettyNumberUseStatus.PRETTY_NUMBER_USE_STATUS_UNUSED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PrettyNumberUseStatus.UNRECOGNIZED;
  }
}

/** 靓号绑定状态 */
export enum PrettyNumberBindStatus {
  /** PRETTY_NUMBER_BIND_STATUS_NONE - 无意义 */
  PRETTY_NUMBER_BIND_STATUS_NONE = 0,
  /** PRETTY_NUMBER_BIND_STATUS_BIND - 绑定中 */
  PRETTY_NUMBER_BIND_STATUS_BIND = 1,
  /** PRETTY_NUMBER_BIND_STATUS_UNBIND - 未绑定 */
  PRETTY_NUMBER_BIND_STATUS_UNBIND = 2,
  UNRECOGNIZED = -1
}

export function prettyNumberBindStatusFromJSON(object: any): PrettyNumberBindStatus {
  switch (object) {
    case 0:
    case 'PRETTY_NUMBER_BIND_STATUS_NONE':
      return PrettyNumberBindStatus.PRETTY_NUMBER_BIND_STATUS_NONE;
    case 1:
    case 'PRETTY_NUMBER_BIND_STATUS_BIND':
      return PrettyNumberBindStatus.PRETTY_NUMBER_BIND_STATUS_BIND;
    case 2:
    case 'PRETTY_NUMBER_BIND_STATUS_UNBIND':
      return PrettyNumberBindStatus.PRETTY_NUMBER_BIND_STATUS_UNBIND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PrettyNumberBindStatus.UNRECOGNIZED;
  }
}

export interface ListResourceReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 分类标识 */
  category_id: CategoryID;
  /** 二级分类标识 */
  sub_category_id: CategoryID;
}

export interface ListResourceRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 特权资源列表 */
  resources: Resource[];
}

export interface ListCategoryReq {
  /** 分类标识 */
  category_id: CategoryID;
}

export interface ListCategoryRsp {
  /** 分类列表 */
  categories: Category[];
}

/** 素材信息 */
export interface AssetInfo {
  /** 资源地址 */
  url: string;
  /** 资源大小(单位: bytes) */
  size: number;
  /** 资源类型 */
  type: AssetType;
  /** 显示区域 */
  area: AssetArea;
}

/** 特权分类 */
export interface Category {
  /** 用户语言对应的资源 */
  asset: Category_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Category_Asset };
  /** 归属类型 */
  target_type: TargetType;
  /** 分类标识 */
  category_id: CategoryID;
  /** 分类使用策略 */
  use_strategy: UseStrategy;
}

export interface Category_Asset {
  /** 名称 */
  name: string;
  /** 图标 */
  icon: AssetInfo | undefined;
}

export interface Category_AssetsEntry {
  key: string;
  value: Category_Asset | undefined;
}

/** 特权资源 */
export interface Resource {
  /** 头像框 */
  headwear?: Headwear | undefined;
  /** 座驾 */
  vehicle?: Vehicle | undefined;
  /** 公屏气泡 */
  bubble?: ScreenBubble | undefined;
  /** 房间背景 */
  room_bg?: RoomBackground | undefined;
  /** 麦位光圈 */
  mic_aperture?: MicAperture | undefined;
  /** 主页动效 */
  home_action?: HomeAction | undefined;
  /** 勋章 */
  medal?: Medal | undefined;
  /** mini资料背景 */
  mini_card_bg?: MiniCardBackground | undefined;
  /** 进房飘窗 */
  bay_window?: EnterRoomBayWindow | undefined;
  /** 房间列表背景 */
  room_list_bg?: RoomListBackground | undefined;
  /** 用户靓号 */
  user_pretty_number?: PrettyNumber | undefined;
  /** 房间靓号 */
  room_pretty_number?: PrettyNumber | undefined;
  /** 昵称颜色 */
  nickname_color?: NicknameColor | undefined;
  /** vip 等级 */
  vip?: Vip | undefined;
}

/** 头像框 */
export interface Headwear {
  /** 用户语言对应的资源 */
  asset: Headwear_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Headwear_Asset };
  /** 资源ID */
  id: number;
}

export interface Headwear_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 缩略图 */
  thumb: AssetInfo | undefined;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface Headwear_AssetsEntry {
  key: string;
  value: Headwear_Asset | undefined;
}

/** 座驾 */
export interface Vehicle {
  /** 用户语言对应的资源 */
  asset: Vehicle_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Vehicle_Asset };
  /** 资源ID */
  id: number;
}

export interface Vehicle_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
  /** 进程横幅 */
  banner: AssetInfo | undefined;
  /** 横幅背景颜色 */
  banner_bg_color: string[];
}

export interface Vehicle_AssetsEntry {
  key: string;
  value: Vehicle_Asset | undefined;
}

/** 公屏气泡 */
export interface ScreenBubble {
  /** 用户语言对应的资源 */
  asset: ScreenBubble_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: ScreenBubble_Asset };
  /** 资源ID */
  id: number;
}

export interface ScreenBubble_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 图 */
  image: AssetInfo | undefined;
  /** 背景图 */
  bg: AssetInfo | undefined;
  /** 左上角 */
  left_top: AssetInfo | undefined;
  /** 左下角 */
  left_bottom: AssetInfo | undefined;
  /** 右上角 */
  right_top: AssetInfo | undefined;
  /** 右下角 */
  right_bottom: AssetInfo | undefined;
  /** 文本颜色 */
  text_color: string;
}

export interface ScreenBubble_AssetsEntry {
  key: string;
  value: ScreenBubble_Asset | undefined;
}

/** 房间背景 */
export interface RoomBackground {
  /** 用户语言对应的资源 */
  asset: RoomBackground_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: RoomBackground_Asset };
  /** 资源ID */
  id: number;
}

export interface RoomBackground_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 缩略图 */
  thumb: AssetInfo | undefined;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画资源 */
  animation: AssetInfo | undefined;
}

export interface RoomBackground_AssetsEntry {
  key: string;
  value: RoomBackground_Asset | undefined;
}

/** 麦位光圈 */
export interface MicAperture {
  /** 用户语言对应的资源 */
  asset: MicAperture_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: MicAperture_Asset };
  /** 资源ID */
  id: number;
}

export interface MicAperture_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface MicAperture_AssetsEntry {
  key: string;
  value: MicAperture_Asset | undefined;
}

/** 主页动效 */
export interface HomeAction {
  /** 用户语言对应的资源 */
  asset: HomeAction_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: HomeAction_Asset };
  /** 资源ID */
  id: number;
}

export interface HomeAction_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface HomeAction_AssetsEntry {
  key: string;
  value: HomeAction_Asset | undefined;
}

/** 勋章 */
export interface Medal {
  /** 用户语言对应的资源 */
  asset: Medal_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Medal_Asset };
  /** 勋章ID */
  id: number;
}

export interface Medal_Asset {
  /** 名称 */
  name: string;
  /** 简介 */
  brief: string;
  /** 缩略图 */
  thumb_image: AssetInfo | undefined;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 缩略 动画 资源 */
  thumb_animation: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface Medal_AssetsEntry {
  key: string;
  value: Medal_Asset | undefined;
}

/** mini资料卡背景 */
export interface MiniCardBackground {
  /** 用户语言对应的资源 */
  asset: MiniCardBackground_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: MiniCardBackground_Asset };
  /** 资源ID */
  id: number;
}

export interface MiniCardBackground_Asset {
  /** 名称 */
  name: string;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface MiniCardBackground_AssetsEntry {
  key: string;
  value: MiniCardBackground_Asset | undefined;
}

/** 进房飘窗 */
export interface EnterRoomBayWindow {
  /** 用户语言对应的资源 */
  asset: EnterRoomBayWindow_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: EnterRoomBayWindow_Asset };
  /** 资源ID */
  id: number;
}

export interface EnterRoomBayWindow_Asset {
  /** 名称 */
  name: string;
  /** 大图 */
  image: AssetInfo | undefined;
  /** 动画 资源 */
  animation: AssetInfo | undefined;
}

export interface EnterRoomBayWindow_AssetsEntry {
  key: string;
  value: EnterRoomBayWindow_Asset | undefined;
}

/** 房间列表背景 */
export interface RoomListBackground {
  /** 用户语言对应的资源 */
  asset: RoomListBackground_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: RoomListBackground_Asset };
  /** 资源ID */
  id: number;
}

export interface RoomListBackground_Asset {
  /** 名称 */
  name: string;
  /** 长方形背景图 */
  rectangle_image: AssetInfo | undefined;
  /** 正方形背景图 */
  square_image: AssetInfo | undefined;
}

export interface RoomListBackground_AssetsEntry {
  key: string;
  value: RoomListBackground_Asset | undefined;
}

/** 昵称颜色 */
export interface NicknameColor {
  /** 用户语言对应的资源 */
  asset: NicknameColor_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: NicknameColor_Asset };
  /** 资源ID */
  id: number;
}

export interface NicknameColor_Asset {
  /** 名称 */
  name: string;
  /** 颜色列表 */
  colors: string[];
  /** 效果图 */
  image: AssetInfo | undefined;
}

export interface NicknameColor_AssetsEntry {
  key: string;
  value: NicknameColor_Asset | undefined;
}

/** VIP等级 */
export interface Vip {
  /** 用户语言对应的资源 */
  asset: Vip_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: Vip_Asset };
  /** 勋章ID */
  id: number;
}

export interface Vip_Asset {
  /** 等级 */
  level: string;
  /** 效果图 */
  image: AssetInfo | undefined;
}

export interface Vip_AssetsEntry {
  key: string;
  value: Vip_Asset | undefined;
}

/** 靓号 */
export interface PrettyNumber {
  /** 用户语言对应的资源 */
  asset: PrettyNumber_Asset | undefined;
  /** 国际化资源 */
  assets: { [key: string]: PrettyNumber_Asset };
  /** 靓号ID */
  pretty_number_id: string;
}

export interface PrettyNumber_Asset {
  /** 图标 */
  icon: AssetInfo | undefined;
  /** 图片 */
  image: AssetInfo | undefined;
}

export interface PrettyNumber_AssetsEntry {
  key: string;
  value: PrettyNumber_Asset | undefined;
}

function createBaseListResourceReq(): ListResourceReq {
  return { page: undefined, category_id: 0, sub_category_id: 0 };
}

export const ListResourceReq: MessageFns<ListResourceReq> = {
  fromJSON(object: any): ListResourceReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListResourceReq>, I>>(base?: I): ListResourceReq {
    return ListResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourceReq>, I>>(object: I): ListResourceReq {
    const message = createBaseListResourceReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    return message;
  }
};

function createBaseListResourceRsp(): ListResourceRsp {
  return { page: undefined, resources: [] };
}

export const ListResourceRsp: MessageFns<ListResourceRsp> = {
  fromJSON(object: any): ListResourceRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      resources: globalThis.Array.isArray(object?.resources)
        ? object.resources.map((e: any) => Resource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourceRsp>, I>>(base?: I): ListResourceRsp {
    return ListResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourceRsp>, I>>(object: I): ListResourceRsp {
    const message = createBaseListResourceRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.resources = object.resources?.map(e => Resource.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListCategoryReq(): ListCategoryReq {
  return { category_id: 0 };
}

export const ListCategoryReq: MessageFns<ListCategoryReq> = {
  fromJSON(object: any): ListCategoryReq {
    return { category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListCategoryReq>, I>>(base?: I): ListCategoryReq {
    return ListCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryReq>, I>>(object: I): ListCategoryReq {
    const message = createBaseListCategoryReq();
    message.category_id = object.category_id ?? 0;
    return message;
  }
};

function createBaseListCategoryRsp(): ListCategoryRsp {
  return { categories: [] };
}

export const ListCategoryRsp: MessageFns<ListCategoryRsp> = {
  fromJSON(object: any): ListCategoryRsp {
    return {
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => Category.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(base?: I): ListCategoryRsp {
    return ListCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(object: I): ListCategoryRsp {
    const message = createBaseListCategoryRsp();
    message.categories = object.categories?.map(e => Category.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAssetInfo(): AssetInfo {
  return { url: '', size: 0, type: 0, area: 0 };
}

export const AssetInfo: MessageFns<AssetInfo> = {
  fromJSON(object: any): AssetInfo {
    return {
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      size: isSet(object.size) ? globalThis.Number(object.size) : 0,
      type: isSet(object.type) ? assetTypeFromJSON(object.type) : 0,
      area: isSet(object.area) ? assetAreaFromJSON(object.area) : 0
    };
  },

  create<I extends Exact<DeepPartial<AssetInfo>, I>>(base?: I): AssetInfo {
    return AssetInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AssetInfo>, I>>(object: I): AssetInfo {
    const message = createBaseAssetInfo();
    message.url = object.url ?? '';
    message.size = object.size ?? 0;
    message.type = object.type ?? 0;
    message.area = object.area ?? 0;
    return message;
  }
};

function createBaseCategory(): Category {
  return { asset: undefined, assets: {}, target_type: 0, category_id: 0, use_strategy: 0 };
}

export const Category: MessageFns<Category> = {
  fromJSON(object: any): Category {
    return {
      asset: isSet(object.asset) ? Category_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Category_Asset }>((acc, [key, value]) => {
            acc[key] = Category_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      use_strategy: isSet(object.use_strategy) ? useStrategyFromJSON(object.use_strategy) : 0
    };
  },

  create<I extends Exact<DeepPartial<Category>, I>>(base?: I): Category {
    return Category.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category>, I>>(object: I): Category {
    const message = createBaseCategory();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Category_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Category_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Category_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.target_type = object.target_type ?? 0;
    message.category_id = object.category_id ?? 0;
    message.use_strategy = object.use_strategy ?? 0;
    return message;
  }
};

function createBaseCategory_Asset(): Category_Asset {
  return { name: '', icon: undefined };
}

export const Category_Asset: MessageFns<Category_Asset> = {
  fromJSON(object: any): Category_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? AssetInfo.fromJSON(object.icon) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Category_Asset>, I>>(base?: I): Category_Asset {
    return Category_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category_Asset>, I>>(object: I): Category_Asset {
    const message = createBaseCategory_Asset();
    message.name = object.name ?? '';
    message.icon = object.icon !== undefined && object.icon !== null ? AssetInfo.fromPartial(object.icon) : undefined;
    return message;
  }
};

function createBaseCategory_AssetsEntry(): Category_AssetsEntry {
  return { key: '', value: undefined };
}

export const Category_AssetsEntry: MessageFns<Category_AssetsEntry> = {
  fromJSON(object: any): Category_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Category_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Category_AssetsEntry>, I>>(base?: I): Category_AssetsEntry {
    return Category_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category_AssetsEntry>, I>>(object: I): Category_AssetsEntry {
    const message = createBaseCategory_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Category_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseResource(): Resource {
  return {
    headwear: undefined,
    vehicle: undefined,
    bubble: undefined,
    room_bg: undefined,
    mic_aperture: undefined,
    home_action: undefined,
    medal: undefined,
    mini_card_bg: undefined,
    bay_window: undefined,
    room_list_bg: undefined,
    user_pretty_number: undefined,
    room_pretty_number: undefined,
    nickname_color: undefined,
    vip: undefined
  };
}

export const Resource: MessageFns<Resource> = {
  fromJSON(object: any): Resource {
    return {
      headwear: isSet(object.headwear) ? Headwear.fromJSON(object.headwear) : undefined,
      vehicle: isSet(object.vehicle) ? Vehicle.fromJSON(object.vehicle) : undefined,
      bubble: isSet(object.bubble) ? ScreenBubble.fromJSON(object.bubble) : undefined,
      room_bg: isSet(object.room_bg) ? RoomBackground.fromJSON(object.room_bg) : undefined,
      mic_aperture: isSet(object.mic_aperture) ? MicAperture.fromJSON(object.mic_aperture) : undefined,
      home_action: isSet(object.home_action) ? HomeAction.fromJSON(object.home_action) : undefined,
      medal: isSet(object.medal) ? Medal.fromJSON(object.medal) : undefined,
      mini_card_bg: isSet(object.mini_card_bg) ? MiniCardBackground.fromJSON(object.mini_card_bg) : undefined,
      bay_window: isSet(object.bay_window) ? EnterRoomBayWindow.fromJSON(object.bay_window) : undefined,
      room_list_bg: isSet(object.room_list_bg) ? RoomListBackground.fromJSON(object.room_list_bg) : undefined,
      user_pretty_number: isSet(object.user_pretty_number)
        ? PrettyNumber.fromJSON(object.user_pretty_number)
        : undefined,
      room_pretty_number: isSet(object.room_pretty_number)
        ? PrettyNumber.fromJSON(object.room_pretty_number)
        : undefined,
      nickname_color: isSet(object.nickname_color) ? NicknameColor.fromJSON(object.nickname_color) : undefined,
      vip: isSet(object.vip) ? Vip.fromJSON(object.vip) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Resource>, I>>(base?: I): Resource {
    return Resource.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Resource>, I>>(object: I): Resource {
    const message = createBaseResource();
    message.headwear =
      object.headwear !== undefined && object.headwear !== null ? Headwear.fromPartial(object.headwear) : undefined;
    message.vehicle =
      object.vehicle !== undefined && object.vehicle !== null ? Vehicle.fromPartial(object.vehicle) : undefined;
    message.bubble =
      object.bubble !== undefined && object.bubble !== null ? ScreenBubble.fromPartial(object.bubble) : undefined;
    message.room_bg =
      object.room_bg !== undefined && object.room_bg !== null ? RoomBackground.fromPartial(object.room_bg) : undefined;
    message.mic_aperture =
      object.mic_aperture !== undefined && object.mic_aperture !== null
        ? MicAperture.fromPartial(object.mic_aperture)
        : undefined;
    message.home_action =
      object.home_action !== undefined && object.home_action !== null
        ? HomeAction.fromPartial(object.home_action)
        : undefined;
    message.medal = object.medal !== undefined && object.medal !== null ? Medal.fromPartial(object.medal) : undefined;
    message.mini_card_bg =
      object.mini_card_bg !== undefined && object.mini_card_bg !== null
        ? MiniCardBackground.fromPartial(object.mini_card_bg)
        : undefined;
    message.bay_window =
      object.bay_window !== undefined && object.bay_window !== null
        ? EnterRoomBayWindow.fromPartial(object.bay_window)
        : undefined;
    message.room_list_bg =
      object.room_list_bg !== undefined && object.room_list_bg !== null
        ? RoomListBackground.fromPartial(object.room_list_bg)
        : undefined;
    message.user_pretty_number =
      object.user_pretty_number !== undefined && object.user_pretty_number !== null
        ? PrettyNumber.fromPartial(object.user_pretty_number)
        : undefined;
    message.room_pretty_number =
      object.room_pretty_number !== undefined && object.room_pretty_number !== null
        ? PrettyNumber.fromPartial(object.room_pretty_number)
        : undefined;
    message.nickname_color =
      object.nickname_color !== undefined && object.nickname_color !== null
        ? NicknameColor.fromPartial(object.nickname_color)
        : undefined;
    message.vip = object.vip !== undefined && object.vip !== null ? Vip.fromPartial(object.vip) : undefined;
    return message;
  }
};

function createBaseHeadwear(): Headwear {
  return { asset: undefined, assets: {}, id: 0 };
}

export const Headwear: MessageFns<Headwear> = {
  fromJSON(object: any): Headwear {
    return {
      asset: isSet(object.asset) ? Headwear_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Headwear_Asset }>((acc, [key, value]) => {
            acc[key] = Headwear_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Headwear>, I>>(base?: I): Headwear {
    return Headwear.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear>, I>>(object: I): Headwear {
    const message = createBaseHeadwear();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Headwear_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Headwear_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Headwear_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseHeadwear_Asset(): Headwear_Asset {
  return { name: '', brief: '', thumb: undefined, image: undefined, animation: undefined };
}

export const Headwear_Asset: MessageFns<Headwear_Asset> = {
  fromJSON(object: any): Headwear_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      thumb: isSet(object.thumb) ? AssetInfo.fromJSON(object.thumb) : undefined,
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Headwear_Asset>, I>>(base?: I): Headwear_Asset {
    return Headwear_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear_Asset>, I>>(object: I): Headwear_Asset {
    const message = createBaseHeadwear_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.thumb =
      object.thumb !== undefined && object.thumb !== null ? AssetInfo.fromPartial(object.thumb) : undefined;
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseHeadwear_AssetsEntry(): Headwear_AssetsEntry {
  return { key: '', value: undefined };
}

export const Headwear_AssetsEntry: MessageFns<Headwear_AssetsEntry> = {
  fromJSON(object: any): Headwear_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Headwear_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Headwear_AssetsEntry>, I>>(base?: I): Headwear_AssetsEntry {
    return Headwear_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Headwear_AssetsEntry>, I>>(object: I): Headwear_AssetsEntry {
    const message = createBaseHeadwear_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Headwear_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseVehicle(): Vehicle {
  return { asset: undefined, assets: {}, id: 0 };
}

export const Vehicle: MessageFns<Vehicle> = {
  fromJSON(object: any): Vehicle {
    return {
      asset: isSet(object.asset) ? Vehicle_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Vehicle_Asset }>((acc, [key, value]) => {
            acc[key] = Vehicle_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Vehicle>, I>>(base?: I): Vehicle {
    return Vehicle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vehicle>, I>>(object: I): Vehicle {
    const message = createBaseVehicle();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Vehicle_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Vehicle_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = Vehicle_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseVehicle_Asset(): Vehicle_Asset {
  return { name: '', brief: '', image: undefined, animation: undefined, banner: undefined, banner_bg_color: [] };
}

export const Vehicle_Asset: MessageFns<Vehicle_Asset> = {
  fromJSON(object: any): Vehicle_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined,
      banner: isSet(object.banner) ? AssetInfo.fromJSON(object.banner) : undefined,
      banner_bg_color: globalThis.Array.isArray(object?.banner_bg_color)
        ? object.banner_bg_color.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<Vehicle_Asset>, I>>(base?: I): Vehicle_Asset {
    return Vehicle_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vehicle_Asset>, I>>(object: I): Vehicle_Asset {
    const message = createBaseVehicle_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    message.banner =
      object.banner !== undefined && object.banner !== null ? AssetInfo.fromPartial(object.banner) : undefined;
    message.banner_bg_color = object.banner_bg_color?.map(e => e) || [];
    return message;
  }
};

function createBaseVehicle_AssetsEntry(): Vehicle_AssetsEntry {
  return { key: '', value: undefined };
}

export const Vehicle_AssetsEntry: MessageFns<Vehicle_AssetsEntry> = {
  fromJSON(object: any): Vehicle_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Vehicle_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Vehicle_AssetsEntry>, I>>(base?: I): Vehicle_AssetsEntry {
    return Vehicle_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vehicle_AssetsEntry>, I>>(object: I): Vehicle_AssetsEntry {
    const message = createBaseVehicle_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Vehicle_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseScreenBubble(): ScreenBubble {
  return { asset: undefined, assets: {}, id: 0 };
}

export const ScreenBubble: MessageFns<ScreenBubble> = {
  fromJSON(object: any): ScreenBubble {
    return {
      asset: isSet(object.asset) ? ScreenBubble_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: ScreenBubble_Asset }>((acc, [key, value]) => {
            acc[key] = ScreenBubble_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ScreenBubble>, I>>(base?: I): ScreenBubble {
    return ScreenBubble.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScreenBubble>, I>>(object: I): ScreenBubble {
    const message = createBaseScreenBubble();
    message.asset =
      object.asset !== undefined && object.asset !== null ? ScreenBubble_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: ScreenBubble_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = ScreenBubble_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseScreenBubble_Asset(): ScreenBubble_Asset {
  return {
    name: '',
    brief: '',
    image: undefined,
    bg: undefined,
    left_top: undefined,
    left_bottom: undefined,
    right_top: undefined,
    right_bottom: undefined,
    text_color: ''
  };
}

export const ScreenBubble_Asset: MessageFns<ScreenBubble_Asset> = {
  fromJSON(object: any): ScreenBubble_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      bg: isSet(object.bg) ? AssetInfo.fromJSON(object.bg) : undefined,
      left_top: isSet(object.left_top) ? AssetInfo.fromJSON(object.left_top) : undefined,
      left_bottom: isSet(object.left_bottom) ? AssetInfo.fromJSON(object.left_bottom) : undefined,
      right_top: isSet(object.right_top) ? AssetInfo.fromJSON(object.right_top) : undefined,
      right_bottom: isSet(object.right_bottom) ? AssetInfo.fromJSON(object.right_bottom) : undefined,
      text_color: isSet(object.text_color) ? globalThis.String(object.text_color) : ''
    };
  },

  create<I extends Exact<DeepPartial<ScreenBubble_Asset>, I>>(base?: I): ScreenBubble_Asset {
    return ScreenBubble_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScreenBubble_Asset>, I>>(object: I): ScreenBubble_Asset {
    const message = createBaseScreenBubble_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.bg = object.bg !== undefined && object.bg !== null ? AssetInfo.fromPartial(object.bg) : undefined;
    message.left_top =
      object.left_top !== undefined && object.left_top !== null ? AssetInfo.fromPartial(object.left_top) : undefined;
    message.left_bottom =
      object.left_bottom !== undefined && object.left_bottom !== null
        ? AssetInfo.fromPartial(object.left_bottom)
        : undefined;
    message.right_top =
      object.right_top !== undefined && object.right_top !== null ? AssetInfo.fromPartial(object.right_top) : undefined;
    message.right_bottom =
      object.right_bottom !== undefined && object.right_bottom !== null
        ? AssetInfo.fromPartial(object.right_bottom)
        : undefined;
    message.text_color = object.text_color ?? '';
    return message;
  }
};

function createBaseScreenBubble_AssetsEntry(): ScreenBubble_AssetsEntry {
  return { key: '', value: undefined };
}

export const ScreenBubble_AssetsEntry: MessageFns<ScreenBubble_AssetsEntry> = {
  fromJSON(object: any): ScreenBubble_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? ScreenBubble_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ScreenBubble_AssetsEntry>, I>>(base?: I): ScreenBubble_AssetsEntry {
    return ScreenBubble_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScreenBubble_AssetsEntry>, I>>(object: I): ScreenBubble_AssetsEntry {
    const message = createBaseScreenBubble_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? ScreenBubble_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseRoomBackground(): RoomBackground {
  return { asset: undefined, assets: {}, id: 0 };
}

export const RoomBackground: MessageFns<RoomBackground> = {
  fromJSON(object: any): RoomBackground {
    return {
      asset: isSet(object.asset) ? RoomBackground_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: RoomBackground_Asset }>((acc, [key, value]) => {
            acc[key] = RoomBackground_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomBackground>, I>>(base?: I): RoomBackground {
    return RoomBackground.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomBackground>, I>>(object: I): RoomBackground {
    const message = createBaseRoomBackground();
    message.asset =
      object.asset !== undefined && object.asset !== null ? RoomBackground_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: RoomBackground_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = RoomBackground_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseRoomBackground_Asset(): RoomBackground_Asset {
  return { name: '', brief: '', thumb: undefined, image: undefined, animation: undefined };
}

export const RoomBackground_Asset: MessageFns<RoomBackground_Asset> = {
  fromJSON(object: any): RoomBackground_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      thumb: isSet(object.thumb) ? AssetInfo.fromJSON(object.thumb) : undefined,
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomBackground_Asset>, I>>(base?: I): RoomBackground_Asset {
    return RoomBackground_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomBackground_Asset>, I>>(object: I): RoomBackground_Asset {
    const message = createBaseRoomBackground_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.thumb =
      object.thumb !== undefined && object.thumb !== null ? AssetInfo.fromPartial(object.thumb) : undefined;
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseRoomBackground_AssetsEntry(): RoomBackground_AssetsEntry {
  return { key: '', value: undefined };
}

export const RoomBackground_AssetsEntry: MessageFns<RoomBackground_AssetsEntry> = {
  fromJSON(object: any): RoomBackground_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? RoomBackground_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomBackground_AssetsEntry>, I>>(base?: I): RoomBackground_AssetsEntry {
    return RoomBackground_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomBackground_AssetsEntry>, I>>(object: I): RoomBackground_AssetsEntry {
    const message = createBaseRoomBackground_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? RoomBackground_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseMicAperture(): MicAperture {
  return { asset: undefined, assets: {}, id: 0 };
}

export const MicAperture: MessageFns<MicAperture> = {
  fromJSON(object: any): MicAperture {
    return {
      asset: isSet(object.asset) ? MicAperture_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: MicAperture_Asset }>((acc, [key, value]) => {
            acc[key] = MicAperture_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<MicAperture>, I>>(base?: I): MicAperture {
    return MicAperture.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MicAperture>, I>>(object: I): MicAperture {
    const message = createBaseMicAperture();
    message.asset =
      object.asset !== undefined && object.asset !== null ? MicAperture_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: MicAperture_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = MicAperture_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseMicAperture_Asset(): MicAperture_Asset {
  return { name: '', brief: '', image: undefined, animation: undefined };
}

export const MicAperture_Asset: MessageFns<MicAperture_Asset> = {
  fromJSON(object: any): MicAperture_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MicAperture_Asset>, I>>(base?: I): MicAperture_Asset {
    return MicAperture_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MicAperture_Asset>, I>>(object: I): MicAperture_Asset {
    const message = createBaseMicAperture_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseMicAperture_AssetsEntry(): MicAperture_AssetsEntry {
  return { key: '', value: undefined };
}

export const MicAperture_AssetsEntry: MessageFns<MicAperture_AssetsEntry> = {
  fromJSON(object: any): MicAperture_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? MicAperture_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MicAperture_AssetsEntry>, I>>(base?: I): MicAperture_AssetsEntry {
    return MicAperture_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MicAperture_AssetsEntry>, I>>(object: I): MicAperture_AssetsEntry {
    const message = createBaseMicAperture_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? MicAperture_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseHomeAction(): HomeAction {
  return { asset: undefined, assets: {}, id: 0 };
}

export const HomeAction: MessageFns<HomeAction> = {
  fromJSON(object: any): HomeAction {
    return {
      asset: isSet(object.asset) ? HomeAction_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: HomeAction_Asset }>((acc, [key, value]) => {
            acc[key] = HomeAction_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<HomeAction>, I>>(base?: I): HomeAction {
    return HomeAction.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomeAction>, I>>(object: I): HomeAction {
    const message = createBaseHomeAction();
    message.asset =
      object.asset !== undefined && object.asset !== null ? HomeAction_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: HomeAction_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = HomeAction_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseHomeAction_Asset(): HomeAction_Asset {
  return { name: '', brief: '', image: undefined, animation: undefined };
}

export const HomeAction_Asset: MessageFns<HomeAction_Asset> = {
  fromJSON(object: any): HomeAction_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<HomeAction_Asset>, I>>(base?: I): HomeAction_Asset {
    return HomeAction_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomeAction_Asset>, I>>(object: I): HomeAction_Asset {
    const message = createBaseHomeAction_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseHomeAction_AssetsEntry(): HomeAction_AssetsEntry {
  return { key: '', value: undefined };
}

export const HomeAction_AssetsEntry: MessageFns<HomeAction_AssetsEntry> = {
  fromJSON(object: any): HomeAction_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? HomeAction_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<HomeAction_AssetsEntry>, I>>(base?: I): HomeAction_AssetsEntry {
    return HomeAction_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<HomeAction_AssetsEntry>, I>>(object: I): HomeAction_AssetsEntry {
    const message = createBaseHomeAction_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? HomeAction_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseMedal(): Medal {
  return { asset: undefined, assets: {}, id: 0 };
}

export const Medal: MessageFns<Medal> = {
  fromJSON(object: any): Medal {
    return {
      asset: isSet(object.asset) ? Medal_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Medal_Asset }>((acc, [key, value]) => {
            acc[key] = Medal_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Medal>, I>>(base?: I): Medal {
    return Medal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Medal>, I>>(object: I): Medal {
    const message = createBaseMedal();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Medal_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Medal_Asset }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Medal_Asset.fromPartial(value);
      }
      return acc;
    }, {});
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseMedal_Asset(): Medal_Asset {
  return {
    name: '',
    brief: '',
    thumb_image: undefined,
    image: undefined,
    thumb_animation: undefined,
    animation: undefined
  };
}

export const Medal_Asset: MessageFns<Medal_Asset> = {
  fromJSON(object: any): Medal_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      thumb_image: isSet(object.thumb_image) ? AssetInfo.fromJSON(object.thumb_image) : undefined,
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      thumb_animation: isSet(object.thumb_animation) ? AssetInfo.fromJSON(object.thumb_animation) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Medal_Asset>, I>>(base?: I): Medal_Asset {
    return Medal_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Medal_Asset>, I>>(object: I): Medal_Asset {
    const message = createBaseMedal_Asset();
    message.name = object.name ?? '';
    message.brief = object.brief ?? '';
    message.thumb_image =
      object.thumb_image !== undefined && object.thumb_image !== null
        ? AssetInfo.fromPartial(object.thumb_image)
        : undefined;
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.thumb_animation =
      object.thumb_animation !== undefined && object.thumb_animation !== null
        ? AssetInfo.fromPartial(object.thumb_animation)
        : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseMedal_AssetsEntry(): Medal_AssetsEntry {
  return { key: '', value: undefined };
}

export const Medal_AssetsEntry: MessageFns<Medal_AssetsEntry> = {
  fromJSON(object: any): Medal_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Medal_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Medal_AssetsEntry>, I>>(base?: I): Medal_AssetsEntry {
    return Medal_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Medal_AssetsEntry>, I>>(object: I): Medal_AssetsEntry {
    const message = createBaseMedal_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Medal_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseMiniCardBackground(): MiniCardBackground {
  return { asset: undefined, assets: {}, id: 0 };
}

export const MiniCardBackground: MessageFns<MiniCardBackground> = {
  fromJSON(object: any): MiniCardBackground {
    return {
      asset: isSet(object.asset) ? MiniCardBackground_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: MiniCardBackground_Asset }>((acc, [key, value]) => {
            acc[key] = MiniCardBackground_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<MiniCardBackground>, I>>(base?: I): MiniCardBackground {
    return MiniCardBackground.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MiniCardBackground>, I>>(object: I): MiniCardBackground {
    const message = createBaseMiniCardBackground();
    message.asset =
      object.asset !== undefined && object.asset !== null
        ? MiniCardBackground_Asset.fromPartial(object.asset)
        : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: MiniCardBackground_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = MiniCardBackground_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseMiniCardBackground_Asset(): MiniCardBackground_Asset {
  return { name: '', image: undefined, animation: undefined };
}

export const MiniCardBackground_Asset: MessageFns<MiniCardBackground_Asset> = {
  fromJSON(object: any): MiniCardBackground_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MiniCardBackground_Asset>, I>>(base?: I): MiniCardBackground_Asset {
    return MiniCardBackground_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MiniCardBackground_Asset>, I>>(object: I): MiniCardBackground_Asset {
    const message = createBaseMiniCardBackground_Asset();
    message.name = object.name ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseMiniCardBackground_AssetsEntry(): MiniCardBackground_AssetsEntry {
  return { key: '', value: undefined };
}

export const MiniCardBackground_AssetsEntry: MessageFns<MiniCardBackground_AssetsEntry> = {
  fromJSON(object: any): MiniCardBackground_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? MiniCardBackground_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MiniCardBackground_AssetsEntry>, I>>(base?: I): MiniCardBackground_AssetsEntry {
    return MiniCardBackground_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MiniCardBackground_AssetsEntry>, I>>(
    object: I
  ): MiniCardBackground_AssetsEntry {
    const message = createBaseMiniCardBackground_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null
        ? MiniCardBackground_Asset.fromPartial(object.value)
        : undefined;
    return message;
  }
};

function createBaseEnterRoomBayWindow(): EnterRoomBayWindow {
  return { asset: undefined, assets: {}, id: 0 };
}

export const EnterRoomBayWindow: MessageFns<EnterRoomBayWindow> = {
  fromJSON(object: any): EnterRoomBayWindow {
    return {
      asset: isSet(object.asset) ? EnterRoomBayWindow_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: EnterRoomBayWindow_Asset }>((acc, [key, value]) => {
            acc[key] = EnterRoomBayWindow_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomBayWindow>, I>>(base?: I): EnterRoomBayWindow {
    return EnterRoomBayWindow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomBayWindow>, I>>(object: I): EnterRoomBayWindow {
    const message = createBaseEnterRoomBayWindow();
    message.asset =
      object.asset !== undefined && object.asset !== null
        ? EnterRoomBayWindow_Asset.fromPartial(object.asset)
        : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: EnterRoomBayWindow_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = EnterRoomBayWindow_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseEnterRoomBayWindow_Asset(): EnterRoomBayWindow_Asset {
  return { name: '', image: undefined, animation: undefined };
}

export const EnterRoomBayWindow_Asset: MessageFns<EnterRoomBayWindow_Asset> = {
  fromJSON(object: any): EnterRoomBayWindow_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined,
      animation: isSet(object.animation) ? AssetInfo.fromJSON(object.animation) : undefined
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomBayWindow_Asset>, I>>(base?: I): EnterRoomBayWindow_Asset {
    return EnterRoomBayWindow_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomBayWindow_Asset>, I>>(object: I): EnterRoomBayWindow_Asset {
    const message = createBaseEnterRoomBayWindow_Asset();
    message.name = object.name ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    message.animation =
      object.animation !== undefined && object.animation !== null ? AssetInfo.fromPartial(object.animation) : undefined;
    return message;
  }
};

function createBaseEnterRoomBayWindow_AssetsEntry(): EnterRoomBayWindow_AssetsEntry {
  return { key: '', value: undefined };
}

export const EnterRoomBayWindow_AssetsEntry: MessageFns<EnterRoomBayWindow_AssetsEntry> = {
  fromJSON(object: any): EnterRoomBayWindow_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? EnterRoomBayWindow_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<EnterRoomBayWindow_AssetsEntry>, I>>(base?: I): EnterRoomBayWindow_AssetsEntry {
    return EnterRoomBayWindow_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnterRoomBayWindow_AssetsEntry>, I>>(
    object: I
  ): EnterRoomBayWindow_AssetsEntry {
    const message = createBaseEnterRoomBayWindow_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null
        ? EnterRoomBayWindow_Asset.fromPartial(object.value)
        : undefined;
    return message;
  }
};

function createBaseRoomListBackground(): RoomListBackground {
  return { asset: undefined, assets: {}, id: 0 };
}

export const RoomListBackground: MessageFns<RoomListBackground> = {
  fromJSON(object: any): RoomListBackground {
    return {
      asset: isSet(object.asset) ? RoomListBackground_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: RoomListBackground_Asset }>((acc, [key, value]) => {
            acc[key] = RoomListBackground_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomListBackground>, I>>(base?: I): RoomListBackground {
    return RoomListBackground.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomListBackground>, I>>(object: I): RoomListBackground {
    const message = createBaseRoomListBackground();
    message.asset =
      object.asset !== undefined && object.asset !== null
        ? RoomListBackground_Asset.fromPartial(object.asset)
        : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: RoomListBackground_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = RoomListBackground_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseRoomListBackground_Asset(): RoomListBackground_Asset {
  return { name: '', rectangle_image: undefined, square_image: undefined };
}

export const RoomListBackground_Asset: MessageFns<RoomListBackground_Asset> = {
  fromJSON(object: any): RoomListBackground_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      rectangle_image: isSet(object.rectangle_image) ? AssetInfo.fromJSON(object.rectangle_image) : undefined,
      square_image: isSet(object.square_image) ? AssetInfo.fromJSON(object.square_image) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomListBackground_Asset>, I>>(base?: I): RoomListBackground_Asset {
    return RoomListBackground_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomListBackground_Asset>, I>>(object: I): RoomListBackground_Asset {
    const message = createBaseRoomListBackground_Asset();
    message.name = object.name ?? '';
    message.rectangle_image =
      object.rectangle_image !== undefined && object.rectangle_image !== null
        ? AssetInfo.fromPartial(object.rectangle_image)
        : undefined;
    message.square_image =
      object.square_image !== undefined && object.square_image !== null
        ? AssetInfo.fromPartial(object.square_image)
        : undefined;
    return message;
  }
};

function createBaseRoomListBackground_AssetsEntry(): RoomListBackground_AssetsEntry {
  return { key: '', value: undefined };
}

export const RoomListBackground_AssetsEntry: MessageFns<RoomListBackground_AssetsEntry> = {
  fromJSON(object: any): RoomListBackground_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? RoomListBackground_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RoomListBackground_AssetsEntry>, I>>(base?: I): RoomListBackground_AssetsEntry {
    return RoomListBackground_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomListBackground_AssetsEntry>, I>>(
    object: I
  ): RoomListBackground_AssetsEntry {
    const message = createBaseRoomListBackground_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null
        ? RoomListBackground_Asset.fromPartial(object.value)
        : undefined;
    return message;
  }
};

function createBaseNicknameColor(): NicknameColor {
  return { asset: undefined, assets: {}, id: 0 };
}

export const NicknameColor: MessageFns<NicknameColor> = {
  fromJSON(object: any): NicknameColor {
    return {
      asset: isSet(object.asset) ? NicknameColor_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: NicknameColor_Asset }>((acc, [key, value]) => {
            acc[key] = NicknameColor_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<NicknameColor>, I>>(base?: I): NicknameColor {
    return NicknameColor.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NicknameColor>, I>>(object: I): NicknameColor {
    const message = createBaseNicknameColor();
    message.asset =
      object.asset !== undefined && object.asset !== null ? NicknameColor_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: NicknameColor_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = NicknameColor_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseNicknameColor_Asset(): NicknameColor_Asset {
  return { name: '', colors: [], image: undefined };
}

export const NicknameColor_Asset: MessageFns<NicknameColor_Asset> = {
  fromJSON(object: any): NicknameColor_Asset {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      colors: globalThis.Array.isArray(object?.colors) ? object.colors.map((e: any) => globalThis.String(e)) : [],
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NicknameColor_Asset>, I>>(base?: I): NicknameColor_Asset {
    return NicknameColor_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NicknameColor_Asset>, I>>(object: I): NicknameColor_Asset {
    const message = createBaseNicknameColor_Asset();
    message.name = object.name ?? '';
    message.colors = object.colors?.map(e => e) || [];
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    return message;
  }
};

function createBaseNicknameColor_AssetsEntry(): NicknameColor_AssetsEntry {
  return { key: '', value: undefined };
}

export const NicknameColor_AssetsEntry: MessageFns<NicknameColor_AssetsEntry> = {
  fromJSON(object: any): NicknameColor_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? NicknameColor_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NicknameColor_AssetsEntry>, I>>(base?: I): NicknameColor_AssetsEntry {
    return NicknameColor_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NicknameColor_AssetsEntry>, I>>(object: I): NicknameColor_AssetsEntry {
    const message = createBaseNicknameColor_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? NicknameColor_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseVip(): Vip {
  return { asset: undefined, assets: {}, id: 0 };
}

export const Vip: MessageFns<Vip> = {
  fromJSON(object: any): Vip {
    return {
      asset: isSet(object.asset) ? Vip_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: Vip_Asset }>((acc, [key, value]) => {
            acc[key] = Vip_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Vip>, I>>(base?: I): Vip {
    return Vip.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vip>, I>>(object: I): Vip {
    const message = createBaseVip();
    message.asset =
      object.asset !== undefined && object.asset !== null ? Vip_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: Vip_Asset }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Vip_Asset.fromPartial(value);
      }
      return acc;
    }, {});
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseVip_Asset(): Vip_Asset {
  return { level: '', image: undefined };
}

export const Vip_Asset: MessageFns<Vip_Asset> = {
  fromJSON(object: any): Vip_Asset {
    return {
      level: isSet(object.level) ? globalThis.String(object.level) : '',
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Vip_Asset>, I>>(base?: I): Vip_Asset {
    return Vip_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vip_Asset>, I>>(object: I): Vip_Asset {
    const message = createBaseVip_Asset();
    message.level = object.level ?? '';
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    return message;
  }
};

function createBaseVip_AssetsEntry(): Vip_AssetsEntry {
  return { key: '', value: undefined };
}

export const Vip_AssetsEntry: MessageFns<Vip_AssetsEntry> = {
  fromJSON(object: any): Vip_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Vip_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Vip_AssetsEntry>, I>>(base?: I): Vip_AssetsEntry {
    return Vip_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Vip_AssetsEntry>, I>>(object: I): Vip_AssetsEntry {
    const message = createBaseVip_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? Vip_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePrettyNumber(): PrettyNumber {
  return { asset: undefined, assets: {}, pretty_number_id: '' };
}

export const PrettyNumber: MessageFns<PrettyNumber> = {
  fromJSON(object: any): PrettyNumber {
    return {
      asset: isSet(object.asset) ? PrettyNumber_Asset.fromJSON(object.asset) : undefined,
      assets: isObject(object.assets)
        ? Object.entries(object.assets).reduce<{ [key: string]: PrettyNumber_Asset }>((acc, [key, value]) => {
            acc[key] = PrettyNumber_Asset.fromJSON(value);
            return acc;
          }, {})
        : {},
      pretty_number_id: isSet(object.pretty_number_id) ? globalThis.String(object.pretty_number_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PrettyNumber>, I>>(base?: I): PrettyNumber {
    return PrettyNumber.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrettyNumber>, I>>(object: I): PrettyNumber {
    const message = createBasePrettyNumber();
    message.asset =
      object.asset !== undefined && object.asset !== null ? PrettyNumber_Asset.fromPartial(object.asset) : undefined;
    message.assets = Object.entries(object.assets ?? {}).reduce<{ [key: string]: PrettyNumber_Asset }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = PrettyNumber_Asset.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.pretty_number_id = object.pretty_number_id ?? '';
    return message;
  }
};

function createBasePrettyNumber_Asset(): PrettyNumber_Asset {
  return { icon: undefined, image: undefined };
}

export const PrettyNumber_Asset: MessageFns<PrettyNumber_Asset> = {
  fromJSON(object: any): PrettyNumber_Asset {
    return {
      icon: isSet(object.icon) ? AssetInfo.fromJSON(object.icon) : undefined,
      image: isSet(object.image) ? AssetInfo.fromJSON(object.image) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PrettyNumber_Asset>, I>>(base?: I): PrettyNumber_Asset {
    return PrettyNumber_Asset.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrettyNumber_Asset>, I>>(object: I): PrettyNumber_Asset {
    const message = createBasePrettyNumber_Asset();
    message.icon = object.icon !== undefined && object.icon !== null ? AssetInfo.fromPartial(object.icon) : undefined;
    message.image =
      object.image !== undefined && object.image !== null ? AssetInfo.fromPartial(object.image) : undefined;
    return message;
  }
};

function createBasePrettyNumber_AssetsEntry(): PrettyNumber_AssetsEntry {
  return { key: '', value: undefined };
}

export const PrettyNumber_AssetsEntry: MessageFns<PrettyNumber_AssetsEntry> = {
  fromJSON(object: any): PrettyNumber_AssetsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? PrettyNumber_Asset.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PrettyNumber_AssetsEntry>, I>>(base?: I): PrettyNumber_AssetsEntry {
    return PrettyNumber_AssetsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrettyNumber_AssetsEntry>, I>>(object: I): PrettyNumber_AssetsEntry {
    const message = createBasePrettyNumber_AssetsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? PrettyNumber_Asset.fromPartial(object.value) : undefined;
    return message;
  }
};

/**
 * 特权资源接口
 * ServiceName: privilege-api
 */
export type PrivilegeV2Definition = typeof PrivilegeV2Definition;
export const PrivilegeV2Definition = {
  name: 'PrivilegeV2',
  fullName: 'api.privilege.v2.PrivilegeV2',
  methods: {
    /** 获取特权资源列表, 并不是自己拥有的而是后台上架了的. */
    listResource: {
      name: 'ListResource',
      requestType: ListResourceReq,
      requestStream: false,
      responseType: ListResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 获取资源分类列表 */
    listCategory: {
      name: 'ListCategory',
      requestType: ListCategoryReq,
      requestStream: false,
      responseType: ListCategoryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
