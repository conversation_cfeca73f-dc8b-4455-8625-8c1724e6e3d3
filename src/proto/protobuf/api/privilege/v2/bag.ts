// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/v2/bag.proto

/* eslint-disable */
import { Page } from '../../common/common';
import {
  Category,
  CategoryID,
  categoryIDFromJSON,
  EnterRoomBayWindow,
  Headwear,
  HomeAction,
  Medal,
  MicAperture,
  MiniCardBackground,
  NicknameColor,
  PrettyNumber,
  Resource,
  RoomBackground,
  RoomListBackground,
  ScreenBubble,
  TargetType,
  targetTypeFromJSON,
  Vehicle,
  Vip
} from './privilege';

export const protobufPackage = 'api.privilege.v2';

/** smicro:spath=gitit.cc/social/components-service/social-privilege/biz/bag/handler */

/** 背包使用中状态 */
export enum BagUsingStatus {
  /** BAG_USING_STATUS_NONE - 无意义 */
  BAG_USING_STATUS_NONE = 0,
  /** BAG_USING_STATUS_USING - 使用中 */
  BAG_USING_STATUS_USING = 1,
  /** BAG_USING_STATUS_DISUSE - 非使用中 */
  BAG_USING_STATUS_DISUSE = 2,
  UNRECOGNIZED = -1
}

export function bagUsingStatusFromJSON(object: any): BagUsingStatus {
  switch (object) {
    case 0:
    case 'BAG_USING_STATUS_NONE':
      return BagUsingStatus.BAG_USING_STATUS_NONE;
    case 1:
    case 'BAG_USING_STATUS_USING':
      return BagUsingStatus.BAG_USING_STATUS_USING;
    case 2:
    case 'BAG_USING_STATUS_DISUSE':
      return BagUsingStatus.BAG_USING_STATUS_DISUSE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BagUsingStatus.UNRECOGNIZED;
  }
}

export enum UseBagResourceErrCode {
  /** USE_BAG_RESOURCE_ERR_CODE_NONE - 无意义 */
  USE_BAG_RESOURCE_ERR_CODE_NONE = 0,
  /** USE_BAG_RESOURCE_ERR_CODE_EXPIRED - 已过期 */
  USE_BAG_RESOURCE_ERR_CODE_EXPIRED = 10,
  /** USE_BAG_RESOURCE_ERR_CODE_NOT_EXIST - 不存在 */
  USE_BAG_RESOURCE_ERR_CODE_NOT_EXIST = 20,
  /** USE_BAG_RESOURCE_ERR_CODE_NOT_ENOUGH - 数量不足 */
  USE_BAG_RESOURCE_ERR_CODE_NOT_ENOUGH = 30,
  UNRECOGNIZED = -1
}

export function useBagResourceErrCodeFromJSON(object: any): UseBagResourceErrCode {
  switch (object) {
    case 0:
    case 'USE_BAG_RESOURCE_ERR_CODE_NONE':
      return UseBagResourceErrCode.USE_BAG_RESOURCE_ERR_CODE_NONE;
    case 10:
    case 'USE_BAG_RESOURCE_ERR_CODE_EXPIRED':
      return UseBagResourceErrCode.USE_BAG_RESOURCE_ERR_CODE_EXPIRED;
    case 20:
    case 'USE_BAG_RESOURCE_ERR_CODE_NOT_EXIST':
      return UseBagResourceErrCode.USE_BAG_RESOURCE_ERR_CODE_NOT_EXIST;
    case 30:
    case 'USE_BAG_RESOURCE_ERR_CODE_NOT_ENOUGH':
      return UseBagResourceErrCode.USE_BAG_RESOURCE_ERR_CODE_NOT_ENOUGH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UseBagResourceErrCode.UNRECOGNIZED;
  }
}

export enum DisuseBagResourceErrCode {
  /** DISUSE_BAG_RESOURCE_ERR_CODE_NONE - 无意义 */
  DISUSE_BAG_RESOURCE_ERR_CODE_NONE = 0,
  /** DISUSE_BAG_RESOURCE_ERR_CODE_NOT_EXIST - 不存在 */
  DISUSE_BAG_RESOURCE_ERR_CODE_NOT_EXIST = 20,
  UNRECOGNIZED = -1
}

export function disuseBagResourceErrCodeFromJSON(object: any): DisuseBagResourceErrCode {
  switch (object) {
    case 0:
    case 'DISUSE_BAG_RESOURCE_ERR_CODE_NONE':
      return DisuseBagResourceErrCode.DISUSE_BAG_RESOURCE_ERR_CODE_NONE;
    case 20:
    case 'DISUSE_BAG_RESOURCE_ERR_CODE_NOT_EXIST':
      return DisuseBagResourceErrCode.DISUSE_BAG_RESOURCE_ERR_CODE_NOT_EXIST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return DisuseBagResourceErrCode.UNRECOGNIZED;
  }
}

/** 背包分类 */
export interface BagCategory {
  /** 分类标识 */
  category_id: CategoryID;
  /** 分类信息 */
  category: Category | undefined;
}

export interface ListBagCategoryReq {}

export interface ListBagCategoryRsp {
  /** 背包分类列表 */
  bag_categories: BagCategory[];
}

/** 背包剩余使用时长 */
export interface BagRemainPeriod {
  /** 商品周期-秒 */
  period_secs: number;
  /** 用户语言对应的文案 */
  show_text: string;
  /** 多语言对应文案 */
  show_texts: { [key: string]: string };
}

export interface BagRemainPeriod_ShowTextsEntry {
  key: string;
  value: string;
}

/** 背包资源 */
export interface BagResource {
  /** 背包ID */
  bag_id: number;
  /** 归属类型 */
  target_type: TargetType;
  /** 归属ID, 例如: 用户ID / 房间ID / ... */
  target_id: number;
  /** 特权资源ID */
  resource_id: number;
  /** 特权资源 */
  resource: Resource | undefined;
  /** 剩余数量 */
  remain_num: number;
  /** 剩余使用时长 */
  remain_period: BagRemainPeriod | undefined;
  /** 分类标识 */
  category_id: CategoryID;
  /** 子分类标识 */
  sub_category_id: CategoryID;
  /** 使用状态 */
  is_using: BagUsingStatus;
  /** 使用位置 */
  using_pos: number;
}

export interface ListBagResourceReq {
  page: Page | undefined;
  /** 分类标识 */
  category_id: CategoryID;
  /** 二级分类标识(目前没用) */
  sub_category_id: CategoryID;
  /** 资源归属类型 */
  target_type: TargetType;
}

export interface ListBagResourceRsp {
  page: Page | undefined;
  /** 背包资源列表 */
  bag_resources: BagResource[];
}

export interface UseBagResourceReq {
  /** 背包ID */
  bag_id: number;
  /** 使用数量, 对于时长类型的可以不传. */
  num: number;
  /** 使用位置, 佩戴勋章时需要. */
  pos: number;
  /** 分类标识 */
  category_id: CategoryID;
}

export interface UseBagResourceRsp {}

export interface DisuseBagResourceReq {
  /** 背包ID */
  bag_id: number;
  /** 分类标识 */
  category_id: CategoryID;
}

export interface DisuseBagResourceRsp {}

/** 使用中的勋章, 勋章比较特殊, 可以佩戴多个. */
export interface UsingMedal {
  /** 勋章信息 */
  medal: Medal | undefined;
  /** 穿戴位置 */
  using_pos: number;
}

/** 使用中的特权 */
export interface UsingPrivilege {
  /** 头像框 */
  headwear: Headwear | undefined;
  /** 座驾 */
  vehicle: Vehicle | undefined;
  /** 公屏气泡 */
  bubble: ScreenBubble | undefined;
  /** 房间背景 */
  room_bg: RoomBackground | undefined;
  /** 麦位光圈 */
  mic_aperture: MicAperture | undefined;
  /** 主页动效 */
  home_action: HomeAction | undefined;
  /** 勋章, 勋章比较特殊, 可以佩戴多个. */
  medals: UsingMedal[];
  /** mini资料背景 */
  mini_card_bg: MiniCardBackground | undefined;
  /** 进房飘窗 */
  bay_window: EnterRoomBayWindow | undefined;
  /** 房间列表背景 */
  room_list_bg: RoomListBackground | undefined;
  /** 用户靓号 */
  user_pretty_number: PrettyNumber | undefined;
  /** 房间靓号 */
  room_pretty_number: PrettyNumber | undefined;
  /** 昵称颜色 */
  nickname_color: NicknameColor | undefined;
  /** vip等级 */
  vip: Vip | undefined;
}

export interface GetUsingPrivilegeReq {
  /** 归属类型 */
  target_type: TargetType;
  /** 归属ID */
  target_id: number;
  /** 特权分类标识, 传 NONE 表示查询所有特权分类. */
  category_id: CategoryID;
}

export interface GetUsingPrivilegeRsp {
  /** 是否用有该权益 */
  has_privilege: boolean;
  /** 背包资源 */
  using_privilege: UsingPrivilege | undefined;
}

export interface BatchGetUsingPrivilegeReq {
  /** 归属类型 */
  target_type: TargetType;
  /** 归属ID: uid / room_id */
  target_ids: number[];
  /** 特权分类标识, 传空数组表示查询所有特权分类. */
  category_ids: CategoryID[];
}

export interface BatchGetUsingPrivilegeRsp {
  /** map<uid / room_id, 使用中的背包资源> */
  target_using_privileges: { [key: number]: UsingPrivilege };
}

export interface BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry {
  key: number;
  value: UsingPrivilege | undefined;
}

function createBaseBagCategory(): BagCategory {
  return { category_id: 0, category: undefined };
}

export const BagCategory: MessageFns<BagCategory> = {
  fromJSON(object: any): BagCategory {
    return {
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      category: isSet(object.category) ? Category.fromJSON(object.category) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BagCategory>, I>>(base?: I): BagCategory {
    return BagCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagCategory>, I>>(object: I): BagCategory {
    const message = createBaseBagCategory();
    message.category_id = object.category_id ?? 0;
    message.category =
      object.category !== undefined && object.category !== null ? Category.fromPartial(object.category) : undefined;
    return message;
  }
};

function createBaseListBagCategoryReq(): ListBagCategoryReq {
  return {};
}

export const ListBagCategoryReq: MessageFns<ListBagCategoryReq> = {
  fromJSON(_: any): ListBagCategoryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListBagCategoryReq>, I>>(base?: I): ListBagCategoryReq {
    return ListBagCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagCategoryReq>, I>>(_: I): ListBagCategoryReq {
    const message = createBaseListBagCategoryReq();
    return message;
  }
};

function createBaseListBagCategoryRsp(): ListBagCategoryRsp {
  return { bag_categories: [] };
}

export const ListBagCategoryRsp: MessageFns<ListBagCategoryRsp> = {
  fromJSON(object: any): ListBagCategoryRsp {
    return {
      bag_categories: globalThis.Array.isArray(object?.bag_categories)
        ? object.bag_categories.map((e: any) => BagCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListBagCategoryRsp>, I>>(base?: I): ListBagCategoryRsp {
    return ListBagCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagCategoryRsp>, I>>(object: I): ListBagCategoryRsp {
    const message = createBaseListBagCategoryRsp();
    message.bag_categories = object.bag_categories?.map(e => BagCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBagRemainPeriod(): BagRemainPeriod {
  return { period_secs: 0, show_text: '', show_texts: {} };
}

export const BagRemainPeriod: MessageFns<BagRemainPeriod> = {
  fromJSON(object: any): BagRemainPeriod {
    return {
      period_secs: isSet(object.period_secs) ? globalThis.Number(object.period_secs) : 0,
      show_text: isSet(object.show_text) ? globalThis.String(object.show_text) : '',
      show_texts: isObject(object.show_texts)
        ? Object.entries(object.show_texts).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BagRemainPeriod>, I>>(base?: I): BagRemainPeriod {
    return BagRemainPeriod.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagRemainPeriod>, I>>(object: I): BagRemainPeriod {
    const message = createBaseBagRemainPeriod();
    message.period_secs = object.period_secs ?? 0;
    message.show_text = object.show_text ?? '';
    message.show_texts = Object.entries(object.show_texts ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBagRemainPeriod_ShowTextsEntry(): BagRemainPeriod_ShowTextsEntry {
  return { key: '', value: '' };
}

export const BagRemainPeriod_ShowTextsEntry: MessageFns<BagRemainPeriod_ShowTextsEntry> = {
  fromJSON(object: any): BagRemainPeriod_ShowTextsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<BagRemainPeriod_ShowTextsEntry>, I>>(base?: I): BagRemainPeriod_ShowTextsEntry {
    return BagRemainPeriod_ShowTextsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagRemainPeriod_ShowTextsEntry>, I>>(
    object: I
  ): BagRemainPeriod_ShowTextsEntry {
    const message = createBaseBagRemainPeriod_ShowTextsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseBagResource(): BagResource {
  return {
    bag_id: 0,
    target_type: 0,
    target_id: 0,
    resource_id: 0,
    resource: undefined,
    remain_num: 0,
    remain_period: undefined,
    category_id: 0,
    sub_category_id: 0,
    is_using: 0,
    using_pos: 0
  };
}

export const BagResource: MessageFns<BagResource> = {
  fromJSON(object: any): BagResource {
    return {
      bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0,
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      target_id: isSet(object.target_id) ? globalThis.Number(object.target_id) : 0,
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      resource: isSet(object.resource) ? Resource.fromJSON(object.resource) : undefined,
      remain_num: isSet(object.remain_num) ? globalThis.Number(object.remain_num) : 0,
      remain_period: isSet(object.remain_period) ? BagRemainPeriod.fromJSON(object.remain_period) : undefined,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0,
      is_using: isSet(object.is_using) ? bagUsingStatusFromJSON(object.is_using) : 0,
      using_pos: isSet(object.using_pos) ? globalThis.Number(object.using_pos) : 0
    };
  },

  create<I extends Exact<DeepPartial<BagResource>, I>>(base?: I): BagResource {
    return BagResource.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagResource>, I>>(object: I): BagResource {
    const message = createBaseBagResource();
    message.bag_id = object.bag_id ?? 0;
    message.target_type = object.target_type ?? 0;
    message.target_id = object.target_id ?? 0;
    message.resource_id = object.resource_id ?? 0;
    message.resource =
      object.resource !== undefined && object.resource !== null ? Resource.fromPartial(object.resource) : undefined;
    message.remain_num = object.remain_num ?? 0;
    message.remain_period =
      object.remain_period !== undefined && object.remain_period !== null
        ? BagRemainPeriod.fromPartial(object.remain_period)
        : undefined;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.is_using = object.is_using ?? 0;
    message.using_pos = object.using_pos ?? 0;
    return message;
  }
};

function createBaseListBagResourceReq(): ListBagResourceReq {
  return { page: undefined, category_id: 0, sub_category_id: 0, target_type: 0 };
}

export const ListBagResourceReq: MessageFns<ListBagResourceReq> = {
  fromJSON(object: any): ListBagResourceReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0,
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBagResourceReq>, I>>(base?: I): ListBagResourceReq {
    return ListBagResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagResourceReq>, I>>(object: I): ListBagResourceReq {
    const message = createBaseListBagResourceReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.target_type = object.target_type ?? 0;
    return message;
  }
};

function createBaseListBagResourceRsp(): ListBagResourceRsp {
  return { page: undefined, bag_resources: [] };
}

export const ListBagResourceRsp: MessageFns<ListBagResourceRsp> = {
  fromJSON(object: any): ListBagResourceRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      bag_resources: globalThis.Array.isArray(object?.bag_resources)
        ? object.bag_resources.map((e: any) => BagResource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListBagResourceRsp>, I>>(base?: I): ListBagResourceRsp {
    return ListBagResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagResourceRsp>, I>>(object: I): ListBagResourceRsp {
    const message = createBaseListBagResourceRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.bag_resources = object.bag_resources?.map(e => BagResource.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUseBagResourceReq(): UseBagResourceReq {
  return { bag_id: 0, num: 0, pos: 0, category_id: 0 };
}

export const UseBagResourceReq: MessageFns<UseBagResourceReq> = {
  fromJSON(object: any): UseBagResourceReq {
    return {
      bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      pos: isSet(object.pos) ? globalThis.Number(object.pos) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<UseBagResourceReq>, I>>(base?: I): UseBagResourceReq {
    return UseBagResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UseBagResourceReq>, I>>(object: I): UseBagResourceReq {
    const message = createBaseUseBagResourceReq();
    message.bag_id = object.bag_id ?? 0;
    message.num = object.num ?? 0;
    message.pos = object.pos ?? 0;
    message.category_id = object.category_id ?? 0;
    return message;
  }
};

function createBaseUseBagResourceRsp(): UseBagResourceRsp {
  return {};
}

export const UseBagResourceRsp: MessageFns<UseBagResourceRsp> = {
  fromJSON(_: any): UseBagResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UseBagResourceRsp>, I>>(base?: I): UseBagResourceRsp {
    return UseBagResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UseBagResourceRsp>, I>>(_: I): UseBagResourceRsp {
    const message = createBaseUseBagResourceRsp();
    return message;
  }
};

function createBaseDisuseBagResourceReq(): DisuseBagResourceReq {
  return { bag_id: 0, category_id: 0 };
}

export const DisuseBagResourceReq: MessageFns<DisuseBagResourceReq> = {
  fromJSON(object: any): DisuseBagResourceReq {
    return {
      bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<DisuseBagResourceReq>, I>>(base?: I): DisuseBagResourceReq {
    return DisuseBagResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisuseBagResourceReq>, I>>(object: I): DisuseBagResourceReq {
    const message = createBaseDisuseBagResourceReq();
    message.bag_id = object.bag_id ?? 0;
    message.category_id = object.category_id ?? 0;
    return message;
  }
};

function createBaseDisuseBagResourceRsp(): DisuseBagResourceRsp {
  return {};
}

export const DisuseBagResourceRsp: MessageFns<DisuseBagResourceRsp> = {
  fromJSON(_: any): DisuseBagResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DisuseBagResourceRsp>, I>>(base?: I): DisuseBagResourceRsp {
    return DisuseBagResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisuseBagResourceRsp>, I>>(_: I): DisuseBagResourceRsp {
    const message = createBaseDisuseBagResourceRsp();
    return message;
  }
};

function createBaseUsingMedal(): UsingMedal {
  return { medal: undefined, using_pos: 0 };
}

export const UsingMedal: MessageFns<UsingMedal> = {
  fromJSON(object: any): UsingMedal {
    return {
      medal: isSet(object.medal) ? Medal.fromJSON(object.medal) : undefined,
      using_pos: isSet(object.using_pos) ? globalThis.Number(object.using_pos) : 0
    };
  },

  create<I extends Exact<DeepPartial<UsingMedal>, I>>(base?: I): UsingMedal {
    return UsingMedal.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UsingMedal>, I>>(object: I): UsingMedal {
    const message = createBaseUsingMedal();
    message.medal = object.medal !== undefined && object.medal !== null ? Medal.fromPartial(object.medal) : undefined;
    message.using_pos = object.using_pos ?? 0;
    return message;
  }
};

function createBaseUsingPrivilege(): UsingPrivilege {
  return {
    headwear: undefined,
    vehicle: undefined,
    bubble: undefined,
    room_bg: undefined,
    mic_aperture: undefined,
    home_action: undefined,
    medals: [],
    mini_card_bg: undefined,
    bay_window: undefined,
    room_list_bg: undefined,
    user_pretty_number: undefined,
    room_pretty_number: undefined,
    nickname_color: undefined,
    vip: undefined
  };
}

export const UsingPrivilege: MessageFns<UsingPrivilege> = {
  fromJSON(object: any): UsingPrivilege {
    return {
      headwear: isSet(object.headwear) ? Headwear.fromJSON(object.headwear) : undefined,
      vehicle: isSet(object.vehicle) ? Vehicle.fromJSON(object.vehicle) : undefined,
      bubble: isSet(object.bubble) ? ScreenBubble.fromJSON(object.bubble) : undefined,
      room_bg: isSet(object.room_bg) ? RoomBackground.fromJSON(object.room_bg) : undefined,
      mic_aperture: isSet(object.mic_aperture) ? MicAperture.fromJSON(object.mic_aperture) : undefined,
      home_action: isSet(object.home_action) ? HomeAction.fromJSON(object.home_action) : undefined,
      medals: globalThis.Array.isArray(object?.medals) ? object.medals.map((e: any) => UsingMedal.fromJSON(e)) : [],
      mini_card_bg: isSet(object.mini_card_bg) ? MiniCardBackground.fromJSON(object.mini_card_bg) : undefined,
      bay_window: isSet(object.bay_window) ? EnterRoomBayWindow.fromJSON(object.bay_window) : undefined,
      room_list_bg: isSet(object.room_list_bg) ? RoomListBackground.fromJSON(object.room_list_bg) : undefined,
      user_pretty_number: isSet(object.user_pretty_number)
        ? PrettyNumber.fromJSON(object.user_pretty_number)
        : undefined,
      room_pretty_number: isSet(object.room_pretty_number)
        ? PrettyNumber.fromJSON(object.room_pretty_number)
        : undefined,
      nickname_color: isSet(object.nickname_color) ? NicknameColor.fromJSON(object.nickname_color) : undefined,
      vip: isSet(object.vip) ? Vip.fromJSON(object.vip) : undefined
    };
  },

  create<I extends Exact<DeepPartial<UsingPrivilege>, I>>(base?: I): UsingPrivilege {
    return UsingPrivilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UsingPrivilege>, I>>(object: I): UsingPrivilege {
    const message = createBaseUsingPrivilege();
    message.headwear =
      object.headwear !== undefined && object.headwear !== null ? Headwear.fromPartial(object.headwear) : undefined;
    message.vehicle =
      object.vehicle !== undefined && object.vehicle !== null ? Vehicle.fromPartial(object.vehicle) : undefined;
    message.bubble =
      object.bubble !== undefined && object.bubble !== null ? ScreenBubble.fromPartial(object.bubble) : undefined;
    message.room_bg =
      object.room_bg !== undefined && object.room_bg !== null ? RoomBackground.fromPartial(object.room_bg) : undefined;
    message.mic_aperture =
      object.mic_aperture !== undefined && object.mic_aperture !== null
        ? MicAperture.fromPartial(object.mic_aperture)
        : undefined;
    message.home_action =
      object.home_action !== undefined && object.home_action !== null
        ? HomeAction.fromPartial(object.home_action)
        : undefined;
    message.medals = object.medals?.map(e => UsingMedal.fromPartial(e)) || [];
    message.mini_card_bg =
      object.mini_card_bg !== undefined && object.mini_card_bg !== null
        ? MiniCardBackground.fromPartial(object.mini_card_bg)
        : undefined;
    message.bay_window =
      object.bay_window !== undefined && object.bay_window !== null
        ? EnterRoomBayWindow.fromPartial(object.bay_window)
        : undefined;
    message.room_list_bg =
      object.room_list_bg !== undefined && object.room_list_bg !== null
        ? RoomListBackground.fromPartial(object.room_list_bg)
        : undefined;
    message.user_pretty_number =
      object.user_pretty_number !== undefined && object.user_pretty_number !== null
        ? PrettyNumber.fromPartial(object.user_pretty_number)
        : undefined;
    message.room_pretty_number =
      object.room_pretty_number !== undefined && object.room_pretty_number !== null
        ? PrettyNumber.fromPartial(object.room_pretty_number)
        : undefined;
    message.nickname_color =
      object.nickname_color !== undefined && object.nickname_color !== null
        ? NicknameColor.fromPartial(object.nickname_color)
        : undefined;
    message.vip = object.vip !== undefined && object.vip !== null ? Vip.fromPartial(object.vip) : undefined;
    return message;
  }
};

function createBaseGetUsingPrivilegeReq(): GetUsingPrivilegeReq {
  return { target_type: 0, target_id: 0, category_id: 0 };
}

export const GetUsingPrivilegeReq: MessageFns<GetUsingPrivilegeReq> = {
  fromJSON(object: any): GetUsingPrivilegeReq {
    return {
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      target_id: isSet(object.target_id) ? globalThis.Number(object.target_id) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetUsingPrivilegeReq>, I>>(base?: I): GetUsingPrivilegeReq {
    return GetUsingPrivilegeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUsingPrivilegeReq>, I>>(object: I): GetUsingPrivilegeReq {
    const message = createBaseGetUsingPrivilegeReq();
    message.target_type = object.target_type ?? 0;
    message.target_id = object.target_id ?? 0;
    message.category_id = object.category_id ?? 0;
    return message;
  }
};

function createBaseGetUsingPrivilegeRsp(): GetUsingPrivilegeRsp {
  return { has_privilege: false, using_privilege: undefined };
}

export const GetUsingPrivilegeRsp: MessageFns<GetUsingPrivilegeRsp> = {
  fromJSON(object: any): GetUsingPrivilegeRsp {
    return {
      has_privilege: isSet(object.has_privilege) ? globalThis.Boolean(object.has_privilege) : false,
      using_privilege: isSet(object.using_privilege) ? UsingPrivilege.fromJSON(object.using_privilege) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetUsingPrivilegeRsp>, I>>(base?: I): GetUsingPrivilegeRsp {
    return GetUsingPrivilegeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUsingPrivilegeRsp>, I>>(object: I): GetUsingPrivilegeRsp {
    const message = createBaseGetUsingPrivilegeRsp();
    message.has_privilege = object.has_privilege ?? false;
    message.using_privilege =
      object.using_privilege !== undefined && object.using_privilege !== null
        ? UsingPrivilege.fromPartial(object.using_privilege)
        : undefined;
    return message;
  }
};

function createBaseBatchGetUsingPrivilegeReq(): BatchGetUsingPrivilegeReq {
  return { target_type: 0, target_ids: [], category_ids: [] };
}

export const BatchGetUsingPrivilegeReq: MessageFns<BatchGetUsingPrivilegeReq> = {
  fromJSON(object: any): BatchGetUsingPrivilegeReq {
    return {
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      target_ids: globalThis.Array.isArray(object?.target_ids)
        ? object.target_ids.map((e: any) => globalThis.Number(e))
        : [],
      category_ids: globalThis.Array.isArray(object?.category_ids)
        ? object.category_ids.map((e: any) => categoryIDFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUsingPrivilegeReq>, I>>(base?: I): BatchGetUsingPrivilegeReq {
    return BatchGetUsingPrivilegeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUsingPrivilegeReq>, I>>(object: I): BatchGetUsingPrivilegeReq {
    const message = createBaseBatchGetUsingPrivilegeReq();
    message.target_type = object.target_type ?? 0;
    message.target_ids = object.target_ids?.map(e => e) || [];
    message.category_ids = object.category_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUsingPrivilegeRsp(): BatchGetUsingPrivilegeRsp {
  return { target_using_privileges: {} };
}

export const BatchGetUsingPrivilegeRsp: MessageFns<BatchGetUsingPrivilegeRsp> = {
  fromJSON(object: any): BatchGetUsingPrivilegeRsp {
    return {
      target_using_privileges: isObject(object.target_using_privileges)
        ? Object.entries(object.target_using_privileges).reduce<{ [key: number]: UsingPrivilege }>(
            (acc, [key, value]) => {
              acc[globalThis.Number(key)] = UsingPrivilege.fromJSON(value);
              return acc;
            },
            {}
          )
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUsingPrivilegeRsp>, I>>(base?: I): BatchGetUsingPrivilegeRsp {
    return BatchGetUsingPrivilegeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUsingPrivilegeRsp>, I>>(object: I): BatchGetUsingPrivilegeRsp {
    const message = createBaseBatchGetUsingPrivilegeRsp();
    message.target_using_privileges = Object.entries(object.target_using_privileges ?? {}).reduce<{
      [key: number]: UsingPrivilege;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = UsingPrivilege.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry(): BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry {
  return { key: 0, value: undefined };
}

export const BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry: MessageFns<BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry> =
  {
    fromJSON(object: any): BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? UsingPrivilege.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry>, I>>(
      base?: I
    ): BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry {
      return BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry>, I>>(
      object: I
    ): BatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry {
      const message = createBaseBatchGetUsingPrivilegeRsp_TargetUsingPrivilegesEntry();
      message.key = object.key ?? 0;
      message.value =
        object.value !== undefined && object.value !== null ? UsingPrivilege.fromPartial(object.value) : undefined;
      return message;
    }
  };

/**
 * 用户背包接口
 * ServiceName: privilege-api
 */
export type BagV2Definition = typeof BagV2Definition;
export const BagV2Definition = {
  name: 'BagV2',
  fullName: 'api.privilege.v2.BagV2',
  methods: {
    /** 背包分类列表 */
    listBagCategory: {
      name: 'ListBagCategory',
      requestType: ListBagCategoryReq,
      requestStream: false,
      responseType: ListBagCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 背包资源列表 */
    listBagResource: {
      name: 'ListBagResource',
      requestType: ListBagResourceReq,
      requestStream: false,
      responseType: ListBagResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 穿戴 / 使用背包资源 */
    useBagResource: {
      name: 'UseBagResource',
      requestType: UseBagResourceReq,
      requestStream: false,
      responseType: UseBagResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 解除穿戴 / 使用背包资源 */
    disuseBagResource: {
      name: 'DisuseBagResource',
      requestType: DisuseBagResourceReq,
      requestStream: false,
      responseType: DisuseBagResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 查询使用中权益(单用户) */
    getUsingPrivilege: {
      name: 'GetUsingPrivilege',
      requestType: GetUsingPrivilegeReq,
      requestStream: false,
      responseType: GetUsingPrivilegeRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询使用中权益(多用户) */
    batchGetUsingPrivilege: {
      name: 'BatchGetUsingPrivilege',
      requestType: BatchGetUsingPrivilegeReq,
      requestStream: false,
      responseType: BatchGetUsingPrivilegeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
