// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/bag.proto

/* eslint-disable */
import { Page } from '../common/common';
import { CategoryAlias, categoryAliasFromJSON } from './enum';
import { BagPrivilege } from './privilege';

export const protobufPackage = 'api.privilege';

/** 背包商品 */
export interface BagGoods {
  /** 背包道具ID bagID */
  id: number;
  /** 商品分类ID */
  category_id: number;
  /** 商品子类ID */
  sub_category_id: number;
  /** 商品分类标识 */
  category_alias: CategoryAlias;
  /** 商品子分类标识 */
  sub_category_alias: CategoryAlias;
  /** 商品名称 */
  name: string;
  /** 商品数量 */
  num: number;
  /** 商品缩略图 */
  thumb: string;
  /** 商品图片 */
  image: string;
  /** 多媒体类型 */
  media_type: string;
  /** 多媒体地址 */
  media_url: string;
  /** 商品描述 */
  brief: string;
  /** 对应的商城good_id */
  good_id: number;
  /** 是否使用中 1-否  2-是 */
  is_using: number;
  /** 是否使用过 1-否  2-是 */
  is_used: number;
  /** 截止有效期 -1 永久有效 */
  period_end: number;
  /** 截止有效期展示 */
  period_end_text: string;
  /** 创建时间 */
  ctime: number;
  /** 更新时间 */
  utime: number;
}

/** 商城商品列表请求 */
export interface ListBagGoodsReq {
  page: Page | undefined;
  /** 分类ID, 优先使用分类ID, 分类ID为空使用分类alias */
  category_id: number;
  /** 分类标识 */
  category_alias: CategoryAlias;
  /** 子分类ID */
  sub_category_id: number;
  /** 二级分类标识 */
  sub_category_alias: CategoryAlias;
}

/** 商城商品列表响应 */
export interface ListBagGoodsRsp {
  /** 背包商品列表 */
  goods: BagGoods[];
}

export interface UseBagGoodsReq {
  bag_id: number;
  num: number;
}

export interface UseBagGoodsRsp {}

export interface DisuseBagGoodsReq {
  bag_id: number;
}

export interface DisuseBagGoodsRsp {}

export interface GetUsingPrivilegeReq {
  /** 背包权益标识：一级/二级分类 */
  privilege_alias: CategoryAlias;
}

export interface GetUsingPrivilegeRsp {
  /** 是否有该权益 */
  has_privilege: boolean;
  /** 权益详情 */
  privilege: BagPrivilege | undefined;
}

function createBaseBagGoods(): BagGoods {
  return {
    id: 0,
    category_id: 0,
    sub_category_id: 0,
    category_alias: 0,
    sub_category_alias: 0,
    name: '',
    num: 0,
    thumb: '',
    image: '',
    media_type: '',
    media_url: '',
    brief: '',
    good_id: 0,
    is_using: 0,
    is_used: 0,
    period_end: 0,
    period_end_text: '',
    ctime: 0,
    utime: 0
  };
}

export const BagGoods: MessageFns<BagGoods> = {
  fromJSON(object: any): BagGoods {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.Number(object.sub_category_id) : 0,
      category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0,
      sub_category_alias: isSet(object.sub_category_alias) ? categoryAliasFromJSON(object.sub_category_alias) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      thumb: isSet(object.thumb) ? globalThis.String(object.thumb) : '',
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      media_type: isSet(object.media_type) ? globalThis.String(object.media_type) : '',
      media_url: isSet(object.media_url) ? globalThis.String(object.media_url) : '',
      brief: isSet(object.brief) ? globalThis.String(object.brief) : '',
      good_id: isSet(object.good_id) ? globalThis.Number(object.good_id) : 0,
      is_using: isSet(object.is_using) ? globalThis.Number(object.is_using) : 0,
      is_used: isSet(object.is_used) ? globalThis.Number(object.is_used) : 0,
      period_end: isSet(object.period_end) ? globalThis.Number(object.period_end) : 0,
      period_end_text: isSet(object.period_end_text) ? globalThis.String(object.period_end_text) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0
    };
  },

  create<I extends Exact<DeepPartial<BagGoods>, I>>(base?: I): BagGoods {
    return BagGoods.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BagGoods>, I>>(object: I): BagGoods {
    const message = createBaseBagGoods();
    message.id = object.id ?? 0;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.category_alias = object.category_alias ?? 0;
    message.sub_category_alias = object.sub_category_alias ?? 0;
    message.name = object.name ?? '';
    message.num = object.num ?? 0;
    message.thumb = object.thumb ?? '';
    message.image = object.image ?? '';
    message.media_type = object.media_type ?? '';
    message.media_url = object.media_url ?? '';
    message.brief = object.brief ?? '';
    message.good_id = object.good_id ?? 0;
    message.is_using = object.is_using ?? 0;
    message.is_used = object.is_used ?? 0;
    message.period_end = object.period_end ?? 0;
    message.period_end_text = object.period_end_text ?? '';
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    return message;
  }
};

function createBaseListBagGoodsReq(): ListBagGoodsReq {
  return { page: undefined, category_id: 0, category_alias: 0, sub_category_id: 0, sub_category_alias: 0 };
}

export const ListBagGoodsReq: MessageFns<ListBagGoodsReq> = {
  fromJSON(object: any): ListBagGoodsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0,
      sub_category_id: isSet(object.sub_category_id) ? globalThis.Number(object.sub_category_id) : 0,
      sub_category_alias: isSet(object.sub_category_alias) ? categoryAliasFromJSON(object.sub_category_alias) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBagGoodsReq>, I>>(base?: I): ListBagGoodsReq {
    return ListBagGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagGoodsReq>, I>>(object: I): ListBagGoodsReq {
    const message = createBaseListBagGoodsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_id = object.category_id ?? 0;
    message.category_alias = object.category_alias ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.sub_category_alias = object.sub_category_alias ?? 0;
    return message;
  }
};

function createBaseListBagGoodsRsp(): ListBagGoodsRsp {
  return { goods: [] };
}

export const ListBagGoodsRsp: MessageFns<ListBagGoodsRsp> = {
  fromJSON(object: any): ListBagGoodsRsp {
    return { goods: globalThis.Array.isArray(object?.goods) ? object.goods.map((e: any) => BagGoods.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListBagGoodsRsp>, I>>(base?: I): ListBagGoodsRsp {
    return ListBagGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBagGoodsRsp>, I>>(object: I): ListBagGoodsRsp {
    const message = createBaseListBagGoodsRsp();
    message.goods = object.goods?.map(e => BagGoods.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUseBagGoodsReq(): UseBagGoodsReq {
  return { bag_id: 0, num: 0 };
}

export const UseBagGoodsReq: MessageFns<UseBagGoodsReq> = {
  fromJSON(object: any): UseBagGoodsReq {
    return {
      bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0
    };
  },

  create<I extends Exact<DeepPartial<UseBagGoodsReq>, I>>(base?: I): UseBagGoodsReq {
    return UseBagGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UseBagGoodsReq>, I>>(object: I): UseBagGoodsReq {
    const message = createBaseUseBagGoodsReq();
    message.bag_id = object.bag_id ?? 0;
    message.num = object.num ?? 0;
    return message;
  }
};

function createBaseUseBagGoodsRsp(): UseBagGoodsRsp {
  return {};
}

export const UseBagGoodsRsp: MessageFns<UseBagGoodsRsp> = {
  fromJSON(_: any): UseBagGoodsRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UseBagGoodsRsp>, I>>(base?: I): UseBagGoodsRsp {
    return UseBagGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UseBagGoodsRsp>, I>>(_: I): UseBagGoodsRsp {
    const message = createBaseUseBagGoodsRsp();
    return message;
  }
};

function createBaseDisuseBagGoodsReq(): DisuseBagGoodsReq {
  return { bag_id: 0 };
}

export const DisuseBagGoodsReq: MessageFns<DisuseBagGoodsReq> = {
  fromJSON(object: any): DisuseBagGoodsReq {
    return { bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DisuseBagGoodsReq>, I>>(base?: I): DisuseBagGoodsReq {
    return DisuseBagGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisuseBagGoodsReq>, I>>(object: I): DisuseBagGoodsReq {
    const message = createBaseDisuseBagGoodsReq();
    message.bag_id = object.bag_id ?? 0;
    return message;
  }
};

function createBaseDisuseBagGoodsRsp(): DisuseBagGoodsRsp {
  return {};
}

export const DisuseBagGoodsRsp: MessageFns<DisuseBagGoodsRsp> = {
  fromJSON(_: any): DisuseBagGoodsRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DisuseBagGoodsRsp>, I>>(base?: I): DisuseBagGoodsRsp {
    return DisuseBagGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DisuseBagGoodsRsp>, I>>(_: I): DisuseBagGoodsRsp {
    const message = createBaseDisuseBagGoodsRsp();
    return message;
  }
};

function createBaseGetUsingPrivilegeReq(): GetUsingPrivilegeReq {
  return { privilege_alias: 0 };
}

export const GetUsingPrivilegeReq: MessageFns<GetUsingPrivilegeReq> = {
  fromJSON(object: any): GetUsingPrivilegeReq {
    return { privilege_alias: isSet(object.privilege_alias) ? categoryAliasFromJSON(object.privilege_alias) : 0 };
  },

  create<I extends Exact<DeepPartial<GetUsingPrivilegeReq>, I>>(base?: I): GetUsingPrivilegeReq {
    return GetUsingPrivilegeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUsingPrivilegeReq>, I>>(object: I): GetUsingPrivilegeReq {
    const message = createBaseGetUsingPrivilegeReq();
    message.privilege_alias = object.privilege_alias ?? 0;
    return message;
  }
};

function createBaseGetUsingPrivilegeRsp(): GetUsingPrivilegeRsp {
  return { has_privilege: false, privilege: undefined };
}

export const GetUsingPrivilegeRsp: MessageFns<GetUsingPrivilegeRsp> = {
  fromJSON(object: any): GetUsingPrivilegeRsp {
    return {
      has_privilege: isSet(object.has_privilege) ? globalThis.Boolean(object.has_privilege) : false,
      privilege: isSet(object.privilege) ? BagPrivilege.fromJSON(object.privilege) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetUsingPrivilegeRsp>, I>>(base?: I): GetUsingPrivilegeRsp {
    return GetUsingPrivilegeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUsingPrivilegeRsp>, I>>(object: I): GetUsingPrivilegeRsp {
    const message = createBaseGetUsingPrivilegeRsp();
    message.has_privilege = object.has_privilege ?? false;
    message.privilege =
      object.privilege !== undefined && object.privilege !== null
        ? BagPrivilege.fromPartial(object.privilege)
        : undefined;
    return message;
  }
};

/** ServiceName: privilege-api */
export type BagApiDefinition = typeof BagApiDefinition;
export const BagApiDefinition = {
  name: 'BagApi',
  fullName: 'api.privilege.BagApi',
  methods: {
    /** 背包商品列表 */
    listBagGoods: {
      name: 'ListBagGoods',
      requestType: ListBagGoodsReq,
      requestStream: false,
      responseType: ListBagGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 使用背包物品 */
    useBagGoods: {
      name: 'UseBagGoods',
      requestType: UseBagGoodsReq,
      requestStream: false,
      responseType: UseBagGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 解除使用 */
    disuseBagGoods: {
      name: 'DisuseBagGoods',
      requestType: DisuseBagGoodsReq,
      requestStream: false,
      responseType: DisuseBagGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 查询使用中权益 */
    getUsingPrivilege: {
      name: 'GetUsingPrivilege',
      requestType: GetUsingPrivilegeReq,
      requestStream: false,
      responseType: GetUsingPrivilegeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
