// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/category.proto

/* eslint-disable */
import { CategoryAlias, categoryAliasFromJSON } from './enum';

export const protobufPackage = 'api.privilege';

/** 商品分类 */
export interface Category {
  /** 分类ID */
  id: number;
  /** 分类 */
  alias: CategoryAlias;
  /** 分类名称 */
  name: string;
  /** 分类图标 */
  icon: string;
  /** 父类ID */
  parent_id: number;
}

export interface ListCategoryReq {
  /** 一级分类标识 */
  category_alias: CategoryAlias;
}

export interface ListCategoryRsp {
  /** 商品分类列表 */
  categories: Category[];
}

function createBaseCategory(): Category {
  return { id: 0, alias: 0, name: '', icon: '', parent_id: 0 };
}

export const Category: MessageFns<Category> = {
  fromJSON(object: any): Category {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      alias: isSet(object.alias) ? categoryAliasFromJSON(object.alias) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      parent_id: isSet(object.parent_id) ? globalThis.Number(object.parent_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<Category>, I>>(base?: I): Category {
    return Category.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category>, I>>(object: I): Category {
    const message = createBaseCategory();
    message.id = object.id ?? 0;
    message.alias = object.alias ?? 0;
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    message.parent_id = object.parent_id ?? 0;
    return message;
  }
};

function createBaseListCategoryReq(): ListCategoryReq {
  return { category_alias: 0 };
}

export const ListCategoryReq: MessageFns<ListCategoryReq> = {
  fromJSON(object: any): ListCategoryReq {
    return { category_alias: isSet(object.category_alias) ? categoryAliasFromJSON(object.category_alias) : 0 };
  },

  create<I extends Exact<DeepPartial<ListCategoryReq>, I>>(base?: I): ListCategoryReq {
    return ListCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryReq>, I>>(object: I): ListCategoryReq {
    const message = createBaseListCategoryReq();
    message.category_alias = object.category_alias ?? 0;
    return message;
  }
};

function createBaseListCategoryRsp(): ListCategoryRsp {
  return { categories: [] };
}

export const ListCategoryRsp: MessageFns<ListCategoryRsp> = {
  fromJSON(object: any): ListCategoryRsp {
    return {
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => Category.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(base?: I): ListCategoryRsp {
    return ListCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCategoryRsp>, I>>(object: I): ListCategoryRsp {
    const message = createBaseListCategoryRsp();
    message.categories = object.categories?.map(e => Category.fromPartial(e)) || [];
    return message;
  }
};

/** ServiceName: privilege-api */
export type CategoryApiDefinition = typeof CategoryApiDefinition;
export const CategoryApiDefinition = {
  name: 'CategoryApi',
  fullName: 'api.privilege.CategoryApi',
  methods: {
    /** 商城分类列表 */
    listCategory: {
      name: 'ListCategory',
      requestType: ListCategoryReq,
      requestStream: false,
      responseType: ListCategoryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
