// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/privilege/enum.proto

/* eslint-disable */

export const protobufPackage = 'api.privilege';

/** 分类标识 */
export enum CategoryAlias {
  CATEGORY_ALIAS_NONE = 0,
  /** CATEGORY_ALIAS_VEHICLE - 一级分类 */
  CATEGORY_ALIAS_VEHICLE = 10,
  /** CATEGORY_ALIAS_HEADWEAR - 头像框 */
  CATEGORY_ALIAS_HEADWEAR = 11,
  /** CATEGORY_ALIAS_COUPON - 优惠券 */
  CATEGORY_ALIAS_COUPON = 12,
  /** CATEGORY_ALIAS_MEDAL - 勋章 */
  CATEGORY_ALIAS_MEDAL = 13,
  /** CATEGORY_ALIAS_TOOL - 道具 */
  CATEGORY_ALIAS_TOOL = 14,
  /** CATEGORY_ALIAS_MIC_APERTURE - 麦位光圈 */
  CATEGORY_ALIAS_MIC_APERTURE = 15,
  /** CATEGORY_ALIAS_ROOM_BACKGROUND - 房间背景 */
  CATEGORY_ALIAS_ROOM_BACKGROUND = 16,
  /** CATEGORY_ALIAS_ROOM_BACKGROUND_OFFICIAL - 二级分类标识 */
  CATEGORY_ALIAS_ROOM_BACKGROUND_OFFICIAL = 16000,
  UNRECOGNIZED = -1
}

export function categoryAliasFromJSON(object: any): CategoryAlias {
  switch (object) {
    case 0:
    case 'CATEGORY_ALIAS_NONE':
      return CategoryAlias.CATEGORY_ALIAS_NONE;
    case 10:
    case 'CATEGORY_ALIAS_VEHICLE':
      return CategoryAlias.CATEGORY_ALIAS_VEHICLE;
    case 11:
    case 'CATEGORY_ALIAS_HEADWEAR':
      return CategoryAlias.CATEGORY_ALIAS_HEADWEAR;
    case 12:
    case 'CATEGORY_ALIAS_COUPON':
      return CategoryAlias.CATEGORY_ALIAS_COUPON;
    case 13:
    case 'CATEGORY_ALIAS_MEDAL':
      return CategoryAlias.CATEGORY_ALIAS_MEDAL;
    case 14:
    case 'CATEGORY_ALIAS_TOOL':
      return CategoryAlias.CATEGORY_ALIAS_TOOL;
    case 15:
    case 'CATEGORY_ALIAS_MIC_APERTURE':
      return CategoryAlias.CATEGORY_ALIAS_MIC_APERTURE;
    case 16:
    case 'CATEGORY_ALIAS_ROOM_BACKGROUND':
      return CategoryAlias.CATEGORY_ALIAS_ROOM_BACKGROUND;
    case 16000:
    case 'CATEGORY_ALIAS_ROOM_BACKGROUND_OFFICIAL':
      return CategoryAlias.CATEGORY_ALIAS_ROOM_BACKGROUND_OFFICIAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryAlias.UNRECOGNIZED;
  }
}

/** 分类状态 */
export enum CategoryStatus {
  CATEGORY_STATUS_NONE = 0,
  /** CATEGORY_STATUS_NORMAL - 正常 */
  CATEGORY_STATUS_NORMAL = 1,
  /** CATEGORY_STATUS_DISABLED - 禁用 */
  CATEGORY_STATUS_DISABLED = 2,
  UNRECOGNIZED = -1
}

export function categoryStatusFromJSON(object: any): CategoryStatus {
  switch (object) {
    case 0:
    case 'CATEGORY_STATUS_NONE':
      return CategoryStatus.CATEGORY_STATUS_NONE;
    case 1:
    case 'CATEGORY_STATUS_NORMAL':
      return CategoryStatus.CATEGORY_STATUS_NORMAL;
    case 2:
    case 'CATEGORY_STATUS_DISABLED':
      return CategoryStatus.CATEGORY_STATUS_DISABLED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryStatus.UNRECOGNIZED;
  }
}

/** GoodsScene 商品使用场景 */
export enum GoodsScene {
  GOODS_SCENE_NONE = 0,
  /** GOODS_SCENE_MALL - 商城 */
  GOODS_SCENE_MALL = 10,
  /** GOODS_SCENE_ROOM - 房间 */
  GOODS_SCENE_ROOM = 11,
  UNRECOGNIZED = -1
}

export function goodsSceneFromJSON(object: any): GoodsScene {
  switch (object) {
    case 0:
    case 'GOODS_SCENE_NONE':
      return GoodsScene.GOODS_SCENE_NONE;
    case 10:
    case 'GOODS_SCENE_MALL':
      return GoodsScene.GOODS_SCENE_MALL;
    case 11:
    case 'GOODS_SCENE_ROOM':
      return GoodsScene.GOODS_SCENE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GoodsScene.UNRECOGNIZED;
  }
}

/** 商品状态 */
export enum GoodsStatus {
  GOODS_STATUS_NONE = 0,
  /** GOODS_STATUS_ONSALE - 上架 */
  GOODS_STATUS_ONSALE = 1,
  /** GOODS_STATUS_OFFSALE - 下架 */
  GOODS_STATUS_OFFSALE = 2,
  /** GOODS_STATUS_DELETED - 已删除 */
  GOODS_STATUS_DELETED = 3,
  UNRECOGNIZED = -1
}

export function goodsStatusFromJSON(object: any): GoodsStatus {
  switch (object) {
    case 0:
    case 'GOODS_STATUS_NONE':
      return GoodsStatus.GOODS_STATUS_NONE;
    case 1:
    case 'GOODS_STATUS_ONSALE':
      return GoodsStatus.GOODS_STATUS_ONSALE;
    case 2:
    case 'GOODS_STATUS_OFFSALE':
      return GoodsStatus.GOODS_STATUS_OFFSALE;
    case 3:
    case 'GOODS_STATUS_DELETED':
      return GoodsStatus.GOODS_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GoodsStatus.UNRECOGNIZED;
  }
}

/** GoodsEffectType 商品生效类型 */
export enum GoodsEffectType {
  GOODS_EFFECT_TYPE_NONE = 0,
  /** GOODS_EFFECT_TYPE_BY_DURATION - 按时长 */
  GOODS_EFFECT_TYPE_BY_DURATION = 1,
  /** GOODS_EFFECT_TYPE_BY_NUM - 按数量 */
  GOODS_EFFECT_TYPE_BY_NUM = 2,
  UNRECOGNIZED = -1
}

export function goodsEffectTypeFromJSON(object: any): GoodsEffectType {
  switch (object) {
    case 0:
    case 'GOODS_EFFECT_TYPE_NONE':
      return GoodsEffectType.GOODS_EFFECT_TYPE_NONE;
    case 1:
    case 'GOODS_EFFECT_TYPE_BY_DURATION':
      return GoodsEffectType.GOODS_EFFECT_TYPE_BY_DURATION;
    case 2:
    case 'GOODS_EFFECT_TYPE_BY_NUM':
      return GoodsEffectType.GOODS_EFFECT_TYPE_BY_NUM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GoodsEffectType.UNRECOGNIZED;
  }
}

/** BagGoodsUsingStatus 背包物品使用中状态 */
export enum BagGoodsUsingStatus {
  BAG_GOODS_USING_STATUS_NONE = 0,
  /** BAG_GOODS_USING_STATUS_DISUSE - 非使用中 */
  BAG_GOODS_USING_STATUS_DISUSE = 1,
  /** BAG_GOODS_USING_STATUS_USING - 使用中 */
  BAG_GOODS_USING_STATUS_USING = 2,
  UNRECOGNIZED = -1
}

export function bagGoodsUsingStatusFromJSON(object: any): BagGoodsUsingStatus {
  switch (object) {
    case 0:
    case 'BAG_GOODS_USING_STATUS_NONE':
      return BagGoodsUsingStatus.BAG_GOODS_USING_STATUS_NONE;
    case 1:
    case 'BAG_GOODS_USING_STATUS_DISUSE':
      return BagGoodsUsingStatus.BAG_GOODS_USING_STATUS_DISUSE;
    case 2:
    case 'BAG_GOODS_USING_STATUS_USING':
      return BagGoodsUsingStatus.BAG_GOODS_USING_STATUS_USING;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BagGoodsUsingStatus.UNRECOGNIZED;
  }
}

/** BagGoodsUsedStatus 背包物品使用历史状态 */
export enum BagGoodsUsedStatus {
  BAG_GOODS_USED_STATUS_NONE = 0,
  /** BAG_GOODS_USED_STATUS_DISUSED - 未使用过 */
  BAG_GOODS_USED_STATUS_DISUSED = 1,
  /** BAG_GOODS_USED_STATUS_USED - 使用过 */
  BAG_GOODS_USED_STATUS_USED = 2,
  UNRECOGNIZED = -1
}

export function bagGoodsUsedStatusFromJSON(object: any): BagGoodsUsedStatus {
  switch (object) {
    case 0:
    case 'BAG_GOODS_USED_STATUS_NONE':
      return BagGoodsUsedStatus.BAG_GOODS_USED_STATUS_NONE;
    case 1:
    case 'BAG_GOODS_USED_STATUS_DISUSED':
      return BagGoodsUsedStatus.BAG_GOODS_USED_STATUS_DISUSED;
    case 2:
    case 'BAG_GOODS_USED_STATUS_USED':
      return BagGoodsUsedStatus.BAG_GOODS_USED_STATUS_USED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BagGoodsUsedStatus.UNRECOGNIZED;
  }
}

/** BagGoodsNewStatus 背包物品新获得状态 */
export enum BagGoodsNewStatus {
  BAG_GOODS_NEW_STATUS_NONE = 0,
  /** BAG_GOODS_NEW_STATUS_USUAL - 非新获得 */
  BAG_GOODS_NEW_STATUS_USUAL = 1,
  /** BAG_GOODS_NEW_STATUS_NEW - 首次新获得 */
  BAG_GOODS_NEW_STATUS_NEW = 2,
  UNRECOGNIZED = -1
}

export function bagGoodsNewStatusFromJSON(object: any): BagGoodsNewStatus {
  switch (object) {
    case 0:
    case 'BAG_GOODS_NEW_STATUS_NONE':
      return BagGoodsNewStatus.BAG_GOODS_NEW_STATUS_NONE;
    case 1:
    case 'BAG_GOODS_NEW_STATUS_USUAL':
      return BagGoodsNewStatus.BAG_GOODS_NEW_STATUS_USUAL;
    case 2:
    case 'BAG_GOODS_NEW_STATUS_NEW':
      return BagGoodsNewStatus.BAG_GOODS_NEW_STATUS_NEW;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BagGoodsNewStatus.UNRECOGNIZED;
  }
}

/** PayStatus 支付状态 */
export enum PayStatus {
  PAY_STATUS_NONE = 0,
  /** PAY_STATUS_PENDING - 待支付 */
  PAY_STATUS_PENDING = 1,
  /** PAY_STATUS_PAIED - 已支付 */
  PAY_STATUS_PAIED = 2,
  /** PAY_STATUS_FAILED - 支付失败 */
  PAY_STATUS_FAILED = 3,
  /** PAY_STATUS_REFUND - 退款 */
  PAY_STATUS_REFUND = 4,
  UNRECOGNIZED = -1
}

export function payStatusFromJSON(object: any): PayStatus {
  switch (object) {
    case 0:
    case 'PAY_STATUS_NONE':
      return PayStatus.PAY_STATUS_NONE;
    case 1:
    case 'PAY_STATUS_PENDING':
      return PayStatus.PAY_STATUS_PENDING;
    case 2:
    case 'PAY_STATUS_PAIED':
      return PayStatus.PAY_STATUS_PAIED;
    case 3:
    case 'PAY_STATUS_FAILED':
      return PayStatus.PAY_STATUS_FAILED;
    case 4:
    case 'PAY_STATUS_REFUND':
      return PayStatus.PAY_STATUS_REFUND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PayStatus.UNRECOGNIZED;
  }
}
