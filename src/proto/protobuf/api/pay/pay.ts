// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/pay/pay.proto

/* eslint-disable */
import { Page } from '../common/common';
import { ApplePayReceipt, GooglePlayReceipt, JumpType, jumpTypeFromJSON } from './common';

export const protobufPackage = 'pbspay';

/** smicro:spath=gitit.cc/social/components-service/social-revenue/goods/internal/handler/pay_handler.go */

/** 货币单位，目前只有美元，后必要再扩展 */
export enum SpayUnit {
  /** SPAY_UNIT_NONE - 未知 */
  SPAY_UNIT_NONE = 0,
  /** SPAY_UNIT_USD - 美金 */
  SPAY_UNIT_USD = 10,
  /** SPAY_UNIT_AED - 阿联酋 */
  SPAY_UNIT_AED = 11,
  /** SPAY_UNIT_EGP - 埃及 */
  SPAY_UNIT_EGP = 12,
  /** SPAY_UNIT_SAR - 沙特阿拉伯 */
  SPAY_UNIT_SAR = 13,
  /** SPAY_UNIT_KWD - 科威特 */
  SPAY_UNIT_KWD = 14,
  /** SPAY_UNIT_JOD - 约旦 */
  SPAY_UNIT_JOD = 15,
  /** SPAY_UNIT_QAR - 卡塔尔 */
  SPAY_UNIT_QAR = 16,
  /** SPAY_UNIT_BHD - 巴林 */
  SPAY_UNIT_BHD = 17,
  /** SPAY_UNIT_OMR - 阿曼 */
  SPAY_UNIT_OMR = 18,
  /** SPAY_UNIT_INR - 印度 */
  SPAY_UNIT_INR = 19,
  /** SPAY_UNIT_IDR - 印尼盾 */
  SPAY_UNIT_IDR = 20,
  /** SPAY_UNIT_IQD - 伊拉克 */
  SPAY_UNIT_IQD = 21,
  /** SPAY_UNIT_TRY - 土耳其 */
  SPAY_UNIT_TRY = 22,
  /** SPAY_UNIT_PKR - 巴基斯坦 */
  SPAY_UNIT_PKR = 23,
  /** SPAY_UNIT_BDT - 孟加拉 */
  SPAY_UNIT_BDT = 24,
  /** SPAY_UNIT_MXN - 墨西哥 */
  SPAY_UNIT_MXN = 25,
  /** SPAY_UNIT_CLP - 智利 */
  SPAY_UNIT_CLP = 26,
  /** SPAY_UNIT_AUD - 澳元 */
  SPAY_UNIT_AUD = 27,
  /** SPAY_UNIT_CAD - 加元 */
  SPAY_UNIT_CAD = 28,
  /** SPAY_UNIT_PEN - 秘鲁 */
  SPAY_UNIT_PEN = 29,
  /** SPAY_UNIT_MYR - 马来西亚 */
  SPAY_UNIT_MYR = 30,
  /** SPAY_UNIT_NGN - 尼日利亚 */
  SPAY_UNIT_NGN = 31,
  UNRECOGNIZED = -1
}

export function spayUnitFromJSON(object: any): SpayUnit {
  switch (object) {
    case 0:
    case 'SPAY_UNIT_NONE':
      return SpayUnit.SPAY_UNIT_NONE;
    case 10:
    case 'SPAY_UNIT_USD':
      return SpayUnit.SPAY_UNIT_USD;
    case 11:
    case 'SPAY_UNIT_AED':
      return SpayUnit.SPAY_UNIT_AED;
    case 12:
    case 'SPAY_UNIT_EGP':
      return SpayUnit.SPAY_UNIT_EGP;
    case 13:
    case 'SPAY_UNIT_SAR':
      return SpayUnit.SPAY_UNIT_SAR;
    case 14:
    case 'SPAY_UNIT_KWD':
      return SpayUnit.SPAY_UNIT_KWD;
    case 15:
    case 'SPAY_UNIT_JOD':
      return SpayUnit.SPAY_UNIT_JOD;
    case 16:
    case 'SPAY_UNIT_QAR':
      return SpayUnit.SPAY_UNIT_QAR;
    case 17:
    case 'SPAY_UNIT_BHD':
      return SpayUnit.SPAY_UNIT_BHD;
    case 18:
    case 'SPAY_UNIT_OMR':
      return SpayUnit.SPAY_UNIT_OMR;
    case 19:
    case 'SPAY_UNIT_INR':
      return SpayUnit.SPAY_UNIT_INR;
    case 20:
    case 'SPAY_UNIT_IDR':
      return SpayUnit.SPAY_UNIT_IDR;
    case 21:
    case 'SPAY_UNIT_IQD':
      return SpayUnit.SPAY_UNIT_IQD;
    case 22:
    case 'SPAY_UNIT_TRY':
      return SpayUnit.SPAY_UNIT_TRY;
    case 23:
    case 'SPAY_UNIT_PKR':
      return SpayUnit.SPAY_UNIT_PKR;
    case 24:
    case 'SPAY_UNIT_BDT':
      return SpayUnit.SPAY_UNIT_BDT;
    case 25:
    case 'SPAY_UNIT_MXN':
      return SpayUnit.SPAY_UNIT_MXN;
    case 26:
    case 'SPAY_UNIT_CLP':
      return SpayUnit.SPAY_UNIT_CLP;
    case 27:
    case 'SPAY_UNIT_AUD':
      return SpayUnit.SPAY_UNIT_AUD;
    case 28:
    case 'SPAY_UNIT_CAD':
      return SpayUnit.SPAY_UNIT_CAD;
    case 29:
    case 'SPAY_UNIT_PEN':
      return SpayUnit.SPAY_UNIT_PEN;
    case 30:
    case 'SPAY_UNIT_MYR':
      return SpayUnit.SPAY_UNIT_MYR;
    case 31:
    case 'SPAY_UNIT_NGN':
      return SpayUnit.SPAY_UNIT_NGN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayUnit.UNRECOGNIZED;
  }
}

/** 需要映射成营收服务枚举 */
export enum SpayCurrencyType {
  SPAY_CURRENCY_TYPE_NONE = 0,
  /** SPAY_CURRENCY_TYPE_GOLD - 金币 */
  SPAY_CURRENCY_TYPE_GOLD = 10,
  /** SPAY_CURRENCY_TYPE_CRYSTAL - 水晶 */
  SPAY_CURRENCY_TYPE_CRYSTAL = 11,
  /** SPAY_CURRENCY_TYPE_DIAMOND - 钻石 */
  SPAY_CURRENCY_TYPE_DIAMOND = 12,
  UNRECOGNIZED = -1
}

export function spayCurrencyTypeFromJSON(object: any): SpayCurrencyType {
  switch (object) {
    case 0:
    case 'SPAY_CURRENCY_TYPE_NONE':
      return SpayCurrencyType.SPAY_CURRENCY_TYPE_NONE;
    case 10:
    case 'SPAY_CURRENCY_TYPE_GOLD':
      return SpayCurrencyType.SPAY_CURRENCY_TYPE_GOLD;
    case 11:
    case 'SPAY_CURRENCY_TYPE_CRYSTAL':
      return SpayCurrencyType.SPAY_CURRENCY_TYPE_CRYSTAL;
    case 12:
    case 'SPAY_CURRENCY_TYPE_DIAMOND':
      return SpayCurrencyType.SPAY_CURRENCY_TYPE_DIAMOND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayCurrencyType.UNRECOGNIZED;
  }
}

/** SpayClientType 客户端类型 */
export enum SpayClientType {
  SPAY_CLIENT_TYPE_NONE = 0,
  /** SPAY_CLIENT_TYPE_ANDROID - Android */
  SPAY_CLIENT_TYPE_ANDROID = 1,
  /** SPAY_CLIENT_TYPE_IOS - IOS */
  SPAY_CLIENT_TYPE_IOS = 2,
  /** SPAY_CLIENT_TYPE_WEB - WEB, H5 */
  SPAY_CLIENT_TYPE_WEB = 3,
  UNRECOGNIZED = -1
}

export function spayClientTypeFromJSON(object: any): SpayClientType {
  switch (object) {
    case 0:
    case 'SPAY_CLIENT_TYPE_NONE':
      return SpayClientType.SPAY_CLIENT_TYPE_NONE;
    case 1:
    case 'SPAY_CLIENT_TYPE_ANDROID':
      return SpayClientType.SPAY_CLIENT_TYPE_ANDROID;
    case 2:
    case 'SPAY_CLIENT_TYPE_IOS':
      return SpayClientType.SPAY_CLIENT_TYPE_IOS;
    case 3:
    case 'SPAY_CLIENT_TYPE_WEB':
      return SpayClientType.SPAY_CLIENT_TYPE_WEB;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayClientType.UNRECOGNIZED;
  }
}

export enum SpaySkuType {
  /** SPAY_SKU_TYPE_NONE - 未知 */
  SPAY_SKU_TYPE_NONE = 0,
  /** SPAY_SKU_TYPE_CURRENCY - 虚拟币 */
  SPAY_SKU_TYPE_CURRENCY = 10,
  /** SPAY_SKU_TYPE_PACKAGE - 包裹，预定义暂未支持，后续也可能扩展其他类型 */
  SPAY_SKU_TYPE_PACKAGE = 20,
  UNRECOGNIZED = -1
}

export function spaySkuTypeFromJSON(object: any): SpaySkuType {
  switch (object) {
    case 0:
    case 'SPAY_SKU_TYPE_NONE':
      return SpaySkuType.SPAY_SKU_TYPE_NONE;
    case 10:
    case 'SPAY_SKU_TYPE_CURRENCY':
      return SpaySkuType.SPAY_SKU_TYPE_CURRENCY;
    case 20:
    case 'SPAY_SKU_TYPE_PACKAGE':
      return SpaySkuType.SPAY_SKU_TYPE_PACKAGE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpaySkuType.UNRECOGNIZED;
  }
}

/** 支付状态 */
export enum SpayStatus {
  /** SPAY_STATUS_NONE - 未支付 */
  SPAY_STATUS_NONE = 0,
  /** SPAY_STATUS_UNPAID - UNPAID:等待支付 */
  SPAY_STATUS_UNPAID = 10,
  /** SPAY_STATUS_PAID - PAID:支付成功 */
  SPAY_STATUS_PAID = 20,
  /** SPAY_STATUS_FAIL - FAIL:支付失败 */
  SPAY_STATUS_FAIL = 30,
  /** SPAY_STATUS_CANCEL - CANCEL:取消失败 */
  SPAY_STATUS_CANCEL = 40,
  UNRECOGNIZED = -1
}

export function spayStatusFromJSON(object: any): SpayStatus {
  switch (object) {
    case 0:
    case 'SPAY_STATUS_NONE':
      return SpayStatus.SPAY_STATUS_NONE;
    case 10:
    case 'SPAY_STATUS_UNPAID':
      return SpayStatus.SPAY_STATUS_UNPAID;
    case 20:
    case 'SPAY_STATUS_PAID':
      return SpayStatus.SPAY_STATUS_PAID;
    case 30:
    case 'SPAY_STATUS_FAIL':
      return SpayStatus.SPAY_STATUS_FAIL;
    case 40:
    case 'SPAY_STATUS_CANCEL':
      return SpayStatus.SPAY_STATUS_CANCEL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayStatus.UNRECOGNIZED;
  }
}

export enum SpayRefundStatus {
  /** SPAY_REFUND_STATUS_NONE - 未退款 */
  SPAY_REFUND_STATUS_NONE = 0,
  /** SPAY_REFUND_STATUS_REFUNDING - 退款中 */
  SPAY_REFUND_STATUS_REFUNDING = 10,
  /** SPAY_REFUND_STATUS_SUCCESS - 退款成功 */
  SPAY_REFUND_STATUS_SUCCESS = 20,
  /** SPAY_REFUND_STATUS_FAIL - 退款失败 */
  SPAY_REFUND_STATUS_FAIL = 30,
  UNRECOGNIZED = -1
}

export function spayRefundStatusFromJSON(object: any): SpayRefundStatus {
  switch (object) {
    case 0:
    case 'SPAY_REFUND_STATUS_NONE':
      return SpayRefundStatus.SPAY_REFUND_STATUS_NONE;
    case 10:
    case 'SPAY_REFUND_STATUS_REFUNDING':
      return SpayRefundStatus.SPAY_REFUND_STATUS_REFUNDING;
    case 20:
    case 'SPAY_REFUND_STATUS_SUCCESS':
      return SpayRefundStatus.SPAY_REFUND_STATUS_SUCCESS;
    case 30:
    case 'SPAY_REFUND_STATUS_FAIL':
      return SpayRefundStatus.SPAY_REFUND_STATUS_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayRefundStatus.UNRECOGNIZED;
  }
}

export enum SkuLimitType {
  /** SKU_LIMIT_TYPE_NONE - 默认值，无意义 */
  SKU_LIMIT_TYPE_NONE = 0,
  /** SKU_LIMIT_TYPE_ONCE - 只能买一次（首充） */
  SKU_LIMIT_TYPE_ONCE = 10,
  UNRECOGNIZED = -1
}

export function skuLimitTypeFromJSON(object: any): SkuLimitType {
  switch (object) {
    case 0:
    case 'SKU_LIMIT_TYPE_NONE':
      return SkuLimitType.SKU_LIMIT_TYPE_NONE;
    case 10:
    case 'SKU_LIMIT_TYPE_ONCE':
      return SkuLimitType.SKU_LIMIT_TYPE_ONCE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SkuLimitType.UNRECOGNIZED;
  }
}

export interface ListSkuReq {
  /** h5必传，客户端类型，不传则使用公参 */
  client_type: SpayClientType;
  /** 非必须，扫描包获得的支付app列表，服务端根据扫包结果可以做一些过滤和排序策略 */
  payment_apps: string[];
  /** 支付方式如payerMax、quarkpay可以不传 */
  pay_types: string[];
  /**
   * key标注
   * scene: 查询场景
   */
  ext: { [key: string]: string };
}

export interface ListSkuReq_ExtEntry {
  key: string;
  value: string;
}

export interface ListSkuRsp {
  /** sku 列表 */
  list: SkuInfo[];
  /** 业务扩展数据，sdk 透传给业务 */
  ext: { [key: string]: string };
}

export interface ListSkuRsp_ExtEntry {
  key: string;
  value: string;
}

export interface CreateSpayOrderReq {
  /** 商品ID，业务配置的id，不是 sku */
  item_id: number;
  /** 非必须，购买数量，默认1，系统扩展预留 */
  num: number;
  /** h5必传，客户端类型，不传则使用公参 */
  client_type: SpayClientType;
  /** 非必须，第三方支付参数，国家编码 */
  location: string;
  /** 非必须，第三方支付参数，支付方式（子渠道） */
  pay_code: string;
  /** 非必须，第三方支付参数，h5 支付后跳转的页面地址 */
  success_url: string;
  /** 非必须，预留字段，用于处理不同业务个性化逻辑 */
  ext: { [key: string]: string };
}

export interface CreateSpayOrderReq_ExtEntry {
  key: string;
  value: string;
}

export interface CreateSpayOrderRsp {
  /** 订单信息 */
  order_info: SpayOrderInfo | undefined;
  /** 扩展信息，sdk 透传给业务，处理不同业务个性化逻辑 */
  ext: { [key: string]: string };
  /** 签名，验单和取消订单接口需要带上 */
  checksum: string;
  /** google_pay 返回 */
  google_play_result: GooglePlayResult | undefined;
  /** apple_pay 返回 */
  apple_pay_result: ApplePayResult | undefined;
  /** 第三方支付返回 */
  third_party_result: ThirdPartyPayResult | undefined;
}

export interface CreateSpayOrderRsp_ExtEntry {
  key: string;
  value: string;
}

export interface PayCallbackReq {
  /** 创单时返回的签名 */
  checksum: string;
  /** 业务订单ID */
  order_no: string;
  /** apple_pay 支付凭证 */
  apple_pay_receipt: ApplePayReceipt | undefined;
  /** google_play 支付凭证 */
  google_play_receipt: GooglePlayReceipt | undefined;
}

export interface PayCallbackRsp {
  /** 验单结果 */
  result: SpayOrderResult | undefined;
  /** 支付成功后业务需要返回的信息，sdk 透传给业务 */
  purchase_info: PurchaseInfo | undefined;
}

export interface CheckSpayOrderReq {
  /** 业务订单号 */
  order_no: string;
}

export interface CheckSpayOrderRsp {
  /** 验单结果 */
  result: SpayOrderResult | undefined;
  /** 订单详情 */
  order: OrderDetail | undefined;
  /** 支付成功后业务需要返回的信息，sdk 透传给业务 */
  purchase_info: PurchaseInfo | undefined;
}

export interface PayCancelReq {
  /** 创单时返回的签名 */
  checksum: string;
  /** 业务订单ID */
  order_no: string;
  /** 取消原因 */
  reason: string;
}

export interface PayCancelRsp {
  /** 取消支付结果 */
  result: SpayOrderResult | undefined;
}

export interface SpayUserOrderReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 支付方式，如：google_play apple_pay huaweipay quarkpay 等 */
  pay_type: string;
  /** 开始时间戳，秒 */
  start_time: number;
  /** 结束时间戳，秒 */
  end_time: number;
  /** 扩展字段，处理不同业务个性化逻辑 */
  ext: { [key: string]: string };
}

export interface SpayUserOrderReq_ExtEntry {
  key: string;
  value: string;
}

export interface SpayUserOrderRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 充值列表 */
  list: OrderDetail[];
}

export interface SkuInfo {
  /** 商品类型，如：虚拟币，包裹，头像框等，根据业务扩展 */
  sku_type: SpaySkuType;
  /** 商品名称 */
  name: string;
  /** 商品名称，多语言 */
  name_lang: { [key: string]: string };
  /** 商品描述 */
  desc: string;
  /** 商品描述，多语言 */
  desc_lang: { [key: string]: string };
  /** 商品图片 */
  image: string;
  /** 标签图片 */
  tag_image: string;
  /** 角标文案 */
  tag: string;
  /** 角标文案，多语言 */
  tag_lang: { [key: string]: string };
  /** 支付金额，单位分 */
  price: number;
  /** 支付金额单位 */
  unit: SpayUnit;
  /** 支付金额单位文案 */
  unit_str: string;
  /** 获得金额，用户显示的金额使用 amount - additional_num */
  amount: number;
  /** 获得金额货币类型 */
  currency_type: SpayCurrencyType;
  /** 购买数量，不能小于1 */
  num: number;
  /** 额外福利获得的金币数数量 */
  additional_num: number;
  /** 折扣百分比，只会大于0，eg: 10 即 10%，即1折，等于0为无折扣，而不是0% */
  discount_rate: number;
  /** 商品id */
  goods_id: number;
  /** 限制购买类型 */
  limit_type: SkuLimitType;
  /** 非必须，支付金额（分，美元就是美分），实际发起支付的金额，如果没有配置，会使用 price */
  target_price: number;
  /** 非必须，实际支付的币种，如果配置了 target_price，则此项必传 */
  target_unit: SpayUnit;
  /** 扩展信息，可以支持一些扩展信息配置，实现特定商品或者指定业务的个性化功能，客户端sdk可以透传给业务，处理个性化需求 */
  ext: { [key: string]: string };
  /** 商品列表，这里是跟具体支付方式绑定的，通过 pay_type 来判断 */
  items: SkuItem[];
}

export interface SkuInfo_NameLangEntry {
  key: string;
  value: string;
}

export interface SkuInfo_DescLangEntry {
  key: string;
  value: string;
}

export interface SkuInfo_TagLangEntry {
  key: string;
  value: string;
}

export interface SkuInfo_ExtEntry {
  key: string;
  value: string;
}

export interface SkuItem {
  /** sku 配置的业务id */
  item_id: number;
  /** 支付渠道配置的 sku 标识 */
  sku: string;
  /** 支付金额，分 */
  price: number;
  /** 支付方式 */
  pay_info: PayInfo | undefined;
  /** 支付中台 mid */
  mid: string;
  /** 跳转方式，第三方充值的情况返回 */
  jump_type: JumpType;
  /** 跳转url */
  jump_url: string;
  /** 支付货币代号，如USD/INR */
  unit: string;
  /** 支付货币符号，如$/¥ */
  unit_str: string;
  /** 价格，用于展示 */
  show_price: string;
  /** 币种，用于展示 */
  show_unit: string;
}

export interface PayInfo {
  /** 支付方式，如：google_play apple_pay huaweipay quarkpay 等 */
  pay_type: string;
  /** 支付方式名称 */
  name: string;
  /** 支付方式多语言 */
  name_lang: { [key: string]: string };
  /** 支付方式图标 */
  image: string;
  /** 具体支付方式 上面那个其实是支付渠道 */
  pay_code: string;
  /** 具体支付方式展示名称 */
  pay_code_name: string;
  /** 具体支付方式展示图标 */
  pay_code_img: string;
}

export interface PayInfo_NameLangEntry {
  key: string;
  value: string;
}

/** GooglePlayResult google_play 下单返回数据 */
export interface GooglePlayResult {
  /** 商户ID，eg: echo carni crushu */
  mid: string;
  /** 用户ID */
  uid: number;
  /** 业务订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付金额 */
  pay_amount: number;
}

/** ApplePayResult apple_pay 下单返回数据 */
export interface ApplePayResult {
  /** 商户ID，eg: echo carni crushu */
  mid: string;
  /** 用户ID */
  uid: number;
  /** 业务订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付金额 */
  pay_amount: number;
}

/** ThirdPartyPayResult 第三方支付下单返回数据，相关文档：https://nemo.yuque.com/pay_system/interface/haaq01#415E8 */
export interface ThirdPartyPayResult {
  /** 商户ID，eg: echo carni crushu */
  mid: string;
  /** 支付金额 */
  uid: string;
  /** 业务订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付平台供应商订单 */
  plat_order_id: string;
  /** 支付金额 */
  pay_amount: number;
  /** 实际使用的支付方式，如：google_play apple_pay huaweipay quarkpay 等 */
  pay_type: string;
  /** 支付商的收银台页面或者是deeplink直接拉起支付应用，根据 jump_type 来判断 */
  jump_url: string;
  /** 跳转方式 */
  jump_type: JumpType;
}

export interface SpayOrderInfo {
  /** 业务订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付平台供应商订单 */
  plat_order_id: string;
  /** 退款单ID */
  refund_id: string;
  /** 需要支付的金额，注意单位是：分！！！ */
  pay_amount: number;
  /** 支付金额单位 */
  unit: SpayUnit;
  /** 支付金额单位文案 */
  unit_str: string;
  /** 支付方式，如：google_play apple_pay huaweipay quarkpay 等 */
  pay_type: string;
  /** 购买数量，默认1，系统扩展预留 */
  num: number;
  /** 换算成元（如果是USD则表示美元）单位 */
  pay_amount_dollar: string;
  /** 购买商品信息 */
  item: SkuItem | undefined;
}

export interface PurchaseInfo {
  /** 废弃（不在验单流程处理发货），充值后钱包余额，PayCallback 接口不返回，因为还没有真正处理发货逻辑 */
  amount: number;
  /** 当比充值获得的数额 */
  recharge_amount: number;
  /** 业务自定义信息 */
  ext: { [key: string]: string };
}

export interface PurchaseInfo_ExtEntry {
  key: string;
  value: string;
}

/** 验单结果 */
export interface SpayOrderResult {
  /** 业务订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付渠道供应商订单 */
  plat_order_id: string;
  /** 支付状态 */
  status: SpayStatus;
  /** 订单支付成功或失败原因 */
  msg: string;
}

/** 订单详细信息 */
export interface OrderDetail {
  /** 充值到账用户ID，即给谁发货 */
  uid: number;
  /** 发起充值的用户ID */
  fuid: number;
  /** 订单信息 */
  order_info: SpayOrderInfo | undefined;
  /** 支付mid */
  mid: string;
  /** 获得虚拟币类型 */
  currency_type: SpayCurrencyType;
  /** 获得虚拟币金额 */
  amount: number;
  /** 客户端类型，客户端-WAP，网页-WEB */
  client_type: string;
  /** 客户端版本 */
  client_version: string;
  /** 设备标识 */
  did: string;
  /** 支付渠道ID */
  channel_id: string;
  /** 支付状态 */
  status: SpayStatus;
  /** 支付时间，秒 */
  pay_time: number;
  /** 退款状态 */
  refund_status: SpayRefundStatus;
  /** 退款时间，秒 */
  refund_time: number;
  /** 购买商品名称，用来显示 */
  goods_name: string;
  /** 创建订单时间，秒 */
  ctime: number;
  /** 扩展字段，处理不同业务个性化逻辑 */
  ext: { [key: string]: string };
}

export interface OrderDetail_ExtEntry {
  key: string;
  value: string;
}

function createBaseListSkuReq(): ListSkuReq {
  return { client_type: 0, payment_apps: [], pay_types: [], ext: {} };
}

export const ListSkuReq: MessageFns<ListSkuReq> = {
  fromJSON(object: any): ListSkuReq {
    return {
      client_type: isSet(object.client_type) ? spayClientTypeFromJSON(object.client_type) : 0,
      payment_apps: globalThis.Array.isArray(object?.payment_apps)
        ? object.payment_apps.map((e: any) => globalThis.String(e))
        : [],
      pay_types: globalThis.Array.isArray(object?.pay_types)
        ? object.pay_types.map((e: any) => globalThis.String(e))
        : [],
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSkuReq>, I>>(base?: I): ListSkuReq {
    return ListSkuReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSkuReq>, I>>(object: I): ListSkuReq {
    const message = createBaseListSkuReq();
    message.client_type = object.client_type ?? 0;
    message.payment_apps = object.payment_apps?.map(e => e) || [];
    message.pay_types = object.pay_types?.map(e => e) || [];
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSkuReq_ExtEntry(): ListSkuReq_ExtEntry {
  return { key: '', value: '' };
}

export const ListSkuReq_ExtEntry: MessageFns<ListSkuReq_ExtEntry> = {
  fromJSON(object: any): ListSkuReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSkuReq_ExtEntry>, I>>(base?: I): ListSkuReq_ExtEntry {
    return ListSkuReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSkuReq_ExtEntry>, I>>(object: I): ListSkuReq_ExtEntry {
    const message = createBaseListSkuReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListSkuRsp(): ListSkuRsp {
  return { list: [], ext: {} };
}

export const ListSkuRsp: MessageFns<ListSkuRsp> = {
  fromJSON(object: any): ListSkuRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => SkuInfo.fromJSON(e)) : [],
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSkuRsp>, I>>(base?: I): ListSkuRsp {
    return ListSkuRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSkuRsp>, I>>(object: I): ListSkuRsp {
    const message = createBaseListSkuRsp();
    message.list = object.list?.map(e => SkuInfo.fromPartial(e)) || [];
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSkuRsp_ExtEntry(): ListSkuRsp_ExtEntry {
  return { key: '', value: '' };
}

export const ListSkuRsp_ExtEntry: MessageFns<ListSkuRsp_ExtEntry> = {
  fromJSON(object: any): ListSkuRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSkuRsp_ExtEntry>, I>>(base?: I): ListSkuRsp_ExtEntry {
    return ListSkuRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSkuRsp_ExtEntry>, I>>(object: I): ListSkuRsp_ExtEntry {
    const message = createBaseListSkuRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCreateSpayOrderReq(): CreateSpayOrderReq {
  return { item_id: 0, num: 0, client_type: 0, location: '', pay_code: '', success_url: '', ext: {} };
}

export const CreateSpayOrderReq: MessageFns<CreateSpayOrderReq> = {
  fromJSON(object: any): CreateSpayOrderReq {
    return {
      item_id: isSet(object.item_id) ? globalThis.Number(object.item_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      client_type: isSet(object.client_type) ? spayClientTypeFromJSON(object.client_type) : 0,
      location: isSet(object.location) ? globalThis.String(object.location) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      success_url: isSet(object.success_url) ? globalThis.String(object.success_url) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CreateSpayOrderReq>, I>>(base?: I): CreateSpayOrderReq {
    return CreateSpayOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSpayOrderReq>, I>>(object: I): CreateSpayOrderReq {
    const message = createBaseCreateSpayOrderReq();
    message.item_id = object.item_id ?? 0;
    message.num = object.num ?? 0;
    message.client_type = object.client_type ?? 0;
    message.location = object.location ?? '';
    message.pay_code = object.pay_code ?? '';
    message.success_url = object.success_url ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseCreateSpayOrderReq_ExtEntry(): CreateSpayOrderReq_ExtEntry {
  return { key: '', value: '' };
}

export const CreateSpayOrderReq_ExtEntry: MessageFns<CreateSpayOrderReq_ExtEntry> = {
  fromJSON(object: any): CreateSpayOrderReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateSpayOrderReq_ExtEntry>, I>>(base?: I): CreateSpayOrderReq_ExtEntry {
    return CreateSpayOrderReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSpayOrderReq_ExtEntry>, I>>(object: I): CreateSpayOrderReq_ExtEntry {
    const message = createBaseCreateSpayOrderReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCreateSpayOrderRsp(): CreateSpayOrderRsp {
  return {
    order_info: undefined,
    ext: {},
    checksum: '',
    google_play_result: undefined,
    apple_pay_result: undefined,
    third_party_result: undefined
  };
}

export const CreateSpayOrderRsp: MessageFns<CreateSpayOrderRsp> = {
  fromJSON(object: any): CreateSpayOrderRsp {
    return {
      order_info: isSet(object.order_info) ? SpayOrderInfo.fromJSON(object.order_info) : undefined,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      checksum: isSet(object.checksum) ? globalThis.String(object.checksum) : '',
      google_play_result: isSet(object.google_play_result)
        ? GooglePlayResult.fromJSON(object.google_play_result)
        : undefined,
      apple_pay_result: isSet(object.apple_pay_result) ? ApplePayResult.fromJSON(object.apple_pay_result) : undefined,
      third_party_result: isSet(object.third_party_result)
        ? ThirdPartyPayResult.fromJSON(object.third_party_result)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<CreateSpayOrderRsp>, I>>(base?: I): CreateSpayOrderRsp {
    return CreateSpayOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSpayOrderRsp>, I>>(object: I): CreateSpayOrderRsp {
    const message = createBaseCreateSpayOrderRsp();
    message.order_info =
      object.order_info !== undefined && object.order_info !== null
        ? SpayOrderInfo.fromPartial(object.order_info)
        : undefined;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.checksum = object.checksum ?? '';
    message.google_play_result =
      object.google_play_result !== undefined && object.google_play_result !== null
        ? GooglePlayResult.fromPartial(object.google_play_result)
        : undefined;
    message.apple_pay_result =
      object.apple_pay_result !== undefined && object.apple_pay_result !== null
        ? ApplePayResult.fromPartial(object.apple_pay_result)
        : undefined;
    message.third_party_result =
      object.third_party_result !== undefined && object.third_party_result !== null
        ? ThirdPartyPayResult.fromPartial(object.third_party_result)
        : undefined;
    return message;
  }
};

function createBaseCreateSpayOrderRsp_ExtEntry(): CreateSpayOrderRsp_ExtEntry {
  return { key: '', value: '' };
}

export const CreateSpayOrderRsp_ExtEntry: MessageFns<CreateSpayOrderRsp_ExtEntry> = {
  fromJSON(object: any): CreateSpayOrderRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateSpayOrderRsp_ExtEntry>, I>>(base?: I): CreateSpayOrderRsp_ExtEntry {
    return CreateSpayOrderRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateSpayOrderRsp_ExtEntry>, I>>(object: I): CreateSpayOrderRsp_ExtEntry {
    const message = createBaseCreateSpayOrderRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePayCallbackReq(): PayCallbackReq {
  return { checksum: '', order_no: '', apple_pay_receipt: undefined, google_play_receipt: undefined };
}

export const PayCallbackReq: MessageFns<PayCallbackReq> = {
  fromJSON(object: any): PayCallbackReq {
    return {
      checksum: isSet(object.checksum) ? globalThis.String(object.checksum) : '',
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      apple_pay_receipt: isSet(object.apple_pay_receipt)
        ? ApplePayReceipt.fromJSON(object.apple_pay_receipt)
        : undefined,
      google_play_receipt: isSet(object.google_play_receipt)
        ? GooglePlayReceipt.fromJSON(object.google_play_receipt)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<PayCallbackReq>, I>>(base?: I): PayCallbackReq {
    return PayCallbackReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCallbackReq>, I>>(object: I): PayCallbackReq {
    const message = createBasePayCallbackReq();
    message.checksum = object.checksum ?? '';
    message.order_no = object.order_no ?? '';
    message.apple_pay_receipt =
      object.apple_pay_receipt !== undefined && object.apple_pay_receipt !== null
        ? ApplePayReceipt.fromPartial(object.apple_pay_receipt)
        : undefined;
    message.google_play_receipt =
      object.google_play_receipt !== undefined && object.google_play_receipt !== null
        ? GooglePlayReceipt.fromPartial(object.google_play_receipt)
        : undefined;
    return message;
  }
};

function createBasePayCallbackRsp(): PayCallbackRsp {
  return { result: undefined, purchase_info: undefined };
}

export const PayCallbackRsp: MessageFns<PayCallbackRsp> = {
  fromJSON(object: any): PayCallbackRsp {
    return {
      result: isSet(object.result) ? SpayOrderResult.fromJSON(object.result) : undefined,
      purchase_info: isSet(object.purchase_info) ? PurchaseInfo.fromJSON(object.purchase_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PayCallbackRsp>, I>>(base?: I): PayCallbackRsp {
    return PayCallbackRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCallbackRsp>, I>>(object: I): PayCallbackRsp {
    const message = createBasePayCallbackRsp();
    message.result =
      object.result !== undefined && object.result !== null ? SpayOrderResult.fromPartial(object.result) : undefined;
    message.purchase_info =
      object.purchase_info !== undefined && object.purchase_info !== null
        ? PurchaseInfo.fromPartial(object.purchase_info)
        : undefined;
    return message;
  }
};

function createBaseCheckSpayOrderReq(): CheckSpayOrderReq {
  return { order_no: '' };
}

export const CheckSpayOrderReq: MessageFns<CheckSpayOrderReq> = {
  fromJSON(object: any): CheckSpayOrderReq {
    return { order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '' };
  },

  create<I extends Exact<DeepPartial<CheckSpayOrderReq>, I>>(base?: I): CheckSpayOrderReq {
    return CheckSpayOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckSpayOrderReq>, I>>(object: I): CheckSpayOrderReq {
    const message = createBaseCheckSpayOrderReq();
    message.order_no = object.order_no ?? '';
    return message;
  }
};

function createBaseCheckSpayOrderRsp(): CheckSpayOrderRsp {
  return { result: undefined, order: undefined, purchase_info: undefined };
}

export const CheckSpayOrderRsp: MessageFns<CheckSpayOrderRsp> = {
  fromJSON(object: any): CheckSpayOrderRsp {
    return {
      result: isSet(object.result) ? SpayOrderResult.fromJSON(object.result) : undefined,
      order: isSet(object.order) ? OrderDetail.fromJSON(object.order) : undefined,
      purchase_info: isSet(object.purchase_info) ? PurchaseInfo.fromJSON(object.purchase_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<CheckSpayOrderRsp>, I>>(base?: I): CheckSpayOrderRsp {
    return CheckSpayOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckSpayOrderRsp>, I>>(object: I): CheckSpayOrderRsp {
    const message = createBaseCheckSpayOrderRsp();
    message.result =
      object.result !== undefined && object.result !== null ? SpayOrderResult.fromPartial(object.result) : undefined;
    message.order =
      object.order !== undefined && object.order !== null ? OrderDetail.fromPartial(object.order) : undefined;
    message.purchase_info =
      object.purchase_info !== undefined && object.purchase_info !== null
        ? PurchaseInfo.fromPartial(object.purchase_info)
        : undefined;
    return message;
  }
};

function createBasePayCancelReq(): PayCancelReq {
  return { checksum: '', order_no: '', reason: '' };
}

export const PayCancelReq: MessageFns<PayCancelReq> = {
  fromJSON(object: any): PayCancelReq {
    return {
      checksum: isSet(object.checksum) ? globalThis.String(object.checksum) : '',
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      reason: isSet(object.reason) ? globalThis.String(object.reason) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayCancelReq>, I>>(base?: I): PayCancelReq {
    return PayCancelReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelReq>, I>>(object: I): PayCancelReq {
    const message = createBasePayCancelReq();
    message.checksum = object.checksum ?? '';
    message.order_no = object.order_no ?? '';
    message.reason = object.reason ?? '';
    return message;
  }
};

function createBasePayCancelRsp(): PayCancelRsp {
  return { result: undefined };
}

export const PayCancelRsp: MessageFns<PayCancelRsp> = {
  fromJSON(object: any): PayCancelRsp {
    return { result: isSet(object.result) ? SpayOrderResult.fromJSON(object.result) : undefined };
  },

  create<I extends Exact<DeepPartial<PayCancelRsp>, I>>(base?: I): PayCancelRsp {
    return PayCancelRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelRsp>, I>>(object: I): PayCancelRsp {
    const message = createBasePayCancelRsp();
    message.result =
      object.result !== undefined && object.result !== null ? SpayOrderResult.fromPartial(object.result) : undefined;
    return message;
  }
};

function createBaseSpayUserOrderReq(): SpayUserOrderReq {
  return { page: undefined, pay_type: '', start_time: 0, end_time: 0, ext: {} };
}

export const SpayUserOrderReq: MessageFns<SpayUserOrderReq> = {
  fromJSON(object: any): SpayUserOrderReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<SpayUserOrderReq>, I>>(base?: I): SpayUserOrderReq {
    return SpayUserOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpayUserOrderReq>, I>>(object: I): SpayUserOrderReq {
    const message = createBaseSpayUserOrderReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.pay_type = object.pay_type ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseSpayUserOrderReq_ExtEntry(): SpayUserOrderReq_ExtEntry {
  return { key: '', value: '' };
}

export const SpayUserOrderReq_ExtEntry: MessageFns<SpayUserOrderReq_ExtEntry> = {
  fromJSON(object: any): SpayUserOrderReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SpayUserOrderReq_ExtEntry>, I>>(base?: I): SpayUserOrderReq_ExtEntry {
    return SpayUserOrderReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpayUserOrderReq_ExtEntry>, I>>(object: I): SpayUserOrderReq_ExtEntry {
    const message = createBaseSpayUserOrderReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSpayUserOrderRsp(): SpayUserOrderRsp {
  return { page: undefined, list: [] };
}

export const SpayUserOrderRsp: MessageFns<SpayUserOrderRsp> = {
  fromJSON(object: any): SpayUserOrderRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => OrderDetail.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SpayUserOrderRsp>, I>>(base?: I): SpayUserOrderRsp {
    return SpayUserOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpayUserOrderRsp>, I>>(object: I): SpayUserOrderRsp {
    const message = createBaseSpayUserOrderRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => OrderDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSkuInfo(): SkuInfo {
  return {
    sku_type: 0,
    name: '',
    name_lang: {},
    desc: '',
    desc_lang: {},
    image: '',
    tag_image: '',
    tag: '',
    tag_lang: {},
    price: 0,
    unit: 0,
    unit_str: '',
    amount: 0,
    currency_type: 0,
    num: 0,
    additional_num: 0,
    discount_rate: 0,
    goods_id: 0,
    limit_type: 0,
    target_price: 0,
    target_unit: 0,
    ext: {},
    items: []
  };
}

export const SkuInfo: MessageFns<SkuInfo> = {
  fromJSON(object: any): SkuInfo {
    return {
      sku_type: isSet(object.sku_type) ? spaySkuTypeFromJSON(object.sku_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_lang: isObject(object.name_lang)
        ? Object.entries(object.name_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      desc_lang: isObject(object.desc_lang)
        ? Object.entries(object.desc_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      tag_image: isSet(object.tag_image) ? globalThis.String(object.tag_image) : '',
      tag: isSet(object.tag) ? globalThis.String(object.tag) : '',
      tag_lang: isObject(object.tag_lang)
        ? Object.entries(object.tag_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      unit: isSet(object.unit) ? spayUnitFromJSON(object.unit) : 0,
      unit_str: isSet(object.unit_str) ? globalThis.String(object.unit_str) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency_type: isSet(object.currency_type) ? spayCurrencyTypeFromJSON(object.currency_type) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      additional_num: isSet(object.additional_num) ? globalThis.Number(object.additional_num) : 0,
      discount_rate: isSet(object.discount_rate) ? globalThis.Number(object.discount_rate) : 0,
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      limit_type: isSet(object.limit_type) ? skuLimitTypeFromJSON(object.limit_type) : 0,
      target_price: isSet(object.target_price) ? globalThis.Number(object.target_price) : 0,
      target_unit: isSet(object.target_unit) ? spayUnitFromJSON(object.target_unit) : 0,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => SkuItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SkuInfo>, I>>(base?: I): SkuInfo {
    return SkuInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuInfo>, I>>(object: I): SkuInfo {
    const message = createBaseSkuInfo();
    message.sku_type = object.sku_type ?? 0;
    message.name = object.name ?? '';
    message.name_lang = Object.entries(object.name_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.desc = object.desc ?? '';
    message.desc_lang = Object.entries(object.desc_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.image = object.image ?? '';
    message.tag_image = object.tag_image ?? '';
    message.tag = object.tag ?? '';
    message.tag_lang = Object.entries(object.tag_lang ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.price = object.price ?? 0;
    message.unit = object.unit ?? 0;
    message.unit_str = object.unit_str ?? '';
    message.amount = object.amount ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.num = object.num ?? 0;
    message.additional_num = object.additional_num ?? 0;
    message.discount_rate = object.discount_rate ?? 0;
    message.goods_id = object.goods_id ?? 0;
    message.limit_type = object.limit_type ?? 0;
    message.target_price = object.target_price ?? 0;
    message.target_unit = object.target_unit ?? 0;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.items = object.items?.map(e => SkuItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSkuInfo_NameLangEntry(): SkuInfo_NameLangEntry {
  return { key: '', value: '' };
}

export const SkuInfo_NameLangEntry: MessageFns<SkuInfo_NameLangEntry> = {
  fromJSON(object: any): SkuInfo_NameLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SkuInfo_NameLangEntry>, I>>(base?: I): SkuInfo_NameLangEntry {
    return SkuInfo_NameLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuInfo_NameLangEntry>, I>>(object: I): SkuInfo_NameLangEntry {
    const message = createBaseSkuInfo_NameLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSkuInfo_DescLangEntry(): SkuInfo_DescLangEntry {
  return { key: '', value: '' };
}

export const SkuInfo_DescLangEntry: MessageFns<SkuInfo_DescLangEntry> = {
  fromJSON(object: any): SkuInfo_DescLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SkuInfo_DescLangEntry>, I>>(base?: I): SkuInfo_DescLangEntry {
    return SkuInfo_DescLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuInfo_DescLangEntry>, I>>(object: I): SkuInfo_DescLangEntry {
    const message = createBaseSkuInfo_DescLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSkuInfo_TagLangEntry(): SkuInfo_TagLangEntry {
  return { key: '', value: '' };
}

export const SkuInfo_TagLangEntry: MessageFns<SkuInfo_TagLangEntry> = {
  fromJSON(object: any): SkuInfo_TagLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SkuInfo_TagLangEntry>, I>>(base?: I): SkuInfo_TagLangEntry {
    return SkuInfo_TagLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuInfo_TagLangEntry>, I>>(object: I): SkuInfo_TagLangEntry {
    const message = createBaseSkuInfo_TagLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSkuInfo_ExtEntry(): SkuInfo_ExtEntry {
  return { key: '', value: '' };
}

export const SkuInfo_ExtEntry: MessageFns<SkuInfo_ExtEntry> = {
  fromJSON(object: any): SkuInfo_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SkuInfo_ExtEntry>, I>>(base?: I): SkuInfo_ExtEntry {
    return SkuInfo_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuInfo_ExtEntry>, I>>(object: I): SkuInfo_ExtEntry {
    const message = createBaseSkuInfo_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSkuItem(): SkuItem {
  return {
    item_id: 0,
    sku: '',
    price: 0,
    pay_info: undefined,
    mid: '',
    jump_type: 0,
    jump_url: '',
    unit: '',
    unit_str: '',
    show_price: '',
    show_unit: ''
  };
}

export const SkuItem: MessageFns<SkuItem> = {
  fromJSON(object: any): SkuItem {
    return {
      item_id: isSet(object.item_id) ? globalThis.Number(object.item_id) : 0,
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      pay_info: isSet(object.pay_info) ? PayInfo.fromJSON(object.pay_info) : undefined,
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      jump_url: isSet(object.jump_url) ? globalThis.String(object.jump_url) : '',
      unit: isSet(object.unit) ? globalThis.String(object.unit) : '',
      unit_str: isSet(object.unit_str) ? globalThis.String(object.unit_str) : '',
      show_price: isSet(object.show_price) ? globalThis.String(object.show_price) : '',
      show_unit: isSet(object.show_unit) ? globalThis.String(object.show_unit) : ''
    };
  },

  create<I extends Exact<DeepPartial<SkuItem>, I>>(base?: I): SkuItem {
    return SkuItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SkuItem>, I>>(object: I): SkuItem {
    const message = createBaseSkuItem();
    message.item_id = object.item_id ?? 0;
    message.sku = object.sku ?? '';
    message.price = object.price ?? 0;
    message.pay_info =
      object.pay_info !== undefined && object.pay_info !== null ? PayInfo.fromPartial(object.pay_info) : undefined;
    message.mid = object.mid ?? '';
    message.jump_type = object.jump_type ?? 0;
    message.jump_url = object.jump_url ?? '';
    message.unit = object.unit ?? '';
    message.unit_str = object.unit_str ?? '';
    message.show_price = object.show_price ?? '';
    message.show_unit = object.show_unit ?? '';
    return message;
  }
};

function createBasePayInfo(): PayInfo {
  return { pay_type: '', name: '', name_lang: {}, image: '', pay_code: '', pay_code_name: '', pay_code_img: '' };
}

export const PayInfo: MessageFns<PayInfo> = {
  fromJSON(object: any): PayInfo {
    return {
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      name_lang: isObject(object.name_lang)
        ? Object.entries(object.name_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      pay_code_name: isSet(object.pay_code_name) ? globalThis.String(object.pay_code_name) : '',
      pay_code_img: isSet(object.pay_code_img) ? globalThis.String(object.pay_code_img) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayInfo>, I>>(base?: I): PayInfo {
    return PayInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayInfo>, I>>(object: I): PayInfo {
    const message = createBasePayInfo();
    message.pay_type = object.pay_type ?? '';
    message.name = object.name ?? '';
    message.name_lang = Object.entries(object.name_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.image = object.image ?? '';
    message.pay_code = object.pay_code ?? '';
    message.pay_code_name = object.pay_code_name ?? '';
    message.pay_code_img = object.pay_code_img ?? '';
    return message;
  }
};

function createBasePayInfo_NameLangEntry(): PayInfo_NameLangEntry {
  return { key: '', value: '' };
}

export const PayInfo_NameLangEntry: MessageFns<PayInfo_NameLangEntry> = {
  fromJSON(object: any): PayInfo_NameLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayInfo_NameLangEntry>, I>>(base?: I): PayInfo_NameLangEntry {
    return PayInfo_NameLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayInfo_NameLangEntry>, I>>(object: I): PayInfo_NameLangEntry {
    const message = createBasePayInfo_NameLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGooglePlayResult(): GooglePlayResult {
  return { mid: '', uid: 0, order_no: '', pay_order_id: '', pay_amount: 0 };
}

export const GooglePlayResult: MessageFns<GooglePlayResult> = {
  fromJSON(object: any): GooglePlayResult {
    return {
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<GooglePlayResult>, I>>(base?: I): GooglePlayResult {
    return GooglePlayResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GooglePlayResult>, I>>(object: I): GooglePlayResult {
    const message = createBaseGooglePlayResult();
    message.mid = object.mid ?? '';
    message.uid = object.uid ?? 0;
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    return message;
  }
};

function createBaseApplePayResult(): ApplePayResult {
  return { mid: '', uid: 0, order_no: '', pay_order_id: '', pay_amount: 0 };
}

export const ApplePayResult: MessageFns<ApplePayResult> = {
  fromJSON(object: any): ApplePayResult {
    return {
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<ApplePayResult>, I>>(base?: I): ApplePayResult {
    return ApplePayResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplePayResult>, I>>(object: I): ApplePayResult {
    const message = createBaseApplePayResult();
    message.mid = object.mid ?? '';
    message.uid = object.uid ?? 0;
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    return message;
  }
};

function createBaseThirdPartyPayResult(): ThirdPartyPayResult {
  return {
    mid: '',
    uid: '',
    order_no: '',
    pay_order_id: '',
    plat_order_id: '',
    pay_amount: 0,
    pay_type: '',
    jump_url: '',
    jump_type: 0
  };
}

export const ThirdPartyPayResult: MessageFns<ThirdPartyPayResult> = {
  fromJSON(object: any): ThirdPartyPayResult {
    return {
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      plat_order_id: isSet(object.plat_order_id) ? globalThis.String(object.plat_order_id) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      jump_url: isSet(object.jump_url) ? globalThis.String(object.jump_url) : '',
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ThirdPartyPayResult>, I>>(base?: I): ThirdPartyPayResult {
    return ThirdPartyPayResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ThirdPartyPayResult>, I>>(object: I): ThirdPartyPayResult {
    const message = createBaseThirdPartyPayResult();
    message.mid = object.mid ?? '';
    message.uid = object.uid ?? '';
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.plat_order_id = object.plat_order_id ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.jump_url = object.jump_url ?? '';
    message.jump_type = object.jump_type ?? 0;
    return message;
  }
};

function createBaseSpayOrderInfo(): SpayOrderInfo {
  return {
    order_no: '',
    pay_order_id: '',
    plat_order_id: '',
    refund_id: '',
    pay_amount: 0,
    unit: 0,
    unit_str: '',
    pay_type: '',
    num: 0,
    pay_amount_dollar: '',
    item: undefined
  };
}

export const SpayOrderInfo: MessageFns<SpayOrderInfo> = {
  fromJSON(object: any): SpayOrderInfo {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      plat_order_id: isSet(object.plat_order_id) ? globalThis.String(object.plat_order_id) : '',
      refund_id: isSet(object.refund_id) ? globalThis.String(object.refund_id) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0,
      unit: isSet(object.unit) ? spayUnitFromJSON(object.unit) : 0,
      unit_str: isSet(object.unit_str) ? globalThis.String(object.unit_str) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      pay_amount_dollar: isSet(object.pay_amount_dollar) ? globalThis.String(object.pay_amount_dollar) : '',
      item: isSet(object.item) ? SkuItem.fromJSON(object.item) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SpayOrderInfo>, I>>(base?: I): SpayOrderInfo {
    return SpayOrderInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpayOrderInfo>, I>>(object: I): SpayOrderInfo {
    const message = createBaseSpayOrderInfo();
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.plat_order_id = object.plat_order_id ?? '';
    message.refund_id = object.refund_id ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    message.unit = object.unit ?? 0;
    message.unit_str = object.unit_str ?? '';
    message.pay_type = object.pay_type ?? '';
    message.num = object.num ?? 0;
    message.pay_amount_dollar = object.pay_amount_dollar ?? '';
    message.item = object.item !== undefined && object.item !== null ? SkuItem.fromPartial(object.item) : undefined;
    return message;
  }
};

function createBasePurchaseInfo(): PurchaseInfo {
  return { amount: 0, recharge_amount: 0, ext: {} };
}

export const PurchaseInfo: MessageFns<PurchaseInfo> = {
  fromJSON(object: any): PurchaseInfo {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      recharge_amount: isSet(object.recharge_amount) ? globalThis.Number(object.recharge_amount) : 0,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<PurchaseInfo>, I>>(base?: I): PurchaseInfo {
    return PurchaseInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PurchaseInfo>, I>>(object: I): PurchaseInfo {
    const message = createBasePurchaseInfo();
    message.amount = object.amount ?? 0;
    message.recharge_amount = object.recharge_amount ?? 0;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBasePurchaseInfo_ExtEntry(): PurchaseInfo_ExtEntry {
  return { key: '', value: '' };
}

export const PurchaseInfo_ExtEntry: MessageFns<PurchaseInfo_ExtEntry> = {
  fromJSON(object: any): PurchaseInfo_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PurchaseInfo_ExtEntry>, I>>(base?: I): PurchaseInfo_ExtEntry {
    return PurchaseInfo_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PurchaseInfo_ExtEntry>, I>>(object: I): PurchaseInfo_ExtEntry {
    const message = createBasePurchaseInfo_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSpayOrderResult(): SpayOrderResult {
  return { order_no: '', pay_order_id: '', plat_order_id: '', status: 0, msg: '' };
}

export const SpayOrderResult: MessageFns<SpayOrderResult> = {
  fromJSON(object: any): SpayOrderResult {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      plat_order_id: isSet(object.plat_order_id) ? globalThis.String(object.plat_order_id) : '',
      status: isSet(object.status) ? spayStatusFromJSON(object.status) : 0,
      msg: isSet(object.msg) ? globalThis.String(object.msg) : ''
    };
  },

  create<I extends Exact<DeepPartial<SpayOrderResult>, I>>(base?: I): SpayOrderResult {
    return SpayOrderResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpayOrderResult>, I>>(object: I): SpayOrderResult {
    const message = createBaseSpayOrderResult();
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.plat_order_id = object.plat_order_id ?? '';
    message.status = object.status ?? 0;
    message.msg = object.msg ?? '';
    return message;
  }
};

function createBaseOrderDetail(): OrderDetail {
  return {
    uid: 0,
    fuid: 0,
    order_info: undefined,
    mid: '',
    currency_type: 0,
    amount: 0,
    client_type: '',
    client_version: '',
    did: '',
    channel_id: '',
    status: 0,
    pay_time: 0,
    refund_status: 0,
    refund_time: 0,
    goods_name: '',
    ctime: 0,
    ext: {}
  };
}

export const OrderDetail: MessageFns<OrderDetail> = {
  fromJSON(object: any): OrderDetail {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      fuid: isSet(object.fuid) ? globalThis.Number(object.fuid) : 0,
      order_info: isSet(object.order_info) ? SpayOrderInfo.fromJSON(object.order_info) : undefined,
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      currency_type: isSet(object.currency_type) ? spayCurrencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      client_type: isSet(object.client_type) ? globalThis.String(object.client_type) : '',
      client_version: isSet(object.client_version) ? globalThis.String(object.client_version) : '',
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      channel_id: isSet(object.channel_id) ? globalThis.String(object.channel_id) : '',
      status: isSet(object.status) ? spayStatusFromJSON(object.status) : 0,
      pay_time: isSet(object.pay_time) ? globalThis.Number(object.pay_time) : 0,
      refund_status: isSet(object.refund_status) ? spayRefundStatusFromJSON(object.refund_status) : 0,
      refund_time: isSet(object.refund_time) ? globalThis.Number(object.refund_time) : 0,
      goods_name: isSet(object.goods_name) ? globalThis.String(object.goods_name) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<OrderDetail>, I>>(base?: I): OrderDetail {
    return OrderDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderDetail>, I>>(object: I): OrderDetail {
    const message = createBaseOrderDetail();
    message.uid = object.uid ?? 0;
    message.fuid = object.fuid ?? 0;
    message.order_info =
      object.order_info !== undefined && object.order_info !== null
        ? SpayOrderInfo.fromPartial(object.order_info)
        : undefined;
    message.mid = object.mid ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.client_type = object.client_type ?? '';
    message.client_version = object.client_version ?? '';
    message.did = object.did ?? '';
    message.channel_id = object.channel_id ?? '';
    message.status = object.status ?? 0;
    message.pay_time = object.pay_time ?? 0;
    message.refund_status = object.refund_status ?? 0;
    message.refund_time = object.refund_time ?? 0;
    message.goods_name = object.goods_name ?? '';
    message.ctime = object.ctime ?? 0;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseOrderDetail_ExtEntry(): OrderDetail_ExtEntry {
  return { key: '', value: '' };
}

export const OrderDetail_ExtEntry: MessageFns<OrderDetail_ExtEntry> = {
  fromJSON(object: any): OrderDetail_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<OrderDetail_ExtEntry>, I>>(base?: I): OrderDetail_ExtEntry {
    return OrderDetail_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderDetail_ExtEntry>, I>>(object: I): OrderDetail_ExtEntry {
    const message = createBaseOrderDetail_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

/** 支付 sdk 协议 */
export type SpayDefinition = typeof SpayDefinition;
export const SpayDefinition = {
  name: 'Spay',
  fullName: 'pbspay.Spay',
  methods: {
    /** 获取sku列表，对sku做了分组处理，并增加了支持的支付方式 */
    listSku: {
      name: 'ListSku',
      requestType: ListSkuReq,
      requestStream: false,
      responseType: ListSkuRsp,
      responseStream: false,
      options: {}
    },
    /** 创单接口 */
    createSpayOrder: {
      name: 'CreateSpayOrder',
      requestType: CreateSpayOrderReq,
      requestStream: false,
      responseType: CreateSpayOrderRsp,
      responseStream: false,
      options: {}
    },
    /**
     * SDK支付回调，终端支付成功后通过此接口通知后台查询订单结果
     * 只针对 apple_pay google_play，其他支付渠道有回调通知，不需要主动验单
     */
    payCallback: {
      name: 'PayCallback',
      requestType: PayCallbackReq,
      requestStream: false,
      responseType: PayCallbackRsp,
      responseStream: false,
      options: {}
    },
    /** 订单状态查询 */
    checkSpayOrder: {
      name: 'CheckSpayOrder',
      requestType: CheckSpayOrderReq,
      requestStream: false,
      responseType: CheckSpayOrderRsp,
      responseStream: false,
      options: {}
    },
    /**
     * 取消支付，在创建订单后，用户主动取消支付，客户端调用该接口更新订单状态
     * 该接口对 apple_pay google_play huaweipay 有效，参考：https://nemo.yuque.com/pay_system/interface/haaq01#pePaa
     */
    payCancel: {
      name: 'PayCancel',
      requestType: PayCancelReq,
      requestStream: false,
      responseType: PayCancelRsp,
      responseStream: false,
      options: {}
    },
    /** 用户充值订单分页查询 */
    spayUserOrder: {
      name: 'SpayUserOrder',
      requestType: SpayUserOrderReq,
      requestStream: false,
      responseType: SpayUserOrderRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
