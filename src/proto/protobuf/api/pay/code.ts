// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/pay/code.proto

/* eslint-disable */

export const protobufPackage = 'pbspay';

/**
 * 支付错误码定义
 * 公共错误码查看：https://gitit.cc/social/protocol/api/net/-/blob/master/common-net.proto?ref_type=heads#L9
 */
export enum SpayCode {
  SPAY_CODE_NONE = 0,
  /** SPAY_CODE_BASE - 支付协议号段 >= 60000 */
  SPAY_CODE_BASE = 60000,
  /** SPAY_CODE_APP_NOT_SUPPORT - 当前 app 不支持 */
  SPAY_CODE_APP_NOT_SUPPORT = 60001,
  /** SPAY_CODE_GOODS_NOT_FOUND - 商品（sku）不存在 */
  SPAY_CODE_GOODS_NOT_FOUND = 60002,
  /** SPAY_CODE_ORDER_NOT_FOUND - 订单不存在 */
  SPAY_CODE_ORDER_NOT_FOUND = 60003,
  /** SPAY_CODE_INVALID_CHECKSUM - 无效签名 */
  SPAY_CODE_INVALID_CHECKSUM = 60004,
  /** SPAY_CODE_PAY_DONE_REPEAT - 支付完成重复通知 */
  SPAY_CODE_PAY_DONE_REPEAT = 60005,
  /** SPAY_CODE_SUBSCRIBE_REPEAT - 重复订阅 */
  SPAY_CODE_SUBSCRIBE_REPEAT = 60006,
  /**
   * SPAY_CODE_APPLE_ORDER_NOT_MATCH - 验单状态码，60000 + 支付中台返回码（4位）
   * 支付中台返回码参考：https://nemo.yuque.com/pay_system/interface/haaq01#bQB7v
   * eg: apply_pay NOT_MATCH 的状态码为：60000 + 1002 = 61002
   * 为了后续增加一些无需处理逻辑的状态码，不用改动协议，其他状态码动态计算返回
   */
  SPAY_CODE_APPLE_ORDER_NOT_MATCH = 61002,
  UNRECOGNIZED = -1
}

export function spayCodeFromJSON(object: any): SpayCode {
  switch (object) {
    case 0:
    case 'SPAY_CODE_NONE':
      return SpayCode.SPAY_CODE_NONE;
    case 60000:
    case 'SPAY_CODE_BASE':
      return SpayCode.SPAY_CODE_BASE;
    case 60001:
    case 'SPAY_CODE_APP_NOT_SUPPORT':
      return SpayCode.SPAY_CODE_APP_NOT_SUPPORT;
    case 60002:
    case 'SPAY_CODE_GOODS_NOT_FOUND':
      return SpayCode.SPAY_CODE_GOODS_NOT_FOUND;
    case 60003:
    case 'SPAY_CODE_ORDER_NOT_FOUND':
      return SpayCode.SPAY_CODE_ORDER_NOT_FOUND;
    case 60004:
    case 'SPAY_CODE_INVALID_CHECKSUM':
      return SpayCode.SPAY_CODE_INVALID_CHECKSUM;
    case 60005:
    case 'SPAY_CODE_PAY_DONE_REPEAT':
      return SpayCode.SPAY_CODE_PAY_DONE_REPEAT;
    case 60006:
    case 'SPAY_CODE_SUBSCRIBE_REPEAT':
      return SpayCode.SPAY_CODE_SUBSCRIBE_REPEAT;
    case 61002:
    case 'SPAY_CODE_APPLE_ORDER_NOT_MATCH':
      return SpayCode.SPAY_CODE_APPLE_ORDER_NOT_MATCH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpayCode.UNRECOGNIZED;
  }
}
