// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/pay/common.proto

/* eslint-disable */

export const protobufPackage = 'pbspay';

/** 周期类型 */
export enum PeriodType {
  /** PERIOD_TYPE_NONE - 无意义 */
  PERIOD_TYPE_NONE = 0,
  /** PERIOD_TYPE_WEEKLY - 每周 */
  PERIOD_TYPE_WEEKLY = 10,
  /** PERIOD_TYPE_MONTHLY - 每月 */
  PERIOD_TYPE_MONTHLY = 20,
  /** PERIOD_TYPE_QUARTERLY - 每季度 */
  PERIOD_TYPE_QUARTERLY = 30,
  /** PERIOD_TYPE_SEMI_ANNUALLY - 每半年 */
  PERIOD_TYPE_SEMI_ANNUALLY = 35,
  /** PERIOD_TYPE_YEARLY - 每年 */
  PERIOD_TYPE_YEARLY = 40,
  /** PERIOD_TYPE_CUSTOM - 自定义 */
  PERIOD_TYPE_CUSTOM = 100,
  UNRECOGNIZED = -1
}

export function periodTypeFromJSON(object: any): PeriodType {
  switch (object) {
    case 0:
    case 'PERIOD_TYPE_NONE':
      return PeriodType.PERIOD_TYPE_NONE;
    case 10:
    case 'PERIOD_TYPE_WEEKLY':
      return PeriodType.PERIOD_TYPE_WEEKLY;
    case 20:
    case 'PERIOD_TYPE_MONTHLY':
      return PeriodType.PERIOD_TYPE_MONTHLY;
    case 30:
    case 'PERIOD_TYPE_QUARTERLY':
      return PeriodType.PERIOD_TYPE_QUARTERLY;
    case 35:
    case 'PERIOD_TYPE_SEMI_ANNUALLY':
      return PeriodType.PERIOD_TYPE_SEMI_ANNUALLY;
    case 40:
    case 'PERIOD_TYPE_YEARLY':
      return PeriodType.PERIOD_TYPE_YEARLY;
    case 100:
    case 'PERIOD_TYPE_CUSTOM':
      return PeriodType.PERIOD_TYPE_CUSTOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PeriodType.UNRECOGNIZED;
  }
}

/** 折扣类型 */
export enum DiscountType {
  /** DISCOUNT_TYPE_NONE - 无折扣 */
  DISCOUNT_TYPE_NONE = 0,
  /** DISCOUNT_TYPE_PERCENTAGE - 百分比折扣 */
  DISCOUNT_TYPE_PERCENTAGE = 1,
  /** DISCOUNT_TYPE_FIXED_AMOUNT - 固定金额减免 */
  DISCOUNT_TYPE_FIXED_AMOUNT = 2,
  UNRECOGNIZED = -1
}

export function discountTypeFromJSON(object: any): DiscountType {
  switch (object) {
    case 0:
    case 'DISCOUNT_TYPE_NONE':
      return DiscountType.DISCOUNT_TYPE_NONE;
    case 1:
    case 'DISCOUNT_TYPE_PERCENTAGE':
      return DiscountType.DISCOUNT_TYPE_PERCENTAGE;
    case 2:
    case 'DISCOUNT_TYPE_FIXED_AMOUNT':
      return DiscountType.DISCOUNT_TYPE_FIXED_AMOUNT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return DiscountType.UNRECOGNIZED;
  }
}

/** 支付方式类别枚举 */
export enum PaymentType {
  PAYMENT_TYPE_NONE = 0,
  PAYMENT_TYPE_APPLE = 1,
  PAYMENT_TYPE_GOOGLE = 2,
  PAYMENT_TYPE_QUARK_PAY = 10,
  PAYMENT_TYPE_PAYER_MAX = 11,
  UNRECOGNIZED = -1
}

export function paymentTypeFromJSON(object: any): PaymentType {
  switch (object) {
    case 0:
    case 'PAYMENT_TYPE_NONE':
      return PaymentType.PAYMENT_TYPE_NONE;
    case 1:
    case 'PAYMENT_TYPE_APPLE':
      return PaymentType.PAYMENT_TYPE_APPLE;
    case 2:
    case 'PAYMENT_TYPE_GOOGLE':
      return PaymentType.PAYMENT_TYPE_GOOGLE;
    case 10:
    case 'PAYMENT_TYPE_QUARK_PAY':
      return PaymentType.PAYMENT_TYPE_QUARK_PAY;
    case 11:
    case 'PAYMENT_TYPE_PAYER_MAX':
      return PaymentType.PAYMENT_TYPE_PAYER_MAX;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PaymentType.UNRECOGNIZED;
  }
}

/** 跳转类型 */
export enum JumpType {
  /** JUMP_TYPE_NONE - 无意义 */
  JUMP_TYPE_NONE = 0,
  /** JUMP_TYPE_H5 - h5 跳转 */
  JUMP_TYPE_H5 = 10,
  /** JUMP_TYPE_DEEPLINK - deeplink 跳转，直接拉起支付app */
  JUMP_TYPE_DEEPLINK = 20,
  UNRECOGNIZED = -1
}

export function jumpTypeFromJSON(object: any): JumpType {
  switch (object) {
    case 0:
    case 'JUMP_TYPE_NONE':
      return JumpType.JUMP_TYPE_NONE;
    case 10:
    case 'JUMP_TYPE_H5':
      return JumpType.JUMP_TYPE_H5;
    case 20:
    case 'JUMP_TYPE_DEEPLINK':
      return JumpType.JUMP_TYPE_DEEPLINK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return JumpType.UNRECOGNIZED;
  }
}

/** 订单状态 */
export enum OrderStatus {
  ORDER_STATUS_NONE = 0,
  /** ORDER_STATUS_PENDING - 待支付 */
  ORDER_STATUS_PENDING = 10,
  /** ORDER_STATUS_PAID - 已支付 */
  ORDER_STATUS_PAID = 20,
  /** ORDER_STATUS_CANCELED - 已取消 */
  ORDER_STATUS_CANCELED = 30,
  /** ORDER_STATUS_REFUNDED - 已退款 */
  ORDER_STATUS_REFUNDED = 40,
  UNRECOGNIZED = -1
}

export function orderStatusFromJSON(object: any): OrderStatus {
  switch (object) {
    case 0:
    case 'ORDER_STATUS_NONE':
      return OrderStatus.ORDER_STATUS_NONE;
    case 10:
    case 'ORDER_STATUS_PENDING':
      return OrderStatus.ORDER_STATUS_PENDING;
    case 20:
    case 'ORDER_STATUS_PAID':
      return OrderStatus.ORDER_STATUS_PAID;
    case 30:
    case 'ORDER_STATUS_CANCELED':
      return OrderStatus.ORDER_STATUS_CANCELED;
    case 40:
    case 'ORDER_STATUS_REFUNDED':
      return OrderStatus.ORDER_STATUS_REFUNDED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return OrderStatus.UNRECOGNIZED;
  }
}

export interface Period {
  /** 周期类型 */
  type: PeriodType;
  /** 自定义周期天数，例如每10天，type=PERIOD_TYPE_CUSTOM时有效 */
  days: number;
}

/** 价格信息 */
export interface Price {
  /** 货币代码，例如 CNY、USD */
  currency: string;
  /** 金额，单位分 */
  amount: number;
  /** 折扣，只用于展示，amount已经表示为实际价格 */
  discount: Discount | undefined;
  /** 如果不为空，则使用该字段作为展示货币，例如 $、¥ */
  show_currency: string;
  /** 如果不为空，则使用该字段作为展示金额，例如 5、9.99 */
  show_amount: string;
}

/** 折扣 */
export interface Discount {
  /** 折扣类型 */
  type: DiscountType;
  /**
   * 当类型为百分比时取值(0-10000)(开区间)，单位0.01%，例如 9500表示95折、95%
   * 当类型为固定金额时表示减免金额，单位分
   */
  amount: number;
}

/** 支付方式 */
export interface Payment {
  /** 支付方式类别，终端可以用来区分平台还是第三方 */
  payment_type: PaymentType;
  /** 支付方式代码，终端无需关注具体值，接口透传即可 */
  payment_code: string;
  /** 支付渠道配置的订阅项ID */
  sku: string;
  /** 图片URL */
  image: string;
  /** 名称 */
  name_i18n: I18NString | undefined;
  /** 价格 */
  price: Price | undefined;
  /** 跳转 */
  jump: Jump | undefined;
}

/** 支付账号 */
export interface PaymentAccount {}

/** 跳转 */
export interface Jump {
  type: JumpType;
  url: string;
}

/** 业务数据 */
export interface BizData {
  /** 数据类型 */
  type: BizData_Type;
  /** 数据内容 */
  data: Uint8Array;
}

/** 数据类型 */
export enum BizData_Type {
  BIZ_DATA_TYPE_NONE = 0,
  /** BIZ_DATA_TYPE_JSON - JSON */
  BIZ_DATA_TYPE_JSON = 10,
  /** BIZ_DATA_TYPE_PROTOBUF - Protobuf */
  BIZ_DATA_TYPE_PROTOBUF = 20,
  UNRECOGNIZED = -1
}

export function bizData_TypeFromJSON(object: any): BizData_Type {
  switch (object) {
    case 0:
    case 'BIZ_DATA_TYPE_NONE':
      return BizData_Type.BIZ_DATA_TYPE_NONE;
    case 10:
    case 'BIZ_DATA_TYPE_JSON':
      return BizData_Type.BIZ_DATA_TYPE_JSON;
    case 20:
    case 'BIZ_DATA_TYPE_PROTOBUF':
      return BizData_Type.BIZ_DATA_TYPE_PROTOBUF;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BizData_Type.UNRECOGNIZED;
  }
}

/** 订单基本信息 */
export interface BaseOrder {
  /** 订单ID */
  order_id: string;
  /** 订单状态 */
  status: OrderStatus;
  /** 创建时间，单位秒 */
  create_at: number;
  /** 支付时间，单位秒 */
  pay_at: number;
  /** 取消时间，单位秒 */
  cancel_at: number;
  /** 支付情况 */
  payment: Payment | undefined;
}

/** 苹果订单票据 */
export interface ApplePayReceipt {
  /** 支付成功的凭证 */
  receipt: string;
  /** Apple 交易 transaction_id */
  purchase_id: string;
  /** 苹果支付环境变量 */
  environment: string;
  /** 原始交易ID */
  original_transaction_id: string;
}

/** 谷歌订单票据 */
export interface GooglePlayReceipt {
  /** app包名，例如：com.flatfish.cal.privacy */
  packageName: string;
  /** google的商品id，例如：hidex_month_a_2_29.99 */
  productId: string;
  /** google的购买token，例如：iklaeinfeplfhiamepdokkjj.AO-J1OxYAFAMFatI1-iziNTCuvXeLvHVVsdOuKr-ufTzN5YKIAp7Gf8IH */
  purchaseToken: string;
  /** 购买方式，SUBS 订阅, INAPP 一次性购买 */
  sku_type: string;
}

/** 付款票据 */
export interface PaymentReceipt {
  apple_receipt: ApplePayReceipt | undefined;
  google_receipt: GooglePlayReceipt | undefined;
}

/** 字符串列表 */
export interface StringList {
  values: string[];
}

/** 多语言文本 */
export interface I18NString {
  /** 默认值，如果找不到对应语言，则使用该值 */
  default: string;
  /** 多语言 */
  i18n: { [key: string]: string };
}

export interface I18NString_I18nEntry {
  key: string;
  value: string;
}

function createBasePeriod(): Period {
  return { type: 0, days: 0 };
}

export const Period: MessageFns<Period> = {
  fromJSON(object: any): Period {
    return {
      type: isSet(object.type) ? periodTypeFromJSON(object.type) : 0,
      days: isSet(object.days) ? globalThis.Number(object.days) : 0
    };
  },

  create<I extends Exact<DeepPartial<Period>, I>>(base?: I): Period {
    return Period.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Period>, I>>(object: I): Period {
    const message = createBasePeriod();
    message.type = object.type ?? 0;
    message.days = object.days ?? 0;
    return message;
  }
};

function createBasePrice(): Price {
  return { currency: '', amount: 0, discount: undefined, show_currency: '', show_amount: '' };
}

export const Price: MessageFns<Price> = {
  fromJSON(object: any): Price {
    return {
      currency: isSet(object.currency) ? globalThis.String(object.currency) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      discount: isSet(object.discount) ? Discount.fromJSON(object.discount) : undefined,
      show_currency: isSet(object.show_currency) ? globalThis.String(object.show_currency) : '',
      show_amount: isSet(object.show_amount) ? globalThis.String(object.show_amount) : ''
    };
  },

  create<I extends Exact<DeepPartial<Price>, I>>(base?: I): Price {
    return Price.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Price>, I>>(object: I): Price {
    const message = createBasePrice();
    message.currency = object.currency ?? '';
    message.amount = object.amount ?? 0;
    message.discount =
      object.discount !== undefined && object.discount !== null ? Discount.fromPartial(object.discount) : undefined;
    message.show_currency = object.show_currency ?? '';
    message.show_amount = object.show_amount ?? '';
    return message;
  }
};

function createBaseDiscount(): Discount {
  return { type: 0, amount: 0 };
}

export const Discount: MessageFns<Discount> = {
  fromJSON(object: any): Discount {
    return {
      type: isSet(object.type) ? discountTypeFromJSON(object.type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<Discount>, I>>(base?: I): Discount {
    return Discount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Discount>, I>>(object: I): Discount {
    const message = createBaseDiscount();
    message.type = object.type ?? 0;
    message.amount = object.amount ?? 0;
    return message;
  }
};

function createBasePayment(): Payment {
  return {
    payment_type: 0,
    payment_code: '',
    sku: '',
    image: '',
    name_i18n: undefined,
    price: undefined,
    jump: undefined
  };
}

export const Payment: MessageFns<Payment> = {
  fromJSON(object: any): Payment {
    return {
      payment_type: isSet(object.payment_type) ? paymentTypeFromJSON(object.payment_type) : 0,
      payment_code: isSet(object.payment_code) ? globalThis.String(object.payment_code) : '',
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      name_i18n: isSet(object.name_i18n) ? I18NString.fromJSON(object.name_i18n) : undefined,
      price: isSet(object.price) ? Price.fromJSON(object.price) : undefined,
      jump: isSet(object.jump) ? Jump.fromJSON(object.jump) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Payment>, I>>(base?: I): Payment {
    return Payment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Payment>, I>>(object: I): Payment {
    const message = createBasePayment();
    message.payment_type = object.payment_type ?? 0;
    message.payment_code = object.payment_code ?? '';
    message.sku = object.sku ?? '';
    message.image = object.image ?? '';
    message.name_i18n =
      object.name_i18n !== undefined && object.name_i18n !== null
        ? I18NString.fromPartial(object.name_i18n)
        : undefined;
    message.price = object.price !== undefined && object.price !== null ? Price.fromPartial(object.price) : undefined;
    message.jump = object.jump !== undefined && object.jump !== null ? Jump.fromPartial(object.jump) : undefined;
    return message;
  }
};

function createBasePaymentAccount(): PaymentAccount {
  return {};
}

export const PaymentAccount: MessageFns<PaymentAccount> = {
  fromJSON(_: any): PaymentAccount {
    return {};
  },

  create<I extends Exact<DeepPartial<PaymentAccount>, I>>(base?: I): PaymentAccount {
    return PaymentAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentAccount>, I>>(_: I): PaymentAccount {
    const message = createBasePaymentAccount();
    return message;
  }
};

function createBaseJump(): Jump {
  return { type: 0, url: '' };
}

export const Jump: MessageFns<Jump> = {
  fromJSON(object: any): Jump {
    return {
      type: isSet(object.type) ? jumpTypeFromJSON(object.type) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : ''
    };
  },

  create<I extends Exact<DeepPartial<Jump>, I>>(base?: I): Jump {
    return Jump.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Jump>, I>>(object: I): Jump {
    const message = createBaseJump();
    message.type = object.type ?? 0;
    message.url = object.url ?? '';
    return message;
  }
};

function createBaseBizData(): BizData {
  return { type: 0, data: new Uint8Array(0) };
}

export const BizData: MessageFns<BizData> = {
  fromJSON(object: any): BizData {
    return {
      type: isSet(object.type) ? bizData_TypeFromJSON(object.type) : 0,
      data: isSet(object.data) ? bytesFromBase64(object.data) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<BizData>, I>>(base?: I): BizData {
    return BizData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizData>, I>>(object: I): BizData {
    const message = createBaseBizData();
    message.type = object.type ?? 0;
    message.data = object.data ?? new Uint8Array(0);
    return message;
  }
};

function createBaseBaseOrder(): BaseOrder {
  return { order_id: '', status: 0, create_at: 0, pay_at: 0, cancel_at: 0, payment: undefined };
}

export const BaseOrder: MessageFns<BaseOrder> = {
  fromJSON(object: any): BaseOrder {
    return {
      order_id: isSet(object.order_id) ? globalThis.String(object.order_id) : '',
      status: isSet(object.status) ? orderStatusFromJSON(object.status) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      pay_at: isSet(object.pay_at) ? globalThis.Number(object.pay_at) : 0,
      cancel_at: isSet(object.cancel_at) ? globalThis.Number(object.cancel_at) : 0,
      payment: isSet(object.payment) ? Payment.fromJSON(object.payment) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BaseOrder>, I>>(base?: I): BaseOrder {
    return BaseOrder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BaseOrder>, I>>(object: I): BaseOrder {
    const message = createBaseBaseOrder();
    message.order_id = object.order_id ?? '';
    message.status = object.status ?? 0;
    message.create_at = object.create_at ?? 0;
    message.pay_at = object.pay_at ?? 0;
    message.cancel_at = object.cancel_at ?? 0;
    message.payment =
      object.payment !== undefined && object.payment !== null ? Payment.fromPartial(object.payment) : undefined;
    return message;
  }
};

function createBaseApplePayReceipt(): ApplePayReceipt {
  return { receipt: '', purchase_id: '', environment: '', original_transaction_id: '' };
}

export const ApplePayReceipt: MessageFns<ApplePayReceipt> = {
  fromJSON(object: any): ApplePayReceipt {
    return {
      receipt: isSet(object.receipt) ? globalThis.String(object.receipt) : '',
      purchase_id: isSet(object.purchase_id) ? globalThis.String(object.purchase_id) : '',
      environment: isSet(object.environment) ? globalThis.String(object.environment) : '',
      original_transaction_id: isSet(object.original_transaction_id)
        ? globalThis.String(object.original_transaction_id)
        : ''
    };
  },

  create<I extends Exact<DeepPartial<ApplePayReceipt>, I>>(base?: I): ApplePayReceipt {
    return ApplePayReceipt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApplePayReceipt>, I>>(object: I): ApplePayReceipt {
    const message = createBaseApplePayReceipt();
    message.receipt = object.receipt ?? '';
    message.purchase_id = object.purchase_id ?? '';
    message.environment = object.environment ?? '';
    message.original_transaction_id = object.original_transaction_id ?? '';
    return message;
  }
};

function createBaseGooglePlayReceipt(): GooglePlayReceipt {
  return { packageName: '', productId: '', purchaseToken: '', sku_type: '' };
}

export const GooglePlayReceipt: MessageFns<GooglePlayReceipt> = {
  fromJSON(object: any): GooglePlayReceipt {
    return {
      packageName: isSet(object.packageName) ? globalThis.String(object.packageName) : '',
      productId: isSet(object.productId) ? globalThis.String(object.productId) : '',
      purchaseToken: isSet(object.purchaseToken) ? globalThis.String(object.purchaseToken) : '',
      sku_type: isSet(object.sku_type) ? globalThis.String(object.sku_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<GooglePlayReceipt>, I>>(base?: I): GooglePlayReceipt {
    return GooglePlayReceipt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GooglePlayReceipt>, I>>(object: I): GooglePlayReceipt {
    const message = createBaseGooglePlayReceipt();
    message.packageName = object.packageName ?? '';
    message.productId = object.productId ?? '';
    message.purchaseToken = object.purchaseToken ?? '';
    message.sku_type = object.sku_type ?? '';
    return message;
  }
};

function createBasePaymentReceipt(): PaymentReceipt {
  return { apple_receipt: undefined, google_receipt: undefined };
}

export const PaymentReceipt: MessageFns<PaymentReceipt> = {
  fromJSON(object: any): PaymentReceipt {
    return {
      apple_receipt: isSet(object.apple_receipt) ? ApplePayReceipt.fromJSON(object.apple_receipt) : undefined,
      google_receipt: isSet(object.google_receipt) ? GooglePlayReceipt.fromJSON(object.google_receipt) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PaymentReceipt>, I>>(base?: I): PaymentReceipt {
    return PaymentReceipt.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PaymentReceipt>, I>>(object: I): PaymentReceipt {
    const message = createBasePaymentReceipt();
    message.apple_receipt =
      object.apple_receipt !== undefined && object.apple_receipt !== null
        ? ApplePayReceipt.fromPartial(object.apple_receipt)
        : undefined;
    message.google_receipt =
      object.google_receipt !== undefined && object.google_receipt !== null
        ? GooglePlayReceipt.fromPartial(object.google_receipt)
        : undefined;
    return message;
  }
};

function createBaseStringList(): StringList {
  return { values: [] };
}

export const StringList: MessageFns<StringList> = {
  fromJSON(object: any): StringList {
    return {
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<StringList>, I>>(base?: I): StringList {
    return StringList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringList>, I>>(object: I): StringList {
    const message = createBaseStringList();
    message.values = object.values?.map(e => e) || [];
    return message;
  }
};

function createBaseI18NString(): I18NString {
  return { default: '', i18n: {} };
}

export const I18NString: MessageFns<I18NString> = {
  fromJSON(object: any): I18NString {
    return {
      default: isSet(object.default) ? globalThis.String(object.default) : '',
      i18n: isObject(object.i18n)
        ? Object.entries(object.i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<I18NString>, I>>(base?: I): I18NString {
    return I18NString.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<I18NString>, I>>(object: I): I18NString {
    const message = createBaseI18NString();
    message.default = object.default ?? '';
    message.i18n = Object.entries(object.i18n ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseI18NString_I18nEntry(): I18NString_I18nEntry {
  return { key: '', value: '' };
}

export const I18NString_I18nEntry: MessageFns<I18NString_I18nEntry> = {
  fromJSON(object: any): I18NString_I18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<I18NString_I18nEntry>, I>>(base?: I): I18NString_I18nEntry {
    return I18NString_I18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<I18NString_I18nEntry>, I>>(object: I): I18NString_I18nEntry {
    const message = createBaseI18NString_I18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
