// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/pay/subscription.proto

/* eslint-disable */
import {
  ApplePayReceipt,
  BaseOrder,
  BizData,
  GooglePlayReceipt,
  I18NString,
  Jump,
  Payment,
  PaymentType,
  paymentTypeFromJSON,
  Period,
  Price
} from './common';

export const protobufPackage = 'pbsubscription';

/** 订阅状态 */
export enum SubscriptionPlanStatus {
  SUBSCRIPTION_PLAN_STATUS_NONE = 0,
  /** SUBSCRIPTION_PLAN_STATUS_ACTIVE - 生效中 */
  SUBSCRIPTION_PLAN_STATUS_ACTIVE = 1,
  /** SUBSCRIPTION_PLAN_STATUS_PAUSED - 暂停（暂停使用，可随时恢复） */
  SUBSCRIPTION_PLAN_STATUS_PAUSED = 2,
  /** SUBSCRIPTION_PLAN_STATUS_CANCELED - 停止（用户主动立即停止） */
  SUBSCRIPTION_PLAN_STATUS_CANCELED = 3,
  /** SUBSCRIPTION_PLAN_STATUS_EXPIRED - 已过期（未续费等情况） */
  SUBSCRIPTION_PLAN_STATUS_EXPIRED = 4,
  UNRECOGNIZED = -1
}

export function subscriptionPlanStatusFromJSON(object: any): SubscriptionPlanStatus {
  switch (object) {
    case 0:
    case 'SUBSCRIPTION_PLAN_STATUS_NONE':
      return SubscriptionPlanStatus.SUBSCRIPTION_PLAN_STATUS_NONE;
    case 1:
    case 'SUBSCRIPTION_PLAN_STATUS_ACTIVE':
      return SubscriptionPlanStatus.SUBSCRIPTION_PLAN_STATUS_ACTIVE;
    case 2:
    case 'SUBSCRIPTION_PLAN_STATUS_PAUSED':
      return SubscriptionPlanStatus.SUBSCRIPTION_PLAN_STATUS_PAUSED;
    case 3:
    case 'SUBSCRIPTION_PLAN_STATUS_CANCELED':
      return SubscriptionPlanStatus.SUBSCRIPTION_PLAN_STATUS_CANCELED;
    case 4:
    case 'SUBSCRIPTION_PLAN_STATUS_EXPIRED':
      return SubscriptionPlanStatus.SUBSCRIPTION_PLAN_STATUS_EXPIRED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SubscriptionPlanStatus.UNRECOGNIZED;
  }
}

export interface ListSubscriptionReq {
  /** 控制只返回该订阅组的列表 */
  group: string;
  /** 控制只返回包含指定支付方式的订阅项，空则返回所有 */
  payment_types: PaymentType[];
  /** 过滤参数，例如scene=vip表示查询vip入口的订阅项列表 */
  filter_params: { [key: string]: string };
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface ListSubscriptionReq_FilterParamsEntry {
  key: string;
  value: string;
}

export interface ListSubscriptionReq_ExtEntry {
  key: string;
  value: string;
}

export interface ListSubscriptionRsp {
  /** 订阅项列表 */
  subscriptions: Subscription[];
  /** 业务代号，支付sdk打点用 */
  mid: string;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface ListSubscriptionRsp_ExtEntry {
  key: string;
  value: string;
}

export interface SubscribeReq {
  /** 订阅项ID */
  subscription_id: string;
  /** 支付方式代码 */
  payment_code: string;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface SubscribeReq_ExtEntry {
  key: string;
  value: string;
}

export interface SubscribeRsp {
  /** 订阅订单 */
  order: SubscriptionOrder | undefined;
  /** 跳转 */
  jump: Jump | undefined;
  /** 是否是有效期内恢复订阅，true时不会创建订单 */
  resubscribe: boolean;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface SubscribeRsp_ExtEntry {
  key: string;
  value: string;
}

export interface UnsubscribeReq {
  /** 订阅项ID */
  subscription_id: string;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface UnsubscribeReq_ExtEntry {
  key: string;
  value: string;
}

export interface UnsubscribeRsp {
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface UnsubscribeRsp_ExtEntry {
  key: string;
  value: string;
}

export interface PayDoneReq {
  /** 订单ID */
  order_id: string;
  apple_receipt?: ApplePayReceipt | undefined;
  google_receipt?: GooglePlayReceipt | undefined;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface PayDoneReq_ExtEntry {
  key: string;
  value: string;
}

export interface PayDoneRsp {
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface PayDoneRsp_ExtEntry {
  key: string;
  value: string;
}

export interface PayCancelReq {
  /** 订单ID */
  order_id: string;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface PayCancelReq_ExtEntry {
  key: string;
  value: string;
}

export interface PayCancelRsp {
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface PayCancelRsp_ExtEntry {
  key: string;
  value: string;
}

export interface GetSubscriptionPlanReq {
  /** 订阅组 */
  group: string;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface GetSubscriptionPlanReq_ExtEntry {
  key: string;
  value: string;
}

export interface GetSubscriptionPlanRsp {
  /** 订阅计划 */
  plan: SubscriptionPlan | undefined;
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface GetSubscriptionPlanRsp_ExtEntry {
  key: string;
  value: string;
}

export interface ListSubscriptionPlanReq {
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface ListSubscriptionPlanReq_ExtEntry {
  key: string;
  value: string;
}

export interface ListSubscriptionPlanRsp {
  /** 订阅列表 */
  plans: SubscriptionPlan[];
  /** 业务扩展数据 */
  ext: { [key: string]: string };
}

export interface ListSubscriptionPlanRsp_ExtEntry {
  key: string;
  value: string;
}

/** 订阅项 */
export interface Subscription {
  /** 订阅项ID */
  subscription_id: string;
  /** 所在订阅组 */
  group: string;
  /** 周期 */
  period: Period | undefined;
  /** 价格 */
  price: Price | undefined;
  /** 图片URL */
  image: string;
  /** 名称 */
  name_i18n: I18NString | undefined;
  /** 描述 */
  desc_i18n: I18NString | undefined;
  /** 支付方式 */
  payments: Payment[];
  /** 业务扩展信息 */
  biz_extra: BizData | undefined;
}

/** 用户订阅情况 */
export interface SubscriptionPlan {
  /** 订阅组 */
  group: string;
  /** 当前周期的订阅项ID */
  subscription_id: string;
  /** 是否开启自动续订，如果关闭则下个周期不会扣费 */
  auto_renew: boolean;
  /** 状态 */
  status: SubscriptionPlanStatus;
  /** 首次成功订阅的时间，单位秒 */
  first_subscribed_at: number;
  /** 重新订阅（打开自动续订）时间，单位秒 */
  resubscribed_at: number;
  /** 取消订阅（关闭自动续订）时间，单位秒 */
  unsubscribed_at: number;
  /** 升降级时间，单位秒 */
  migrated_at: number;
  /** 当前周期开始时间，单位秒 */
  current_period_start: number;
  /** 下个周期开始时间，单位秒，auto_renew=false时可以视为到期时间 */
  next_period_start: number;
  /** 关联最近一笔订单 */
  order: SubscriptionOrder | undefined;
}

/** 订阅订单 */
export interface SubscriptionOrder {
  /** 必定有的订单基础信息 */
  base_order: BaseOrder | undefined;
}

function createBaseListSubscriptionReq(): ListSubscriptionReq {
  return { group: '', payment_types: [], filter_params: {}, ext: {} };
}

export const ListSubscriptionReq: MessageFns<ListSubscriptionReq> = {
  fromJSON(object: any): ListSubscriptionReq {
    return {
      group: isSet(object.group) ? globalThis.String(object.group) : '',
      payment_types: globalThis.Array.isArray(object?.payment_types)
        ? object.payment_types.map((e: any) => paymentTypeFromJSON(e))
        : [],
      filter_params: isObject(object.filter_params)
        ? Object.entries(object.filter_params).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionReq>, I>>(base?: I): ListSubscriptionReq {
    return ListSubscriptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionReq>, I>>(object: I): ListSubscriptionReq {
    const message = createBaseListSubscriptionReq();
    message.group = object.group ?? '';
    message.payment_types = object.payment_types?.map(e => e) || [];
    message.filter_params = Object.entries(object.filter_params ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSubscriptionReq_FilterParamsEntry(): ListSubscriptionReq_FilterParamsEntry {
  return { key: '', value: '' };
}

export const ListSubscriptionReq_FilterParamsEntry: MessageFns<ListSubscriptionReq_FilterParamsEntry> = {
  fromJSON(object: any): ListSubscriptionReq_FilterParamsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionReq_FilterParamsEntry>, I>>(
    base?: I
  ): ListSubscriptionReq_FilterParamsEntry {
    return ListSubscriptionReq_FilterParamsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionReq_FilterParamsEntry>, I>>(
    object: I
  ): ListSubscriptionReq_FilterParamsEntry {
    const message = createBaseListSubscriptionReq_FilterParamsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListSubscriptionReq_ExtEntry(): ListSubscriptionReq_ExtEntry {
  return { key: '', value: '' };
}

export const ListSubscriptionReq_ExtEntry: MessageFns<ListSubscriptionReq_ExtEntry> = {
  fromJSON(object: any): ListSubscriptionReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionReq_ExtEntry>, I>>(base?: I): ListSubscriptionReq_ExtEntry {
    return ListSubscriptionReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionReq_ExtEntry>, I>>(object: I): ListSubscriptionReq_ExtEntry {
    const message = createBaseListSubscriptionReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListSubscriptionRsp(): ListSubscriptionRsp {
  return { subscriptions: [], mid: '', ext: {} };
}

export const ListSubscriptionRsp: MessageFns<ListSubscriptionRsp> = {
  fromJSON(object: any): ListSubscriptionRsp {
    return {
      subscriptions: globalThis.Array.isArray(object?.subscriptions)
        ? object.subscriptions.map((e: any) => Subscription.fromJSON(e))
        : [],
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionRsp>, I>>(base?: I): ListSubscriptionRsp {
    return ListSubscriptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionRsp>, I>>(object: I): ListSubscriptionRsp {
    const message = createBaseListSubscriptionRsp();
    message.subscriptions = object.subscriptions?.map(e => Subscription.fromPartial(e)) || [];
    message.mid = object.mid ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSubscriptionRsp_ExtEntry(): ListSubscriptionRsp_ExtEntry {
  return { key: '', value: '' };
}

export const ListSubscriptionRsp_ExtEntry: MessageFns<ListSubscriptionRsp_ExtEntry> = {
  fromJSON(object: any): ListSubscriptionRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionRsp_ExtEntry>, I>>(base?: I): ListSubscriptionRsp_ExtEntry {
    return ListSubscriptionRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionRsp_ExtEntry>, I>>(object: I): ListSubscriptionRsp_ExtEntry {
    const message = createBaseListSubscriptionRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSubscribeReq(): SubscribeReq {
  return { subscription_id: '', payment_code: '', ext: {} };
}

export const SubscribeReq: MessageFns<SubscribeReq> = {
  fromJSON(object: any): SubscribeReq {
    return {
      subscription_id: isSet(object.subscription_id) ? globalThis.String(object.subscription_id) : '',
      payment_code: isSet(object.payment_code) ? globalThis.String(object.payment_code) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<SubscribeReq>, I>>(base?: I): SubscribeReq {
    return SubscribeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeReq>, I>>(object: I): SubscribeReq {
    const message = createBaseSubscribeReq();
    message.subscription_id = object.subscription_id ?? '';
    message.payment_code = object.payment_code ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseSubscribeReq_ExtEntry(): SubscribeReq_ExtEntry {
  return { key: '', value: '' };
}

export const SubscribeReq_ExtEntry: MessageFns<SubscribeReq_ExtEntry> = {
  fromJSON(object: any): SubscribeReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SubscribeReq_ExtEntry>, I>>(base?: I): SubscribeReq_ExtEntry {
    return SubscribeReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeReq_ExtEntry>, I>>(object: I): SubscribeReq_ExtEntry {
    const message = createBaseSubscribeReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSubscribeRsp(): SubscribeRsp {
  return { order: undefined, jump: undefined, resubscribe: false, ext: {} };
}

export const SubscribeRsp: MessageFns<SubscribeRsp> = {
  fromJSON(object: any): SubscribeRsp {
    return {
      order: isSet(object.order) ? SubscriptionOrder.fromJSON(object.order) : undefined,
      jump: isSet(object.jump) ? Jump.fromJSON(object.jump) : undefined,
      resubscribe: isSet(object.resubscribe) ? globalThis.Boolean(object.resubscribe) : false,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<SubscribeRsp>, I>>(base?: I): SubscribeRsp {
    return SubscribeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRsp>, I>>(object: I): SubscribeRsp {
    const message = createBaseSubscribeRsp();
    message.order =
      object.order !== undefined && object.order !== null ? SubscriptionOrder.fromPartial(object.order) : undefined;
    message.jump = object.jump !== undefined && object.jump !== null ? Jump.fromPartial(object.jump) : undefined;
    message.resubscribe = object.resubscribe ?? false;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseSubscribeRsp_ExtEntry(): SubscribeRsp_ExtEntry {
  return { key: '', value: '' };
}

export const SubscribeRsp_ExtEntry: MessageFns<SubscribeRsp_ExtEntry> = {
  fromJSON(object: any): SubscribeRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SubscribeRsp_ExtEntry>, I>>(base?: I): SubscribeRsp_ExtEntry {
    return SubscribeRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscribeRsp_ExtEntry>, I>>(object: I): SubscribeRsp_ExtEntry {
    const message = createBaseSubscribeRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUnsubscribeReq(): UnsubscribeReq {
  return { subscription_id: '', ext: {} };
}

export const UnsubscribeReq: MessageFns<UnsubscribeReq> = {
  fromJSON(object: any): UnsubscribeReq {
    return {
      subscription_id: isSet(object.subscription_id) ? globalThis.String(object.subscription_id) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<UnsubscribeReq>, I>>(base?: I): UnsubscribeReq {
    return UnsubscribeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnsubscribeReq>, I>>(object: I): UnsubscribeReq {
    const message = createBaseUnsubscribeReq();
    message.subscription_id = object.subscription_id ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseUnsubscribeReq_ExtEntry(): UnsubscribeReq_ExtEntry {
  return { key: '', value: '' };
}

export const UnsubscribeReq_ExtEntry: MessageFns<UnsubscribeReq_ExtEntry> = {
  fromJSON(object: any): UnsubscribeReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UnsubscribeReq_ExtEntry>, I>>(base?: I): UnsubscribeReq_ExtEntry {
    return UnsubscribeReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnsubscribeReq_ExtEntry>, I>>(object: I): UnsubscribeReq_ExtEntry {
    const message = createBaseUnsubscribeReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUnsubscribeRsp(): UnsubscribeRsp {
  return { ext: {} };
}

export const UnsubscribeRsp: MessageFns<UnsubscribeRsp> = {
  fromJSON(object: any): UnsubscribeRsp {
    return {
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<UnsubscribeRsp>, I>>(base?: I): UnsubscribeRsp {
    return UnsubscribeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnsubscribeRsp>, I>>(object: I): UnsubscribeRsp {
    const message = createBaseUnsubscribeRsp();
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseUnsubscribeRsp_ExtEntry(): UnsubscribeRsp_ExtEntry {
  return { key: '', value: '' };
}

export const UnsubscribeRsp_ExtEntry: MessageFns<UnsubscribeRsp_ExtEntry> = {
  fromJSON(object: any): UnsubscribeRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UnsubscribeRsp_ExtEntry>, I>>(base?: I): UnsubscribeRsp_ExtEntry {
    return UnsubscribeRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnsubscribeRsp_ExtEntry>, I>>(object: I): UnsubscribeRsp_ExtEntry {
    const message = createBaseUnsubscribeRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePayDoneReq(): PayDoneReq {
  return { order_id: '', apple_receipt: undefined, google_receipt: undefined, ext: {} };
}

export const PayDoneReq: MessageFns<PayDoneReq> = {
  fromJSON(object: any): PayDoneReq {
    return {
      order_id: isSet(object.order_id) ? globalThis.String(object.order_id) : '',
      apple_receipt: isSet(object.apple_receipt) ? ApplePayReceipt.fromJSON(object.apple_receipt) : undefined,
      google_receipt: isSet(object.google_receipt) ? GooglePlayReceipt.fromJSON(object.google_receipt) : undefined,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<PayDoneReq>, I>>(base?: I): PayDoneReq {
    return PayDoneReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayDoneReq>, I>>(object: I): PayDoneReq {
    const message = createBasePayDoneReq();
    message.order_id = object.order_id ?? '';
    message.apple_receipt =
      object.apple_receipt !== undefined && object.apple_receipt !== null
        ? ApplePayReceipt.fromPartial(object.apple_receipt)
        : undefined;
    message.google_receipt =
      object.google_receipt !== undefined && object.google_receipt !== null
        ? GooglePlayReceipt.fromPartial(object.google_receipt)
        : undefined;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBasePayDoneReq_ExtEntry(): PayDoneReq_ExtEntry {
  return { key: '', value: '' };
}

export const PayDoneReq_ExtEntry: MessageFns<PayDoneReq_ExtEntry> = {
  fromJSON(object: any): PayDoneReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayDoneReq_ExtEntry>, I>>(base?: I): PayDoneReq_ExtEntry {
    return PayDoneReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayDoneReq_ExtEntry>, I>>(object: I): PayDoneReq_ExtEntry {
    const message = createBasePayDoneReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePayDoneRsp(): PayDoneRsp {
  return { ext: {} };
}

export const PayDoneRsp: MessageFns<PayDoneRsp> = {
  fromJSON(object: any): PayDoneRsp {
    return {
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<PayDoneRsp>, I>>(base?: I): PayDoneRsp {
    return PayDoneRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayDoneRsp>, I>>(object: I): PayDoneRsp {
    const message = createBasePayDoneRsp();
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBasePayDoneRsp_ExtEntry(): PayDoneRsp_ExtEntry {
  return { key: '', value: '' };
}

export const PayDoneRsp_ExtEntry: MessageFns<PayDoneRsp_ExtEntry> = {
  fromJSON(object: any): PayDoneRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayDoneRsp_ExtEntry>, I>>(base?: I): PayDoneRsp_ExtEntry {
    return PayDoneRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayDoneRsp_ExtEntry>, I>>(object: I): PayDoneRsp_ExtEntry {
    const message = createBasePayDoneRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePayCancelReq(): PayCancelReq {
  return { order_id: '', ext: {} };
}

export const PayCancelReq: MessageFns<PayCancelReq> = {
  fromJSON(object: any): PayCancelReq {
    return {
      order_id: isSet(object.order_id) ? globalThis.String(object.order_id) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<PayCancelReq>, I>>(base?: I): PayCancelReq {
    return PayCancelReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelReq>, I>>(object: I): PayCancelReq {
    const message = createBasePayCancelReq();
    message.order_id = object.order_id ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBasePayCancelReq_ExtEntry(): PayCancelReq_ExtEntry {
  return { key: '', value: '' };
}

export const PayCancelReq_ExtEntry: MessageFns<PayCancelReq_ExtEntry> = {
  fromJSON(object: any): PayCancelReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayCancelReq_ExtEntry>, I>>(base?: I): PayCancelReq_ExtEntry {
    return PayCancelReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelReq_ExtEntry>, I>>(object: I): PayCancelReq_ExtEntry {
    const message = createBasePayCancelReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePayCancelRsp(): PayCancelRsp {
  return { ext: {} };
}

export const PayCancelRsp: MessageFns<PayCancelRsp> = {
  fromJSON(object: any): PayCancelRsp {
    return {
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<PayCancelRsp>, I>>(base?: I): PayCancelRsp {
    return PayCancelRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelRsp>, I>>(object: I): PayCancelRsp {
    const message = createBasePayCancelRsp();
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBasePayCancelRsp_ExtEntry(): PayCancelRsp_ExtEntry {
  return { key: '', value: '' };
}

export const PayCancelRsp_ExtEntry: MessageFns<PayCancelRsp_ExtEntry> = {
  fromJSON(object: any): PayCancelRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayCancelRsp_ExtEntry>, I>>(base?: I): PayCancelRsp_ExtEntry {
    return PayCancelRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayCancelRsp_ExtEntry>, I>>(object: I): PayCancelRsp_ExtEntry {
    const message = createBasePayCancelRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetSubscriptionPlanReq(): GetSubscriptionPlanReq {
  return { group: '', ext: {} };
}

export const GetSubscriptionPlanReq: MessageFns<GetSubscriptionPlanReq> = {
  fromJSON(object: any): GetSubscriptionPlanReq {
    return {
      group: isSet(object.group) ? globalThis.String(object.group) : '',
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetSubscriptionPlanReq>, I>>(base?: I): GetSubscriptionPlanReq {
    return GetSubscriptionPlanReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubscriptionPlanReq>, I>>(object: I): GetSubscriptionPlanReq {
    const message = createBaseGetSubscriptionPlanReq();
    message.group = object.group ?? '';
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGetSubscriptionPlanReq_ExtEntry(): GetSubscriptionPlanReq_ExtEntry {
  return { key: '', value: '' };
}

export const GetSubscriptionPlanReq_ExtEntry: MessageFns<GetSubscriptionPlanReq_ExtEntry> = {
  fromJSON(object: any): GetSubscriptionPlanReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetSubscriptionPlanReq_ExtEntry>, I>>(base?: I): GetSubscriptionPlanReq_ExtEntry {
    return GetSubscriptionPlanReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubscriptionPlanReq_ExtEntry>, I>>(
    object: I
  ): GetSubscriptionPlanReq_ExtEntry {
    const message = createBaseGetSubscriptionPlanReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetSubscriptionPlanRsp(): GetSubscriptionPlanRsp {
  return { plan: undefined, ext: {} };
}

export const GetSubscriptionPlanRsp: MessageFns<GetSubscriptionPlanRsp> = {
  fromJSON(object: any): GetSubscriptionPlanRsp {
    return {
      plan: isSet(object.plan) ? SubscriptionPlan.fromJSON(object.plan) : undefined,
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetSubscriptionPlanRsp>, I>>(base?: I): GetSubscriptionPlanRsp {
    return GetSubscriptionPlanRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubscriptionPlanRsp>, I>>(object: I): GetSubscriptionPlanRsp {
    const message = createBaseGetSubscriptionPlanRsp();
    message.plan =
      object.plan !== undefined && object.plan !== null ? SubscriptionPlan.fromPartial(object.plan) : undefined;
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGetSubscriptionPlanRsp_ExtEntry(): GetSubscriptionPlanRsp_ExtEntry {
  return { key: '', value: '' };
}

export const GetSubscriptionPlanRsp_ExtEntry: MessageFns<GetSubscriptionPlanRsp_ExtEntry> = {
  fromJSON(object: any): GetSubscriptionPlanRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetSubscriptionPlanRsp_ExtEntry>, I>>(base?: I): GetSubscriptionPlanRsp_ExtEntry {
    return GetSubscriptionPlanRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSubscriptionPlanRsp_ExtEntry>, I>>(
    object: I
  ): GetSubscriptionPlanRsp_ExtEntry {
    const message = createBaseGetSubscriptionPlanRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListSubscriptionPlanReq(): ListSubscriptionPlanReq {
  return { ext: {} };
}

export const ListSubscriptionPlanReq: MessageFns<ListSubscriptionPlanReq> = {
  fromJSON(object: any): ListSubscriptionPlanReq {
    return {
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionPlanReq>, I>>(base?: I): ListSubscriptionPlanReq {
    return ListSubscriptionPlanReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionPlanReq>, I>>(object: I): ListSubscriptionPlanReq {
    const message = createBaseListSubscriptionPlanReq();
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSubscriptionPlanReq_ExtEntry(): ListSubscriptionPlanReq_ExtEntry {
  return { key: '', value: '' };
}

export const ListSubscriptionPlanReq_ExtEntry: MessageFns<ListSubscriptionPlanReq_ExtEntry> = {
  fromJSON(object: any): ListSubscriptionPlanReq_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionPlanReq_ExtEntry>, I>>(
    base?: I
  ): ListSubscriptionPlanReq_ExtEntry {
    return ListSubscriptionPlanReq_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionPlanReq_ExtEntry>, I>>(
    object: I
  ): ListSubscriptionPlanReq_ExtEntry {
    const message = createBaseListSubscriptionPlanReq_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListSubscriptionPlanRsp(): ListSubscriptionPlanRsp {
  return { plans: [], ext: {} };
}

export const ListSubscriptionPlanRsp: MessageFns<ListSubscriptionPlanRsp> = {
  fromJSON(object: any): ListSubscriptionPlanRsp {
    return {
      plans: globalThis.Array.isArray(object?.plans) ? object.plans.map((e: any) => SubscriptionPlan.fromJSON(e)) : [],
      ext: isObject(object.ext)
        ? Object.entries(object.ext).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionPlanRsp>, I>>(base?: I): ListSubscriptionPlanRsp {
    return ListSubscriptionPlanRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionPlanRsp>, I>>(object: I): ListSubscriptionPlanRsp {
    const message = createBaseListSubscriptionPlanRsp();
    message.plans = object.plans?.map(e => SubscriptionPlan.fromPartial(e)) || [];
    message.ext = Object.entries(object.ext ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseListSubscriptionPlanRsp_ExtEntry(): ListSubscriptionPlanRsp_ExtEntry {
  return { key: '', value: '' };
}

export const ListSubscriptionPlanRsp_ExtEntry: MessageFns<ListSubscriptionPlanRsp_ExtEntry> = {
  fromJSON(object: any): ListSubscriptionPlanRsp_ExtEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSubscriptionPlanRsp_ExtEntry>, I>>(
    base?: I
  ): ListSubscriptionPlanRsp_ExtEntry {
    return ListSubscriptionPlanRsp_ExtEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSubscriptionPlanRsp_ExtEntry>, I>>(
    object: I
  ): ListSubscriptionPlanRsp_ExtEntry {
    const message = createBaseListSubscriptionPlanRsp_ExtEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSubscription(): Subscription {
  return {
    subscription_id: '',
    group: '',
    period: undefined,
    price: undefined,
    image: '',
    name_i18n: undefined,
    desc_i18n: undefined,
    payments: [],
    biz_extra: undefined
  };
}

export const Subscription: MessageFns<Subscription> = {
  fromJSON(object: any): Subscription {
    return {
      subscription_id: isSet(object.subscription_id) ? globalThis.String(object.subscription_id) : '',
      group: isSet(object.group) ? globalThis.String(object.group) : '',
      period: isSet(object.period) ? Period.fromJSON(object.period) : undefined,
      price: isSet(object.price) ? Price.fromJSON(object.price) : undefined,
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      name_i18n: isSet(object.name_i18n) ? I18NString.fromJSON(object.name_i18n) : undefined,
      desc_i18n: isSet(object.desc_i18n) ? I18NString.fromJSON(object.desc_i18n) : undefined,
      payments: globalThis.Array.isArray(object?.payments) ? object.payments.map((e: any) => Payment.fromJSON(e)) : [],
      biz_extra: isSet(object.biz_extra) ? BizData.fromJSON(object.biz_extra) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Subscription>, I>>(base?: I): Subscription {
    return Subscription.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Subscription>, I>>(object: I): Subscription {
    const message = createBaseSubscription();
    message.subscription_id = object.subscription_id ?? '';
    message.group = object.group ?? '';
    message.period =
      object.period !== undefined && object.period !== null ? Period.fromPartial(object.period) : undefined;
    message.price = object.price !== undefined && object.price !== null ? Price.fromPartial(object.price) : undefined;
    message.image = object.image ?? '';
    message.name_i18n =
      object.name_i18n !== undefined && object.name_i18n !== null
        ? I18NString.fromPartial(object.name_i18n)
        : undefined;
    message.desc_i18n =
      object.desc_i18n !== undefined && object.desc_i18n !== null
        ? I18NString.fromPartial(object.desc_i18n)
        : undefined;
    message.payments = object.payments?.map(e => Payment.fromPartial(e)) || [];
    message.biz_extra =
      object.biz_extra !== undefined && object.biz_extra !== null ? BizData.fromPartial(object.biz_extra) : undefined;
    return message;
  }
};

function createBaseSubscriptionPlan(): SubscriptionPlan {
  return {
    group: '',
    subscription_id: '',
    auto_renew: false,
    status: 0,
    first_subscribed_at: 0,
    resubscribed_at: 0,
    unsubscribed_at: 0,
    migrated_at: 0,
    current_period_start: 0,
    next_period_start: 0,
    order: undefined
  };
}

export const SubscriptionPlan: MessageFns<SubscriptionPlan> = {
  fromJSON(object: any): SubscriptionPlan {
    return {
      group: isSet(object.group) ? globalThis.String(object.group) : '',
      subscription_id: isSet(object.subscription_id) ? globalThis.String(object.subscription_id) : '',
      auto_renew: isSet(object.auto_renew) ? globalThis.Boolean(object.auto_renew) : false,
      status: isSet(object.status) ? subscriptionPlanStatusFromJSON(object.status) : 0,
      first_subscribed_at: isSet(object.first_subscribed_at) ? globalThis.Number(object.first_subscribed_at) : 0,
      resubscribed_at: isSet(object.resubscribed_at) ? globalThis.Number(object.resubscribed_at) : 0,
      unsubscribed_at: isSet(object.unsubscribed_at) ? globalThis.Number(object.unsubscribed_at) : 0,
      migrated_at: isSet(object.migrated_at) ? globalThis.Number(object.migrated_at) : 0,
      current_period_start: isSet(object.current_period_start) ? globalThis.Number(object.current_period_start) : 0,
      next_period_start: isSet(object.next_period_start) ? globalThis.Number(object.next_period_start) : 0,
      order: isSet(object.order) ? SubscriptionOrder.fromJSON(object.order) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SubscriptionPlan>, I>>(base?: I): SubscriptionPlan {
    return SubscriptionPlan.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscriptionPlan>, I>>(object: I): SubscriptionPlan {
    const message = createBaseSubscriptionPlan();
    message.group = object.group ?? '';
    message.subscription_id = object.subscription_id ?? '';
    message.auto_renew = object.auto_renew ?? false;
    message.status = object.status ?? 0;
    message.first_subscribed_at = object.first_subscribed_at ?? 0;
    message.resubscribed_at = object.resubscribed_at ?? 0;
    message.unsubscribed_at = object.unsubscribed_at ?? 0;
    message.migrated_at = object.migrated_at ?? 0;
    message.current_period_start = object.current_period_start ?? 0;
    message.next_period_start = object.next_period_start ?? 0;
    message.order =
      object.order !== undefined && object.order !== null ? SubscriptionOrder.fromPartial(object.order) : undefined;
    return message;
  }
};

function createBaseSubscriptionOrder(): SubscriptionOrder {
  return { base_order: undefined };
}

export const SubscriptionOrder: MessageFns<SubscriptionOrder> = {
  fromJSON(object: any): SubscriptionOrder {
    return { base_order: isSet(object.base_order) ? BaseOrder.fromJSON(object.base_order) : undefined };
  },

  create<I extends Exact<DeepPartial<SubscriptionOrder>, I>>(base?: I): SubscriptionOrder {
    return SubscriptionOrder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubscriptionOrder>, I>>(object: I): SubscriptionOrder {
    const message = createBaseSubscriptionOrder();
    message.base_order =
      object.base_order !== undefined && object.base_order !== null
        ? BaseOrder.fromPartial(object.base_order)
        : undefined;
    return message;
  }
};

/** 支付订阅相关API */
export type SubscriptionAPIDefinition = typeof SubscriptionAPIDefinition;
export const SubscriptionAPIDefinition = {
  name: 'SubscriptionAPI',
  fullName: 'pbsubscription.SubscriptionAPI',
  methods: {
    /** 获取订阅项列表 */
    listSubscription: {
      name: 'ListSubscription',
      requestType: ListSubscriptionReq,
      requestStream: false,
      responseType: ListSubscriptionRsp,
      responseStream: false,
      options: {}
    },
    /** 订阅 */
    subscribe: {
      name: 'Subscribe',
      requestType: SubscribeReq,
      requestStream: false,
      responseType: SubscribeRsp,
      responseStream: false,
      options: {}
    },
    /** 取消订阅 */
    unsubscribe: {
      name: 'Unsubscribe',
      requestType: UnsubscribeReq,
      requestStream: false,
      responseType: UnsubscribeRsp,
      responseStream: false,
      options: {}
    },
    /** 支付完成回调 */
    payDone: {
      name: 'PayDone',
      requestType: PayDoneReq,
      requestStream: false,
      responseType: PayDoneRsp,
      responseStream: false,
      options: {}
    },
    /** 支付取消（APP端内关闭支付面板） */
    payCancel: {
      name: 'PayCancel',
      requestType: PayCancelReq,
      requestStream: false,
      responseType: PayCancelRsp,
      responseStream: false,
      options: {}
    },
    /** 指定订阅组，获取用户的订阅计划 */
    getSubscriptionPlan: {
      name: 'GetSubscriptionPlan',
      requestType: GetSubscriptionPlanReq,
      requestStream: false,
      responseType: GetSubscriptionPlanRsp,
      responseStream: false,
      options: {}
    },
    /** 获取用户的订阅计划列表 */
    listSubscriptionPlan: {
      name: 'ListSubscriptionPlan',
      requestType: ListSubscriptionPlanReq,
      requestStream: false,
      responseType: ListSubscriptionPlanRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
