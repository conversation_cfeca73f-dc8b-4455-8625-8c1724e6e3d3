// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/common.proto

/* eslint-disable */

export const protobufPackage = 'pbrevenue';

/** 错误码 */
export enum ErrCode {
  ERR_CODE_NONE = 0,
  /** ERR_CODE_ILLEGAL_PARAMETER - 参数错误 */
  ERR_CODE_ILLEGAL_PARAMETER = 4001,
  /** ERR_CODE_ANM_NOT_EXIST - 参数错误-没有传 APP 字段 */
  ERR_CODE_ANM_NOT_EXIST = 4002,
  /** ERR_CODE_ANM_NOT_SUPPORT - 参数错误-APP字段不合法 */
  ERR_CODE_ANM_NOT_SUPPORT = 4003,
  /** ERR_CODE_SERVER_ERROR - 系统错误 */
  ERR_CODE_SERVER_ERROR = 5001,
  /** ERR_CODE_GOODS_NOT_FOUND - 充值档位不存在 */
  ERR_CODE_GOODS_NOT_FOUND = 10004,
  /** ERR_CODE_GOODS_CREATE_ORDER_FAIL - 创建充值订单失败 */
  ERR_CODE_GOODS_CREATE_ORDER_FAIL = 10005,
  /** ERR_CODE_GOODS_PAY_APP_NOT_SUPPORT - APP 未配置 */
  ERR_CODE_GOODS_PAY_APP_NOT_SUPPORT = 10006,
  /** ERR_CODE_GOODS_PAY_TYPE_NOT_SUPPORT - 支付方式不支持 */
  ERR_CODE_GOODS_PAY_TYPE_NOT_SUPPORT = 10007,
  /** ERR_CODE_GOODS_PAY_ORDER_NOT_FOUND - 充值订单不存在 */
  ERR_CODE_GOODS_PAY_ORDER_NOT_FOUND = 10008,
  /** ERR_CODE_GOODS_MGR_SKU_IS_EMPTY - SKU 为空 */
  ERR_CODE_GOODS_MGR_SKU_IS_EMPTY = 11008,
  /** ERR_CODE_GIFT_BLOCKED - 已将对方拉黑 */
  ERR_CODE_GIFT_BLOCKED = 20001,
  /** ERR_CODE_GIFT_BE_BLOCKED - 已被对方拉黑 */
  ERR_CODE_GIFT_BE_BLOCKED = 20002,
  /** ERR_CODE_GIFT_TO_SELF - 送礼给自己 */
  ERR_CODE_GIFT_TO_SELF = 20003,
  /** ERR_CODE_GIFT_REPEAT_OPERATION - 重复操作 */
  ERR_CODE_GIFT_REPEAT_OPERATION = 20004,
  /** ERR_CODE_GIFT_GIFT_NOT_EXIST - 礼物不存在 */
  ERR_CODE_GIFT_GIFT_NOT_EXIST = 20005,
  /** ERR_CODE_GIFT_RECEIVER_NOT_EXIST - 收礼人不存在 */
  ERR_CODE_GIFT_RECEIVER_NOT_EXIST = 20006,
  /** ERR_CODE_GIFT_UNABLE_GET_USER_INFO - 无法获取用户信息 */
  ERR_CODE_GIFT_UNABLE_GET_USER_INFO = 20007,
  /** ERR_CODE_GIFT_UNABLE_GET_SENDER_INFO - 无法获取送礼用户信息 */
  ERR_CODE_GIFT_UNABLE_GET_SENDER_INFO = 20008,
  /** ERR_CODE_GIFT_REPEATED_CLAIM - 重复领取 */
  ERR_CODE_GIFT_REPEATED_CLAIM = 20009,
  /** ERR_CODE_GIFT_EXPIRED_CLAIM - 领取时间已经过期 */
  ERR_CODE_GIFT_EXPIRED_CLAIM = 20010,
  /** ERR_CODE_BALANCE_NOT_ENOUGH - 账户余额不足(金币) */
  ERR_CODE_BALANCE_NOT_ENOUGH = 30001,
  /** ERR_CODE_BALANCE_CRYSTAL_NOT_ENOUGH - 账户水晶不足 */
  ERR_CODE_BALANCE_CRYSTAL_NOT_ENOUGH = 30002,
  /** ERR_CODE_BALANCE_DIAMOND_NOT_ENOUGH - 账户钻石不足 */
  ERR_CODE_BALANCE_DIAMOND_NOT_ENOUGH = 30003,
  /** ERR_CODE_BALANCE_COIN_NOT_ENOUGH - 账户硬币不足 */
  ERR_CODE_BALANCE_COIN_NOT_ENOUGH = 30004,
  /** ERR_CODE_GOODS_BUY_ONCE - 该商品已经购买过了（只能买一次） */
  ERR_CODE_GOODS_BUY_ONCE = 31001,
  /** ERR_CODE_EXCHANGE_QUANTITY_ERROR - 钻石兑换金币时, 输入数量比最低数量少 */
  ERR_CODE_EXCHANGE_QUANTITY_ERROR = 40001,
  /** ERR_CODE_EXCHANGE_COIN_QUANTITY_ERROR - 经营货币兑换金币时, 输入数量比最低数量少 */
  ERR_CODE_EXCHANGE_COIN_QUANTITY_ERROR = 40002,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ERR_CODE_NONE':
      return ErrCode.ERR_CODE_NONE;
    case 4001:
    case 'ERR_CODE_ILLEGAL_PARAMETER':
      return ErrCode.ERR_CODE_ILLEGAL_PARAMETER;
    case 4002:
    case 'ERR_CODE_ANM_NOT_EXIST':
      return ErrCode.ERR_CODE_ANM_NOT_EXIST;
    case 4003:
    case 'ERR_CODE_ANM_NOT_SUPPORT':
      return ErrCode.ERR_CODE_ANM_NOT_SUPPORT;
    case 5001:
    case 'ERR_CODE_SERVER_ERROR':
      return ErrCode.ERR_CODE_SERVER_ERROR;
    case 10004:
    case 'ERR_CODE_GOODS_NOT_FOUND':
      return ErrCode.ERR_CODE_GOODS_NOT_FOUND;
    case 10005:
    case 'ERR_CODE_GOODS_CREATE_ORDER_FAIL':
      return ErrCode.ERR_CODE_GOODS_CREATE_ORDER_FAIL;
    case 10006:
    case 'ERR_CODE_GOODS_PAY_APP_NOT_SUPPORT':
      return ErrCode.ERR_CODE_GOODS_PAY_APP_NOT_SUPPORT;
    case 10007:
    case 'ERR_CODE_GOODS_PAY_TYPE_NOT_SUPPORT':
      return ErrCode.ERR_CODE_GOODS_PAY_TYPE_NOT_SUPPORT;
    case 10008:
    case 'ERR_CODE_GOODS_PAY_ORDER_NOT_FOUND':
      return ErrCode.ERR_CODE_GOODS_PAY_ORDER_NOT_FOUND;
    case 11008:
    case 'ERR_CODE_GOODS_MGR_SKU_IS_EMPTY':
      return ErrCode.ERR_CODE_GOODS_MGR_SKU_IS_EMPTY;
    case 20001:
    case 'ERR_CODE_GIFT_BLOCKED':
      return ErrCode.ERR_CODE_GIFT_BLOCKED;
    case 20002:
    case 'ERR_CODE_GIFT_BE_BLOCKED':
      return ErrCode.ERR_CODE_GIFT_BE_BLOCKED;
    case 20003:
    case 'ERR_CODE_GIFT_TO_SELF':
      return ErrCode.ERR_CODE_GIFT_TO_SELF;
    case 20004:
    case 'ERR_CODE_GIFT_REPEAT_OPERATION':
      return ErrCode.ERR_CODE_GIFT_REPEAT_OPERATION;
    case 20005:
    case 'ERR_CODE_GIFT_GIFT_NOT_EXIST':
      return ErrCode.ERR_CODE_GIFT_GIFT_NOT_EXIST;
    case 20006:
    case 'ERR_CODE_GIFT_RECEIVER_NOT_EXIST':
      return ErrCode.ERR_CODE_GIFT_RECEIVER_NOT_EXIST;
    case 20007:
    case 'ERR_CODE_GIFT_UNABLE_GET_USER_INFO':
      return ErrCode.ERR_CODE_GIFT_UNABLE_GET_USER_INFO;
    case 20008:
    case 'ERR_CODE_GIFT_UNABLE_GET_SENDER_INFO':
      return ErrCode.ERR_CODE_GIFT_UNABLE_GET_SENDER_INFO;
    case 20009:
    case 'ERR_CODE_GIFT_REPEATED_CLAIM':
      return ErrCode.ERR_CODE_GIFT_REPEATED_CLAIM;
    case 20010:
    case 'ERR_CODE_GIFT_EXPIRED_CLAIM':
      return ErrCode.ERR_CODE_GIFT_EXPIRED_CLAIM;
    case 30001:
    case 'ERR_CODE_BALANCE_NOT_ENOUGH':
      return ErrCode.ERR_CODE_BALANCE_NOT_ENOUGH;
    case 30002:
    case 'ERR_CODE_BALANCE_CRYSTAL_NOT_ENOUGH':
      return ErrCode.ERR_CODE_BALANCE_CRYSTAL_NOT_ENOUGH;
    case 30003:
    case 'ERR_CODE_BALANCE_DIAMOND_NOT_ENOUGH':
      return ErrCode.ERR_CODE_BALANCE_DIAMOND_NOT_ENOUGH;
    case 30004:
    case 'ERR_CODE_BALANCE_COIN_NOT_ENOUGH':
      return ErrCode.ERR_CODE_BALANCE_COIN_NOT_ENOUGH;
    case 31001:
    case 'ERR_CODE_GOODS_BUY_ONCE':
      return ErrCode.ERR_CODE_GOODS_BUY_ONCE;
    case 40001:
    case 'ERR_CODE_EXCHANGE_QUANTITY_ERROR':
      return ErrCode.ERR_CODE_EXCHANGE_QUANTITY_ERROR;
    case 40002:
    case 'ERR_CODE_EXCHANGE_COIN_QUANTITY_ERROR':
      return ErrCode.ERR_CODE_EXCHANGE_COIN_QUANTITY_ERROR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}

/** 所有业务的币种统一放在这一个枚举中 */
export enum CurrencyType {
  CURRENCY_TYPE_NONE = 0,
  /** CURRENCY_TYPE_DOLLAR - 虚拟美金 */
  CURRENCY_TYPE_DOLLAR = 1,
  /** CURRENCY_TYPE_GOLD - 金币 */
  CURRENCY_TYPE_GOLD = 10,
  /** CURRENCY_TYPE_CRYSTAL - 水晶 */
  CURRENCY_TYPE_CRYSTAL = 11,
  /** CURRENCY_TYPE_DIAMOND - 钻石 */
  CURRENCY_TYPE_DIAMOND = 12,
  /** CURRENCY_TYPE_COIN - 硬币，carne经营货币 */
  CURRENCY_TYPE_COIN = 13,
  /** CURRENCY_TYPE_GOLDEN_TICKET - 币商货币-金票 */
  CURRENCY_TYPE_GOLDEN_TICKET = 14,
  /** CURRENCY_TYPE_FREE_GOLD - 免费金币，tomeet蓝钻 */
  CURRENCY_TYPE_FREE_GOLD = 15,
  UNRECOGNIZED = -1
}

export function currencyTypeFromJSON(object: any): CurrencyType {
  switch (object) {
    case 0:
    case 'CURRENCY_TYPE_NONE':
      return CurrencyType.CURRENCY_TYPE_NONE;
    case 1:
    case 'CURRENCY_TYPE_DOLLAR':
      return CurrencyType.CURRENCY_TYPE_DOLLAR;
    case 10:
    case 'CURRENCY_TYPE_GOLD':
      return CurrencyType.CURRENCY_TYPE_GOLD;
    case 11:
    case 'CURRENCY_TYPE_CRYSTAL':
      return CurrencyType.CURRENCY_TYPE_CRYSTAL;
    case 12:
    case 'CURRENCY_TYPE_DIAMOND':
      return CurrencyType.CURRENCY_TYPE_DIAMOND;
    case 13:
    case 'CURRENCY_TYPE_COIN':
      return CurrencyType.CURRENCY_TYPE_COIN;
    case 14:
    case 'CURRENCY_TYPE_GOLDEN_TICKET':
      return CurrencyType.CURRENCY_TYPE_GOLDEN_TICKET;
    case 15:
    case 'CURRENCY_TYPE_FREE_GOLD':
      return CurrencyType.CURRENCY_TYPE_FREE_GOLD;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CurrencyType.UNRECOGNIZED;
  }
}

/** 支付币种单位 */
export enum Unit {
  /** USD - 美金 */
  USD = 0,
  /** UNIT_NONE - 补充一个值来区分为空的情况，新的支付接口是在 pay，会另外单独定义一份，旧的接口在服务端做映射处理 */
  UNIT_NONE = -1,
  /** IDR - 印尼盾 */
  IDR = 10,
  /** AED - 阿联酋 */
  AED = 11,
  /** EGP - 埃及 */
  EGP = 12,
  /** SAR - 沙特阿拉伯 */
  SAR = 13,
  /** KWD - 科威特 */
  KWD = 14,
  /** JOD - 约旦 */
  JOD = 15,
  /** QAR - 卡塔尔 */
  QAR = 16,
  /** BHD - 巴林 */
  BHD = 17,
  /** OMR - 阿曼 */
  OMR = 18,
  /** INR - 印度 */
  INR = 19,
  /** IQD - 伊拉克 */
  IQD = 20,
  /** TRY - 土耳其 */
  TRY = 21,
  /** PKR - 巴基斯坦 */
  PKR = 22,
  /** BDT - 孟加拉 */
  BDT = 23,
  /** BRL - 巴西 */
  BRL = 24,
  /** MXN - 墨西哥 */
  MXN = 25,
  /** CLP - 智利 */
  CLP = 26,
  /** AUD - 澳元 */
  AUD = 27,
  /** CAD - 加元 */
  CAD = 28,
  /** PEN - 秘鲁 */
  PEN = 29,
  /** MYR - 马来西亚 */
  MYR = 30,
  /** NGN - 尼日利亚 */
  NGN = 31
}

export function unitFromJSON(object: any): Unit {
  switch (object) {
    case 0:
    case 'USD':
      return Unit.USD;
    case -1:
    case 'UNIT_NONE':
      return Unit.UNIT_NONE;
    case 10:
    case 'IDR':
      return Unit.IDR;
    case 11:
    case 'AED':
      return Unit.AED;
    case 12:
    case 'EGP':
      return Unit.EGP;
    case 13:
    case 'SAR':
      return Unit.SAR;
    case 14:
    case 'KWD':
      return Unit.KWD;
    case 15:
    case 'JOD':
      return Unit.JOD;
    case 16:
    case 'QAR':
      return Unit.QAR;
    case 17:
    case 'BHD':
      return Unit.BHD;
    case 18:
    case 'OMR':
      return Unit.OMR;
    case 19:
    case 'INR':
      return Unit.INR;
    case 20:
    case 'IQD':
      return Unit.IQD;
    case 21:
    case 'TRY':
      return Unit.TRY;
    case 22:
    case 'PKR':
      return Unit.PKR;
    case 23:
    case 'BDT':
      return Unit.BDT;
    case 24:
    case 'BRL':
      return Unit.BRL;
    case 25:
    case 'MXN':
      return Unit.MXN;
    case 26:
    case 'CLP':
      return Unit.CLP;
    case 27:
    case 'AUD':
      return Unit.AUD;
    case 28:
    case 'CAD':
      return Unit.CAD;
    case 29:
    case 'PEN':
      return Unit.PEN;
    case 30:
    case 'MYR':
      return Unit.MYR;
    case 31:
    case 'NGN':
      return Unit.NGN;
    default:
      return Unit.UNIT_NONE;
  }
}

export enum GoodsType {
  /** CURRENCY - 虚拟币，原来的枚举值没定义好，不应该使用0 */
  CURRENCY = 0,
  /** PACKAGE - 组合包裹（奖励包） */
  PACKAGE = 10,
  /** GoodsType_NONE - 补充一个值来区分为空的情况 */
  GoodsType_NONE = -1,
  /** GoodsType_VIP - vip */
  GoodsType_VIP = 20
}

export function goodsTypeFromJSON(object: any): GoodsType {
  switch (object) {
    case 0:
    case 'CURRENCY':
      return GoodsType.CURRENCY;
    case 10:
    case 'PACKAGE':
      return GoodsType.PACKAGE;
    case -1:
    case 'GoodsType_NONE':
      return GoodsType.GoodsType_NONE;
    case 20:
    case 'GoodsType_VIP':
      return GoodsType.GoodsType_VIP;
    default:
      return GoodsType.GoodsType_NONE;
  }
}

/** LimitType 限制购买类型 */
export enum GoodsLimitType {
  /** GoodsLimitType_NONE - 默认值，无意义 */
  GoodsLimitType_NONE = 0,
  /** GoodsLimitType_ONCE - 只能买一次（首充） */
  GoodsLimitType_ONCE = 10,
  UNRECOGNIZED = -1
}

export function goodsLimitTypeFromJSON(object: any): GoodsLimitType {
  switch (object) {
    case 0:
    case 'GoodsLimitType_NONE':
      return GoodsLimitType.GoodsLimitType_NONE;
    case 10:
    case 'GoodsLimitType_ONCE':
      return GoodsLimitType.GoodsLimitType_ONCE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GoodsLimitType.UNRECOGNIZED;
  }
}

/** 支付状态 */
export enum PayStatus {
  /** NONE - 未支付 */
  NONE = 0,
  /** UNPAID - UNPAID:等待支付 */
  UNPAID = 10,
  /** PAID - PAID:支付成功 */
  PAID = 20,
  /** FAIL - FAIL:支付失败 */
  FAIL = 30,
  /** REFUND - REFUND 已退款 */
  REFUND = 100,
  UNRECOGNIZED = -1
}

export function payStatusFromJSON(object: any): PayStatus {
  switch (object) {
    case 0:
    case 'NONE':
      return PayStatus.NONE;
    case 10:
    case 'UNPAID':
      return PayStatus.UNPAID;
    case 20:
    case 'PAID':
      return PayStatus.PAID;
    case 30:
    case 'FAIL':
      return PayStatus.FAIL;
    case 100:
    case 'REFUND':
      return PayStatus.REFUND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PayStatus.UNRECOGNIZED;
  }
}

/** 退款状态 */
export enum RefundStatus {
  /** REFUND_NONE - 未退款 */
  REFUND_NONE = 0,
  /** REFUND_REFUNDING - 退款中 */
  REFUND_REFUNDING = 10,
  /** REFUND_DONE - 退款成功 */
  REFUND_DONE = 20,
  /** REFUND_FAIL - 退款失败 */
  REFUND_FAIL = 30,
  UNRECOGNIZED = -1
}

export function refundStatusFromJSON(object: any): RefundStatus {
  switch (object) {
    case 0:
    case 'REFUND_NONE':
      return RefundStatus.REFUND_NONE;
    case 10:
    case 'REFUND_REFUNDING':
      return RefundStatus.REFUND_REFUNDING;
    case 20:
    case 'REFUND_DONE':
      return RefundStatus.REFUND_DONE;
    case 30:
    case 'REFUND_FAIL':
      return RefundStatus.REFUND_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RefundStatus.UNRECOGNIZED;
  }
}

export enum BusinessType {
  BUSINESS_TYPE_NONE = 0,
  /** BUSINESS_TYPE_SEND_GIFT - 送礼 */
  BUSINESS_TYPE_SEND_GIFT = 1,
  /** BUSINESS_TYPE_HEADWEAR - 头像 */
  BUSINESS_TYPE_HEADWEAR = 2,
  /** BUSINESS_TYPE_VEHICLE - 座驾 */
  BUSINESS_TYPE_VEHICLE = 3,
  /** BUSINESS_TYPE_SPONSTOR_CARNI - 嘉年华 */
  BUSINESS_TYPE_SPONSTOR_CARNI = 4,
  /** BUSINESS_TYPE_RECEIVE_GIFT - 收礼 */
  BUSINESS_TYPE_RECEIVE_GIFT = 5,
  /** BUSINESS_TYPE_RECEIVE_EXCHANGE - 交换 */
  BUSINESS_TYPE_RECEIVE_EXCHANGE = 7,
  /** BUSINESS_TYPE_TRANSFER - 转账 */
  BUSINESS_TYPE_TRANSFER = 8,
  UNRECOGNIZED = -1
}

export function businessTypeFromJSON(object: any): BusinessType {
  switch (object) {
    case 0:
    case 'BUSINESS_TYPE_NONE':
      return BusinessType.BUSINESS_TYPE_NONE;
    case 1:
    case 'BUSINESS_TYPE_SEND_GIFT':
      return BusinessType.BUSINESS_TYPE_SEND_GIFT;
    case 2:
    case 'BUSINESS_TYPE_HEADWEAR':
      return BusinessType.BUSINESS_TYPE_HEADWEAR;
    case 3:
    case 'BUSINESS_TYPE_VEHICLE':
      return BusinessType.BUSINESS_TYPE_VEHICLE;
    case 4:
    case 'BUSINESS_TYPE_SPONSTOR_CARNI':
      return BusinessType.BUSINESS_TYPE_SPONSTOR_CARNI;
    case 5:
    case 'BUSINESS_TYPE_RECEIVE_GIFT':
      return BusinessType.BUSINESS_TYPE_RECEIVE_GIFT;
    case 7:
    case 'BUSINESS_TYPE_RECEIVE_EXCHANGE':
      return BusinessType.BUSINESS_TYPE_RECEIVE_EXCHANGE;
    case 8:
    case 'BUSINESS_TYPE_TRANSFER':
      return BusinessType.BUSINESS_TYPE_TRANSFER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BusinessType.UNRECOGNIZED;
  }
}

export enum ClientType {
  CLIENT_TYPE_NONE = 0,
  /** CLIENT_TYPE_ANDROID - Android */
  CLIENT_TYPE_ANDROID = 1,
  /** CLIENT_TYPE_IOS - IOS */
  CLIENT_TYPE_IOS = 2,
  /** CLIENT_TYPE_WEB - Web */
  CLIENT_TYPE_WEB = 3,
  UNRECOGNIZED = -1
}

export function clientTypeFromJSON(object: any): ClientType {
  switch (object) {
    case 0:
    case 'CLIENT_TYPE_NONE':
      return ClientType.CLIENT_TYPE_NONE;
    case 1:
    case 'CLIENT_TYPE_ANDROID':
      return ClientType.CLIENT_TYPE_ANDROID;
    case 2:
    case 'CLIENT_TYPE_IOS':
      return ClientType.CLIENT_TYPE_IOS;
    case 3:
    case 'CLIENT_TYPE_WEB':
      return ClientType.CLIENT_TYPE_WEB;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ClientType.UNRECOGNIZED;
  }
}

/** 是否为心愿礼物 */
export enum WishGiftType {
  /** WISH_GIFT_TYPE_NONE - 未知 */
  WISH_GIFT_TYPE_NONE = 0,
  /** WISH_GIFT_TYPE_NO - 不是心愿礼物 */
  WISH_GIFT_TYPE_NO = 1,
  /** WISH_GIFT_TYPE_YES - 是心愿礼物 */
  WISH_GIFT_TYPE_YES = 2,
  UNRECOGNIZED = -1
}

export function wishGiftTypeFromJSON(object: any): WishGiftType {
  switch (object) {
    case 0:
    case 'WISH_GIFT_TYPE_NONE':
      return WishGiftType.WISH_GIFT_TYPE_NONE;
    case 1:
    case 'WISH_GIFT_TYPE_NO':
      return WishGiftType.WISH_GIFT_TYPE_NO;
    case 2:
    case 'WISH_GIFT_TYPE_YES':
      return WishGiftType.WISH_GIFT_TYPE_YES;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return WishGiftType.UNRECOGNIZED;
  }
}

/** 跳转方式 */
export enum JumpType {
  /** JUMP_TYPE_NONE - 无意义 */
  JUMP_TYPE_NONE = 0,
  /** JUMP_TYPE_H5 - h5 */
  JUMP_TYPE_H5 = 10,
  /** JUMP_TYPE_ROOM - 房间 */
  JUMP_TYPE_ROOM = 20,
  /** JUMP_TYPE_DEEPLINK - 路由 */
  JUMP_TYPE_DEEPLINK = 30,
  UNRECOGNIZED = -1
}

export function jumpTypeFromJSON(object: any): JumpType {
  switch (object) {
    case 0:
    case 'JUMP_TYPE_NONE':
      return JumpType.JUMP_TYPE_NONE;
    case 10:
    case 'JUMP_TYPE_H5':
      return JumpType.JUMP_TYPE_H5;
    case 20:
    case 'JUMP_TYPE_ROOM':
      return JumpType.JUMP_TYPE_ROOM;
    case 30:
    case 'JUMP_TYPE_DEEPLINK':
      return JumpType.JUMP_TYPE_DEEPLINK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return JumpType.UNRECOGNIZED;
  }
}

/** GoodsGoodsStyle 商品显示样式定义 */
export interface GoodsStyle {
  /** 弹窗样式图片，多语言 */
  dialog_svga: { [key: string]: string };
  /** 按钮图片，多语言 */
  btn_svga: { [key: string]: string };
}

export interface GoodsStyle_DialogSvgaEntry {
  key: string;
  value: string;
}

export interface GoodsStyle_BtnSvgaEntry {
  key: string;
  value: string;
}

/** 用户认证信息 */
export interface UserAuthInfo {
  /** icon地址 */
  icon: string;
  /** 认证内容 */
  content: string;
  /** 字体颜色 */
  text_color: string;
  /** 背景颜色 */
  bg_color: string;
}

/** 用户信息 */
export interface UserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
  auth_info: UserAuthInfo | undefined;
  medal_icon: string;
  /** 用户信息业务透传(各自业务端用户信息pb解析) */
  biz_info: Uint8Array;
}

export interface Balance {
  /** 币种 */
  currency_type: CurrencyType;
  /** 余额 */
  amount: number;
  /** 流水记录URL */
  detail_url: string;
  /** 保证金总额 */
  guarantee_amount: number;
  /** 保证金门槛 */
  guarantee_threshold: number;
  /** 是否已冻结 */
  frozen: boolean;
}

/** 商品订单信息 */
export interface GoodsOrderInfo {
  /** 充值订单订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付平台供应商订单 */
  plat_order_id: string;
  /** 退款单ID */
  refund_id: string;
  /** 充值到账用户ID */
  uid: number;
  /** 支付mid */
  mid: string;
  /** 商品ID */
  goods_id: number;
  /** 购买数量 */
  num: number;
  /** 支付平台配置的sk */
  sku: string;
  /** 支付金额：分（美分） */
  pay_amount: number;
  /** 货币单位：USD */
  unit: string;
  /** 获得金额货币类型 */
  currency_type: CurrencyType;
  /** 或的虚拟币金额 */
  amount: number;
  /** 支付方式 */
  pay_type: string;
  /** 客户端类型 */
  client_type: string;
  /** 客户端版本 */
  client_version: string;
  /** 设备标识 */
  did: string;
  /** 支付渠道ID */
  channel_id: string;
  /** 支付状态 */
  status: PayStatus;
  /** 支付时间 */
  pay_time: number;
  /** 退款状态 */
  refund_status: RefundStatus;
  /** 退款时间 */
  refund_time: number;
  /** 购买商品名称 */
  goods_name: string;
  /** 发起充值的用户ID */
  fuid: number;
  /** pay_amount换算成元单位 */
  pay_amount_dollar: string;
  /** 创建订单时间 */
  ctime: number;
  /** 购买商品类型 */
  goods_type: GoodsType;
  /** 礼包详情 */
  package_item: PackageItem | undefined;
  /** 客户端包名 */
  pkg: string;
  /** 第三方支付参数，国家编码 */
  location: string;
}

export interface GoodsInfo {
  /** deprecated 注意！！这个goods_id不是对应oms里的，而是映射到商品的某个具体支付方式的item_id */
  goods_id: number;
  /** 商品类型，如：虚拟币，包裹 */
  goods_type: GoodsType;
  /** 支付渠道定义的 sku_id */
  sku_id: string;
  /** 商品名称 */
  name: string;
  /** 商品描述 */
  desc: string;
  /** 商品图片 */
  images: string;
  /** 标签图片 */
  tag_images: string;
  /** 标签 */
  tag_text: string;
  /** 支付金额（分，美元就是美分） */
  price: number;
  /** 支付金额单位，如：美元（USD），印尼盾(IDR)... */
  unit: Unit;
  /** 获得金额 */
  amount: number;
  /** 获得金额货币类型 */
  currency_type: CurrencyType;
  /** 购买数量，不能小于1 */
  num: number;
  /** 额外福利数量 */
  additional_num: number;
  /** 扩展信息，创建订单时原样带回 */
  ext: string;
  /** price换算成元单位 */
  price_dollar: string;
  /** 商品名称，多语言 */
  name_lang: { [key: string]: string };
  /** 商品描述，多语言 */
  desc_lang: { [key: string]: string };
  /** 标签，多语言 */
  tag_lang: { [key: string]: string };
  /** 折扣百分比，只会大于0，eg: 10 即 10%，即1折，等于0为无折扣，而不是0% */
  discount_rate: number;
  /** 奖励包ID */
  package_id: string;
  /** 限制购买类型 */
  limit_type: GoodsLimitType;
  /** 显示样式，不同场景需要的样式不同 */
  style: GoodsStyle | undefined;
  /** 过滤参数配置，多个参数是与的关系 */
  param_filter: { [key: string]: StringList };
  /** 商品ID，对应oms的goods_id */
  product_id: number;
}

export interface GoodsInfo_NameLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfo_DescLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfo_TagLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfo_ParamFilterEntry {
  key: string;
  value: StringList | undefined;
}

/** 奖励包信息 */
export interface PackageItem {
  /** 奖励配置项ID, 为了通用这里采用字符串形式, 例如礼物ID, 头像框ID, 房间背景ID, ... */
  item_id: string;
  /** 奖励配置项名称, 例如: 礼物的名称 / 头像框的名称. 这是缺省名称, 如果 oms 需要国际化显示则需要提供 i18n_category_name 字段. */
  item_name: string;
  /** 国家化的奖励配置项名称, key 的取值有 zh / en / ar / tr ... */
  i18n_item_name: { [key: string]: string };
  /** 奖励配置项图标, 例如: 礼物的图标 / 头像框的图标. 这是缺省图标, 如果 oms 需要国际化显示则需要提供 i18n_category_icon 字段. */
  item_icon: string;
  /** 国家化的奖励配置项图标, key 的取值有 zh / en / ar / tr ... */
  i18n_item_icon: { [key: string]: string };
  /** 奖励配置项动画资源 */
  item_animation: string;
  /** 拓展信息, 应当设计成一个 JSON 便于后续拓展. */
  expand: string;
}

export interface PackageItem_I18nItemNameEntry {
  key: string;
  value: string;
}

export interface PackageItem_I18nItemIconEntry {
  key: string;
  value: string;
}

export interface StringList {
  values: string[];
}

/** darling 的业务拓展信息 */
export interface GiftBizExtDarling {
  /** PK 积分. */
  pk_points: number;
  /** 是否为心愿礼物 */
  wish_gift_type: WishGiftType;
}

function createBaseGoodsStyle(): GoodsStyle {
  return { dialog_svga: {}, btn_svga: {} };
}

export const GoodsStyle: MessageFns<GoodsStyle> = {
  fromJSON(object: any): GoodsStyle {
    return {
      dialog_svga: isObject(object.dialog_svga)
        ? Object.entries(object.dialog_svga).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      btn_svga: isObject(object.btn_svga)
        ? Object.entries(object.btn_svga).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GoodsStyle>, I>>(base?: I): GoodsStyle {
    return GoodsStyle.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsStyle>, I>>(object: I): GoodsStyle {
    const message = createBaseGoodsStyle();
    message.dialog_svga = Object.entries(object.dialog_svga ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.btn_svga = Object.entries(object.btn_svga ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGoodsStyle_DialogSvgaEntry(): GoodsStyle_DialogSvgaEntry {
  return { key: '', value: '' };
}

export const GoodsStyle_DialogSvgaEntry: MessageFns<GoodsStyle_DialogSvgaEntry> = {
  fromJSON(object: any): GoodsStyle_DialogSvgaEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsStyle_DialogSvgaEntry>, I>>(base?: I): GoodsStyle_DialogSvgaEntry {
    return GoodsStyle_DialogSvgaEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsStyle_DialogSvgaEntry>, I>>(object: I): GoodsStyle_DialogSvgaEntry {
    const message = createBaseGoodsStyle_DialogSvgaEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsStyle_BtnSvgaEntry(): GoodsStyle_BtnSvgaEntry {
  return { key: '', value: '' };
}

export const GoodsStyle_BtnSvgaEntry: MessageFns<GoodsStyle_BtnSvgaEntry> = {
  fromJSON(object: any): GoodsStyle_BtnSvgaEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsStyle_BtnSvgaEntry>, I>>(base?: I): GoodsStyle_BtnSvgaEntry {
    return GoodsStyle_BtnSvgaEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsStyle_BtnSvgaEntry>, I>>(object: I): GoodsStyle_BtnSvgaEntry {
    const message = createBaseGoodsStyle_BtnSvgaEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUserAuthInfo(): UserAuthInfo {
  return { icon: '', content: '', text_color: '', bg_color: '' };
}

export const UserAuthInfo: MessageFns<UserAuthInfo> = {
  fromJSON(object: any): UserAuthInfo {
    return {
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      text_color: isSet(object.text_color) ? globalThis.String(object.text_color) : '',
      bg_color: isSet(object.bg_color) ? globalThis.String(object.bg_color) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserAuthInfo>, I>>(base?: I): UserAuthInfo {
    return UserAuthInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserAuthInfo>, I>>(object: I): UserAuthInfo {
    const message = createBaseUserAuthInfo();
    message.icon = object.icon ?? '';
    message.content = object.content ?? '';
    message.text_color = object.text_color ?? '';
    message.bg_color = object.bg_color ?? '';
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return {
    uid: 0,
    show_uid: '',
    nickname: '',
    avatar: '',
    auth_info: undefined,
    medal_icon: '',
    biz_info: new Uint8Array(0)
  };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      auth_info: isSet(object.auth_info) ? UserAuthInfo.fromJSON(object.auth_info) : undefined,
      medal_icon: isSet(object.medal_icon) ? globalThis.String(object.medal_icon) : '',
      biz_info: isSet(object.biz_info) ? bytesFromBase64(object.biz_info) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.auth_info =
      object.auth_info !== undefined && object.auth_info !== null
        ? UserAuthInfo.fromPartial(object.auth_info)
        : undefined;
    message.medal_icon = object.medal_icon ?? '';
    message.biz_info = object.biz_info ?? new Uint8Array(0);
    return message;
  }
};

function createBaseBalance(): Balance {
  return { currency_type: 0, amount: 0, detail_url: '', guarantee_amount: 0, guarantee_threshold: 0, frozen: false };
}

export const Balance: MessageFns<Balance> = {
  fromJSON(object: any): Balance {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      detail_url: isSet(object.detail_url) ? globalThis.String(object.detail_url) : '',
      guarantee_amount: isSet(object.guarantee_amount) ? globalThis.Number(object.guarantee_amount) : 0,
      guarantee_threshold: isSet(object.guarantee_threshold) ? globalThis.Number(object.guarantee_threshold) : 0,
      frozen: isSet(object.frozen) ? globalThis.Boolean(object.frozen) : false
    };
  },

  create<I extends Exact<DeepPartial<Balance>, I>>(base?: I): Balance {
    return Balance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Balance>, I>>(object: I): Balance {
    const message = createBaseBalance();
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.detail_url = object.detail_url ?? '';
    message.guarantee_amount = object.guarantee_amount ?? 0;
    message.guarantee_threshold = object.guarantee_threshold ?? 0;
    message.frozen = object.frozen ?? false;
    return message;
  }
};

function createBaseGoodsOrderInfo(): GoodsOrderInfo {
  return {
    order_no: '',
    pay_order_id: '',
    plat_order_id: '',
    refund_id: '',
    uid: 0,
    mid: '',
    goods_id: 0,
    num: 0,
    sku: '',
    pay_amount: 0,
    unit: '',
    currency_type: 0,
    amount: 0,
    pay_type: '',
    client_type: '',
    client_version: '',
    did: '',
    channel_id: '',
    status: 0,
    pay_time: 0,
    refund_status: 0,
    refund_time: 0,
    goods_name: '',
    fuid: 0,
    pay_amount_dollar: '',
    ctime: 0,
    goods_type: 0,
    package_item: undefined,
    pkg: '',
    location: ''
  };
}

export const GoodsOrderInfo: MessageFns<GoodsOrderInfo> = {
  fromJSON(object: any): GoodsOrderInfo {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      plat_order_id: isSet(object.plat_order_id) ? globalThis.String(object.plat_order_id) : '',
      refund_id: isSet(object.refund_id) ? globalThis.String(object.refund_id) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0,
      unit: isSet(object.unit) ? globalThis.String(object.unit) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      client_type: isSet(object.client_type) ? globalThis.String(object.client_type) : '',
      client_version: isSet(object.client_version) ? globalThis.String(object.client_version) : '',
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      channel_id: isSet(object.channel_id) ? globalThis.String(object.channel_id) : '',
      status: isSet(object.status) ? payStatusFromJSON(object.status) : 0,
      pay_time: isSet(object.pay_time) ? globalThis.Number(object.pay_time) : 0,
      refund_status: isSet(object.refund_status) ? refundStatusFromJSON(object.refund_status) : 0,
      refund_time: isSet(object.refund_time) ? globalThis.Number(object.refund_time) : 0,
      goods_name: isSet(object.goods_name) ? globalThis.String(object.goods_name) : '',
      fuid: isSet(object.fuid) ? globalThis.Number(object.fuid) : 0,
      pay_amount_dollar: isSet(object.pay_amount_dollar) ? globalThis.String(object.pay_amount_dollar) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      goods_type: isSet(object.goods_type) ? goodsTypeFromJSON(object.goods_type) : 0,
      package_item: isSet(object.package_item) ? PackageItem.fromJSON(object.package_item) : undefined,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      location: isSet(object.location) ? globalThis.String(object.location) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsOrderInfo>, I>>(base?: I): GoodsOrderInfo {
    return GoodsOrderInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsOrderInfo>, I>>(object: I): GoodsOrderInfo {
    const message = createBaseGoodsOrderInfo();
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.plat_order_id = object.plat_order_id ?? '';
    message.refund_id = object.refund_id ?? '';
    message.uid = object.uid ?? 0;
    message.mid = object.mid ?? '';
    message.goods_id = object.goods_id ?? 0;
    message.num = object.num ?? 0;
    message.sku = object.sku ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    message.unit = object.unit ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.client_type = object.client_type ?? '';
    message.client_version = object.client_version ?? '';
    message.did = object.did ?? '';
    message.channel_id = object.channel_id ?? '';
    message.status = object.status ?? 0;
    message.pay_time = object.pay_time ?? 0;
    message.refund_status = object.refund_status ?? 0;
    message.refund_time = object.refund_time ?? 0;
    message.goods_name = object.goods_name ?? '';
    message.fuid = object.fuid ?? 0;
    message.pay_amount_dollar = object.pay_amount_dollar ?? '';
    message.ctime = object.ctime ?? 0;
    message.goods_type = object.goods_type ?? 0;
    message.package_item =
      object.package_item !== undefined && object.package_item !== null
        ? PackageItem.fromPartial(object.package_item)
        : undefined;
    message.pkg = object.pkg ?? '';
    message.location = object.location ?? '';
    return message;
  }
};

function createBaseGoodsInfo(): GoodsInfo {
  return {
    goods_id: 0,
    goods_type: 0,
    sku_id: '',
    name: '',
    desc: '',
    images: '',
    tag_images: '',
    tag_text: '',
    price: 0,
    unit: 0,
    amount: 0,
    currency_type: 0,
    num: 0,
    additional_num: 0,
    ext: '',
    price_dollar: '',
    name_lang: {},
    desc_lang: {},
    tag_lang: {},
    discount_rate: 0,
    package_id: '',
    limit_type: 0,
    style: undefined,
    param_filter: {},
    product_id: 0
  };
}

export const GoodsInfo: MessageFns<GoodsInfo> = {
  fromJSON(object: any): GoodsInfo {
    return {
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      goods_type: isSet(object.goods_type) ? goodsTypeFromJSON(object.goods_type) : 0,
      sku_id: isSet(object.sku_id) ? globalThis.String(object.sku_id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      images: isSet(object.images) ? globalThis.String(object.images) : '',
      tag_images: isSet(object.tag_images) ? globalThis.String(object.tag_images) : '',
      tag_text: isSet(object.tag_text) ? globalThis.String(object.tag_text) : '',
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      unit: isSet(object.unit) ? unitFromJSON(object.unit) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      additional_num: isSet(object.additional_num) ? globalThis.Number(object.additional_num) : 0,
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      price_dollar: isSet(object.price_dollar) ? globalThis.String(object.price_dollar) : '',
      name_lang: isObject(object.name_lang)
        ? Object.entries(object.name_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      desc_lang: isObject(object.desc_lang)
        ? Object.entries(object.desc_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      tag_lang: isObject(object.tag_lang)
        ? Object.entries(object.tag_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      discount_rate: isSet(object.discount_rate) ? globalThis.Number(object.discount_rate) : 0,
      package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '',
      limit_type: isSet(object.limit_type) ? goodsLimitTypeFromJSON(object.limit_type) : 0,
      style: isSet(object.style) ? GoodsStyle.fromJSON(object.style) : undefined,
      param_filter: isObject(object.param_filter)
        ? Object.entries(object.param_filter).reduce<{ [key: string]: StringList }>((acc, [key, value]) => {
            acc[key] = StringList.fromJSON(value);
            return acc;
          }, {})
        : {},
      product_id: isSet(object.product_id) ? globalThis.Number(object.product_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfo>, I>>(base?: I): GoodsInfo {
    return GoodsInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfo>, I>>(object: I): GoodsInfo {
    const message = createBaseGoodsInfo();
    message.goods_id = object.goods_id ?? 0;
    message.goods_type = object.goods_type ?? 0;
    message.sku_id = object.sku_id ?? '';
    message.name = object.name ?? '';
    message.desc = object.desc ?? '';
    message.images = object.images ?? '';
    message.tag_images = object.tag_images ?? '';
    message.tag_text = object.tag_text ?? '';
    message.price = object.price ?? 0;
    message.unit = object.unit ?? 0;
    message.amount = object.amount ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.num = object.num ?? 0;
    message.additional_num = object.additional_num ?? 0;
    message.ext = object.ext ?? '';
    message.price_dollar = object.price_dollar ?? '';
    message.name_lang = Object.entries(object.name_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.desc_lang = Object.entries(object.desc_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.tag_lang = Object.entries(object.tag_lang ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.discount_rate = object.discount_rate ?? 0;
    message.package_id = object.package_id ?? '';
    message.limit_type = object.limit_type ?? 0;
    message.style =
      object.style !== undefined && object.style !== null ? GoodsStyle.fromPartial(object.style) : undefined;
    message.param_filter = Object.entries(object.param_filter ?? {}).reduce<{ [key: string]: StringList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = StringList.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.product_id = object.product_id ?? 0;
    return message;
  }
};

function createBaseGoodsInfo_NameLangEntry(): GoodsInfo_NameLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfo_NameLangEntry: MessageFns<GoodsInfo_NameLangEntry> = {
  fromJSON(object: any): GoodsInfo_NameLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfo_NameLangEntry>, I>>(base?: I): GoodsInfo_NameLangEntry {
    return GoodsInfo_NameLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfo_NameLangEntry>, I>>(object: I): GoodsInfo_NameLangEntry {
    const message = createBaseGoodsInfo_NameLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfo_DescLangEntry(): GoodsInfo_DescLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfo_DescLangEntry: MessageFns<GoodsInfo_DescLangEntry> = {
  fromJSON(object: any): GoodsInfo_DescLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfo_DescLangEntry>, I>>(base?: I): GoodsInfo_DescLangEntry {
    return GoodsInfo_DescLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfo_DescLangEntry>, I>>(object: I): GoodsInfo_DescLangEntry {
    const message = createBaseGoodsInfo_DescLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfo_TagLangEntry(): GoodsInfo_TagLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfo_TagLangEntry: MessageFns<GoodsInfo_TagLangEntry> = {
  fromJSON(object: any): GoodsInfo_TagLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfo_TagLangEntry>, I>>(base?: I): GoodsInfo_TagLangEntry {
    return GoodsInfo_TagLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfo_TagLangEntry>, I>>(object: I): GoodsInfo_TagLangEntry {
    const message = createBaseGoodsInfo_TagLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfo_ParamFilterEntry(): GoodsInfo_ParamFilterEntry {
  return { key: '', value: undefined };
}

export const GoodsInfo_ParamFilterEntry: MessageFns<GoodsInfo_ParamFilterEntry> = {
  fromJSON(object: any): GoodsInfo_ParamFilterEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? StringList.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfo_ParamFilterEntry>, I>>(base?: I): GoodsInfo_ParamFilterEntry {
    return GoodsInfo_ParamFilterEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfo_ParamFilterEntry>, I>>(object: I): GoodsInfo_ParamFilterEntry {
    const message = createBaseGoodsInfo_ParamFilterEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? StringList.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePackageItem(): PackageItem {
  return {
    item_id: '',
    item_name: '',
    i18n_item_name: {},
    item_icon: '',
    i18n_item_icon: {},
    item_animation: '',
    expand: ''
  };
}

export const PackageItem: MessageFns<PackageItem> = {
  fromJSON(object: any): PackageItem {
    return {
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      i18n_item_name: isObject(object.i18n_item_name)
        ? Object.entries(object.i18n_item_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_icon: isSet(object.item_icon) ? globalThis.String(object.item_icon) : '',
      i18n_item_icon: isObject(object.i18n_item_icon)
        ? Object.entries(object.i18n_item_icon).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_animation: isSet(object.item_animation) ? globalThis.String(object.item_animation) : '',
      expand: isSet(object.expand) ? globalThis.String(object.expand) : ''
    };
  },

  create<I extends Exact<DeepPartial<PackageItem>, I>>(base?: I): PackageItem {
    return PackageItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PackageItem>, I>>(object: I): PackageItem {
    const message = createBasePackageItem();
    message.item_id = object.item_id ?? '';
    message.item_name = object.item_name ?? '';
    message.i18n_item_name = Object.entries(object.i18n_item_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_icon = object.item_icon ?? '';
    message.i18n_item_icon = Object.entries(object.i18n_item_icon ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_animation = object.item_animation ?? '';
    message.expand = object.expand ?? '';
    return message;
  }
};

function createBasePackageItem_I18nItemNameEntry(): PackageItem_I18nItemNameEntry {
  return { key: '', value: '' };
}

export const PackageItem_I18nItemNameEntry: MessageFns<PackageItem_I18nItemNameEntry> = {
  fromJSON(object: any): PackageItem_I18nItemNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PackageItem_I18nItemNameEntry>, I>>(base?: I): PackageItem_I18nItemNameEntry {
    return PackageItem_I18nItemNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PackageItem_I18nItemNameEntry>, I>>(
    object: I
  ): PackageItem_I18nItemNameEntry {
    const message = createBasePackageItem_I18nItemNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePackageItem_I18nItemIconEntry(): PackageItem_I18nItemIconEntry {
  return { key: '', value: '' };
}

export const PackageItem_I18nItemIconEntry: MessageFns<PackageItem_I18nItemIconEntry> = {
  fromJSON(object: any): PackageItem_I18nItemIconEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PackageItem_I18nItemIconEntry>, I>>(base?: I): PackageItem_I18nItemIconEntry {
    return PackageItem_I18nItemIconEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PackageItem_I18nItemIconEntry>, I>>(
    object: I
  ): PackageItem_I18nItemIconEntry {
    const message = createBasePackageItem_I18nItemIconEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseStringList(): StringList {
  return { values: [] };
}

export const StringList: MessageFns<StringList> = {
  fromJSON(object: any): StringList {
    return {
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<StringList>, I>>(base?: I): StringList {
    return StringList.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StringList>, I>>(object: I): StringList {
    const message = createBaseStringList();
    message.values = object.values?.map(e => e) || [];
    return message;
  }
};

function createBaseGiftBizExtDarling(): GiftBizExtDarling {
  return { pk_points: 0, wish_gift_type: 0 };
}

export const GiftBizExtDarling: MessageFns<GiftBizExtDarling> = {
  fromJSON(object: any): GiftBizExtDarling {
    return {
      pk_points: isSet(object.pk_points) ? globalThis.Number(object.pk_points) : 0,
      wish_gift_type: isSet(object.wish_gift_type) ? wishGiftTypeFromJSON(object.wish_gift_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftBizExtDarling>, I>>(base?: I): GiftBizExtDarling {
    return GiftBizExtDarling.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBizExtDarling>, I>>(object: I): GiftBizExtDarling {
    const message = createBaseGiftBizExtDarling();
    message.pk_points = object.pk_points ?? 0;
    message.wish_gift_type = object.wish_gift_type ?? 0;
    return message;
  }
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
