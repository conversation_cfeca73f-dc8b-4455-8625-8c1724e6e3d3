// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/orders.proto

/* eslint-disable */
import { CurrencyType, currencyTypeFromJSON, PayStatus, payStatusFromJSON } from './common';

export const protobufPackage = 'pbrevenue';

export interface GetOrdersByUidReq {
  /** uid */
  uid: number;
}

export interface GetOrdersByUidRsp {
  /** 订单列表 */
  order_list: OrderShowInfo[];
}

/** 客户端展示订单信息 */
export interface OrderShowInfo {
  /** 充值订单订单号 */
  order_no: string;
  /** 充值到账用户ID */
  uid: number;
  /** 商品ID */
  goods_id: number;
  /** 购买数量 */
  num: number;
  /** 支付金额：分（美分） */
  pay_amount: number;
  /** 货币单位：USD */
  unit: string;
  /** 获得金额货币类型 */
  currency_type: CurrencyType;
  /** 或的虚拟币金额 */
  amount: number;
  /** 支付方式 */
  pay_type: string;
  /** 支付状态 */
  status: PayStatus;
  /** 支付时间 */
  pay_time: number;
  /** 购买商品名称 */
  goods_name: string;
  /** pay_amount换算成元单位 */
  pay_amount_dollar: string;
}

function createBaseGetOrdersByUidReq(): GetOrdersByUidReq {
  return { uid: 0 };
}

export const GetOrdersByUidReq: MessageFns<GetOrdersByUidReq> = {
  fromJSON(object: any): GetOrdersByUidReq {
    return { uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0 };
  },

  create<I extends Exact<DeepPartial<GetOrdersByUidReq>, I>>(base?: I): GetOrdersByUidReq {
    return GetOrdersByUidReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrdersByUidReq>, I>>(object: I): GetOrdersByUidReq {
    const message = createBaseGetOrdersByUidReq();
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseGetOrdersByUidRsp(): GetOrdersByUidRsp {
  return { order_list: [] };
}

export const GetOrdersByUidRsp: MessageFns<GetOrdersByUidRsp> = {
  fromJSON(object: any): GetOrdersByUidRsp {
    return {
      order_list: globalThis.Array.isArray(object?.order_list)
        ? object.order_list.map((e: any) => OrderShowInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetOrdersByUidRsp>, I>>(base?: I): GetOrdersByUidRsp {
    return GetOrdersByUidRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetOrdersByUidRsp>, I>>(object: I): GetOrdersByUidRsp {
    const message = createBaseGetOrdersByUidRsp();
    message.order_list = object.order_list?.map(e => OrderShowInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseOrderShowInfo(): OrderShowInfo {
  return {
    order_no: '',
    uid: 0,
    goods_id: 0,
    num: 0,
    pay_amount: 0,
    unit: '',
    currency_type: 0,
    amount: 0,
    pay_type: '',
    status: 0,
    pay_time: 0,
    goods_name: '',
    pay_amount_dollar: ''
  };
}

export const OrderShowInfo: MessageFns<OrderShowInfo> = {
  fromJSON(object: any): OrderShowInfo {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0,
      unit: isSet(object.unit) ? globalThis.String(object.unit) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      status: isSet(object.status) ? payStatusFromJSON(object.status) : 0,
      pay_time: isSet(object.pay_time) ? globalThis.Number(object.pay_time) : 0,
      goods_name: isSet(object.goods_name) ? globalThis.String(object.goods_name) : '',
      pay_amount_dollar: isSet(object.pay_amount_dollar) ? globalThis.String(object.pay_amount_dollar) : ''
    };
  },

  create<I extends Exact<DeepPartial<OrderShowInfo>, I>>(base?: I): OrderShowInfo {
    return OrderShowInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderShowInfo>, I>>(object: I): OrderShowInfo {
    const message = createBaseOrderShowInfo();
    message.order_no = object.order_no ?? '';
    message.uid = object.uid ?? 0;
    message.goods_id = object.goods_id ?? 0;
    message.num = object.num ?? 0;
    message.pay_amount = object.pay_amount ?? 0;
    message.unit = object.unit ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.status = object.status ?? 0;
    message.pay_time = object.pay_time ?? 0;
    message.goods_name = object.goods_name ?? '';
    message.pay_amount_dollar = object.pay_amount_dollar ?? '';
    return message;
  }
};

export type OrdersDefinition = typeof OrdersDefinition;
export const OrdersDefinition = {
  name: 'Orders',
  fullName: 'pbrevenue.Orders',
  methods: {
    /** 查询某用户近30日的充值订单 */
    getOrdersByUid: {
      name: 'GetOrdersByUid',
      requestType: GetOrdersByUidReq,
      requestStream: false,
      responseType: GetOrdersByUidRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
