// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/wallet.proto

/* eslint-disable */
import { Page } from '../common/common';
import { Balance, BusinessType, businessTypeFromJSON, CurrencyType, currencyTypeFromJSON, UserInfo } from './common';

export const protobufPackage = 'pbrevenue';

/** 交易类型 */
export enum TransactionType {
  TRANSACTION_TYPE_NONE = 0,
  /** TRANSACTION_TYPE_RECHARGE - 充值 */
  TRANSACTION_TYPE_RECHARGE = 1,
  /** TRANSACTION_TYPE_CONSUME - 消费 */
  TRANSACTION_TYPE_CONSUME = 2,
  /** TRANSACTION_TYPE_INCOME - 收入 */
  TRANSACTION_TYPE_INCOME = 3,
  /** TRANSACTION_TYPE_REFUND - 退款 */
  TRANSACTION_TYPE_REFUND = 4,
  UNRECOGNIZED = -1
}

export function transactionTypeFromJSON(object: any): TransactionType {
  switch (object) {
    case 0:
    case 'TRANSACTION_TYPE_NONE':
      return TransactionType.TRANSACTION_TYPE_NONE;
    case 1:
    case 'TRANSACTION_TYPE_RECHARGE':
      return TransactionType.TRANSACTION_TYPE_RECHARGE;
    case 2:
    case 'TRANSACTION_TYPE_CONSUME':
      return TransactionType.TRANSACTION_TYPE_CONSUME;
    case 3:
    case 'TRANSACTION_TYPE_INCOME':
      return TransactionType.TRANSACTION_TYPE_INCOME;
    case 4:
    case 'TRANSACTION_TYPE_REFUND':
      return TransactionType.TRANSACTION_TYPE_REFUND;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TransactionType.UNRECOGNIZED;
  }
}

export enum BalanceStatus {
  /** BALANCE_STATUS_NONE - 正常状态 */
  BALANCE_STATUS_NONE = 0,
  /** BALANCE_STATUS_FROZEN - 冻结 */
  BALANCE_STATUS_FROZEN = 3,
  UNRECOGNIZED = -1
}

export function balanceStatusFromJSON(object: any): BalanceStatus {
  switch (object) {
    case 0:
    case 'BALANCE_STATUS_NONE':
      return BalanceStatus.BALANCE_STATUS_NONE;
    case 3:
    case 'BALANCE_STATUS_FROZEN':
      return BalanceStatus.BALANCE_STATUS_FROZEN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BalanceStatus.UNRECOGNIZED;
  }
}

export enum WithdrawStatus {
  WITHDRAW_STATUS_NONE = 0,
  /** WITHDRAW_STATUS_IN_REVIEW - 申请审核中 */
  WITHDRAW_STATUS_IN_REVIEW = 1,
  /** WITHDRAW_STATUS_IN_CANCEL_REVIEW - 取消审核中 */
  WITHDRAW_STATUS_IN_CANCEL_REVIEW = 2,
  /** WITHDRAW_STATUS_SUCCESS - 审核通过，提现成功 */
  WITHDRAW_STATUS_SUCCESS = 3,
  /** WITHDRAW_STATUS_FAILED - 审核不通过 */
  WITHDRAW_STATUS_FAILED = 4,
  /** WITHDRAW_STATUS_CANCELED - 用户主动取消 */
  WITHDRAW_STATUS_CANCELED = 5,
  UNRECOGNIZED = -1
}

export function withdrawStatusFromJSON(object: any): WithdrawStatus {
  switch (object) {
    case 0:
    case 'WITHDRAW_STATUS_NONE':
      return WithdrawStatus.WITHDRAW_STATUS_NONE;
    case 1:
    case 'WITHDRAW_STATUS_IN_REVIEW':
      return WithdrawStatus.WITHDRAW_STATUS_IN_REVIEW;
    case 2:
    case 'WITHDRAW_STATUS_IN_CANCEL_REVIEW':
      return WithdrawStatus.WITHDRAW_STATUS_IN_CANCEL_REVIEW;
    case 3:
    case 'WITHDRAW_STATUS_SUCCESS':
      return WithdrawStatus.WITHDRAW_STATUS_SUCCESS;
    case 4:
    case 'WITHDRAW_STATUS_FAILED':
      return WithdrawStatus.WITHDRAW_STATUS_FAILED;
    case 5:
    case 'WITHDRAW_STATUS_CANCELED':
      return WithdrawStatus.WITHDRAW_STATUS_CANCELED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return WithdrawStatus.UNRECOGNIZED;
  }
}

export interface GetBalanceReq {
  currency_types: CurrencyType[];
}

export interface GetBalanceRsp {
  balances: Balance[];
  /** 消费记录详情跳转url */
  detail_url: string;
  /** 联系我们跳转url */
  contact_us_url: string;
}

export interface TransactionSnapshot {
  /** 收礼用户信息 */
  receiver: UserInfo | undefined;
  /** 消费类型 */
  business_type: string;
  /** 子类型 */
  sub_business_type: string;
  /** 子类型文案 */
  sub_business_type_text: string;
  /** 商品或物品ID */
  item_id: number;
  /** 商品名称 */
  item_name: string;
  /** 道具icon */
  icon: string;
  /** 数量或时长 */
  unit: number;
  /** 送礼用户信息 */
  sender: UserInfo | undefined;
  /** 兑换场景用到 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 可以获得多少数量货币 */
  target_quantity: number;
  /** 发起兑换的用户信息 */
  source_user: UserInfo | undefined;
  /** 兑换对象信息 */
  target_user: UserInfo | undefined;
  /** 业务自定义扩展信息 */
  expand: { [key: string]: string };
}

export interface TransactionSnapshot_ExpandEntry {
  key: string;
  value: string;
}

export interface TransactionDetail {
  id: number;
  type: TransactionType;
  /** 消费类型文案 对应-business_type */
  title: string;
  currency_type: CurrencyType;
  amount: number;
  balance: number;
  /** 交易发生的时间戳, unix-timestamp 精确到秒. */
  time_transacted: number;
  /** 消费扩展信息 */
  snapshot: TransactionSnapshot | undefined;
  /** 扣费业务类型,废除 */
  business_type: BusinessType;
  /** 币种icon图片 */
  currency_type_icon: string;
  /** 业务类型 */
  origin_business_type: string;
}

export interface ListTransactionDetailReq {
  page: Page | undefined;
  /** 交易类型 */
  types: TransactionType[];
  /** 币种 */
  currency_types: CurrencyType[];
  /** 最小金额 */
  min_amount: number;
  /** 最大金额 */
  max_amount: number;
  /** 起始时间, unix-timestamp 精确到秒. */
  min_time_transacted: number;
  /** 结束时间, unix-timestamp 精确到秒. */
  max_time_transacted: number;
  /** 扣费业务类型,废除 */
  business_type: BusinessType;
  /** 业务类型 */
  origin_business_type: string;
}

export interface ListTransactionDetailRsp {
  page: Page | undefined;
  /** 按交易时间倒序排列 */
  transaction_details: TransactionDetail[];
}

export interface ListTransactionSearchTypeReq {
  /** 币种 */
  currency_type: CurrencyType;
}

export interface ListTransactionSearchTypeRsp {
  /** 业务类型 */
  list: BusinessType[];
}

export interface TransferBalanceReq {
  seq_id: string;
  to_uid: number;
  currency_type: CurrencyType;
  amount: number;
  /** 消费类型 */
  business_type: string;
  /** 子类型 */
  sub_business_type: string;
  /** 子类型文案 */
  sub_business_type_text: string;
}

export interface TransferBalanceRsp {
  /** 返回自己的余额 */
  balance: Balance | undefined;
}

export interface WalletWithdrawReq {
  /** 币种 */
  currency_type: CurrencyType;
  /** 提现金额 */
  amount: number;
  /** 提现信息 */
  withdraw_info: WithdrawInfo | undefined;
}

export interface WalletWithdrawRsp {}

export interface WithdrawInfo {
  /** 名字 */
  name: string;
  /** 国家 */
  cou: string;
  /** 手机 */
  phone: string;
  /** 备注 */
  remark: string;
  /** 拓展字段 */
  expand: { [key: string]: string };
}

export interface WithdrawInfo_ExpandEntry {
  key: string;
  value: string;
}

export interface WalletCancelWithdrawReq {
  /** 提现记录id */
  id: number;
}

export interface WalletCancelWithdrawRsp {}

export interface ListWithdrawDetailReq {
  page: Page | undefined;
  /** 币种 */
  currency_type: CurrencyType;
}

export interface ListWithdrawDetailRsp {
  page: Page | undefined;
  withdraw_details: WithdrawDetail[];
}

export interface WithdrawDetail {
  /** 提现记录id */
  id: number;
  /** 提现数额 */
  amount: number;
  /** 用户申请时填写的信息 */
  withdraw_info: WithdrawInfo | undefined;
  /** 状态 */
  status: WithdrawStatus;
  /** 申请时间 */
  create_at: number;
  /** 审核结果 */
  audit_info: WithdrawAuditInfo | undefined;
}

export interface WithdrawAuditInfo {
  remark: string;
}

function createBaseGetBalanceReq(): GetBalanceReq {
  return { currency_types: [] };
}

export const GetBalanceReq: MessageFns<GetBalanceReq> = {
  fromJSON(object: any): GetBalanceReq {
    return {
      currency_types: globalThis.Array.isArray(object?.currency_types)
        ? object.currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetBalanceReq>, I>>(base?: I): GetBalanceReq {
    return GetBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBalanceReq>, I>>(object: I): GetBalanceReq {
    const message = createBaseGetBalanceReq();
    message.currency_types = object.currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseGetBalanceRsp(): GetBalanceRsp {
  return { balances: [], detail_url: '', contact_us_url: '' };
}

export const GetBalanceRsp: MessageFns<GetBalanceRsp> = {
  fromJSON(object: any): GetBalanceRsp {
    return {
      balances: globalThis.Array.isArray(object?.balances) ? object.balances.map((e: any) => Balance.fromJSON(e)) : [],
      detail_url: isSet(object.detail_url) ? globalThis.String(object.detail_url) : '',
      contact_us_url: isSet(object.contact_us_url) ? globalThis.String(object.contact_us_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetBalanceRsp>, I>>(base?: I): GetBalanceRsp {
    return GetBalanceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBalanceRsp>, I>>(object: I): GetBalanceRsp {
    const message = createBaseGetBalanceRsp();
    message.balances = object.balances?.map(e => Balance.fromPartial(e)) || [];
    message.detail_url = object.detail_url ?? '';
    message.contact_us_url = object.contact_us_url ?? '';
    return message;
  }
};

function createBaseTransactionSnapshot(): TransactionSnapshot {
  return {
    receiver: undefined,
    business_type: '',
    sub_business_type: '',
    sub_business_type_text: '',
    item_id: 0,
    item_name: '',
    icon: '',
    unit: 0,
    sender: undefined,
    source_currency_type: 0,
    source_quantity: 0,
    target_currency_type: 0,
    target_quantity: 0,
    source_user: undefined,
    target_user: undefined,
    expand: {}
  };
}

export const TransactionSnapshot: MessageFns<TransactionSnapshot> = {
  fromJSON(object: any): TransactionSnapshot {
    return {
      receiver: isSet(object.receiver) ? UserInfo.fromJSON(object.receiver) : undefined,
      business_type: isSet(object.business_type) ? globalThis.String(object.business_type) : '',
      sub_business_type: isSet(object.sub_business_type) ? globalThis.String(object.sub_business_type) : '',
      sub_business_type_text: isSet(object.sub_business_type_text)
        ? globalThis.String(object.sub_business_type_text)
        : '',
      item_id: isSet(object.item_id) ? globalThis.Number(object.item_id) : 0,
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      unit: isSet(object.unit) ? globalThis.Number(object.unit) : 0,
      sender: isSet(object.sender) ? UserInfo.fromJSON(object.sender) : undefined,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      source_user: isSet(object.source_user) ? UserInfo.fromJSON(object.source_user) : undefined,
      target_user: isSet(object.target_user) ? UserInfo.fromJSON(object.target_user) : undefined,
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<TransactionSnapshot>, I>>(base?: I): TransactionSnapshot {
    return TransactionSnapshot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionSnapshot>, I>>(object: I): TransactionSnapshot {
    const message = createBaseTransactionSnapshot();
    message.receiver =
      object.receiver !== undefined && object.receiver !== null ? UserInfo.fromPartial(object.receiver) : undefined;
    message.business_type = object.business_type ?? '';
    message.sub_business_type = object.sub_business_type ?? '';
    message.sub_business_type_text = object.sub_business_type_text ?? '';
    message.item_id = object.item_id ?? 0;
    message.item_name = object.item_name ?? '';
    message.icon = object.icon ?? '';
    message.unit = object.unit ?? 0;
    message.sender =
      object.sender !== undefined && object.sender !== null ? UserInfo.fromPartial(object.sender) : undefined;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.source_user =
      object.source_user !== undefined && object.source_user !== null
        ? UserInfo.fromPartial(object.source_user)
        : undefined;
    message.target_user =
      object.target_user !== undefined && object.target_user !== null
        ? UserInfo.fromPartial(object.target_user)
        : undefined;
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseTransactionSnapshot_ExpandEntry(): TransactionSnapshot_ExpandEntry {
  return { key: '', value: '' };
}

export const TransactionSnapshot_ExpandEntry: MessageFns<TransactionSnapshot_ExpandEntry> = {
  fromJSON(object: any): TransactionSnapshot_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<TransactionSnapshot_ExpandEntry>, I>>(base?: I): TransactionSnapshot_ExpandEntry {
    return TransactionSnapshot_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionSnapshot_ExpandEntry>, I>>(
    object: I
  ): TransactionSnapshot_ExpandEntry {
    const message = createBaseTransactionSnapshot_ExpandEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseTransactionDetail(): TransactionDetail {
  return {
    id: 0,
    type: 0,
    title: '',
    currency_type: 0,
    amount: 0,
    balance: 0,
    time_transacted: 0,
    snapshot: undefined,
    business_type: 0,
    currency_type_icon: '',
    origin_business_type: ''
  };
}

export const TransactionDetail: MessageFns<TransactionDetail> = {
  fromJSON(object: any): TransactionDetail {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      type: isSet(object.type) ? transactionTypeFromJSON(object.type) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      balance: isSet(object.balance) ? globalThis.Number(object.balance) : 0,
      time_transacted: isSet(object.time_transacted) ? globalThis.Number(object.time_transacted) : 0,
      snapshot: isSet(object.snapshot) ? TransactionSnapshot.fromJSON(object.snapshot) : undefined,
      business_type: isSet(object.business_type) ? businessTypeFromJSON(object.business_type) : 0,
      currency_type_icon: isSet(object.currency_type_icon) ? globalThis.String(object.currency_type_icon) : '',
      origin_business_type: isSet(object.origin_business_type) ? globalThis.String(object.origin_business_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<TransactionDetail>, I>>(base?: I): TransactionDetail {
    return TransactionDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionDetail>, I>>(object: I): TransactionDetail {
    const message = createBaseTransactionDetail();
    message.id = object.id ?? 0;
    message.type = object.type ?? 0;
    message.title = object.title ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.balance = object.balance ?? 0;
    message.time_transacted = object.time_transacted ?? 0;
    message.snapshot =
      object.snapshot !== undefined && object.snapshot !== null
        ? TransactionSnapshot.fromPartial(object.snapshot)
        : undefined;
    message.business_type = object.business_type ?? 0;
    message.currency_type_icon = object.currency_type_icon ?? '';
    message.origin_business_type = object.origin_business_type ?? '';
    return message;
  }
};

function createBaseListTransactionDetailReq(): ListTransactionDetailReq {
  return {
    page: undefined,
    types: [],
    currency_types: [],
    min_amount: 0,
    max_amount: 0,
    min_time_transacted: 0,
    max_time_transacted: 0,
    business_type: 0,
    origin_business_type: ''
  };
}

export const ListTransactionDetailReq: MessageFns<ListTransactionDetailReq> = {
  fromJSON(object: any): ListTransactionDetailReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      types: globalThis.Array.isArray(object?.types) ? object.types.map((e: any) => transactionTypeFromJSON(e)) : [],
      currency_types: globalThis.Array.isArray(object?.currency_types)
        ? object.currency_types.map((e: any) => currencyTypeFromJSON(e))
        : [],
      min_amount: isSet(object.min_amount) ? globalThis.Number(object.min_amount) : 0,
      max_amount: isSet(object.max_amount) ? globalThis.Number(object.max_amount) : 0,
      min_time_transacted: isSet(object.min_time_transacted) ? globalThis.Number(object.min_time_transacted) : 0,
      max_time_transacted: isSet(object.max_time_transacted) ? globalThis.Number(object.max_time_transacted) : 0,
      business_type: isSet(object.business_type) ? businessTypeFromJSON(object.business_type) : 0,
      origin_business_type: isSet(object.origin_business_type) ? globalThis.String(object.origin_business_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListTransactionDetailReq>, I>>(base?: I): ListTransactionDetailReq {
    return ListTransactionDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionDetailReq>, I>>(object: I): ListTransactionDetailReq {
    const message = createBaseListTransactionDetailReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.types = object.types?.map(e => e) || [];
    message.currency_types = object.currency_types?.map(e => e) || [];
    message.min_amount = object.min_amount ?? 0;
    message.max_amount = object.max_amount ?? 0;
    message.min_time_transacted = object.min_time_transacted ?? 0;
    message.max_time_transacted = object.max_time_transacted ?? 0;
    message.business_type = object.business_type ?? 0;
    message.origin_business_type = object.origin_business_type ?? '';
    return message;
  }
};

function createBaseListTransactionDetailRsp(): ListTransactionDetailRsp {
  return { page: undefined, transaction_details: [] };
}

export const ListTransactionDetailRsp: MessageFns<ListTransactionDetailRsp> = {
  fromJSON(object: any): ListTransactionDetailRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      transaction_details: globalThis.Array.isArray(object?.transaction_details)
        ? object.transaction_details.map((e: any) => TransactionDetail.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListTransactionDetailRsp>, I>>(base?: I): ListTransactionDetailRsp {
    return ListTransactionDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionDetailRsp>, I>>(object: I): ListTransactionDetailRsp {
    const message = createBaseListTransactionDetailRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.transaction_details = object.transaction_details?.map(e => TransactionDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListTransactionSearchTypeReq(): ListTransactionSearchTypeReq {
  return { currency_type: 0 };
}

export const ListTransactionSearchTypeReq: MessageFns<ListTransactionSearchTypeReq> = {
  fromJSON(object: any): ListTransactionSearchTypeReq {
    return { currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0 };
  },

  create<I extends Exact<DeepPartial<ListTransactionSearchTypeReq>, I>>(base?: I): ListTransactionSearchTypeReq {
    return ListTransactionSearchTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionSearchTypeReq>, I>>(object: I): ListTransactionSearchTypeReq {
    const message = createBaseListTransactionSearchTypeReq();
    message.currency_type = object.currency_type ?? 0;
    return message;
  }
};

function createBaseListTransactionSearchTypeRsp(): ListTransactionSearchTypeRsp {
  return { list: [] };
}

export const ListTransactionSearchTypeRsp: MessageFns<ListTransactionSearchTypeRsp> = {
  fromJSON(object: any): ListTransactionSearchTypeRsp {
    return { list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => businessTypeFromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListTransactionSearchTypeRsp>, I>>(base?: I): ListTransactionSearchTypeRsp {
    return ListTransactionSearchTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionSearchTypeRsp>, I>>(object: I): ListTransactionSearchTypeRsp {
    const message = createBaseListTransactionSearchTypeRsp();
    message.list = object.list?.map(e => e) || [];
    return message;
  }
};

function createBaseTransferBalanceReq(): TransferBalanceReq {
  return {
    seq_id: '',
    to_uid: 0,
    currency_type: 0,
    amount: 0,
    business_type: '',
    sub_business_type: '',
    sub_business_type_text: ''
  };
}

export const TransferBalanceReq: MessageFns<TransferBalanceReq> = {
  fromJSON(object: any): TransferBalanceReq {
    return {
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      to_uid: isSet(object.to_uid) ? globalThis.Number(object.to_uid) : 0,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      business_type: isSet(object.business_type) ? globalThis.String(object.business_type) : '',
      sub_business_type: isSet(object.sub_business_type) ? globalThis.String(object.sub_business_type) : '',
      sub_business_type_text: isSet(object.sub_business_type_text)
        ? globalThis.String(object.sub_business_type_text)
        : ''
    };
  },

  create<I extends Exact<DeepPartial<TransferBalanceReq>, I>>(base?: I): TransferBalanceReq {
    return TransferBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransferBalanceReq>, I>>(object: I): TransferBalanceReq {
    const message = createBaseTransferBalanceReq();
    message.seq_id = object.seq_id ?? '';
    message.to_uid = object.to_uid ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.business_type = object.business_type ?? '';
    message.sub_business_type = object.sub_business_type ?? '';
    message.sub_business_type_text = object.sub_business_type_text ?? '';
    return message;
  }
};

function createBaseTransferBalanceRsp(): TransferBalanceRsp {
  return { balance: undefined };
}

export const TransferBalanceRsp: MessageFns<TransferBalanceRsp> = {
  fromJSON(object: any): TransferBalanceRsp {
    return { balance: isSet(object.balance) ? Balance.fromJSON(object.balance) : undefined };
  },

  create<I extends Exact<DeepPartial<TransferBalanceRsp>, I>>(base?: I): TransferBalanceRsp {
    return TransferBalanceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransferBalanceRsp>, I>>(object: I): TransferBalanceRsp {
    const message = createBaseTransferBalanceRsp();
    message.balance =
      object.balance !== undefined && object.balance !== null ? Balance.fromPartial(object.balance) : undefined;
    return message;
  }
};

function createBaseWalletWithdrawReq(): WalletWithdrawReq {
  return { currency_type: 0, amount: 0, withdraw_info: undefined };
}

export const WalletWithdrawReq: MessageFns<WalletWithdrawReq> = {
  fromJSON(object: any): WalletWithdrawReq {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      withdraw_info: isSet(object.withdraw_info) ? WithdrawInfo.fromJSON(object.withdraw_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<WalletWithdrawReq>, I>>(base?: I): WalletWithdrawReq {
    return WalletWithdrawReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WalletWithdrawReq>, I>>(object: I): WalletWithdrawReq {
    const message = createBaseWalletWithdrawReq();
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.withdraw_info =
      object.withdraw_info !== undefined && object.withdraw_info !== null
        ? WithdrawInfo.fromPartial(object.withdraw_info)
        : undefined;
    return message;
  }
};

function createBaseWalletWithdrawRsp(): WalletWithdrawRsp {
  return {};
}

export const WalletWithdrawRsp: MessageFns<WalletWithdrawRsp> = {
  fromJSON(_: any): WalletWithdrawRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<WalletWithdrawRsp>, I>>(base?: I): WalletWithdrawRsp {
    return WalletWithdrawRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WalletWithdrawRsp>, I>>(_: I): WalletWithdrawRsp {
    const message = createBaseWalletWithdrawRsp();
    return message;
  }
};

function createBaseWithdrawInfo(): WithdrawInfo {
  return { name: '', cou: '', phone: '', remark: '', expand: {} };
}

export const WithdrawInfo: MessageFns<WithdrawInfo> = {
  fromJSON(object: any): WithdrawInfo {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<WithdrawInfo>, I>>(base?: I): WithdrawInfo {
    return WithdrawInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawInfo>, I>>(object: I): WithdrawInfo {
    const message = createBaseWithdrawInfo();
    message.name = object.name ?? '';
    message.cou = object.cou ?? '';
    message.phone = object.phone ?? '';
    message.remark = object.remark ?? '';
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseWithdrawInfo_ExpandEntry(): WithdrawInfo_ExpandEntry {
  return { key: '', value: '' };
}

export const WithdrawInfo_ExpandEntry: MessageFns<WithdrawInfo_ExpandEntry> = {
  fromJSON(object: any): WithdrawInfo_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<WithdrawInfo_ExpandEntry>, I>>(base?: I): WithdrawInfo_ExpandEntry {
    return WithdrawInfo_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawInfo_ExpandEntry>, I>>(object: I): WithdrawInfo_ExpandEntry {
    const message = createBaseWithdrawInfo_ExpandEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseWalletCancelWithdrawReq(): WalletCancelWithdrawReq {
  return { id: 0 };
}

export const WalletCancelWithdrawReq: MessageFns<WalletCancelWithdrawReq> = {
  fromJSON(object: any): WalletCancelWithdrawReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<WalletCancelWithdrawReq>, I>>(base?: I): WalletCancelWithdrawReq {
    return WalletCancelWithdrawReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WalletCancelWithdrawReq>, I>>(object: I): WalletCancelWithdrawReq {
    const message = createBaseWalletCancelWithdrawReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseWalletCancelWithdrawRsp(): WalletCancelWithdrawRsp {
  return {};
}

export const WalletCancelWithdrawRsp: MessageFns<WalletCancelWithdrawRsp> = {
  fromJSON(_: any): WalletCancelWithdrawRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<WalletCancelWithdrawRsp>, I>>(base?: I): WalletCancelWithdrawRsp {
    return WalletCancelWithdrawRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WalletCancelWithdrawRsp>, I>>(_: I): WalletCancelWithdrawRsp {
    const message = createBaseWalletCancelWithdrawRsp();
    return message;
  }
};

function createBaseListWithdrawDetailReq(): ListWithdrawDetailReq {
  return { page: undefined, currency_type: 0 };
}

export const ListWithdrawDetailReq: MessageFns<ListWithdrawDetailReq> = {
  fromJSON(object: any): ListWithdrawDetailReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListWithdrawDetailReq>, I>>(base?: I): ListWithdrawDetailReq {
    return ListWithdrawDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListWithdrawDetailReq>, I>>(object: I): ListWithdrawDetailReq {
    const message = createBaseListWithdrawDetailReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.currency_type = object.currency_type ?? 0;
    return message;
  }
};

function createBaseListWithdrawDetailRsp(): ListWithdrawDetailRsp {
  return { page: undefined, withdraw_details: [] };
}

export const ListWithdrawDetailRsp: MessageFns<ListWithdrawDetailRsp> = {
  fromJSON(object: any): ListWithdrawDetailRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      withdraw_details: globalThis.Array.isArray(object?.withdraw_details)
        ? object.withdraw_details.map((e: any) => WithdrawDetail.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListWithdrawDetailRsp>, I>>(base?: I): ListWithdrawDetailRsp {
    return ListWithdrawDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListWithdrawDetailRsp>, I>>(object: I): ListWithdrawDetailRsp {
    const message = createBaseListWithdrawDetailRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.withdraw_details = object.withdraw_details?.map(e => WithdrawDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBaseWithdrawDetail(): WithdrawDetail {
  return { id: 0, amount: 0, withdraw_info: undefined, status: 0, create_at: 0, audit_info: undefined };
}

export const WithdrawDetail: MessageFns<WithdrawDetail> = {
  fromJSON(object: any): WithdrawDetail {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      withdraw_info: isSet(object.withdraw_info) ? WithdrawInfo.fromJSON(object.withdraw_info) : undefined,
      status: isSet(object.status) ? withdrawStatusFromJSON(object.status) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      audit_info: isSet(object.audit_info) ? WithdrawAuditInfo.fromJSON(object.audit_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<WithdrawDetail>, I>>(base?: I): WithdrawDetail {
    return WithdrawDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawDetail>, I>>(object: I): WithdrawDetail {
    const message = createBaseWithdrawDetail();
    message.id = object.id ?? 0;
    message.amount = object.amount ?? 0;
    message.withdraw_info =
      object.withdraw_info !== undefined && object.withdraw_info !== null
        ? WithdrawInfo.fromPartial(object.withdraw_info)
        : undefined;
    message.status = object.status ?? 0;
    message.create_at = object.create_at ?? 0;
    message.audit_info =
      object.audit_info !== undefined && object.audit_info !== null
        ? WithdrawAuditInfo.fromPartial(object.audit_info)
        : undefined;
    return message;
  }
};

function createBaseWithdrawAuditInfo(): WithdrawAuditInfo {
  return { remark: '' };
}

export const WithdrawAuditInfo: MessageFns<WithdrawAuditInfo> = {
  fromJSON(object: any): WithdrawAuditInfo {
    return { remark: isSet(object.remark) ? globalThis.String(object.remark) : '' };
  },

  create<I extends Exact<DeepPartial<WithdrawAuditInfo>, I>>(base?: I): WithdrawAuditInfo {
    return WithdrawAuditInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawAuditInfo>, I>>(object: I): WithdrawAuditInfo {
    const message = createBaseWithdrawAuditInfo();
    message.remark = object.remark ?? '';
    return message;
  }
};

export type WalletDefinition = typeof WalletDefinition;
export const WalletDefinition = {
  name: 'Wallet',
  fullName: 'pbrevenue.Wallet',
  methods: {
    /** 获取自己的余额, 支持多个币种同时查询. */
    getBalance: {
      name: 'GetBalance',
      requestType: GetBalanceReq,
      requestStream: false,
      responseType: GetBalanceRsp,
      responseStream: false,
      options: {}
    },
    /** 查询交易记录 */
    listTransactionDetail: {
      name: 'ListTransactionDetail',
      requestType: ListTransactionDetailReq,
      requestStream: false,
      responseType: ListTransactionDetailRsp,
      responseStream: false,
      options: {}
    },
    /** 获取交易记录类型,根据不同场景返回 */
    listTransactionSearchType: {
      name: 'ListTransactionSearchType',
      requestType: ListTransactionSearchTypeReq,
      requestStream: false,
      responseType: ListTransactionSearchTypeRsp,
      responseStream: false,
      options: {}
    },
    /** 转账 */
    transferBalance: {
      name: 'TransferBalance',
      requestType: TransferBalanceReq,
      requestStream: false,
      responseType: TransferBalanceRsp,
      responseStream: false,
      options: {}
    },
    /** 提现 */
    withdraw: {
      name: 'Withdraw',
      requestType: WalletWithdrawReq,
      requestStream: false,
      responseType: WalletWithdrawRsp,
      responseStream: false,
      options: {}
    },
    /** 取消提现 */
    cancelWithdraw: {
      name: 'CancelWithdraw',
      requestType: WalletCancelWithdrawReq,
      requestStream: false,
      responseType: WalletCancelWithdrawRsp,
      responseStream: false,
      options: {}
    },
    /** 查询提现记录 */
    listWithdrawDetail: {
      name: 'ListWithdrawDetail',
      requestType: ListWithdrawDetailReq,
      requestStream: false,
      responseType: ListWithdrawDetailRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
