// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/exchange.proto

/* eslint-disable */
import { Page } from '../common/common';
import { Balance, CurrencyType, currencyTypeFromJSON, UserInfo } from './common';

export const protobufPackage = 'pbrevenue';

/** 兑换汇率 */
export interface ExchangeRate {
  /** 兑换汇率ID */
  exchange_rate_id: number;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 可以获得多少数量货币 */
  target_quantity: number;
}

/** 兑换选项 */
export interface ExchangeOption {
  /** 兑换汇率ID */
  exchange_option_id: number;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 可以获得多少数量货币 */
  target_quantity: number;
}

/** 兑换币种 */
export interface ExchangeCurrency {
  /** 消耗币种 */
  currency_type: CurrencyType;
  /** 兑换汇率 */
  exchange_rates: ExchangeRate[];
  /** 兑换选项 */
  exchange_options: ExchangeOption[];
}

/** 获取兑换配置请求 */
export interface GetExchangeConfigReq {
  /** 需要获取的币种 */
  currency_types: CurrencyType[];
}

/** 获取兑换配置响应 */
export interface GetExchangeConfigRsp {
  /** 对应币种的兑换配置 */
  exchange_currencies: ExchangeCurrency[];
}

/** 兑换请求 */
export interface ExchangeReq {
  /** 兑换汇率ID, 根据汇率兑换时传 */
  exchange_rate_id: number;
  /** 兑换选项ID, 根据选项兑换时传 */
  exchange_option_id: number;
  /** 消耗币种, 根据汇率兑换时传 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币, 根据汇率兑换时传 */
  source_quantity: number;
  /** 给他人兑换，如果不传默认是自己与自己兑换 */
  target_uid: number;
}

/** 兑换响应 */
export interface ExchangeRsp {
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 获得多少数量货币 */
  target_quantity: number;
  /** 账户余额 */
  balances: Balance[];
}

export interface ExchangeRecordReq {
  page: Page | undefined;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
}

export interface ExchangeRecordDetail {
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 获得多少数量货币 */
  target_quantity: number;
  /** 消耗币种, 根据汇率兑换时传 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币, 根据汇率兑换时传 */
  source_quantity: number;
  /** 交易发生的时间戳, unix-timestamp 精确到秒. */
  time_transacted: number;
  /** 标题 */
  title: string;
}

export interface ExchangeRecordRsp {
  page: Page | undefined;
  /** 记录 */
  records: ExchangeRecordDetail[];
}

/** 预兑换请求 */
export interface PreExchangeReq {
  /** 兑换汇率ID */
  exchange_rate_id: number;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 预期消耗多少数量货币 */
  source_quantity: number;
}

/** 预兑换响应 */
export interface PreExchangeRsp {
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 实际消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 实际获得多少数量货币 */
  target_quantity: number;
}

/** 可兑换用户列表请求 */
export interface ListExchangeTargetReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
}

/** 可兑换用户列表响应 */
export interface ListExchangeTargetRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 充值对象列表 */
  list: ExchangeTargetUser[];
}

/** 兑换用户信息 */
export interface ExchangeTargetUser {
  /** 用户信息 */
  user: UserInfo | undefined;
  i18n_tag: { [key: string]: string };
  /** 可获得币种 */
  target_currency_types: CurrencyType[];
}

export interface ExchangeTargetUser_I18nTagEntry {
  key: string;
  value: string;
}

/** 获取单个兑换比例 */
export interface GetExchangeRateReq {
  /** 源货币类型 */
  source_currency_type: CurrencyType;
  /** 目标货币类型 */
  target_currency_type: CurrencyType;
}

/** 获取单个兑换比例 */
export interface GetExchangeRateRsp {
  /** 兑换比例配置, 如果不支持这两种货币兑换时, 需要注意: 这里返回是空! */
  exchange_rate: ExchangeRate | undefined;
}

function createBaseExchangeRate(): ExchangeRate {
  return {
    exchange_rate_id: 0,
    source_currency_type: 0,
    source_quantity: 0,
    target_currency_type: 0,
    target_quantity: 0
  };
}

export const ExchangeRate: MessageFns<ExchangeRate> = {
  fromJSON(object: any): ExchangeRate {
    return {
      exchange_rate_id: isSet(object.exchange_rate_id) ? globalThis.Number(object.exchange_rate_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRate>, I>>(base?: I): ExchangeRate {
    return ExchangeRate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRate>, I>>(object: I): ExchangeRate {
    const message = createBaseExchangeRate();
    message.exchange_rate_id = object.exchange_rate_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    return message;
  }
};

function createBaseExchangeOption(): ExchangeOption {
  return {
    exchange_option_id: 0,
    source_currency_type: 0,
    source_quantity: 0,
    target_currency_type: 0,
    target_quantity: 0
  };
}

export const ExchangeOption: MessageFns<ExchangeOption> = {
  fromJSON(object: any): ExchangeOption {
    return {
      exchange_option_id: isSet(object.exchange_option_id) ? globalThis.Number(object.exchange_option_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeOption>, I>>(base?: I): ExchangeOption {
    return ExchangeOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeOption>, I>>(object: I): ExchangeOption {
    const message = createBaseExchangeOption();
    message.exchange_option_id = object.exchange_option_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    return message;
  }
};

function createBaseExchangeCurrency(): ExchangeCurrency {
  return { currency_type: 0, exchange_rates: [], exchange_options: [] };
}

export const ExchangeCurrency: MessageFns<ExchangeCurrency> = {
  fromJSON(object: any): ExchangeCurrency {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      exchange_rates: globalThis.Array.isArray(object?.exchange_rates)
        ? object.exchange_rates.map((e: any) => ExchangeRate.fromJSON(e))
        : [],
      exchange_options: globalThis.Array.isArray(object?.exchange_options)
        ? object.exchange_options.map((e: any) => ExchangeOption.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ExchangeCurrency>, I>>(base?: I): ExchangeCurrency {
    return ExchangeCurrency.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeCurrency>, I>>(object: I): ExchangeCurrency {
    const message = createBaseExchangeCurrency();
    message.currency_type = object.currency_type ?? 0;
    message.exchange_rates = object.exchange_rates?.map(e => ExchangeRate.fromPartial(e)) || [];
    message.exchange_options = object.exchange_options?.map(e => ExchangeOption.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetExchangeConfigReq(): GetExchangeConfigReq {
  return { currency_types: [] };
}

export const GetExchangeConfigReq: MessageFns<GetExchangeConfigReq> = {
  fromJSON(object: any): GetExchangeConfigReq {
    return {
      currency_types: globalThis.Array.isArray(object?.currency_types)
        ? object.currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetExchangeConfigReq>, I>>(base?: I): GetExchangeConfigReq {
    return GetExchangeConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeConfigReq>, I>>(object: I): GetExchangeConfigReq {
    const message = createBaseGetExchangeConfigReq();
    message.currency_types = object.currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseGetExchangeConfigRsp(): GetExchangeConfigRsp {
  return { exchange_currencies: [] };
}

export const GetExchangeConfigRsp: MessageFns<GetExchangeConfigRsp> = {
  fromJSON(object: any): GetExchangeConfigRsp {
    return {
      exchange_currencies: globalThis.Array.isArray(object?.exchange_currencies)
        ? object.exchange_currencies.map((e: any) => ExchangeCurrency.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetExchangeConfigRsp>, I>>(base?: I): GetExchangeConfigRsp {
    return GetExchangeConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeConfigRsp>, I>>(object: I): GetExchangeConfigRsp {
    const message = createBaseGetExchangeConfigRsp();
    message.exchange_currencies = object.exchange_currencies?.map(e => ExchangeCurrency.fromPartial(e)) || [];
    return message;
  }
};

function createBaseExchangeReq(): ExchangeReq {
  return { exchange_rate_id: 0, exchange_option_id: 0, source_currency_type: 0, source_quantity: 0, target_uid: 0 };
}

export const ExchangeReq: MessageFns<ExchangeReq> = {
  fromJSON(object: any): ExchangeReq {
    return {
      exchange_rate_id: isSet(object.exchange_rate_id) ? globalThis.Number(object.exchange_rate_id) : 0,
      exchange_option_id: isSet(object.exchange_option_id) ? globalThis.Number(object.exchange_option_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeReq>, I>>(base?: I): ExchangeReq {
    return ExchangeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeReq>, I>>(object: I): ExchangeReq {
    const message = createBaseExchangeReq();
    message.exchange_rate_id = object.exchange_rate_id ?? 0;
    message.exchange_option_id = object.exchange_option_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_uid = object.target_uid ?? 0;
    return message;
  }
};

function createBaseExchangeRsp(): ExchangeRsp {
  return { target_currency_type: 0, target_quantity: 0, balances: [] };
}

export const ExchangeRsp: MessageFns<ExchangeRsp> = {
  fromJSON(object: any): ExchangeRsp {
    return {
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      balances: globalThis.Array.isArray(object?.balances) ? object.balances.map((e: any) => Balance.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRsp>, I>>(base?: I): ExchangeRsp {
    return ExchangeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRsp>, I>>(object: I): ExchangeRsp {
    const message = createBaseExchangeRsp();
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.balances = object.balances?.map(e => Balance.fromPartial(e)) || [];
    return message;
  }
};

function createBaseExchangeRecordReq(): ExchangeRecordReq {
  return { page: undefined, start_time: 0, end_time: 0 };
}

export const ExchangeRecordReq: MessageFns<ExchangeRecordReq> = {
  fromJSON(object: any): ExchangeRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRecordReq>, I>>(base?: I): ExchangeRecordReq {
    return ExchangeRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRecordReq>, I>>(object: I): ExchangeRecordReq {
    const message = createBaseExchangeRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBaseExchangeRecordDetail(): ExchangeRecordDetail {
  return {
    target_currency_type: 0,
    target_quantity: 0,
    source_currency_type: 0,
    source_quantity: 0,
    time_transacted: 0,
    title: ''
  };
}

export const ExchangeRecordDetail: MessageFns<ExchangeRecordDetail> = {
  fromJSON(object: any): ExchangeRecordDetail {
    return {
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      time_transacted: isSet(object.time_transacted) ? globalThis.Number(object.time_transacted) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : ''
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRecordDetail>, I>>(base?: I): ExchangeRecordDetail {
    return ExchangeRecordDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRecordDetail>, I>>(object: I): ExchangeRecordDetail {
    const message = createBaseExchangeRecordDetail();
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.time_transacted = object.time_transacted ?? 0;
    message.title = object.title ?? '';
    return message;
  }
};

function createBaseExchangeRecordRsp(): ExchangeRecordRsp {
  return { page: undefined, records: [] };
}

export const ExchangeRecordRsp: MessageFns<ExchangeRecordRsp> = {
  fromJSON(object: any): ExchangeRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      records: globalThis.Array.isArray(object?.records)
        ? object.records.map((e: any) => ExchangeRecordDetail.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRecordRsp>, I>>(base?: I): ExchangeRecordRsp {
    return ExchangeRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRecordRsp>, I>>(object: I): ExchangeRecordRsp {
    const message = createBaseExchangeRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.records = object.records?.map(e => ExchangeRecordDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBasePreExchangeReq(): PreExchangeReq {
  return { exchange_rate_id: 0, source_currency_type: 0, source_quantity: 0 };
}

export const PreExchangeReq: MessageFns<PreExchangeReq> = {
  fromJSON(object: any): PreExchangeReq {
    return {
      exchange_rate_id: isSet(object.exchange_rate_id) ? globalThis.Number(object.exchange_rate_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<PreExchangeReq>, I>>(base?: I): PreExchangeReq {
    return PreExchangeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PreExchangeReq>, I>>(object: I): PreExchangeReq {
    const message = createBasePreExchangeReq();
    message.exchange_rate_id = object.exchange_rate_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    return message;
  }
};

function createBasePreExchangeRsp(): PreExchangeRsp {
  return { source_currency_type: 0, source_quantity: 0, target_currency_type: 0, target_quantity: 0 };
}

export const PreExchangeRsp: MessageFns<PreExchangeRsp> = {
  fromJSON(object: any): PreExchangeRsp {
    return {
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<PreExchangeRsp>, I>>(base?: I): PreExchangeRsp {
    return PreExchangeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PreExchangeRsp>, I>>(object: I): PreExchangeRsp {
    const message = createBasePreExchangeRsp();
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    return message;
  }
};

function createBaseListExchangeTargetReq(): ListExchangeTargetReq {
  return { page: undefined, source_currency_type: 0 };
}

export const ListExchangeTargetReq: MessageFns<ListExchangeTargetReq> = {
  fromJSON(object: any): ListExchangeTargetReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeTargetReq>, I>>(base?: I): ListExchangeTargetReq {
    return ListExchangeTargetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeTargetReq>, I>>(object: I): ListExchangeTargetReq {
    const message = createBaseListExchangeTargetReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.source_currency_type = object.source_currency_type ?? 0;
    return message;
  }
};

function createBaseListExchangeTargetRsp(): ListExchangeTargetRsp {
  return { page: undefined, list: [] };
}

export const ListExchangeTargetRsp: MessageFns<ListExchangeTargetRsp> = {
  fromJSON(object: any): ListExchangeTargetRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ExchangeTargetUser.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeTargetRsp>, I>>(base?: I): ListExchangeTargetRsp {
    return ListExchangeTargetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeTargetRsp>, I>>(object: I): ListExchangeTargetRsp {
    const message = createBaseListExchangeTargetRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => ExchangeTargetUser.fromPartial(e)) || [];
    return message;
  }
};

function createBaseExchangeTargetUser(): ExchangeTargetUser {
  return { user: undefined, i18n_tag: {}, target_currency_types: [] };
}

export const ExchangeTargetUser: MessageFns<ExchangeTargetUser> = {
  fromJSON(object: any): ExchangeTargetUser {
    return {
      user: isSet(object.user) ? UserInfo.fromJSON(object.user) : undefined,
      i18n_tag: isObject(object.i18n_tag)
        ? Object.entries(object.i18n_tag).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      target_currency_types: globalThis.Array.isArray(object?.target_currency_types)
        ? object.target_currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ExchangeTargetUser>, I>>(base?: I): ExchangeTargetUser {
    return ExchangeTargetUser.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeTargetUser>, I>>(object: I): ExchangeTargetUser {
    const message = createBaseExchangeTargetUser();
    message.user = object.user !== undefined && object.user !== null ? UserInfo.fromPartial(object.user) : undefined;
    message.i18n_tag = Object.entries(object.i18n_tag ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.target_currency_types = object.target_currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseExchangeTargetUser_I18nTagEntry(): ExchangeTargetUser_I18nTagEntry {
  return { key: '', value: '' };
}

export const ExchangeTargetUser_I18nTagEntry: MessageFns<ExchangeTargetUser_I18nTagEntry> = {
  fromJSON(object: any): ExchangeTargetUser_I18nTagEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ExchangeTargetUser_I18nTagEntry>, I>>(base?: I): ExchangeTargetUser_I18nTagEntry {
    return ExchangeTargetUser_I18nTagEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeTargetUser_I18nTagEntry>, I>>(
    object: I
  ): ExchangeTargetUser_I18nTagEntry {
    const message = createBaseExchangeTargetUser_I18nTagEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetExchangeRateReq(): GetExchangeRateReq {
  return { source_currency_type: 0, target_currency_type: 0 };
}

export const GetExchangeRateReq: MessageFns<GetExchangeRateReq> = {
  fromJSON(object: any): GetExchangeRateReq {
    return {
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetExchangeRateReq>, I>>(base?: I): GetExchangeRateReq {
    return GetExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRateReq>, I>>(object: I): GetExchangeRateReq {
    const message = createBaseGetExchangeRateReq();
    message.source_currency_type = object.source_currency_type ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    return message;
  }
};

function createBaseGetExchangeRateRsp(): GetExchangeRateRsp {
  return { exchange_rate: undefined };
}

export const GetExchangeRateRsp: MessageFns<GetExchangeRateRsp> = {
  fromJSON(object: any): GetExchangeRateRsp {
    return { exchange_rate: isSet(object.exchange_rate) ? ExchangeRate.fromJSON(object.exchange_rate) : undefined };
  },

  create<I extends Exact<DeepPartial<GetExchangeRateRsp>, I>>(base?: I): GetExchangeRateRsp {
    return GetExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRateRsp>, I>>(object: I): GetExchangeRateRsp {
    const message = createBaseGetExchangeRateRsp();
    message.exchange_rate =
      object.exchange_rate !== undefined && object.exchange_rate !== null
        ? ExchangeRate.fromPartial(object.exchange_rate)
        : undefined;
    return message;
  }
};

/** 货币兑换服务 */
export type ExchangeDefinition = typeof ExchangeDefinition;
export const ExchangeDefinition = {
  name: 'Exchange',
  fullName: 'pbrevenue.Exchange',
  methods: {
    /** 获取兑换配置 */
    getExchangeConfig: {
      name: 'GetExchangeConfig',
      requestType: GetExchangeConfigReq,
      requestStream: false,
      responseType: GetExchangeConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 兑换 */
    exchange: {
      name: 'Exchange',
      requestType: ExchangeReq,
      requestStream: false,
      responseType: ExchangeRsp,
      responseStream: false,
      options: {}
    },
    /** 兑换记录 */
    exchangeRecord: {
      name: 'ExchangeRecord',
      requestType: ExchangeRecordReq,
      requestStream: false,
      responseType: ExchangeRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 预兑换 */
    preExchange: {
      name: 'PreExchange',
      requestType: PreExchangeReq,
      requestStream: false,
      responseType: PreExchangeRsp,
      responseStream: false,
      options: {}
    },
    /** 获取可兑换的用户列表（与他人兑换） */
    listExchangeTarget: {
      name: 'ListExchangeTarget',
      requestType: ListExchangeTargetReq,
      requestStream: false,
      responseType: ListExchangeTargetRsp,
      responseStream: false,
      options: {}
    },
    /** 获取单个兑换比率 */
    getExchangeRate: {
      name: 'GetExchangeRate',
      requestType: GetExchangeRateReq,
      requestStream: false,
      responseType: GetExchangeRateRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
