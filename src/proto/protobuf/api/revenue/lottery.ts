// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/lottery.proto

/* eslint-disable */
import { CurrencyType, currencyTypeFromJSON } from './common';

export const protobufPackage = 'pbrevenue';

/** 抽奖类型 */
export enum LotteryType {
  LOTTERY_TYPE_NONE = 0,
  /** LOTTERY_TYPE_MYSTERY_GIFT - 盲盒礼物 */
  LOTTERY_TYPE_MYSTERY_GIFT = 10,
  /** LOTTERY_TYPE_LUCKY_GIFT - 幸运礼物 */
  LOTTERY_TYPE_LUCKY_GIFT = 20,
  UNRECOGNIZED = -1
}

export function lotteryTypeFromJSON(object: any): LotteryType {
  switch (object) {
    case 0:
    case 'LOTTERY_TYPE_NONE':
      return LotteryType.LOTTERY_TYPE_NONE;
    case 10:
    case 'LOTTERY_TYPE_MYSTERY_GIFT':
      return LotteryType.LOTTERY_TYPE_MYSTERY_GIFT;
    case 20:
    case 'LOTTERY_TYPE_LUCKY_GIFT':
      return LotteryType.LOTTERY_TYPE_LUCKY_GIFT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LotteryType.UNRECOGNIZED;
  }
}

/** 奖品类型 */
export enum PrizeType {
  PRIZE_TYPE_NONE = 0,
  /** PRIZE_TYPE_GIFT - 礼物, prize_value 是一个礼物ID */
  PRIZE_TYPE_GIFT = 10,
  /** PRIZE_TYPE_CURRENCY - 货币, prize_value 是货币数量 */
  PRIZE_TYPE_CURRENCY = 20,
  UNRECOGNIZED = -1
}

export function prizeTypeFromJSON(object: any): PrizeType {
  switch (object) {
    case 0:
    case 'PRIZE_TYPE_NONE':
      return PrizeType.PRIZE_TYPE_NONE;
    case 10:
    case 'PRIZE_TYPE_GIFT':
      return PrizeType.PRIZE_TYPE_GIFT;
    case 20:
    case 'PRIZE_TYPE_CURRENCY':
      return PrizeType.PRIZE_TYPE_CURRENCY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PrizeType.UNRECOGNIZED;
  }
}

export interface LotteryInfo {
  id: number;
  /** 默认名称 */
  name: string;
  /** 国际化名称 */
  i18n_names: { [key: string]: string };
  icon: string;
  lottery_type: LotteryType;
  /** 跳转链接 */
  jump_url: string;
}

export interface LotteryInfo_I18nNamesEntry {
  key: string;
  value: string;
}

/** 奖励信息 */
export interface PrizeInfo {
  id: number;
  /** 默认名称 */
  name: string;
  /** 国际化名称 */
  i18n_names: { [key: string]: string };
  icon: string;
  prize_type: PrizeType;
  currency_type: CurrencyType;
  price: number;
}

export interface PrizeInfo_I18nNamesEntry {
  key: string;
  value: string;
}

export interface GetLotteryTopPrizeReq {
  lottery_id: number;
}

export interface GetLotteryTopPrizeRsp {
  lottery: LotteryInfo | undefined;
  top_prize: PrizeInfo | undefined;
}

export interface GetLotteryAllPrizeReq {
  lottery_id: number;
}

export interface GetLotteryAllPrizeRsp {
  lottery: LotteryInfo | undefined;
  prizes: PrizeInfo[];
}

function createBaseLotteryInfo(): LotteryInfo {
  return { id: 0, name: '', i18n_names: {}, icon: '', lottery_type: 0, jump_url: '' };
}

export const LotteryInfo: MessageFns<LotteryInfo> = {
  fromJSON(object: any): LotteryInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      lottery_type: isSet(object.lottery_type) ? lotteryTypeFromJSON(object.lottery_type) : 0,
      jump_url: isSet(object.jump_url) ? globalThis.String(object.jump_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryInfo>, I>>(base?: I): LotteryInfo {
    return LotteryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryInfo>, I>>(object: I): LotteryInfo {
    const message = createBaseLotteryInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.icon = object.icon ?? '';
    message.lottery_type = object.lottery_type ?? 0;
    message.jump_url = object.jump_url ?? '';
    return message;
  }
};

function createBaseLotteryInfo_I18nNamesEntry(): LotteryInfo_I18nNamesEntry {
  return { key: '', value: '' };
}

export const LotteryInfo_I18nNamesEntry: MessageFns<LotteryInfo_I18nNamesEntry> = {
  fromJSON(object: any): LotteryInfo_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryInfo_I18nNamesEntry>, I>>(base?: I): LotteryInfo_I18nNamesEntry {
    return LotteryInfo_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryInfo_I18nNamesEntry>, I>>(object: I): LotteryInfo_I18nNamesEntry {
    const message = createBaseLotteryInfo_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePrizeInfo(): PrizeInfo {
  return { id: 0, name: '', i18n_names: {}, icon: '', prize_type: 0, currency_type: 0, price: 0 };
}

export const PrizeInfo: MessageFns<PrizeInfo> = {
  fromJSON(object: any): PrizeInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      prize_type: isSet(object.prize_type) ? prizeTypeFromJSON(object.prize_type) : 0,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizeInfo>, I>>(base?: I): PrizeInfo {
    return PrizeInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizeInfo>, I>>(object: I): PrizeInfo {
    const message = createBasePrizeInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.icon = object.icon ?? '';
    message.prize_type = object.prize_type ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.price = object.price ?? 0;
    return message;
  }
};

function createBasePrizeInfo_I18nNamesEntry(): PrizeInfo_I18nNamesEntry {
  return { key: '', value: '' };
}

export const PrizeInfo_I18nNamesEntry: MessageFns<PrizeInfo_I18nNamesEntry> = {
  fromJSON(object: any): PrizeInfo_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PrizeInfo_I18nNamesEntry>, I>>(base?: I): PrizeInfo_I18nNamesEntry {
    return PrizeInfo_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizeInfo_I18nNamesEntry>, I>>(object: I): PrizeInfo_I18nNamesEntry {
    const message = createBasePrizeInfo_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetLotteryTopPrizeReq(): GetLotteryTopPrizeReq {
  return { lottery_id: 0 };
}

export const GetLotteryTopPrizeReq: MessageFns<GetLotteryTopPrizeReq> = {
  fromJSON(object: any): GetLotteryTopPrizeReq {
    return { lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetLotteryTopPrizeReq>, I>>(base?: I): GetLotteryTopPrizeReq {
    return GetLotteryTopPrizeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryTopPrizeReq>, I>>(object: I): GetLotteryTopPrizeReq {
    const message = createBaseGetLotteryTopPrizeReq();
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseGetLotteryTopPrizeRsp(): GetLotteryTopPrizeRsp {
  return { lottery: undefined, top_prize: undefined };
}

export const GetLotteryTopPrizeRsp: MessageFns<GetLotteryTopPrizeRsp> = {
  fromJSON(object: any): GetLotteryTopPrizeRsp {
    return {
      lottery: isSet(object.lottery) ? LotteryInfo.fromJSON(object.lottery) : undefined,
      top_prize: isSet(object.top_prize) ? PrizeInfo.fromJSON(object.top_prize) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetLotteryTopPrizeRsp>, I>>(base?: I): GetLotteryTopPrizeRsp {
    return GetLotteryTopPrizeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryTopPrizeRsp>, I>>(object: I): GetLotteryTopPrizeRsp {
    const message = createBaseGetLotteryTopPrizeRsp();
    message.lottery =
      object.lottery !== undefined && object.lottery !== null ? LotteryInfo.fromPartial(object.lottery) : undefined;
    message.top_prize =
      object.top_prize !== undefined && object.top_prize !== null ? PrizeInfo.fromPartial(object.top_prize) : undefined;
    return message;
  }
};

function createBaseGetLotteryAllPrizeReq(): GetLotteryAllPrizeReq {
  return { lottery_id: 0 };
}

export const GetLotteryAllPrizeReq: MessageFns<GetLotteryAllPrizeReq> = {
  fromJSON(object: any): GetLotteryAllPrizeReq {
    return { lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetLotteryAllPrizeReq>, I>>(base?: I): GetLotteryAllPrizeReq {
    return GetLotteryAllPrizeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryAllPrizeReq>, I>>(object: I): GetLotteryAllPrizeReq {
    const message = createBaseGetLotteryAllPrizeReq();
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseGetLotteryAllPrizeRsp(): GetLotteryAllPrizeRsp {
  return { lottery: undefined, prizes: [] };
}

export const GetLotteryAllPrizeRsp: MessageFns<GetLotteryAllPrizeRsp> = {
  fromJSON(object: any): GetLotteryAllPrizeRsp {
    return {
      lottery: isSet(object.lottery) ? LotteryInfo.fromJSON(object.lottery) : undefined,
      prizes: globalThis.Array.isArray(object?.prizes) ? object.prizes.map((e: any) => PrizeInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetLotteryAllPrizeRsp>, I>>(base?: I): GetLotteryAllPrizeRsp {
    return GetLotteryAllPrizeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryAllPrizeRsp>, I>>(object: I): GetLotteryAllPrizeRsp {
    const message = createBaseGetLotteryAllPrizeRsp();
    message.lottery =
      object.lottery !== undefined && object.lottery !== null ? LotteryInfo.fromPartial(object.lottery) : undefined;
    message.prizes = object.prizes?.map(e => PrizeInfo.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 抽奖
 * ServiceName: api.micro.social.revenue
 * smicro:spath=gitit.cc/social/components-service/social-revenue/lottery/handler
 */
export type LotteryDefinition = typeof LotteryDefinition;
export const LotteryDefinition = {
  name: 'Lottery',
  fullName: 'pbrevenue.Lottery',
  methods: {
    /** 获取抽奖的最高奖励 */
    getLotteryTopPrize: {
      name: 'GetLotteryTopPrize',
      requestType: GetLotteryTopPrizeReq,
      requestStream: false,
      responseType: GetLotteryTopPrizeRsp,
      responseStream: false,
      options: {}
    },
    /** 获取抽奖的所有奖励 */
    getLotteryAllPrize: {
      name: 'GetLotteryAllPrize',
      requestType: GetLotteryAllPrizeReq,
      requestStream: false,
      responseType: GetLotteryAllPrizeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
