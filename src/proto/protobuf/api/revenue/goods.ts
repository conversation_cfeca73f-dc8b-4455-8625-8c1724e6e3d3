// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/goods.proto

/* eslint-disable */
import { Page } from '../common/common';
import { ClientType, clientTypeFromJSON, GoodsInfo, GoodsOrderInfo, PayStatus, payStatusFromJSON } from './common';

export const protobufPackage = 'pbrevenue';

export interface ListGoodsReq {
  /** 非必须，h5 quarkpay 支付需要传 */
  cou: string;
  /** 支付方式 google_pay apple_pay huaweipay quarkpay 等，默认根据客户端类型判断，h5 根据实际支付渠道传 */
  pay_type: string;
  /** 非必须，quarkpay 支付需要传 CARD（银行卡），STCPAY（钱包），参考中台支付接口定义，https://nemo.yuque.com/be/mmszip/mb0hzzhhgr1zhy98#qqPCn */
  pay_code: string;
  /** h5必传，客户端类型，不传则使用公参 */
  client_type: ClientType;
  /** 非必须，充值用户ID，默认取当前登录用户ID，给别人充值场景传 */
  uid: number;
}

export interface ListGoodsRsp {
  /** 商品（SKU）列表 */
  goods: GoodsInfo[];
  /** 第三方充值url */
  third_party_recharge_url: string;
}

export interface CreateOrderReq {
  /** 商品ID */
  goods_id: number;
  /** 非必须，购买数量，默认1，系统扩展预留 */
  num: number;
  /** 支付方式，如：huaweipay google_play apple_pay quarkpay */
  pay_type: string;
  /** sdk 支付 */
  order_by_sdk: boolean;
  /** 可选，用户手机号 */
  phone_number: string;
  /** 支付渠道，由支付方传入，不超过30个字符 */
  channel_id: string;
  /** 非必须，透传给业务 */
  ext: string;
  /** 非必须，quarkpay 支付需要传，选择国家编码 */
  location: string;
  /** 非必须，quarkpay 支付需要传，选择语言编码 */
  language: string;
  /** 非必须，quarkpay 支付需要传，支付成功后跳转的页面地址， */
  success_url: string;
  /** 非必须，quarkpay 支付需要传，支付方式编码 */
  pay_code: string;
  /** 非必须，充值用户ID，默认取当前登录用户ID，给别人充值场景传 */
  uid: number;
}

export interface CreateOrderRsp {
  /** 订单信息 */
  order_info: OrderInfo | undefined;
  /** 非必须，非sdk支付返回，中台创建订单返回结果，因中台接口返回内容字段不统一，且为了保持后续扩展性，直接使用json字符串，具体查看中台下单接口https://nemo.yuque.com/pay_system/interface/haaq01#415E8 */
  pay_order_json_result: string;
}

export interface OrderInfo {
  /** 订单号 */
  order_no: string;
  /** 订单名称 */
  order_name: string;
  /** 支付中心参数 */
  mid: string;
  /** 客户端类型 */
  client_type: string;
  /** 签名 */
  checksum: string;
  /** 需要支付的金额（分） */
  pay_amount: number;
  /** 支付金额单位，如：USD */
  unit: string;
  /** 支付方式 */
  pay_type: string;
  /** 可选，支付渠道ID */
  channel_id: string;
  /** 可选，回调url，通过此地址来通知付款结果（如果不填需要联系研发在后台配置回调地址） */
  callback_url: string;
  /** 可选，支付完成后跳转的url，仅web支付使用 */
  return_url: string;
  /** 购买商品信息 */
  goods_info: GoodsInfo | undefined;
  /** 购买数量，默认1，系统扩展预留 */
  num: number;
  /** 换算成美元单位 */
  pay_amount_dollar: string;
}

export interface CheckOrderReq {
  /** 订单ID */
  order_no: string;
}

export interface CheckOrderRsp {
  /** 支付状态 */
  status: PayStatus;
  /** 充值后钱包金币数 */
  coin: number;
  /** 当比充值获得金币 */
  recharge_coin: number;
}

export interface PageUserOrderReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 支付方式 */
  pay_type: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
}

export interface PageUserOrderRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 充值列表 */
  list: GoodsOrderInfo[];
}

export interface ListPayInfoReq {
  /** 可选，国家编码，不传则查询所有国家 */
  cou: string;
  /** 可选，支付方式，如：quarkpay，不传则是所有支付方式 */
  pay_type: string;
  /** 必传，客户端类型 */
  client_type: ClientType;
  /** 非必须，充值用户ID，默认取当前登录用户ID，给别人充值场景传 */
  uid: number;
}

export interface ListPayInfoRsp {
  list: PayInfo[];
}

export interface PayInfo {
  /** 国家 */
  cou: string;
  /** 支付方式 */
  pay_type: string;
  /** 支付code，如：CARD（银行卡），STCPAY（钱包），参考中台支付接口定义，https://nemo.yuque.com/be/mmszip/mb0hzzhhgr1zhy98#qqPCn */
  pay_code: string;
  /** 支付code对应的图片 */
  pay_code_img: string;
  /** 支付code对应的名称 */
  pay_code_name: string;
}

function createBaseListGoodsReq(): ListGoodsReq {
  return { cou: '', pay_type: '', pay_code: '', client_type: 0, uid: 0 };
}

export const ListGoodsReq: MessageFns<ListGoodsReq> = {
  fromJSON(object: any): ListGoodsReq {
    return {
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      client_type: isSet(object.client_type) ? clientTypeFromJSON(object.client_type) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListGoodsReq>, I>>(base?: I): ListGoodsReq {
    return ListGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoodsReq>, I>>(object: I): ListGoodsReq {
    const message = createBaseListGoodsReq();
    message.cou = object.cou ?? '';
    message.pay_type = object.pay_type ?? '';
    message.pay_code = object.pay_code ?? '';
    message.client_type = object.client_type ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseListGoodsRsp(): ListGoodsRsp {
  return { goods: [], third_party_recharge_url: '' };
}

export const ListGoodsRsp: MessageFns<ListGoodsRsp> = {
  fromJSON(object: any): ListGoodsRsp {
    return {
      goods: globalThis.Array.isArray(object?.goods) ? object.goods.map((e: any) => GoodsInfo.fromJSON(e)) : [],
      third_party_recharge_url: isSet(object.third_party_recharge_url)
        ? globalThis.String(object.third_party_recharge_url)
        : ''
    };
  },

  create<I extends Exact<DeepPartial<ListGoodsRsp>, I>>(base?: I): ListGoodsRsp {
    return ListGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoodsRsp>, I>>(object: I): ListGoodsRsp {
    const message = createBaseListGoodsRsp();
    message.goods = object.goods?.map(e => GoodsInfo.fromPartial(e)) || [];
    message.third_party_recharge_url = object.third_party_recharge_url ?? '';
    return message;
  }
};

function createBaseCreateOrderReq(): CreateOrderReq {
  return {
    goods_id: 0,
    num: 0,
    pay_type: '',
    order_by_sdk: false,
    phone_number: '',
    channel_id: '',
    ext: '',
    location: '',
    language: '',
    success_url: '',
    pay_code: '',
    uid: 0
  };
}

export const CreateOrderReq: MessageFns<CreateOrderReq> = {
  fromJSON(object: any): CreateOrderReq {
    return {
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      order_by_sdk: isSet(object.order_by_sdk) ? globalThis.Boolean(object.order_by_sdk) : false,
      phone_number: isSet(object.phone_number) ? globalThis.String(object.phone_number) : '',
      channel_id: isSet(object.channel_id) ? globalThis.String(object.channel_id) : '',
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      location: isSet(object.location) ? globalThis.String(object.location) : '',
      language: isSet(object.language) ? globalThis.String(object.language) : '',
      success_url: isSet(object.success_url) ? globalThis.String(object.success_url) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<CreateOrderReq>, I>>(base?: I): CreateOrderReq {
    return CreateOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateOrderReq>, I>>(object: I): CreateOrderReq {
    const message = createBaseCreateOrderReq();
    message.goods_id = object.goods_id ?? 0;
    message.num = object.num ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.order_by_sdk = object.order_by_sdk ?? false;
    message.phone_number = object.phone_number ?? '';
    message.channel_id = object.channel_id ?? '';
    message.ext = object.ext ?? '';
    message.location = object.location ?? '';
    message.language = object.language ?? '';
    message.success_url = object.success_url ?? '';
    message.pay_code = object.pay_code ?? '';
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseCreateOrderRsp(): CreateOrderRsp {
  return { order_info: undefined, pay_order_json_result: '' };
}

export const CreateOrderRsp: MessageFns<CreateOrderRsp> = {
  fromJSON(object: any): CreateOrderRsp {
    return {
      order_info: isSet(object.order_info) ? OrderInfo.fromJSON(object.order_info) : undefined,
      pay_order_json_result: isSet(object.pay_order_json_result) ? globalThis.String(object.pay_order_json_result) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateOrderRsp>, I>>(base?: I): CreateOrderRsp {
    return CreateOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateOrderRsp>, I>>(object: I): CreateOrderRsp {
    const message = createBaseCreateOrderRsp();
    message.order_info =
      object.order_info !== undefined && object.order_info !== null
        ? OrderInfo.fromPartial(object.order_info)
        : undefined;
    message.pay_order_json_result = object.pay_order_json_result ?? '';
    return message;
  }
};

function createBaseOrderInfo(): OrderInfo {
  return {
    order_no: '',
    order_name: '',
    mid: '',
    client_type: '',
    checksum: '',
    pay_amount: 0,
    unit: '',
    pay_type: '',
    channel_id: '',
    callback_url: '',
    return_url: '',
    goods_info: undefined,
    num: 0,
    pay_amount_dollar: ''
  };
}

export const OrderInfo: MessageFns<OrderInfo> = {
  fromJSON(object: any): OrderInfo {
    return {
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      order_name: isSet(object.order_name) ? globalThis.String(object.order_name) : '',
      mid: isSet(object.mid) ? globalThis.String(object.mid) : '',
      client_type: isSet(object.client_type) ? globalThis.String(object.client_type) : '',
      checksum: isSet(object.checksum) ? globalThis.String(object.checksum) : '',
      pay_amount: isSet(object.pay_amount) ? globalThis.Number(object.pay_amount) : 0,
      unit: isSet(object.unit) ? globalThis.String(object.unit) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      channel_id: isSet(object.channel_id) ? globalThis.String(object.channel_id) : '',
      callback_url: isSet(object.callback_url) ? globalThis.String(object.callback_url) : '',
      return_url: isSet(object.return_url) ? globalThis.String(object.return_url) : '',
      goods_info: isSet(object.goods_info) ? GoodsInfo.fromJSON(object.goods_info) : undefined,
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      pay_amount_dollar: isSet(object.pay_amount_dollar) ? globalThis.String(object.pay_amount_dollar) : ''
    };
  },

  create<I extends Exact<DeepPartial<OrderInfo>, I>>(base?: I): OrderInfo {
    return OrderInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OrderInfo>, I>>(object: I): OrderInfo {
    const message = createBaseOrderInfo();
    message.order_no = object.order_no ?? '';
    message.order_name = object.order_name ?? '';
    message.mid = object.mid ?? '';
    message.client_type = object.client_type ?? '';
    message.checksum = object.checksum ?? '';
    message.pay_amount = object.pay_amount ?? 0;
    message.unit = object.unit ?? '';
    message.pay_type = object.pay_type ?? '';
    message.channel_id = object.channel_id ?? '';
    message.callback_url = object.callback_url ?? '';
    message.return_url = object.return_url ?? '';
    message.goods_info =
      object.goods_info !== undefined && object.goods_info !== null
        ? GoodsInfo.fromPartial(object.goods_info)
        : undefined;
    message.num = object.num ?? 0;
    message.pay_amount_dollar = object.pay_amount_dollar ?? '';
    return message;
  }
};

function createBaseCheckOrderReq(): CheckOrderReq {
  return { order_no: '' };
}

export const CheckOrderReq: MessageFns<CheckOrderReq> = {
  fromJSON(object: any): CheckOrderReq {
    return { order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '' };
  },

  create<I extends Exact<DeepPartial<CheckOrderReq>, I>>(base?: I): CheckOrderReq {
    return CheckOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckOrderReq>, I>>(object: I): CheckOrderReq {
    const message = createBaseCheckOrderReq();
    message.order_no = object.order_no ?? '';
    return message;
  }
};

function createBaseCheckOrderRsp(): CheckOrderRsp {
  return { status: 0, coin: 0, recharge_coin: 0 };
}

export const CheckOrderRsp: MessageFns<CheckOrderRsp> = {
  fromJSON(object: any): CheckOrderRsp {
    return {
      status: isSet(object.status) ? payStatusFromJSON(object.status) : 0,
      coin: isSet(object.coin) ? globalThis.Number(object.coin) : 0,
      recharge_coin: isSet(object.recharge_coin) ? globalThis.Number(object.recharge_coin) : 0
    };
  },

  create<I extends Exact<DeepPartial<CheckOrderRsp>, I>>(base?: I): CheckOrderRsp {
    return CheckOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckOrderRsp>, I>>(object: I): CheckOrderRsp {
    const message = createBaseCheckOrderRsp();
    message.status = object.status ?? 0;
    message.coin = object.coin ?? 0;
    message.recharge_coin = object.recharge_coin ?? 0;
    return message;
  }
};

function createBasePageUserOrderReq(): PageUserOrderReq {
  return { page: undefined, pay_type: '', start_time: 0, end_time: 0 };
}

export const PageUserOrderReq: MessageFns<PageUserOrderReq> = {
  fromJSON(object: any): PageUserOrderReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<PageUserOrderReq>, I>>(base?: I): PageUserOrderReq {
    return PageUserOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageUserOrderReq>, I>>(object: I): PageUserOrderReq {
    const message = createBasePageUserOrderReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.pay_type = object.pay_type ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBasePageUserOrderRsp(): PageUserOrderRsp {
  return { page: undefined, list: [] };
}

export const PageUserOrderRsp: MessageFns<PageUserOrderRsp> = {
  fromJSON(object: any): PageUserOrderRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GoodsOrderInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<PageUserOrderRsp>, I>>(base?: I): PageUserOrderRsp {
    return PageUserOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageUserOrderRsp>, I>>(object: I): PageUserOrderRsp {
    const message = createBasePageUserOrderRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GoodsOrderInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListPayInfoReq(): ListPayInfoReq {
  return { cou: '', pay_type: '', client_type: 0, uid: 0 };
}

export const ListPayInfoReq: MessageFns<ListPayInfoReq> = {
  fromJSON(object: any): ListPayInfoReq {
    return {
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      client_type: isSet(object.client_type) ? clientTypeFromJSON(object.client_type) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListPayInfoReq>, I>>(base?: I): ListPayInfoReq {
    return ListPayInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPayInfoReq>, I>>(object: I): ListPayInfoReq {
    const message = createBaseListPayInfoReq();
    message.cou = object.cou ?? '';
    message.pay_type = object.pay_type ?? '';
    message.client_type = object.client_type ?? 0;
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseListPayInfoRsp(): ListPayInfoRsp {
  return { list: [] };
}

export const ListPayInfoRsp: MessageFns<ListPayInfoRsp> = {
  fromJSON(object: any): ListPayInfoRsp {
    return { list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => PayInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListPayInfoRsp>, I>>(base?: I): ListPayInfoRsp {
    return ListPayInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPayInfoRsp>, I>>(object: I): ListPayInfoRsp {
    const message = createBaseListPayInfoRsp();
    message.list = object.list?.map(e => PayInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBasePayInfo(): PayInfo {
  return { cou: '', pay_type: '', pay_code: '', pay_code_img: '', pay_code_name: '' };
}

export const PayInfo: MessageFns<PayInfo> = {
  fromJSON(object: any): PayInfo {
    return {
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      pay_code_img: isSet(object.pay_code_img) ? globalThis.String(object.pay_code_img) : '',
      pay_code_name: isSet(object.pay_code_name) ? globalThis.String(object.pay_code_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayInfo>, I>>(base?: I): PayInfo {
    return PayInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayInfo>, I>>(object: I): PayInfo {
    const message = createBasePayInfo();
    message.cou = object.cou ?? '';
    message.pay_type = object.pay_type ?? '';
    message.pay_code = object.pay_code ?? '';
    message.pay_code_img = object.pay_code_img ?? '';
    message.pay_code_name = object.pay_code_name ?? '';
    return message;
  }
};

/**
 * 支付sdk已经升级到pb协议，支付相关协议迁移到 https://gitit.cc/social/protocol/api/pay
 * 该协议暂时保留，仅提供carne旧版本使用
 */
export type GoodsDefinition = typeof GoodsDefinition;
export const GoodsDefinition = {
  name: 'Goods',
  fullName: 'pbrevenue.Goods',
  methods: {
    /** 获取商品列表 */
    listGoods: {
      name: 'ListGoods',
      requestType: ListGoodsReq,
      requestStream: false,
      responseType: ListGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 创单接口 */
    createOrder: {
      name: 'CreateOrder',
      requestType: CreateOrderReq,
      requestStream: false,
      responseType: CreateOrderRsp,
      responseStream: false,
      options: {}
    },
    /** 校验订单接口 */
    checkOrder: {
      name: 'CheckOrder',
      requestType: CheckOrderReq,
      requestStream: false,
      responseType: CheckOrderRsp,
      responseStream: false,
      options: {}
    },
    /** 用户充值订单分页查询 */
    pageUserOrder: {
      name: 'PageUserOrder',
      requestType: PageUserOrderReq,
      requestStream: false,
      responseType: PageUserOrderRsp,
      responseStream: false,
      options: {}
    },
    /** 支持的支付方式列表查询 */
    listPayInfo: {
      name: 'ListPayInfo',
      requestType: ListPayInfoReq,
      requestStream: false,
      responseType: ListPayInfoRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
