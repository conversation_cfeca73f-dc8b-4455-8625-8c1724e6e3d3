// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/gift.proto

/* eslint-disable */
import { Page } from '../common/common';
import {
  Balance,
  CurrencyType,
  currencyTypeFromJSON,
  JumpType,
  jumpTypeFromJSON,
  UserInfo,
  WishGiftType,
  wishGiftTypeFromJSON
} from './common';

export const protobufPackage = 'pbrevenue';

/** 赠送优先策略 */
export enum GiftStrategy {
  GIFT_STRATEGY_NONE = 0,
  /** GIFT_STRATEGY_BAG_ONLY - 只消耗包裹 */
  GIFT_STRATEGY_BAG_ONLY = 1,
  /** GIFT_STRATEGY_PURCHASE_ONLY - 只购买礼物 */
  GIFT_STRATEGY_PURCHASE_ONLY = 2,
  UNRECOGNIZED = -1
}

export function giftStrategyFromJSON(object: any): GiftStrategy {
  switch (object) {
    case 0:
    case 'GIFT_STRATEGY_NONE':
      return GiftStrategy.GIFT_STRATEGY_NONE;
    case 1:
    case 'GIFT_STRATEGY_BAG_ONLY':
      return GiftStrategy.GIFT_STRATEGY_BAG_ONLY;
    case 2:
    case 'GIFT_STRATEGY_PURCHASE_ONLY':
      return GiftStrategy.GIFT_STRATEGY_PURCHASE_ONLY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftStrategy.UNRECOGNIZED;
  }
}

/** 礼物等级 */
export enum GiftLevel {
  /** GIFT_LEVEL_LOW - 低 */
  GIFT_LEVEL_LOW = 0,
  /** GIFT_LEVEL_MIDDLE - 中 */
  GIFT_LEVEL_MIDDLE = 1,
  /** GIFT_LEVEL_HIGH - 高 */
  GIFT_LEVEL_HIGH = 2,
  UNRECOGNIZED = -1
}

export function giftLevelFromJSON(object: any): GiftLevel {
  switch (object) {
    case 0:
    case 'GIFT_LEVEL_LOW':
      return GiftLevel.GIFT_LEVEL_LOW;
    case 1:
    case 'GIFT_LEVEL_MIDDLE':
      return GiftLevel.GIFT_LEVEL_MIDDLE;
    case 2:
    case 'GIFT_LEVEL_HIGH':
      return GiftLevel.GIFT_LEVEL_HIGH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftLevel.UNRECOGNIZED;
  }
}

/** 收送礼分类 */
export enum GiftKind {
  GIFT_KIND_NONE = 0,
  /** GIFT_KIND_SEND - 送礼 */
  GIFT_KIND_SEND = 1,
  /** GIFT_KIND_RECEIVE - 收礼 */
  GIFT_KIND_RECEIVE = 2,
  UNRECOGNIZED = -1
}

export function giftKindFromJSON(object: any): GiftKind {
  switch (object) {
    case 0:
    case 'GIFT_KIND_NONE':
      return GiftKind.GIFT_KIND_NONE;
    case 1:
    case 'GIFT_KIND_SEND':
      return GiftKind.GIFT_KIND_SEND;
    case 2:
    case 'GIFT_KIND_RECEIVE':
      return GiftKind.GIFT_KIND_RECEIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftKind.UNRECOGNIZED;
  }
}

/** 礼物场景 */
export enum GiftScene {
  GIFT_SCENE_NONE = 0,
  /** GIFT_SCENE_ROOM - 房间 */
  GIFT_SCENE_ROOM = 1,
  /** GIFT_SCENE_IM - IM */
  GIFT_SCENE_IM = 2,
  /** GIFT_SCENE_CARNIVAL - 嘉年华 */
  GIFT_SCENE_CARNIVAL = 3,
  /** GIFT_SCENE_CAFE - 清吧房 */
  GIFT_SCENE_CAFE = 4,
  /** GIFT_SCENE_VIDEO_CALL_1V1 - 1v1 视频通话 */
  GIFT_SCENE_VIDEO_CALL_1V1 = 5,
  /** GIFT_SCENE_VIRTUAL_ROOM - 虚拟房 */
  GIFT_SCENE_VIRTUAL_ROOM = 6,
  UNRECOGNIZED = -1
}

export function giftSceneFromJSON(object: any): GiftScene {
  switch (object) {
    case 0:
    case 'GIFT_SCENE_NONE':
      return GiftScene.GIFT_SCENE_NONE;
    case 1:
    case 'GIFT_SCENE_ROOM':
      return GiftScene.GIFT_SCENE_ROOM;
    case 2:
    case 'GIFT_SCENE_IM':
      return GiftScene.GIFT_SCENE_IM;
    case 3:
    case 'GIFT_SCENE_CARNIVAL':
      return GiftScene.GIFT_SCENE_CARNIVAL;
    case 4:
    case 'GIFT_SCENE_CAFE':
      return GiftScene.GIFT_SCENE_CAFE;
    case 5:
    case 'GIFT_SCENE_VIDEO_CALL_1V1':
      return GiftScene.GIFT_SCENE_VIDEO_CALL_1V1;
    case 6:
    case 'GIFT_SCENE_VIRTUAL_ROOM':
      return GiftScene.GIFT_SCENE_VIRTUAL_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftScene.UNRECOGNIZED;
  }
}

/** 礼物类型 */
export enum GiftType {
  GIFT_TYPE_NONE = 0,
  /** GIFT_TYPE_NORMAL - 普通礼物 */
  GIFT_TYPE_NORMAL = 1,
  /** GIFT_TYPE_HEADWEAR - 头像框礼物 */
  GIFT_TYPE_HEADWEAR = 2,
  /** GIFT_TYPE_STORE - 商城礼物 */
  GIFT_TYPE_STORE = 3,
  /** GIFT_TYPE_SPONSOR - 冠名礼物 */
  GIFT_TYPE_SPONSOR = 4,
  /** GIFT_TYPE_LUCKY - 幸运礼物 */
  GIFT_TYPE_LUCKY = 5,
  /** GIFT_TYPE_SPECIAL - 特殊礼物 */
  GIFT_TYPE_SPECIAL = 6,
  /** GIFT_TYPE_FLY_MIC - 飞麦礼物 */
  GIFT_TYPE_FLY_MIC = 7,
  /** GIFT_TYPE_MYSTERY_BOX - 盲盒礼物 */
  GIFT_TYPE_MYSTERY_BOX = 8,
  /** GIFT_TYPE_MYSTERY_BOX_PRIZE - 盲盒礼物奖品礼物 */
  GIFT_TYPE_MYSTERY_BOX_PRIZE = 9,
  /** GIFT_TYPE_ACCOMPANY - 陪陪礼物 */
  GIFT_TYPE_ACCOMPANY = 10,
  /** GIFT_TYPE_GUARDIAN_ACTIVATION - 守护礼物-开通礼物 */
  GIFT_TYPE_GUARDIAN_ACTIVATION = 11,
  /** GIFT_TYPE_GUARDIAN_EXCLUSIVE - 守护礼物-专属礼物 */
  GIFT_TYPE_GUARDIAN_EXCLUSIVE = 12,
  UNRECOGNIZED = -1
}

export function giftTypeFromJSON(object: any): GiftType {
  switch (object) {
    case 0:
    case 'GIFT_TYPE_NONE':
      return GiftType.GIFT_TYPE_NONE;
    case 1:
    case 'GIFT_TYPE_NORMAL':
      return GiftType.GIFT_TYPE_NORMAL;
    case 2:
    case 'GIFT_TYPE_HEADWEAR':
      return GiftType.GIFT_TYPE_HEADWEAR;
    case 3:
    case 'GIFT_TYPE_STORE':
      return GiftType.GIFT_TYPE_STORE;
    case 4:
    case 'GIFT_TYPE_SPONSOR':
      return GiftType.GIFT_TYPE_SPONSOR;
    case 5:
    case 'GIFT_TYPE_LUCKY':
      return GiftType.GIFT_TYPE_LUCKY;
    case 6:
    case 'GIFT_TYPE_SPECIAL':
      return GiftType.GIFT_TYPE_SPECIAL;
    case 7:
    case 'GIFT_TYPE_FLY_MIC':
      return GiftType.GIFT_TYPE_FLY_MIC;
    case 8:
    case 'GIFT_TYPE_MYSTERY_BOX':
      return GiftType.GIFT_TYPE_MYSTERY_BOX;
    case 9:
    case 'GIFT_TYPE_MYSTERY_BOX_PRIZE':
      return GiftType.GIFT_TYPE_MYSTERY_BOX_PRIZE;
    case 10:
    case 'GIFT_TYPE_ACCOMPANY':
      return GiftType.GIFT_TYPE_ACCOMPANY;
    case 11:
    case 'GIFT_TYPE_GUARDIAN_ACTIVATION':
      return GiftType.GIFT_TYPE_GUARDIAN_ACTIVATION;
    case 12:
    case 'GIFT_TYPE_GUARDIAN_EXCLUSIVE':
      return GiftType.GIFT_TYPE_GUARDIAN_EXCLUSIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftType.UNRECOGNIZED;
  }
}

/** 礼物样式尺寸 */
export enum GiftAssetsSize {
  /** GIFT_ASSETS_SIZE_ADAPTIVE - 自适应, 根据实际素材尺寸而定. */
  GIFT_ASSETS_SIZE_ADAPTIVE = 0,
  /** GIFT_ASSETS_SIZE_FULLSCREEN - 全屏 */
  GIFT_ASSETS_SIZE_FULLSCREEN = 1,
  UNRECOGNIZED = -1
}

export function giftAssetsSizeFromJSON(object: any): GiftAssetsSize {
  switch (object) {
    case 0:
    case 'GIFT_ASSETS_SIZE_ADAPTIVE':
      return GiftAssetsSize.GIFT_ASSETS_SIZE_ADAPTIVE;
    case 1:
    case 'GIFT_ASSETS_SIZE_FULLSCREEN':
      return GiftAssetsSize.GIFT_ASSETS_SIZE_FULLSCREEN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftAssetsSize.UNRECOGNIZED;
  }
}

/** 礼物限制类型 */
export enum GiftLimitType {
  /** GIFT_LIMIT_TYPE_NONE - 无限制, 普通礼物 */
  GIFT_LIMIT_TYPE_NONE = 0,
  /** GIFT_LIMIT_TYPE_VIP - VIP 礼物 */
  GIFT_LIMIT_TYPE_VIP = 1,
  /** GIFT_LIMIT_TYPE_ARISTOCRACY - 贵族礼物 */
  GIFT_LIMIT_TYPE_ARISTOCRACY = 2,
  UNRECOGNIZED = -1
}

export function giftLimitTypeFromJSON(object: any): GiftLimitType {
  switch (object) {
    case 0:
    case 'GIFT_LIMIT_TYPE_NONE':
      return GiftLimitType.GIFT_LIMIT_TYPE_NONE;
    case 1:
    case 'GIFT_LIMIT_TYPE_VIP':
      return GiftLimitType.GIFT_LIMIT_TYPE_VIP;
    case 2:
    case 'GIFT_LIMIT_TYPE_ARISTOCRACY':
      return GiftLimitType.GIFT_LIMIT_TYPE_ARISTOCRACY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftLimitType.UNRECOGNIZED;
  }
}

/** 礼物批量赠送类型 */
export enum GiftBatchType {
  /** GIFT_BATCH_TYPE_SINGLE - 只能选择收礼人 */
  GIFT_BATCH_TYPE_SINGLE = 0,
  /** GIFT_BATCH_TYPE_ALL_ON_MIC - 支持 选择收礼人 和 送全麦 */
  GIFT_BATCH_TYPE_ALL_ON_MIC = 1,
  /** GIFT_BATCH_TYPE_ALL_IN_ROOM - 支持 选择收礼人、送全麦 和 送房间在线用户 */
  GIFT_BATCH_TYPE_ALL_IN_ROOM = 2,
  /** GIFT_BATCH_TYPE_CUSTOMIZED_CAFE - 业务自定义的清吧房礼物批量赠送类型 */
  GIFT_BATCH_TYPE_CUSTOMIZED_CAFE = 3,
  UNRECOGNIZED = -1
}

export function giftBatchTypeFromJSON(object: any): GiftBatchType {
  switch (object) {
    case 0:
    case 'GIFT_BATCH_TYPE_SINGLE':
      return GiftBatchType.GIFT_BATCH_TYPE_SINGLE;
    case 1:
    case 'GIFT_BATCH_TYPE_ALL_ON_MIC':
      return GiftBatchType.GIFT_BATCH_TYPE_ALL_ON_MIC;
    case 2:
    case 'GIFT_BATCH_TYPE_ALL_IN_ROOM':
      return GiftBatchType.GIFT_BATCH_TYPE_ALL_IN_ROOM;
    case 3:
    case 'GIFT_BATCH_TYPE_CUSTOMIZED_CAFE':
      return GiftBatchType.GIFT_BATCH_TYPE_CUSTOMIZED_CAFE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftBatchType.UNRECOGNIZED;
  }
}

/** 礼物跳转类型 */
export enum GiftJumpType {
  /** GIFT_JUMP_TYPE_NONE - 无跳转 */
  GIFT_JUMP_TYPE_NONE = 0,
  /** GIFT_JUMP_TYPE_H5 - 跳到 H5 页面 */
  GIFT_JUMP_TYPE_H5 = 1,
  UNRECOGNIZED = -1
}

export function giftJumpTypeFromJSON(object: any): GiftJumpType {
  switch (object) {
    case 0:
    case 'GIFT_JUMP_TYPE_NONE':
      return GiftJumpType.GIFT_JUMP_TYPE_NONE;
    case 1:
    case 'GIFT_JUMP_TYPE_H5':
      return GiftJumpType.GIFT_JUMP_TYPE_H5;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftJumpType.UNRECOGNIZED;
  }
}

/** 礼物玩法类型 */
export enum GiftPlayType {
  /** GIFT_PLAY_TYPE_NONE - 普通礼物，无特殊玩法 */
  GIFT_PLAY_TYPE_NONE = 0,
  /** GIFT_PLAY_TYPE_PROBABILITY - 概率玩法，按配置概率抽取 */
  GIFT_PLAY_TYPE_PROBABILITY = 10,
  /** GIFT_PLAY_TYPE_LOTTERY - 抽奖玩法 */
  GIFT_PLAY_TYPE_LOTTERY = 20,
  UNRECOGNIZED = -1
}

export function giftPlayTypeFromJSON(object: any): GiftPlayType {
  switch (object) {
    case 0:
    case 'GIFT_PLAY_TYPE_NONE':
      return GiftPlayType.GIFT_PLAY_TYPE_NONE;
    case 10:
    case 'GIFT_PLAY_TYPE_PROBABILITY':
      return GiftPlayType.GIFT_PLAY_TYPE_PROBABILITY;
    case 20:
    case 'GIFT_PLAY_TYPE_LOTTERY':
      return GiftPlayType.GIFT_PLAY_TYPE_LOTTERY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftPlayType.UNRECOGNIZED;
  }
}

/** 礼物显示的必要条件类型 */
export enum GiftRequirementType {
  GIFT_REQUIREMENT_TYPE_NONE = 0,
  /** GIFT_REQUIREMENT_TYPE_MIN_VERSION - 客户端最小版本号(包含), 对应 in.Verc */
  GIFT_REQUIREMENT_TYPE_MIN_VERSION = 1,
  /** GIFT_REQUIREMENT_TYPE_MAX_VERSION - 客户端最大版本号(包含), 对应 in.Verc */
  GIFT_REQUIREMENT_TYPE_MAX_VERSION = 2,
  /** GIFT_REQUIREMENT_TYPE_ROOM_MODES - 房间模式, 由业务自己定义, 多个模式用英文逗号分隔. */
  GIFT_REQUIREMENT_TYPE_ROOM_MODES = 3,
  /** GIFT_REQUIREMENT_TYPE_MAX_REGISTER_DAYS - 最大注册天数, 即注册天数小于此值的用户才显示. */
  GIFT_REQUIREMENT_TYPE_MAX_REGISTER_DAYS = 4,
  UNRECOGNIZED = -1
}

export function giftRequirementTypeFromJSON(object: any): GiftRequirementType {
  switch (object) {
    case 0:
    case 'GIFT_REQUIREMENT_TYPE_NONE':
      return GiftRequirementType.GIFT_REQUIREMENT_TYPE_NONE;
    case 1:
    case 'GIFT_REQUIREMENT_TYPE_MIN_VERSION':
      return GiftRequirementType.GIFT_REQUIREMENT_TYPE_MIN_VERSION;
    case 2:
    case 'GIFT_REQUIREMENT_TYPE_MAX_VERSION':
      return GiftRequirementType.GIFT_REQUIREMENT_TYPE_MAX_VERSION;
    case 3:
    case 'GIFT_REQUIREMENT_TYPE_ROOM_MODES':
      return GiftRequirementType.GIFT_REQUIREMENT_TYPE_ROOM_MODES;
    case 4:
    case 'GIFT_REQUIREMENT_TYPE_MAX_REGISTER_DAYS':
      return GiftRequirementType.GIFT_REQUIREMENT_TYPE_MAX_REGISTER_DAYS;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftRequirementType.UNRECOGNIZED;
  }
}

/** 礼物隐藏类型 */
export enum GiftHiddenType {
  GIFT_HIDDEN_TYPE_NONE = 0,
  /** GIFT_HIDDEN_TYPE_AUDIT - 审核期间隐藏 */
  GIFT_HIDDEN_TYPE_AUDIT = 1,
  UNRECOGNIZED = -1
}

export function giftHiddenTypeFromJSON(object: any): GiftHiddenType {
  switch (object) {
    case 0:
    case 'GIFT_HIDDEN_TYPE_NONE':
      return GiftHiddenType.GIFT_HIDDEN_TYPE_NONE;
    case 1:
    case 'GIFT_HIDDEN_TYPE_AUDIT':
      return GiftHiddenType.GIFT_HIDDEN_TYPE_AUDIT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftHiddenType.UNRECOGNIZED;
  }
}

/** 礼物接收策略 */
export enum GiftReceiveStrategy {
  /** GIFT_RECEIVE_STRATEGY_NONE - 默认: 即送即收（送出和收到是同一个动作，不分割的） */
  GIFT_RECEIVE_STRATEGY_NONE = 0,
  /** GIFT_RECEIVE_STRATEGY_CLAIM - 需要手动领取 */
  GIFT_RECEIVE_STRATEGY_CLAIM = 1,
  /** GIFT_RECEIVE_STRATEGY_PLAY_RESULT - 按礼物玩法结果（实际收礼）计算收益 */
  GIFT_RECEIVE_STRATEGY_PLAY_RESULT = 2,
  UNRECOGNIZED = -1
}

export function giftReceiveStrategyFromJSON(object: any): GiftReceiveStrategy {
  switch (object) {
    case 0:
    case 'GIFT_RECEIVE_STRATEGY_NONE':
      return GiftReceiveStrategy.GIFT_RECEIVE_STRATEGY_NONE;
    case 1:
    case 'GIFT_RECEIVE_STRATEGY_CLAIM':
      return GiftReceiveStrategy.GIFT_RECEIVE_STRATEGY_CLAIM;
    case 2:
    case 'GIFT_RECEIVE_STRATEGY_PLAY_RESULT':
      return GiftReceiveStrategy.GIFT_RECEIVE_STRATEGY_PLAY_RESULT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftReceiveStrategy.UNRECOGNIZED;
  }
}

export interface GetGiftPanelReq {
  /** 礼物面板显示场景 */
  scene: GiftScene;
  /** 客户端当前数据版本 */
  version: number;
  /** 拓展字段, 房间模式key: room_mode */
  extension: { [key: string]: string };
}

export interface GetGiftPanelReq_ExtensionEntry {
  key: string;
  value: string;
}

export interface GetGiftPanelRsp {
  /** 礼物列表 */
  gift_tabs: GiftTab[];
  /** 服务端最新数据版本 */
  version: number;
  /** 礼物包裹 */
  gift_bags: GiftBag[];
}

export interface GetGiftBagReq {
  scene: GiftScene;
}

export interface GetGiftBagRsp {
  gift_bags: GiftBag[];
}

export interface GiftBag {
  gift_info: GiftInfo | undefined;
  quantity: number;
}

export interface SendGiftReq {
  /** 礼物ID */
  gift_Info_Id: number;
  /** 赠送数量 */
  quantity: number;
  /** 赠送场景 */
  scene: GiftScene;
  /** 消耗策略 */
  strategy: GiftStrategy;
  /** 批量类型 */
  batch_type: GiftBatchType;
  /** 收礼人列表, 多个收礼人时表示每人送 quantity 个. */
  receiver_uids: number[];
  /** 房间 ID */
  room_id: string;
  /** 拓展字段, key 为拓展字段枚举的数字值. */
  expand: { [key: number]: string };
  /** 透传给业务的上下文, 对于营收中台没有意义. */
  context: { [key: string]: string };
  /** 对于业务自定义的礼物批量送礼类型, 客户端通过 GetCustomizedSendGiftInfo 接口获得的 customer_id 返回值. */
  customized_id: string;
}

/** 拓展字段枚举 */
export enum SendGiftReq_ExpandKey {
  EXPAND_KEY_NONE = 0,
  EXPAND_KEY_FAMILY_ID = 1,
  EXPAND_KEY_CP_ID = 2,
  UNRECOGNIZED = -1
}

export function sendGiftReq_ExpandKeyFromJSON(object: any): SendGiftReq_ExpandKey {
  switch (object) {
    case 0:
    case 'EXPAND_KEY_NONE':
      return SendGiftReq_ExpandKey.EXPAND_KEY_NONE;
    case 1:
    case 'EXPAND_KEY_FAMILY_ID':
      return SendGiftReq_ExpandKey.EXPAND_KEY_FAMILY_ID;
    case 2:
    case 'EXPAND_KEY_CP_ID':
      return SendGiftReq_ExpandKey.EXPAND_KEY_CP_ID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SendGiftReq_ExpandKey.UNRECOGNIZED;
  }
}

export interface SendGiftReq_ExpandEntry {
  key: number;
  value: string;
}

export interface SendGiftReq_ContextEntry {
  key: string;
  value: string;
}

export interface SendGiftRsp {
  /** 账户余额 */
  balances: Balance[];
  /** 连击开关 */
  combo_switch: boolean;
  /** 连击间隔 */
  combo_interval_secs: number;
  /** 送礼结果 */
  result: SendGiftResult | undefined;
}

/** 送礼结果，如果异常需要toast和跳转 */
export interface SendGiftResult {
  /** 0表示成功，其他则异常 */
  code: number;
  /** toast文案，国家代号->文案 */
  toast: { [key: string]: string };
  /** 跳转 */
  jump_url: string;
}

export interface SendGiftResult_ToastEntry {
  key: string;
  value: string;
}

export interface GetGiftStatsReq {
  gift_Info_Id: number;
}

export interface GetGiftStatsRsp {
  need_count: number;
  send_count: number;
}

export interface ListGiftStatsReq {
  page: Page | undefined;
  /** 支持客态查看 */
  target_uid: number;
  /** 收送礼类型 */
  type: ListGiftStatsReq_GiftStatsType;
  /** 统计周期 */
  period: ListGiftStatsReq_GiftStatsPeriod;
  /** 自定义时间的起始日期 */
  start_date: string;
  /** 自定义时间的结束日期 */
  end_date: string;
}

/** 收送礼类型 */
export enum ListGiftStatsReq_GiftStatsType {
  GIFT_STATS_TYPE_NONE = 0,
  /** GIFT_STATS_TYPE_SEND - 送礼 */
  GIFT_STATS_TYPE_SEND = 1,
  /** GIFT_STATS_TYPE_RECEIVE - 收礼 */
  GIFT_STATS_TYPE_RECEIVE = 2,
  UNRECOGNIZED = -1
}

export function listGiftStatsReq_GiftStatsTypeFromJSON(object: any): ListGiftStatsReq_GiftStatsType {
  switch (object) {
    case 0:
    case 'GIFT_STATS_TYPE_NONE':
      return ListGiftStatsReq_GiftStatsType.GIFT_STATS_TYPE_NONE;
    case 1:
    case 'GIFT_STATS_TYPE_SEND':
      return ListGiftStatsReq_GiftStatsType.GIFT_STATS_TYPE_SEND;
    case 2:
    case 'GIFT_STATS_TYPE_RECEIVE':
      return ListGiftStatsReq_GiftStatsType.GIFT_STATS_TYPE_RECEIVE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ListGiftStatsReq_GiftStatsType.UNRECOGNIZED;
  }
}

/** 统计周期 */
export enum ListGiftStatsReq_GiftStatsPeriod {
  GIFT_STATS_PERIOD_NONE = 0,
  /** GIFT_STATS_PERIOD_ALL - 全部时间 */
  GIFT_STATS_PERIOD_ALL = 1,
  /** GIFT_STATS_PERIOD_DIY - 自定义 */
  GIFT_STATS_PERIOD_DIY = 2,
  /** GIFT_STATS_PERIOD_DAILY - 本日 */
  GIFT_STATS_PERIOD_DAILY = 3,
  /** GIFT_STATS_PERIOD_WEEKLY - 本周 */
  GIFT_STATS_PERIOD_WEEKLY = 4,
  /** GIFT_STATS_PERIOD_MONTHLY - 本月 */
  GIFT_STATS_PERIOD_MONTHLY = 5,
  UNRECOGNIZED = -1
}

export function listGiftStatsReq_GiftStatsPeriodFromJSON(object: any): ListGiftStatsReq_GiftStatsPeriod {
  switch (object) {
    case 0:
    case 'GIFT_STATS_PERIOD_NONE':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_NONE;
    case 1:
    case 'GIFT_STATS_PERIOD_ALL':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_ALL;
    case 2:
    case 'GIFT_STATS_PERIOD_DIY':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_DIY;
    case 3:
    case 'GIFT_STATS_PERIOD_DAILY':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_DAILY;
    case 4:
    case 'GIFT_STATS_PERIOD_WEEKLY':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_WEEKLY;
    case 5:
    case 'GIFT_STATS_PERIOD_MONTHLY':
      return ListGiftStatsReq_GiftStatsPeriod.GIFT_STATS_PERIOD_MONTHLY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ListGiftStatsReq_GiftStatsPeriod.UNRECOGNIZED;
  }
}

export interface ListGiftStatsRsp {
  page: Page | undefined;
  total_count: number;
  gift_stats: GiftStats[];
}

export interface ListGiftRecordReq {
  page: Page | undefined;
  kind: GiftKind;
  /** 房间id;有值会导致kind条件失效 */
  room_id: string;
  /** 送礼起始时间 */
  min_time_sent: number;
  /** 送礼结束时间 */
  max_time_sent: number;
  /** 礼物类型 */
  gift_types: GiftType[];
}

export interface ListGiftRecordRsp {
  page: Page | undefined;
  gift_records: GiftRecord[];
}

/** 获取业务自定义送礼信息 */
export interface GetCustomizedSendGiftInfoReq {
  /** 送礼请求 */
  send_gift_req: SendGiftReq | undefined;
  /** 业务自定义的礼物批量送礼类型拓展参数, 最好定义成 JSON 结构字符串方便后续的拓展. */
  customized_value: string;
}

/** 获取业务自定义送礼信息 */
export interface GetCustomizedSendGiftInfoRsp {
  /** 本次业务自定义礼物批量赠送类型的ID标识, 后续需要通过 SendGift 接口的 customized_id 带给服务端. */
  customized_id: string;
  /** 业务与客户端之间自定义的批量送礼返回值, 最好定义成 JSON 结构字符串方便后续的拓展. */
  customized_result: string;
  /** 本次送礼涉及的收礼房间数 */
  receive_room_count: number;
  /** 本次送礼涉及的收礼用户数 */
  receive_user_count: number;
  /** 本次送礼总共所需货币数量 */
  total_spend_amount: number;
}

export interface GiftRecord {
  /** 记录 ID */
  id: number;
  /** 礼物信息 */
  gift_info: GiftInfo | undefined;
  /** 礼物数量 */
  quantity: number;
  /** 送礼人 */
  sender: UserInfo | undefined;
  /** 收礼人 */
  receiver: UserInfo | undefined;
  /** 送礼时间 */
  time_sent: number;
}

/** 礼物标签, 例如: 限时, 七夕 ... */
export interface GiftTag {
  /** 标签名称 */
  name: string;
  /** 标签图标 */
  icon: string;
  /** <语言, 标签图标> */
  icon_i18n: { [key: string]: string };
}

export interface GiftTag_IconI18nEntry {
  key: string;
  value: string;
}

/** 礼物名称, 包含国际化支持 */
export interface GiftName {
  /** 缺省名称 */
  name: string;
  /** <国际码, 名称> */
  names: { [key: string]: string };
}

export interface GiftName_NamesEntry {
  key: string;
  value: string;
}

/** 礼物价格 */
export interface GiftPrice {
  /** 币种 */
  currency_type: CurrencyType;
  /** 现价 */
  price: number;
}

/** 礼物冠名信息 */
export interface GiftSponsorInfo {
  /** 冠名用户信息 */
  sponsors: UserInfo[];
  /** 冠名开始时间 */
  start_time: number;
  /** 冠名结束时间 */
  end_time: number;
}

/** 礼物样式 */
export interface GiftAssets {
  /** 礼物图标 */
  icon: string;
  /** svga动画 */
  svga: string;
  /** 视频动画 */
  video: string;
  /** 动画尺寸 */
  size: GiftAssetsSize;
  /** 飞麦动画 */
  fly_svga: string;
  /** 冠名信息 */
  sponsor: GiftSponsorInfo | undefined;
  /** 是否动画离线资源下载 */
  ani_offline_res: boolean;
  /** 动画离线资源ID, 需要根据 svga 和 video 字段判断该资源的文件类型. */
  ani_offline_res_id: string;
}

/** 离线资源信息查询 */
export interface GetGiftOfflineResInfoReq {}

/** 离线资源信息查询 */
export interface GetGiftOfflineResInfoResp {
  /** 离线资源总数 */
  total: number;
}

/** 礼物 banner */
export interface GiftBanner {
  width: number;
  height: number;
  image: string;
  link: string;
  sponsors: UserInfo[];
  i18n_image: { [key: string]: string };
  /** 礼物 banner 连接打开埋点 ID */
  dot_id: string;
  /** 跳转方式 */
  jump_type: JumpType;
  /** banner id */
  id: number;
}

export interface GiftBanner_I18nImageEntry {
  key: string;
  value: string;
}

/** 礼物限制 */
export interface GiftLimit {
  /** 过期时间 */
  deadline: number;
  /** 限制类型 */
  type: GiftLimitType;
  /** 结合限制类型的限制等级, 如果 type 是 VIP, level = 3, 则表示需要 VIP 3 级以上才能使用 */
  level: number;
  /** 礼物限制图标 */
  tag: GiftTag | undefined;
  /** 特殊礼物的归属人 */
  belong_uid: number;
}

/** 礼物有效时长, 部分礼物类型才有, 例如: 头像框 */
export interface GiftDuration {
  /** 后端计算好的有效期说明 */
  duration: string;
  /** 真实的有效期秒数 */
  seconds: number;
}

/** 礼物跳转, 部分礼物类型才有, 礼物: 商场礼物 */
export interface GiftJump {
  type: GiftJumpType;
  link: string;
}

/** 礼物显示的必要条件 */
export interface GiftRequirement {
  type: GiftRequirementType;
  value: string;
}

/** 礼物隐藏开关 */
export interface GiftHiddenSwitch {
  /** 隐藏类型 */
  type: GiftHiddenType;
  /** 是否隐藏 */
  hidden: boolean;
}

/** 礼物Tab */
export interface GiftTab {
  /** 缺省名称 */
  name: string;
  /** <国际码, 名称> */
  names: { [key: string]: string };
  /** 礼物列表 */
  gift_infos: GiftInfo[];
  /** tab ID */
  id: number;
  /** 礼物显示的必要条件 */
  requirements: GiftRequirement[];
  /** 礼物隐藏开关 */
  hidden_switches: GiftHiddenSwitch[];
}

export interface GiftTab_NamesEntry {
  key: string;
  value: string;
}

/** 概率礼物玩法配置 */
export interface GiftPlayProbability {
  list: GiftPlayProbability_GiftProbability[];
  /** 玩法动画时长-秒 */
  animation_secs: number;
  /** 排序规则 */
  sort: GiftPlayProbability_GiftSortType;
}

export enum GiftPlayProbability_GiftSortType {
  /** SORT_TYPE_NONE - 默认按giftID排序 */
  SORT_TYPE_NONE = 0,
  /** SORT_TYPE_PRICE_ASC - 按价格升序 */
  SORT_TYPE_PRICE_ASC = 1,
  /** SORT_TYPE_PRICE_DESC - 按价格降序 */
  SORT_TYPE_PRICE_DESC = 2,
  /** SORT_TYPE_PROBABILITY_ASC - 按概率生效 */
  SORT_TYPE_PROBABILITY_ASC = 3,
  /** SORT_TYPE_PROBABILITY_DESC - 按概率降序 */
  SORT_TYPE_PROBABILITY_DESC = 4,
  UNRECOGNIZED = -1
}

export function giftPlayProbability_GiftSortTypeFromJSON(object: any): GiftPlayProbability_GiftSortType {
  switch (object) {
    case 0:
    case 'SORT_TYPE_NONE':
      return GiftPlayProbability_GiftSortType.SORT_TYPE_NONE;
    case 1:
    case 'SORT_TYPE_PRICE_ASC':
      return GiftPlayProbability_GiftSortType.SORT_TYPE_PRICE_ASC;
    case 2:
    case 'SORT_TYPE_PRICE_DESC':
      return GiftPlayProbability_GiftSortType.SORT_TYPE_PRICE_DESC;
    case 3:
    case 'SORT_TYPE_PROBABILITY_ASC':
      return GiftPlayProbability_GiftSortType.SORT_TYPE_PROBABILITY_ASC;
    case 4:
    case 'SORT_TYPE_PROBABILITY_DESC':
      return GiftPlayProbability_GiftSortType.SORT_TYPE_PROBABILITY_DESC;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GiftPlayProbability_GiftSortType.UNRECOGNIZED;
  }
}

export interface GiftPlayProbability_GiftProbability {
  /** 礼物ID */
  gift_id: number;
  /** 概率值，全部概率值之和为100 */
  probability: number;
}

/** 礼物玩法 */
export interface GiftPlay {
  type: GiftPlayType;
  probability: GiftPlayProbability | undefined;
  /** 业务自定义扩展信息 */
  expand: { [key: string]: string };
  /** 抽奖配置ID */
  lottery_id: number;
}

export interface GiftPlay_ExpandEntry {
  key: string;
  value: string;
}

/** 礼物信息 */
export interface GiftInfo {
  id: number;
  version: number;
  type: GiftType;
  tags: GiftTag[];
  name: GiftName | undefined;
  price: GiftPrice | undefined;
  assets: GiftAssets | undefined;
  banner: GiftBanner | undefined;
  limit: GiftLimit | undefined;
  batch_type: GiftBatchType;
  duration: GiftDuration | undefined;
  jump: GiftJump | undefined;
  level: GiftLevel;
  tab: GiftTab | undefined;
  /** 礼物标识，保持测试和正式环境配置一致 */
  sid: string;
  /** 礼物玩法 */
  play: GiftPlay | undefined;
  /** 礼物的业务拓展信息 JSON 字符串, 根据业务名找到对应的结构体 GiftBizExtXXX 来解析，例如: GiftBizExtDarling */
  biz_ext: string;
  /** 阶级 */
  tier: number;
}

/** 收送礼统计 */
export interface GiftStats {
  gift_info: GiftInfo | undefined;
  count: number;
}

/** 送礼金额等级 */
export interface GiftAmountLevel {
  /** 金额 */
  amount: number;
  /** 等级 */
  level: number;
}

/** 礼物玩法信息 */
export interface GetGiftPlayInfoReq {
  /** 礼物ID */
  gift_id: number;
}

export interface GetGiftPlayInfoRsp {
  /** 礼物玩法类型 */
  type: GiftPlayType;
  /** 概率配置 */
  probability: GiftPlayProbability | undefined;
  /** 礼物列表 */
  gifts: GiftInfo[];
  /** 入参礼物信息 */
  gift: GiftInfo | undefined;
}

export interface GiftPlayResult {
  type: GiftPlayType;
  /** 玩法选中礼物信息 */
  picked: GiftInfo | undefined;
  /** 玩法配置礼物列表 */
  gifts: GiftInfo[];
  /** 玩法中抽中的货币 */
  picked_currency: PickedCurrency | undefined;
  /** 玩法扩展信息 */
  expand: { [key: string]: string };
}

export interface GiftPlayResult_ExpandEntry {
  key: string;
  value: string;
}

/** 玩法中抽中的货币 */
export interface PickedCurrency {
  /** 币种 */
  currency_type: CurrencyType;
  /** 金额 */
  amount: number;
}

/** 送礼通知 */
export interface NotifySendGift {
  /** 记录 ID */
  seq_id: string;
  /** 礼物信息 */
  gift_info: GiftInfo | undefined;
  /** 礼物数量 */
  quantity: number;
  /** 送礼人 */
  sender: UserInfo | undefined;
  /** 收礼人 */
  receivers: UserInfo[];
  /** 送礼时间 */
  time_sent: number;
  /** 赠送策略 */
  strategy: GiftStrategy;
  /** 礼物批量赠送类型 */
  batch_type: GiftBatchType;
  /** 拓展字段, key 为拓展字段枚举的数字值. */
  expand: { [key: number]: string };
  /** 透传给业务的上下文, 对于营收中台没有意义. */
  context: { [key: string]: string };
  /** 送礼场景 */
  scene: GiftScene;
  /** 送礼的房间ID */
  room_id: string;
  /** 送礼人信息业务透传(各自业务端用户信息pb解析) */
  biz_sender: Uint8Array;
  /** 送礼金额等级 */
  amount_level: GiftAmountLevel | undefined;
}

export interface NotifySendGift_ExpandEntry {
  key: number;
  value: string;
}

export interface NotifySendGift_ContextEntry {
  key: string;
  value: string;
}

/** 幸运礼物中奖通知 */
export interface NotifyLuckyGiftWinning {
  /** 唯一ID */
  seq_id: string;
  /** 中奖用户 */
  user_info: UserInfo | undefined;
  /** 货币类型 */
  currency_type: CurrencyType;
  /** 中奖货币数 */
  amount: number;
  /** 中奖房间ID */
  room_id: string;
}

/** 通知 */
export interface Notify {
  send_gift: NotifySendGift | undefined;
  lucky_gift_winning: NotifyLuckyGiftWinning | undefined;
}

export interface ClaimGiftReq {
  /** 幂等id */
  seq_id: string;
}

export interface ClaimGiftRsp {}

/** 礼物收益 */
export interface GiftProfit {
  /** 币种 */
  currency_type: CurrencyType;
  /** 数额 */
  amount: number;
  /** 免费数额 */
  free_amount: number;
}

export interface GiftReceiveStrategyClaim {
  /** 允许领取的过期时间 */
  expire_at: number;
}

export interface GetGiftTabsReq {
  /** 礼物面板显示场景 */
  scene: GiftScene;
  /** 客户端当前数据版本 */
  version: number;
  /** 拓展字段, 房间模式key: room_mode */
  extension: { [key: string]: string };
}

export interface GetGiftTabsReq_ExtensionEntry {
  key: string;
  value: string;
}

export interface GetGiftTabsRsp {
  /** 礼物tab列表-不包含礼物列表 */
  gift_tabs: GiftTab[];
  /** 服务端最新数据版本 */
  version: number;
}

export interface ListTabGiftsReq {
  page: Page | undefined;
  /** tab ID */
  tab_id: number;
  /** 礼物显示场景 */
  scene: GiftScene;
}

export interface ListTabGiftsRsp {
  page: Page | undefined;
  /** 礼物列表 */
  gift_infos: GiftInfo[];
}

export interface ListRoomGiftStatsReq {
  /** 房间id */
  room_id: string;
  /** 自定义时间的起始日期;格式:yyyy-MM-dd */
  start_date: string;
  /** 自定义时间的结束日期;格式:yyyy-MM-dd */
  end_date: string;
  /** 礼物类型 */
  gift_types: GiftType[];
}

export interface ListRoomGiftStatsRsp {
  items: RoomGiftStatsRecord[];
}

export interface RoomGiftStatsRecord {
  /** 房间id */
  room_id: string;
  /** 日期;格式:yyyy-MM-dd */
  date: string;
  /** 礼物类型 */
  gift_type: GiftType;
  /** 送礼数量 */
  send_count: number;
  /** 送礼消耗总金额 */
  amount: number;
}

export interface ListGiftsReq {
  /** 礼物id列表 */
  ids: number[];
  /** 业务拓展信息(biz_ext)字段的查询 */
  biz_ext_args: BizExtArgs | undefined;
}

export interface ListGiftsRsp {
  /** 礼物列表 */
  gift_infos: GiftInfo[];
}

export interface BizExtArgs {
  /** 是否为心愿礼物 */
  wish_gift_type: WishGiftType;
}

function createBaseGetGiftPanelReq(): GetGiftPanelReq {
  return { scene: 0, version: 0, extension: {} };
}

export const GetGiftPanelReq: MessageFns<GetGiftPanelReq> = {
  fromJSON(object: any): GetGiftPanelReq {
    return {
      scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      extension: isObject(object.extension)
        ? Object.entries(object.extension).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetGiftPanelReq>, I>>(base?: I): GetGiftPanelReq {
    return GetGiftPanelReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftPanelReq>, I>>(object: I): GetGiftPanelReq {
    const message = createBaseGetGiftPanelReq();
    message.scene = object.scene ?? 0;
    message.version = object.version ?? 0;
    message.extension = Object.entries(object.extension ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGetGiftPanelReq_ExtensionEntry(): GetGiftPanelReq_ExtensionEntry {
  return { key: '', value: '' };
}

export const GetGiftPanelReq_ExtensionEntry: MessageFns<GetGiftPanelReq_ExtensionEntry> = {
  fromJSON(object: any): GetGiftPanelReq_ExtensionEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetGiftPanelReq_ExtensionEntry>, I>>(base?: I): GetGiftPanelReq_ExtensionEntry {
    return GetGiftPanelReq_ExtensionEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftPanelReq_ExtensionEntry>, I>>(
    object: I
  ): GetGiftPanelReq_ExtensionEntry {
    const message = createBaseGetGiftPanelReq_ExtensionEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetGiftPanelRsp(): GetGiftPanelRsp {
  return { gift_tabs: [], version: 0, gift_bags: [] };
}

export const GetGiftPanelRsp: MessageFns<GetGiftPanelRsp> = {
  fromJSON(object: any): GetGiftPanelRsp {
    return {
      gift_tabs: globalThis.Array.isArray(object?.gift_tabs)
        ? object.gift_tabs.map((e: any) => GiftTab.fromJSON(e))
        : [],
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      gift_bags: globalThis.Array.isArray(object?.gift_bags)
        ? object.gift_bags.map((e: any) => GiftBag.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetGiftPanelRsp>, I>>(base?: I): GetGiftPanelRsp {
    return GetGiftPanelRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftPanelRsp>, I>>(object: I): GetGiftPanelRsp {
    const message = createBaseGetGiftPanelRsp();
    message.gift_tabs = object.gift_tabs?.map(e => GiftTab.fromPartial(e)) || [];
    message.version = object.version ?? 0;
    message.gift_bags = object.gift_bags?.map(e => GiftBag.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetGiftBagReq(): GetGiftBagReq {
  return { scene: 0 };
}

export const GetGiftBagReq: MessageFns<GetGiftBagReq> = {
  fromJSON(object: any): GetGiftBagReq {
    return { scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGiftBagReq>, I>>(base?: I): GetGiftBagReq {
    return GetGiftBagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftBagReq>, I>>(object: I): GetGiftBagReq {
    const message = createBaseGetGiftBagReq();
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseGetGiftBagRsp(): GetGiftBagRsp {
  return { gift_bags: [] };
}

export const GetGiftBagRsp: MessageFns<GetGiftBagRsp> = {
  fromJSON(object: any): GetGiftBagRsp {
    return {
      gift_bags: globalThis.Array.isArray(object?.gift_bags)
        ? object.gift_bags.map((e: any) => GiftBag.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetGiftBagRsp>, I>>(base?: I): GetGiftBagRsp {
    return GetGiftBagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftBagRsp>, I>>(object: I): GetGiftBagRsp {
    const message = createBaseGetGiftBagRsp();
    message.gift_bags = object.gift_bags?.map(e => GiftBag.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGiftBag(): GiftBag {
  return { gift_info: undefined, quantity: 0 };
}

export const GiftBag: MessageFns<GiftBag> = {
  fromJSON(object: any): GiftBag {
    return {
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftBag>, I>>(base?: I): GiftBag {
    return GiftBag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBag>, I>>(object: I): GiftBag {
    const message = createBaseGiftBag();
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.quantity = object.quantity ?? 0;
    return message;
  }
};

function createBaseSendGiftReq(): SendGiftReq {
  return {
    gift_Info_Id: 0,
    quantity: 0,
    scene: 0,
    strategy: 0,
    batch_type: 0,
    receiver_uids: [],
    room_id: '',
    expand: {},
    context: {},
    customized_id: ''
  };
}

export const SendGiftReq: MessageFns<SendGiftReq> = {
  fromJSON(object: any): SendGiftReq {
    return {
      gift_Info_Id: isSet(object.gift_Info_Id) ? globalThis.Number(object.gift_Info_Id) : 0,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0,
      scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0,
      strategy: isSet(object.strategy) ? giftStrategyFromJSON(object.strategy) : 0,
      batch_type: isSet(object.batch_type) ? giftBatchTypeFromJSON(object.batch_type) : 0,
      receiver_uids: globalThis.Array.isArray(object?.receiver_uids)
        ? object.receiver_uids.map((e: any) => globalThis.Number(e))
        : [],
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: number]: string }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = String(value);
            return acc;
          }, {})
        : {},
      context: isObject(object.context)
        ? Object.entries(object.context).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      customized_id: isSet(object.customized_id) ? globalThis.String(object.customized_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendGiftReq>, I>>(base?: I): SendGiftReq {
    return SendGiftReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftReq>, I>>(object: I): SendGiftReq {
    const message = createBaseSendGiftReq();
    message.gift_Info_Id = object.gift_Info_Id ?? 0;
    message.quantity = object.quantity ?? 0;
    message.scene = object.scene ?? 0;
    message.strategy = object.strategy ?? 0;
    message.batch_type = object.batch_type ?? 0;
    message.receiver_uids = object.receiver_uids?.map(e => e) || [];
    message.room_id = object.room_id ?? '';
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: number]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.context = Object.entries(object.context ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.customized_id = object.customized_id ?? '';
    return message;
  }
};

function createBaseSendGiftReq_ExpandEntry(): SendGiftReq_ExpandEntry {
  return { key: 0, value: '' };
}

export const SendGiftReq_ExpandEntry: MessageFns<SendGiftReq_ExpandEntry> = {
  fromJSON(object: any): SendGiftReq_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendGiftReq_ExpandEntry>, I>>(base?: I): SendGiftReq_ExpandEntry {
    return SendGiftReq_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftReq_ExpandEntry>, I>>(object: I): SendGiftReq_ExpandEntry {
    const message = createBaseSendGiftReq_ExpandEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSendGiftReq_ContextEntry(): SendGiftReq_ContextEntry {
  return { key: '', value: '' };
}

export const SendGiftReq_ContextEntry: MessageFns<SendGiftReq_ContextEntry> = {
  fromJSON(object: any): SendGiftReq_ContextEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendGiftReq_ContextEntry>, I>>(base?: I): SendGiftReq_ContextEntry {
    return SendGiftReq_ContextEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftReq_ContextEntry>, I>>(object: I): SendGiftReq_ContextEntry {
    const message = createBaseSendGiftReq_ContextEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSendGiftRsp(): SendGiftRsp {
  return { balances: [], combo_switch: false, combo_interval_secs: 0, result: undefined };
}

export const SendGiftRsp: MessageFns<SendGiftRsp> = {
  fromJSON(object: any): SendGiftRsp {
    return {
      balances: globalThis.Array.isArray(object?.balances) ? object.balances.map((e: any) => Balance.fromJSON(e)) : [],
      combo_switch: isSet(object.combo_switch) ? globalThis.Boolean(object.combo_switch) : false,
      combo_interval_secs: isSet(object.combo_interval_secs) ? globalThis.Number(object.combo_interval_secs) : 0,
      result: isSet(object.result) ? SendGiftResult.fromJSON(object.result) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SendGiftRsp>, I>>(base?: I): SendGiftRsp {
    return SendGiftRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftRsp>, I>>(object: I): SendGiftRsp {
    const message = createBaseSendGiftRsp();
    message.balances = object.balances?.map(e => Balance.fromPartial(e)) || [];
    message.combo_switch = object.combo_switch ?? false;
    message.combo_interval_secs = object.combo_interval_secs ?? 0;
    message.result =
      object.result !== undefined && object.result !== null ? SendGiftResult.fromPartial(object.result) : undefined;
    return message;
  }
};

function createBaseSendGiftResult(): SendGiftResult {
  return { code: 0, toast: {}, jump_url: '' };
}

export const SendGiftResult: MessageFns<SendGiftResult> = {
  fromJSON(object: any): SendGiftResult {
    return {
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
      toast: isObject(object.toast)
        ? Object.entries(object.toast).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      jump_url: isSet(object.jump_url) ? globalThis.String(object.jump_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendGiftResult>, I>>(base?: I): SendGiftResult {
    return SendGiftResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftResult>, I>>(object: I): SendGiftResult {
    const message = createBaseSendGiftResult();
    message.code = object.code ?? 0;
    message.toast = Object.entries(object.toast ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.jump_url = object.jump_url ?? '';
    return message;
  }
};

function createBaseSendGiftResult_ToastEntry(): SendGiftResult_ToastEntry {
  return { key: '', value: '' };
}

export const SendGiftResult_ToastEntry: MessageFns<SendGiftResult_ToastEntry> = {
  fromJSON(object: any): SendGiftResult_ToastEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendGiftResult_ToastEntry>, I>>(base?: I): SendGiftResult_ToastEntry {
    return SendGiftResult_ToastEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendGiftResult_ToastEntry>, I>>(object: I): SendGiftResult_ToastEntry {
    const message = createBaseSendGiftResult_ToastEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetGiftStatsReq(): GetGiftStatsReq {
  return { gift_Info_Id: 0 };
}

export const GetGiftStatsReq: MessageFns<GetGiftStatsReq> = {
  fromJSON(object: any): GetGiftStatsReq {
    return { gift_Info_Id: isSet(object.gift_Info_Id) ? globalThis.Number(object.gift_Info_Id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGiftStatsReq>, I>>(base?: I): GetGiftStatsReq {
    return GetGiftStatsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftStatsReq>, I>>(object: I): GetGiftStatsReq {
    const message = createBaseGetGiftStatsReq();
    message.gift_Info_Id = object.gift_Info_Id ?? 0;
    return message;
  }
};

function createBaseGetGiftStatsRsp(): GetGiftStatsRsp {
  return { need_count: 0, send_count: 0 };
}

export const GetGiftStatsRsp: MessageFns<GetGiftStatsRsp> = {
  fromJSON(object: any): GetGiftStatsRsp {
    return {
      need_count: isSet(object.need_count) ? globalThis.Number(object.need_count) : 0,
      send_count: isSet(object.send_count) ? globalThis.Number(object.send_count) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetGiftStatsRsp>, I>>(base?: I): GetGiftStatsRsp {
    return GetGiftStatsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftStatsRsp>, I>>(object: I): GetGiftStatsRsp {
    const message = createBaseGetGiftStatsRsp();
    message.need_count = object.need_count ?? 0;
    message.send_count = object.send_count ?? 0;
    return message;
  }
};

function createBaseListGiftStatsReq(): ListGiftStatsReq {
  return { page: undefined, target_uid: 0, type: 0, period: 0, start_date: '', end_date: '' };
}

export const ListGiftStatsReq: MessageFns<ListGiftStatsReq> = {
  fromJSON(object: any): ListGiftStatsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0,
      type: isSet(object.type) ? listGiftStatsReq_GiftStatsTypeFromJSON(object.type) : 0,
      period: isSet(object.period) ? listGiftStatsReq_GiftStatsPeriodFromJSON(object.period) : 0,
      start_date: isSet(object.start_date) ? globalThis.String(object.start_date) : '',
      end_date: isSet(object.end_date) ? globalThis.String(object.end_date) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListGiftStatsReq>, I>>(base?: I): ListGiftStatsReq {
    return ListGiftStatsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftStatsReq>, I>>(object: I): ListGiftStatsReq {
    const message = createBaseListGiftStatsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.target_uid = object.target_uid ?? 0;
    message.type = object.type ?? 0;
    message.period = object.period ?? 0;
    message.start_date = object.start_date ?? '';
    message.end_date = object.end_date ?? '';
    return message;
  }
};

function createBaseListGiftStatsRsp(): ListGiftStatsRsp {
  return { page: undefined, total_count: 0, gift_stats: [] };
}

export const ListGiftStatsRsp: MessageFns<ListGiftStatsRsp> = {
  fromJSON(object: any): ListGiftStatsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      total_count: isSet(object.total_count) ? globalThis.Number(object.total_count) : 0,
      gift_stats: globalThis.Array.isArray(object?.gift_stats)
        ? object.gift_stats.map((e: any) => GiftStats.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGiftStatsRsp>, I>>(base?: I): ListGiftStatsRsp {
    return ListGiftStatsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftStatsRsp>, I>>(object: I): ListGiftStatsRsp {
    const message = createBaseListGiftStatsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.total_count = object.total_count ?? 0;
    message.gift_stats = object.gift_stats?.map(e => GiftStats.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListGiftRecordReq(): ListGiftRecordReq {
  return { page: undefined, kind: 0, room_id: '', min_time_sent: 0, max_time_sent: 0, gift_types: [] };
}

export const ListGiftRecordReq: MessageFns<ListGiftRecordReq> = {
  fromJSON(object: any): ListGiftRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      kind: isSet(object.kind) ? giftKindFromJSON(object.kind) : 0,
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      min_time_sent: isSet(object.min_time_sent) ? globalThis.Number(object.min_time_sent) : 0,
      max_time_sent: isSet(object.max_time_sent) ? globalThis.Number(object.max_time_sent) : 0,
      gift_types: globalThis.Array.isArray(object?.gift_types)
        ? object.gift_types.map((e: any) => giftTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGiftRecordReq>, I>>(base?: I): ListGiftRecordReq {
    return ListGiftRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftRecordReq>, I>>(object: I): ListGiftRecordReq {
    const message = createBaseListGiftRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.kind = object.kind ?? 0;
    message.room_id = object.room_id ?? '';
    message.min_time_sent = object.min_time_sent ?? 0;
    message.max_time_sent = object.max_time_sent ?? 0;
    message.gift_types = object.gift_types?.map(e => e) || [];
    return message;
  }
};

function createBaseListGiftRecordRsp(): ListGiftRecordRsp {
  return { page: undefined, gift_records: [] };
}

export const ListGiftRecordRsp: MessageFns<ListGiftRecordRsp> = {
  fromJSON(object: any): ListGiftRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      gift_records: globalThis.Array.isArray(object?.gift_records)
        ? object.gift_records.map((e: any) => GiftRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGiftRecordRsp>, I>>(base?: I): ListGiftRecordRsp {
    return ListGiftRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftRecordRsp>, I>>(object: I): ListGiftRecordRsp {
    const message = createBaseListGiftRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.gift_records = object.gift_records?.map(e => GiftRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetCustomizedSendGiftInfoReq(): GetCustomizedSendGiftInfoReq {
  return { send_gift_req: undefined, customized_value: '' };
}

export const GetCustomizedSendGiftInfoReq: MessageFns<GetCustomizedSendGiftInfoReq> = {
  fromJSON(object: any): GetCustomizedSendGiftInfoReq {
    return {
      send_gift_req: isSet(object.send_gift_req) ? SendGiftReq.fromJSON(object.send_gift_req) : undefined,
      customized_value: isSet(object.customized_value) ? globalThis.String(object.customized_value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetCustomizedSendGiftInfoReq>, I>>(base?: I): GetCustomizedSendGiftInfoReq {
    return GetCustomizedSendGiftInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCustomizedSendGiftInfoReq>, I>>(object: I): GetCustomizedSendGiftInfoReq {
    const message = createBaseGetCustomizedSendGiftInfoReq();
    message.send_gift_req =
      object.send_gift_req !== undefined && object.send_gift_req !== null
        ? SendGiftReq.fromPartial(object.send_gift_req)
        : undefined;
    message.customized_value = object.customized_value ?? '';
    return message;
  }
};

function createBaseGetCustomizedSendGiftInfoRsp(): GetCustomizedSendGiftInfoRsp {
  return {
    customized_id: '',
    customized_result: '',
    receive_room_count: 0,
    receive_user_count: 0,
    total_spend_amount: 0
  };
}

export const GetCustomizedSendGiftInfoRsp: MessageFns<GetCustomizedSendGiftInfoRsp> = {
  fromJSON(object: any): GetCustomizedSendGiftInfoRsp {
    return {
      customized_id: isSet(object.customized_id) ? globalThis.String(object.customized_id) : '',
      customized_result: isSet(object.customized_result) ? globalThis.String(object.customized_result) : '',
      receive_room_count: isSet(object.receive_room_count) ? globalThis.Number(object.receive_room_count) : 0,
      receive_user_count: isSet(object.receive_user_count) ? globalThis.Number(object.receive_user_count) : 0,
      total_spend_amount: isSet(object.total_spend_amount) ? globalThis.Number(object.total_spend_amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetCustomizedSendGiftInfoRsp>, I>>(base?: I): GetCustomizedSendGiftInfoRsp {
    return GetCustomizedSendGiftInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetCustomizedSendGiftInfoRsp>, I>>(object: I): GetCustomizedSendGiftInfoRsp {
    const message = createBaseGetCustomizedSendGiftInfoRsp();
    message.customized_id = object.customized_id ?? '';
    message.customized_result = object.customized_result ?? '';
    message.receive_room_count = object.receive_room_count ?? 0;
    message.receive_user_count = object.receive_user_count ?? 0;
    message.total_spend_amount = object.total_spend_amount ?? 0;
    return message;
  }
};

function createBaseGiftRecord(): GiftRecord {
  return { id: 0, gift_info: undefined, quantity: 0, sender: undefined, receiver: undefined, time_sent: 0 };
}

export const GiftRecord: MessageFns<GiftRecord> = {
  fromJSON(object: any): GiftRecord {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0,
      sender: isSet(object.sender) ? UserInfo.fromJSON(object.sender) : undefined,
      receiver: isSet(object.receiver) ? UserInfo.fromJSON(object.receiver) : undefined,
      time_sent: isSet(object.time_sent) ? globalThis.Number(object.time_sent) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftRecord>, I>>(base?: I): GiftRecord {
    return GiftRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftRecord>, I>>(object: I): GiftRecord {
    const message = createBaseGiftRecord();
    message.id = object.id ?? 0;
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.quantity = object.quantity ?? 0;
    message.sender =
      object.sender !== undefined && object.sender !== null ? UserInfo.fromPartial(object.sender) : undefined;
    message.receiver =
      object.receiver !== undefined && object.receiver !== null ? UserInfo.fromPartial(object.receiver) : undefined;
    message.time_sent = object.time_sent ?? 0;
    return message;
  }
};

function createBaseGiftTag(): GiftTag {
  return { name: '', icon: '', icon_i18n: {} };
}

export const GiftTag: MessageFns<GiftTag> = {
  fromJSON(object: any): GiftTag {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      icon_i18n: isObject(object.icon_i18n)
        ? Object.entries(object.icon_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GiftTag>, I>>(base?: I): GiftTag {
    return GiftTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTag>, I>>(object: I): GiftTag {
    const message = createBaseGiftTag();
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    message.icon_i18n = Object.entries(object.icon_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGiftTag_IconI18nEntry(): GiftTag_IconI18nEntry {
  return { key: '', value: '' };
}

export const GiftTag_IconI18nEntry: MessageFns<GiftTag_IconI18nEntry> = {
  fromJSON(object: any): GiftTag_IconI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftTag_IconI18nEntry>, I>>(base?: I): GiftTag_IconI18nEntry {
    return GiftTag_IconI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTag_IconI18nEntry>, I>>(object: I): GiftTag_IconI18nEntry {
    const message = createBaseGiftTag_IconI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftName(): GiftName {
  return { name: '', names: {} };
}

export const GiftName: MessageFns<GiftName> = {
  fromJSON(object: any): GiftName {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      names: isObject(object.names)
        ? Object.entries(object.names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GiftName>, I>>(base?: I): GiftName {
    return GiftName.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftName>, I>>(object: I): GiftName {
    const message = createBaseGiftName();
    message.name = object.name ?? '';
    message.names = Object.entries(object.names ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGiftName_NamesEntry(): GiftName_NamesEntry {
  return { key: '', value: '' };
}

export const GiftName_NamesEntry: MessageFns<GiftName_NamesEntry> = {
  fromJSON(object: any): GiftName_NamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftName_NamesEntry>, I>>(base?: I): GiftName_NamesEntry {
    return GiftName_NamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftName_NamesEntry>, I>>(object: I): GiftName_NamesEntry {
    const message = createBaseGiftName_NamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftPrice(): GiftPrice {
  return { currency_type: 0, price: 0 };
}

export const GiftPrice: MessageFns<GiftPrice> = {
  fromJSON(object: any): GiftPrice {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      price: isSet(object.price) ? globalThis.Number(object.price) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftPrice>, I>>(base?: I): GiftPrice {
    return GiftPrice.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPrice>, I>>(object: I): GiftPrice {
    const message = createBaseGiftPrice();
    message.currency_type = object.currency_type ?? 0;
    message.price = object.price ?? 0;
    return message;
  }
};

function createBaseGiftSponsorInfo(): GiftSponsorInfo {
  return { sponsors: [], start_time: 0, end_time: 0 };
}

export const GiftSponsorInfo: MessageFns<GiftSponsorInfo> = {
  fromJSON(object: any): GiftSponsorInfo {
    return {
      sponsors: globalThis.Array.isArray(object?.sponsors) ? object.sponsors.map((e: any) => UserInfo.fromJSON(e)) : [],
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftSponsorInfo>, I>>(base?: I): GiftSponsorInfo {
    return GiftSponsorInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftSponsorInfo>, I>>(object: I): GiftSponsorInfo {
    const message = createBaseGiftSponsorInfo();
    message.sponsors = object.sponsors?.map(e => UserInfo.fromPartial(e)) || [];
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBaseGiftAssets(): GiftAssets {
  return {
    icon: '',
    svga: '',
    video: '',
    size: 0,
    fly_svga: '',
    sponsor: undefined,
    ani_offline_res: false,
    ani_offline_res_id: ''
  };
}

export const GiftAssets: MessageFns<GiftAssets> = {
  fromJSON(object: any): GiftAssets {
    return {
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      svga: isSet(object.svga) ? globalThis.String(object.svga) : '',
      video: isSet(object.video) ? globalThis.String(object.video) : '',
      size: isSet(object.size) ? giftAssetsSizeFromJSON(object.size) : 0,
      fly_svga: isSet(object.fly_svga) ? globalThis.String(object.fly_svga) : '',
      sponsor: isSet(object.sponsor) ? GiftSponsorInfo.fromJSON(object.sponsor) : undefined,
      ani_offline_res: isSet(object.ani_offline_res) ? globalThis.Boolean(object.ani_offline_res) : false,
      ani_offline_res_id: isSet(object.ani_offline_res_id) ? globalThis.String(object.ani_offline_res_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftAssets>, I>>(base?: I): GiftAssets {
    return GiftAssets.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftAssets>, I>>(object: I): GiftAssets {
    const message = createBaseGiftAssets();
    message.icon = object.icon ?? '';
    message.svga = object.svga ?? '';
    message.video = object.video ?? '';
    message.size = object.size ?? 0;
    message.fly_svga = object.fly_svga ?? '';
    message.sponsor =
      object.sponsor !== undefined && object.sponsor !== null ? GiftSponsorInfo.fromPartial(object.sponsor) : undefined;
    message.ani_offline_res = object.ani_offline_res ?? false;
    message.ani_offline_res_id = object.ani_offline_res_id ?? '';
    return message;
  }
};

function createBaseGetGiftOfflineResInfoReq(): GetGiftOfflineResInfoReq {
  return {};
}

export const GetGiftOfflineResInfoReq: MessageFns<GetGiftOfflineResInfoReq> = {
  fromJSON(_: any): GetGiftOfflineResInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetGiftOfflineResInfoReq>, I>>(base?: I): GetGiftOfflineResInfoReq {
    return GetGiftOfflineResInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftOfflineResInfoReq>, I>>(_: I): GetGiftOfflineResInfoReq {
    const message = createBaseGetGiftOfflineResInfoReq();
    return message;
  }
};

function createBaseGetGiftOfflineResInfoResp(): GetGiftOfflineResInfoResp {
  return { total: 0 };
}

export const GetGiftOfflineResInfoResp: MessageFns<GetGiftOfflineResInfoResp> = {
  fromJSON(object: any): GetGiftOfflineResInfoResp {
    return { total: isSet(object.total) ? globalThis.Number(object.total) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGiftOfflineResInfoResp>, I>>(base?: I): GetGiftOfflineResInfoResp {
    return GetGiftOfflineResInfoResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftOfflineResInfoResp>, I>>(object: I): GetGiftOfflineResInfoResp {
    const message = createBaseGetGiftOfflineResInfoResp();
    message.total = object.total ?? 0;
    return message;
  }
};

function createBaseGiftBanner(): GiftBanner {
  return { width: 0, height: 0, image: '', link: '', sponsors: [], i18n_image: {}, dot_id: '', jump_type: 0, id: 0 };
}

export const GiftBanner: MessageFns<GiftBanner> = {
  fromJSON(object: any): GiftBanner {
    return {
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      image: isSet(object.image) ? globalThis.String(object.image) : '',
      link: isSet(object.link) ? globalThis.String(object.link) : '',
      sponsors: globalThis.Array.isArray(object?.sponsors) ? object.sponsors.map((e: any) => UserInfo.fromJSON(e)) : [],
      i18n_image: isObject(object.i18n_image)
        ? Object.entries(object.i18n_image).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      dot_id: isSet(object.dot_id) ? globalThis.String(object.dot_id) : '',
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftBanner>, I>>(base?: I): GiftBanner {
    return GiftBanner.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBanner>, I>>(object: I): GiftBanner {
    const message = createBaseGiftBanner();
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.image = object.image ?? '';
    message.link = object.link ?? '';
    message.sponsors = object.sponsors?.map(e => UserInfo.fromPartial(e)) || [];
    message.i18n_image = Object.entries(object.i18n_image ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.dot_id = object.dot_id ?? '';
    message.jump_type = object.jump_type ?? 0;
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGiftBanner_I18nImageEntry(): GiftBanner_I18nImageEntry {
  return { key: '', value: '' };
}

export const GiftBanner_I18nImageEntry: MessageFns<GiftBanner_I18nImageEntry> = {
  fromJSON(object: any): GiftBanner_I18nImageEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftBanner_I18nImageEntry>, I>>(base?: I): GiftBanner_I18nImageEntry {
    return GiftBanner_I18nImageEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBanner_I18nImageEntry>, I>>(object: I): GiftBanner_I18nImageEntry {
    const message = createBaseGiftBanner_I18nImageEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftLimit(): GiftLimit {
  return { deadline: 0, type: 0, level: 0, tag: undefined, belong_uid: 0 };
}

export const GiftLimit: MessageFns<GiftLimit> = {
  fromJSON(object: any): GiftLimit {
    return {
      deadline: isSet(object.deadline) ? globalThis.Number(object.deadline) : 0,
      type: isSet(object.type) ? giftLimitTypeFromJSON(object.type) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      tag: isSet(object.tag) ? GiftTag.fromJSON(object.tag) : undefined,
      belong_uid: isSet(object.belong_uid) ? globalThis.Number(object.belong_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftLimit>, I>>(base?: I): GiftLimit {
    return GiftLimit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftLimit>, I>>(object: I): GiftLimit {
    const message = createBaseGiftLimit();
    message.deadline = object.deadline ?? 0;
    message.type = object.type ?? 0;
    message.level = object.level ?? 0;
    message.tag = object.tag !== undefined && object.tag !== null ? GiftTag.fromPartial(object.tag) : undefined;
    message.belong_uid = object.belong_uid ?? 0;
    return message;
  }
};

function createBaseGiftDuration(): GiftDuration {
  return { duration: '', seconds: 0 };
}

export const GiftDuration: MessageFns<GiftDuration> = {
  fromJSON(object: any): GiftDuration {
    return {
      duration: isSet(object.duration) ? globalThis.String(object.duration) : '',
      seconds: isSet(object.seconds) ? globalThis.Number(object.seconds) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftDuration>, I>>(base?: I): GiftDuration {
    return GiftDuration.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftDuration>, I>>(object: I): GiftDuration {
    const message = createBaseGiftDuration();
    message.duration = object.duration ?? '';
    message.seconds = object.seconds ?? 0;
    return message;
  }
};

function createBaseGiftJump(): GiftJump {
  return { type: 0, link: '' };
}

export const GiftJump: MessageFns<GiftJump> = {
  fromJSON(object: any): GiftJump {
    return {
      type: isSet(object.type) ? giftJumpTypeFromJSON(object.type) : 0,
      link: isSet(object.link) ? globalThis.String(object.link) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftJump>, I>>(base?: I): GiftJump {
    return GiftJump.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftJump>, I>>(object: I): GiftJump {
    const message = createBaseGiftJump();
    message.type = object.type ?? 0;
    message.link = object.link ?? '';
    return message;
  }
};

function createBaseGiftRequirement(): GiftRequirement {
  return { type: 0, value: '' };
}

export const GiftRequirement: MessageFns<GiftRequirement> = {
  fromJSON(object: any): GiftRequirement {
    return {
      type: isSet(object.type) ? giftRequirementTypeFromJSON(object.type) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftRequirement>, I>>(base?: I): GiftRequirement {
    return GiftRequirement.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftRequirement>, I>>(object: I): GiftRequirement {
    const message = createBaseGiftRequirement();
    message.type = object.type ?? 0;
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftHiddenSwitch(): GiftHiddenSwitch {
  return { type: 0, hidden: false };
}

export const GiftHiddenSwitch: MessageFns<GiftHiddenSwitch> = {
  fromJSON(object: any): GiftHiddenSwitch {
    return {
      type: isSet(object.type) ? giftHiddenTypeFromJSON(object.type) : 0,
      hidden: isSet(object.hidden) ? globalThis.Boolean(object.hidden) : false
    };
  },

  create<I extends Exact<DeepPartial<GiftHiddenSwitch>, I>>(base?: I): GiftHiddenSwitch {
    return GiftHiddenSwitch.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftHiddenSwitch>, I>>(object: I): GiftHiddenSwitch {
    const message = createBaseGiftHiddenSwitch();
    message.type = object.type ?? 0;
    message.hidden = object.hidden ?? false;
    return message;
  }
};

function createBaseGiftTab(): GiftTab {
  return { name: '', names: {}, gift_infos: [], id: 0, requirements: [], hidden_switches: [] };
}

export const GiftTab: MessageFns<GiftTab> = {
  fromJSON(object: any): GiftTab {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      names: isObject(object.names)
        ? Object.entries(object.names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      gift_infos: globalThis.Array.isArray(object?.gift_infos)
        ? object.gift_infos.map((e: any) => GiftInfo.fromJSON(e))
        : [],
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      requirements: globalThis.Array.isArray(object?.requirements)
        ? object.requirements.map((e: any) => GiftRequirement.fromJSON(e))
        : [],
      hidden_switches: globalThis.Array.isArray(object?.hidden_switches)
        ? object.hidden_switches.map((e: any) => GiftHiddenSwitch.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GiftTab>, I>>(base?: I): GiftTab {
    return GiftTab.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTab>, I>>(object: I): GiftTab {
    const message = createBaseGiftTab();
    message.name = object.name ?? '';
    message.names = Object.entries(object.names ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.gift_infos = object.gift_infos?.map(e => GiftInfo.fromPartial(e)) || [];
    message.id = object.id ?? 0;
    message.requirements = object.requirements?.map(e => GiftRequirement.fromPartial(e)) || [];
    message.hidden_switches = object.hidden_switches?.map(e => GiftHiddenSwitch.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGiftTab_NamesEntry(): GiftTab_NamesEntry {
  return { key: '', value: '' };
}

export const GiftTab_NamesEntry: MessageFns<GiftTab_NamesEntry> = {
  fromJSON(object: any): GiftTab_NamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftTab_NamesEntry>, I>>(base?: I): GiftTab_NamesEntry {
    return GiftTab_NamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTab_NamesEntry>, I>>(object: I): GiftTab_NamesEntry {
    const message = createBaseGiftTab_NamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftPlayProbability(): GiftPlayProbability {
  return { list: [], animation_secs: 0, sort: 0 };
}

export const GiftPlayProbability: MessageFns<GiftPlayProbability> = {
  fromJSON(object: any): GiftPlayProbability {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => GiftPlayProbability_GiftProbability.fromJSON(e))
        : [],
      animation_secs: isSet(object.animation_secs) ? globalThis.Number(object.animation_secs) : 0,
      sort: isSet(object.sort) ? giftPlayProbability_GiftSortTypeFromJSON(object.sort) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftPlayProbability>, I>>(base?: I): GiftPlayProbability {
    return GiftPlayProbability.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlayProbability>, I>>(object: I): GiftPlayProbability {
    const message = createBaseGiftPlayProbability();
    message.list = object.list?.map(e => GiftPlayProbability_GiftProbability.fromPartial(e)) || [];
    message.animation_secs = object.animation_secs ?? 0;
    message.sort = object.sort ?? 0;
    return message;
  }
};

function createBaseGiftPlayProbability_GiftProbability(): GiftPlayProbability_GiftProbability {
  return { gift_id: 0, probability: 0 };
}

export const GiftPlayProbability_GiftProbability: MessageFns<GiftPlayProbability_GiftProbability> = {
  fromJSON(object: any): GiftPlayProbability_GiftProbability {
    return {
      gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0,
      probability: isSet(object.probability) ? globalThis.Number(object.probability) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftPlayProbability_GiftProbability>, I>>(
    base?: I
  ): GiftPlayProbability_GiftProbability {
    return GiftPlayProbability_GiftProbability.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlayProbability_GiftProbability>, I>>(
    object: I
  ): GiftPlayProbability_GiftProbability {
    const message = createBaseGiftPlayProbability_GiftProbability();
    message.gift_id = object.gift_id ?? 0;
    message.probability = object.probability ?? 0;
    return message;
  }
};

function createBaseGiftPlay(): GiftPlay {
  return { type: 0, probability: undefined, expand: {}, lottery_id: 0 };
}

export const GiftPlay: MessageFns<GiftPlay> = {
  fromJSON(object: any): GiftPlay {
    return {
      type: isSet(object.type) ? giftPlayTypeFromJSON(object.type) : 0,
      probability: isSet(object.probability) ? GiftPlayProbability.fromJSON(object.probability) : undefined,
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftPlay>, I>>(base?: I): GiftPlay {
    return GiftPlay.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlay>, I>>(object: I): GiftPlay {
    const message = createBaseGiftPlay();
    message.type = object.type ?? 0;
    message.probability =
      object.probability !== undefined && object.probability !== null
        ? GiftPlayProbability.fromPartial(object.probability)
        : undefined;
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseGiftPlay_ExpandEntry(): GiftPlay_ExpandEntry {
  return { key: '', value: '' };
}

export const GiftPlay_ExpandEntry: MessageFns<GiftPlay_ExpandEntry> = {
  fromJSON(object: any): GiftPlay_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftPlay_ExpandEntry>, I>>(base?: I): GiftPlay_ExpandEntry {
    return GiftPlay_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlay_ExpandEntry>, I>>(object: I): GiftPlay_ExpandEntry {
    const message = createBaseGiftPlay_ExpandEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGiftInfo(): GiftInfo {
  return {
    id: 0,
    version: 0,
    type: 0,
    tags: [],
    name: undefined,
    price: undefined,
    assets: undefined,
    banner: undefined,
    limit: undefined,
    batch_type: 0,
    duration: undefined,
    jump: undefined,
    level: 0,
    tab: undefined,
    sid: '',
    play: undefined,
    biz_ext: '',
    tier: 0
  };
}

export const GiftInfo: MessageFns<GiftInfo> = {
  fromJSON(object: any): GiftInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      type: isSet(object.type) ? giftTypeFromJSON(object.type) : 0,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => GiftTag.fromJSON(e)) : [],
      name: isSet(object.name) ? GiftName.fromJSON(object.name) : undefined,
      price: isSet(object.price) ? GiftPrice.fromJSON(object.price) : undefined,
      assets: isSet(object.assets) ? GiftAssets.fromJSON(object.assets) : undefined,
      banner: isSet(object.banner) ? GiftBanner.fromJSON(object.banner) : undefined,
      limit: isSet(object.limit) ? GiftLimit.fromJSON(object.limit) : undefined,
      batch_type: isSet(object.batch_type) ? giftBatchTypeFromJSON(object.batch_type) : 0,
      duration: isSet(object.duration) ? GiftDuration.fromJSON(object.duration) : undefined,
      jump: isSet(object.jump) ? GiftJump.fromJSON(object.jump) : undefined,
      level: isSet(object.level) ? giftLevelFromJSON(object.level) : 0,
      tab: isSet(object.tab) ? GiftTab.fromJSON(object.tab) : undefined,
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      play: isSet(object.play) ? GiftPlay.fromJSON(object.play) : undefined,
      biz_ext: isSet(object.biz_ext) ? globalThis.String(object.biz_ext) : '',
      tier: isSet(object.tier) ? globalThis.Number(object.tier) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftInfo>, I>>(base?: I): GiftInfo {
    return GiftInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftInfo>, I>>(object: I): GiftInfo {
    const message = createBaseGiftInfo();
    message.id = object.id ?? 0;
    message.version = object.version ?? 0;
    message.type = object.type ?? 0;
    message.tags = object.tags?.map(e => GiftTag.fromPartial(e)) || [];
    message.name = object.name !== undefined && object.name !== null ? GiftName.fromPartial(object.name) : undefined;
    message.price =
      object.price !== undefined && object.price !== null ? GiftPrice.fromPartial(object.price) : undefined;
    message.assets =
      object.assets !== undefined && object.assets !== null ? GiftAssets.fromPartial(object.assets) : undefined;
    message.banner =
      object.banner !== undefined && object.banner !== null ? GiftBanner.fromPartial(object.banner) : undefined;
    message.limit =
      object.limit !== undefined && object.limit !== null ? GiftLimit.fromPartial(object.limit) : undefined;
    message.batch_type = object.batch_type ?? 0;
    message.duration =
      object.duration !== undefined && object.duration !== null ? GiftDuration.fromPartial(object.duration) : undefined;
    message.jump = object.jump !== undefined && object.jump !== null ? GiftJump.fromPartial(object.jump) : undefined;
    message.level = object.level ?? 0;
    message.tab = object.tab !== undefined && object.tab !== null ? GiftTab.fromPartial(object.tab) : undefined;
    message.sid = object.sid ?? '';
    message.play = object.play !== undefined && object.play !== null ? GiftPlay.fromPartial(object.play) : undefined;
    message.biz_ext = object.biz_ext ?? '';
    message.tier = object.tier ?? 0;
    return message;
  }
};

function createBaseGiftStats(): GiftStats {
  return { gift_info: undefined, count: 0 };
}

export const GiftStats: MessageFns<GiftStats> = {
  fromJSON(object: any): GiftStats {
    return {
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      count: isSet(object.count) ? globalThis.Number(object.count) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftStats>, I>>(base?: I): GiftStats {
    return GiftStats.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftStats>, I>>(object: I): GiftStats {
    const message = createBaseGiftStats();
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseGiftAmountLevel(): GiftAmountLevel {
  return { amount: 0, level: 0 };
}

export const GiftAmountLevel: MessageFns<GiftAmountLevel> = {
  fromJSON(object: any): GiftAmountLevel {
    return {
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftAmountLevel>, I>>(base?: I): GiftAmountLevel {
    return GiftAmountLevel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftAmountLevel>, I>>(object: I): GiftAmountLevel {
    const message = createBaseGiftAmountLevel();
    message.amount = object.amount ?? 0;
    message.level = object.level ?? 0;
    return message;
  }
};

function createBaseGetGiftPlayInfoReq(): GetGiftPlayInfoReq {
  return { gift_id: 0 };
}

export const GetGiftPlayInfoReq: MessageFns<GetGiftPlayInfoReq> = {
  fromJSON(object: any): GetGiftPlayInfoReq {
    return { gift_id: isSet(object.gift_id) ? globalThis.Number(object.gift_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGiftPlayInfoReq>, I>>(base?: I): GetGiftPlayInfoReq {
    return GetGiftPlayInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftPlayInfoReq>, I>>(object: I): GetGiftPlayInfoReq {
    const message = createBaseGetGiftPlayInfoReq();
    message.gift_id = object.gift_id ?? 0;
    return message;
  }
};

function createBaseGetGiftPlayInfoRsp(): GetGiftPlayInfoRsp {
  return { type: 0, probability: undefined, gifts: [], gift: undefined };
}

export const GetGiftPlayInfoRsp: MessageFns<GetGiftPlayInfoRsp> = {
  fromJSON(object: any): GetGiftPlayInfoRsp {
    return {
      type: isSet(object.type) ? giftPlayTypeFromJSON(object.type) : 0,
      probability: isSet(object.probability) ? GiftPlayProbability.fromJSON(object.probability) : undefined,
      gifts: globalThis.Array.isArray(object?.gifts) ? object.gifts.map((e: any) => GiftInfo.fromJSON(e)) : [],
      gift: isSet(object.gift) ? GiftInfo.fromJSON(object.gift) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetGiftPlayInfoRsp>, I>>(base?: I): GetGiftPlayInfoRsp {
    return GetGiftPlayInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftPlayInfoRsp>, I>>(object: I): GetGiftPlayInfoRsp {
    const message = createBaseGetGiftPlayInfoRsp();
    message.type = object.type ?? 0;
    message.probability =
      object.probability !== undefined && object.probability !== null
        ? GiftPlayProbability.fromPartial(object.probability)
        : undefined;
    message.gifts = object.gifts?.map(e => GiftInfo.fromPartial(e)) || [];
    message.gift = object.gift !== undefined && object.gift !== null ? GiftInfo.fromPartial(object.gift) : undefined;
    return message;
  }
};

function createBaseGiftPlayResult(): GiftPlayResult {
  return { type: 0, picked: undefined, gifts: [], picked_currency: undefined, expand: {} };
}

export const GiftPlayResult: MessageFns<GiftPlayResult> = {
  fromJSON(object: any): GiftPlayResult {
    return {
      type: isSet(object.type) ? giftPlayTypeFromJSON(object.type) : 0,
      picked: isSet(object.picked) ? GiftInfo.fromJSON(object.picked) : undefined,
      gifts: globalThis.Array.isArray(object?.gifts) ? object.gifts.map((e: any) => GiftInfo.fromJSON(e)) : [],
      picked_currency: isSet(object.picked_currency) ? PickedCurrency.fromJSON(object.picked_currency) : undefined,
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GiftPlayResult>, I>>(base?: I): GiftPlayResult {
    return GiftPlayResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlayResult>, I>>(object: I): GiftPlayResult {
    const message = createBaseGiftPlayResult();
    message.type = object.type ?? 0;
    message.picked =
      object.picked !== undefined && object.picked !== null ? GiftInfo.fromPartial(object.picked) : undefined;
    message.gifts = object.gifts?.map(e => GiftInfo.fromPartial(e)) || [];
    message.picked_currency =
      object.picked_currency !== undefined && object.picked_currency !== null
        ? PickedCurrency.fromPartial(object.picked_currency)
        : undefined;
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseGiftPlayResult_ExpandEntry(): GiftPlayResult_ExpandEntry {
  return { key: '', value: '' };
}

export const GiftPlayResult_ExpandEntry: MessageFns<GiftPlayResult_ExpandEntry> = {
  fromJSON(object: any): GiftPlayResult_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftPlayResult_ExpandEntry>, I>>(base?: I): GiftPlayResult_ExpandEntry {
    return GiftPlayResult_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftPlayResult_ExpandEntry>, I>>(object: I): GiftPlayResult_ExpandEntry {
    const message = createBaseGiftPlayResult_ExpandEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePickedCurrency(): PickedCurrency {
  return { currency_type: 0, amount: 0 };
}

export const PickedCurrency: MessageFns<PickedCurrency> = {
  fromJSON(object: any): PickedCurrency {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<PickedCurrency>, I>>(base?: I): PickedCurrency {
    return PickedCurrency.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PickedCurrency>, I>>(object: I): PickedCurrency {
    const message = createBasePickedCurrency();
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    return message;
  }
};

function createBaseNotifySendGift(): NotifySendGift {
  return {
    seq_id: '',
    gift_info: undefined,
    quantity: 0,
    sender: undefined,
    receivers: [],
    time_sent: 0,
    strategy: 0,
    batch_type: 0,
    expand: {},
    context: {},
    scene: 0,
    room_id: '',
    biz_sender: new Uint8Array(0),
    amount_level: undefined
  };
}

export const NotifySendGift: MessageFns<NotifySendGift> = {
  fromJSON(object: any): NotifySendGift {
    return {
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0,
      sender: isSet(object.sender) ? UserInfo.fromJSON(object.sender) : undefined,
      receivers: globalThis.Array.isArray(object?.receivers)
        ? object.receivers.map((e: any) => UserInfo.fromJSON(e))
        : [],
      time_sent: isSet(object.time_sent) ? globalThis.Number(object.time_sent) : 0,
      strategy: isSet(object.strategy) ? giftStrategyFromJSON(object.strategy) : 0,
      batch_type: isSet(object.batch_type) ? giftBatchTypeFromJSON(object.batch_type) : 0,
      expand: isObject(object.expand)
        ? Object.entries(object.expand).reduce<{ [key: number]: string }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = String(value);
            return acc;
          }, {})
        : {},
      context: isObject(object.context)
        ? Object.entries(object.context).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0,
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      biz_sender: isSet(object.biz_sender) ? bytesFromBase64(object.biz_sender) : new Uint8Array(0),
      amount_level: isSet(object.amount_level) ? GiftAmountLevel.fromJSON(object.amount_level) : undefined
    };
  },

  create<I extends Exact<DeepPartial<NotifySendGift>, I>>(base?: I): NotifySendGift {
    return NotifySendGift.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySendGift>, I>>(object: I): NotifySendGift {
    const message = createBaseNotifySendGift();
    message.seq_id = object.seq_id ?? '';
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.quantity = object.quantity ?? 0;
    message.sender =
      object.sender !== undefined && object.sender !== null ? UserInfo.fromPartial(object.sender) : undefined;
    message.receivers = object.receivers?.map(e => UserInfo.fromPartial(e)) || [];
    message.time_sent = object.time_sent ?? 0;
    message.strategy = object.strategy ?? 0;
    message.batch_type = object.batch_type ?? 0;
    message.expand = Object.entries(object.expand ?? {}).reduce<{ [key: number]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.context = Object.entries(object.context ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.scene = object.scene ?? 0;
    message.room_id = object.room_id ?? '';
    message.biz_sender = object.biz_sender ?? new Uint8Array(0);
    message.amount_level =
      object.amount_level !== undefined && object.amount_level !== null
        ? GiftAmountLevel.fromPartial(object.amount_level)
        : undefined;
    return message;
  }
};

function createBaseNotifySendGift_ExpandEntry(): NotifySendGift_ExpandEntry {
  return { key: 0, value: '' };
}

export const NotifySendGift_ExpandEntry: MessageFns<NotifySendGift_ExpandEntry> = {
  fromJSON(object: any): NotifySendGift_ExpandEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifySendGift_ExpandEntry>, I>>(base?: I): NotifySendGift_ExpandEntry {
    return NotifySendGift_ExpandEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySendGift_ExpandEntry>, I>>(object: I): NotifySendGift_ExpandEntry {
    const message = createBaseNotifySendGift_ExpandEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseNotifySendGift_ContextEntry(): NotifySendGift_ContextEntry {
  return { key: '', value: '' };
}

export const NotifySendGift_ContextEntry: MessageFns<NotifySendGift_ContextEntry> = {
  fromJSON(object: any): NotifySendGift_ContextEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifySendGift_ContextEntry>, I>>(base?: I): NotifySendGift_ContextEntry {
    return NotifySendGift_ContextEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifySendGift_ContextEntry>, I>>(object: I): NotifySendGift_ContextEntry {
    const message = createBaseNotifySendGift_ContextEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseNotifyLuckyGiftWinning(): NotifyLuckyGiftWinning {
  return { seq_id: '', user_info: undefined, currency_type: 0, amount: 0, room_id: '' };
}

export const NotifyLuckyGiftWinning: MessageFns<NotifyLuckyGiftWinning> = {
  fromJSON(object: any): NotifyLuckyGiftWinning {
    return {
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifyLuckyGiftWinning>, I>>(base?: I): NotifyLuckyGiftWinning {
    return NotifyLuckyGiftWinning.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyLuckyGiftWinning>, I>>(object: I): NotifyLuckyGiftWinning {
    const message = createBaseNotifyLuckyGiftWinning();
    message.seq_id = object.seq_id ?? '';
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.room_id = object.room_id ?? '';
    return message;
  }
};

function createBaseNotify(): Notify {
  return { send_gift: undefined, lucky_gift_winning: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return {
      send_gift: isSet(object.send_gift) ? NotifySendGift.fromJSON(object.send_gift) : undefined,
      lucky_gift_winning: isSet(object.lucky_gift_winning)
        ? NotifyLuckyGiftWinning.fromJSON(object.lucky_gift_winning)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.send_gift =
      object.send_gift !== undefined && object.send_gift !== null
        ? NotifySendGift.fromPartial(object.send_gift)
        : undefined;
    message.lucky_gift_winning =
      object.lucky_gift_winning !== undefined && object.lucky_gift_winning !== null
        ? NotifyLuckyGiftWinning.fromPartial(object.lucky_gift_winning)
        : undefined;
    return message;
  }
};

function createBaseClaimGiftReq(): ClaimGiftReq {
  return { seq_id: '' };
}

export const ClaimGiftReq: MessageFns<ClaimGiftReq> = {
  fromJSON(object: any): ClaimGiftReq {
    return { seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '' };
  },

  create<I extends Exact<DeepPartial<ClaimGiftReq>, I>>(base?: I): ClaimGiftReq {
    return ClaimGiftReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClaimGiftReq>, I>>(object: I): ClaimGiftReq {
    const message = createBaseClaimGiftReq();
    message.seq_id = object.seq_id ?? '';
    return message;
  }
};

function createBaseClaimGiftRsp(): ClaimGiftRsp {
  return {};
}

export const ClaimGiftRsp: MessageFns<ClaimGiftRsp> = {
  fromJSON(_: any): ClaimGiftRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<ClaimGiftRsp>, I>>(base?: I): ClaimGiftRsp {
    return ClaimGiftRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClaimGiftRsp>, I>>(_: I): ClaimGiftRsp {
    const message = createBaseClaimGiftRsp();
    return message;
  }
};

function createBaseGiftProfit(): GiftProfit {
  return { currency_type: 0, amount: 0, free_amount: 0 };
}

export const GiftProfit: MessageFns<GiftProfit> = {
  fromJSON(object: any): GiftProfit {
    return {
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      free_amount: isSet(object.free_amount) ? globalThis.Number(object.free_amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftProfit>, I>>(base?: I): GiftProfit {
    return GiftProfit.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftProfit>, I>>(object: I): GiftProfit {
    const message = createBaseGiftProfit();
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.free_amount = object.free_amount ?? 0;
    return message;
  }
};

function createBaseGiftReceiveStrategyClaim(): GiftReceiveStrategyClaim {
  return { expire_at: 0 };
}

export const GiftReceiveStrategyClaim: MessageFns<GiftReceiveStrategyClaim> = {
  fromJSON(object: any): GiftReceiveStrategyClaim {
    return { expire_at: isSet(object.expire_at) ? globalThis.Number(object.expire_at) : 0 };
  },

  create<I extends Exact<DeepPartial<GiftReceiveStrategyClaim>, I>>(base?: I): GiftReceiveStrategyClaim {
    return GiftReceiveStrategyClaim.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftReceiveStrategyClaim>, I>>(object: I): GiftReceiveStrategyClaim {
    const message = createBaseGiftReceiveStrategyClaim();
    message.expire_at = object.expire_at ?? 0;
    return message;
  }
};

function createBaseGetGiftTabsReq(): GetGiftTabsReq {
  return { scene: 0, version: 0, extension: {} };
}

export const GetGiftTabsReq: MessageFns<GetGiftTabsReq> = {
  fromJSON(object: any): GetGiftTabsReq {
    return {
      scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      extension: isObject(object.extension)
        ? Object.entries(object.extension).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetGiftTabsReq>, I>>(base?: I): GetGiftTabsReq {
    return GetGiftTabsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftTabsReq>, I>>(object: I): GetGiftTabsReq {
    const message = createBaseGetGiftTabsReq();
    message.scene = object.scene ?? 0;
    message.version = object.version ?? 0;
    message.extension = Object.entries(object.extension ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGetGiftTabsReq_ExtensionEntry(): GetGiftTabsReq_ExtensionEntry {
  return { key: '', value: '' };
}

export const GetGiftTabsReq_ExtensionEntry: MessageFns<GetGiftTabsReq_ExtensionEntry> = {
  fromJSON(object: any): GetGiftTabsReq_ExtensionEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetGiftTabsReq_ExtensionEntry>, I>>(base?: I): GetGiftTabsReq_ExtensionEntry {
    return GetGiftTabsReq_ExtensionEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftTabsReq_ExtensionEntry>, I>>(
    object: I
  ): GetGiftTabsReq_ExtensionEntry {
    const message = createBaseGetGiftTabsReq_ExtensionEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetGiftTabsRsp(): GetGiftTabsRsp {
  return { gift_tabs: [], version: 0 };
}

export const GetGiftTabsRsp: MessageFns<GetGiftTabsRsp> = {
  fromJSON(object: any): GetGiftTabsRsp {
    return {
      gift_tabs: globalThis.Array.isArray(object?.gift_tabs)
        ? object.gift_tabs.map((e: any) => GiftTab.fromJSON(e))
        : [],
      version: isSet(object.version) ? globalThis.Number(object.version) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetGiftTabsRsp>, I>>(base?: I): GetGiftTabsRsp {
    return GetGiftTabsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftTabsRsp>, I>>(object: I): GetGiftTabsRsp {
    const message = createBaseGetGiftTabsRsp();
    message.gift_tabs = object.gift_tabs?.map(e => GiftTab.fromPartial(e)) || [];
    message.version = object.version ?? 0;
    return message;
  }
};

function createBaseListTabGiftsReq(): ListTabGiftsReq {
  return { page: undefined, tab_id: 0, scene: 0 };
}

export const ListTabGiftsReq: MessageFns<ListTabGiftsReq> = {
  fromJSON(object: any): ListTabGiftsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      tab_id: isSet(object.tab_id) ? globalThis.Number(object.tab_id) : 0,
      scene: isSet(object.scene) ? giftSceneFromJSON(object.scene) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListTabGiftsReq>, I>>(base?: I): ListTabGiftsReq {
    return ListTabGiftsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTabGiftsReq>, I>>(object: I): ListTabGiftsReq {
    const message = createBaseListTabGiftsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.tab_id = object.tab_id ?? 0;
    message.scene = object.scene ?? 0;
    return message;
  }
};

function createBaseListTabGiftsRsp(): ListTabGiftsRsp {
  return { page: undefined, gift_infos: [] };
}

export const ListTabGiftsRsp: MessageFns<ListTabGiftsRsp> = {
  fromJSON(object: any): ListTabGiftsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      gift_infos: globalThis.Array.isArray(object?.gift_infos)
        ? object.gift_infos.map((e: any) => GiftInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListTabGiftsRsp>, I>>(base?: I): ListTabGiftsRsp {
    return ListTabGiftsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTabGiftsRsp>, I>>(object: I): ListTabGiftsRsp {
    const message = createBaseListTabGiftsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.gift_infos = object.gift_infos?.map(e => GiftInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListRoomGiftStatsReq(): ListRoomGiftStatsReq {
  return { room_id: '', start_date: '', end_date: '', gift_types: [] };
}

export const ListRoomGiftStatsReq: MessageFns<ListRoomGiftStatsReq> = {
  fromJSON(object: any): ListRoomGiftStatsReq {
    return {
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      start_date: isSet(object.start_date) ? globalThis.String(object.start_date) : '',
      end_date: isSet(object.end_date) ? globalThis.String(object.end_date) : '',
      gift_types: globalThis.Array.isArray(object?.gift_types)
        ? object.gift_types.map((e: any) => giftTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomGiftStatsReq>, I>>(base?: I): ListRoomGiftStatsReq {
    return ListRoomGiftStatsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomGiftStatsReq>, I>>(object: I): ListRoomGiftStatsReq {
    const message = createBaseListRoomGiftStatsReq();
    message.room_id = object.room_id ?? '';
    message.start_date = object.start_date ?? '';
    message.end_date = object.end_date ?? '';
    message.gift_types = object.gift_types?.map(e => e) || [];
    return message;
  }
};

function createBaseListRoomGiftStatsRsp(): ListRoomGiftStatsRsp {
  return { items: [] };
}

export const ListRoomGiftStatsRsp: MessageFns<ListRoomGiftStatsRsp> = {
  fromJSON(object: any): ListRoomGiftStatsRsp {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => RoomGiftStatsRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRoomGiftStatsRsp>, I>>(base?: I): ListRoomGiftStatsRsp {
    return ListRoomGiftStatsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRoomGiftStatsRsp>, I>>(object: I): ListRoomGiftStatsRsp {
    const message = createBaseListRoomGiftStatsRsp();
    message.items = object.items?.map(e => RoomGiftStatsRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRoomGiftStatsRecord(): RoomGiftStatsRecord {
  return { room_id: '', date: '', gift_type: 0, send_count: 0, amount: 0 };
}

export const RoomGiftStatsRecord: MessageFns<RoomGiftStatsRecord> = {
  fromJSON(object: any): RoomGiftStatsRecord {
    return {
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      date: isSet(object.date) ? globalThis.String(object.date) : '',
      gift_type: isSet(object.gift_type) ? giftTypeFromJSON(object.gift_type) : 0,
      send_count: isSet(object.send_count) ? globalThis.Number(object.send_count) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomGiftStatsRecord>, I>>(base?: I): RoomGiftStatsRecord {
    return RoomGiftStatsRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomGiftStatsRecord>, I>>(object: I): RoomGiftStatsRecord {
    const message = createBaseRoomGiftStatsRecord();
    message.room_id = object.room_id ?? '';
    message.date = object.date ?? '';
    message.gift_type = object.gift_type ?? 0;
    message.send_count = object.send_count ?? 0;
    message.amount = object.amount ?? 0;
    return message;
  }
};

function createBaseListGiftsReq(): ListGiftsReq {
  return { ids: [], biz_ext_args: undefined };
}

export const ListGiftsReq: MessageFns<ListGiftsReq> = {
  fromJSON(object: any): ListGiftsReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      biz_ext_args: isSet(object.biz_ext_args) ? BizExtArgs.fromJSON(object.biz_ext_args) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftsReq>, I>>(base?: I): ListGiftsReq {
    return ListGiftsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftsReq>, I>>(object: I): ListGiftsReq {
    const message = createBaseListGiftsReq();
    message.ids = object.ids?.map(e => e) || [];
    message.biz_ext_args =
      object.biz_ext_args !== undefined && object.biz_ext_args !== null
        ? BizExtArgs.fromPartial(object.biz_ext_args)
        : undefined;
    return message;
  }
};

function createBaseListGiftsRsp(): ListGiftsRsp {
  return { gift_infos: [] };
}

export const ListGiftsRsp: MessageFns<ListGiftsRsp> = {
  fromJSON(object: any): ListGiftsRsp {
    return {
      gift_infos: globalThis.Array.isArray(object?.gift_infos)
        ? object.gift_infos.map((e: any) => GiftInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGiftsRsp>, I>>(base?: I): ListGiftsRsp {
    return ListGiftsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftsRsp>, I>>(object: I): ListGiftsRsp {
    const message = createBaseListGiftsRsp();
    message.gift_infos = object.gift_infos?.map(e => GiftInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBizExtArgs(): BizExtArgs {
  return { wish_gift_type: 0 };
}

export const BizExtArgs: MessageFns<BizExtArgs> = {
  fromJSON(object: any): BizExtArgs {
    return { wish_gift_type: isSet(object.wish_gift_type) ? wishGiftTypeFromJSON(object.wish_gift_type) : 0 };
  },

  create<I extends Exact<DeepPartial<BizExtArgs>, I>>(base?: I): BizExtArgs {
    return BizExtArgs.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizExtArgs>, I>>(object: I): BizExtArgs {
    const message = createBaseBizExtArgs();
    message.wish_gift_type = object.wish_gift_type ?? 0;
    return message;
  }
};

/**
 * 礼物服务
 * serviceName: api.micro.social.revenue
 * smicro:spath=gitit.cc/social/components-service/social-revenue/gift
 */
export type GiftDefinition = typeof GiftDefinition;
export const GiftDefinition = {
  name: 'Gift',
  fullName: 'pbrevenue.Gift',
  methods: {
    /** 获取礼物面板 */
    getGiftPanel: {
      name: 'GetGiftPanel',
      requestType: GetGiftPanelReq,
      requestStream: false,
      responseType: GetGiftPanelRsp,
      responseStream: false,
      options: {}
    },
    /** 获取礼物包裹 */
    getGiftBag: {
      name: 'GetGiftBag',
      requestType: GetGiftBagReq,
      requestStream: false,
      responseType: GetGiftBagRsp,
      responseStream: false,
      options: {}
    },
    /** 赠送礼物 */
    sendGift: {
      name: 'SendGift',
      requestType: SendGiftReq,
      requestStream: false,
      responseType: SendGiftRsp,
      responseStream: false,
      options: {}
    },
    /** 查询礼物记录 */
    listGiftRecord: {
      name: 'ListGiftRecord',
      requestType: ListGiftRecordReq,
      requestStream: false,
      responseType: ListGiftRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 获取特殊礼物的统计数据 */
    getGiftStats: {
      name: 'GetGiftStats',
      requestType: GetGiftStatsReq,
      requestStream: false,
      responseType: GetGiftStatsRsp,
      responseStream: false,
      options: {}
    },
    /** 分页获取收送礼统计 */
    listGiftStats: {
      name: 'ListGiftStats',
      requestType: ListGiftStatsReq,
      requestStream: false,
      responseType: ListGiftStatsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取业务自定义送礼信息 */
    getCustomizedSendGiftInfo: {
      name: 'GetCustomizedSendGiftInfo',
      requestType: GetCustomizedSendGiftInfoReq,
      requestStream: false,
      responseType: GetCustomizedSendGiftInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 领取礼物 */
    claimGift: {
      name: 'ClaimGift',
      requestType: ClaimGiftReq,
      requestStream: false,
      responseType: ClaimGiftRsp,
      responseStream: false,
      options: {}
    },
    /** 查询礼物玩法信息 */
    getGiftPlayInfo: {
      name: 'GetGiftPlayInfo',
      requestType: GetGiftPlayInfoReq,
      requestStream: false,
      responseType: GetGiftPlayInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 离线资源信息查询 */
    getGiftOfflineResInfo: {
      name: 'GetGiftOfflineResInfo',
      requestType: GetGiftOfflineResInfoReq,
      requestStream: false,
      responseType: GetGiftOfflineResInfoResp,
      responseStream: false,
      options: {}
    },
    /** 获取礼物tab列表 */
    getGiftTabs: {
      name: 'GetGiftTabs',
      requestType: GetGiftTabsReq,
      requestStream: false,
      responseType: GetGiftTabsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取tab下礼物列表 */
    listTabGifts: {
      name: 'ListTabGifts',
      requestType: ListTabGiftsReq,
      requestStream: false,
      responseType: ListTabGiftsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取房间维度送礼统计 */
    listRoomGiftStats: {
      name: 'ListRoomGiftStats',
      requestType: ListRoomGiftStatsReq,
      requestStream: false,
      responseType: ListRoomGiftStatsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取礼物列表 */
    listGifts: {
      name: 'ListGifts',
      requestType: ListGiftsReq,
      requestStream: false,
      responseType: ListGiftsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
