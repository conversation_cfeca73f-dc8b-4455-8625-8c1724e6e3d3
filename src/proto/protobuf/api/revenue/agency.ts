// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/agency.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.revenue';

/** 币商分级 */
export enum GoldAgencyLevel {
  /** GOLD_AGENCY_LEVEL_NONE - 非币商 */
  GOLD_AGENCY_LEVEL_NONE = 0,
  /** GOLD_AGENCY_LEVEL_PRIMARY - 一级币商 */
  GOLD_AGENCY_LEVEL_PRIMARY = 1,
  /** GOLD_AGENCY_LEVEL_SECONDARY - 二级币商 */
  GOLD_AGENCY_LEVEL_SECONDARY = 2,
  /** GOLD_AGENCY_LEVEL_NORMAL - ... 更多等级币商, 但目前只有两级 */
  GOLD_AGENCY_LEVEL_NORMAL = 99,
  UNRECOGNIZED = -1
}

export function goldAgencyLevelFromJSON(object: any): GoldAgencyLevel {
  switch (object) {
    case 0:
    case 'GOLD_AGENCY_LEVEL_NONE':
      return GoldAgencyLevel.GOLD_AGENCY_LEVEL_NONE;
    case 1:
    case 'GOLD_AGENCY_LEVEL_PRIMARY':
      return GoldAgencyLevel.GOLD_AGENCY_LEVEL_PRIMARY;
    case 2:
    case 'GOLD_AGENCY_LEVEL_SECONDARY':
      return GoldAgencyLevel.GOLD_AGENCY_LEVEL_SECONDARY;
    case 99:
    case 'GOLD_AGENCY_LEVEL_NORMAL':
      return GoldAgencyLevel.GOLD_AGENCY_LEVEL_NORMAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return GoldAgencyLevel.UNRECOGNIZED;
  }
}

/** 币商信息 */
export interface GoldAgency {
  /** 币商ID */
  id: number;
  /** 币商分级 */
  level: number;
  /** 业务UID */
  uid: number;
  /** 上级币商UID，如果是一级币商或普通币商则为 0 */
  superior_uid: number;
  /** 累计充值金额 */
  recharge_amount: number;
  /** 累计交易次数 */
  transaction_count: number;
  /** 初始化交易次数 */
  initial_transaction_count: number;
  /** 最新充值时间 */
  last_recharge_at: number;
  /** 可售卖国家列表 */
  sellable_countries: string[];
  /** 备注(对用户不可见) */
  remark: string;
  /** 是否已对用户隐藏 */
  hidden: boolean;
  /** 创建时间 */
  create_at: number;
  /** 更新时间 */
  update_at: number;
}

/** 根据uid查询币商信息 */
export interface ListGoldAgencyByUidsReq {
  /** uid 列表 */
  uids: number[];
  /** 不读取缓存, 强制读数据库最新数据. */
  no_cache: boolean;
}

/** 根据uid查询币商信息 */
export interface ListGoldAgencyByUidsRsp {
  /** 币商信息 */
  gold_agencies: GoldAgency[];
}

function createBaseGoldAgency(): GoldAgency {
  return {
    id: 0,
    level: 0,
    uid: 0,
    superior_uid: 0,
    recharge_amount: 0,
    transaction_count: 0,
    initial_transaction_count: 0,
    last_recharge_at: 0,
    sellable_countries: [],
    remark: '',
    hidden: false,
    create_at: 0,
    update_at: 0
  };
}

export const GoldAgency: MessageFns<GoldAgency> = {
  fromJSON(object: any): GoldAgency {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      superior_uid: isSet(object.superior_uid) ? globalThis.Number(object.superior_uid) : 0,
      recharge_amount: isSet(object.recharge_amount) ? globalThis.Number(object.recharge_amount) : 0,
      transaction_count: isSet(object.transaction_count) ? globalThis.Number(object.transaction_count) : 0,
      initial_transaction_count: isSet(object.initial_transaction_count)
        ? globalThis.Number(object.initial_transaction_count)
        : 0,
      last_recharge_at: isSet(object.last_recharge_at) ? globalThis.Number(object.last_recharge_at) : 0,
      sellable_countries: globalThis.Array.isArray(object?.sellable_countries)
        ? object.sellable_countries.map((e: any) => globalThis.String(e))
        : [],
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      hidden: isSet(object.hidden) ? globalThis.Boolean(object.hidden) : false,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<GoldAgency>, I>>(base?: I): GoldAgency {
    return GoldAgency.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoldAgency>, I>>(object: I): GoldAgency {
    const message = createBaseGoldAgency();
    message.id = object.id ?? 0;
    message.level = object.level ?? 0;
    message.uid = object.uid ?? 0;
    message.superior_uid = object.superior_uid ?? 0;
    message.recharge_amount = object.recharge_amount ?? 0;
    message.transaction_count = object.transaction_count ?? 0;
    message.initial_transaction_count = object.initial_transaction_count ?? 0;
    message.last_recharge_at = object.last_recharge_at ?? 0;
    message.sellable_countries = object.sellable_countries?.map(e => e) || [];
    message.remark = object.remark ?? '';
    message.hidden = object.hidden ?? false;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    return message;
  }
};

function createBaseListGoldAgencyByUidsReq(): ListGoldAgencyByUidsReq {
  return { uids: [], no_cache: false };
}

export const ListGoldAgencyByUidsReq: MessageFns<ListGoldAgencyByUidsReq> = {
  fromJSON(object: any): ListGoldAgencyByUidsReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      no_cache: isSet(object.no_cache) ? globalThis.Boolean(object.no_cache) : false
    };
  },

  create<I extends Exact<DeepPartial<ListGoldAgencyByUidsReq>, I>>(base?: I): ListGoldAgencyByUidsReq {
    return ListGoldAgencyByUidsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoldAgencyByUidsReq>, I>>(object: I): ListGoldAgencyByUidsReq {
    const message = createBaseListGoldAgencyByUidsReq();
    message.uids = object.uids?.map(e => e) || [];
    message.no_cache = object.no_cache ?? false;
    return message;
  }
};

function createBaseListGoldAgencyByUidsRsp(): ListGoldAgencyByUidsRsp {
  return { gold_agencies: [] };
}

export const ListGoldAgencyByUidsRsp: MessageFns<ListGoldAgencyByUidsRsp> = {
  fromJSON(object: any): ListGoldAgencyByUidsRsp {
    return {
      gold_agencies: globalThis.Array.isArray(object?.gold_agencies)
        ? object.gold_agencies.map((e: any) => GoldAgency.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGoldAgencyByUidsRsp>, I>>(base?: I): ListGoldAgencyByUidsRsp {
    return ListGoldAgencyByUidsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoldAgencyByUidsRsp>, I>>(object: I): ListGoldAgencyByUidsRsp {
    const message = createBaseListGoldAgencyByUidsRsp();
    message.gold_agencies = object.gold_agencies?.map(e => GoldAgency.fromPartial(e)) || [];
    return message;
  }
};

/** 社交中台币商服务 */
export type GoldagencyDefinition = typeof GoldagencyDefinition;
export const GoldagencyDefinition = {
  name: 'Goldagency',
  fullName: 'comm.api.revenue.Goldagency',
  methods: {
    /** 根据uid查询币商信息 */
    listGoldAgencyByUids: {
      name: 'ListGoldAgencyByUids',
      requestType: ListGoldAgencyByUidsReq,
      requestStream: false,
      responseType: ListGoldAgencyByUidsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
