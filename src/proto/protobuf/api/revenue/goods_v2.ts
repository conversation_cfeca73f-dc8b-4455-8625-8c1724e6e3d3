// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/revenue/goods_v2.proto

/* eslint-disable */
import { GoodsInfo, GoodsType, goodsTypeFromJSON, PackageItem } from './common';

export const protobufPackage = 'api.pbgoods';

export interface GetGoodsInfoReq {
  /** 商品id */
  goods_id: number;
}

export interface GetGoodsInfoRsp {
  /** 商品（SKU）列表 */
  goods_info: GoodsInfo | undefined;
  /** 礼包信息 */
  package_items: PackageItem | undefined;
}

export interface BatchGetGoodsByIdsReq {
  goods_ids: number[];
}

export interface BatchGetGoodsByIdsRsp {
  goods_map: { [key: number]: GoodsDetail };
}

export interface BatchGetGoodsByIdsRsp_GoodsMapEntry {
  key: number;
  value: GoodsDetail | undefined;
}

export interface GoodsDetail {
  /** 商品信息 */
  goods_info: GoodsInfo | undefined;
  /** 非必须，商品对应礼包信息，非礼包类型不返回 */
  package_items: PackageItem | undefined;
}

export interface ListGoodsByTypeReq {
  goods_type: GoodsType;
}

export interface ListGoodsByTypeRsp {
  /** 商品信息列表 */
  goods_list: GoodsDetail[];
}

function createBaseGetGoodsInfoReq(): GetGoodsInfoReq {
  return { goods_id: 0 };
}

export const GetGoodsInfoReq: MessageFns<GetGoodsInfoReq> = {
  fromJSON(object: any): GetGoodsInfoReq {
    return { goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGoodsInfoReq>, I>>(base?: I): GetGoodsInfoReq {
    return GetGoodsInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGoodsInfoReq>, I>>(object: I): GetGoodsInfoReq {
    const message = createBaseGetGoodsInfoReq();
    message.goods_id = object.goods_id ?? 0;
    return message;
  }
};

function createBaseGetGoodsInfoRsp(): GetGoodsInfoRsp {
  return { goods_info: undefined, package_items: undefined };
}

export const GetGoodsInfoRsp: MessageFns<GetGoodsInfoRsp> = {
  fromJSON(object: any): GetGoodsInfoRsp {
    return {
      goods_info: isSet(object.goods_info) ? GoodsInfo.fromJSON(object.goods_info) : undefined,
      package_items: isSet(object.package_items) ? PackageItem.fromJSON(object.package_items) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetGoodsInfoRsp>, I>>(base?: I): GetGoodsInfoRsp {
    return GetGoodsInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGoodsInfoRsp>, I>>(object: I): GetGoodsInfoRsp {
    const message = createBaseGetGoodsInfoRsp();
    message.goods_info =
      object.goods_info !== undefined && object.goods_info !== null
        ? GoodsInfo.fromPartial(object.goods_info)
        : undefined;
    message.package_items =
      object.package_items !== undefined && object.package_items !== null
        ? PackageItem.fromPartial(object.package_items)
        : undefined;
    return message;
  }
};

function createBaseBatchGetGoodsByIdsReq(): BatchGetGoodsByIdsReq {
  return { goods_ids: [] };
}

export const BatchGetGoodsByIdsReq: MessageFns<BatchGetGoodsByIdsReq> = {
  fromJSON(object: any): BatchGetGoodsByIdsReq {
    return {
      goods_ids: globalThis.Array.isArray(object?.goods_ids)
        ? object.goods_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGoodsByIdsReq>, I>>(base?: I): BatchGetGoodsByIdsReq {
    return BatchGetGoodsByIdsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGoodsByIdsReq>, I>>(object: I): BatchGetGoodsByIdsReq {
    const message = createBaseBatchGetGoodsByIdsReq();
    message.goods_ids = object.goods_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetGoodsByIdsRsp(): BatchGetGoodsByIdsRsp {
  return { goods_map: {} };
}

export const BatchGetGoodsByIdsRsp: MessageFns<BatchGetGoodsByIdsRsp> = {
  fromJSON(object: any): BatchGetGoodsByIdsRsp {
    return {
      goods_map: isObject(object.goods_map)
        ? Object.entries(object.goods_map).reduce<{ [key: number]: GoodsDetail }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = GoodsDetail.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGoodsByIdsRsp>, I>>(base?: I): BatchGetGoodsByIdsRsp {
    return BatchGetGoodsByIdsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGoodsByIdsRsp>, I>>(object: I): BatchGetGoodsByIdsRsp {
    const message = createBaseBatchGetGoodsByIdsRsp();
    message.goods_map = Object.entries(object.goods_map ?? {}).reduce<{ [key: number]: GoodsDetail }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = GoodsDetail.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchGetGoodsByIdsRsp_GoodsMapEntry(): BatchGetGoodsByIdsRsp_GoodsMapEntry {
  return { key: 0, value: undefined };
}

export const BatchGetGoodsByIdsRsp_GoodsMapEntry: MessageFns<BatchGetGoodsByIdsRsp_GoodsMapEntry> = {
  fromJSON(object: any): BatchGetGoodsByIdsRsp_GoodsMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? GoodsDetail.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGoodsByIdsRsp_GoodsMapEntry>, I>>(
    base?: I
  ): BatchGetGoodsByIdsRsp_GoodsMapEntry {
    return BatchGetGoodsByIdsRsp_GoodsMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGoodsByIdsRsp_GoodsMapEntry>, I>>(
    object: I
  ): BatchGetGoodsByIdsRsp_GoodsMapEntry {
    const message = createBaseBatchGetGoodsByIdsRsp_GoodsMapEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? GoodsDetail.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseGoodsDetail(): GoodsDetail {
  return { goods_info: undefined, package_items: undefined };
}

export const GoodsDetail: MessageFns<GoodsDetail> = {
  fromJSON(object: any): GoodsDetail {
    return {
      goods_info: isSet(object.goods_info) ? GoodsInfo.fromJSON(object.goods_info) : undefined,
      package_items: isSet(object.package_items) ? PackageItem.fromJSON(object.package_items) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GoodsDetail>, I>>(base?: I): GoodsDetail {
    return GoodsDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsDetail>, I>>(object: I): GoodsDetail {
    const message = createBaseGoodsDetail();
    message.goods_info =
      object.goods_info !== undefined && object.goods_info !== null
        ? GoodsInfo.fromPartial(object.goods_info)
        : undefined;
    message.package_items =
      object.package_items !== undefined && object.package_items !== null
        ? PackageItem.fromPartial(object.package_items)
        : undefined;
    return message;
  }
};

function createBaseListGoodsByTypeReq(): ListGoodsByTypeReq {
  return { goods_type: 0 };
}

export const ListGoodsByTypeReq: MessageFns<ListGoodsByTypeReq> = {
  fromJSON(object: any): ListGoodsByTypeReq {
    return { goods_type: isSet(object.goods_type) ? goodsTypeFromJSON(object.goods_type) : 0 };
  },

  create<I extends Exact<DeepPartial<ListGoodsByTypeReq>, I>>(base?: I): ListGoodsByTypeReq {
    return ListGoodsByTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoodsByTypeReq>, I>>(object: I): ListGoodsByTypeReq {
    const message = createBaseListGoodsByTypeReq();
    message.goods_type = object.goods_type ?? 0;
    return message;
  }
};

function createBaseListGoodsByTypeRsp(): ListGoodsByTypeRsp {
  return { goods_list: [] };
}

export const ListGoodsByTypeRsp: MessageFns<ListGoodsByTypeRsp> = {
  fromJSON(object: any): ListGoodsByTypeRsp {
    return {
      goods_list: globalThis.Array.isArray(object?.goods_list)
        ? object.goods_list.map((e: any) => GoodsDetail.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListGoodsByTypeRsp>, I>>(base?: I): ListGoodsByTypeRsp {
    return ListGoodsByTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGoodsByTypeRsp>, I>>(object: I): ListGoodsByTypeRsp {
    const message = createBaseListGoodsByTypeRsp();
    message.goods_list = object.goods_list?.map(e => GoodsDetail.fromPartial(e)) || [];
    return message;
  }
};

export type GoodsV2Definition = typeof GoodsV2Definition;
export const GoodsV2Definition = {
  name: 'GoodsV2',
  fullName: 'api.pbgoods.GoodsV2',
  methods: {
    /** 根据id查询商品信息 */
    getGoodsById: {
      name: 'GetGoodsById',
      requestType: GetGoodsInfoReq,
      requestStream: false,
      responseType: GetGoodsInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询商品信息 */
    batchGetGoodsByIds: {
      name: 'BatchGetGoodsByIds',
      requestType: BatchGetGoodsByIdsReq,
      requestStream: false,
      responseType: BatchGetGoodsByIdsRsp,
      responseStream: false,
      options: {}
    },
    /** 根据类型查询商品列表 */
    listGoodsByType: {
      name: 'ListGoodsByType',
      requestType: ListGoodsByTypeReq,
      requestStream: false,
      responseType: ListGoodsByTypeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
