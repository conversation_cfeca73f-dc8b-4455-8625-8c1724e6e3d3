// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/common.proto

/* eslint-disable */

export const protobufPackage = 'scommon';

/** 通用的分页设置 */
export interface Page {
  /** 总数，客户端很多场景需要展示总数 */
  total: number;
  /** 从0开始，客户端带上offset，服务端返回数据的时候要修改offset的值，加上当前数据的条目数 */
  offset: number;
  /** 客户端填充，表示请求多少，-1表示请求所有（服务的要判断是否允许），0表示由服务的决定分页大小 */
  limit: number;
  /** 是否还有更多，服务端填 */
  has_more: boolean;
  /**
   * 对于同一个页面，初次请求的时候不用填，后续的请求填服务端的响应即可。
   * 用于表征一次会话，不同的会话不能混用
   */
  session: string;
}

/** 通用的排序设置, 建议使用 Sorts. */
export interface Sort {
  /**
   * 服务端会定义一个枚举用于表征所有可以做排序的字段，这里的field填对应的枚举值。
   *
   * 设计考量: 这里定义成 int32 而不是 string 来直接传递排序字段名称是为了增加使用门槛让使用者思考字段是否应该拿来排序,
   * 通常具体的接口需要将可以排序的字段通过枚举定义出来, 前端传枚举的数字值, 后端映射到排序字段名称, 以便思考字段是否有索引.
   */
  field: number;
  /** 排序顺序 */
  order: Sort_Order;
}

/** 排序顺序 */
export enum Sort_Order {
  /** ORDER_NONE - 无意义 */
  ORDER_NONE = 0,
  /** ORDER_ASC - 正序 */
  ORDER_ASC = 1,
  /** ORDER_DESC - 反序 */
  ORDER_DESC = 2,
  UNRECOGNIZED = -1
}

export function sort_OrderFromJSON(object: any): Sort_Order {
  switch (object) {
    case 0:
    case 'ORDER_NONE':
      return Sort_Order.ORDER_NONE;
    case 1:
    case 'ORDER_ASC':
      return Sort_Order.ORDER_ASC;
    case 2:
    case 'ORDER_DESC':
      return Sort_Order.ORDER_DESC;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Sort_Order.UNRECOGNIZED;
  }
}

/** 通用的排序设置列表, 支持多个字段排序. */
export interface Sorts {
  sorts: Sort[];
}

function createBasePage(): Page {
  return { total: 0, offset: 0, limit: 0, has_more: false, session: '' };
}

export const Page: MessageFns<Page> = {
  fromJSON(object: any): Page {
    return {
      total: isSet(object.total) ? globalThis.Number(object.total) : 0,
      offset: isSet(object.offset) ? globalThis.Number(object.offset) : 0,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0,
      has_more: isSet(object.has_more) ? globalThis.Boolean(object.has_more) : false,
      session: isSet(object.session) ? globalThis.String(object.session) : ''
    };
  },

  create<I extends Exact<DeepPartial<Page>, I>>(base?: I): Page {
    return Page.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Page>, I>>(object: I): Page {
    const message = createBasePage();
    message.total = object.total ?? 0;
    message.offset = object.offset ?? 0;
    message.limit = object.limit ?? 0;
    message.has_more = object.has_more ?? false;
    message.session = object.session ?? '';
    return message;
  }
};

function createBaseSort(): Sort {
  return { field: 0, order: 0 };
}

export const Sort: MessageFns<Sort> = {
  fromJSON(object: any): Sort {
    return {
      field: isSet(object.field) ? globalThis.Number(object.field) : 0,
      order: isSet(object.order) ? sort_OrderFromJSON(object.order) : 0
    };
  },

  create<I extends Exact<DeepPartial<Sort>, I>>(base?: I): Sort {
    return Sort.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Sort>, I>>(object: I): Sort {
    const message = createBaseSort();
    message.field = object.field ?? 0;
    message.order = object.order ?? 0;
    return message;
  }
};

function createBaseSorts(): Sorts {
  return { sorts: [] };
}

export const Sorts: MessageFns<Sorts> = {
  fromJSON(object: any): Sorts {
    return { sorts: globalThis.Array.isArray(object?.sorts) ? object.sorts.map((e: any) => Sort.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<Sorts>, I>>(base?: I): Sorts {
    return Sorts.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Sorts>, I>>(object: I): Sorts {
    const message = createBaseSorts();
    message.sorts = object.sorts?.map(e => Sort.fromPartial(e)) || [];
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
