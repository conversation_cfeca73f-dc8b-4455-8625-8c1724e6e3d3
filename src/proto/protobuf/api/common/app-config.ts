// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/app-config.proto

/* eslint-disable */

export const protobufPackage = 'protobuf.api.common.appconfig';

export interface GetConfigReq {
  /**
   * 各个key的版本信息，第一次请求，这个应该是空的。如果key的版本没变化，则不会返回这个key的信息
   * 用于减少通信量
   */
  key_versions: { [key: string]: number };
  /** 部分公参变化之后，请求需要重新获取，至于那些公参会影响，服务端来控制。第一次请求这个传空，后续请求则传之前服务端返回的。 */
  pub_para: string;
  /** 区分新老版本，新版传true */
  pb: boolean;
}

export interface GetConfigReq_KeyVersionsEntry {
  key: string;
  value: number;
}

export interface GetConfigRsp {
  /** 有变化的配置列表 */
  configs: Config[];
  /** 删除的配置key，用于某些key要删除恢复默认值的情况 */
  delete_keys: string[];
  /** 影响的公参，服务端控制 */
  pub_para: string;
}

export interface Config {
  key: string;
  version: number;
  /** 值的json格式 */
  value: string;
  /** // 值的的pb格式 */
  pb: Uint8Array;
  /** 非登录态也返回 */
  allow_not_login: boolean;
}

function createBaseGetConfigReq(): GetConfigReq {
  return { key_versions: {}, pub_para: '', pb: false };
}

export const GetConfigReq: MessageFns<GetConfigReq> = {
  fromJSON(object: any): GetConfigReq {
    return {
      key_versions: isObject(object.key_versions)
        ? Object.entries(object.key_versions).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      pub_para: isSet(object.pub_para) ? globalThis.String(object.pub_para) : '',
      pb: isSet(object.pb) ? globalThis.Boolean(object.pb) : false
    };
  },

  create<I extends Exact<DeepPartial<GetConfigReq>, I>>(base?: I): GetConfigReq {
    return GetConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigReq>, I>>(object: I): GetConfigReq {
    const message = createBaseGetConfigReq();
    message.key_versions = Object.entries(object.key_versions ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.pub_para = object.pub_para ?? '';
    message.pb = object.pb ?? false;
    return message;
  }
};

function createBaseGetConfigReq_KeyVersionsEntry(): GetConfigReq_KeyVersionsEntry {
  return { key: '', value: 0 };
}

export const GetConfigReq_KeyVersionsEntry: MessageFns<GetConfigReq_KeyVersionsEntry> = {
  fromJSON(object: any): GetConfigReq_KeyVersionsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetConfigReq_KeyVersionsEntry>, I>>(base?: I): GetConfigReq_KeyVersionsEntry {
    return GetConfigReq_KeyVersionsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigReq_KeyVersionsEntry>, I>>(
    object: I
  ): GetConfigReq_KeyVersionsEntry {
    const message = createBaseGetConfigReq_KeyVersionsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseGetConfigRsp(): GetConfigRsp {
  return { configs: [], delete_keys: [], pub_para: '' };
}

export const GetConfigRsp: MessageFns<GetConfigRsp> = {
  fromJSON(object: any): GetConfigRsp {
    return {
      configs: globalThis.Array.isArray(object?.configs) ? object.configs.map((e: any) => Config.fromJSON(e)) : [],
      delete_keys: globalThis.Array.isArray(object?.delete_keys)
        ? object.delete_keys.map((e: any) => globalThis.String(e))
        : [],
      pub_para: isSet(object.pub_para) ? globalThis.String(object.pub_para) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetConfigRsp>, I>>(base?: I): GetConfigRsp {
    return GetConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigRsp>, I>>(object: I): GetConfigRsp {
    const message = createBaseGetConfigRsp();
    message.configs = object.configs?.map(e => Config.fromPartial(e)) || [];
    message.delete_keys = object.delete_keys?.map(e => e) || [];
    message.pub_para = object.pub_para ?? '';
    return message;
  }
};

function createBaseConfig(): Config {
  return { key: '', version: 0, value: '', pb: new Uint8Array(0), allow_not_login: false };
}

export const Config: MessageFns<Config> = {
  fromJSON(object: any): Config {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      pb: isSet(object.pb) ? bytesFromBase64(object.pb) : new Uint8Array(0),
      allow_not_login: isSet(object.allow_not_login) ? globalThis.Boolean(object.allow_not_login) : false
    };
  },

  create<I extends Exact<DeepPartial<Config>, I>>(base?: I): Config {
    return Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Config>, I>>(object: I): Config {
    const message = createBaseConfig();
    message.key = object.key ?? '';
    message.version = object.version ?? 0;
    message.value = object.value ?? '';
    message.pb = object.pb ?? new Uint8Array(0);
    message.allow_not_login = object.allow_not_login ?? false;
    return message;
  }
};

/** serviceName: social-config */
export type APPConfigDefinition = typeof APPConfigDefinition;
export const APPConfigDefinition = {
  name: 'APPConfig',
  fullName: 'protobuf.api.common.appconfig.APPConfig',
  methods: {
    /** 读取配置 */
    getConfig: {
      name: 'GetConfig',
      requestType: GetConfigReq,
      requestStream: false,
      responseType: GetConfigRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
