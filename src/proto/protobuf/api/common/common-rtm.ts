// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/common-rtm.proto

/* eslint-disable */
import { CompressType, compressTypeFromJSON } from './common-net';

export const protobufPackage = 'scommon';

/**
 * 为了统一 RTM 和自研的单广播通道下发的内容结构,
 * 每个服务都只有一个 Notify 的 Protobuf Message 定义,
 * 但是对于一个客户端则有多个 Notify, 因为一个客户端不止对接一个业务服务还有多个中台服务,
 * 所以通过 RTM 下发消息的时候需要带上消息到底是哪个服务的 Notify.
 */
export interface RtmPayload {
  /** 通知包名 */
  notify_pkg: string;
  /** 通知数据 */
  notify_data: Uint8Array;
  /** 去重序号 */
  seq_id: string;
  /** 时间戳, 精确到秒. */
  timestamp: number;
  /** 压缩类型 */
  compress_type: CompressType;
  /** 广播组ID, 单播时为空字符串, 广播时是广播组ID, ... */
  group_id: string;
  /** 通知类型, 1: 单播,  2: 广播, ... */
  notify_type: number;
  /** 拓展信息 */
  extension: { [key: string]: string };
}

export interface RtmPayload_ExtensionEntry {
  key: string;
  value: string;
}

function createBaseRtmPayload(): RtmPayload {
  return {
    notify_pkg: '',
    notify_data: new Uint8Array(0),
    seq_id: '',
    timestamp: 0,
    compress_type: 0,
    group_id: '',
    notify_type: 0,
    extension: {}
  };
}

export const RtmPayload: MessageFns<RtmPayload> = {
  fromJSON(object: any): RtmPayload {
    return {
      notify_pkg: isSet(object.notify_pkg) ? globalThis.String(object.notify_pkg) : '',
      notify_data: isSet(object.notify_data) ? bytesFromBase64(object.notify_data) : new Uint8Array(0),
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      timestamp: isSet(object.timestamp) ? globalThis.Number(object.timestamp) : 0,
      compress_type: isSet(object.compress_type) ? compressTypeFromJSON(object.compress_type) : 0,
      group_id: isSet(object.group_id) ? globalThis.String(object.group_id) : '',
      notify_type: isSet(object.notify_type) ? globalThis.Number(object.notify_type) : 0,
      extension: isObject(object.extension)
        ? Object.entries(object.extension).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<RtmPayload>, I>>(base?: I): RtmPayload {
    return RtmPayload.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RtmPayload>, I>>(object: I): RtmPayload {
    const message = createBaseRtmPayload();
    message.notify_pkg = object.notify_pkg ?? '';
    message.notify_data = object.notify_data ?? new Uint8Array(0);
    message.seq_id = object.seq_id ?? '';
    message.timestamp = object.timestamp ?? 0;
    message.compress_type = object.compress_type ?? 0;
    message.group_id = object.group_id ?? '';
    message.notify_type = object.notify_type ?? 0;
    message.extension = Object.entries(object.extension ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseRtmPayload_ExtensionEntry(): RtmPayload_ExtensionEntry {
  return { key: '', value: '' };
}

export const RtmPayload_ExtensionEntry: MessageFns<RtmPayload_ExtensionEntry> = {
  fromJSON(object: any): RtmPayload_ExtensionEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RtmPayload_ExtensionEntry>, I>>(base?: I): RtmPayload_ExtensionEntry {
    return RtmPayload_ExtensionEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RtmPayload_ExtensionEntry>, I>>(object: I): RtmPayload_ExtensionEntry {
    const message = createBaseRtmPayload_ExtensionEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
