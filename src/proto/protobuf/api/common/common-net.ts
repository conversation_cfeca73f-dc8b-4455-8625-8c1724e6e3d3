// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/common-net.proto

/* eslint-disable */

export const protobufPackage = 'scommon';

/** 返回码，0表示成功，10000-20000 为通用错误码 */
export enum Code {
  /** CODE_OK - 成功 */
  CODE_OK = 0,
  /** CODE_SERVER_ERROR - 服务的未知错误，非必要不要用 */
  CODE_SERVER_ERROR = 10000,
  /** CODE_INVALID_PARAMETER - 参数非法 */
  CODE_INVALID_PARAMETER = 10001,
  /** CODE_UNAUTH - 未授权用户，未登录、黑名单用户都可以返回这个 */
  CODE_UNAUTH = 10002,
  /** CODE_UNSUPPORT_VERSION - 不支持的版本 */
  CODE_UNSUPPORT_VERSION = 10003,
  /** CODE_NOT_FOUND - 目标不存在 */
  CODE_NOT_FOUND = 10004,
  /** CODE_ALREADY_EXISTS - 已经存在 */
  CODE_ALREADY_EXISTS = 10005,
  /** CODE_DUPLICATE_SEQID - seqid重复 */
  CODE_DUPLICATE_SEQID = 10006,
  /** CODE_NOT_STARTED - 未开始 */
  CODE_NOT_STARTED = 10007,
  /** CODE_OVER - 已结束 */
  CODE_OVER = 10008,
  /** CODE_TOKEN_AGED - TOKEN 过期 */
  CODE_TOKEN_AGED = 10009,
  /** CODE_BANNED - 被封禁 */
  CODE_BANNED = 10010,
  /** CODE_APP_NOT_FOUND - 主要给cproxy用的 */
  CODE_APP_NOT_FOUND = 10500,
  /** CODE_UNKNOW_CONTENT_TYPE - 不支持的content-type */
  CODE_UNKNOW_CONTENT_TYPE = 10501,
  /** CODE_ENCRYPT_KEY_AGED - 加密密钥已经过期了 */
  CODE_ENCRYPT_KEY_AGED = 10502,
  /** CODE_DECRYPT_FAIL - 解密失败 */
  CODE_DECRYPT_FAIL = 10503,
  /** CODE_BASE64_FAIL - base64解码失败 */
  CODE_BASE64_FAIL = 10504,
  /** CODE_UNZIP_FAIL - 解压失败，没有压缩失败的错误码，压缩失败就不压缩 */
  CODE_UNZIP_FAIL = 10505,
  /** CODE_DECODE_DATA_FAIL - 反序列化请求失败 */
  CODE_DECODE_DATA_FAIL = 10506,
  /** CODE_ENCODE_DATA_FAIL - 序列化响应失败 */
  CODE_ENCODE_DATA_FAIL = 10507,
  UNRECOGNIZED = -1
}

export function codeFromJSON(object: any): Code {
  switch (object) {
    case 0:
    case 'CODE_OK':
      return Code.CODE_OK;
    case 10000:
    case 'CODE_SERVER_ERROR':
      return Code.CODE_SERVER_ERROR;
    case 10001:
    case 'CODE_INVALID_PARAMETER':
      return Code.CODE_INVALID_PARAMETER;
    case 10002:
    case 'CODE_UNAUTH':
      return Code.CODE_UNAUTH;
    case 10003:
    case 'CODE_UNSUPPORT_VERSION':
      return Code.CODE_UNSUPPORT_VERSION;
    case 10004:
    case 'CODE_NOT_FOUND':
      return Code.CODE_NOT_FOUND;
    case 10005:
    case 'CODE_ALREADY_EXISTS':
      return Code.CODE_ALREADY_EXISTS;
    case 10006:
    case 'CODE_DUPLICATE_SEQID':
      return Code.CODE_DUPLICATE_SEQID;
    case 10007:
    case 'CODE_NOT_STARTED':
      return Code.CODE_NOT_STARTED;
    case 10008:
    case 'CODE_OVER':
      return Code.CODE_OVER;
    case 10009:
    case 'CODE_TOKEN_AGED':
      return Code.CODE_TOKEN_AGED;
    case 10010:
    case 'CODE_BANNED':
      return Code.CODE_BANNED;
    case 10500:
    case 'CODE_APP_NOT_FOUND':
      return Code.CODE_APP_NOT_FOUND;
    case 10501:
    case 'CODE_UNKNOW_CONTENT_TYPE':
      return Code.CODE_UNKNOW_CONTENT_TYPE;
    case 10502:
    case 'CODE_ENCRYPT_KEY_AGED':
      return Code.CODE_ENCRYPT_KEY_AGED;
    case 10503:
    case 'CODE_DECRYPT_FAIL':
      return Code.CODE_DECRYPT_FAIL;
    case 10504:
    case 'CODE_BASE64_FAIL':
      return Code.CODE_BASE64_FAIL;
    case 10505:
    case 'CODE_UNZIP_FAIL':
      return Code.CODE_UNZIP_FAIL;
    case 10506:
    case 'CODE_DECODE_DATA_FAIL':
      return Code.CODE_DECODE_DATA_FAIL;
    case 10507:
    case 'CODE_ENCODE_DATA_FAIL':
      return Code.CODE_ENCODE_DATA_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Code.UNRECOGNIZED;
  }
}

/**
 * 发起请求的原因，这个可以用来辅助服务端做策略，比如在流量暴增的情况下，
 * 应该优先保证用户触发的请求
 */
export enum RPCReason {
  RPC_REASON_NONE = 0,
  /** RPC_REASON_USER_ACTION - 用户行为触发 */
  RPC_REASON_USER_ACTION = 1,
  /** RPC_REASON_PRE_LOAD - 预加载 */
  RPC_REASON_PRE_LOAD = 10,
  /** RPC_REASON_TIMER - 定时器 */
  RPC_REASON_TIMER = 11,
  UNRECOGNIZED = -1
}

export function rPCReasonFromJSON(object: any): RPCReason {
  switch (object) {
    case 0:
    case 'RPC_REASON_NONE':
      return RPCReason.RPC_REASON_NONE;
    case 1:
    case 'RPC_REASON_USER_ACTION':
      return RPCReason.RPC_REASON_USER_ACTION;
    case 10:
    case 'RPC_REASON_PRE_LOAD':
      return RPCReason.RPC_REASON_PRE_LOAD;
    case 11:
    case 'RPC_REASON_TIMER':
      return RPCReason.RPC_REASON_TIMER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RPCReason.UNRECOGNIZED;
  }
}

/** 主要用于在二进制协议包中表示序列化方式 */
export enum ContentType {
  /** CONTENT_TYPE_PROTO - protobuf */
  CONTENT_TYPE_PROTO = 0,
  /** CONTENT_TYPE_JSON - json */
  CONTENT_TYPE_JSON = 1,
  UNRECOGNIZED = -1
}

export function contentTypeFromJSON(object: any): ContentType {
  switch (object) {
    case 0:
    case 'CONTENT_TYPE_PROTO':
      return ContentType.CONTENT_TYPE_PROTO;
    case 1:
    case 'CONTENT_TYPE_JSON':
      return ContentType.CONTENT_TYPE_JSON;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ContentType.UNRECOGNIZED;
  }
}

/** 压缩类型 */
export enum CompressType {
  /** COMPRESS_TYPE_NONE - 没有压缩 */
  COMPRESS_TYPE_NONE = 0,
  /** COMPRESS_TYPE_GZIP - gzip压缩 */
  COMPRESS_TYPE_GZIP = 1,
  UNRECOGNIZED = -1
}

export function compressTypeFromJSON(object: any): CompressType {
  switch (object) {
    case 0:
    case 'COMPRESS_TYPE_NONE':
      return CompressType.COMPRESS_TYPE_NONE;
    case 1:
    case 'COMPRESS_TYPE_GZIP':
      return CompressType.COMPRESS_TYPE_GZIP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CompressType.UNRECOGNIZED;
  }
}

/**
 * 公参信息
 * 系统类型
 */
export enum OSType {
  OS_TYPE_NONE = 0,
  OS_TYPE_ANDROID = 1,
  OS_TYPE_IOS = 2,
  OS_TYPE_WEB = 100,
  UNRECOGNIZED = -1
}

export function oSTypeFromJSON(object: any): OSType {
  switch (object) {
    case 0:
    case 'OS_TYPE_NONE':
      return OSType.OS_TYPE_NONE;
    case 1:
    case 'OS_TYPE_ANDROID':
      return OSType.OS_TYPE_ANDROID;
    case 2:
    case 'OS_TYPE_IOS':
      return OSType.OS_TYPE_IOS;
    case 100:
    case 'OS_TYPE_WEB':
      return OSType.OS_TYPE_WEB;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return OSType.UNRECOGNIZED;
  }
}

/** 性别信息 */
export enum Gender {
  GENDER_NONE = 0,
  GENDER_MALE = 1,
  GENDER_FEMALE = 2,
  /** GENDER_UNISEX - 性别保密、未填写 */
  GENDER_UNISEX = 3,
  UNRECOGNIZED = -1
}

export function genderFromJSON(object: any): Gender {
  switch (object) {
    case 0:
    case 'GENDER_NONE':
      return Gender.GENDER_NONE;
    case 1:
    case 'GENDER_MALE':
      return Gender.GENDER_MALE;
    case 2:
    case 'GENDER_FEMALE':
      return Gender.GENDER_FEMALE;
    case 3:
    case 'GENDER_UNISEX':
      return Gender.GENDER_UNISEX;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Gender.UNRECOGNIZED;
  }
}

/** 消息类型 */
export enum RspNotifyType {
  MSG_TYPE_NONE = 0,
  /** MSG_TYPE_RESPONSE - request的响应，response */
  MSG_TYPE_RESPONSE = 1,
  /** MSG_TYPE_NOTIFY - 服务端主动发送的通知 */
  MSG_TYPE_NOTIFY = 2,
  UNRECOGNIZED = -1
}

export function rspNotifyTypeFromJSON(object: any): RspNotifyType {
  switch (object) {
    case 0:
    case 'MSG_TYPE_NONE':
      return RspNotifyType.MSG_TYPE_NONE;
    case 1:
    case 'MSG_TYPE_RESPONSE':
      return RspNotifyType.MSG_TYPE_RESPONSE;
    case 2:
    case 'MSG_TYPE_NOTIFY':
      return RspNotifyType.MSG_TYPE_NOTIFY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RspNotifyType.UNRECOGNIZED;
  }
}

/** Notify 的场景，只定义一些非常基础的场景，不要定义太多细节场景 */
export enum NotifyScene {
  NOTIFY_SCENE_NONE = 0,
  /** NOTIFY_SCENE_ROOM - 语音房 */
  NOTIFY_SCENE_ROOM = 1,
  /** NOTIFY_SCENE_CALL - 语音/视频通话 */
  NOTIFY_SCENE_CALL = 50,
  UNRECOGNIZED = -1
}

export function notifySceneFromJSON(object: any): NotifyScene {
  switch (object) {
    case 0:
    case 'NOTIFY_SCENE_NONE':
      return NotifyScene.NOTIFY_SCENE_NONE;
    case 1:
    case 'NOTIFY_SCENE_ROOM':
      return NotifyScene.NOTIFY_SCENE_ROOM;
    case 50:
    case 'NOTIFY_SCENE_CALL':
      return NotifyScene.NOTIFY_SCENE_CALL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return NotifyScene.UNRECOGNIZED;
  }
}

export enum Op {
  OP_NONE = 0,
  /** OP_AND - && */
  OP_AND = 1,
  /** OP_OR - || */
  OP_OR = 2,
  /** OP_NOT - ! */
  OP_NOT = 3,
  /** OP_EQUAL - == */
  OP_EQUAL = 10,
  /** OP_NE - != */
  OP_NE = 11,
  /** OP_IN - 在一个列表里面 */
  OP_IN = 12,
  /** OP_NOT_IN - 不在一个列表里面 */
  OP_NOT_IN = 13,
  /** OP_GT - > */
  OP_GT = 20,
  /** OP_GTE - >= */
  OP_GTE = 21,
  /** OP_LT - < */
  OP_LT = 22,
  /** OP_LTE - <= */
  OP_LTE = 23,
  UNRECOGNIZED = -1
}

export function opFromJSON(object: any): Op {
  switch (object) {
    case 0:
    case 'OP_NONE':
      return Op.OP_NONE;
    case 1:
    case 'OP_AND':
      return Op.OP_AND;
    case 2:
    case 'OP_OR':
      return Op.OP_OR;
    case 3:
    case 'OP_NOT':
      return Op.OP_NOT;
    case 10:
    case 'OP_EQUAL':
      return Op.OP_EQUAL;
    case 11:
    case 'OP_NE':
      return Op.OP_NE;
    case 12:
    case 'OP_IN':
      return Op.OP_IN;
    case 13:
    case 'OP_NOT_IN':
      return Op.OP_NOT_IN;
    case 20:
    case 'OP_GT':
      return Op.OP_GT;
    case 21:
    case 'OP_GTE':
      return Op.OP_GTE;
    case 22:
    case 'OP_LT':
      return Op.OP_LT;
    case 23:
    case 'OP_LTE':
      return Op.OP_LTE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Op.UNRECOGNIZED;
  }
}

/** 请求控制体 */
export interface ReqControl {
  /** 服务名 */
  service: string;
  /** RPC方法名 */
  method: string;
  /**
   * 请求序列ID，同用户，必须单调递增（两次不同的登录也要求递增），
   * 如果是重传，则seqid不变。一种建议的方案是：记录上一次的seqid，如果是0，则获取当前的毫秒时间。
   * 如果当前的毫秒时间小于或者等于上一次的seqid，则用上一次的seqid+1，记得每次使用要更新上一次的seqid
   */
  seqid: number;
  /** 用于把请求发送到指定的某台服务器，用于调试，客户端一般不填 */
  route_key: string;
  /**
   * 【后端处理的超时时间】，单位毫秒，默认是3000ms，对于部分比较耗时的请求，可以设置一个大一点的timeout，
   * 用默认值的可以不设置这个header
   */
  timeout: number;
  /**
   * 客户端自己生成一个16字节的密钥，用公钥加密，再做base64，然后拼上公钥编号（以防后续密钥对要更新的情况）
   * 最终的格式就是 0,xxxxxxx
   * 其中0是公钥的编号，编号服务端给APP公钥的时候会告诉编号，从0开始递增
   * 后面的xxxxx就是密钥经过公钥加密并做base64之后的数值
   * 长连接可以每次建立连接生成一个新的密钥，短连接建议客户端定期更新，避免非对称解密浪费服务端资源
   */
  encrypt: string;
  /** 压缩算法，目前就支持"gzip"， 空表示不压缩 */
  compress: CompressType;
  /** 发送这个请求的时候，APP是否处于后台。 */
  background: boolean;
  /** 发起请求的原因 */
  reason: RPCReason;
}

export interface PubPara {
  /** starhalo；版本号，分三段，主版本 | 次版本(两位) | 灰度版本(三位)，如：2.06.008 */
  ver: string;
  /** 递增的编译版本号 */
  verc: number;
  /** 设备ID */
  did: string;
  /** 系统类型 */
  os_type: OSType;
  /** 系统版本 */
  os_ver: string;
  /** 系统API版本 */
  os_api_ver: string;
  /** 语言，优先APP设置的语言，如果APP没有语言设置，用系统语言 */
  lan: string;
  /** 包名 */
  pkg: string;
  /** 当前的国家码，优先取sim卡的，没有则取系统的 */
  cou: string;
  /** 性别信息 */
  gender: Gender;
  /** pkg_channel，历史原因继续使用rel，包渠道，比如google play/app store、huawei、oppo、vivo、小米等。 */
  rel: string;
  /** ad_channel，历史原因继续使用cha，广告渠道(用户渠道），比如google/facebook/toutiao/kuaishou等 */
  cha: string;
  /** 子渠道，原来的命名是sub，过于通用了，所以改成sub_cha */
  sub_cha: string;
  /** 业务版本号，比如：V1.0.0，用于多个马甲包版本号不一样的情况，功能一致的情况下，业务版本是一致的 */
  biz_ver: string;
  /** 业务公参，json格式，需要定义一个pb与之对应 */
  biz: string;
}

/** code&msg */
export interface Result {
  /** 这里不用枚举，需要设置一些其他协议定义的错误码 */
  code: number;
  /** 一般不需要填这个字段，客户端更应该用code，这个字段方便调试 */
  message: string;
}

/** 通用的toast */
export interface Toast {
  /** 多语言内容，{语言码:文案} */
  msg: { [key: string]: string };
}

export interface Toast_MsgEntry {
  key: string;
  value: string;
}

/**
 * Response&Notify消息，在长连接里面，这些是流，然后也没有header表明这个是一个
 * response还是一个Notify，所以放一块
 */
export interface RspNotifyControl {
  msg_type: RspNotifyType;
  /** 服务名，rsp和Notify都填，rsp是透传req的service */
  service: string;
  /** RPC方法名，rsp透传，Notify不填 */
  method: string;
  /** 因为一个service会包含多个proto文件，也就是会有多个Notify的结构体，所以需要区分是哪个proto文件的Notify，这里填proto的package名称 */
  notify_pkg: string;
  /** rsp透传，Notify服务端生 */
  seqid: number;
  /** 服务器的unix timestamp, seconds, response带，Notify是否带不确定，0表示不带 */
  ts: number;
  /** 如果是发的广播，则表示广播组的ID */
  bc_group: string;
  /** 和ts一样，但是单位是ms */
  ts_ms: number;
  /**
   * 客户端请求的密钥用的是非对称加密，密钥长度很长，为了节省传输，服务端会对密钥做一次对称加密之后
   * 回传给客户端，客户端后续请求可以使用携带该密钥，需要注意的是，用经过处理的密钥，不要携带公钥id
   */
  encrypt: string;
  /** 压缩算法，目前就支持"gzip"， 空表示不压缩 */
  compress: CompressType;
  /**
   * 这两个字段，都是Response才有。result里面的message是用于debug和打印日志用的，
   * 不要把对应的message展示给用户看。客户端的错误提示主要应该是根据code来提示。
   */
  result: Result | undefined;
  /**
   * toast是作为一种【补救手段】用的，千万不要滥用了！！！！！！
   * 何为补救手段，这里举个例子。比如原来用户是可以随意编辑自己的昵称的，在B版本里面，
   * 产品提了个需求：每个用户每天只能编辑三次昵称。这个时候，老版本是没有“编辑次数超过
   * 限制”这个提示的。为了解决这个问题，当老版本编辑次数超过限制的时候，服务端就应该
   * 返回对应的toast。简单来说：toast不应该用在当前的版本的提示中！！！！
   */
  toast: Toast | undefined;
  scene: NotifyScene;
  /** 语音房填roomid */
  room_id?: number | undefined;
  /** 音视频通话的session id */
  call_id?: string | undefined;
  /** 这个不会使用了 */
  deprecated1: NotifyFilter | undefined;
  /** Notify的过滤，filter_out 优先， */
  notify_filter_out: FilterCond | undefined;
  /** 如果不匹配则丢弃notify */
  notify_filter_in: FilterCond | undefined;
}

/**
 * Notify的过滤规则，各个条件是与的关系，表示全部都满足才处理这个Notify
 * 这个不会使用了
 */
export interface NotifyFilter {
  /** 如果非空表示只有这些语言才处理这个通知，格式是zh/en这种双字母语言码(小写) */
  langs: string[];
  /** 如果非空表示只有这些国家才处理这个通知，格式是CN/US这种双字母国家码(大写) */
  cous: string[];
  /** 如果非空表示只有这些版本才处理这个通知 */
  ver_range: VersionRange[];
}

/** 版本范围，闭区间，如果只写了min没写max，则表示[min, ∞)，反之表示(0, max] */
export interface VersionRange {
  min: string;
  max: string;
}

/** 过滤条件 */
export interface FilterCond {
  /** 过滤规则的名字，用于调试&日志，只有最顶层的Cond才有 */
  name: string;
  /** 原始的表达式，只有最顶层的Cond才会有。 */
  raw_expr: string;
  op: Op;
  /** PubPara的成员名字 */
  field_name: string;
  /** field是int或者枚举的时候用 */
  int_val: number;
  /** OP_IN/OP_NOT_IN 的时候用 */
  int_vals: number[];
  /** field是string的时候用 */
  str_val: string;
  /** OP_IN/OP_NOT_IN 的时候用 */
  str_vals: string[];
  /** && || !的时候用，!的时候应该只有一个 */
  sub_conds: FilterCond[];
}

function createBaseReqControl(): ReqControl {
  return {
    service: '',
    method: '',
    seqid: 0,
    route_key: '',
    timeout: 0,
    encrypt: '',
    compress: 0,
    background: false,
    reason: 0
  };
}

export const ReqControl: MessageFns<ReqControl> = {
  fromJSON(object: any): ReqControl {
    return {
      service: isSet(object.service) ? globalThis.String(object.service) : '',
      method: isSet(object.method) ? globalThis.String(object.method) : '',
      seqid: isSet(object.seqid) ? globalThis.Number(object.seqid) : 0,
      route_key: isSet(object.route_key) ? globalThis.String(object.route_key) : '',
      timeout: isSet(object.timeout) ? globalThis.Number(object.timeout) : 0,
      encrypt: isSet(object.encrypt) ? globalThis.String(object.encrypt) : '',
      compress: isSet(object.compress) ? compressTypeFromJSON(object.compress) : 0,
      background: isSet(object.background) ? globalThis.Boolean(object.background) : false,
      reason: isSet(object.reason) ? rPCReasonFromJSON(object.reason) : 0
    };
  },

  create<I extends Exact<DeepPartial<ReqControl>, I>>(base?: I): ReqControl {
    return ReqControl.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ReqControl>, I>>(object: I): ReqControl {
    const message = createBaseReqControl();
    message.service = object.service ?? '';
    message.method = object.method ?? '';
    message.seqid = object.seqid ?? 0;
    message.route_key = object.route_key ?? '';
    message.timeout = object.timeout ?? 0;
    message.encrypt = object.encrypt ?? '';
    message.compress = object.compress ?? 0;
    message.background = object.background ?? false;
    message.reason = object.reason ?? 0;
    return message;
  }
};

function createBasePubPara(): PubPara {
  return {
    ver: '',
    verc: 0,
    did: '',
    os_type: 0,
    os_ver: '',
    os_api_ver: '',
    lan: '',
    pkg: '',
    cou: '',
    gender: 0,
    rel: '',
    cha: '',
    sub_cha: '',
    biz_ver: '',
    biz: ''
  };
}

export const PubPara: MessageFns<PubPara> = {
  fromJSON(object: any): PubPara {
    return {
      ver: isSet(object.ver) ? globalThis.String(object.ver) : '',
      verc: isSet(object.verc) ? globalThis.Number(object.verc) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      os_ver: isSet(object.os_ver) ? globalThis.String(object.os_ver) : '',
      os_api_ver: isSet(object.os_api_ver) ? globalThis.String(object.os_api_ver) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      gender: isSet(object.gender) ? genderFromJSON(object.gender) : 0,
      rel: isSet(object.rel) ? globalThis.String(object.rel) : '',
      cha: isSet(object.cha) ? globalThis.String(object.cha) : '',
      sub_cha: isSet(object.sub_cha) ? globalThis.String(object.sub_cha) : '',
      biz_ver: isSet(object.biz_ver) ? globalThis.String(object.biz_ver) : '',
      biz: isSet(object.biz) ? globalThis.String(object.biz) : ''
    };
  },

  create<I extends Exact<DeepPartial<PubPara>, I>>(base?: I): PubPara {
    return PubPara.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PubPara>, I>>(object: I): PubPara {
    const message = createBasePubPara();
    message.ver = object.ver ?? '';
    message.verc = object.verc ?? 0;
    message.did = object.did ?? '';
    message.os_type = object.os_type ?? 0;
    message.os_ver = object.os_ver ?? '';
    message.os_api_ver = object.os_api_ver ?? '';
    message.lan = object.lan ?? '';
    message.pkg = object.pkg ?? '';
    message.cou = object.cou ?? '';
    message.gender = object.gender ?? 0;
    message.rel = object.rel ?? '';
    message.cha = object.cha ?? '';
    message.sub_cha = object.sub_cha ?? '';
    message.biz_ver = object.biz_ver ?? '';
    message.biz = object.biz ?? '';
    return message;
  }
};

function createBaseResult(): Result {
  return { code: 0, message: '' };
}

export const Result: MessageFns<Result> = {
  fromJSON(object: any): Result {
    return {
      code: isSet(object.code) ? globalThis.Number(object.code) : 0,
      message: isSet(object.message) ? globalThis.String(object.message) : ''
    };
  },

  create<I extends Exact<DeepPartial<Result>, I>>(base?: I): Result {
    return Result.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Result>, I>>(object: I): Result {
    const message = createBaseResult();
    message.code = object.code ?? 0;
    message.message = object.message ?? '';
    return message;
  }
};

function createBaseToast(): Toast {
  return { msg: {} };
}

export const Toast: MessageFns<Toast> = {
  fromJSON(object: any): Toast {
    return {
      msg: isObject(object.msg)
        ? Object.entries(object.msg).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<Toast>, I>>(base?: I): Toast {
    return Toast.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Toast>, I>>(object: I): Toast {
    const message = createBaseToast();
    message.msg = Object.entries(object.msg ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseToast_MsgEntry(): Toast_MsgEntry {
  return { key: '', value: '' };
}

export const Toast_MsgEntry: MessageFns<Toast_MsgEntry> = {
  fromJSON(object: any): Toast_MsgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Toast_MsgEntry>, I>>(base?: I): Toast_MsgEntry {
    return Toast_MsgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Toast_MsgEntry>, I>>(object: I): Toast_MsgEntry {
    const message = createBaseToast_MsgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRspNotifyControl(): RspNotifyControl {
  return {
    msg_type: 0,
    service: '',
    method: '',
    notify_pkg: '',
    seqid: 0,
    ts: 0,
    bc_group: '',
    ts_ms: 0,
    encrypt: '',
    compress: 0,
    result: undefined,
    toast: undefined,
    scene: 0,
    room_id: undefined,
    call_id: undefined,
    deprecated1: undefined,
    notify_filter_out: undefined,
    notify_filter_in: undefined
  };
}

export const RspNotifyControl: MessageFns<RspNotifyControl> = {
  fromJSON(object: any): RspNotifyControl {
    return {
      msg_type: isSet(object.msg_type) ? rspNotifyTypeFromJSON(object.msg_type) : 0,
      service: isSet(object.service) ? globalThis.String(object.service) : '',
      method: isSet(object.method) ? globalThis.String(object.method) : '',
      notify_pkg: isSet(object.notify_pkg) ? globalThis.String(object.notify_pkg) : '',
      seqid: isSet(object.seqid) ? globalThis.Number(object.seqid) : 0,
      ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0,
      bc_group: isSet(object.bc_group) ? globalThis.String(object.bc_group) : '',
      ts_ms: isSet(object.ts_ms) ? globalThis.Number(object.ts_ms) : 0,
      encrypt: isSet(object.encrypt) ? globalThis.String(object.encrypt) : '',
      compress: isSet(object.compress) ? compressTypeFromJSON(object.compress) : 0,
      result: isSet(object.result) ? Result.fromJSON(object.result) : undefined,
      toast: isSet(object.toast) ? Toast.fromJSON(object.toast) : undefined,
      scene: isSet(object.scene) ? notifySceneFromJSON(object.scene) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : undefined,
      call_id: isSet(object.call_id) ? globalThis.String(object.call_id) : undefined,
      deprecated1: isSet(object.deprecated1) ? NotifyFilter.fromJSON(object.deprecated1) : undefined,
      notify_filter_out: isSet(object.notify_filter_out) ? FilterCond.fromJSON(object.notify_filter_out) : undefined,
      notify_filter_in: isSet(object.notify_filter_in) ? FilterCond.fromJSON(object.notify_filter_in) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RspNotifyControl>, I>>(base?: I): RspNotifyControl {
    return RspNotifyControl.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RspNotifyControl>, I>>(object: I): RspNotifyControl {
    const message = createBaseRspNotifyControl();
    message.msg_type = object.msg_type ?? 0;
    message.service = object.service ?? '';
    message.method = object.method ?? '';
    message.notify_pkg = object.notify_pkg ?? '';
    message.seqid = object.seqid ?? 0;
    message.ts = object.ts ?? 0;
    message.bc_group = object.bc_group ?? '';
    message.ts_ms = object.ts_ms ?? 0;
    message.encrypt = object.encrypt ?? '';
    message.compress = object.compress ?? 0;
    message.result =
      object.result !== undefined && object.result !== null ? Result.fromPartial(object.result) : undefined;
    message.toast = object.toast !== undefined && object.toast !== null ? Toast.fromPartial(object.toast) : undefined;
    message.scene = object.scene ?? 0;
    message.room_id = object.room_id ?? undefined;
    message.call_id = object.call_id ?? undefined;
    message.deprecated1 =
      object.deprecated1 !== undefined && object.deprecated1 !== null
        ? NotifyFilter.fromPartial(object.deprecated1)
        : undefined;
    message.notify_filter_out =
      object.notify_filter_out !== undefined && object.notify_filter_out !== null
        ? FilterCond.fromPartial(object.notify_filter_out)
        : undefined;
    message.notify_filter_in =
      object.notify_filter_in !== undefined && object.notify_filter_in !== null
        ? FilterCond.fromPartial(object.notify_filter_in)
        : undefined;
    return message;
  }
};

function createBaseNotifyFilter(): NotifyFilter {
  return { langs: [], cous: [], ver_range: [] };
}

export const NotifyFilter: MessageFns<NotifyFilter> = {
  fromJSON(object: any): NotifyFilter {
    return {
      langs: globalThis.Array.isArray(object?.langs) ? object.langs.map((e: any) => globalThis.String(e)) : [],
      cous: globalThis.Array.isArray(object?.cous) ? object.cous.map((e: any) => globalThis.String(e)) : [],
      ver_range: globalThis.Array.isArray(object?.ver_range)
        ? object.ver_range.map((e: any) => VersionRange.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<NotifyFilter>, I>>(base?: I): NotifyFilter {
    return NotifyFilter.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyFilter>, I>>(object: I): NotifyFilter {
    const message = createBaseNotifyFilter();
    message.langs = object.langs?.map(e => e) || [];
    message.cous = object.cous?.map(e => e) || [];
    message.ver_range = object.ver_range?.map(e => VersionRange.fromPartial(e)) || [];
    return message;
  }
};

function createBaseVersionRange(): VersionRange {
  return { min: '', max: '' };
}

export const VersionRange: MessageFns<VersionRange> = {
  fromJSON(object: any): VersionRange {
    return {
      min: isSet(object.min) ? globalThis.String(object.min) : '',
      max: isSet(object.max) ? globalThis.String(object.max) : ''
    };
  },

  create<I extends Exact<DeepPartial<VersionRange>, I>>(base?: I): VersionRange {
    return VersionRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<VersionRange>, I>>(object: I): VersionRange {
    const message = createBaseVersionRange();
    message.min = object.min ?? '';
    message.max = object.max ?? '';
    return message;
  }
};

function createBaseFilterCond(): FilterCond {
  return {
    name: '',
    raw_expr: '',
    op: 0,
    field_name: '',
    int_val: 0,
    int_vals: [],
    str_val: '',
    str_vals: [],
    sub_conds: []
  };
}

export const FilterCond: MessageFns<FilterCond> = {
  fromJSON(object: any): FilterCond {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      raw_expr: isSet(object.raw_expr) ? globalThis.String(object.raw_expr) : '',
      op: isSet(object.op) ? opFromJSON(object.op) : 0,
      field_name: isSet(object.field_name) ? globalThis.String(object.field_name) : '',
      int_val: isSet(object.int_val) ? globalThis.Number(object.int_val) : 0,
      int_vals: globalThis.Array.isArray(object?.int_vals) ? object.int_vals.map((e: any) => globalThis.Number(e)) : [],
      str_val: isSet(object.str_val) ? globalThis.String(object.str_val) : '',
      str_vals: globalThis.Array.isArray(object?.str_vals) ? object.str_vals.map((e: any) => globalThis.String(e)) : [],
      sub_conds: globalThis.Array.isArray(object?.sub_conds)
        ? object.sub_conds.map((e: any) => FilterCond.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<FilterCond>, I>>(base?: I): FilterCond {
    return FilterCond.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FilterCond>, I>>(object: I): FilterCond {
    const message = createBaseFilterCond();
    message.name = object.name ?? '';
    message.raw_expr = object.raw_expr ?? '';
    message.op = object.op ?? 0;
    message.field_name = object.field_name ?? '';
    message.int_val = object.int_val ?? 0;
    message.int_vals = object.int_vals?.map(e => e) || [];
    message.str_val = object.str_val ?? '';
    message.str_vals = object.str_vals?.map(e => e) || [];
    message.sub_conds = object.sub_conds?.map(e => FilterCond.fromPartial(e)) || [];
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
