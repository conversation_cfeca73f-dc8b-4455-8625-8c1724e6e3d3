// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/crank.proto

/* eslint-disable */
import { Page } from './common';

export const protobufPackage = 'crank';

export enum State {
  STATE_NONE = 0,
  /** STATE_NOT_BEGIN - 未开始 */
  STATE_NOT_BEGIN = 1,
  /** STATE_ING - 进行中 */
  STATE_ING = 2,
  /** STATE_REST - 中间休息阶段 */
  STATE_REST = 3,
  /** STATE_END - 已结束 */
  STATE_END = 10,
  UNRECOGNIZED = -1
}

export function stateFromJSON(object: any): State {
  switch (object) {
    case 0:
    case 'STATE_NONE':
      return State.STATE_NONE;
    case 1:
    case 'STATE_NOT_BEGIN':
      return State.STATE_NOT_BEGIN;
    case 2:
    case 'STATE_ING':
      return State.STATE_ING;
    case 3:
    case 'STATE_REST':
      return State.STATE_REST;
    case 10:
    case 'STATE_END':
      return State.STATE_END;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return State.UNRECOGNIZED;
  }
}

/** 获取榜单需要的数据 */
export interface GetPara {
  /** 这个必须填，如果不填服务端会初始化一个默认的分页，limit为20 */
  page: Page | undefined;
  /** 榜单的模板和key，服务端告诉前端 */
  template: string;
  key: string;
  /** member非空，则表示获取member这个子榜 */
  member: string;
  /** 周期偏移。0表示当前周期，-n表示前N个周期。比如日榜，0表示今天的榜单，-1表示昨天的榜单 */
  cycle_offset: number;
  /** 指定cycle key，而不是用time 和cycle offset来计算，具体计算方法要和服务端确认 */
  cycle_key: string;
  /**
   * 很多榜单要展示自己的分数和排名，如果me非空，则获取me的分数和排名。建议是第一页获取，其他页不要获取。
   * 这里也不一定要是自己的分数和排名，me可以指定任意一个主体
   */
  me: string;
  /** 用于每个榜单item都需要获取子榜的情况 */
  top_n_need_sub: number;
  /** 子榜的长度。比如只需要展示贡献榜前两名，那么sub_len就是2 */
  sub_len: number;
  /** 各个服务自行和客户端协商增加的信息 */
  extend: string;
}

/** 主体基本信息（人、语音房等） */
export interface EntityInfo {
  /** 用string更加通用一些，用oneof处理比较复杂 */
  id: string;
  /** 头像 */
  avatar: string;
  /** 昵称 */
  nick: string;
  /** 扩展信息，各个服务自定义 */
  extend: string;
}

/** 一个榜单元素 */
export interface Item {
  /** 分数 */
  score: number;
  /** 排名 */
  rank: number;
  /** 这里用repeated，用于CP榜之类的场景 */
  entitys: EntityInfo[];
  /** 子榜信息 */
  subs: Item[];
  extend: string;
}

/** 一个榜单 */
export interface Rank {
  page: Page | undefined;
  /** 下面这几个原样透传回去，用于标识是哪个榜单 */
  template: string;
  key: string;
  member: string;
  items: Item[];
  me: Item | undefined;
  /** 当前榜单的开始结束时间，unix timestamp, seconds */
  begin: number;
  end: number;
  /** 当前榜单阶段 */
  state: State;
  extend: string;
}

export interface GetRankReq {
  /** 服务端提供，就是NewResource里面的name */
  name: string;
  /** 支持一次获取多个榜单，必须是同一个name内的 */
  paras: GetPara[];
}

export interface GetRankRsp {
  /** 这里的顺序和请求的顺序一致 */
  ranks: Rank[];
}

function createBaseGetPara(): GetPara {
  return {
    page: undefined,
    template: '',
    key: '',
    member: '',
    cycle_offset: 0,
    cycle_key: '',
    me: '',
    top_n_need_sub: 0,
    sub_len: 0,
    extend: ''
  };
}

export const GetPara: MessageFns<GetPara> = {
  fromJSON(object: any): GetPara {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      template: isSet(object.template) ? globalThis.String(object.template) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      cycle_offset: isSet(object.cycle_offset) ? globalThis.Number(object.cycle_offset) : 0,
      cycle_key: isSet(object.cycle_key) ? globalThis.String(object.cycle_key) : '',
      me: isSet(object.me) ? globalThis.String(object.me) : '',
      top_n_need_sub: isSet(object.top_n_need_sub) ? globalThis.Number(object.top_n_need_sub) : 0,
      sub_len: isSet(object.sub_len) ? globalThis.Number(object.sub_len) : 0,
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetPara>, I>>(base?: I): GetPara {
    return GetPara.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPara>, I>>(object: I): GetPara {
    const message = createBaseGetPara();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.template = object.template ?? '';
    message.key = object.key ?? '';
    message.member = object.member ?? '';
    message.cycle_offset = object.cycle_offset ?? 0;
    message.cycle_key = object.cycle_key ?? '';
    message.me = object.me ?? '';
    message.top_n_need_sub = object.top_n_need_sub ?? 0;
    message.sub_len = object.sub_len ?? 0;
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseEntityInfo(): EntityInfo {
  return { id: '', avatar: '', nick: '', extend: '' };
}

export const EntityInfo: MessageFns<EntityInfo> = {
  fromJSON(object: any): EntityInfo {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      nick: isSet(object.nick) ? globalThis.String(object.nick) : '',
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<EntityInfo>, I>>(base?: I): EntityInfo {
    return EntityInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EntityInfo>, I>>(object: I): EntityInfo {
    const message = createBaseEntityInfo();
    message.id = object.id ?? '';
    message.avatar = object.avatar ?? '';
    message.nick = object.nick ?? '';
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseItem(): Item {
  return { score: 0, rank: 0, entitys: [], subs: [], extend: '' };
}

export const Item: MessageFns<Item> = {
  fromJSON(object: any): Item {
    return {
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      entitys: globalThis.Array.isArray(object?.entitys) ? object.entitys.map((e: any) => EntityInfo.fromJSON(e)) : [],
      subs: globalThis.Array.isArray(object?.subs) ? object.subs.map((e: any) => Item.fromJSON(e)) : [],
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<Item>, I>>(base?: I): Item {
    return Item.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Item>, I>>(object: I): Item {
    const message = createBaseItem();
    message.score = object.score ?? 0;
    message.rank = object.rank ?? 0;
    message.entitys = object.entitys?.map(e => EntityInfo.fromPartial(e)) || [];
    message.subs = object.subs?.map(e => Item.fromPartial(e)) || [];
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseRank(): Rank {
  return {
    page: undefined,
    template: '',
    key: '',
    member: '',
    items: [],
    me: undefined,
    begin: 0,
    end: 0,
    state: 0,
    extend: ''
  };
}

export const Rank: MessageFns<Rank> = {
  fromJSON(object: any): Rank {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      template: isSet(object.template) ? globalThis.String(object.template) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => Item.fromJSON(e)) : [],
      me: isSet(object.me) ? Item.fromJSON(object.me) : undefined,
      begin: isSet(object.begin) ? globalThis.Number(object.begin) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0,
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<Rank>, I>>(base?: I): Rank {
    return Rank.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Rank>, I>>(object: I): Rank {
    const message = createBaseRank();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.template = object.template ?? '';
    message.key = object.key ?? '';
    message.member = object.member ?? '';
    message.items = object.items?.map(e => Item.fromPartial(e)) || [];
    message.me = object.me !== undefined && object.me !== null ? Item.fromPartial(object.me) : undefined;
    message.begin = object.begin ?? 0;
    message.end = object.end ?? 0;
    message.state = object.state ?? 0;
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseGetRankReq(): GetRankReq {
  return { name: '', paras: [] };
}

export const GetRankReq: MessageFns<GetRankReq> = {
  fromJSON(object: any): GetRankReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      paras: globalThis.Array.isArray(object?.paras) ? object.paras.map((e: any) => GetPara.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetRankReq>, I>>(base?: I): GetRankReq {
    return GetRankReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRankReq>, I>>(object: I): GetRankReq {
    const message = createBaseGetRankReq();
    message.name = object.name ?? '';
    message.paras = object.paras?.map(e => GetPara.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetRankRsp(): GetRankRsp {
  return { ranks: [] };
}

export const GetRankRsp: MessageFns<GetRankRsp> = {
  fromJSON(object: any): GetRankRsp {
    return { ranks: globalThis.Array.isArray(object?.ranks) ? object.ranks.map((e: any) => Rank.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetRankRsp>, I>>(base?: I): GetRankRsp {
    return GetRankRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRankRsp>, I>>(object: I): GetRankRsp {
    const message = createBaseGetRankRsp();
    message.ranks = object.ranks?.map(e => Rank.fromPartial(e)) || [];
    return message;
  }
};

export type CRankDefinition = typeof CRankDefinition;
export const CRankDefinition = {
  name: 'CRank',
  fullName: 'crank.CRank',
  methods: {
    getRank: {
      name: 'GetRank',
      requestType: GetRankReq,
      requestStream: false,
      responseType: GetRankRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
