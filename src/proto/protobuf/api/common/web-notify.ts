// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/web-notify.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.notify.web';

export interface Notify {
  notify: NotifyWeb | undefined;
}

export interface NotifyWeb {
  /** {{package}}.{{message}} */
  event: string;
  /** message的json格式 */
  data: string;
}

function createBaseNotify(): Notify {
  return { notify: undefined };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return { notify: isSet(object.notify) ? NotifyWeb.fromJSON(object.notify) : undefined };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.notify =
      object.notify !== undefined && object.notify !== null ? NotifyWeb.fromPartial(object.notify) : undefined;
    return message;
  }
};

function createBaseNotifyWeb(): NotifyWeb {
  return { event: '', data: '' };
}

export const NotifyWeb: MessageFns<NotifyWeb> = {
  fromJSON(object: any): NotifyWeb {
    return {
      event: isSet(object.event) ? globalThis.String(object.event) : '',
      data: isSet(object.data) ? globalThis.String(object.data) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotifyWeb>, I>>(base?: I): NotifyWeb {
    return NotifyWeb.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotifyWeb>, I>>(object: I): NotifyWeb {
    const message = createBaseNotifyWeb();
    message.event = object.event ?? '';
    message.data = object.data ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
