// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/common/cproxy.proto

/* eslint-disable */
import { ReqControl, RspNotifyControl } from './common-net';

export const protobufPackage = 'cproxy';

export interface SendHTTPReq {
  /** GET/POST */
  method: string;
  /** 不包含参数的url */
  url: string;
  /** 参数列表 */
  query: { [key: string]: string };
  /** 请求体 */
  body: Uint8Array;
}

export interface SendHTTPReq_QueryEntry {
  key: string;
  value: string;
}

export interface SendHTTPRsp {
  /** 响应的body */
  body: Uint8Array;
}

export interface BatchReq {
  reqs: BatchReqItem[];
  /** 超时等待时间，单位 毫秒 ！！！！ */
  timeout: number;
}

export interface BatchReqItem {
  req_ctrl: ReqControl | undefined;
  req_body: Uint8Array;
}

export interface BatchRspItem {
  result: RspNotifyControl | undefined;
  rsp_body: Uint8Array;
}

export interface BatchRsp {
  rsps: BatchRspItem[];
}

/** 用来给长连接发送Notify的加密的key（长连接没法在建连的时候发送一个response） */
export interface Notify {
  /** 单播广播加密的key */
  notify_ent_key: string;
}

function createBaseSendHTTPReq(): SendHTTPReq {
  return { method: '', url: '', query: {}, body: new Uint8Array(0) };
}

export const SendHTTPReq: MessageFns<SendHTTPReq> = {
  fromJSON(object: any): SendHTTPReq {
    return {
      method: isSet(object.method) ? globalThis.String(object.method) : '',
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      query: isObject(object.query)
        ? Object.entries(object.query).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      body: isSet(object.body) ? bytesFromBase64(object.body) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<SendHTTPReq>, I>>(base?: I): SendHTTPReq {
    return SendHTTPReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendHTTPReq>, I>>(object: I): SendHTTPReq {
    const message = createBaseSendHTTPReq();
    message.method = object.method ?? '';
    message.url = object.url ?? '';
    message.query = Object.entries(object.query ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.body = object.body ?? new Uint8Array(0);
    return message;
  }
};

function createBaseSendHTTPReq_QueryEntry(): SendHTTPReq_QueryEntry {
  return { key: '', value: '' };
}

export const SendHTTPReq_QueryEntry: MessageFns<SendHTTPReq_QueryEntry> = {
  fromJSON(object: any): SendHTTPReq_QueryEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendHTTPReq_QueryEntry>, I>>(base?: I): SendHTTPReq_QueryEntry {
    return SendHTTPReq_QueryEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendHTTPReq_QueryEntry>, I>>(object: I): SendHTTPReq_QueryEntry {
    const message = createBaseSendHTTPReq_QueryEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSendHTTPRsp(): SendHTTPRsp {
  return { body: new Uint8Array(0) };
}

export const SendHTTPRsp: MessageFns<SendHTTPRsp> = {
  fromJSON(object: any): SendHTTPRsp {
    return { body: isSet(object.body) ? bytesFromBase64(object.body) : new Uint8Array(0) };
  },

  create<I extends Exact<DeepPartial<SendHTTPRsp>, I>>(base?: I): SendHTTPRsp {
    return SendHTTPRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendHTTPRsp>, I>>(object: I): SendHTTPRsp {
    const message = createBaseSendHTTPRsp();
    message.body = object.body ?? new Uint8Array(0);
    return message;
  }
};

function createBaseBatchReq(): BatchReq {
  return { reqs: [], timeout: 0 };
}

export const BatchReq: MessageFns<BatchReq> = {
  fromJSON(object: any): BatchReq {
    return {
      reqs: globalThis.Array.isArray(object?.reqs) ? object.reqs.map((e: any) => BatchReqItem.fromJSON(e)) : [],
      timeout: isSet(object.timeout) ? globalThis.Number(object.timeout) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchReq>, I>>(base?: I): BatchReq {
    return BatchReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchReq>, I>>(object: I): BatchReq {
    const message = createBaseBatchReq();
    message.reqs = object.reqs?.map(e => BatchReqItem.fromPartial(e)) || [];
    message.timeout = object.timeout ?? 0;
    return message;
  }
};

function createBaseBatchReqItem(): BatchReqItem {
  return { req_ctrl: undefined, req_body: new Uint8Array(0) };
}

export const BatchReqItem: MessageFns<BatchReqItem> = {
  fromJSON(object: any): BatchReqItem {
    return {
      req_ctrl: isSet(object.req_ctrl) ? ReqControl.fromJSON(object.req_ctrl) : undefined,
      req_body: isSet(object.req_body) ? bytesFromBase64(object.req_body) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<BatchReqItem>, I>>(base?: I): BatchReqItem {
    return BatchReqItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchReqItem>, I>>(object: I): BatchReqItem {
    const message = createBaseBatchReqItem();
    message.req_ctrl =
      object.req_ctrl !== undefined && object.req_ctrl !== null ? ReqControl.fromPartial(object.req_ctrl) : undefined;
    message.req_body = object.req_body ?? new Uint8Array(0);
    return message;
  }
};

function createBaseBatchRspItem(): BatchRspItem {
  return { result: undefined, rsp_body: new Uint8Array(0) };
}

export const BatchRspItem: MessageFns<BatchRspItem> = {
  fromJSON(object: any): BatchRspItem {
    return {
      result: isSet(object.result) ? RspNotifyControl.fromJSON(object.result) : undefined,
      rsp_body: isSet(object.rsp_body) ? bytesFromBase64(object.rsp_body) : new Uint8Array(0)
    };
  },

  create<I extends Exact<DeepPartial<BatchRspItem>, I>>(base?: I): BatchRspItem {
    return BatchRspItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchRspItem>, I>>(object: I): BatchRspItem {
    const message = createBaseBatchRspItem();
    message.result =
      object.result !== undefined && object.result !== null ? RspNotifyControl.fromPartial(object.result) : undefined;
    message.rsp_body = object.rsp_body ?? new Uint8Array(0);
    return message;
  }
};

function createBaseBatchRsp(): BatchRsp {
  return { rsps: [] };
}

export const BatchRsp: MessageFns<BatchRsp> = {
  fromJSON(object: any): BatchRsp {
    return {
      rsps: globalThis.Array.isArray(object?.rsps) ? object.rsps.map((e: any) => BatchRspItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchRsp>, I>>(base?: I): BatchRsp {
    return BatchRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchRsp>, I>>(object: I): BatchRsp {
    const message = createBaseBatchRsp();
    message.rsps = object.rsps?.map(e => BatchRspItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotify(): Notify {
  return { notify_ent_key: '' };
}

export const Notify: MessageFns<Notify> = {
  fromJSON(object: any): Notify {
    return { notify_ent_key: isSet(object.notify_ent_key) ? globalThis.String(object.notify_ent_key) : '' };
  },

  create<I extends Exact<DeepPartial<Notify>, I>>(base?: I): Notify {
    return Notify.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notify>, I>>(object: I): Notify {
    const message = createBaseNotify();
    message.notify_ent_key = object.notify_ent_key ?? '';
    return message;
  }
};

/** serviceName: api.gateway.cproxy */
export type CProxyDefinition = typeof CProxyDefinition;
export const CProxyDefinition = {
  name: 'CProxy',
  fullName: 'cproxy.CProxy',
  methods: {
    /** 兼容旧框架的http请求 */
    sendHTTP: {
      name: 'SendHTTP',
      requestType: SendHTTPReq,
      requestStream: false,
      responseType: SendHTTPRsp,
      responseStream: false,
      options: {}
    },
    /** 批量请求 */
    batch: {
      name: 'Batch',
      requestType: BatchReq,
      requestStream: false,
      responseType: BatchRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

function bytesFromBase64(b64: string): Uint8Array {
  if ((globalThis as any).Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
