// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/ctask/handler.proto

/* eslint-disable */
import { Reward, Task } from './task';

export const protobufPackage = 'comm.api.ctask';

export enum Code {
  CODE_OK = 0,
  CODE_VERSION_NOT_MATCH = 20001,
  CODE_INVENTORY_NOT_ENOUGH = 20002,
  UNRECOGNIZED = -1
}

export function codeFromJSON(object: any): Code {
  switch (object) {
    case 0:
    case 'CODE_OK':
      return Code.CODE_OK;
    case 20001:
    case 'CODE_VERSION_NOT_MATCH':
      return Code.CODE_VERSION_NOT_MATCH;
    case 20002:
    case 'CODE_INVENTORY_NOT_ENOUGH':
      return Code.CODE_INVENTORY_NOT_ENOUGH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Code.UNRECOGNIZED;
  }
}

export interface BaseReq {
  /** name/template由后端提供 */
  name: string;
  /** 类似于活动id的定义 */
  template: string;
}

export interface ListTasksReq {
  base_req: BaseReq | undefined;
  /**
   * 被查询的对象，即要查谁的任务
   * 用户视角有两种对象，1是发起查询动作的对象（公参uid），2是被查询的对象，往往1和2是同一个
   * e.g. 用户查自己的用户任务，member=公参uid；用户查自己的房间任务，member=房间id
   */
  member: string;
}

export interface ListTasksRsp {
  ret: ListTasksRet | undefined;
}

export interface ListTasksRet {
  /** 任务列表 */
  tasks: Task[];
  /** 版本号，领取奖励时前端需要传回来，后端检查配置是否一致 */
  version: number;
}

export interface DoTaskReq {
  base_req: BaseReq | undefined;
  /** 由 ListTasksRet 获得，后端用于校验配置是否已经发生更改 */
  version: number;
  /** 同 ListTasksReq.Member */
  member: string;
  /** 任务id */
  task_id: string;
  /** 要增加的进度值 */
  incr_progress: number;
}

export interface DoTaskRsp {
  ret: DoTaskRet | undefined;
}

export interface DoTaskRet {
  /** 实际加的进度值 */
  incr_progress: number;
  /** 新的进度值，当incr_progress>0时才会赋值 */
  new_progress: number;
}

export interface TakeRewardReq {
  base_req: BaseReq | undefined;
  /** 由 ListTasksRet 获得，后端用于校验配置是否已经发生更改 */
  version: number;
  /** 同 ListTasksReq.Member */
  member: string;
  /** 要领取的任务id */
  task_id: string;
  /**
   * 领取指定阶段时传对应阶段的索引值（从0开始）
   * 一键领取不传
   */
  stages: number[];
  /** 领取的奖励数量，0表示领取所有 */
  count: number;
}

export interface TakeRewardRsp {
  ret: TakeRewardRet | undefined;
}

export interface TakeRewardRet {
  rewards: Reward[];
}

export interface BatchListTasksReq {
  /** name/template由后端提供 */
  name: string;
  template: string[];
  /** 被查询的对象，即要查谁的任务 */
  member: string;
}

export interface BatchListTasksRsp {
  ret: BatchListTasksRet | undefined;
}

export interface BatchListTasksRet {
  batch_tasks: { [key: string]: ListTasksRet };
}

export interface BatchListTasksRet_BatchTasksEntry {
  key: string;
  value: ListTasksRet | undefined;
}

function createBaseBaseReq(): BaseReq {
  return { name: '', template: '' };
}

export const BaseReq: MessageFns<BaseReq> = {
  fromJSON(object: any): BaseReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      template: isSet(object.template) ? globalThis.String(object.template) : ''
    };
  },

  create<I extends Exact<DeepPartial<BaseReq>, I>>(base?: I): BaseReq {
    return BaseReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BaseReq>, I>>(object: I): BaseReq {
    const message = createBaseBaseReq();
    message.name = object.name ?? '';
    message.template = object.template ?? '';
    return message;
  }
};

function createBaseListTasksReq(): ListTasksReq {
  return { base_req: undefined, member: '' };
}

export const ListTasksReq: MessageFns<ListTasksReq> = {
  fromJSON(object: any): ListTasksReq {
    return {
      base_req: isSet(object.base_req) ? BaseReq.fromJSON(object.base_req) : undefined,
      member: isSet(object.member) ? globalThis.String(object.member) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListTasksReq>, I>>(base?: I): ListTasksReq {
    return ListTasksReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTasksReq>, I>>(object: I): ListTasksReq {
    const message = createBaseListTasksReq();
    message.base_req =
      object.base_req !== undefined && object.base_req !== null ? BaseReq.fromPartial(object.base_req) : undefined;
    message.member = object.member ?? '';
    return message;
  }
};

function createBaseListTasksRsp(): ListTasksRsp {
  return { ret: undefined };
}

export const ListTasksRsp: MessageFns<ListTasksRsp> = {
  fromJSON(object: any): ListTasksRsp {
    return { ret: isSet(object.ret) ? ListTasksRet.fromJSON(object.ret) : undefined };
  },

  create<I extends Exact<DeepPartial<ListTasksRsp>, I>>(base?: I): ListTasksRsp {
    return ListTasksRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTasksRsp>, I>>(object: I): ListTasksRsp {
    const message = createBaseListTasksRsp();
    message.ret = object.ret !== undefined && object.ret !== null ? ListTasksRet.fromPartial(object.ret) : undefined;
    return message;
  }
};

function createBaseListTasksRet(): ListTasksRet {
  return { tasks: [], version: 0 };
}

export const ListTasksRet: MessageFns<ListTasksRet> = {
  fromJSON(object: any): ListTasksRet {
    return {
      tasks: globalThis.Array.isArray(object?.tasks) ? object.tasks.map((e: any) => Task.fromJSON(e)) : [],
      version: isSet(object.version) ? globalThis.Number(object.version) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListTasksRet>, I>>(base?: I): ListTasksRet {
    return ListTasksRet.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTasksRet>, I>>(object: I): ListTasksRet {
    const message = createBaseListTasksRet();
    message.tasks = object.tasks?.map(e => Task.fromPartial(e)) || [];
    message.version = object.version ?? 0;
    return message;
  }
};

function createBaseDoTaskReq(): DoTaskReq {
  return { base_req: undefined, version: 0, member: '', task_id: '', incr_progress: 0 };
}

export const DoTaskReq: MessageFns<DoTaskReq> = {
  fromJSON(object: any): DoTaskReq {
    return {
      base_req: isSet(object.base_req) ? BaseReq.fromJSON(object.base_req) : undefined,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      task_id: isSet(object.task_id) ? globalThis.String(object.task_id) : '',
      incr_progress: isSet(object.incr_progress) ? globalThis.Number(object.incr_progress) : 0
    };
  },

  create<I extends Exact<DeepPartial<DoTaskReq>, I>>(base?: I): DoTaskReq {
    return DoTaskReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoTaskReq>, I>>(object: I): DoTaskReq {
    const message = createBaseDoTaskReq();
    message.base_req =
      object.base_req !== undefined && object.base_req !== null ? BaseReq.fromPartial(object.base_req) : undefined;
    message.version = object.version ?? 0;
    message.member = object.member ?? '';
    message.task_id = object.task_id ?? '';
    message.incr_progress = object.incr_progress ?? 0;
    return message;
  }
};

function createBaseDoTaskRsp(): DoTaskRsp {
  return { ret: undefined };
}

export const DoTaskRsp: MessageFns<DoTaskRsp> = {
  fromJSON(object: any): DoTaskRsp {
    return { ret: isSet(object.ret) ? DoTaskRet.fromJSON(object.ret) : undefined };
  },

  create<I extends Exact<DeepPartial<DoTaskRsp>, I>>(base?: I): DoTaskRsp {
    return DoTaskRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoTaskRsp>, I>>(object: I): DoTaskRsp {
    const message = createBaseDoTaskRsp();
    message.ret = object.ret !== undefined && object.ret !== null ? DoTaskRet.fromPartial(object.ret) : undefined;
    return message;
  }
};

function createBaseDoTaskRet(): DoTaskRet {
  return { incr_progress: 0, new_progress: 0 };
}

export const DoTaskRet: MessageFns<DoTaskRet> = {
  fromJSON(object: any): DoTaskRet {
    return {
      incr_progress: isSet(object.incr_progress) ? globalThis.Number(object.incr_progress) : 0,
      new_progress: isSet(object.new_progress) ? globalThis.Number(object.new_progress) : 0
    };
  },

  create<I extends Exact<DeepPartial<DoTaskRet>, I>>(base?: I): DoTaskRet {
    return DoTaskRet.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoTaskRet>, I>>(object: I): DoTaskRet {
    const message = createBaseDoTaskRet();
    message.incr_progress = object.incr_progress ?? 0;
    message.new_progress = object.new_progress ?? 0;
    return message;
  }
};

function createBaseTakeRewardReq(): TakeRewardReq {
  return { base_req: undefined, version: 0, member: '', task_id: '', stages: [], count: 0 };
}

export const TakeRewardReq: MessageFns<TakeRewardReq> = {
  fromJSON(object: any): TakeRewardReq {
    return {
      base_req: isSet(object.base_req) ? BaseReq.fromJSON(object.base_req) : undefined,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      task_id: isSet(object.task_id) ? globalThis.String(object.task_id) : '',
      stages: globalThis.Array.isArray(object?.stages) ? object.stages.map((e: any) => globalThis.Number(e)) : [],
      count: isSet(object.count) ? globalThis.Number(object.count) : 0
    };
  },

  create<I extends Exact<DeepPartial<TakeRewardReq>, I>>(base?: I): TakeRewardReq {
    return TakeRewardReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TakeRewardReq>, I>>(object: I): TakeRewardReq {
    const message = createBaseTakeRewardReq();
    message.base_req =
      object.base_req !== undefined && object.base_req !== null ? BaseReq.fromPartial(object.base_req) : undefined;
    message.version = object.version ?? 0;
    message.member = object.member ?? '';
    message.task_id = object.task_id ?? '';
    message.stages = object.stages?.map(e => e) || [];
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseTakeRewardRsp(): TakeRewardRsp {
  return { ret: undefined };
}

export const TakeRewardRsp: MessageFns<TakeRewardRsp> = {
  fromJSON(object: any): TakeRewardRsp {
    return { ret: isSet(object.ret) ? TakeRewardRet.fromJSON(object.ret) : undefined };
  },

  create<I extends Exact<DeepPartial<TakeRewardRsp>, I>>(base?: I): TakeRewardRsp {
    return TakeRewardRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TakeRewardRsp>, I>>(object: I): TakeRewardRsp {
    const message = createBaseTakeRewardRsp();
    message.ret = object.ret !== undefined && object.ret !== null ? TakeRewardRet.fromPartial(object.ret) : undefined;
    return message;
  }
};

function createBaseTakeRewardRet(): TakeRewardRet {
  return { rewards: [] };
}

export const TakeRewardRet: MessageFns<TakeRewardRet> = {
  fromJSON(object: any): TakeRewardRet {
    return {
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<TakeRewardRet>, I>>(base?: I): TakeRewardRet {
    return TakeRewardRet.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TakeRewardRet>, I>>(object: I): TakeRewardRet {
    const message = createBaseTakeRewardRet();
    message.rewards = object.rewards?.map(e => Reward.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchListTasksReq(): BatchListTasksReq {
  return { name: '', template: [], member: '' };
}

export const BatchListTasksReq: MessageFns<BatchListTasksReq> = {
  fromJSON(object: any): BatchListTasksReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      template: globalThis.Array.isArray(object?.template) ? object.template.map((e: any) => globalThis.String(e)) : [],
      member: isSet(object.member) ? globalThis.String(object.member) : ''
    };
  },

  create<I extends Exact<DeepPartial<BatchListTasksReq>, I>>(base?: I): BatchListTasksReq {
    return BatchListTasksReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchListTasksReq>, I>>(object: I): BatchListTasksReq {
    const message = createBaseBatchListTasksReq();
    message.name = object.name ?? '';
    message.template = object.template?.map(e => e) || [];
    message.member = object.member ?? '';
    return message;
  }
};

function createBaseBatchListTasksRsp(): BatchListTasksRsp {
  return { ret: undefined };
}

export const BatchListTasksRsp: MessageFns<BatchListTasksRsp> = {
  fromJSON(object: any): BatchListTasksRsp {
    return { ret: isSet(object.ret) ? BatchListTasksRet.fromJSON(object.ret) : undefined };
  },

  create<I extends Exact<DeepPartial<BatchListTasksRsp>, I>>(base?: I): BatchListTasksRsp {
    return BatchListTasksRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchListTasksRsp>, I>>(object: I): BatchListTasksRsp {
    const message = createBaseBatchListTasksRsp();
    message.ret =
      object.ret !== undefined && object.ret !== null ? BatchListTasksRet.fromPartial(object.ret) : undefined;
    return message;
  }
};

function createBaseBatchListTasksRet(): BatchListTasksRet {
  return { batch_tasks: {} };
}

export const BatchListTasksRet: MessageFns<BatchListTasksRet> = {
  fromJSON(object: any): BatchListTasksRet {
    return {
      batch_tasks: isObject(object.batch_tasks)
        ? Object.entries(object.batch_tasks).reduce<{ [key: string]: ListTasksRet }>((acc, [key, value]) => {
            acc[key] = ListTasksRet.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchListTasksRet>, I>>(base?: I): BatchListTasksRet {
    return BatchListTasksRet.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchListTasksRet>, I>>(object: I): BatchListTasksRet {
    const message = createBaseBatchListTasksRet();
    message.batch_tasks = Object.entries(object.batch_tasks ?? {}).reduce<{ [key: string]: ListTasksRet }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = ListTasksRet.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchListTasksRet_BatchTasksEntry(): BatchListTasksRet_BatchTasksEntry {
  return { key: '', value: undefined };
}

export const BatchListTasksRet_BatchTasksEntry: MessageFns<BatchListTasksRet_BatchTasksEntry> = {
  fromJSON(object: any): BatchListTasksRet_BatchTasksEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? ListTasksRet.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchListTasksRet_BatchTasksEntry>, I>>(
    base?: I
  ): BatchListTasksRet_BatchTasksEntry {
    return BatchListTasksRet_BatchTasksEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchListTasksRet_BatchTasksEntry>, I>>(
    object: I
  ): BatchListTasksRet_BatchTasksEntry {
    const message = createBaseBatchListTasksRet_BatchTasksEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? ListTasksRet.fromPartial(object.value) : undefined;
    return message;
  }
};

export type CTaskDefinition = typeof CTaskDefinition;
export const CTaskDefinition = {
  name: 'CTask',
  fullName: 'comm.api.ctask.CTask',
  methods: {
    /** 按活动的维度查询对象的所有任务 */
    listTasks: {
      name: 'ListTasks',
      requestType: ListTasksReq,
      requestStream: false,
      responseType: ListTasksRsp,
      responseStream: false,
      options: {}
    },
    /**
     * 做任务，给任务加进度
     * 版本号不匹配时，提示用户刷新：CODE_VERSION_NOT_MATCH
     */
    doTask: {
      name: 'DoTask',
      requestType: DoTaskReq,
      requestStream: false,
      responseType: DoTaskRsp,
      responseStream: false,
      options: {}
    },
    /**
     * 领取任务奖励
     * 版本号不匹配时，提示用户刷新：CODE_VERSION_NOT_MATCH
     */
    takeReward: {
      name: 'TakeReward',
      requestType: TakeRewardReq,
      requestStream: false,
      responseType: TakeRewardRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询（用于不是基于活动层次去组织任务的情况） */
    batchListTasks: {
      name: 'BatchListTasks',
      requestType: BatchListTasksReq,
      requestStream: false,
      responseType: BatchListTasksRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
