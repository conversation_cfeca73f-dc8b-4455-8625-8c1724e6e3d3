// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/api/ctask/task.proto

/* eslint-disable */

export const protobufPackage = 'comm.api.ctask';

export interface Task {
  /** 任务id */
  id: string;
  /** 展示信息 */
  show: TaskShow | undefined;
  /**
   * 版本号，无需关心具体值，透传即可
   * 领取奖励等接口可能要求带回来，如果产运修改了配置，方便后端做策略
   */
  version: number;
  /**
   * 最多可循环完成几次，即进度条可以循环填满几次
   * <=0表示不限制
   */
  max_times: number;
  /**
   * 每个阶段的进度、状态等信息
   * 一个任务可分多个阶段进行，至少有一个阶段
   * 最后一个阶段的进度值就是总的进度条长度
   */
  stages: Stage[];
  /** 进度条已经填充的长度，需要配合stages字段控制进度条的百分比 */
  progress: number;
  /**
   * 任务是否已经完成
   * 通常用于UI展示或控制是否可以查看下一个任务
   */
  has_completed: boolean;
  /**
   * 奖励领取是否受限制
   * 用于该任务有前置任务的情况，领取奖励前需要前置任务完成
   */
  reward_restricted: boolean;
  /**
   * 倒计时秒数，周期性任务才存在
   * <=0不展示倒计时
   */
  countdown: number;
  /**
   * 聚焦阶段的索引（-1表示无需聚焦，0表示第一个阶段）
   * 例如签到场景，前端需要定位到今天是第几天
   */
  focus_stage: number;
  /** 进度条汇总数据 */
  progress_bar: ProgressBar | undefined;
  /** 子任务列表 */
  subs: Task[];
}

/** 前端展示信息，后端不做逻辑使用 */
export interface TaskShow {
  /** 多语言名称（语言二字码 -> 文案） */
  name_i18n: { [key: string]: string };
  /** 多语言描述（语言二字码 -> 文案） */
  description_i18n: { [key: string]: string };
  /** 跳转 */
  deeplink: string;
  /** 图标 */
  icon: string;
  /** 前端映射样式配置的id */
  id: string;
  /**
   * 进度值除数，如果>0，progress相关字段需要除以该除数
   * 用于展示和统计的粒度不一致的情况，通常计算的粒度会比展示的粒度要细，而不会更粗
   * 例如前端是按分钟展示，而后端是按秒统计，则前端需要除以这个除数再展示
   */
  progress_divisor: number;
  /**
   * 分组，后端给定
   * 用于区分任务组的情况。例如A组一个区域和样式展示，B组另外一个区域和样式展示
   */
  group: string;
  /** 扩展字段 */
  extend: string;
}

export interface TaskShow_NameI18nEntry {
  key: string;
  value: string;
}

export interface TaskShow_DescriptionI18nEntry {
  key: string;
  value: string;
}

/**
 * 常用状态（斜杠仅是展示字符，非算术除法）
 * 可循环任务，一般是展示数量
 *    进度条展示：progress(当前进度) / need_progress(进度条长度)
 *    完成次数展示：completed_count(已完成次数) / max_count(总共能完成几次)
 *    奖励领取次数展示：taken_reward_count(已领取次数) / completed_count(目前总共可领取次数)
 * 只能循环一次的任务，一般是展示状态
 *    未完成状态：completed_count == 0
 *    已完成但未领取状态：completed_count > 0 && taken_reward_count == 0
 *    已完成且已领取状态：completed_count > 0 && taken_reward_count > 0
 */
export interface Stage {
  /** 完成该阶段需要的进度 */
  need_progress: number;
  /** 奖励信息 */
  reward: Reward | undefined;
  /** 已完成任务次数 */
  completed_count: number;
  /** 已领取奖励次数 */
  taken_reward_count: number;
  /** 领取奖励是否受限制, 组合+里程碑 任务用来限制这个阶段任务是否可以领取奖励 */
  reward_restricted: boolean;
}

export interface Reward {
  /** 奖励ID */
  id: string;
  /** 数量 */
  count: number;
  /** 奖励信息 */
  extend: string;
}

/** 进度条数据 */
export interface ProgressBar {
  /** 循环后所处阶段索引，-1表示一个阶段都未达成，0表示完成第一个阶段，达到最大循环时为最大阶段索引 */
  current_stage: number;
  /** 达到下一个阶段还需要的进度，0表示已达到最大 */
  next_stage_need: number;
}

function createBaseTask(): Task {
  return {
    id: '',
    show: undefined,
    version: 0,
    max_times: 0,
    stages: [],
    progress: 0,
    has_completed: false,
    reward_restricted: false,
    countdown: 0,
    focus_stage: 0,
    progress_bar: undefined,
    subs: []
  };
}

export const Task: MessageFns<Task> = {
  fromJSON(object: any): Task {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      show: isSet(object.show) ? TaskShow.fromJSON(object.show) : undefined,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      max_times: isSet(object.max_times) ? globalThis.Number(object.max_times) : 0,
      stages: globalThis.Array.isArray(object?.stages) ? object.stages.map((e: any) => Stage.fromJSON(e)) : [],
      progress: isSet(object.progress) ? globalThis.Number(object.progress) : 0,
      has_completed: isSet(object.has_completed) ? globalThis.Boolean(object.has_completed) : false,
      reward_restricted: isSet(object.reward_restricted) ? globalThis.Boolean(object.reward_restricted) : false,
      countdown: isSet(object.countdown) ? globalThis.Number(object.countdown) : 0,
      focus_stage: isSet(object.focus_stage) ? globalThis.Number(object.focus_stage) : 0,
      progress_bar: isSet(object.progress_bar) ? ProgressBar.fromJSON(object.progress_bar) : undefined,
      subs: globalThis.Array.isArray(object?.subs) ? object.subs.map((e: any) => Task.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Task>, I>>(base?: I): Task {
    return Task.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Task>, I>>(object: I): Task {
    const message = createBaseTask();
    message.id = object.id ?? '';
    message.show = object.show !== undefined && object.show !== null ? TaskShow.fromPartial(object.show) : undefined;
    message.version = object.version ?? 0;
    message.max_times = object.max_times ?? 0;
    message.stages = object.stages?.map(e => Stage.fromPartial(e)) || [];
    message.progress = object.progress ?? 0;
    message.has_completed = object.has_completed ?? false;
    message.reward_restricted = object.reward_restricted ?? false;
    message.countdown = object.countdown ?? 0;
    message.focus_stage = object.focus_stage ?? 0;
    message.progress_bar =
      object.progress_bar !== undefined && object.progress_bar !== null
        ? ProgressBar.fromPartial(object.progress_bar)
        : undefined;
    message.subs = object.subs?.map(e => Task.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTaskShow(): TaskShow {
  return {
    name_i18n: {},
    description_i18n: {},
    deeplink: '',
    icon: '',
    id: '',
    progress_divisor: 0,
    group: '',
    extend: ''
  };
}

export const TaskShow: MessageFns<TaskShow> = {
  fromJSON(object: any): TaskShow {
    return {
      name_i18n: isObject(object.name_i18n)
        ? Object.entries(object.name_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      description_i18n: isObject(object.description_i18n)
        ? Object.entries(object.description_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      deeplink: isSet(object.deeplink) ? globalThis.String(object.deeplink) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      progress_divisor: isSet(object.progress_divisor) ? globalThis.Number(object.progress_divisor) : 0,
      group: isSet(object.group) ? globalThis.String(object.group) : '',
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<TaskShow>, I>>(base?: I): TaskShow {
    return TaskShow.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskShow>, I>>(object: I): TaskShow {
    const message = createBaseTaskShow();
    message.name_i18n = Object.entries(object.name_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.description_i18n = Object.entries(object.description_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.deeplink = object.deeplink ?? '';
    message.icon = object.icon ?? '';
    message.id = object.id ?? '';
    message.progress_divisor = object.progress_divisor ?? 0;
    message.group = object.group ?? '';
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseTaskShow_NameI18nEntry(): TaskShow_NameI18nEntry {
  return { key: '', value: '' };
}

export const TaskShow_NameI18nEntry: MessageFns<TaskShow_NameI18nEntry> = {
  fromJSON(object: any): TaskShow_NameI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<TaskShow_NameI18nEntry>, I>>(base?: I): TaskShow_NameI18nEntry {
    return TaskShow_NameI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskShow_NameI18nEntry>, I>>(object: I): TaskShow_NameI18nEntry {
    const message = createBaseTaskShow_NameI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseTaskShow_DescriptionI18nEntry(): TaskShow_DescriptionI18nEntry {
  return { key: '', value: '' };
}

export const TaskShow_DescriptionI18nEntry: MessageFns<TaskShow_DescriptionI18nEntry> = {
  fromJSON(object: any): TaskShow_DescriptionI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<TaskShow_DescriptionI18nEntry>, I>>(base?: I): TaskShow_DescriptionI18nEntry {
    return TaskShow_DescriptionI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskShow_DescriptionI18nEntry>, I>>(
    object: I
  ): TaskShow_DescriptionI18nEntry {
    const message = createBaseTaskShow_DescriptionI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseStage(): Stage {
  return { need_progress: 0, reward: undefined, completed_count: 0, taken_reward_count: 0, reward_restricted: false };
}

export const Stage: MessageFns<Stage> = {
  fromJSON(object: any): Stage {
    return {
      need_progress: isSet(object.need_progress) ? globalThis.Number(object.need_progress) : 0,
      reward: isSet(object.reward) ? Reward.fromJSON(object.reward) : undefined,
      completed_count: isSet(object.completed_count) ? globalThis.Number(object.completed_count) : 0,
      taken_reward_count: isSet(object.taken_reward_count) ? globalThis.Number(object.taken_reward_count) : 0,
      reward_restricted: isSet(object.reward_restricted) ? globalThis.Boolean(object.reward_restricted) : false
    };
  },

  create<I extends Exact<DeepPartial<Stage>, I>>(base?: I): Stage {
    return Stage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Stage>, I>>(object: I): Stage {
    const message = createBaseStage();
    message.need_progress = object.need_progress ?? 0;
    message.reward =
      object.reward !== undefined && object.reward !== null ? Reward.fromPartial(object.reward) : undefined;
    message.completed_count = object.completed_count ?? 0;
    message.taken_reward_count = object.taken_reward_count ?? 0;
    message.reward_restricted = object.reward_restricted ?? false;
    return message;
  }
};

function createBaseReward(): Reward {
  return { id: '', count: 0, extend: '' };
}

export const Reward: MessageFns<Reward> = {
  fromJSON(object: any): Reward {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
      extend: isSet(object.extend) ? globalThis.String(object.extend) : ''
    };
  },

  create<I extends Exact<DeepPartial<Reward>, I>>(base?: I): Reward {
    return Reward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reward>, I>>(object: I): Reward {
    const message = createBaseReward();
    message.id = object.id ?? '';
    message.count = object.count ?? 0;
    message.extend = object.extend ?? '';
    return message;
  }
};

function createBaseProgressBar(): ProgressBar {
  return { current_stage: 0, next_stage_need: 0 };
}

export const ProgressBar: MessageFns<ProgressBar> = {
  fromJSON(object: any): ProgressBar {
    return {
      current_stage: isSet(object.current_stage) ? globalThis.Number(object.current_stage) : 0,
      next_stage_need: isSet(object.next_stage_need) ? globalThis.Number(object.next_stage_need) : 0
    };
  },

  create<I extends Exact<DeepPartial<ProgressBar>, I>>(base?: I): ProgressBar {
    return ProgressBar.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProgressBar>, I>>(object: I): ProgressBar {
    const message = createBaseProgressBar();
    message.current_stage = object.current_stage ?? 0;
    message.next_stage_need = object.next_stage_need ?? 0;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
