// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/svr/platform/pkgs.proto

/* eslint-disable */
import { OSType, oSTypeFromJSON } from '../../api/common/common-net';

export const protobufPackage = 'comm.svr.platform.pkgs';

export interface GetPackagesReq {
  /** 马甲名，如果没有，则不填 */
  sockpuppet: string;
}

export interface PkgOstype {
  pkg: string;
  os_type: OSType;
}

export interface GetPackagesRsp {
  /** 所有包名，包括ios和android */
  pkgs: PkgOstype[];
}

function createBaseGetPackagesReq(): GetPackagesReq {
  return { sockpuppet: '' };
}

export const GetPackagesReq: MessageFns<GetPackagesReq> = {
  fromJSON(object: any): GetPackagesReq {
    return { sockpuppet: isSet(object.sockpuppet) ? globalThis.String(object.sockpuppet) : '' };
  },

  create<I extends Exact<DeepPartial<GetPackagesReq>, I>>(base?: I): GetPackagesReq {
    return GetPackagesReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPackagesReq>, I>>(object: I): GetPackagesReq {
    const message = createBaseGetPackagesReq();
    message.sockpuppet = object.sockpuppet ?? '';
    return message;
  }
};

function createBasePkgOstype(): PkgOstype {
  return { pkg: '', os_type: 0 };
}

export const PkgOstype: MessageFns<PkgOstype> = {
  fromJSON(object: any): PkgOstype {
    return {
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<PkgOstype>, I>>(base?: I): PkgOstype {
    return PkgOstype.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PkgOstype>, I>>(object: I): PkgOstype {
    const message = createBasePkgOstype();
    message.pkg = object.pkg ?? '';
    message.os_type = object.os_type ?? 0;
    return message;
  }
};

function createBaseGetPackagesRsp(): GetPackagesRsp {
  return { pkgs: [] };
}

export const GetPackagesRsp: MessageFns<GetPackagesRsp> = {
  fromJSON(object: any): GetPackagesRsp {
    return { pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => PkgOstype.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetPackagesRsp>, I>>(base?: I): GetPackagesRsp {
    return GetPackagesRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPackagesRsp>, I>>(object: I): GetPackagesRsp {
    const message = createBaseGetPackagesRsp();
    message.pkgs = object.pkgs?.map(e => PkgOstype.fromPartial(e)) || [];
    return message;
  }
};

/** 获取包名列表,业务后端实现，由中台调用 */
export type PackagesSvrDefinition = typeof PackagesSvrDefinition;
export const PackagesSvrDefinition = {
  name: 'PackagesSvr',
  fullName: 'comm.svr.platform.pkgs.PackagesSvr',
  methods: {
    /** 根据in.app 来获取包名列表 */
    getPackages: {
      name: 'GetPackages',
      requestType: GetPackagesReq,
      requestStream: false,
      responseType: GetPackagesRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
