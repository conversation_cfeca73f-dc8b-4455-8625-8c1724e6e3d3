// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/customer/complaint.proto

/* eslint-disable */
import { Reply } from '../../api/customer/complaint';

export const protobufPackage = 'comm.mgr.customer';

export enum Operate {
  /** OPERATE_NONE - 无 */
  OPERATE_NONE = 0,
  /** OPERATE_CREATE - 创建 */
  OPERATE_CREATE = 1,
  /** OPERATE_UPDATE - 更新 */
  OPERATE_UPDATE = 2,
  /** OPERATE_SEACH - 查询 */
  OPERATE_SEACH = 3,
  UNRECOGNIZED = -1
}

export function operateFromJSON(object: any): Operate {
  switch (object) {
    case 0:
    case 'OPERATE_NONE':
      return Operate.OPERATE_NONE;
    case 1:
    case 'OPERATE_CREATE':
      return Operate.OPERATE_CREATE;
    case 2:
    case 'OPERATE_UPDATE':
      return Operate.OPERATE_UPDATE;
    case 3:
    case 'OPERATE_SEACH':
      return Operate.OPERATE_SEACH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Operate.UNRECOGNIZED;
  }
}

/** 相关分类枚举 */
export enum CategoryType {
  /** CATEGORY_TYPE_NONE - 无 */
  CATEGORY_TYPE_NONE = 0,
  /** CATEGORY_TYPE_PAY - 支付类型 */
  CATEGORY_TYPE_PAY = 1,
  /** CATEGORY_TYPE_OTHERS - 其他类型 */
  CATEGORY_TYPE_OTHERS = 2,
  /** CATEGORY_TYPE_Withdraw - 提现类型 */
  CATEGORY_TYPE_Withdraw = 3,
  UNRECOGNIZED = -1
}

export function categoryTypeFromJSON(object: any): CategoryType {
  switch (object) {
    case 0:
    case 'CATEGORY_TYPE_NONE':
      return CategoryType.CATEGORY_TYPE_NONE;
    case 1:
    case 'CATEGORY_TYPE_PAY':
      return CategoryType.CATEGORY_TYPE_PAY;
    case 2:
    case 'CATEGORY_TYPE_OTHERS':
      return CategoryType.CATEGORY_TYPE_OTHERS;
    case 3:
    case 'CATEGORY_TYPE_Withdraw':
      return CategoryType.CATEGORY_TYPE_Withdraw;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CategoryType.UNRECOGNIZED;
  }
}

export enum State {
  /** STATE_NONE - 无 */
  STATE_NONE = 0,
  /** STATE_RESOLVED - 已通过 */
  STATE_RESOLVED = 1,
  /** STATE_PROCESSING - 处理中 */
  STATE_PROCESSING = 2,
  /** STATE_INVALID - 拒绝 */
  STATE_INVALID = 3,
  /** STATE_PENDING - 挂起 */
  STATE_PENDING = 4,
  UNRECOGNIZED = -1
}

export function stateFromJSON(object: any): State {
  switch (object) {
    case 0:
    case 'STATE_NONE':
      return State.STATE_NONE;
    case 1:
    case 'STATE_RESOLVED':
      return State.STATE_RESOLVED;
    case 2:
    case 'STATE_PROCESSING':
      return State.STATE_PROCESSING;
    case 3:
    case 'STATE_INVALID':
      return State.STATE_INVALID;
    case 4:
    case 'STATE_PENDING':
      return State.STATE_PENDING;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return State.UNRECOGNIZED;
  }
}

/** 用户角色 */
export enum UserRole {
  USER_ROLE_NONE = 0,
  /** USER_ROLE_NORMAL - 普通用户 */
  USER_ROLE_NORMAL = 1,
  /** USER_ROLE_GUILD - 公会长 */
  USER_ROLE_GUILD = 2,
  UNRECOGNIZED = -1
}

export function userRoleFromJSON(object: any): UserRole {
  switch (object) {
    case 0:
    case 'USER_ROLE_NONE':
      return UserRole.USER_ROLE_NONE;
    case 1:
    case 'USER_ROLE_NORMAL':
      return UserRole.USER_ROLE_NORMAL;
    case 2:
    case 'USER_ROLE_GUILD':
      return UserRole.USER_ROLE_GUILD;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserRole.UNRECOGNIZED;
  }
}

export enum WealthSection {
  WEALTH_SECTION_NONE = 0,
  /** WEALTH_SECTION_UNDER_SIX - [0-6] */
  WEALTH_SECTION_UNDER_SIX = 1,
  /** WEALTH_SECTION_ABOVE_SIX - > 6 */
  WEALTH_SECTION_ABOVE_SIX = 2,
  UNRECOGNIZED = -1
}

export function wealthSectionFromJSON(object: any): WealthSection {
  switch (object) {
    case 0:
    case 'WEALTH_SECTION_NONE':
      return WealthSection.WEALTH_SECTION_NONE;
    case 1:
    case 'WEALTH_SECTION_UNDER_SIX':
      return WealthSection.WEALTH_SECTION_UNDER_SIX;
    case 2:
    case 'WEALTH_SECTION_ABOVE_SIX':
      return WealthSection.WEALTH_SECTION_ABOVE_SIX;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return WealthSection.UNRECOGNIZED;
  }
}

/** track 状态 */
export enum TrackingStatus {
  /** TRACKING_STATUS_NONE - none */
  TRACKING_STATUS_NONE = 0,
  /** TRACKING_STATUS_NO_TRACKE - no track */
  TRACKING_STATUS_NO_TRACKE = 1,
  /** TRACKING_STATUS_TRACKING - tracking */
  TRACKING_STATUS_TRACKING = 2,
  /** TRACKING_STATUS_NULL - 空值 */
  TRACKING_STATUS_NULL = 3,
  UNRECOGNIZED = -1
}

export function trackingStatusFromJSON(object: any): TrackingStatus {
  switch (object) {
    case 0:
    case 'TRACKING_STATUS_NONE':
      return TrackingStatus.TRACKING_STATUS_NONE;
    case 1:
    case 'TRACKING_STATUS_NO_TRACKE':
      return TrackingStatus.TRACKING_STATUS_NO_TRACKE;
    case 2:
    case 'TRACKING_STATUS_TRACKING':
      return TrackingStatus.TRACKING_STATUS_TRACKING;
    case 3:
    case 'TRACKING_STATUS_NULL':
      return TrackingStatus.TRACKING_STATUS_NULL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TrackingStatus.UNRECOGNIZED;
  }
}

export interface SearchComplaintTypeReq {
  /** 分类ID */
  complaint_category_id: number;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface SearchComplaintTypeRsp {
  /** 申诉类型详情 */
  complaint_types: ComplaintType[];
}

export interface ListPkgReq {
  /** 状态数组,审核记录{1,3} 审核列表{2,4} */
  states: State[];
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface ListPkgRsp {
  /** pkg名称 */
  name: string[];
}

export interface UpdateStateReq {
  /** 记录id */
  id: number[];
  /** 更新状态 */
  state: State;
  /** 操作人 */
  operator: string;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface UpdateStateRsp {}

export interface Pagination {
  /** 页码 */
  page: number;
  /** 页数 */
  pageSize: number;
  /** 总数 */
  count: number;
}

export interface Category {
  /** 分类id */
  id: number;
  /** 投诉类型 名称中文 */
  name: string;
  /** 创建人 */
  created_by: string;
  /** 编辑人 */
  updated_by: string;
  /** 创建时间 */
  created_at: number;
  /** 更新时间 */
  updated_at: number;
  /** 投诉类型 名称阿语 */
  name_ar: string;
  /** 投诉类型 名称土耳其 */
  name_tr: string;
  /** 投诉类型 名称英语 */
  name_en: string;
  /** 类型枚举 */
  category_type: CategoryType;
}

export interface ComplaintType {
  /** 申诉类型id */
  id: number;
  /** 分类ID */
  complaint_category_id: number;
  /** 申诉类型多语言中文 */
  type_text: string;
  /** icon */
  icon: string;
  /** 是否显示在投诉页 */
  is_visible: boolean;
  /** 备注 */
  remark: string;
  /** 创建人 */
  created_by: string;
  /** 编辑人 */
  updated_by: string;
  /** 创建时间 */
  created_at: number;
  /** 更新时间 */
  updated_at: number;
  /** 排序 */
  sort: number;
  /** 黑暗模式 icon */
  icon_dark: string;
  /** 申诉类型多语言阿语 */
  type_text_ar: string;
  /** 申诉类型多语言土耳其 */
  type_text_tr: string;
  /** 是否删除 1已删除 2未删除 */
  is_delete: number;
  /** 申诉类型多语言英语 */
  type_text_en: string;
}

export interface OperateCategoryReq {
  /** 操作类型 */
  operate: Operate;
  /** 分页 */
  pagination: Pagination | undefined;
  /** 分类详情 */
  category: Category | undefined;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface OperateCategoryRsp {
  /** 分类列表 */
  categorys: Category[];
  /** 分页 */
  pagination: Pagination | undefined;
}

export interface SearchComplaintParams {
  /** 申诉类型搜索 */
  key: string;
  /** 分类ID */
  complaint_category_id: number;
  /** 是否展示 0所有,1展示,2不展示 */
  is_visible: number;
  /** 是否获取所有有效的申诉类型 */
  is_all: boolean;
  /** 申诉类型ID */
  id: number;
  /** 是否删除 1已删除 2未删除 */
  is_delete: number;
  /** 权重排序 0不排序 1升序 2降序 */
  use_sort: number;
}

export interface OperateComplaintTypeReq {
  /** 操作类型 */
  operate: Operate;
  /** 分页 */
  pagination: Pagination | undefined;
  /** 申诉详情 */
  complaint_type: ComplaintType | undefined;
  /** 搜索参数 */
  params: SearchComplaintParams | undefined;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface OperateComplaintTypeRsp {
  /** 申诉详情 */
  detail: ComplaintType[];
  /** 分页 */
  pagination: Pagination | undefined;
}

export interface FAQ {
  /** 申诉类型id */
  complaint_type_id: number;
  /** 排序 */
  sort: number;
  /** 英文 */
  question: string;
  /** 阿语 */
  question_ar: string;
  /** 土耳其 */
  question_tr: string;
  /** 英文 */
  answer: string;
  /** 阿语 */
  answer_ar: string;
  /** 土耳其 */
  answer_tr: string;
  /** 英文图片 */
  answer_images: string[];
  /** 阿语图片 */
  answer_images_ar: string[];
  /** 土耳其图片 */
  answer_images_tr: string[];
  /** 备注 */
  remark: string;
  /** 创建人 */
  created_by: string;
  /** 编辑人 */
  updated_by: string;
  /** 创建时间 */
  created_at: number;
  /** 更新时间 */
  updated_at: number;
  /** 数据状态 1:正常 2:已删除 */
  state: number;
  /** 主键ID */
  id: number;
  /** 申诉类型详情 */
  complaint_type_info: ComplaintType | undefined;
  /** 包名 */
  pkgs: string[];
}

export interface SearchFAQParams {
  /** 申诉类型ID */
  complaint_type_id: number;
  /** 问题关键词 */
  question_key: string;
  /** 数据状态 0:所有 1:正常 2:已删除 */
  state: number;
  /** 包名 */
  pkgs: string[];
}

export interface OperateFAQReq {
  /** 操作类型 */
  operate: Operate;
  /** 分页 */
  pagination: Pagination | undefined;
  /** FAQ详情 */
  faq: FAQ | undefined;
  /** 查询参数 */
  params: SearchFAQParams | undefined;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface OperateFAQRsp {
  /** 分页 */
  pagination: Pagination | undefined;
  /** FAQ详情列表 */
  faq: FAQ[];
}

/** 支付相关 */
export interface PayType {
  /** 支付方式 */
  method: string;
  /** 支付时间 秒 */
  time: number;
  /** 订单号 */
  number: string;
  /** 支付截图 */
  screenshots: string[];
  /** 平台订单号 */
  platform_order_id: string;
}

/** 普通相关 */
export interface CommonType {
  /** 问题描述 */
  detail: string;
  /** 问题时间 秒 */
  time: number;
  /** 系统提示文案 */
  system_prompt: string;
  /** 图片上传 */
  picture: string[];
}

/** 提现相关 */
export interface WithdrawType {
  /** 问题描述 */
  detail: string;
  /** 问题时间 秒 */
  time: number;
  /** 订单号 */
  number: string;
  /** pdf格式 */
  statement: string[];
}

export interface Complaint {
  /** 工单号 */
  id: number;
  /** 用户ID */
  uid: number;
  /** 申诉类型id */
  complaint_type_id: number;
  /** 分类ID */
  complaint_category_id: number;
  /** 工单状态 */
  state: State;
  /** 支付相关信息 */
  pay_info: PayType | undefined;
  /** 常规相关的信息 */
  common_info: CommonType | undefined;
  /** 提现相关的信息 */
  withdraw_info: WithdrawType | undefined;
  /** pkg包名 */
  pkg: string;
  /** 提交时间 */
  submit_time: number;
  /** 处理时间 */
  operator_time: number;
  /** 处理人 */
  operator: string;
  /** 用户信息 */
  user_info: UserInfo | undefined;
  /** 1:有效 2:无效 */
  validity: number;
  /** 申诉次数 */
  complaint_num: number;
  /** 申诉类型详情 */
  complaint_type_info: ComplaintType | undefined;
  /** 用户角色 */
  user_role: UserRole;
  /** 回复状态 */
  reply_status: boolean;
  /** 跟踪状态 */
  tracking_status: TrackingStatus;
  /** 回复内容 */
  replies: Reply[];
}

export interface SearchRecordParams {
  /** 用户UID */
  uid: number;
  /** pkg包名 */
  pkg: string;
  /** 申诉类型ID */
  complaint_type_id: number;
  /** 分类ID */
  category_id: number;
  /** 状态数组,审核记录{1,3} 审核列表{2,4} */
  states: State[];
  /** 1:有效 2:无效 */
  validity: number;
  /** 提交开始时间 */
  submit_start_time: number;
  /** 提交结束时间 */
  submit_end_time: number;
  /** 国家 */
  country: string;
  /** 问题描述 */
  issue_desc: string;
  /** 用户角色 */
  user_role: UserRole;
  /** 回复状态,为空时则都展示。查询已回复，填[true] */
  reply_status: boolean[];
  /** 跟踪状态 */
  tracking_status: TrackingStatus;
  /** 财富等级 */
  wealth_section: WealthSection;
  /** 申诉类型ID */
  complaint_type_ids: number[];
}

export interface ListComplaintsReq {
  /** 分页 */
  pagination: Pagination | undefined;
  /** 查询参数 */
  params: SearchRecordParams | undefined;
  /** 是否需要用户信息 */
  need_user: boolean;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface ListComplaintsRsp {
  /** 记录列表 */
  complaints: Complaint[];
  /** 分页 */
  pagination: Pagination | undefined;
}

export interface UpdateCategoryReq {
  /** 工单号 */
  complaint_id: number[];
  /** 分类ID */
  complaint_category_id: number;
  /** 申诉类型ID */
  complaint_type_id: number;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface UpdateCategoryRsp {}

export interface AddReplyReq {
  /** 工单号 */
  complaint_id: number[];
  /** 回复内容 */
  content: string;
  /** 回复图片 */
  images: string[];
  /** 创建人 */
  created_by: string;
  /** 编辑人 */
  updated_by: string;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface AddReplyRsp {}

export interface BatchUserInfosReq {
  /** 用户uid */
  uids: number[];
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface UserInfo {
  /** 用户ID */
  uid: number;
  /** 用户昵称 */
  nickname: string;
  /** 性别 1:男2:女3:无性别 */
  gender: number;
  /** 头像 */
  avatar: string;
  /** 靓号 */
  show_uid: string;
  /** 年龄 */
  age: number;
  /** 生日 */
  birthday: string;
  /** 国家 */
  country: string;
  /** 财富等级 */
  wealth_level: number;
  /** 注册时间 */
  register_time: number;
  /** 最后登录时间 */
  last_login_time: number;
  /** 当前版本号 */
  version: string;
  /** 包名 */
  pkg: string;
  /** 用户角色 */
  user_role: UserRole;
}

export interface BatchUserInfosRsp {
  /** 用户详情列表 */
  list: UserInfo[];
}

export interface SearchKeywordParams {
  /** id */
  id: number;
  /** 关键词 */
  keyword: string;
  /** 语种 */
  lan: string;
  /** 数据状态 1:正常 2:已删除 */
  state: number;
}

export interface Keyword {
  /** ID */
  id: number;
  /** 关键词 */
  word: string;
  /** 语种 */
  lan: string;
  /** 创建人 */
  created_by: string;
  /** 编辑人 */
  updated_by: string;
  /** 创建时间 */
  created_at: number;
  /** 更新时间 */
  updated_at: number;
  /** 数据状态 1:正常 2:已删除 */
  state: number;
}

export interface OperateKeywordReq {
  /** 操作类型 */
  operate: Operate;
  /** 分页 */
  pagination: Pagination | undefined;
  /** 详情 */
  keyword: Keyword | undefined;
  /** 搜索参数 */
  params: SearchKeywordParams | undefined;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface OperateKeywordRsp {
  /** 详情 */
  detail: Keyword[];
  /** 分页 */
  pagination: Pagination | undefined;
}

export interface Lan {
  /** 语种名称(展示用) */
  text: string;
  /** 语种简称(新增,查询传递服务端) */
  name: string;
}

export interface ListKeywordLanReq {
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface ListKeywordLanRsp {
  /** 语种列表 */
  lans: Lan[];
}

export interface ListCountryReq {
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface ListCountryRsp {
  /** 国家列表 */
  countries: string[];
}

export interface TrackingReq {
  /** 工单号 */
  complaint_id: number[];
  /** 是否tracking */
  tracking: boolean;
  /** 操作人 */
  operator: string;
  /** 针对同域名不同anm的情况下需要的参数（通常情况下不需要） */
  anm: string;
}

export interface TrackingRsp {}

function createBaseSearchComplaintTypeReq(): SearchComplaintTypeReq {
  return { complaint_category_id: 0, anm: '' };
}

export const SearchComplaintTypeReq: MessageFns<SearchComplaintTypeReq> = {
  fromJSON(object: any): SearchComplaintTypeReq {
    return {
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<SearchComplaintTypeReq>, I>>(base?: I): SearchComplaintTypeReq {
    return SearchComplaintTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchComplaintTypeReq>, I>>(object: I): SearchComplaintTypeReq {
    const message = createBaseSearchComplaintTypeReq();
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseSearchComplaintTypeRsp(): SearchComplaintTypeRsp {
  return { complaint_types: [] };
}

export const SearchComplaintTypeRsp: MessageFns<SearchComplaintTypeRsp> = {
  fromJSON(object: any): SearchComplaintTypeRsp {
    return {
      complaint_types: globalThis.Array.isArray(object?.complaint_types)
        ? object.complaint_types.map((e: any) => ComplaintType.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchComplaintTypeRsp>, I>>(base?: I): SearchComplaintTypeRsp {
    return SearchComplaintTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchComplaintTypeRsp>, I>>(object: I): SearchComplaintTypeRsp {
    const message = createBaseSearchComplaintTypeRsp();
    message.complaint_types = object.complaint_types?.map(e => ComplaintType.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListPkgReq(): ListPkgReq {
  return { states: [], anm: '' };
}

export const ListPkgReq: MessageFns<ListPkgReq> = {
  fromJSON(object: any): ListPkgReq {
    return {
      states: globalThis.Array.isArray(object?.states) ? object.states.map((e: any) => stateFromJSON(e)) : [],
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListPkgReq>, I>>(base?: I): ListPkgReq {
    return ListPkgReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPkgReq>, I>>(object: I): ListPkgReq {
    const message = createBaseListPkgReq();
    message.states = object.states?.map(e => e) || [];
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseListPkgRsp(): ListPkgRsp {
  return { name: [] };
}

export const ListPkgRsp: MessageFns<ListPkgRsp> = {
  fromJSON(object: any): ListPkgRsp {
    return { name: globalThis.Array.isArray(object?.name) ? object.name.map((e: any) => globalThis.String(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListPkgRsp>, I>>(base?: I): ListPkgRsp {
    return ListPkgRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPkgRsp>, I>>(object: I): ListPkgRsp {
    const message = createBaseListPkgRsp();
    message.name = object.name?.map(e => e) || [];
    return message;
  }
};

function createBaseUpdateStateReq(): UpdateStateReq {
  return { id: [], state: 0, operator: '', anm: '' };
}

export const UpdateStateReq: MessageFns<UpdateStateReq> = {
  fromJSON(object: any): UpdateStateReq {
    return {
      id: globalThis.Array.isArray(object?.id) ? object.id.map((e: any) => globalThis.Number(e)) : [],
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateStateReq>, I>>(base?: I): UpdateStateReq {
    return UpdateStateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStateReq>, I>>(object: I): UpdateStateReq {
    const message = createBaseUpdateStateReq();
    message.id = object.id?.map(e => e) || [];
    message.state = object.state ?? 0;
    message.operator = object.operator ?? '';
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseUpdateStateRsp(): UpdateStateRsp {
  return {};
}

export const UpdateStateRsp: MessageFns<UpdateStateRsp> = {
  fromJSON(_: any): UpdateStateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateStateRsp>, I>>(base?: I): UpdateStateRsp {
    return UpdateStateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStateRsp>, I>>(_: I): UpdateStateRsp {
    const message = createBaseUpdateStateRsp();
    return message;
  }
};

function createBasePagination(): Pagination {
  return { page: 0, pageSize: 0, count: 0 };
}

export const Pagination: MessageFns<Pagination> = {
  fromJSON(object: any): Pagination {
    return {
      page: isSet(object.page) ? globalThis.Number(object.page) : 0,
      pageSize: isSet(object.pageSize) ? globalThis.Number(object.pageSize) : 0,
      count: isSet(object.count) ? globalThis.Number(object.count) : 0
    };
  },

  create<I extends Exact<DeepPartial<Pagination>, I>>(base?: I): Pagination {
    return Pagination.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Pagination>, I>>(object: I): Pagination {
    const message = createBasePagination();
    message.page = object.page ?? 0;
    message.pageSize = object.pageSize ?? 0;
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseCategory(): Category {
  return {
    id: 0,
    name: '',
    created_by: '',
    updated_by: '',
    created_at: 0,
    updated_at: 0,
    name_ar: '',
    name_tr: '',
    name_en: '',
    category_type: 0
  };
}

export const Category: MessageFns<Category> = {
  fromJSON(object: any): Category {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      name_ar: isSet(object.name_ar) ? globalThis.String(object.name_ar) : '',
      name_tr: isSet(object.name_tr) ? globalThis.String(object.name_tr) : '',
      name_en: isSet(object.name_en) ? globalThis.String(object.name_en) : '',
      category_type: isSet(object.category_type) ? categoryTypeFromJSON(object.category_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<Category>, I>>(base?: I): Category {
    return Category.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Category>, I>>(object: I): Category {
    const message = createBaseCategory();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.created_by = object.created_by ?? '';
    message.updated_by = object.updated_by ?? '';
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.name_ar = object.name_ar ?? '';
    message.name_tr = object.name_tr ?? '';
    message.name_en = object.name_en ?? '';
    message.category_type = object.category_type ?? 0;
    return message;
  }
};

function createBaseComplaintType(): ComplaintType {
  return {
    id: 0,
    complaint_category_id: 0,
    type_text: '',
    icon: '',
    is_visible: false,
    remark: '',
    created_by: '',
    updated_by: '',
    created_at: 0,
    updated_at: 0,
    sort: 0,
    icon_dark: '',
    type_text_ar: '',
    type_text_tr: '',
    is_delete: 0,
    type_text_en: ''
  };
}

export const ComplaintType: MessageFns<ComplaintType> = {
  fromJSON(object: any): ComplaintType {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      type_text: isSet(object.type_text) ? globalThis.String(object.type_text) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      is_visible: isSet(object.is_visible) ? globalThis.Boolean(object.is_visible) : false,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      icon_dark: isSet(object.icon_dark) ? globalThis.String(object.icon_dark) : '',
      type_text_ar: isSet(object.type_text_ar) ? globalThis.String(object.type_text_ar) : '',
      type_text_tr: isSet(object.type_text_tr) ? globalThis.String(object.type_text_tr) : '',
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0,
      type_text_en: isSet(object.type_text_en) ? globalThis.String(object.type_text_en) : ''
    };
  },

  create<I extends Exact<DeepPartial<ComplaintType>, I>>(base?: I): ComplaintType {
    return ComplaintType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ComplaintType>, I>>(object: I): ComplaintType {
    const message = createBaseComplaintType();
    message.id = object.id ?? 0;
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.type_text = object.type_text ?? '';
    message.icon = object.icon ?? '';
    message.is_visible = object.is_visible ?? false;
    message.remark = object.remark ?? '';
    message.created_by = object.created_by ?? '';
    message.updated_by = object.updated_by ?? '';
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.sort = object.sort ?? 0;
    message.icon_dark = object.icon_dark ?? '';
    message.type_text_ar = object.type_text_ar ?? '';
    message.type_text_tr = object.type_text_tr ?? '';
    message.is_delete = object.is_delete ?? 0;
    message.type_text_en = object.type_text_en ?? '';
    return message;
  }
};

function createBaseOperateCategoryReq(): OperateCategoryReq {
  return { operate: 0, pagination: undefined, category: undefined, anm: '' };
}

export const OperateCategoryReq: MessageFns<OperateCategoryReq> = {
  fromJSON(object: any): OperateCategoryReq {
    return {
      operate: isSet(object.operate) ? operateFromJSON(object.operate) : 0,
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      category: isSet(object.category) ? Category.fromJSON(object.category) : undefined,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperateCategoryReq>, I>>(base?: I): OperateCategoryReq {
    return OperateCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateCategoryReq>, I>>(object: I): OperateCategoryReq {
    const message = createBaseOperateCategoryReq();
    message.operate = object.operate ?? 0;
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.category =
      object.category !== undefined && object.category !== null ? Category.fromPartial(object.category) : undefined;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseOperateCategoryRsp(): OperateCategoryRsp {
  return { categorys: [], pagination: undefined };
}

export const OperateCategoryRsp: MessageFns<OperateCategoryRsp> = {
  fromJSON(object: any): OperateCategoryRsp {
    return {
      categorys: globalThis.Array.isArray(object?.categorys)
        ? object.categorys.map((e: any) => Category.fromJSON(e))
        : [],
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined
    };
  },

  create<I extends Exact<DeepPartial<OperateCategoryRsp>, I>>(base?: I): OperateCategoryRsp {
    return OperateCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateCategoryRsp>, I>>(object: I): OperateCategoryRsp {
    const message = createBaseOperateCategoryRsp();
    message.categorys = object.categorys?.map(e => Category.fromPartial(e)) || [];
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    return message;
  }
};

function createBaseSearchComplaintParams(): SearchComplaintParams {
  return { key: '', complaint_category_id: 0, is_visible: 0, is_all: false, id: 0, is_delete: 0, use_sort: 0 };
}

export const SearchComplaintParams: MessageFns<SearchComplaintParams> = {
  fromJSON(object: any): SearchComplaintParams {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      is_visible: isSet(object.is_visible) ? globalThis.Number(object.is_visible) : 0,
      is_all: isSet(object.is_all) ? globalThis.Boolean(object.is_all) : false,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0,
      use_sort: isSet(object.use_sort) ? globalThis.Number(object.use_sort) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchComplaintParams>, I>>(base?: I): SearchComplaintParams {
    return SearchComplaintParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchComplaintParams>, I>>(object: I): SearchComplaintParams {
    const message = createBaseSearchComplaintParams();
    message.key = object.key ?? '';
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.is_visible = object.is_visible ?? 0;
    message.is_all = object.is_all ?? false;
    message.id = object.id ?? 0;
    message.is_delete = object.is_delete ?? 0;
    message.use_sort = object.use_sort ?? 0;
    return message;
  }
};

function createBaseOperateComplaintTypeReq(): OperateComplaintTypeReq {
  return { operate: 0, pagination: undefined, complaint_type: undefined, params: undefined, anm: '' };
}

export const OperateComplaintTypeReq: MessageFns<OperateComplaintTypeReq> = {
  fromJSON(object: any): OperateComplaintTypeReq {
    return {
      operate: isSet(object.operate) ? operateFromJSON(object.operate) : 0,
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      complaint_type: isSet(object.complaint_type) ? ComplaintType.fromJSON(object.complaint_type) : undefined,
      params: isSet(object.params) ? SearchComplaintParams.fromJSON(object.params) : undefined,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperateComplaintTypeReq>, I>>(base?: I): OperateComplaintTypeReq {
    return OperateComplaintTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateComplaintTypeReq>, I>>(object: I): OperateComplaintTypeReq {
    const message = createBaseOperateComplaintTypeReq();
    message.operate = object.operate ?? 0;
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.complaint_type =
      object.complaint_type !== undefined && object.complaint_type !== null
        ? ComplaintType.fromPartial(object.complaint_type)
        : undefined;
    message.params =
      object.params !== undefined && object.params !== null
        ? SearchComplaintParams.fromPartial(object.params)
        : undefined;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseOperateComplaintTypeRsp(): OperateComplaintTypeRsp {
  return { detail: [], pagination: undefined };
}

export const OperateComplaintTypeRsp: MessageFns<OperateComplaintTypeRsp> = {
  fromJSON(object: any): OperateComplaintTypeRsp {
    return {
      detail: globalThis.Array.isArray(object?.detail) ? object.detail.map((e: any) => ComplaintType.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined
    };
  },

  create<I extends Exact<DeepPartial<OperateComplaintTypeRsp>, I>>(base?: I): OperateComplaintTypeRsp {
    return OperateComplaintTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateComplaintTypeRsp>, I>>(object: I): OperateComplaintTypeRsp {
    const message = createBaseOperateComplaintTypeRsp();
    message.detail = object.detail?.map(e => ComplaintType.fromPartial(e)) || [];
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    return message;
  }
};

function createBaseFAQ(): FAQ {
  return {
    complaint_type_id: 0,
    sort: 0,
    question: '',
    question_ar: '',
    question_tr: '',
    answer: '',
    answer_ar: '',
    answer_tr: '',
    answer_images: [],
    answer_images_ar: [],
    answer_images_tr: [],
    remark: '',
    created_by: '',
    updated_by: '',
    created_at: 0,
    updated_at: 0,
    state: 0,
    id: 0,
    complaint_type_info: undefined,
    pkgs: []
  };
}

export const FAQ: MessageFns<FAQ> = {
  fromJSON(object: any): FAQ {
    return {
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      question: isSet(object.question) ? globalThis.String(object.question) : '',
      question_ar: isSet(object.question_ar) ? globalThis.String(object.question_ar) : '',
      question_tr: isSet(object.question_tr) ? globalThis.String(object.question_tr) : '',
      answer: isSet(object.answer) ? globalThis.String(object.answer) : '',
      answer_ar: isSet(object.answer_ar) ? globalThis.String(object.answer_ar) : '',
      answer_tr: isSet(object.answer_tr) ? globalThis.String(object.answer_tr) : '',
      answer_images: globalThis.Array.isArray(object?.answer_images)
        ? object.answer_images.map((e: any) => globalThis.String(e))
        : [],
      answer_images_ar: globalThis.Array.isArray(object?.answer_images_ar)
        ? object.answer_images_ar.map((e: any) => globalThis.String(e))
        : [],
      answer_images_tr: globalThis.Array.isArray(object?.answer_images_tr)
        ? object.answer_images_tr.map((e: any) => globalThis.String(e))
        : [],
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      state: isSet(object.state) ? globalThis.Number(object.state) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      complaint_type_info: isSet(object.complaint_type_info)
        ? ComplaintType.fromJSON(object.complaint_type_info)
        : undefined,
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<FAQ>, I>>(base?: I): FAQ {
    return FAQ.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FAQ>, I>>(object: I): FAQ {
    const message = createBaseFAQ();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.sort = object.sort ?? 0;
    message.question = object.question ?? '';
    message.question_ar = object.question_ar ?? '';
    message.question_tr = object.question_tr ?? '';
    message.answer = object.answer ?? '';
    message.answer_ar = object.answer_ar ?? '';
    message.answer_tr = object.answer_tr ?? '';
    message.answer_images = object.answer_images?.map(e => e) || [];
    message.answer_images_ar = object.answer_images_ar?.map(e => e) || [];
    message.answer_images_tr = object.answer_images_tr?.map(e => e) || [];
    message.remark = object.remark ?? '';
    message.created_by = object.created_by ?? '';
    message.updated_by = object.updated_by ?? '';
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.state = object.state ?? 0;
    message.id = object.id ?? 0;
    message.complaint_type_info =
      object.complaint_type_info !== undefined && object.complaint_type_info !== null
        ? ComplaintType.fromPartial(object.complaint_type_info)
        : undefined;
    message.pkgs = object.pkgs?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchFAQParams(): SearchFAQParams {
  return { complaint_type_id: 0, question_key: '', state: 0, pkgs: [] };
}

export const SearchFAQParams: MessageFns<SearchFAQParams> = {
  fromJSON(object: any): SearchFAQParams {
    return {
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      question_key: isSet(object.question_key) ? globalThis.String(object.question_key) : '',
      state: isSet(object.state) ? globalThis.Number(object.state) : 0,
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchFAQParams>, I>>(base?: I): SearchFAQParams {
    return SearchFAQParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchFAQParams>, I>>(object: I): SearchFAQParams {
    const message = createBaseSearchFAQParams();
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.question_key = object.question_key ?? '';
    message.state = object.state ?? 0;
    message.pkgs = object.pkgs?.map(e => e) || [];
    return message;
  }
};

function createBaseOperateFAQReq(): OperateFAQReq {
  return { operate: 0, pagination: undefined, faq: undefined, params: undefined, anm: '' };
}

export const OperateFAQReq: MessageFns<OperateFAQReq> = {
  fromJSON(object: any): OperateFAQReq {
    return {
      operate: isSet(object.operate) ? operateFromJSON(object.operate) : 0,
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      faq: isSet(object.faq) ? FAQ.fromJSON(object.faq) : undefined,
      params: isSet(object.params) ? SearchFAQParams.fromJSON(object.params) : undefined,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperateFAQReq>, I>>(base?: I): OperateFAQReq {
    return OperateFAQReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateFAQReq>, I>>(object: I): OperateFAQReq {
    const message = createBaseOperateFAQReq();
    message.operate = object.operate ?? 0;
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.faq = object.faq !== undefined && object.faq !== null ? FAQ.fromPartial(object.faq) : undefined;
    message.params =
      object.params !== undefined && object.params !== null ? SearchFAQParams.fromPartial(object.params) : undefined;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseOperateFAQRsp(): OperateFAQRsp {
  return { pagination: undefined, faq: [] };
}

export const OperateFAQRsp: MessageFns<OperateFAQRsp> = {
  fromJSON(object: any): OperateFAQRsp {
    return {
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      faq: globalThis.Array.isArray(object?.faq) ? object.faq.map((e: any) => FAQ.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<OperateFAQRsp>, I>>(base?: I): OperateFAQRsp {
    return OperateFAQRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateFAQRsp>, I>>(object: I): OperateFAQRsp {
    const message = createBaseOperateFAQRsp();
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.faq = object.faq?.map(e => FAQ.fromPartial(e)) || [];
    return message;
  }
};

function createBasePayType(): PayType {
  return { method: '', time: 0, number: '', screenshots: [], platform_order_id: '' };
}

export const PayType: MessageFns<PayType> = {
  fromJSON(object: any): PayType {
    return {
      method: isSet(object.method) ? globalThis.String(object.method) : '',
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      number: isSet(object.number) ? globalThis.String(object.number) : '',
      screenshots: globalThis.Array.isArray(object?.screenshots)
        ? object.screenshots.map((e: any) => globalThis.String(e))
        : [],
      platform_order_id: isSet(object.platform_order_id) ? globalThis.String(object.platform_order_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PayType>, I>>(base?: I): PayType {
    return PayType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayType>, I>>(object: I): PayType {
    const message = createBasePayType();
    message.method = object.method ?? '';
    message.time = object.time ?? 0;
    message.number = object.number ?? '';
    message.screenshots = object.screenshots?.map(e => e) || [];
    message.platform_order_id = object.platform_order_id ?? '';
    return message;
  }
};

function createBaseCommonType(): CommonType {
  return { detail: '', time: 0, system_prompt: '', picture: [] };
}

export const CommonType: MessageFns<CommonType> = {
  fromJSON(object: any): CommonType {
    return {
      detail: isSet(object.detail) ? globalThis.String(object.detail) : '',
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      system_prompt: isSet(object.system_prompt) ? globalThis.String(object.system_prompt) : '',
      picture: globalThis.Array.isArray(object?.picture) ? object.picture.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<CommonType>, I>>(base?: I): CommonType {
    return CommonType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CommonType>, I>>(object: I): CommonType {
    const message = createBaseCommonType();
    message.detail = object.detail ?? '';
    message.time = object.time ?? 0;
    message.system_prompt = object.system_prompt ?? '';
    message.picture = object.picture?.map(e => e) || [];
    return message;
  }
};

function createBaseWithdrawType(): WithdrawType {
  return { detail: '', time: 0, number: '', statement: [] };
}

export const WithdrawType: MessageFns<WithdrawType> = {
  fromJSON(object: any): WithdrawType {
    return {
      detail: isSet(object.detail) ? globalThis.String(object.detail) : '',
      time: isSet(object.time) ? globalThis.Number(object.time) : 0,
      number: isSet(object.number) ? globalThis.String(object.number) : '',
      statement: globalThis.Array.isArray(object?.statement)
        ? object.statement.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<WithdrawType>, I>>(base?: I): WithdrawType {
    return WithdrawType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<WithdrawType>, I>>(object: I): WithdrawType {
    const message = createBaseWithdrawType();
    message.detail = object.detail ?? '';
    message.time = object.time ?? 0;
    message.number = object.number ?? '';
    message.statement = object.statement?.map(e => e) || [];
    return message;
  }
};

function createBaseComplaint(): Complaint {
  return {
    id: 0,
    uid: 0,
    complaint_type_id: 0,
    complaint_category_id: 0,
    state: 0,
    pay_info: undefined,
    common_info: undefined,
    withdraw_info: undefined,
    pkg: '',
    submit_time: 0,
    operator_time: 0,
    operator: '',
    user_info: undefined,
    validity: 0,
    complaint_num: 0,
    complaint_type_info: undefined,
    user_role: 0,
    reply_status: false,
    tracking_status: 0,
    replies: []
  };
}

export const Complaint: MessageFns<Complaint> = {
  fromJSON(object: any): Complaint {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      state: isSet(object.state) ? stateFromJSON(object.state) : 0,
      pay_info: isSet(object.pay_info) ? PayType.fromJSON(object.pay_info) : undefined,
      common_info: isSet(object.common_info) ? CommonType.fromJSON(object.common_info) : undefined,
      withdraw_info: isSet(object.withdraw_info) ? WithdrawType.fromJSON(object.withdraw_info) : undefined,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      submit_time: isSet(object.submit_time) ? globalThis.Number(object.submit_time) : 0,
      operator_time: isSet(object.operator_time) ? globalThis.Number(object.operator_time) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      validity: isSet(object.validity) ? globalThis.Number(object.validity) : 0,
      complaint_num: isSet(object.complaint_num) ? globalThis.Number(object.complaint_num) : 0,
      complaint_type_info: isSet(object.complaint_type_info)
        ? ComplaintType.fromJSON(object.complaint_type_info)
        : undefined,
      user_role: isSet(object.user_role) ? userRoleFromJSON(object.user_role) : 0,
      reply_status: isSet(object.reply_status) ? globalThis.Boolean(object.reply_status) : false,
      tracking_status: isSet(object.tracking_status) ? trackingStatusFromJSON(object.tracking_status) : 0,
      replies: globalThis.Array.isArray(object?.replies) ? object.replies.map((e: any) => Reply.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Complaint>, I>>(base?: I): Complaint {
    return Complaint.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Complaint>, I>>(object: I): Complaint {
    const message = createBaseComplaint();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.state = object.state ?? 0;
    message.pay_info =
      object.pay_info !== undefined && object.pay_info !== null ? PayType.fromPartial(object.pay_info) : undefined;
    message.common_info =
      object.common_info !== undefined && object.common_info !== null
        ? CommonType.fromPartial(object.common_info)
        : undefined;
    message.withdraw_info =
      object.withdraw_info !== undefined && object.withdraw_info !== null
        ? WithdrawType.fromPartial(object.withdraw_info)
        : undefined;
    message.pkg = object.pkg ?? '';
    message.submit_time = object.submit_time ?? 0;
    message.operator_time = object.operator_time ?? 0;
    message.operator = object.operator ?? '';
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.validity = object.validity ?? 0;
    message.complaint_num = object.complaint_num ?? 0;
    message.complaint_type_info =
      object.complaint_type_info !== undefined && object.complaint_type_info !== null
        ? ComplaintType.fromPartial(object.complaint_type_info)
        : undefined;
    message.user_role = object.user_role ?? 0;
    message.reply_status = object.reply_status ?? false;
    message.tracking_status = object.tracking_status ?? 0;
    message.replies = object.replies?.map(e => Reply.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSearchRecordParams(): SearchRecordParams {
  return {
    uid: 0,
    pkg: '',
    complaint_type_id: 0,
    category_id: 0,
    states: [],
    validity: 0,
    submit_start_time: 0,
    submit_end_time: 0,
    country: '',
    issue_desc: '',
    user_role: 0,
    reply_status: [],
    tracking_status: 0,
    wealth_section: 0,
    complaint_type_ids: []
  };
}

export const SearchRecordParams: MessageFns<SearchRecordParams> = {
  fromJSON(object: any): SearchRecordParams {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      category_id: isSet(object.category_id) ? globalThis.Number(object.category_id) : 0,
      states: globalThis.Array.isArray(object?.states) ? object.states.map((e: any) => stateFromJSON(e)) : [],
      validity: isSet(object.validity) ? globalThis.Number(object.validity) : 0,
      submit_start_time: isSet(object.submit_start_time) ? globalThis.Number(object.submit_start_time) : 0,
      submit_end_time: isSet(object.submit_end_time) ? globalThis.Number(object.submit_end_time) : 0,
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      issue_desc: isSet(object.issue_desc) ? globalThis.String(object.issue_desc) : '',
      user_role: isSet(object.user_role) ? userRoleFromJSON(object.user_role) : 0,
      reply_status: globalThis.Array.isArray(object?.reply_status)
        ? object.reply_status.map((e: any) => globalThis.Boolean(e))
        : [],
      tracking_status: isSet(object.tracking_status) ? trackingStatusFromJSON(object.tracking_status) : 0,
      wealth_section: isSet(object.wealth_section) ? wealthSectionFromJSON(object.wealth_section) : 0,
      complaint_type_ids: globalThis.Array.isArray(object?.complaint_type_ids)
        ? object.complaint_type_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRecordParams>, I>>(base?: I): SearchRecordParams {
    return SearchRecordParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRecordParams>, I>>(object: I): SearchRecordParams {
    const message = createBaseSearchRecordParams();
    message.uid = object.uid ?? 0;
    message.pkg = object.pkg ?? '';
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.category_id = object.category_id ?? 0;
    message.states = object.states?.map(e => e) || [];
    message.validity = object.validity ?? 0;
    message.submit_start_time = object.submit_start_time ?? 0;
    message.submit_end_time = object.submit_end_time ?? 0;
    message.country = object.country ?? '';
    message.issue_desc = object.issue_desc ?? '';
    message.user_role = object.user_role ?? 0;
    message.reply_status = object.reply_status?.map(e => e) || [];
    message.tracking_status = object.tracking_status ?? 0;
    message.wealth_section = object.wealth_section ?? 0;
    message.complaint_type_ids = object.complaint_type_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListComplaintsReq(): ListComplaintsReq {
  return { pagination: undefined, params: undefined, need_user: false, anm: '' };
}

export const ListComplaintsReq: MessageFns<ListComplaintsReq> = {
  fromJSON(object: any): ListComplaintsReq {
    return {
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      params: isSet(object.params) ? SearchRecordParams.fromJSON(object.params) : undefined,
      need_user: isSet(object.need_user) ? globalThis.Boolean(object.need_user) : false,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListComplaintsReq>, I>>(base?: I): ListComplaintsReq {
    return ListComplaintsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListComplaintsReq>, I>>(object: I): ListComplaintsReq {
    const message = createBaseListComplaintsReq();
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.params =
      object.params !== undefined && object.params !== null ? SearchRecordParams.fromPartial(object.params) : undefined;
    message.need_user = object.need_user ?? false;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseListComplaintsRsp(): ListComplaintsRsp {
  return { complaints: [], pagination: undefined };
}

export const ListComplaintsRsp: MessageFns<ListComplaintsRsp> = {
  fromJSON(object: any): ListComplaintsRsp {
    return {
      complaints: globalThis.Array.isArray(object?.complaints)
        ? object.complaints.map((e: any) => Complaint.fromJSON(e))
        : [],
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListComplaintsRsp>, I>>(base?: I): ListComplaintsRsp {
    return ListComplaintsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListComplaintsRsp>, I>>(object: I): ListComplaintsRsp {
    const message = createBaseListComplaintsRsp();
    message.complaints = object.complaints?.map(e => Complaint.fromPartial(e)) || [];
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    return message;
  }
};

function createBaseUpdateCategoryReq(): UpdateCategoryReq {
  return { complaint_id: [], complaint_category_id: 0, complaint_type_id: 0, anm: '' };
}

export const UpdateCategoryReq: MessageFns<UpdateCategoryReq> = {
  fromJSON(object: any): UpdateCategoryReq {
    return {
      complaint_id: globalThis.Array.isArray(object?.complaint_id)
        ? object.complaint_id.map((e: any) => globalThis.Number(e))
        : [],
      complaint_category_id: isSet(object.complaint_category_id) ? globalThis.Number(object.complaint_category_id) : 0,
      complaint_type_id: isSet(object.complaint_type_id) ? globalThis.Number(object.complaint_type_id) : 0,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateCategoryReq>, I>>(base?: I): UpdateCategoryReq {
    return UpdateCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateCategoryReq>, I>>(object: I): UpdateCategoryReq {
    const message = createBaseUpdateCategoryReq();
    message.complaint_id = object.complaint_id?.map(e => e) || [];
    message.complaint_category_id = object.complaint_category_id ?? 0;
    message.complaint_type_id = object.complaint_type_id ?? 0;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseUpdateCategoryRsp(): UpdateCategoryRsp {
  return {};
}

export const UpdateCategoryRsp: MessageFns<UpdateCategoryRsp> = {
  fromJSON(_: any): UpdateCategoryRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateCategoryRsp>, I>>(base?: I): UpdateCategoryRsp {
    return UpdateCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateCategoryRsp>, I>>(_: I): UpdateCategoryRsp {
    const message = createBaseUpdateCategoryRsp();
    return message;
  }
};

function createBaseAddReplyReq(): AddReplyReq {
  return { complaint_id: [], content: '', images: [], created_by: '', updated_by: '', anm: '' };
}

export const AddReplyReq: MessageFns<AddReplyReq> = {
  fromJSON(object: any): AddReplyReq {
    return {
      complaint_id: globalThis.Array.isArray(object?.complaint_id)
        ? object.complaint_id.map((e: any) => globalThis.Number(e))
        : [],
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => globalThis.String(e)) : [],
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddReplyReq>, I>>(base?: I): AddReplyReq {
    return AddReplyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReplyReq>, I>>(object: I): AddReplyReq {
    const message = createBaseAddReplyReq();
    message.complaint_id = object.complaint_id?.map(e => e) || [];
    message.content = object.content ?? '';
    message.images = object.images?.map(e => e) || [];
    message.created_by = object.created_by ?? '';
    message.updated_by = object.updated_by ?? '';
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseAddReplyRsp(): AddReplyRsp {
  return {};
}

export const AddReplyRsp: MessageFns<AddReplyRsp> = {
  fromJSON(_: any): AddReplyRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddReplyRsp>, I>>(base?: I): AddReplyRsp {
    return AddReplyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReplyRsp>, I>>(_: I): AddReplyRsp {
    const message = createBaseAddReplyRsp();
    return message;
  }
};

function createBaseBatchUserInfosReq(): BatchUserInfosReq {
  return { uids: [], anm: '' };
}

export const BatchUserInfosReq: MessageFns<BatchUserInfosReq> = {
  fromJSON(object: any): BatchUserInfosReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<BatchUserInfosReq>, I>>(base?: I): BatchUserInfosReq {
    return BatchUserInfosReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchUserInfosReq>, I>>(object: I): BatchUserInfosReq {
    const message = createBaseBatchUserInfosReq();
    message.uids = object.uids?.map(e => e) || [];
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return {
    uid: 0,
    nickname: '',
    gender: 0,
    avatar: '',
    show_uid: '',
    age: 0,
    birthday: '',
    country: '',
    wealth_level: 0,
    register_time: 0,
    last_login_time: 0,
    version: '',
    pkg: '',
    user_role: 0
  };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      gender: isSet(object.gender) ? globalThis.Number(object.gender) : 0,
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      age: isSet(object.age) ? globalThis.Number(object.age) : 0,
      birthday: isSet(object.birthday) ? globalThis.String(object.birthday) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      wealth_level: isSet(object.wealth_level) ? globalThis.Number(object.wealth_level) : 0,
      register_time: isSet(object.register_time) ? globalThis.Number(object.register_time) : 0,
      last_login_time: isSet(object.last_login_time) ? globalThis.Number(object.last_login_time) : 0,
      version: isSet(object.version) ? globalThis.String(object.version) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      user_role: isSet(object.user_role) ? userRoleFromJSON(object.user_role) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.nickname = object.nickname ?? '';
    message.gender = object.gender ?? 0;
    message.avatar = object.avatar ?? '';
    message.show_uid = object.show_uid ?? '';
    message.age = object.age ?? 0;
    message.birthday = object.birthday ?? '';
    message.country = object.country ?? '';
    message.wealth_level = object.wealth_level ?? 0;
    message.register_time = object.register_time ?? 0;
    message.last_login_time = object.last_login_time ?? 0;
    message.version = object.version ?? '';
    message.pkg = object.pkg ?? '';
    message.user_role = object.user_role ?? 0;
    return message;
  }
};

function createBaseBatchUserInfosRsp(): BatchUserInfosRsp {
  return { list: [] };
}

export const BatchUserInfosRsp: MessageFns<BatchUserInfosRsp> = {
  fromJSON(object: any): BatchUserInfosRsp {
    return { list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => UserInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchUserInfosRsp>, I>>(base?: I): BatchUserInfosRsp {
    return BatchUserInfosRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchUserInfosRsp>, I>>(object: I): BatchUserInfosRsp {
    const message = createBaseBatchUserInfosRsp();
    message.list = object.list?.map(e => UserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSearchKeywordParams(): SearchKeywordParams {
  return { id: 0, keyword: '', lan: '', state: 0 };
}

export const SearchKeywordParams: MessageFns<SearchKeywordParams> = {
  fromJSON(object: any): SearchKeywordParams {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      state: isSet(object.state) ? globalThis.Number(object.state) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchKeywordParams>, I>>(base?: I): SearchKeywordParams {
    return SearchKeywordParams.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchKeywordParams>, I>>(object: I): SearchKeywordParams {
    const message = createBaseSearchKeywordParams();
    message.id = object.id ?? 0;
    message.keyword = object.keyword ?? '';
    message.lan = object.lan ?? '';
    message.state = object.state ?? 0;
    return message;
  }
};

function createBaseKeyword(): Keyword {
  return { id: 0, word: '', lan: '', created_by: '', updated_by: '', created_at: 0, updated_at: 0, state: 0 };
}

export const Keyword: MessageFns<Keyword> = {
  fromJSON(object: any): Keyword {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      word: isSet(object.word) ? globalThis.String(object.word) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      state: isSet(object.state) ? globalThis.Number(object.state) : 0
    };
  },

  create<I extends Exact<DeepPartial<Keyword>, I>>(base?: I): Keyword {
    return Keyword.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Keyword>, I>>(object: I): Keyword {
    const message = createBaseKeyword();
    message.id = object.id ?? 0;
    message.word = object.word ?? '';
    message.lan = object.lan ?? '';
    message.created_by = object.created_by ?? '';
    message.updated_by = object.updated_by ?? '';
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.state = object.state ?? 0;
    return message;
  }
};

function createBaseOperateKeywordReq(): OperateKeywordReq {
  return { operate: 0, pagination: undefined, keyword: undefined, params: undefined, anm: '' };
}

export const OperateKeywordReq: MessageFns<OperateKeywordReq> = {
  fromJSON(object: any): OperateKeywordReq {
    return {
      operate: isSet(object.operate) ? operateFromJSON(object.operate) : 0,
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined,
      keyword: isSet(object.keyword) ? Keyword.fromJSON(object.keyword) : undefined,
      params: isSet(object.params) ? SearchKeywordParams.fromJSON(object.params) : undefined,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperateKeywordReq>, I>>(base?: I): OperateKeywordReq {
    return OperateKeywordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateKeywordReq>, I>>(object: I): OperateKeywordReq {
    const message = createBaseOperateKeywordReq();
    message.operate = object.operate ?? 0;
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    message.keyword =
      object.keyword !== undefined && object.keyword !== null ? Keyword.fromPartial(object.keyword) : undefined;
    message.params =
      object.params !== undefined && object.params !== null
        ? SearchKeywordParams.fromPartial(object.params)
        : undefined;
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseOperateKeywordRsp(): OperateKeywordRsp {
  return { detail: [], pagination: undefined };
}

export const OperateKeywordRsp: MessageFns<OperateKeywordRsp> = {
  fromJSON(object: any): OperateKeywordRsp {
    return {
      detail: globalThis.Array.isArray(object?.detail) ? object.detail.map((e: any) => Keyword.fromJSON(e)) : [],
      pagination: isSet(object.pagination) ? Pagination.fromJSON(object.pagination) : undefined
    };
  },

  create<I extends Exact<DeepPartial<OperateKeywordRsp>, I>>(base?: I): OperateKeywordRsp {
    return OperateKeywordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateKeywordRsp>, I>>(object: I): OperateKeywordRsp {
    const message = createBaseOperateKeywordRsp();
    message.detail = object.detail?.map(e => Keyword.fromPartial(e)) || [];
    message.pagination =
      object.pagination !== undefined && object.pagination !== null
        ? Pagination.fromPartial(object.pagination)
        : undefined;
    return message;
  }
};

function createBaseLan(): Lan {
  return { text: '', name: '' };
}

export const Lan: MessageFns<Lan> = {
  fromJSON(object: any): Lan {
    return {
      text: isSet(object.text) ? globalThis.String(object.text) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<Lan>, I>>(base?: I): Lan {
    return Lan.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Lan>, I>>(object: I): Lan {
    const message = createBaseLan();
    message.text = object.text ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListKeywordLanReq(): ListKeywordLanReq {
  return { anm: '' };
}

export const ListKeywordLanReq: MessageFns<ListKeywordLanReq> = {
  fromJSON(object: any): ListKeywordLanReq {
    return { anm: isSet(object.anm) ? globalThis.String(object.anm) : '' };
  },

  create<I extends Exact<DeepPartial<ListKeywordLanReq>, I>>(base?: I): ListKeywordLanReq {
    return ListKeywordLanReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListKeywordLanReq>, I>>(object: I): ListKeywordLanReq {
    const message = createBaseListKeywordLanReq();
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseListKeywordLanRsp(): ListKeywordLanRsp {
  return { lans: [] };
}

export const ListKeywordLanRsp: MessageFns<ListKeywordLanRsp> = {
  fromJSON(object: any): ListKeywordLanRsp {
    return { lans: globalThis.Array.isArray(object?.lans) ? object.lans.map((e: any) => Lan.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListKeywordLanRsp>, I>>(base?: I): ListKeywordLanRsp {
    return ListKeywordLanRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListKeywordLanRsp>, I>>(object: I): ListKeywordLanRsp {
    const message = createBaseListKeywordLanRsp();
    message.lans = object.lans?.map(e => Lan.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListCountryReq(): ListCountryReq {
  return { anm: '' };
}

export const ListCountryReq: MessageFns<ListCountryReq> = {
  fromJSON(object: any): ListCountryReq {
    return { anm: isSet(object.anm) ? globalThis.String(object.anm) : '' };
  },

  create<I extends Exact<DeepPartial<ListCountryReq>, I>>(base?: I): ListCountryReq {
    return ListCountryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCountryReq>, I>>(object: I): ListCountryReq {
    const message = createBaseListCountryReq();
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseListCountryRsp(): ListCountryRsp {
  return { countries: [] };
}

export const ListCountryRsp: MessageFns<ListCountryRsp> = {
  fromJSON(object: any): ListCountryRsp {
    return {
      countries: globalThis.Array.isArray(object?.countries)
        ? object.countries.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListCountryRsp>, I>>(base?: I): ListCountryRsp {
    return ListCountryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCountryRsp>, I>>(object: I): ListCountryRsp {
    const message = createBaseListCountryRsp();
    message.countries = object.countries?.map(e => e) || [];
    return message;
  }
};

function createBaseTrackingReq(): TrackingReq {
  return { complaint_id: [], tracking: false, operator: '', anm: '' };
}

export const TrackingReq: MessageFns<TrackingReq> = {
  fromJSON(object: any): TrackingReq {
    return {
      complaint_id: globalThis.Array.isArray(object?.complaint_id)
        ? object.complaint_id.map((e: any) => globalThis.Number(e))
        : [],
      tracking: isSet(object.tracking) ? globalThis.Boolean(object.tracking) : false,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      anm: isSet(object.anm) ? globalThis.String(object.anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<TrackingReq>, I>>(base?: I): TrackingReq {
    return TrackingReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TrackingReq>, I>>(object: I): TrackingReq {
    const message = createBaseTrackingReq();
    message.complaint_id = object.complaint_id?.map(e => e) || [];
    message.tracking = object.tracking ?? false;
    message.operator = object.operator ?? '';
    message.anm = object.anm ?? '';
    return message;
  }
};

function createBaseTrackingRsp(): TrackingRsp {
  return {};
}

export const TrackingRsp: MessageFns<TrackingRsp> = {
  fromJSON(_: any): TrackingRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<TrackingRsp>, I>>(base?: I): TrackingRsp {
    return TrackingRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TrackingRsp>, I>>(_: I): TrackingRsp {
    const message = createBaseTrackingRsp();
    return message;
  }
};

export type ComplaintMgrDefinition = typeof ComplaintMgrDefinition;
export const ComplaintMgrDefinition = {
  name: 'ComplaintMgr',
  fullName: 'comm.mgr.customer.ComplaintMgr',
  methods: {
    /** 操作申诉分类 */
    operateCategory: {
      name: 'OperateCategory',
      requestType: OperateCategoryReq,
      requestStream: false,
      responseType: OperateCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 操作申诉类型数据 */
    operateComplaintType: {
      name: 'OperateComplaintType',
      requestType: OperateComplaintTypeReq,
      requestStream: false,
      responseType: OperateComplaintTypeRsp,
      responseStream: false,
      options: {}
    },
    /** 操作FAQ数据 */
    operateFAQ: {
      name: 'OperateFAQ',
      requestType: OperateFAQReq,
      requestStream: false,
      responseType: OperateFAQRsp,
      responseStream: false,
      options: {}
    },
    /** 展示用户申诉列表 */
    listComplaints: {
      name: 'ListComplaints',
      requestType: ListComplaintsReq,
      requestStream: false,
      responseType: ListComplaintsRsp,
      responseStream: false,
      options: {}
    },
    /** 修改申诉分类,申诉类型 */
    updateCategory: {
      name: 'UpdateCategory',
      requestType: UpdateCategoryReq,
      requestStream: false,
      responseType: UpdateCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 更新回复用户申诉工单 */
    addReply: {
      name: 'AddReply',
      requestType: AddReplyReq,
      requestStream: false,
      responseType: AddReplyRsp,
      responseStream: false,
      options: {}
    },
    /** 查询申诉类型 */
    searchComplaintType: {
      name: 'SearchComplaintType',
      requestType: SearchComplaintTypeReq,
      requestStream: false,
      responseType: SearchComplaintTypeRsp,
      responseStream: false,
      options: {}
    },
    /** pkg */
    listPkg: {
      name: 'ListPkg',
      requestType: ListPkgReq,
      requestStream: false,
      responseType: ListPkgRsp,
      responseStream: false,
      options: {}
    },
    /** 批量更新申诉工单状态 */
    updateState: {
      name: 'UpdateState',
      requestType: UpdateStateReq,
      requestStream: false,
      responseType: UpdateStateRsp,
      responseStream: false,
      options: {}
    },
    /** 批量查询用户信息 */
    batchUserInfos: {
      name: 'BatchUserInfos',
      requestType: BatchUserInfosReq,
      requestStream: false,
      responseType: BatchUserInfosRsp,
      responseStream: false,
      options: {}
    },
    /** 操作关键词 */
    operateKeyword: {
      name: 'OperateKeyword',
      requestType: OperateKeywordReq,
      requestStream: false,
      responseType: OperateKeywordRsp,
      responseStream: false,
      options: {}
    },
    /** 操作关键词语种 */
    listKeywordLan: {
      name: 'ListKeywordLan',
      requestType: ListKeywordLanReq,
      requestStream: false,
      responseType: ListKeywordLanRsp,
      responseStream: false,
      options: {}
    },
    /** 国家列表 */
    listCountry: {
      name: 'ListCountry',
      requestType: ListCountryReq,
      requestStream: false,
      responseType: ListCountryRsp,
      responseStream: false,
      options: {}
    },
    /** 更新回复用户申诉工单 */
    tracking: {
      name: 'Tracking',
      requestType: TrackingReq,
      requestStream: false,
      responseType: TrackingRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
