// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/customer/user.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.customer';

export interface BatchCheckUserExistReq {
  /** 用户uid */
  uids: number[];
}

export interface BatchCheckUserExistRsp {
  /** 存在的ID列表 */
  existingUIDs: number[];
  /** 不存在的ID列表 */
  nonExistingUIDs: number[];
}

function createBaseBatchCheckUserExistReq(): BatchCheckUserExistReq {
  return { uids: [] };
}

export const BatchCheckUserExistReq: MessageFns<BatchCheckUserExistReq> = {
  fromJSON(object: any): BatchCheckUserExistReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchCheckUserExistReq>, I>>(base?: I): BatchCheckUserExistReq {
    return BatchCheckUserExistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCheckUserExistReq>, I>>(object: I): BatchCheckUserExistReq {
    const message = createBaseBatchCheckUserExistReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchCheckUserExistRsp(): BatchCheckUserExistRsp {
  return { existingUIDs: [], nonExistingUIDs: [] };
}

export const BatchCheckUserExistRsp: MessageFns<BatchCheckUserExistRsp> = {
  fromJSON(object: any): BatchCheckUserExistRsp {
    return {
      existingUIDs: globalThis.Array.isArray(object?.existingUIDs)
        ? object.existingUIDs.map((e: any) => globalThis.Number(e))
        : [],
      nonExistingUIDs: globalThis.Array.isArray(object?.nonExistingUIDs)
        ? object.nonExistingUIDs.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchCheckUserExistRsp>, I>>(base?: I): BatchCheckUserExistRsp {
    return BatchCheckUserExistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCheckUserExistRsp>, I>>(object: I): BatchCheckUserExistRsp {
    const message = createBaseBatchCheckUserExistRsp();
    message.existingUIDs = object.existingUIDs?.map(e => e) || [];
    message.nonExistingUIDs = object.nonExistingUIDs?.map(e => e) || [];
    return message;
  }
};

/** serviceName: social-customersvc */
export type UserMgrDefinition = typeof UserMgrDefinition;
export const UserMgrDefinition = {
  name: 'UserMgr',
  fullName: 'comm.mgr.customer.UserMgr',
  methods: {
    /** 批量检查用户是否存在 */
    batchCheckUserExist: {
      name: 'BatchCheckUserExist',
      requestType: BatchCheckUserExistReq,
      requestStream: false,
      responseType: BatchCheckUserExistRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
