// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/relation/num.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.relation';

/** smicro:spath=gitit.cc/social/components-service/social-relation/biz/relation/handlermgr/relationnum */

export interface UserRelationNum {
  /** 关注房间数 */
  room_follow: number;
  /** 关注用户数 */
  user_follow: number;
  /** 粉丝用户数 */
  user_fans: number;
  /** 好友用户数 */
  user_friend: number;
  /** 拉黑用户数 */
  black_user: number;
  /** 拉黑房间数 */
  black_room: number;
}

export interface RoomRelationNum {
  /** 房间粉丝数 */
  room_fans: number;
  /** 房间拉黑用户数 */
  room_black_user: number;
}

export interface GetUserRelationNumReq {
  user_ids: number[];
}

export interface GetUserRelationNumRsp {
  user_num_map: { [key: number]: UserRelationNum };
}

export interface GetUserRelationNumRsp_UserNumMapEntry {
  key: number;
  value: UserRelationNum | undefined;
}

export interface GetRoomRelationNumReq {
  room_ids: number[];
}

export interface GetRoomRelationNumRsp {
  room_num_map: { [key: number]: RoomRelationNum };
}

export interface GetRoomRelationNumRsp_RoomNumMapEntry {
  key: number;
  value: RoomRelationNum | undefined;
}

function createBaseUserRelationNum(): UserRelationNum {
  return { room_follow: 0, user_follow: 0, user_fans: 0, user_friend: 0, black_user: 0, black_room: 0 };
}

export const UserRelationNum: MessageFns<UserRelationNum> = {
  fromJSON(object: any): UserRelationNum {
    return {
      room_follow: isSet(object.room_follow) ? globalThis.Number(object.room_follow) : 0,
      user_follow: isSet(object.user_follow) ? globalThis.Number(object.user_follow) : 0,
      user_fans: isSet(object.user_fans) ? globalThis.Number(object.user_fans) : 0,
      user_friend: isSet(object.user_friend) ? globalThis.Number(object.user_friend) : 0,
      black_user: isSet(object.black_user) ? globalThis.Number(object.black_user) : 0,
      black_room: isSet(object.black_room) ? globalThis.Number(object.black_room) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserRelationNum>, I>>(base?: I): UserRelationNum {
    return UserRelationNum.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRelationNum>, I>>(object: I): UserRelationNum {
    const message = createBaseUserRelationNum();
    message.room_follow = object.room_follow ?? 0;
    message.user_follow = object.user_follow ?? 0;
    message.user_fans = object.user_fans ?? 0;
    message.user_friend = object.user_friend ?? 0;
    message.black_user = object.black_user ?? 0;
    message.black_room = object.black_room ?? 0;
    return message;
  }
};

function createBaseRoomRelationNum(): RoomRelationNum {
  return { room_fans: 0, room_black_user: 0 };
}

export const RoomRelationNum: MessageFns<RoomRelationNum> = {
  fromJSON(object: any): RoomRelationNum {
    return {
      room_fans: isSet(object.room_fans) ? globalThis.Number(object.room_fans) : 0,
      room_black_user: isSet(object.room_black_user) ? globalThis.Number(object.room_black_user) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomRelationNum>, I>>(base?: I): RoomRelationNum {
    return RoomRelationNum.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRelationNum>, I>>(object: I): RoomRelationNum {
    const message = createBaseRoomRelationNum();
    message.room_fans = object.room_fans ?? 0;
    message.room_black_user = object.room_black_user ?? 0;
    return message;
  }
};

function createBaseGetUserRelationNumReq(): GetUserRelationNumReq {
  return { user_ids: [] };
}

export const GetUserRelationNumReq: MessageFns<GetUserRelationNumReq> = {
  fromJSON(object: any): GetUserRelationNumReq {
    return {
      user_ids: globalThis.Array.isArray(object?.user_ids) ? object.user_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationNumReq>, I>>(base?: I): GetUserRelationNumReq {
    return GetUserRelationNumReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationNumReq>, I>>(object: I): GetUserRelationNumReq {
    const message = createBaseGetUserRelationNumReq();
    message.user_ids = object.user_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetUserRelationNumRsp(): GetUserRelationNumRsp {
  return { user_num_map: {} };
}

export const GetUserRelationNumRsp: MessageFns<GetUserRelationNumRsp> = {
  fromJSON(object: any): GetUserRelationNumRsp {
    return {
      user_num_map: isObject(object.user_num_map)
        ? Object.entries(object.user_num_map).reduce<{ [key: number]: UserRelationNum }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = UserRelationNum.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationNumRsp>, I>>(base?: I): GetUserRelationNumRsp {
    return GetUserRelationNumRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationNumRsp>, I>>(object: I): GetUserRelationNumRsp {
    const message = createBaseGetUserRelationNumRsp();
    message.user_num_map = Object.entries(object.user_num_map ?? {}).reduce<{ [key: number]: UserRelationNum }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = UserRelationNum.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGetUserRelationNumRsp_UserNumMapEntry(): GetUserRelationNumRsp_UserNumMapEntry {
  return { key: 0, value: undefined };
}

export const GetUserRelationNumRsp_UserNumMapEntry: MessageFns<GetUserRelationNumRsp_UserNumMapEntry> = {
  fromJSON(object: any): GetUserRelationNumRsp_UserNumMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? UserRelationNum.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetUserRelationNumRsp_UserNumMapEntry>, I>>(
    base?: I
  ): GetUserRelationNumRsp_UserNumMapEntry {
    return GetUserRelationNumRsp_UserNumMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserRelationNumRsp_UserNumMapEntry>, I>>(
    object: I
  ): GetUserRelationNumRsp_UserNumMapEntry {
    const message = createBaseGetUserRelationNumRsp_UserNumMapEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? UserRelationNum.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseGetRoomRelationNumReq(): GetRoomRelationNumReq {
  return { room_ids: [] };
}

export const GetRoomRelationNumReq: MessageFns<GetRoomRelationNumReq> = {
  fromJSON(object: any): GetRoomRelationNumReq {
    return {
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetRoomRelationNumReq>, I>>(base?: I): GetRoomRelationNumReq {
    return GetRoomRelationNumReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRelationNumReq>, I>>(object: I): GetRoomRelationNumReq {
    const message = createBaseGetRoomRelationNumReq();
    message.room_ids = object.room_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetRoomRelationNumRsp(): GetRoomRelationNumRsp {
  return { room_num_map: {} };
}

export const GetRoomRelationNumRsp: MessageFns<GetRoomRelationNumRsp> = {
  fromJSON(object: any): GetRoomRelationNumRsp {
    return {
      room_num_map: isObject(object.room_num_map)
        ? Object.entries(object.room_num_map).reduce<{ [key: number]: RoomRelationNum }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = RoomRelationNum.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GetRoomRelationNumRsp>, I>>(base?: I): GetRoomRelationNumRsp {
    return GetRoomRelationNumRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRelationNumRsp>, I>>(object: I): GetRoomRelationNumRsp {
    const message = createBaseGetRoomRelationNumRsp();
    message.room_num_map = Object.entries(object.room_num_map ?? {}).reduce<{ [key: number]: RoomRelationNum }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = RoomRelationNum.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGetRoomRelationNumRsp_RoomNumMapEntry(): GetRoomRelationNumRsp_RoomNumMapEntry {
  return { key: 0, value: undefined };
}

export const GetRoomRelationNumRsp_RoomNumMapEntry: MessageFns<GetRoomRelationNumRsp_RoomNumMapEntry> = {
  fromJSON(object: any): GetRoomRelationNumRsp_RoomNumMapEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? RoomRelationNum.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetRoomRelationNumRsp_RoomNumMapEntry>, I>>(
    base?: I
  ): GetRoomRelationNumRsp_RoomNumMapEntry {
    return GetRoomRelationNumRsp_RoomNumMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRelationNumRsp_RoomNumMapEntry>, I>>(
    object: I
  ): GetRoomRelationNumRsp_RoomNumMapEntry {
    const message = createBaseGetRoomRelationNumRsp_RoomNumMapEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? RoomRelationNum.fromPartial(object.value) : undefined;
    return message;
  }
};

/** 关系数量服务 */
export type RelationNumMgrDefinition = typeof RelationNumMgrDefinition;
export const RelationNumMgrDefinition = {
  name: 'RelationNumMgr',
  fullName: 'comm.mgr.relation.RelationNumMgr',
  methods: {
    /** 获取用户关系数量 */
    getUserRelationNum: {
      name: 'GetUserRelationNum',
      requestType: GetUserRelationNumReq,
      requestStream: false,
      responseType: GetUserRelationNumRsp,
      responseStream: false,
      options: {}
    },
    /** 获取房间关系数量 */
    getRoomRelationNum: {
      name: 'GetRoomRelationNum',
      requestType: GetRoomRelationNumReq,
      requestStream: false,
      responseType: GetRoomRelationNumRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
