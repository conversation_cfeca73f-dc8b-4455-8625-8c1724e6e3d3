// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/activity.proto

/* eslint-disable */
import { UserInfo } from '../../api/activity/common';
import { Page } from '../../api/common/common';
import { CustomFilters, CustomFilterType, customFilterTypeFromJSON } from './biz';
import {
  Activity,
  ActivityInfo,
  BatchUpdateRankReward,
  FamilyInfo,
  GuildInfo,
  LotteryConfig,
  LotteryInfo,
  LotteryRewardConfig,
  PKConfig,
  PKDetail,
  PKInfo,
  PrizePoolConfig,
  PrizePoolInfo,
  RankConfig,
  RankInfo,
  RewardCategory,
  RewardItem,
  RewardSubCategory,
  RoomIDType,
  roomIDTypeFromJSON,
  RoomRecommendConfig,
  RoomRecommendInfo,
  SavePresentationConfig,
  TaskConfig,
  TaskH5Config,
  TaskInfo,
  TaskRewardConfig,
  TaskSubConfig,
  UserRoomInfo
} from './common';

export const protobufPackage = 'comm.mgr.activity';

export enum BWListType {
  BW_LIST_TYPE_NONE = 0,
  /** BW_LIST_TYPE_BLACK - 黑名单 */
  BW_LIST_TYPE_BLACK = 1,
  /** BW_LIST_TYPE_WHITE - 白名单 */
  BW_LIST_TYPE_WHITE = 2,
  /** BW_LIST_TYPE_BLACK_AND_WHITE - 黑名单 + 白名单 */
  BW_LIST_TYPE_BLACK_AND_WHITE = 3,
  UNRECOGNIZED = -1
}

export function bWListTypeFromJSON(object: any): BWListType {
  switch (object) {
    case 0:
    case 'BW_LIST_TYPE_NONE':
      return BWListType.BW_LIST_TYPE_NONE;
    case 1:
    case 'BW_LIST_TYPE_BLACK':
      return BWListType.BW_LIST_TYPE_BLACK;
    case 2:
    case 'BW_LIST_TYPE_WHITE':
      return BWListType.BW_LIST_TYPE_WHITE;
    case 3:
    case 'BW_LIST_TYPE_BLACK_AND_WHITE':
      return BWListType.BW_LIST_TYPE_BLACK_AND_WHITE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BWListType.UNRECOGNIZED;
  }
}

export enum LabelCategory {
  LABEL_CATEGORY_NONE = 0,
  /** LABEL_CATEGORY_USER - 用户标签 */
  LABEL_CATEGORY_USER = 1,
  /** LABEL_CATEGORY_USER_REALTIME - 人群，实时的 */
  LABEL_CATEGORY_USER_REALTIME = 2,
  UNRECOGNIZED = -1
}

export function labelCategoryFromJSON(object: any): LabelCategory {
  switch (object) {
    case 0:
    case 'LABEL_CATEGORY_NONE':
      return LabelCategory.LABEL_CATEGORY_NONE;
    case 1:
    case 'LABEL_CATEGORY_USER':
      return LabelCategory.LABEL_CATEGORY_USER;
    case 2:
    case 'LABEL_CATEGORY_USER_REALTIME':
      return LabelCategory.LABEL_CATEGORY_USER_REALTIME;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LabelCategory.UNRECOGNIZED;
  }
}

export enum SearchActivityType {
  SEARCH_ACTIVITY_TYPE_NONE = 0,
  /** SEARCH_ACTIVITY_TYPE_RANK - 搜索榜单 */
  SEARCH_ACTIVITY_TYPE_RANK = 1,
  /** SEARCH_ACTIVITY_TYPE_TASK - 搜索任务 */
  SEARCH_ACTIVITY_TYPE_TASK = 2,
  /** SEARCH_ACTIVITY_TYPE_LOTTERY - 搜索抽奖 */
  SEARCH_ACTIVITY_TYPE_LOTTERY = 3,
  /** SEARCH_ACTIVITY_TYPE_PRIZEPOOL - 搜索奖池 */
  SEARCH_ACTIVITY_TYPE_PRIZEPOOL = 4,
  UNRECOGNIZED = -1
}

export function searchActivityTypeFromJSON(object: any): SearchActivityType {
  switch (object) {
    case 0:
    case 'SEARCH_ACTIVITY_TYPE_NONE':
      return SearchActivityType.SEARCH_ACTIVITY_TYPE_NONE;
    case 1:
    case 'SEARCH_ACTIVITY_TYPE_RANK':
      return SearchActivityType.SEARCH_ACTIVITY_TYPE_RANK;
    case 2:
    case 'SEARCH_ACTIVITY_TYPE_TASK':
      return SearchActivityType.SEARCH_ACTIVITY_TYPE_TASK;
    case 3:
    case 'SEARCH_ACTIVITY_TYPE_LOTTERY':
      return SearchActivityType.SEARCH_ACTIVITY_TYPE_LOTTERY;
    case 4:
    case 'SEARCH_ACTIVITY_TYPE_PRIZEPOOL':
      return SearchActivityType.SEARCH_ACTIVITY_TYPE_PRIZEPOOL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SearchActivityType.UNRECOGNIZED;
  }
}

export interface RankScoreItem {
  key: string;
  score: number;
}

export interface BatchAddRankScoreReq {
  rank_id: number;
  /** 用来去重 */
  seq_id: string;
  /** 周期秒级时间戳, ActivityStatRankInfo 返回的值 */
  cycle_ts: number;
  score_list: RankScoreItem[];
}

export interface BatchAddRankScoreRsp {}

export interface BatchGetActivityReq {
  application_codes: string[];
}

export interface BatchGetActivityRsp {
  /** 活动配置 */
  activity_infos: ActivityInfo[];
}

export interface ListCustomFiltersReq {
  filter_type: CustomFilterType;
}

export interface ListCustomFiltersRsp {
  list: CustomFilters[];
}

export interface PrizePoolInfoSaveConfig {
  config: PrizePoolConfig | undefined;
  relate_rank_config: RankSaveConfig | undefined;
}

export interface SavePrizePoolReq {
  prize_pool_config: PrizePoolInfoSaveConfig | undefined;
}

export interface SavePrizePoolRsp {
  id: number;
}

export interface DeletePrizePoolReq {
  id: number;
  relate_rank_id: number;
}

export interface DeletePrizePoolRsp {}

export interface BatchGetPrizePoolByIdsReq {
  ids: number[];
}

export interface BatchGetPrizePoolByIdsRsp {
  /** 奖池配置 */
  infos: PrizePoolInfo[];
}

export interface GetPrizePoolByIdReq {
  /** 奖池id */
  id: number;
}

export interface GetPrizePoolByIdRsp {
  /** 奖池配置 */
  prize_pool_info: PrizePoolInfo | undefined;
}

export interface BatchSavePrizePoolConfig {
  /** 奖池配置,id为空表示新增 */
  configs: PrizePoolInfoSaveConfig[];
  /** 需要删除的奖池配置Id */
  delete_ids: number[];
}

export interface DeletePresentationConfigReq {
  config_ids: number[];
}

export interface DeletePresentationConfigRsp {}

export interface SavePresentationConfigReq {
  config: SavePresentationConfig | undefined;
}

export interface SavePresentationConfigRsp {
  config_id: number;
}

export interface GetPresentationConfigByIdReq {
  config_id: number;
}

export interface GetPresentationConfigByIdRsp {
  config: SavePresentationConfig | undefined;
}

export interface BatchSavePresentationConfig {
  /** 展示配置,id为空表示新增 */
  configs: SavePresentationConfig[];
  /** 需要删除的展示配置Id */
  delete_config_ids: number[];
}

export interface BatchGetUserInfosReq {
  uids: number[];
}

export interface BatchGetUserInfosRsp {
  user_infos: UserInfo[];
}

export interface BatchGetUserRoomInfosByWhiteListIdReq {
  /** 黑白名单id */
  white_list_id: string;
  /** 黑白名单分类，USER\ROOM，recommend_type== 1 的时候用 */
  white_list_category: string;
  /** 房间类型 */
  room_id_types: RoomIDType[];
  /** 数量限制 */
  limit: number;
}

export interface BatchGetUserRoomInfosByWhiteListIdRsp {
  user_room_infos: UserRoomInfo[];
}

export interface ListBWListItemReq {
  page: Page | undefined;
  config_id: string;
}

export interface ListBWListItemRsp {
  page: Page | undefined;
  /** 黑名单列表 */
  black_list: string[];
  /** 白名单列表 */
  white_list: string[];
  /** 是黑名单还是白名单 */
  type: BWListType;
}

export interface BatchGetUserRoomInfosReq {
  uids: number[];
  room_id_types: RoomIDType[];
}

export interface BatchGetUserRoomInfosRsp {
  user_room_infos: UserRoomInfo[];
}

export interface BatchSaveRecommendRoomConfig {
  /** 任务配置,id为空表示新增 */
  room_recommend_configs: SaveRoomRecommendConfig[];
  /** 需要删除的房间推荐配置Id */
  delete_room_recommend_config_ids: number[];
}

export interface DeleteRoomRecommendConfigReq {
  config_ids: number[];
}

export interface DeleteRoomRecommendConfigRsp {}

export interface SaveRoomRecommendConfigReq {
  config: SaveRoomRecommendConfig | undefined;
}

export interface SaveRoomRecommendConfig {
  config: RoomRecommendConfig | undefined;
}

export interface SaveRoomRecommendConfigRsp {
  /** 配置ID */
  config_id: number;
}

export interface GetRoomRecommendConfigByIdReq {
  id: number;
}

export interface GetRoomRecommendConfigByIdRsp {
  config: RoomRecommendInfo | undefined;
}

export interface BatchSaveLotteryConfig {
  /** 任务配置,id为空表示新增 */
  lottery_configs: SaveLotteryConfig[];
  /** 需要删除的抽奖配置Id */
  delete_lottery_config_ids: number[];
}

export interface DeleteLotteryConfigReq {
  lottery_id: number;
}

export interface DeleteLotteryConfigRsp {}

export interface SaveLotteryConfigReq {
  config: SaveLotteryConfig | undefined;
}

export interface SaveLotteryConfig {
  config: LotteryConfig | undefined;
  reward_configs: LotteryRewardConfig[];
  delete_reward_config_ids: number[];
  batch_save_task_infos: BatchSaveTaskConfig | undefined;
}

export interface SaveLotteryConfigRsp {
  /** 抽奖ID */
  lottery_id: number;
}

export interface GetLotteryConfigByIdReq {
  id: number;
}

export interface GetLotteryConfigByIdRsp {
  lottery_config: LotteryInfo | undefined;
}

export interface BatchGetGuildInfoReq {
  guild_ids: number[];
}

export interface BatchGetGuildInfoRsp {
  list: GuildInfo[];
}

export interface BatchGetFamilyInfoReq {
  family_ids: number[];
}

export interface BatchGetFamilyInfoRsp {
  list: FamilyInfo[];
}

export interface BatchGetTaskByIdsReq {
  /** 任务id */
  task_config_id: number[];
}

export interface BatchGetTaskByIdsRsp {
  /** 任务配置 */
  task_info: TaskInfo[];
}

export interface GetTaskByIdReq {
  /** 任务id */
  task_config_id: number;
}

export interface GetTaskByIdRsp {
  /** 任务配置 */
  task_info: TaskInfo | undefined;
}

export interface SaveActivityReq {
  /** 活动配置。活动ID为空表示新增，不为空则修改 */
  activity: Activity | undefined;
  /** 榜单配置 */
  rank_config: RankBatchSaveConfig | undefined;
  /** 任务配置 */
  batch_save_task_config: BatchSaveTaskConfig | undefined;
  /** pk配置 */
  batch_save_pk_config: BatchSavePKConfig | undefined;
  /** 抽奖配置 */
  batch_save_lottery_config: BatchSaveLotteryConfig | undefined;
  /** 房间推荐配置 */
  batch_save_recommend_room_config: BatchSaveRecommendRoomConfig | undefined;
  /** 展示配置 */
  batch_save_presentation_config: BatchSavePresentationConfig | undefined;
  /** 奖池配置 */
  batch_save_prize_pool_config: BatchSavePrizePoolConfig | undefined;
}

export interface SaveActivityRsp {}

export interface SaveTaskReq {
  task_config: SaveTaskConfig | undefined;
}

export interface SaveTaskRsp {
  /** 任务配置ID */
  task_config_id: number;
}

export interface DeleteTaskReq {
  /** 任务配置ID */
  task_config_ids: number[];
}

export interface DeleteTaskRsp {}

export interface SaveRankReq {
  /** 榜单配置 */
  rank_config: RankSaveConfig | undefined;
}

export interface SaveRankRsp {
  /** 榜单配置ID */
  rank_config_id: number;
}

export interface DeleteRankReq {
  /** 榜单配置ID */
  rank_config_id: number;
}

export interface DeleteRankRsp {}

export interface GetRankByIdReq {
  /** 榜单配置ID */
  rank_config_id: number;
}

export interface GetRankByIdRsp {
  /** 榜单信息 */
  rank_info: RankInfo | undefined;
}

export interface UpdateActivityRsp {}

export interface UpdateStatusReq {
  /** 活动ID */
  activity_id: number;
  /** 配置状态：normal(上架) disable(下架) del(删除) */
  status: string;
}

export interface UpdateStatusRsp {}

export interface PageQueryReq {
  page: Page | undefined;
  /** 活动名称 */
  name: string;
}

export interface PageQueryRsp {
  page: Page | undefined;
  /** 活动配置 */
  list: ActivityInfo[];
}

export interface RankRewardConfig {
  /** 新增、修改榜单奖励 */
  batch_update_rank_rewards: BatchUpdateRankReward[];
  /** 删除榜单奖励-榜单奖励Id */
  delete_rank_reward_ids: number[];
}

export interface RankSaveConfig {
  /** 榜单配置，id为空表示新增 */
  rank_config: RankConfig | undefined;
  /** 榜单奖励配置 */
  rank_reward_config: RankRewardConfig | undefined;
  /** 贡献榜奖励配置 */
  supporter_rank_reward_config: RankRewardConfig | undefined;
}

export interface RankBatchSaveConfig {
  /** 榜单配置，id为空表示新增 */
  rank_config_list: RankSaveConfig[];
  /** 需要删除的榜单Id */
  delete_rank_ids: number[];
}

/** 获取奖励分类列表请求 */
export interface ListRewardCategoryReq {}

/** 获取奖励分类列表响应 */
export interface ListRewardCategoryRsp {
  /** 业务支持的所有奖励分类列表 */
  categories: RewardCategory[];
}

/** 根据分类来查询子分类，不会太多,无其他查询条件 */
export interface ListRewardSubCategoryReq {
  /** 奖励分类标识, 必传. */
  category_key: string;
}

export interface ListRewardSubCategoryRsp {
  data: RewardSubCategory[];
}

/** 搜索指定奖励分类的奖励配置项请求 */
export interface SearchRewardItemReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 奖励分类标识, 必传. */
  category_key: string;
  /** 根据奖励ID查询, 可选. */
  item_id: string;
  /** 根据奖励名称查询, 可选. */
  item_name: string;
  /** 关键字模糊查询, 可选. */
  keyword: string;
  /** 是否获取完整的信息，true 会返回creator 之类的管理后台用的信息, 默认为false */
  is_get_complete_info: boolean;
  /** 根据子分类的id来查 */
  sub_category_ids: string[];
  /** 按照货币类型筛选 */
  currencys: number[];
}

/** 搜索指定奖励分类的奖励配置项响应 */
export interface SearchRewardItemRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 奖励配置项列表 */
  items: RewardItem[];
}

export interface BatchSaveTaskConfig {
  /** 任务配置,id为空表示新增 */
  task_configs: SaveTaskConfig[];
  /** 需要删除的任务Id */
  delete_task_ids: number[];
}

export interface SaveTaskConfig {
  /** 活动任务配置;任务ID为0表示新增,不为0则修改 */
  task_config: TaskConfig | undefined;
  /** 活动子任务配置;子任务ID为0表示新增,不为0则修改 */
  task_sub_configs: TaskSubConfig[];
  /** 活动任务奖励配置;奖励ID为0表示新增,不为0则修改 */
  task_reward_configs: TaskRewardConfig[];
  /** 需要删除的子任务Id */
  delete_task_sub_ids: number[];
  /** 需要删除的任务奖励Id */
  delete_task_reward_ids: number[];
  /** H5用到的配置信息 */
  task_h5_configs: TaskH5Config[];
  /** 需要删除的配置信息id */
  delete_task_h5_ids: number[];
}

/** 批量根据ID获取奖励配置项信息请求 */
export interface ListRewardItemByIdReq {
  /** 奖励分类标识, 必传. */
  category_key: string;
  /** 根据奖励ID查询, 可选. */
  item_ids: string[];
  /** 是否获取完整的信息， true 会返回creator 之类的管理后台用的信息, 默认为false */
  is_get_complete_info: boolean;
}

/** 批量根据ID获取奖励配置项信息响应 */
export interface ListRewardItemByIdRsp {
  /** 奖励配置项列表 */
  items: RewardItem[];
}

export interface SaveSpecialRewardItemReq {
  /** 奖励分类标识 */
  category_key: string;
  /** 特殊奖励配置 */
  reward_item: RewardItem | undefined;
}

export interface SaveSpecialRewardItemRsp {
  /** 特殊奖励ID */
  reward_item_id: number;
}

export interface DeleteSpecialRewardItemReq {
  /** 奖励分类标识 */
  category_key: string;
  /** 特殊奖励ID */
  reward_item_ids: number[];
}

export interface DeleteSpecialRewardItemRsp {}

/** 根据活动ID获取活动配置 */
export interface GetActivityByApplicationCodeReq {
  /** 积木应用标识 */
  application_code: string;
  /** 榜单id，任务id，抽奖id */
  reltated_id: number;
  /** 搜索类型 */
  type: SearchActivityType;
}

/** 根据活动ID获取活动配置 */
export interface GetActivityByApplicationCodeRsp {
  /** 活动配置 */
  activity_info: ActivityInfo | undefined;
}

/** 黑白名单配置 */
export interface BWListConfig {
  /** 配置ID */
  id: string;
  /** 黑白名单分类, USER: 用户的  ROOM: 房间的  FAMILY: 家族的 ... */
  category: string;
  /** 配置名称 */
  name: string;
  /** 国际化国家名称 */
  i18n_names: { [key: string]: string };
  /** 黑名单列表 */
  black_list: string[];
  /** 白名单列表 */
  white_list: string[];
  /** 是黑名单还是白名单 */
  type: BWListType;
}

export interface BWListConfig_I18nNamesEntry {
  key: string;
  value: string;
}

export interface GetBWListConfigReq {
  /** 黑白名单分类, USER: 用户的  ROOM: 房间的  FAMILY: 家族的 ... */
  category: string;
}

export interface GetBWListConfigRsp {
  /** 黑白名单配置列表 */
  configs: BWListConfig[];
}

/** 标签配置 */
export interface LabelConfig {
  /** 标签id */
  id: string;
  /** 国际化标签名称 */
  i18n_names: { [key: string]: string };
}

export interface LabelConfig_I18nNamesEntry {
  key: string;
  value: string;
}

export interface ListLabelConfigReq {
  category: LabelCategory;
}

export interface ListLabelConfigRsp {
  configs: LabelConfig[];
}

export interface SavePKConfigReq {
  pk_config: SavePKConfig | undefined;
}

export interface SavePKConfig {
  pk_config: PKConfig | undefined;
  /** id = 0 新增，id >0 更新 */
  details: PKDetail[];
  delete_pk_detail_ids: number[];
}

export interface SavePKConfigRsp {
  pk_config_id: number;
}

export interface GetPKConfigByIdReq {
  pk_config_id: number;
}

export interface GetPKConfigByIdRsp {
  /** 活动任务配置;任务ID为0表示新增,不为0则修改 */
  pk_info: PKInfo | undefined;
}

export interface DeletePKConfigReq {
  pk_config_id: number;
}

export interface DeletePKConfigRsp {}

export interface BatchSavePKConfig {
  /** 任务配置,id为空表示新增 */
  pk_configs: SavePKConfig[];
  /** 需要删除的pk配置Id */
  delete_pk_config_ids: number[];
}

export interface GetPromotionListByRankIdReq {
  rank_id: number;
}

export interface GetPromotionListByRankIdRsp {
  entities: PromotionEntity[];
}

export interface PromotionEntity {
  member_id: string;
  nick: string;
  avatar: string;
  rank: number;
  score: number;
}

export interface CopyActivityByApplicationCodeReq {
  /** 源应用 */
  from_application_code: string;
  /** 目标应用 */
  to_application_code: string;
  /** 应用转模板 */
  to_template: boolean;
  /** 目标时区 */
  to_cou: string;
}

export interface CopyActivityByApplicationCodeRsp {
  /** 复制后的活动id */
  activity_id: number;
  /** 榜单映射关系 */
  rank_id_map: { [key: number]: number };
  /** 榜单奖励映射关系 */
  rank_reward_id_map: { [key: number]: number };
  /** 榜单奖励详情映射关系 */
  rank_reward_detail_id_map: { [key: number]: number };
  /** 小头像榜单奖励映射关系 */
  supporter_rank_reward_id_map: { [key: number]: number };
  /** 小头像榜单奖励详情映射关系 */
  supporter_rank_reward_detail_id_map: { [key: number]: number };
  /** 父任务映射关系 */
  task_id_map: { [key: number]: number };
  /** 子任务映射关系 */
  task_sub_id_map: { [key: number]: number };
  /** 任务奖励映射关系 */
  task_reward_id_map: { [key: number]: number };
  /** 任务h5配置映射关系 */
  task_h5_config_id_map: { [key: number]: number };
  /** pk映射关系 */
  pk_id_map: { [key: number]: number };
  /** pk详情映射关系 */
  pk_config_detail_id_map: { [key: number]: number };
  /** 抽奖映射关系 */
  lottery_id_map: { [key: number]: number };
  /** 抽奖奖品信息映射关系 */
  lottery_reward_id_map: { [key: number]: number };
  /** 推荐信息映射关系 */
  recommend_id_map: { [key: number]: number };
  /** 荣誉推荐组件 */
  presentation_id_map: { [key: number]: number };
  /** 奖池组件 */
  prize_pool_id_map: { [key: number]: number };
}

export interface CopyActivityByApplicationCodeRsp_RankIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_TaskIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_PkIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_LotteryIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_RecommendIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_PresentationIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeReq {
  /** 源应用 */
  from_application_code: string;
  /** 目标应用 */
  to_application_code: string;
  /** 应用转模板 */
  to_template: boolean;
  /** 目标时区 */
  to_cou: string;
  /** 目标子业务 */
  to_sub_anm: string;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp {
  /** 复制后的活动id */
  activity_id: number;
  /** 榜单映射关系 */
  rank_id_map: { [key: number]: number };
  /** 榜单奖励映射关系 */
  rank_reward_id_map: { [key: number]: number };
  /** 榜单奖励详情映射关系 */
  rank_reward_detail_id_map: { [key: number]: number };
  /** 小头像榜单奖励映射关系 */
  supporter_rank_reward_id_map: { [key: number]: number };
  /** 小头像榜单奖励详情映射关系 */
  supporter_rank_reward_detail_id_map: { [key: number]: number };
  /** 父任务映射关系 */
  task_id_map: { [key: number]: number };
  /** 子任务映射关系 */
  task_sub_id_map: { [key: number]: number };
  /** 任务奖励映射关系 */
  task_reward_id_map: { [key: number]: number };
  /** 任务h5配置映射关系 */
  task_h5_config_id_map: { [key: number]: number };
  /** pk映射关系 */
  pk_id_map: { [key: number]: number };
  /** pk详情映射关系 */
  pk_config_detail_id_map: { [key: number]: number };
  /** 抽奖映射关系 */
  lottery_id_map: { [key: number]: number };
  /** 抽奖奖品信息映射关系 */
  lottery_reward_id_map: { [key: number]: number };
  /** 推荐信息映射关系 */
  recommend_id_map: { [key: number]: number };
  /** 荣誉推荐组件 */
  presentation_id_map: { [key: number]: number };
  /** 奖池组件 */
  prize_pool_id_map: { [key: number]: number };
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry {
  key: number;
  value: number;
}

export interface CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry {
  key: number;
  value: number;
}

export interface GetActivityByRelatedIDReq {
  /** 榜单id，任务id，抽奖id */
  related_id: number;
  /** 搜索类型 */
  type: SearchActivityType;
}

export interface GetActivityByRelatedIDRsp {
  /** 仅为活动配置，不包含榜单/任务/抽奖信息 */
  activity: Activity | undefined;
}

function createBaseRankScoreItem(): RankScoreItem {
  return { key: '', score: 0 };
}

export const RankScoreItem: MessageFns<RankScoreItem> = {
  fromJSON(object: any): RankScoreItem {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      score: isSet(object.score) ? globalThis.Number(object.score) : 0
    };
  },

  create<I extends Exact<DeepPartial<RankScoreItem>, I>>(base?: I): RankScoreItem {
    return RankScoreItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankScoreItem>, I>>(object: I): RankScoreItem {
    const message = createBaseRankScoreItem();
    message.key = object.key ?? '';
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseBatchAddRankScoreReq(): BatchAddRankScoreReq {
  return { rank_id: 0, seq_id: '', cycle_ts: 0, score_list: [] };
}

export const BatchAddRankScoreReq: MessageFns<BatchAddRankScoreReq> = {
  fromJSON(object: any): BatchAddRankScoreReq {
    return {
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      cycle_ts: isSet(object.cycle_ts) ? globalThis.Number(object.cycle_ts) : 0,
      score_list: globalThis.Array.isArray(object?.score_list)
        ? object.score_list.map((e: any) => RankScoreItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchAddRankScoreReq>, I>>(base?: I): BatchAddRankScoreReq {
    return BatchAddRankScoreReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchAddRankScoreReq>, I>>(object: I): BatchAddRankScoreReq {
    const message = createBaseBatchAddRankScoreReq();
    message.rank_id = object.rank_id ?? 0;
    message.seq_id = object.seq_id ?? '';
    message.cycle_ts = object.cycle_ts ?? 0;
    message.score_list = object.score_list?.map(e => RankScoreItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchAddRankScoreRsp(): BatchAddRankScoreRsp {
  return {};
}

export const BatchAddRankScoreRsp: MessageFns<BatchAddRankScoreRsp> = {
  fromJSON(_: any): BatchAddRankScoreRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchAddRankScoreRsp>, I>>(base?: I): BatchAddRankScoreRsp {
    return BatchAddRankScoreRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchAddRankScoreRsp>, I>>(_: I): BatchAddRankScoreRsp {
    const message = createBaseBatchAddRankScoreRsp();
    return message;
  }
};

function createBaseBatchGetActivityReq(): BatchGetActivityReq {
  return { application_codes: [] };
}

export const BatchGetActivityReq: MessageFns<BatchGetActivityReq> = {
  fromJSON(object: any): BatchGetActivityReq {
    return {
      application_codes: globalThis.Array.isArray(object?.application_codes)
        ? object.application_codes.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetActivityReq>, I>>(base?: I): BatchGetActivityReq {
    return BatchGetActivityReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetActivityReq>, I>>(object: I): BatchGetActivityReq {
    const message = createBaseBatchGetActivityReq();
    message.application_codes = object.application_codes?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetActivityRsp(): BatchGetActivityRsp {
  return { activity_infos: [] };
}

export const BatchGetActivityRsp: MessageFns<BatchGetActivityRsp> = {
  fromJSON(object: any): BatchGetActivityRsp {
    return {
      activity_infos: globalThis.Array.isArray(object?.activity_infos)
        ? object.activity_infos.map((e: any) => ActivityInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetActivityRsp>, I>>(base?: I): BatchGetActivityRsp {
    return BatchGetActivityRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetActivityRsp>, I>>(object: I): BatchGetActivityRsp {
    const message = createBaseBatchGetActivityRsp();
    message.activity_infos = object.activity_infos?.map(e => ActivityInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListCustomFiltersReq(): ListCustomFiltersReq {
  return { filter_type: 0 };
}

export const ListCustomFiltersReq: MessageFns<ListCustomFiltersReq> = {
  fromJSON(object: any): ListCustomFiltersReq {
    return { filter_type: isSet(object.filter_type) ? customFilterTypeFromJSON(object.filter_type) : 0 };
  },

  create<I extends Exact<DeepPartial<ListCustomFiltersReq>, I>>(base?: I): ListCustomFiltersReq {
    return ListCustomFiltersReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCustomFiltersReq>, I>>(object: I): ListCustomFiltersReq {
    const message = createBaseListCustomFiltersReq();
    message.filter_type = object.filter_type ?? 0;
    return message;
  }
};

function createBaseListCustomFiltersRsp(): ListCustomFiltersRsp {
  return { list: [] };
}

export const ListCustomFiltersRsp: MessageFns<ListCustomFiltersRsp> = {
  fromJSON(object: any): ListCustomFiltersRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => CustomFilters.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListCustomFiltersRsp>, I>>(base?: I): ListCustomFiltersRsp {
    return ListCustomFiltersRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListCustomFiltersRsp>, I>>(object: I): ListCustomFiltersRsp {
    const message = createBaseListCustomFiltersRsp();
    message.list = object.list?.map(e => CustomFilters.fromPartial(e)) || [];
    return message;
  }
};

function createBasePrizePoolInfoSaveConfig(): PrizePoolInfoSaveConfig {
  return { config: undefined, relate_rank_config: undefined };
}

export const PrizePoolInfoSaveConfig: MessageFns<PrizePoolInfoSaveConfig> = {
  fromJSON(object: any): PrizePoolInfoSaveConfig {
    return {
      config: isSet(object.config) ? PrizePoolConfig.fromJSON(object.config) : undefined,
      relate_rank_config: isSet(object.relate_rank_config)
        ? RankSaveConfig.fromJSON(object.relate_rank_config)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<PrizePoolInfoSaveConfig>, I>>(base?: I): PrizePoolInfoSaveConfig {
    return PrizePoolInfoSaveConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizePoolInfoSaveConfig>, I>>(object: I): PrizePoolInfoSaveConfig {
    const message = createBasePrizePoolInfoSaveConfig();
    message.config =
      object.config !== undefined && object.config !== null ? PrizePoolConfig.fromPartial(object.config) : undefined;
    message.relate_rank_config =
      object.relate_rank_config !== undefined && object.relate_rank_config !== null
        ? RankSaveConfig.fromPartial(object.relate_rank_config)
        : undefined;
    return message;
  }
};

function createBaseSavePrizePoolReq(): SavePrizePoolReq {
  return { prize_pool_config: undefined };
}

export const SavePrizePoolReq: MessageFns<SavePrizePoolReq> = {
  fromJSON(object: any): SavePrizePoolReq {
    return {
      prize_pool_config: isSet(object.prize_pool_config)
        ? PrizePoolInfoSaveConfig.fromJSON(object.prize_pool_config)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<SavePrizePoolReq>, I>>(base?: I): SavePrizePoolReq {
    return SavePrizePoolReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePrizePoolReq>, I>>(object: I): SavePrizePoolReq {
    const message = createBaseSavePrizePoolReq();
    message.prize_pool_config =
      object.prize_pool_config !== undefined && object.prize_pool_config !== null
        ? PrizePoolInfoSaveConfig.fromPartial(object.prize_pool_config)
        : undefined;
    return message;
  }
};

function createBaseSavePrizePoolRsp(): SavePrizePoolRsp {
  return { id: 0 };
}

export const SavePrizePoolRsp: MessageFns<SavePrizePoolRsp> = {
  fromJSON(object: any): SavePrizePoolRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<SavePrizePoolRsp>, I>>(base?: I): SavePrizePoolRsp {
    return SavePrizePoolRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePrizePoolRsp>, I>>(object: I): SavePrizePoolRsp {
    const message = createBaseSavePrizePoolRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeletePrizePoolReq(): DeletePrizePoolReq {
  return { id: 0, relate_rank_id: 0 };
}

export const DeletePrizePoolReq: MessageFns<DeletePrizePoolReq> = {
  fromJSON(object: any): DeletePrizePoolReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      relate_rank_id: isSet(object.relate_rank_id) ? globalThis.Number(object.relate_rank_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<DeletePrizePoolReq>, I>>(base?: I): DeletePrizePoolReq {
    return DeletePrizePoolReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePrizePoolReq>, I>>(object: I): DeletePrizePoolReq {
    const message = createBaseDeletePrizePoolReq();
    message.id = object.id ?? 0;
    message.relate_rank_id = object.relate_rank_id ?? 0;
    return message;
  }
};

function createBaseDeletePrizePoolRsp(): DeletePrizePoolRsp {
  return {};
}

export const DeletePrizePoolRsp: MessageFns<DeletePrizePoolRsp> = {
  fromJSON(_: any): DeletePrizePoolRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePrizePoolRsp>, I>>(base?: I): DeletePrizePoolRsp {
    return DeletePrizePoolRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePrizePoolRsp>, I>>(_: I): DeletePrizePoolRsp {
    const message = createBaseDeletePrizePoolRsp();
    return message;
  }
};

function createBaseBatchGetPrizePoolByIdsReq(): BatchGetPrizePoolByIdsReq {
  return { ids: [] };
}

export const BatchGetPrizePoolByIdsReq: MessageFns<BatchGetPrizePoolByIdsReq> = {
  fromJSON(object: any): BatchGetPrizePoolByIdsReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetPrizePoolByIdsReq>, I>>(base?: I): BatchGetPrizePoolByIdsReq {
    return BatchGetPrizePoolByIdsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetPrizePoolByIdsReq>, I>>(object: I): BatchGetPrizePoolByIdsReq {
    const message = createBaseBatchGetPrizePoolByIdsReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetPrizePoolByIdsRsp(): BatchGetPrizePoolByIdsRsp {
  return { infos: [] };
}

export const BatchGetPrizePoolByIdsRsp: MessageFns<BatchGetPrizePoolByIdsRsp> = {
  fromJSON(object: any): BatchGetPrizePoolByIdsRsp {
    return {
      infos: globalThis.Array.isArray(object?.infos) ? object.infos.map((e: any) => PrizePoolInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetPrizePoolByIdsRsp>, I>>(base?: I): BatchGetPrizePoolByIdsRsp {
    return BatchGetPrizePoolByIdsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetPrizePoolByIdsRsp>, I>>(object: I): BatchGetPrizePoolByIdsRsp {
    const message = createBaseBatchGetPrizePoolByIdsRsp();
    message.infos = object.infos?.map(e => PrizePoolInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetPrizePoolByIdReq(): GetPrizePoolByIdReq {
  return { id: 0 };
}

export const GetPrizePoolByIdReq: MessageFns<GetPrizePoolByIdReq> = {
  fromJSON(object: any): GetPrizePoolByIdReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetPrizePoolByIdReq>, I>>(base?: I): GetPrizePoolByIdReq {
    return GetPrizePoolByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrizePoolByIdReq>, I>>(object: I): GetPrizePoolByIdReq {
    const message = createBaseGetPrizePoolByIdReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetPrizePoolByIdRsp(): GetPrizePoolByIdRsp {
  return { prize_pool_info: undefined };
}

export const GetPrizePoolByIdRsp: MessageFns<GetPrizePoolByIdRsp> = {
  fromJSON(object: any): GetPrizePoolByIdRsp {
    return {
      prize_pool_info: isSet(object.prize_pool_info) ? PrizePoolInfo.fromJSON(object.prize_pool_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetPrizePoolByIdRsp>, I>>(base?: I): GetPrizePoolByIdRsp {
    return GetPrizePoolByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPrizePoolByIdRsp>, I>>(object: I): GetPrizePoolByIdRsp {
    const message = createBaseGetPrizePoolByIdRsp();
    message.prize_pool_info =
      object.prize_pool_info !== undefined && object.prize_pool_info !== null
        ? PrizePoolInfo.fromPartial(object.prize_pool_info)
        : undefined;
    return message;
  }
};

function createBaseBatchSavePrizePoolConfig(): BatchSavePrizePoolConfig {
  return { configs: [], delete_ids: [] };
}

export const BatchSavePrizePoolConfig: MessageFns<BatchSavePrizePoolConfig> = {
  fromJSON(object: any): BatchSavePrizePoolConfig {
    return {
      configs: globalThis.Array.isArray(object?.configs)
        ? object.configs.map((e: any) => PrizePoolInfoSaveConfig.fromJSON(e))
        : [],
      delete_ids: globalThis.Array.isArray(object?.delete_ids)
        ? object.delete_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSavePrizePoolConfig>, I>>(base?: I): BatchSavePrizePoolConfig {
    return BatchSavePrizePoolConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSavePrizePoolConfig>, I>>(object: I): BatchSavePrizePoolConfig {
    const message = createBaseBatchSavePrizePoolConfig();
    message.configs = object.configs?.map(e => PrizePoolInfoSaveConfig.fromPartial(e)) || [];
    message.delete_ids = object.delete_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeletePresentationConfigReq(): DeletePresentationConfigReq {
  return { config_ids: [] };
}

export const DeletePresentationConfigReq: MessageFns<DeletePresentationConfigReq> = {
  fromJSON(object: any): DeletePresentationConfigReq {
    return {
      config_ids: globalThis.Array.isArray(object?.config_ids)
        ? object.config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeletePresentationConfigReq>, I>>(base?: I): DeletePresentationConfigReq {
    return DeletePresentationConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePresentationConfigReq>, I>>(object: I): DeletePresentationConfigReq {
    const message = createBaseDeletePresentationConfigReq();
    message.config_ids = object.config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeletePresentationConfigRsp(): DeletePresentationConfigRsp {
  return {};
}

export const DeletePresentationConfigRsp: MessageFns<DeletePresentationConfigRsp> = {
  fromJSON(_: any): DeletePresentationConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePresentationConfigRsp>, I>>(base?: I): DeletePresentationConfigRsp {
    return DeletePresentationConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePresentationConfigRsp>, I>>(_: I): DeletePresentationConfigRsp {
    const message = createBaseDeletePresentationConfigRsp();
    return message;
  }
};

function createBaseSavePresentationConfigReq(): SavePresentationConfigReq {
  return { config: undefined };
}

export const SavePresentationConfigReq: MessageFns<SavePresentationConfigReq> = {
  fromJSON(object: any): SavePresentationConfigReq {
    return { config: isSet(object.config) ? SavePresentationConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<SavePresentationConfigReq>, I>>(base?: I): SavePresentationConfigReq {
    return SavePresentationConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePresentationConfigReq>, I>>(object: I): SavePresentationConfigReq {
    const message = createBaseSavePresentationConfigReq();
    message.config =
      object.config !== undefined && object.config !== null
        ? SavePresentationConfig.fromPartial(object.config)
        : undefined;
    return message;
  }
};

function createBaseSavePresentationConfigRsp(): SavePresentationConfigRsp {
  return { config_id: 0 };
}

export const SavePresentationConfigRsp: MessageFns<SavePresentationConfigRsp> = {
  fromJSON(object: any): SavePresentationConfigRsp {
    return { config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SavePresentationConfigRsp>, I>>(base?: I): SavePresentationConfigRsp {
    return SavePresentationConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePresentationConfigRsp>, I>>(object: I): SavePresentationConfigRsp {
    const message = createBaseSavePresentationConfigRsp();
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseGetPresentationConfigByIdReq(): GetPresentationConfigByIdReq {
  return { config_id: 0 };
}

export const GetPresentationConfigByIdReq: MessageFns<GetPresentationConfigByIdReq> = {
  fromJSON(object: any): GetPresentationConfigByIdReq {
    return { config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetPresentationConfigByIdReq>, I>>(base?: I): GetPresentationConfigByIdReq {
    return GetPresentationConfigByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPresentationConfigByIdReq>, I>>(object: I): GetPresentationConfigByIdReq {
    const message = createBaseGetPresentationConfigByIdReq();
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseGetPresentationConfigByIdRsp(): GetPresentationConfigByIdRsp {
  return { config: undefined };
}

export const GetPresentationConfigByIdRsp: MessageFns<GetPresentationConfigByIdRsp> = {
  fromJSON(object: any): GetPresentationConfigByIdRsp {
    return { config: isSet(object.config) ? SavePresentationConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<GetPresentationConfigByIdRsp>, I>>(base?: I): GetPresentationConfigByIdRsp {
    return GetPresentationConfigByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPresentationConfigByIdRsp>, I>>(object: I): GetPresentationConfigByIdRsp {
    const message = createBaseGetPresentationConfigByIdRsp();
    message.config =
      object.config !== undefined && object.config !== null
        ? SavePresentationConfig.fromPartial(object.config)
        : undefined;
    return message;
  }
};

function createBaseBatchSavePresentationConfig(): BatchSavePresentationConfig {
  return { configs: [], delete_config_ids: [] };
}

export const BatchSavePresentationConfig: MessageFns<BatchSavePresentationConfig> = {
  fromJSON(object: any): BatchSavePresentationConfig {
    return {
      configs: globalThis.Array.isArray(object?.configs)
        ? object.configs.map((e: any) => SavePresentationConfig.fromJSON(e))
        : [],
      delete_config_ids: globalThis.Array.isArray(object?.delete_config_ids)
        ? object.delete_config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSavePresentationConfig>, I>>(base?: I): BatchSavePresentationConfig {
    return BatchSavePresentationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSavePresentationConfig>, I>>(object: I): BatchSavePresentationConfig {
    const message = createBaseBatchSavePresentationConfig();
    message.configs = object.configs?.map(e => SavePresentationConfig.fromPartial(e)) || [];
    message.delete_config_ids = object.delete_config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserInfosReq(): BatchGetUserInfosReq {
  return { uids: [] };
}

export const BatchGetUserInfosReq: MessageFns<BatchGetUserInfosReq> = {
  fromJSON(object: any): BatchGetUserInfosReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetUserInfosReq>, I>>(base?: I): BatchGetUserInfosReq {
    return BatchGetUserInfosReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserInfosReq>, I>>(object: I): BatchGetUserInfosReq {
    const message = createBaseBatchGetUserInfosReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserInfosRsp(): BatchGetUserInfosRsp {
  return { user_infos: [] };
}

export const BatchGetUserInfosRsp: MessageFns<BatchGetUserInfosRsp> = {
  fromJSON(object: any): BatchGetUserInfosRsp {
    return {
      user_infos: globalThis.Array.isArray(object?.user_infos)
        ? object.user_infos.map((e: any) => UserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserInfosRsp>, I>>(base?: I): BatchGetUserInfosRsp {
    return BatchGetUserInfosRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserInfosRsp>, I>>(object: I): BatchGetUserInfosRsp {
    const message = createBaseBatchGetUserInfosRsp();
    message.user_infos = object.user_infos?.map(e => UserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetUserRoomInfosByWhiteListIdReq(): BatchGetUserRoomInfosByWhiteListIdReq {
  return { white_list_id: '', white_list_category: '', room_id_types: [], limit: 0 };
}

export const BatchGetUserRoomInfosByWhiteListIdReq: MessageFns<BatchGetUserRoomInfosByWhiteListIdReq> = {
  fromJSON(object: any): BatchGetUserRoomInfosByWhiteListIdReq {
    return {
      white_list_id: isSet(object.white_list_id) ? globalThis.String(object.white_list_id) : '',
      white_list_category: isSet(object.white_list_category) ? globalThis.String(object.white_list_category) : '',
      room_id_types: globalThis.Array.isArray(object?.room_id_types)
        ? object.room_id_types.map((e: any) => roomIDTypeFromJSON(e))
        : [],
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserRoomInfosByWhiteListIdReq>, I>>(
    base?: I
  ): BatchGetUserRoomInfosByWhiteListIdReq {
    return BatchGetUserRoomInfosByWhiteListIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserRoomInfosByWhiteListIdReq>, I>>(
    object: I
  ): BatchGetUserRoomInfosByWhiteListIdReq {
    const message = createBaseBatchGetUserRoomInfosByWhiteListIdReq();
    message.white_list_id = object.white_list_id ?? '';
    message.white_list_category = object.white_list_category ?? '';
    message.room_id_types = object.room_id_types?.map(e => e) || [];
    message.limit = object.limit ?? 0;
    return message;
  }
};

function createBaseBatchGetUserRoomInfosByWhiteListIdRsp(): BatchGetUserRoomInfosByWhiteListIdRsp {
  return { user_room_infos: [] };
}

export const BatchGetUserRoomInfosByWhiteListIdRsp: MessageFns<BatchGetUserRoomInfosByWhiteListIdRsp> = {
  fromJSON(object: any): BatchGetUserRoomInfosByWhiteListIdRsp {
    return {
      user_room_infos: globalThis.Array.isArray(object?.user_room_infos)
        ? object.user_room_infos.map((e: any) => UserRoomInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserRoomInfosByWhiteListIdRsp>, I>>(
    base?: I
  ): BatchGetUserRoomInfosByWhiteListIdRsp {
    return BatchGetUserRoomInfosByWhiteListIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserRoomInfosByWhiteListIdRsp>, I>>(
    object: I
  ): BatchGetUserRoomInfosByWhiteListIdRsp {
    const message = createBaseBatchGetUserRoomInfosByWhiteListIdRsp();
    message.user_room_infos = object.user_room_infos?.map(e => UserRoomInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListBWListItemReq(): ListBWListItemReq {
  return { page: undefined, config_id: '' };
}

export const ListBWListItemReq: MessageFns<ListBWListItemReq> = {
  fromJSON(object: any): ListBWListItemReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      config_id: isSet(object.config_id) ? globalThis.String(object.config_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListBWListItemReq>, I>>(base?: I): ListBWListItemReq {
    return ListBWListItemReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBWListItemReq>, I>>(object: I): ListBWListItemReq {
    const message = createBaseListBWListItemReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.config_id = object.config_id ?? '';
    return message;
  }
};

function createBaseListBWListItemRsp(): ListBWListItemRsp {
  return { page: undefined, black_list: [], white_list: [], type: 0 };
}

export const ListBWListItemRsp: MessageFns<ListBWListItemRsp> = {
  fromJSON(object: any): ListBWListItemRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      black_list: globalThis.Array.isArray(object?.black_list)
        ? object.black_list.map((e: any) => globalThis.String(e))
        : [],
      white_list: globalThis.Array.isArray(object?.white_list)
        ? object.white_list.map((e: any) => globalThis.String(e))
        : [],
      type: isSet(object.type) ? bWListTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBWListItemRsp>, I>>(base?: I): ListBWListItemRsp {
    return ListBWListItemRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBWListItemRsp>, I>>(object: I): ListBWListItemRsp {
    const message = createBaseListBWListItemRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.black_list = object.black_list?.map(e => e) || [];
    message.white_list = object.white_list?.map(e => e) || [];
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseBatchGetUserRoomInfosReq(): BatchGetUserRoomInfosReq {
  return { uids: [], room_id_types: [] };
}

export const BatchGetUserRoomInfosReq: MessageFns<BatchGetUserRoomInfosReq> = {
  fromJSON(object: any): BatchGetUserRoomInfosReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      room_id_types: globalThis.Array.isArray(object?.room_id_types)
        ? object.room_id_types.map((e: any) => roomIDTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserRoomInfosReq>, I>>(base?: I): BatchGetUserRoomInfosReq {
    return BatchGetUserRoomInfosReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserRoomInfosReq>, I>>(object: I): BatchGetUserRoomInfosReq {
    const message = createBaseBatchGetUserRoomInfosReq();
    message.uids = object.uids?.map(e => e) || [];
    message.room_id_types = object.room_id_types?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserRoomInfosRsp(): BatchGetUserRoomInfosRsp {
  return { user_room_infos: [] };
}

export const BatchGetUserRoomInfosRsp: MessageFns<BatchGetUserRoomInfosRsp> = {
  fromJSON(object: any): BatchGetUserRoomInfosRsp {
    return {
      user_room_infos: globalThis.Array.isArray(object?.user_room_infos)
        ? object.user_room_infos.map((e: any) => UserRoomInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserRoomInfosRsp>, I>>(base?: I): BatchGetUserRoomInfosRsp {
    return BatchGetUserRoomInfosRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserRoomInfosRsp>, I>>(object: I): BatchGetUserRoomInfosRsp {
    const message = createBaseBatchGetUserRoomInfosRsp();
    message.user_room_infos = object.user_room_infos?.map(e => UserRoomInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchSaveRecommendRoomConfig(): BatchSaveRecommendRoomConfig {
  return { room_recommend_configs: [], delete_room_recommend_config_ids: [] };
}

export const BatchSaveRecommendRoomConfig: MessageFns<BatchSaveRecommendRoomConfig> = {
  fromJSON(object: any): BatchSaveRecommendRoomConfig {
    return {
      room_recommend_configs: globalThis.Array.isArray(object?.room_recommend_configs)
        ? object.room_recommend_configs.map((e: any) => SaveRoomRecommendConfig.fromJSON(e))
        : [],
      delete_room_recommend_config_ids: globalThis.Array.isArray(object?.delete_room_recommend_config_ids)
        ? object.delete_room_recommend_config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSaveRecommendRoomConfig>, I>>(base?: I): BatchSaveRecommendRoomConfig {
    return BatchSaveRecommendRoomConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSaveRecommendRoomConfig>, I>>(object: I): BatchSaveRecommendRoomConfig {
    const message = createBaseBatchSaveRecommendRoomConfig();
    message.room_recommend_configs =
      object.room_recommend_configs?.map(e => SaveRoomRecommendConfig.fromPartial(e)) || [];
    message.delete_room_recommend_config_ids = object.delete_room_recommend_config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteRoomRecommendConfigReq(): DeleteRoomRecommendConfigReq {
  return { config_ids: [] };
}

export const DeleteRoomRecommendConfigReq: MessageFns<DeleteRoomRecommendConfigReq> = {
  fromJSON(object: any): DeleteRoomRecommendConfigReq {
    return {
      config_ids: globalThis.Array.isArray(object?.config_ids)
        ? object.config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteRoomRecommendConfigReq>, I>>(base?: I): DeleteRoomRecommendConfigReq {
    return DeleteRoomRecommendConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomRecommendConfigReq>, I>>(object: I): DeleteRoomRecommendConfigReq {
    const message = createBaseDeleteRoomRecommendConfigReq();
    message.config_ids = object.config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteRoomRecommendConfigRsp(): DeleteRoomRecommendConfigRsp {
  return {};
}

export const DeleteRoomRecommendConfigRsp: MessageFns<DeleteRoomRecommendConfigRsp> = {
  fromJSON(_: any): DeleteRoomRecommendConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRoomRecommendConfigRsp>, I>>(base?: I): DeleteRoomRecommendConfigRsp {
    return DeleteRoomRecommendConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRoomRecommendConfigRsp>, I>>(_: I): DeleteRoomRecommendConfigRsp {
    const message = createBaseDeleteRoomRecommendConfigRsp();
    return message;
  }
};

function createBaseSaveRoomRecommendConfigReq(): SaveRoomRecommendConfigReq {
  return { config: undefined };
}

export const SaveRoomRecommendConfigReq: MessageFns<SaveRoomRecommendConfigReq> = {
  fromJSON(object: any): SaveRoomRecommendConfigReq {
    return { config: isSet(object.config) ? SaveRoomRecommendConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveRoomRecommendConfigReq>, I>>(base?: I): SaveRoomRecommendConfigReq {
    return SaveRoomRecommendConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveRoomRecommendConfigReq>, I>>(object: I): SaveRoomRecommendConfigReq {
    const message = createBaseSaveRoomRecommendConfigReq();
    message.config =
      object.config !== undefined && object.config !== null
        ? SaveRoomRecommendConfig.fromPartial(object.config)
        : undefined;
    return message;
  }
};

function createBaseSaveRoomRecommendConfig(): SaveRoomRecommendConfig {
  return { config: undefined };
}

export const SaveRoomRecommendConfig: MessageFns<SaveRoomRecommendConfig> = {
  fromJSON(object: any): SaveRoomRecommendConfig {
    return { config: isSet(object.config) ? RoomRecommendConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveRoomRecommendConfig>, I>>(base?: I): SaveRoomRecommendConfig {
    return SaveRoomRecommendConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveRoomRecommendConfig>, I>>(object: I): SaveRoomRecommendConfig {
    const message = createBaseSaveRoomRecommendConfig();
    message.config =
      object.config !== undefined && object.config !== null
        ? RoomRecommendConfig.fromPartial(object.config)
        : undefined;
    return message;
  }
};

function createBaseSaveRoomRecommendConfigRsp(): SaveRoomRecommendConfigRsp {
  return { config_id: 0 };
}

export const SaveRoomRecommendConfigRsp: MessageFns<SaveRoomRecommendConfigRsp> = {
  fromJSON(object: any): SaveRoomRecommendConfigRsp {
    return { config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveRoomRecommendConfigRsp>, I>>(base?: I): SaveRoomRecommendConfigRsp {
    return SaveRoomRecommendConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveRoomRecommendConfigRsp>, I>>(object: I): SaveRoomRecommendConfigRsp {
    const message = createBaseSaveRoomRecommendConfigRsp();
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseGetRoomRecommendConfigByIdReq(): GetRoomRecommendConfigByIdReq {
  return { id: 0 };
}

export const GetRoomRecommendConfigByIdReq: MessageFns<GetRoomRecommendConfigByIdReq> = {
  fromJSON(object: any): GetRoomRecommendConfigByIdReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRoomRecommendConfigByIdReq>, I>>(base?: I): GetRoomRecommendConfigByIdReq {
    return GetRoomRecommendConfigByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRecommendConfigByIdReq>, I>>(
    object: I
  ): GetRoomRecommendConfigByIdReq {
    const message = createBaseGetRoomRecommendConfigByIdReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetRoomRecommendConfigByIdRsp(): GetRoomRecommendConfigByIdRsp {
  return { config: undefined };
}

export const GetRoomRecommendConfigByIdRsp: MessageFns<GetRoomRecommendConfigByIdRsp> = {
  fromJSON(object: any): GetRoomRecommendConfigByIdRsp {
    return { config: isSet(object.config) ? RoomRecommendInfo.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<GetRoomRecommendConfigByIdRsp>, I>>(base?: I): GetRoomRecommendConfigByIdRsp {
    return GetRoomRecommendConfigByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoomRecommendConfigByIdRsp>, I>>(
    object: I
  ): GetRoomRecommendConfigByIdRsp {
    const message = createBaseGetRoomRecommendConfigByIdRsp();
    message.config =
      object.config !== undefined && object.config !== null ? RoomRecommendInfo.fromPartial(object.config) : undefined;
    return message;
  }
};

function createBaseBatchSaveLotteryConfig(): BatchSaveLotteryConfig {
  return { lottery_configs: [], delete_lottery_config_ids: [] };
}

export const BatchSaveLotteryConfig: MessageFns<BatchSaveLotteryConfig> = {
  fromJSON(object: any): BatchSaveLotteryConfig {
    return {
      lottery_configs: globalThis.Array.isArray(object?.lottery_configs)
        ? object.lottery_configs.map((e: any) => SaveLotteryConfig.fromJSON(e))
        : [],
      delete_lottery_config_ids: globalThis.Array.isArray(object?.delete_lottery_config_ids)
        ? object.delete_lottery_config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSaveLotteryConfig>, I>>(base?: I): BatchSaveLotteryConfig {
    return BatchSaveLotteryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSaveLotteryConfig>, I>>(object: I): BatchSaveLotteryConfig {
    const message = createBaseBatchSaveLotteryConfig();
    message.lottery_configs = object.lottery_configs?.map(e => SaveLotteryConfig.fromPartial(e)) || [];
    message.delete_lottery_config_ids = object.delete_lottery_config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteLotteryConfigReq(): DeleteLotteryConfigReq {
  return { lottery_id: 0 };
}

export const DeleteLotteryConfigReq: MessageFns<DeleteLotteryConfigReq> = {
  fromJSON(object: any): DeleteLotteryConfigReq {
    return { lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteLotteryConfigReq>, I>>(base?: I): DeleteLotteryConfigReq {
    return DeleteLotteryConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteLotteryConfigReq>, I>>(object: I): DeleteLotteryConfigReq {
    const message = createBaseDeleteLotteryConfigReq();
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseDeleteLotteryConfigRsp(): DeleteLotteryConfigRsp {
  return {};
}

export const DeleteLotteryConfigRsp: MessageFns<DeleteLotteryConfigRsp> = {
  fromJSON(_: any): DeleteLotteryConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteLotteryConfigRsp>, I>>(base?: I): DeleteLotteryConfigRsp {
    return DeleteLotteryConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteLotteryConfigRsp>, I>>(_: I): DeleteLotteryConfigRsp {
    const message = createBaseDeleteLotteryConfigRsp();
    return message;
  }
};

function createBaseSaveLotteryConfigReq(): SaveLotteryConfigReq {
  return { config: undefined };
}

export const SaveLotteryConfigReq: MessageFns<SaveLotteryConfigReq> = {
  fromJSON(object: any): SaveLotteryConfigReq {
    return { config: isSet(object.config) ? SaveLotteryConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveLotteryConfigReq>, I>>(base?: I): SaveLotteryConfigReq {
    return SaveLotteryConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveLotteryConfigReq>, I>>(object: I): SaveLotteryConfigReq {
    const message = createBaseSaveLotteryConfigReq();
    message.config =
      object.config !== undefined && object.config !== null ? SaveLotteryConfig.fromPartial(object.config) : undefined;
    return message;
  }
};

function createBaseSaveLotteryConfig(): SaveLotteryConfig {
  return { config: undefined, reward_configs: [], delete_reward_config_ids: [], batch_save_task_infos: undefined };
}

export const SaveLotteryConfig: MessageFns<SaveLotteryConfig> = {
  fromJSON(object: any): SaveLotteryConfig {
    return {
      config: isSet(object.config) ? LotteryConfig.fromJSON(object.config) : undefined,
      reward_configs: globalThis.Array.isArray(object?.reward_configs)
        ? object.reward_configs.map((e: any) => LotteryRewardConfig.fromJSON(e))
        : [],
      delete_reward_config_ids: globalThis.Array.isArray(object?.delete_reward_config_ids)
        ? object.delete_reward_config_ids.map((e: any) => globalThis.Number(e))
        : [],
      batch_save_task_infos: isSet(object.batch_save_task_infos)
        ? BatchSaveTaskConfig.fromJSON(object.batch_save_task_infos)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<SaveLotteryConfig>, I>>(base?: I): SaveLotteryConfig {
    return SaveLotteryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveLotteryConfig>, I>>(object: I): SaveLotteryConfig {
    const message = createBaseSaveLotteryConfig();
    message.config =
      object.config !== undefined && object.config !== null ? LotteryConfig.fromPartial(object.config) : undefined;
    message.reward_configs = object.reward_configs?.map(e => LotteryRewardConfig.fromPartial(e)) || [];
    message.delete_reward_config_ids = object.delete_reward_config_ids?.map(e => e) || [];
    message.batch_save_task_infos =
      object.batch_save_task_infos !== undefined && object.batch_save_task_infos !== null
        ? BatchSaveTaskConfig.fromPartial(object.batch_save_task_infos)
        : undefined;
    return message;
  }
};

function createBaseSaveLotteryConfigRsp(): SaveLotteryConfigRsp {
  return { lottery_id: 0 };
}

export const SaveLotteryConfigRsp: MessageFns<SaveLotteryConfigRsp> = {
  fromJSON(object: any): SaveLotteryConfigRsp {
    return { lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveLotteryConfigRsp>, I>>(base?: I): SaveLotteryConfigRsp {
    return SaveLotteryConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveLotteryConfigRsp>, I>>(object: I): SaveLotteryConfigRsp {
    const message = createBaseSaveLotteryConfigRsp();
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseGetLotteryConfigByIdReq(): GetLotteryConfigByIdReq {
  return { id: 0 };
}

export const GetLotteryConfigByIdReq: MessageFns<GetLotteryConfigByIdReq> = {
  fromJSON(object: any): GetLotteryConfigByIdReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetLotteryConfigByIdReq>, I>>(base?: I): GetLotteryConfigByIdReq {
    return GetLotteryConfigByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryConfigByIdReq>, I>>(object: I): GetLotteryConfigByIdReq {
    const message = createBaseGetLotteryConfigByIdReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetLotteryConfigByIdRsp(): GetLotteryConfigByIdRsp {
  return { lottery_config: undefined };
}

export const GetLotteryConfigByIdRsp: MessageFns<GetLotteryConfigByIdRsp> = {
  fromJSON(object: any): GetLotteryConfigByIdRsp {
    return { lottery_config: isSet(object.lottery_config) ? LotteryInfo.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<GetLotteryConfigByIdRsp>, I>>(base?: I): GetLotteryConfigByIdRsp {
    return GetLotteryConfigByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLotteryConfigByIdRsp>, I>>(object: I): GetLotteryConfigByIdRsp {
    const message = createBaseGetLotteryConfigByIdRsp();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryInfo.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseBatchGetGuildInfoReq(): BatchGetGuildInfoReq {
  return { guild_ids: [] };
}

export const BatchGetGuildInfoReq: MessageFns<BatchGetGuildInfoReq> = {
  fromJSON(object: any): BatchGetGuildInfoReq {
    return {
      guild_ids: globalThis.Array.isArray(object?.guild_ids)
        ? object.guild_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGuildInfoReq>, I>>(base?: I): BatchGetGuildInfoReq {
    return BatchGetGuildInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGuildInfoReq>, I>>(object: I): BatchGetGuildInfoReq {
    const message = createBaseBatchGetGuildInfoReq();
    message.guild_ids = object.guild_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetGuildInfoRsp(): BatchGetGuildInfoRsp {
  return { list: [] };
}

export const BatchGetGuildInfoRsp: MessageFns<BatchGetGuildInfoRsp> = {
  fromJSON(object: any): BatchGetGuildInfoRsp {
    return { list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GuildInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetGuildInfoRsp>, I>>(base?: I): BatchGetGuildInfoRsp {
    return BatchGetGuildInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGuildInfoRsp>, I>>(object: I): BatchGetGuildInfoRsp {
    const message = createBaseBatchGetGuildInfoRsp();
    message.list = object.list?.map(e => GuildInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetFamilyInfoReq(): BatchGetFamilyInfoReq {
  return { family_ids: [] };
}

export const BatchGetFamilyInfoReq: MessageFns<BatchGetFamilyInfoReq> = {
  fromJSON(object: any): BatchGetFamilyInfoReq {
    return {
      family_ids: globalThis.Array.isArray(object?.family_ids)
        ? object.family_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetFamilyInfoReq>, I>>(base?: I): BatchGetFamilyInfoReq {
    return BatchGetFamilyInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetFamilyInfoReq>, I>>(object: I): BatchGetFamilyInfoReq {
    const message = createBaseBatchGetFamilyInfoReq();
    message.family_ids = object.family_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetFamilyInfoRsp(): BatchGetFamilyInfoRsp {
  return { list: [] };
}

export const BatchGetFamilyInfoRsp: MessageFns<BatchGetFamilyInfoRsp> = {
  fromJSON(object: any): BatchGetFamilyInfoRsp {
    return { list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => FamilyInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetFamilyInfoRsp>, I>>(base?: I): BatchGetFamilyInfoRsp {
    return BatchGetFamilyInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetFamilyInfoRsp>, I>>(object: I): BatchGetFamilyInfoRsp {
    const message = createBaseBatchGetFamilyInfoRsp();
    message.list = object.list?.map(e => FamilyInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetTaskByIdsReq(): BatchGetTaskByIdsReq {
  return { task_config_id: [] };
}

export const BatchGetTaskByIdsReq: MessageFns<BatchGetTaskByIdsReq> = {
  fromJSON(object: any): BatchGetTaskByIdsReq {
    return {
      task_config_id: globalThis.Array.isArray(object?.task_config_id)
        ? object.task_config_id.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetTaskByIdsReq>, I>>(base?: I): BatchGetTaskByIdsReq {
    return BatchGetTaskByIdsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetTaskByIdsReq>, I>>(object: I): BatchGetTaskByIdsReq {
    const message = createBaseBatchGetTaskByIdsReq();
    message.task_config_id = object.task_config_id?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetTaskByIdsRsp(): BatchGetTaskByIdsRsp {
  return { task_info: [] };
}

export const BatchGetTaskByIdsRsp: MessageFns<BatchGetTaskByIdsRsp> = {
  fromJSON(object: any): BatchGetTaskByIdsRsp {
    return {
      task_info: globalThis.Array.isArray(object?.task_info)
        ? object.task_info.map((e: any) => TaskInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetTaskByIdsRsp>, I>>(base?: I): BatchGetTaskByIdsRsp {
    return BatchGetTaskByIdsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetTaskByIdsRsp>, I>>(object: I): BatchGetTaskByIdsRsp {
    const message = createBaseBatchGetTaskByIdsRsp();
    message.task_info = object.task_info?.map(e => TaskInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetTaskByIdReq(): GetTaskByIdReq {
  return { task_config_id: 0 };
}

export const GetTaskByIdReq: MessageFns<GetTaskByIdReq> = {
  fromJSON(object: any): GetTaskByIdReq {
    return { task_config_id: isSet(object.task_config_id) ? globalThis.Number(object.task_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetTaskByIdReq>, I>>(base?: I): GetTaskByIdReq {
    return GetTaskByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskByIdReq>, I>>(object: I): GetTaskByIdReq {
    const message = createBaseGetTaskByIdReq();
    message.task_config_id = object.task_config_id ?? 0;
    return message;
  }
};

function createBaseGetTaskByIdRsp(): GetTaskByIdRsp {
  return { task_info: undefined };
}

export const GetTaskByIdRsp: MessageFns<GetTaskByIdRsp> = {
  fromJSON(object: any): GetTaskByIdRsp {
    return { task_info: isSet(object.task_info) ? TaskInfo.fromJSON(object.task_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetTaskByIdRsp>, I>>(base?: I): GetTaskByIdRsp {
    return GetTaskByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetTaskByIdRsp>, I>>(object: I): GetTaskByIdRsp {
    const message = createBaseGetTaskByIdRsp();
    message.task_info =
      object.task_info !== undefined && object.task_info !== null ? TaskInfo.fromPartial(object.task_info) : undefined;
    return message;
  }
};

function createBaseSaveActivityReq(): SaveActivityReq {
  return {
    activity: undefined,
    rank_config: undefined,
    batch_save_task_config: undefined,
    batch_save_pk_config: undefined,
    batch_save_lottery_config: undefined,
    batch_save_recommend_room_config: undefined,
    batch_save_presentation_config: undefined,
    batch_save_prize_pool_config: undefined
  };
}

export const SaveActivityReq: MessageFns<SaveActivityReq> = {
  fromJSON(object: any): SaveActivityReq {
    return {
      activity: isSet(object.activity) ? Activity.fromJSON(object.activity) : undefined,
      rank_config: isSet(object.rank_config) ? RankBatchSaveConfig.fromJSON(object.rank_config) : undefined,
      batch_save_task_config: isSet(object.batch_save_task_config)
        ? BatchSaveTaskConfig.fromJSON(object.batch_save_task_config)
        : undefined,
      batch_save_pk_config: isSet(object.batch_save_pk_config)
        ? BatchSavePKConfig.fromJSON(object.batch_save_pk_config)
        : undefined,
      batch_save_lottery_config: isSet(object.batch_save_lottery_config)
        ? BatchSaveLotteryConfig.fromJSON(object.batch_save_lottery_config)
        : undefined,
      batch_save_recommend_room_config: isSet(object.batch_save_recommend_room_config)
        ? BatchSaveRecommendRoomConfig.fromJSON(object.batch_save_recommend_room_config)
        : undefined,
      batch_save_presentation_config: isSet(object.batch_save_presentation_config)
        ? BatchSavePresentationConfig.fromJSON(object.batch_save_presentation_config)
        : undefined,
      batch_save_prize_pool_config: isSet(object.batch_save_prize_pool_config)
        ? BatchSavePrizePoolConfig.fromJSON(object.batch_save_prize_pool_config)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<SaveActivityReq>, I>>(base?: I): SaveActivityReq {
    return SaveActivityReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveActivityReq>, I>>(object: I): SaveActivityReq {
    const message = createBaseSaveActivityReq();
    message.activity =
      object.activity !== undefined && object.activity !== null ? Activity.fromPartial(object.activity) : undefined;
    message.rank_config =
      object.rank_config !== undefined && object.rank_config !== null
        ? RankBatchSaveConfig.fromPartial(object.rank_config)
        : undefined;
    message.batch_save_task_config =
      object.batch_save_task_config !== undefined && object.batch_save_task_config !== null
        ? BatchSaveTaskConfig.fromPartial(object.batch_save_task_config)
        : undefined;
    message.batch_save_pk_config =
      object.batch_save_pk_config !== undefined && object.batch_save_pk_config !== null
        ? BatchSavePKConfig.fromPartial(object.batch_save_pk_config)
        : undefined;
    message.batch_save_lottery_config =
      object.batch_save_lottery_config !== undefined && object.batch_save_lottery_config !== null
        ? BatchSaveLotteryConfig.fromPartial(object.batch_save_lottery_config)
        : undefined;
    message.batch_save_recommend_room_config =
      object.batch_save_recommend_room_config !== undefined && object.batch_save_recommend_room_config !== null
        ? BatchSaveRecommendRoomConfig.fromPartial(object.batch_save_recommend_room_config)
        : undefined;
    message.batch_save_presentation_config =
      object.batch_save_presentation_config !== undefined && object.batch_save_presentation_config !== null
        ? BatchSavePresentationConfig.fromPartial(object.batch_save_presentation_config)
        : undefined;
    message.batch_save_prize_pool_config =
      object.batch_save_prize_pool_config !== undefined && object.batch_save_prize_pool_config !== null
        ? BatchSavePrizePoolConfig.fromPartial(object.batch_save_prize_pool_config)
        : undefined;
    return message;
  }
};

function createBaseSaveActivityRsp(): SaveActivityRsp {
  return {};
}

export const SaveActivityRsp: MessageFns<SaveActivityRsp> = {
  fromJSON(_: any): SaveActivityRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SaveActivityRsp>, I>>(base?: I): SaveActivityRsp {
    return SaveActivityRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveActivityRsp>, I>>(_: I): SaveActivityRsp {
    const message = createBaseSaveActivityRsp();
    return message;
  }
};

function createBaseSaveTaskReq(): SaveTaskReq {
  return { task_config: undefined };
}

export const SaveTaskReq: MessageFns<SaveTaskReq> = {
  fromJSON(object: any): SaveTaskReq {
    return { task_config: isSet(object.task_config) ? SaveTaskConfig.fromJSON(object.task_config) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveTaskReq>, I>>(base?: I): SaveTaskReq {
    return SaveTaskReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveTaskReq>, I>>(object: I): SaveTaskReq {
    const message = createBaseSaveTaskReq();
    message.task_config =
      object.task_config !== undefined && object.task_config !== null
        ? SaveTaskConfig.fromPartial(object.task_config)
        : undefined;
    return message;
  }
};

function createBaseSaveTaskRsp(): SaveTaskRsp {
  return { task_config_id: 0 };
}

export const SaveTaskRsp: MessageFns<SaveTaskRsp> = {
  fromJSON(object: any): SaveTaskRsp {
    return { task_config_id: isSet(object.task_config_id) ? globalThis.Number(object.task_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveTaskRsp>, I>>(base?: I): SaveTaskRsp {
    return SaveTaskRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveTaskRsp>, I>>(object: I): SaveTaskRsp {
    const message = createBaseSaveTaskRsp();
    message.task_config_id = object.task_config_id ?? 0;
    return message;
  }
};

function createBaseDeleteTaskReq(): DeleteTaskReq {
  return { task_config_ids: [] };
}

export const DeleteTaskReq: MessageFns<DeleteTaskReq> = {
  fromJSON(object: any): DeleteTaskReq {
    return {
      task_config_ids: globalThis.Array.isArray(object?.task_config_ids)
        ? object.task_config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteTaskReq>, I>>(base?: I): DeleteTaskReq {
    return DeleteTaskReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTaskReq>, I>>(object: I): DeleteTaskReq {
    const message = createBaseDeleteTaskReq();
    message.task_config_ids = object.task_config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteTaskRsp(): DeleteTaskRsp {
  return {};
}

export const DeleteTaskRsp: MessageFns<DeleteTaskRsp> = {
  fromJSON(_: any): DeleteTaskRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteTaskRsp>, I>>(base?: I): DeleteTaskRsp {
    return DeleteTaskRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteTaskRsp>, I>>(_: I): DeleteTaskRsp {
    const message = createBaseDeleteTaskRsp();
    return message;
  }
};

function createBaseSaveRankReq(): SaveRankReq {
  return { rank_config: undefined };
}

export const SaveRankReq: MessageFns<SaveRankReq> = {
  fromJSON(object: any): SaveRankReq {
    return { rank_config: isSet(object.rank_config) ? RankSaveConfig.fromJSON(object.rank_config) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveRankReq>, I>>(base?: I): SaveRankReq {
    return SaveRankReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveRankReq>, I>>(object: I): SaveRankReq {
    const message = createBaseSaveRankReq();
    message.rank_config =
      object.rank_config !== undefined && object.rank_config !== null
        ? RankSaveConfig.fromPartial(object.rank_config)
        : undefined;
    return message;
  }
};

function createBaseSaveRankRsp(): SaveRankRsp {
  return { rank_config_id: 0 };
}

export const SaveRankRsp: MessageFns<SaveRankRsp> = {
  fromJSON(object: any): SaveRankRsp {
    return { rank_config_id: isSet(object.rank_config_id) ? globalThis.Number(object.rank_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveRankRsp>, I>>(base?: I): SaveRankRsp {
    return SaveRankRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveRankRsp>, I>>(object: I): SaveRankRsp {
    const message = createBaseSaveRankRsp();
    message.rank_config_id = object.rank_config_id ?? 0;
    return message;
  }
};

function createBaseDeleteRankReq(): DeleteRankReq {
  return { rank_config_id: 0 };
}

export const DeleteRankReq: MessageFns<DeleteRankReq> = {
  fromJSON(object: any): DeleteRankReq {
    return { rank_config_id: isSet(object.rank_config_id) ? globalThis.Number(object.rank_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteRankReq>, I>>(base?: I): DeleteRankReq {
    return DeleteRankReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRankReq>, I>>(object: I): DeleteRankReq {
    const message = createBaseDeleteRankReq();
    message.rank_config_id = object.rank_config_id ?? 0;
    return message;
  }
};

function createBaseDeleteRankRsp(): DeleteRankRsp {
  return {};
}

export const DeleteRankRsp: MessageFns<DeleteRankRsp> = {
  fromJSON(_: any): DeleteRankRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRankRsp>, I>>(base?: I): DeleteRankRsp {
    return DeleteRankRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRankRsp>, I>>(_: I): DeleteRankRsp {
    const message = createBaseDeleteRankRsp();
    return message;
  }
};

function createBaseGetRankByIdReq(): GetRankByIdReq {
  return { rank_config_id: 0 };
}

export const GetRankByIdReq: MessageFns<GetRankByIdReq> = {
  fromJSON(object: any): GetRankByIdReq {
    return { rank_config_id: isSet(object.rank_config_id) ? globalThis.Number(object.rank_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRankByIdReq>, I>>(base?: I): GetRankByIdReq {
    return GetRankByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRankByIdReq>, I>>(object: I): GetRankByIdReq {
    const message = createBaseGetRankByIdReq();
    message.rank_config_id = object.rank_config_id ?? 0;
    return message;
  }
};

function createBaseGetRankByIdRsp(): GetRankByIdRsp {
  return { rank_info: undefined };
}

export const GetRankByIdRsp: MessageFns<GetRankByIdRsp> = {
  fromJSON(object: any): GetRankByIdRsp {
    return { rank_info: isSet(object.rank_info) ? RankInfo.fromJSON(object.rank_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetRankByIdRsp>, I>>(base?: I): GetRankByIdRsp {
    return GetRankByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRankByIdRsp>, I>>(object: I): GetRankByIdRsp {
    const message = createBaseGetRankByIdRsp();
    message.rank_info =
      object.rank_info !== undefined && object.rank_info !== null ? RankInfo.fromPartial(object.rank_info) : undefined;
    return message;
  }
};

function createBaseUpdateActivityRsp(): UpdateActivityRsp {
  return {};
}

export const UpdateActivityRsp: MessageFns<UpdateActivityRsp> = {
  fromJSON(_: any): UpdateActivityRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateActivityRsp>, I>>(base?: I): UpdateActivityRsp {
    return UpdateActivityRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateActivityRsp>, I>>(_: I): UpdateActivityRsp {
    const message = createBaseUpdateActivityRsp();
    return message;
  }
};

function createBaseUpdateStatusReq(): UpdateStatusReq {
  return { activity_id: 0, status: '' };
}

export const UpdateStatusReq: MessageFns<UpdateStatusReq> = {
  fromJSON(object: any): UpdateStatusReq {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateStatusReq>, I>>(base?: I): UpdateStatusReq {
    return UpdateStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusReq>, I>>(object: I): UpdateStatusReq {
    const message = createBaseUpdateStatusReq();
    message.activity_id = object.activity_id ?? 0;
    message.status = object.status ?? '';
    return message;
  }
};

function createBaseUpdateStatusRsp(): UpdateStatusRsp {
  return {};
}

export const UpdateStatusRsp: MessageFns<UpdateStatusRsp> = {
  fromJSON(_: any): UpdateStatusRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateStatusRsp>, I>>(base?: I): UpdateStatusRsp {
    return UpdateStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusRsp>, I>>(_: I): UpdateStatusRsp {
    const message = createBaseUpdateStatusRsp();
    return message;
  }
};

function createBasePageQueryReq(): PageQueryReq {
  return { page: undefined, name: '' };
}

export const PageQueryReq: MessageFns<PageQueryReq> = {
  fromJSON(object: any): PageQueryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<PageQueryReq>, I>>(base?: I): PageQueryReq {
    return PageQueryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageQueryReq>, I>>(object: I): PageQueryReq {
    const message = createBasePageQueryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.name = object.name ?? '';
    return message;
  }
};

function createBasePageQueryRsp(): PageQueryRsp {
  return { page: undefined, list: [] };
}

export const PageQueryRsp: MessageFns<PageQueryRsp> = {
  fromJSON(object: any): PageQueryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ActivityInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<PageQueryRsp>, I>>(base?: I): PageQueryRsp {
    return PageQueryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageQueryRsp>, I>>(object: I): PageQueryRsp {
    const message = createBasePageQueryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => ActivityInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRankRewardConfig(): RankRewardConfig {
  return { batch_update_rank_rewards: [], delete_rank_reward_ids: [] };
}

export const RankRewardConfig: MessageFns<RankRewardConfig> = {
  fromJSON(object: any): RankRewardConfig {
    return {
      batch_update_rank_rewards: globalThis.Array.isArray(object?.batch_update_rank_rewards)
        ? object.batch_update_rank_rewards.map((e: any) => BatchUpdateRankReward.fromJSON(e))
        : [],
      delete_rank_reward_ids: globalThis.Array.isArray(object?.delete_rank_reward_ids)
        ? object.delete_rank_reward_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RankRewardConfig>, I>>(base?: I): RankRewardConfig {
    return RankRewardConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankRewardConfig>, I>>(object: I): RankRewardConfig {
    const message = createBaseRankRewardConfig();
    message.batch_update_rank_rewards =
      object.batch_update_rank_rewards?.map(e => BatchUpdateRankReward.fromPartial(e)) || [];
    message.delete_rank_reward_ids = object.delete_rank_reward_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseRankSaveConfig(): RankSaveConfig {
  return { rank_config: undefined, rank_reward_config: undefined, supporter_rank_reward_config: undefined };
}

export const RankSaveConfig: MessageFns<RankSaveConfig> = {
  fromJSON(object: any): RankSaveConfig {
    return {
      rank_config: isSet(object.rank_config) ? RankConfig.fromJSON(object.rank_config) : undefined,
      rank_reward_config: isSet(object.rank_reward_config)
        ? RankRewardConfig.fromJSON(object.rank_reward_config)
        : undefined,
      supporter_rank_reward_config: isSet(object.supporter_rank_reward_config)
        ? RankRewardConfig.fromJSON(object.supporter_rank_reward_config)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<RankSaveConfig>, I>>(base?: I): RankSaveConfig {
    return RankSaveConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankSaveConfig>, I>>(object: I): RankSaveConfig {
    const message = createBaseRankSaveConfig();
    message.rank_config =
      object.rank_config !== undefined && object.rank_config !== null
        ? RankConfig.fromPartial(object.rank_config)
        : undefined;
    message.rank_reward_config =
      object.rank_reward_config !== undefined && object.rank_reward_config !== null
        ? RankRewardConfig.fromPartial(object.rank_reward_config)
        : undefined;
    message.supporter_rank_reward_config =
      object.supporter_rank_reward_config !== undefined && object.supporter_rank_reward_config !== null
        ? RankRewardConfig.fromPartial(object.supporter_rank_reward_config)
        : undefined;
    return message;
  }
};

function createBaseRankBatchSaveConfig(): RankBatchSaveConfig {
  return { rank_config_list: [], delete_rank_ids: [] };
}

export const RankBatchSaveConfig: MessageFns<RankBatchSaveConfig> = {
  fromJSON(object: any): RankBatchSaveConfig {
    return {
      rank_config_list: globalThis.Array.isArray(object?.rank_config_list)
        ? object.rank_config_list.map((e: any) => RankSaveConfig.fromJSON(e))
        : [],
      delete_rank_ids: globalThis.Array.isArray(object?.delete_rank_ids)
        ? object.delete_rank_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RankBatchSaveConfig>, I>>(base?: I): RankBatchSaveConfig {
    return RankBatchSaveConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankBatchSaveConfig>, I>>(object: I): RankBatchSaveConfig {
    const message = createBaseRankBatchSaveConfig();
    message.rank_config_list = object.rank_config_list?.map(e => RankSaveConfig.fromPartial(e)) || [];
    message.delete_rank_ids = object.delete_rank_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListRewardCategoryReq(): ListRewardCategoryReq {
  return {};
}

export const ListRewardCategoryReq: MessageFns<ListRewardCategoryReq> = {
  fromJSON(_: any): ListRewardCategoryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListRewardCategoryReq>, I>>(base?: I): ListRewardCategoryReq {
    return ListRewardCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardCategoryReq>, I>>(_: I): ListRewardCategoryReq {
    const message = createBaseListRewardCategoryReq();
    return message;
  }
};

function createBaseListRewardCategoryRsp(): ListRewardCategoryRsp {
  return { categories: [] };
}

export const ListRewardCategoryRsp: MessageFns<ListRewardCategoryRsp> = {
  fromJSON(object: any): ListRewardCategoryRsp {
    return {
      categories: globalThis.Array.isArray(object?.categories)
        ? object.categories.map((e: any) => RewardCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRewardCategoryRsp>, I>>(base?: I): ListRewardCategoryRsp {
    return ListRewardCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardCategoryRsp>, I>>(object: I): ListRewardCategoryRsp {
    const message = createBaseListRewardCategoryRsp();
    message.categories = object.categories?.map(e => RewardCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListRewardSubCategoryReq(): ListRewardSubCategoryReq {
  return { category_key: '' };
}

export const ListRewardSubCategoryReq: MessageFns<ListRewardSubCategoryReq> = {
  fromJSON(object: any): ListRewardSubCategoryReq {
    return { category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '' };
  },

  create<I extends Exact<DeepPartial<ListRewardSubCategoryReq>, I>>(base?: I): ListRewardSubCategoryReq {
    return ListRewardSubCategoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardSubCategoryReq>, I>>(object: I): ListRewardSubCategoryReq {
    const message = createBaseListRewardSubCategoryReq();
    message.category_key = object.category_key ?? '';
    return message;
  }
};

function createBaseListRewardSubCategoryRsp(): ListRewardSubCategoryRsp {
  return { data: [] };
}

export const ListRewardSubCategoryRsp: MessageFns<ListRewardSubCategoryRsp> = {
  fromJSON(object: any): ListRewardSubCategoryRsp {
    return {
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RewardSubCategory.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRewardSubCategoryRsp>, I>>(base?: I): ListRewardSubCategoryRsp {
    return ListRewardSubCategoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardSubCategoryRsp>, I>>(object: I): ListRewardSubCategoryRsp {
    const message = createBaseListRewardSubCategoryRsp();
    message.data = object.data?.map(e => RewardSubCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSearchRewardItemReq(): SearchRewardItemReq {
  return {
    page: undefined,
    category_key: '',
    item_id: '',
    item_name: '',
    keyword: '',
    is_get_complete_info: false,
    sub_category_ids: [],
    currencys: []
  };
}

export const SearchRewardItemReq: MessageFns<SearchRewardItemReq> = {
  fromJSON(object: any): SearchRewardItemReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : '',
      is_get_complete_info: isSet(object.is_get_complete_info)
        ? globalThis.Boolean(object.is_get_complete_info)
        : false,
      sub_category_ids: globalThis.Array.isArray(object?.sub_category_ids)
        ? object.sub_category_ids.map((e: any) => globalThis.String(e))
        : [],
      currencys: globalThis.Array.isArray(object?.currencys)
        ? object.currencys.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRewardItemReq>, I>>(base?: I): SearchRewardItemReq {
    return SearchRewardItemReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRewardItemReq>, I>>(object: I): SearchRewardItemReq {
    const message = createBaseSearchRewardItemReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_key = object.category_key ?? '';
    message.item_id = object.item_id ?? '';
    message.item_name = object.item_name ?? '';
    message.keyword = object.keyword ?? '';
    message.is_get_complete_info = object.is_get_complete_info ?? false;
    message.sub_category_ids = object.sub_category_ids?.map(e => e) || [];
    message.currencys = object.currencys?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRewardItemRsp(): SearchRewardItemRsp {
  return { page: undefined, items: [] };
}

export const SearchRewardItemRsp: MessageFns<SearchRewardItemRsp> = {
  fromJSON(object: any): SearchRewardItemRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRewardItemRsp>, I>>(base?: I): SearchRewardItemRsp {
    return SearchRewardItemRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRewardItemRsp>, I>>(object: I): SearchRewardItemRsp {
    const message = createBaseSearchRewardItemRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => RewardItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchSaveTaskConfig(): BatchSaveTaskConfig {
  return { task_configs: [], delete_task_ids: [] };
}

export const BatchSaveTaskConfig: MessageFns<BatchSaveTaskConfig> = {
  fromJSON(object: any): BatchSaveTaskConfig {
    return {
      task_configs: globalThis.Array.isArray(object?.task_configs)
        ? object.task_configs.map((e: any) => SaveTaskConfig.fromJSON(e))
        : [],
      delete_task_ids: globalThis.Array.isArray(object?.delete_task_ids)
        ? object.delete_task_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSaveTaskConfig>, I>>(base?: I): BatchSaveTaskConfig {
    return BatchSaveTaskConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSaveTaskConfig>, I>>(object: I): BatchSaveTaskConfig {
    const message = createBaseBatchSaveTaskConfig();
    message.task_configs = object.task_configs?.map(e => SaveTaskConfig.fromPartial(e)) || [];
    message.delete_task_ids = object.delete_task_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseSaveTaskConfig(): SaveTaskConfig {
  return {
    task_config: undefined,
    task_sub_configs: [],
    task_reward_configs: [],
    delete_task_sub_ids: [],
    delete_task_reward_ids: [],
    task_h5_configs: [],
    delete_task_h5_ids: []
  };
}

export const SaveTaskConfig: MessageFns<SaveTaskConfig> = {
  fromJSON(object: any): SaveTaskConfig {
    return {
      task_config: isSet(object.task_config) ? TaskConfig.fromJSON(object.task_config) : undefined,
      task_sub_configs: globalThis.Array.isArray(object?.task_sub_configs)
        ? object.task_sub_configs.map((e: any) => TaskSubConfig.fromJSON(e))
        : [],
      task_reward_configs: globalThis.Array.isArray(object?.task_reward_configs)
        ? object.task_reward_configs.map((e: any) => TaskRewardConfig.fromJSON(e))
        : [],
      delete_task_sub_ids: globalThis.Array.isArray(object?.delete_task_sub_ids)
        ? object.delete_task_sub_ids.map((e: any) => globalThis.Number(e))
        : [],
      delete_task_reward_ids: globalThis.Array.isArray(object?.delete_task_reward_ids)
        ? object.delete_task_reward_ids.map((e: any) => globalThis.Number(e))
        : [],
      task_h5_configs: globalThis.Array.isArray(object?.task_h5_configs)
        ? object.task_h5_configs.map((e: any) => TaskH5Config.fromJSON(e))
        : [],
      delete_task_h5_ids: globalThis.Array.isArray(object?.delete_task_h5_ids)
        ? object.delete_task_h5_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SaveTaskConfig>, I>>(base?: I): SaveTaskConfig {
    return SaveTaskConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveTaskConfig>, I>>(object: I): SaveTaskConfig {
    const message = createBaseSaveTaskConfig();
    message.task_config =
      object.task_config !== undefined && object.task_config !== null
        ? TaskConfig.fromPartial(object.task_config)
        : undefined;
    message.task_sub_configs = object.task_sub_configs?.map(e => TaskSubConfig.fromPartial(e)) || [];
    message.task_reward_configs = object.task_reward_configs?.map(e => TaskRewardConfig.fromPartial(e)) || [];
    message.delete_task_sub_ids = object.delete_task_sub_ids?.map(e => e) || [];
    message.delete_task_reward_ids = object.delete_task_reward_ids?.map(e => e) || [];
    message.task_h5_configs = object.task_h5_configs?.map(e => TaskH5Config.fromPartial(e)) || [];
    message.delete_task_h5_ids = object.delete_task_h5_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListRewardItemByIdReq(): ListRewardItemByIdReq {
  return { category_key: '', item_ids: [], is_get_complete_info: false };
}

export const ListRewardItemByIdReq: MessageFns<ListRewardItemByIdReq> = {
  fromJSON(object: any): ListRewardItemByIdReq {
    return {
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      item_ids: globalThis.Array.isArray(object?.item_ids) ? object.item_ids.map((e: any) => globalThis.String(e)) : [],
      is_get_complete_info: isSet(object.is_get_complete_info) ? globalThis.Boolean(object.is_get_complete_info) : false
    };
  },

  create<I extends Exact<DeepPartial<ListRewardItemByIdReq>, I>>(base?: I): ListRewardItemByIdReq {
    return ListRewardItemByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardItemByIdReq>, I>>(object: I): ListRewardItemByIdReq {
    const message = createBaseListRewardItemByIdReq();
    message.category_key = object.category_key ?? '';
    message.item_ids = object.item_ids?.map(e => e) || [];
    message.is_get_complete_info = object.is_get_complete_info ?? false;
    return message;
  }
};

function createBaseListRewardItemByIdRsp(): ListRewardItemByIdRsp {
  return { items: [] };
}

export const ListRewardItemByIdRsp: MessageFns<ListRewardItemByIdRsp> = {
  fromJSON(object: any): ListRewardItemByIdRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRewardItemByIdRsp>, I>>(base?: I): ListRewardItemByIdRsp {
    return ListRewardItemByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRewardItemByIdRsp>, I>>(object: I): ListRewardItemByIdRsp {
    const message = createBaseListRewardItemByIdRsp();
    message.items = object.items?.map(e => RewardItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSaveSpecialRewardItemReq(): SaveSpecialRewardItemReq {
  return { category_key: '', reward_item: undefined };
}

export const SaveSpecialRewardItemReq: MessageFns<SaveSpecialRewardItemReq> = {
  fromJSON(object: any): SaveSpecialRewardItemReq {
    return {
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      reward_item: isSet(object.reward_item) ? RewardItem.fromJSON(object.reward_item) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SaveSpecialRewardItemReq>, I>>(base?: I): SaveSpecialRewardItemReq {
    return SaveSpecialRewardItemReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveSpecialRewardItemReq>, I>>(object: I): SaveSpecialRewardItemReq {
    const message = createBaseSaveSpecialRewardItemReq();
    message.category_key = object.category_key ?? '';
    message.reward_item =
      object.reward_item !== undefined && object.reward_item !== null
        ? RewardItem.fromPartial(object.reward_item)
        : undefined;
    return message;
  }
};

function createBaseSaveSpecialRewardItemRsp(): SaveSpecialRewardItemRsp {
  return { reward_item_id: 0 };
}

export const SaveSpecialRewardItemRsp: MessageFns<SaveSpecialRewardItemRsp> = {
  fromJSON(object: any): SaveSpecialRewardItemRsp {
    return { reward_item_id: isSet(object.reward_item_id) ? globalThis.Number(object.reward_item_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveSpecialRewardItemRsp>, I>>(base?: I): SaveSpecialRewardItemRsp {
    return SaveSpecialRewardItemRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveSpecialRewardItemRsp>, I>>(object: I): SaveSpecialRewardItemRsp {
    const message = createBaseSaveSpecialRewardItemRsp();
    message.reward_item_id = object.reward_item_id ?? 0;
    return message;
  }
};

function createBaseDeleteSpecialRewardItemReq(): DeleteSpecialRewardItemReq {
  return { category_key: '', reward_item_ids: [] };
}

export const DeleteSpecialRewardItemReq: MessageFns<DeleteSpecialRewardItemReq> = {
  fromJSON(object: any): DeleteSpecialRewardItemReq {
    return {
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      reward_item_ids: globalThis.Array.isArray(object?.reward_item_ids)
        ? object.reward_item_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteSpecialRewardItemReq>, I>>(base?: I): DeleteSpecialRewardItemReq {
    return DeleteSpecialRewardItemReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteSpecialRewardItemReq>, I>>(object: I): DeleteSpecialRewardItemReq {
    const message = createBaseDeleteSpecialRewardItemReq();
    message.category_key = object.category_key ?? '';
    message.reward_item_ids = object.reward_item_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteSpecialRewardItemRsp(): DeleteSpecialRewardItemRsp {
  return {};
}

export const DeleteSpecialRewardItemRsp: MessageFns<DeleteSpecialRewardItemRsp> = {
  fromJSON(_: any): DeleteSpecialRewardItemRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteSpecialRewardItemRsp>, I>>(base?: I): DeleteSpecialRewardItemRsp {
    return DeleteSpecialRewardItemRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteSpecialRewardItemRsp>, I>>(_: I): DeleteSpecialRewardItemRsp {
    const message = createBaseDeleteSpecialRewardItemRsp();
    return message;
  }
};

function createBaseGetActivityByApplicationCodeReq(): GetActivityByApplicationCodeReq {
  return { application_code: '', reltated_id: 0, type: 0 };
}

export const GetActivityByApplicationCodeReq: MessageFns<GetActivityByApplicationCodeReq> = {
  fromJSON(object: any): GetActivityByApplicationCodeReq {
    return {
      application_code: isSet(object.application_code) ? globalThis.String(object.application_code) : '',
      reltated_id: isSet(object.reltated_id) ? globalThis.Number(object.reltated_id) : 0,
      type: isSet(object.type) ? searchActivityTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityByApplicationCodeReq>, I>>(base?: I): GetActivityByApplicationCodeReq {
    return GetActivityByApplicationCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByApplicationCodeReq>, I>>(
    object: I
  ): GetActivityByApplicationCodeReq {
    const message = createBaseGetActivityByApplicationCodeReq();
    message.application_code = object.application_code ?? '';
    message.reltated_id = object.reltated_id ?? 0;
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseGetActivityByApplicationCodeRsp(): GetActivityByApplicationCodeRsp {
  return { activity_info: undefined };
}

export const GetActivityByApplicationCodeRsp: MessageFns<GetActivityByApplicationCodeRsp> = {
  fromJSON(object: any): GetActivityByApplicationCodeRsp {
    return { activity_info: isSet(object.activity_info) ? ActivityInfo.fromJSON(object.activity_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetActivityByApplicationCodeRsp>, I>>(base?: I): GetActivityByApplicationCodeRsp {
    return GetActivityByApplicationCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByApplicationCodeRsp>, I>>(
    object: I
  ): GetActivityByApplicationCodeRsp {
    const message = createBaseGetActivityByApplicationCodeRsp();
    message.activity_info =
      object.activity_info !== undefined && object.activity_info !== null
        ? ActivityInfo.fromPartial(object.activity_info)
        : undefined;
    return message;
  }
};

function createBaseBWListConfig(): BWListConfig {
  return { id: '', category: '', name: '', i18n_names: {}, black_list: [], white_list: [], type: 0 };
}

export const BWListConfig: MessageFns<BWListConfig> = {
  fromJSON(object: any): BWListConfig {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      black_list: globalThis.Array.isArray(object?.black_list)
        ? object.black_list.map((e: any) => globalThis.String(e))
        : [],
      white_list: globalThis.Array.isArray(object?.white_list)
        ? object.white_list.map((e: any) => globalThis.String(e))
        : [],
      type: isSet(object.type) ? bWListTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<BWListConfig>, I>>(base?: I): BWListConfig {
    return BWListConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BWListConfig>, I>>(object: I): BWListConfig {
    const message = createBaseBWListConfig();
    message.id = object.id ?? '';
    message.category = object.category ?? '';
    message.name = object.name ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.black_list = object.black_list?.map(e => e) || [];
    message.white_list = object.white_list?.map(e => e) || [];
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseBWListConfig_I18nNamesEntry(): BWListConfig_I18nNamesEntry {
  return { key: '', value: '' };
}

export const BWListConfig_I18nNamesEntry: MessageFns<BWListConfig_I18nNamesEntry> = {
  fromJSON(object: any): BWListConfig_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<BWListConfig_I18nNamesEntry>, I>>(base?: I): BWListConfig_I18nNamesEntry {
    return BWListConfig_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BWListConfig_I18nNamesEntry>, I>>(object: I): BWListConfig_I18nNamesEntry {
    const message = createBaseBWListConfig_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetBWListConfigReq(): GetBWListConfigReq {
  return { category: '' };
}

export const GetBWListConfigReq: MessageFns<GetBWListConfigReq> = {
  fromJSON(object: any): GetBWListConfigReq {
    return { category: isSet(object.category) ? globalThis.String(object.category) : '' };
  },

  create<I extends Exact<DeepPartial<GetBWListConfigReq>, I>>(base?: I): GetBWListConfigReq {
    return GetBWListConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBWListConfigReq>, I>>(object: I): GetBWListConfigReq {
    const message = createBaseGetBWListConfigReq();
    message.category = object.category ?? '';
    return message;
  }
};

function createBaseGetBWListConfigRsp(): GetBWListConfigRsp {
  return { configs: [] };
}

export const GetBWListConfigRsp: MessageFns<GetBWListConfigRsp> = {
  fromJSON(object: any): GetBWListConfigRsp {
    return {
      configs: globalThis.Array.isArray(object?.configs) ? object.configs.map((e: any) => BWListConfig.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetBWListConfigRsp>, I>>(base?: I): GetBWListConfigRsp {
    return GetBWListConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBWListConfigRsp>, I>>(object: I): GetBWListConfigRsp {
    const message = createBaseGetBWListConfigRsp();
    message.configs = object.configs?.map(e => BWListConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLabelConfig(): LabelConfig {
  return { id: '', i18n_names: {} };
}

export const LabelConfig: MessageFns<LabelConfig> = {
  fromJSON(object: any): LabelConfig {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      i18n_names: isObject(object.i18n_names)
        ? Object.entries(object.i18n_names).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<LabelConfig>, I>>(base?: I): LabelConfig {
    return LabelConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LabelConfig>, I>>(object: I): LabelConfig {
    const message = createBaseLabelConfig();
    message.id = object.id ?? '';
    message.i18n_names = Object.entries(object.i18n_names ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseLabelConfig_I18nNamesEntry(): LabelConfig_I18nNamesEntry {
  return { key: '', value: '' };
}

export const LabelConfig_I18nNamesEntry: MessageFns<LabelConfig_I18nNamesEntry> = {
  fromJSON(object: any): LabelConfig_I18nNamesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<LabelConfig_I18nNamesEntry>, I>>(base?: I): LabelConfig_I18nNamesEntry {
    return LabelConfig_I18nNamesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LabelConfig_I18nNamesEntry>, I>>(object: I): LabelConfig_I18nNamesEntry {
    const message = createBaseLabelConfig_I18nNamesEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListLabelConfigReq(): ListLabelConfigReq {
  return { category: 0 };
}

export const ListLabelConfigReq: MessageFns<ListLabelConfigReq> = {
  fromJSON(object: any): ListLabelConfigReq {
    return { category: isSet(object.category) ? labelCategoryFromJSON(object.category) : 0 };
  },

  create<I extends Exact<DeepPartial<ListLabelConfigReq>, I>>(base?: I): ListLabelConfigReq {
    return ListLabelConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLabelConfigReq>, I>>(object: I): ListLabelConfigReq {
    const message = createBaseListLabelConfigReq();
    message.category = object.category ?? 0;
    return message;
  }
};

function createBaseListLabelConfigRsp(): ListLabelConfigRsp {
  return { configs: [] };
}

export const ListLabelConfigRsp: MessageFns<ListLabelConfigRsp> = {
  fromJSON(object: any): ListLabelConfigRsp {
    return {
      configs: globalThis.Array.isArray(object?.configs) ? object.configs.map((e: any) => LabelConfig.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListLabelConfigRsp>, I>>(base?: I): ListLabelConfigRsp {
    return ListLabelConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLabelConfigRsp>, I>>(object: I): ListLabelConfigRsp {
    const message = createBaseListLabelConfigRsp();
    message.configs = object.configs?.map(e => LabelConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSavePKConfigReq(): SavePKConfigReq {
  return { pk_config: undefined };
}

export const SavePKConfigReq: MessageFns<SavePKConfigReq> = {
  fromJSON(object: any): SavePKConfigReq {
    return { pk_config: isSet(object.pk_config) ? SavePKConfig.fromJSON(object.pk_config) : undefined };
  },

  create<I extends Exact<DeepPartial<SavePKConfigReq>, I>>(base?: I): SavePKConfigReq {
    return SavePKConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePKConfigReq>, I>>(object: I): SavePKConfigReq {
    const message = createBaseSavePKConfigReq();
    message.pk_config =
      object.pk_config !== undefined && object.pk_config !== null
        ? SavePKConfig.fromPartial(object.pk_config)
        : undefined;
    return message;
  }
};

function createBaseSavePKConfig(): SavePKConfig {
  return { pk_config: undefined, details: [], delete_pk_detail_ids: [] };
}

export const SavePKConfig: MessageFns<SavePKConfig> = {
  fromJSON(object: any): SavePKConfig {
    return {
      pk_config: isSet(object.pk_config) ? PKConfig.fromJSON(object.pk_config) : undefined,
      details: globalThis.Array.isArray(object?.details) ? object.details.map((e: any) => PKDetail.fromJSON(e)) : [],
      delete_pk_detail_ids: globalThis.Array.isArray(object?.delete_pk_detail_ids)
        ? object.delete_pk_detail_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SavePKConfig>, I>>(base?: I): SavePKConfig {
    return SavePKConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePKConfig>, I>>(object: I): SavePKConfig {
    const message = createBaseSavePKConfig();
    message.pk_config =
      object.pk_config !== undefined && object.pk_config !== null ? PKConfig.fromPartial(object.pk_config) : undefined;
    message.details = object.details?.map(e => PKDetail.fromPartial(e)) || [];
    message.delete_pk_detail_ids = object.delete_pk_detail_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseSavePKConfigRsp(): SavePKConfigRsp {
  return { pk_config_id: 0 };
}

export const SavePKConfigRsp: MessageFns<SavePKConfigRsp> = {
  fromJSON(object: any): SavePKConfigRsp {
    return { pk_config_id: isSet(object.pk_config_id) ? globalThis.Number(object.pk_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SavePKConfigRsp>, I>>(base?: I): SavePKConfigRsp {
    return SavePKConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePKConfigRsp>, I>>(object: I): SavePKConfigRsp {
    const message = createBaseSavePKConfigRsp();
    message.pk_config_id = object.pk_config_id ?? 0;
    return message;
  }
};

function createBaseGetPKConfigByIdReq(): GetPKConfigByIdReq {
  return { pk_config_id: 0 };
}

export const GetPKConfigByIdReq: MessageFns<GetPKConfigByIdReq> = {
  fromJSON(object: any): GetPKConfigByIdReq {
    return { pk_config_id: isSet(object.pk_config_id) ? globalThis.Number(object.pk_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetPKConfigByIdReq>, I>>(base?: I): GetPKConfigByIdReq {
    return GetPKConfigByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPKConfigByIdReq>, I>>(object: I): GetPKConfigByIdReq {
    const message = createBaseGetPKConfigByIdReq();
    message.pk_config_id = object.pk_config_id ?? 0;
    return message;
  }
};

function createBaseGetPKConfigByIdRsp(): GetPKConfigByIdRsp {
  return { pk_info: undefined };
}

export const GetPKConfigByIdRsp: MessageFns<GetPKConfigByIdRsp> = {
  fromJSON(object: any): GetPKConfigByIdRsp {
    return { pk_info: isSet(object.pk_info) ? PKInfo.fromJSON(object.pk_info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetPKConfigByIdRsp>, I>>(base?: I): GetPKConfigByIdRsp {
    return GetPKConfigByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPKConfigByIdRsp>, I>>(object: I): GetPKConfigByIdRsp {
    const message = createBaseGetPKConfigByIdRsp();
    message.pk_info =
      object.pk_info !== undefined && object.pk_info !== null ? PKInfo.fromPartial(object.pk_info) : undefined;
    return message;
  }
};

function createBaseDeletePKConfigReq(): DeletePKConfigReq {
  return { pk_config_id: 0 };
}

export const DeletePKConfigReq: MessageFns<DeletePKConfigReq> = {
  fromJSON(object: any): DeletePKConfigReq {
    return { pk_config_id: isSet(object.pk_config_id) ? globalThis.Number(object.pk_config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeletePKConfigReq>, I>>(base?: I): DeletePKConfigReq {
    return DeletePKConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePKConfigReq>, I>>(object: I): DeletePKConfigReq {
    const message = createBaseDeletePKConfigReq();
    message.pk_config_id = object.pk_config_id ?? 0;
    return message;
  }
};

function createBaseDeletePKConfigRsp(): DeletePKConfigRsp {
  return {};
}

export const DeletePKConfigRsp: MessageFns<DeletePKConfigRsp> = {
  fromJSON(_: any): DeletePKConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePKConfigRsp>, I>>(base?: I): DeletePKConfigRsp {
    return DeletePKConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePKConfigRsp>, I>>(_: I): DeletePKConfigRsp {
    const message = createBaseDeletePKConfigRsp();
    return message;
  }
};

function createBaseBatchSavePKConfig(): BatchSavePKConfig {
  return { pk_configs: [], delete_pk_config_ids: [] };
}

export const BatchSavePKConfig: MessageFns<BatchSavePKConfig> = {
  fromJSON(object: any): BatchSavePKConfig {
    return {
      pk_configs: globalThis.Array.isArray(object?.pk_configs)
        ? object.pk_configs.map((e: any) => SavePKConfig.fromJSON(e))
        : [],
      delete_pk_config_ids: globalThis.Array.isArray(object?.delete_pk_config_ids)
        ? object.delete_pk_config_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchSavePKConfig>, I>>(base?: I): BatchSavePKConfig {
    return BatchSavePKConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchSavePKConfig>, I>>(object: I): BatchSavePKConfig {
    const message = createBaseBatchSavePKConfig();
    message.pk_configs = object.pk_configs?.map(e => SavePKConfig.fromPartial(e)) || [];
    message.delete_pk_config_ids = object.delete_pk_config_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetPromotionListByRankIdReq(): GetPromotionListByRankIdReq {
  return { rank_id: 0 };
}

export const GetPromotionListByRankIdReq: MessageFns<GetPromotionListByRankIdReq> = {
  fromJSON(object: any): GetPromotionListByRankIdReq {
    return { rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetPromotionListByRankIdReq>, I>>(base?: I): GetPromotionListByRankIdReq {
    return GetPromotionListByRankIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPromotionListByRankIdReq>, I>>(object: I): GetPromotionListByRankIdReq {
    const message = createBaseGetPromotionListByRankIdReq();
    message.rank_id = object.rank_id ?? 0;
    return message;
  }
};

function createBaseGetPromotionListByRankIdRsp(): GetPromotionListByRankIdRsp {
  return { entities: [] };
}

export const GetPromotionListByRankIdRsp: MessageFns<GetPromotionListByRankIdRsp> = {
  fromJSON(object: any): GetPromotionListByRankIdRsp {
    return {
      entities: globalThis.Array.isArray(object?.entities)
        ? object.entities.map((e: any) => PromotionEntity.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetPromotionListByRankIdRsp>, I>>(base?: I): GetPromotionListByRankIdRsp {
    return GetPromotionListByRankIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPromotionListByRankIdRsp>, I>>(object: I): GetPromotionListByRankIdRsp {
    const message = createBaseGetPromotionListByRankIdRsp();
    message.entities = object.entities?.map(e => PromotionEntity.fromPartial(e)) || [];
    return message;
  }
};

function createBasePromotionEntity(): PromotionEntity {
  return { member_id: '', nick: '', avatar: '', rank: 0, score: 0 };
}

export const PromotionEntity: MessageFns<PromotionEntity> = {
  fromJSON(object: any): PromotionEntity {
    return {
      member_id: isSet(object.member_id) ? globalThis.String(object.member_id) : '',
      nick: isSet(object.nick) ? globalThis.String(object.nick) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0
    };
  },

  create<I extends Exact<DeepPartial<PromotionEntity>, I>>(base?: I): PromotionEntity {
    return PromotionEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PromotionEntity>, I>>(object: I): PromotionEntity {
    const message = createBasePromotionEntity();
    message.member_id = object.member_id ?? '';
    message.nick = object.nick ?? '';
    message.avatar = object.avatar ?? '';
    message.rank = object.rank ?? 0;
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseCopyActivityByApplicationCodeReq(): CopyActivityByApplicationCodeReq {
  return { from_application_code: '', to_application_code: '', to_template: false, to_cou: '' };
}

export const CopyActivityByApplicationCodeReq: MessageFns<CopyActivityByApplicationCodeReq> = {
  fromJSON(object: any): CopyActivityByApplicationCodeReq {
    return {
      from_application_code: isSet(object.from_application_code) ? globalThis.String(object.from_application_code) : '',
      to_application_code: isSet(object.to_application_code) ? globalThis.String(object.to_application_code) : '',
      to_template: isSet(object.to_template) ? globalThis.Boolean(object.to_template) : false,
      to_cou: isSet(object.to_cou) ? globalThis.String(object.to_cou) : ''
    };
  },

  create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeReq>, I>>(
    base?: I
  ): CopyActivityByApplicationCodeReq {
    return CopyActivityByApplicationCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeReq>, I>>(
    object: I
  ): CopyActivityByApplicationCodeReq {
    const message = createBaseCopyActivityByApplicationCodeReq();
    message.from_application_code = object.from_application_code ?? '';
    message.to_application_code = object.to_application_code ?? '';
    message.to_template = object.to_template ?? false;
    message.to_cou = object.to_cou ?? '';
    return message;
  }
};

function createBaseCopyActivityByApplicationCodeRsp(): CopyActivityByApplicationCodeRsp {
  return {
    activity_id: 0,
    rank_id_map: {},
    rank_reward_id_map: {},
    rank_reward_detail_id_map: {},
    supporter_rank_reward_id_map: {},
    supporter_rank_reward_detail_id_map: {},
    task_id_map: {},
    task_sub_id_map: {},
    task_reward_id_map: {},
    task_h5_config_id_map: {},
    pk_id_map: {},
    pk_config_detail_id_map: {},
    lottery_id_map: {},
    lottery_reward_id_map: {},
    recommend_id_map: {},
    presentation_id_map: {},
    prize_pool_id_map: {}
  };
}

export const CopyActivityByApplicationCodeRsp: MessageFns<CopyActivityByApplicationCodeRsp> = {
  fromJSON(object: any): CopyActivityByApplicationCodeRsp {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      rank_id_map: isObject(object.rank_id_map)
        ? Object.entries(object.rank_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      rank_reward_id_map: isObject(object.rank_reward_id_map)
        ? Object.entries(object.rank_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      rank_reward_detail_id_map: isObject(object.rank_reward_detail_id_map)
        ? Object.entries(object.rank_reward_detail_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      supporter_rank_reward_id_map: isObject(object.supporter_rank_reward_id_map)
        ? Object.entries(object.supporter_rank_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      supporter_rank_reward_detail_id_map: isObject(object.supporter_rank_reward_detail_id_map)
        ? Object.entries(object.supporter_rank_reward_detail_id_map).reduce<{ [key: number]: number }>(
            (acc, [key, value]) => {
              acc[globalThis.Number(key)] = Number(value);
              return acc;
            },
            {}
          )
        : {},
      task_id_map: isObject(object.task_id_map)
        ? Object.entries(object.task_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_sub_id_map: isObject(object.task_sub_id_map)
        ? Object.entries(object.task_sub_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_reward_id_map: isObject(object.task_reward_id_map)
        ? Object.entries(object.task_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_h5_config_id_map: isObject(object.task_h5_config_id_map)
        ? Object.entries(object.task_h5_config_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      pk_id_map: isObject(object.pk_id_map)
        ? Object.entries(object.pk_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      pk_config_detail_id_map: isObject(object.pk_config_detail_id_map)
        ? Object.entries(object.pk_config_detail_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      lottery_id_map: isObject(object.lottery_id_map)
        ? Object.entries(object.lottery_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      lottery_reward_id_map: isObject(object.lottery_reward_id_map)
        ? Object.entries(object.lottery_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      recommend_id_map: isObject(object.recommend_id_map)
        ? Object.entries(object.recommend_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      presentation_id_map: isObject(object.presentation_id_map)
        ? Object.entries(object.presentation_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      prize_pool_id_map: isObject(object.prize_pool_id_map)
        ? Object.entries(object.prize_pool_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp>, I>>(
    base?: I
  ): CopyActivityByApplicationCodeRsp {
    return CopyActivityByApplicationCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp>, I>>(
    object: I
  ): CopyActivityByApplicationCodeRsp {
    const message = createBaseCopyActivityByApplicationCodeRsp();
    message.activity_id = object.activity_id ?? 0;
    message.rank_id_map = Object.entries(object.rank_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.rank_reward_id_map = Object.entries(object.rank_reward_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.rank_reward_detail_id_map = Object.entries(object.rank_reward_detail_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.supporter_rank_reward_id_map = Object.entries(object.supporter_rank_reward_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.supporter_rank_reward_detail_id_map = Object.entries(
      object.supporter_rank_reward_detail_id_map ?? {}
    ).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.task_id_map = Object.entries(object.task_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_sub_id_map = Object.entries(object.task_sub_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_reward_id_map = Object.entries(object.task_reward_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_h5_config_id_map = Object.entries(object.task_h5_config_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.pk_id_map = Object.entries(object.pk_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.pk_config_detail_id_map = Object.entries(object.pk_config_detail_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.lottery_id_map = Object.entries(object.lottery_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.lottery_reward_id_map = Object.entries(object.lottery_reward_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.recommend_id_map = Object.entries(object.recommend_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.presentation_id_map = Object.entries(object.presentation_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.prize_pool_id_map = Object.entries(object.prize_pool_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCopyActivityByApplicationCodeRsp_RankIdMapEntry(): CopyActivityByApplicationCodeRsp_RankIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_RankIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_RankIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_RankIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_RankIdMapEntry {
      return CopyActivityByApplicationCodeRsp_RankIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_RankIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_RankIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_RankRewardIdMapEntry(): CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry {
      return CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_RankRewardIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_RankRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry(): CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      return CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_RankRewardDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry(): CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      return CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_SupporterRankRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry(): CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      return CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_TaskIdMapEntry(): CopyActivityByApplicationCodeRsp_TaskIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_TaskIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_TaskIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_TaskIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_TaskIdMapEntry {
      return CopyActivityByApplicationCodeRsp_TaskIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_TaskIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_TaskIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_TaskSubIdMapEntry(): CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry {
      return CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_TaskSubIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_TaskSubIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry(): CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry {
      return CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_TaskRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry(): CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      return CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_TaskH5ConfigIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_PkIdMapEntry(): CopyActivityByApplicationCodeRsp_PkIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_PkIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_PkIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_PkIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PkIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_PkIdMapEntry {
      return CopyActivityByApplicationCodeRsp_PkIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PkIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_PkIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_PkIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry(): CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      return CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_PkConfigDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_LotteryIdMapEntry(): CopyActivityByApplicationCodeRsp_LotteryIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_LotteryIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_LotteryIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_LotteryIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_LotteryIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_LotteryIdMapEntry {
      return CopyActivityByApplicationCodeRsp_LotteryIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_LotteryIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_LotteryIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_LotteryIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry(): CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry {
      return CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_LotteryRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_RecommendIdMapEntry(): CopyActivityByApplicationCodeRsp_RecommendIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_RecommendIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_RecommendIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_RecommendIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RecommendIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_RecommendIdMapEntry {
      return CopyActivityByApplicationCodeRsp_RecommendIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_RecommendIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_RecommendIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_RecommendIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_PresentationIdMapEntry(): CopyActivityByApplicationCodeRsp_PresentationIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_PresentationIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_PresentationIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_PresentationIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PresentationIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_PresentationIdMapEntry {
      return CopyActivityByApplicationCodeRsp_PresentationIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PresentationIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_PresentationIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_PresentationIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry(): CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry: MessageFns<CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry>, I>>(
      base?: I
    ): CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry {
      return CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry>, I>>(
      object: I
    ): CopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry {
      const message = createBaseCopyActivityByApplicationCodeRsp_PrizePoolIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeReq(): CopyActivityToSubAnmByApplicationCodeReq {
  return { from_application_code: '', to_application_code: '', to_template: false, to_cou: '', to_sub_anm: '' };
}

export const CopyActivityToSubAnmByApplicationCodeReq: MessageFns<CopyActivityToSubAnmByApplicationCodeReq> = {
  fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeReq {
    return {
      from_application_code: isSet(object.from_application_code) ? globalThis.String(object.from_application_code) : '',
      to_application_code: isSet(object.to_application_code) ? globalThis.String(object.to_application_code) : '',
      to_template: isSet(object.to_template) ? globalThis.Boolean(object.to_template) : false,
      to_cou: isSet(object.to_cou) ? globalThis.String(object.to_cou) : '',
      to_sub_anm: isSet(object.to_sub_anm) ? globalThis.String(object.to_sub_anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeReq>, I>>(
    base?: I
  ): CopyActivityToSubAnmByApplicationCodeReq {
    return CopyActivityToSubAnmByApplicationCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeReq>, I>>(
    object: I
  ): CopyActivityToSubAnmByApplicationCodeReq {
    const message = createBaseCopyActivityToSubAnmByApplicationCodeReq();
    message.from_application_code = object.from_application_code ?? '';
    message.to_application_code = object.to_application_code ?? '';
    message.to_template = object.to_template ?? false;
    message.to_cou = object.to_cou ?? '';
    message.to_sub_anm = object.to_sub_anm ?? '';
    return message;
  }
};

function createBaseCopyActivityToSubAnmByApplicationCodeRsp(): CopyActivityToSubAnmByApplicationCodeRsp {
  return {
    activity_id: 0,
    rank_id_map: {},
    rank_reward_id_map: {},
    rank_reward_detail_id_map: {},
    supporter_rank_reward_id_map: {},
    supporter_rank_reward_detail_id_map: {},
    task_id_map: {},
    task_sub_id_map: {},
    task_reward_id_map: {},
    task_h5_config_id_map: {},
    pk_id_map: {},
    pk_config_detail_id_map: {},
    lottery_id_map: {},
    lottery_reward_id_map: {},
    recommend_id_map: {},
    presentation_id_map: {},
    prize_pool_id_map: {}
  };
}

export const CopyActivityToSubAnmByApplicationCodeRsp: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp> = {
  fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      rank_id_map: isObject(object.rank_id_map)
        ? Object.entries(object.rank_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      rank_reward_id_map: isObject(object.rank_reward_id_map)
        ? Object.entries(object.rank_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      rank_reward_detail_id_map: isObject(object.rank_reward_detail_id_map)
        ? Object.entries(object.rank_reward_detail_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      supporter_rank_reward_id_map: isObject(object.supporter_rank_reward_id_map)
        ? Object.entries(object.supporter_rank_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      supporter_rank_reward_detail_id_map: isObject(object.supporter_rank_reward_detail_id_map)
        ? Object.entries(object.supporter_rank_reward_detail_id_map).reduce<{ [key: number]: number }>(
            (acc, [key, value]) => {
              acc[globalThis.Number(key)] = Number(value);
              return acc;
            },
            {}
          )
        : {},
      task_id_map: isObject(object.task_id_map)
        ? Object.entries(object.task_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_sub_id_map: isObject(object.task_sub_id_map)
        ? Object.entries(object.task_sub_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_reward_id_map: isObject(object.task_reward_id_map)
        ? Object.entries(object.task_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      task_h5_config_id_map: isObject(object.task_h5_config_id_map)
        ? Object.entries(object.task_h5_config_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      pk_id_map: isObject(object.pk_id_map)
        ? Object.entries(object.pk_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      pk_config_detail_id_map: isObject(object.pk_config_detail_id_map)
        ? Object.entries(object.pk_config_detail_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      lottery_id_map: isObject(object.lottery_id_map)
        ? Object.entries(object.lottery_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      lottery_reward_id_map: isObject(object.lottery_reward_id_map)
        ? Object.entries(object.lottery_reward_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      recommend_id_map: isObject(object.recommend_id_map)
        ? Object.entries(object.recommend_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      presentation_id_map: isObject(object.presentation_id_map)
        ? Object.entries(object.presentation_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      prize_pool_id_map: isObject(object.prize_pool_id_map)
        ? Object.entries(object.prize_pool_id_map).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp>, I>>(
    base?: I
  ): CopyActivityToSubAnmByApplicationCodeRsp {
    return CopyActivityToSubAnmByApplicationCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp>, I>>(
    object: I
  ): CopyActivityToSubAnmByApplicationCodeRsp {
    const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp();
    message.activity_id = object.activity_id ?? 0;
    message.rank_id_map = Object.entries(object.rank_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.rank_reward_id_map = Object.entries(object.rank_reward_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.rank_reward_detail_id_map = Object.entries(object.rank_reward_detail_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.supporter_rank_reward_id_map = Object.entries(object.supporter_rank_reward_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.supporter_rank_reward_detail_id_map = Object.entries(
      object.supporter_rank_reward_detail_id_map ?? {}
    ).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.task_id_map = Object.entries(object.task_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_sub_id_map = Object.entries(object.task_sub_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_reward_id_map = Object.entries(object.task_reward_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_h5_config_id_map = Object.entries(object.task_h5_config_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.pk_id_map = Object.entries(object.pk_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.pk_config_detail_id_map = Object.entries(object.pk_config_detail_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.lottery_id_map = Object.entries(object.lottery_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.lottery_reward_id_map = Object.entries(object.lottery_reward_id_map ?? {}).reduce<{
      [key: number]: number;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    message.recommend_id_map = Object.entries(object.recommend_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.presentation_id_map = Object.entries(object.presentation_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.prize_pool_id_map = Object.entries(object.prize_pool_id_map ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_RankRewardDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<
      I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry>, I>
    >(object: I): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<
      I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry>, I>
    >(base?: I): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry.fromPartial(
        base ?? ({} as any)
      );
    },
    fromPartial<
      I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry>, I>
    >(object: I): CopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_SupporterRankRewardDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskSubIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_TaskH5ConfigIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_PkIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_PkConfigDetailIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_LotteryIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_LotteryRewardIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_RecommendIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_PresentationIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseCopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry(): CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry {
  return { key: 0, value: 0 };
}

export const CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry: MessageFns<CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry> =
  {
    fromJSON(object: any): CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry {
      return {
        key: isSet(object.key) ? globalThis.Number(object.key) : 0,
        value: isSet(object.value) ? globalThis.Number(object.value) : 0
      };
    },

    create<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry>, I>>(
      base?: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry {
      return CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry>, I>>(
      object: I
    ): CopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry {
      const message = createBaseCopyActivityToSubAnmByApplicationCodeRsp_PrizePoolIdMapEntry();
      message.key = object.key ?? 0;
      message.value = object.value ?? 0;
      return message;
    }
  };

function createBaseGetActivityByRelatedIDReq(): GetActivityByRelatedIDReq {
  return { related_id: 0, type: 0 };
}

export const GetActivityByRelatedIDReq: MessageFns<GetActivityByRelatedIDReq> = {
  fromJSON(object: any): GetActivityByRelatedIDReq {
    return {
      related_id: isSet(object.related_id) ? globalThis.Number(object.related_id) : 0,
      type: isSet(object.type) ? searchActivityTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityByRelatedIDReq>, I>>(base?: I): GetActivityByRelatedIDReq {
    return GetActivityByRelatedIDReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByRelatedIDReq>, I>>(object: I): GetActivityByRelatedIDReq {
    const message = createBaseGetActivityByRelatedIDReq();
    message.related_id = object.related_id ?? 0;
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseGetActivityByRelatedIDRsp(): GetActivityByRelatedIDRsp {
  return { activity: undefined };
}

export const GetActivityByRelatedIDRsp: MessageFns<GetActivityByRelatedIDRsp> = {
  fromJSON(object: any): GetActivityByRelatedIDRsp {
    return { activity: isSet(object.activity) ? Activity.fromJSON(object.activity) : undefined };
  },

  create<I extends Exact<DeepPartial<GetActivityByRelatedIDRsp>, I>>(base?: I): GetActivityByRelatedIDRsp {
    return GetActivityByRelatedIDRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByRelatedIDRsp>, I>>(object: I): GetActivityByRelatedIDRsp {
    const message = createBaseGetActivityByRelatedIDRsp();
    message.activity =
      object.activity !== undefined && object.activity !== null ? Activity.fromPartial(object.activity) : undefined;
    return message;
  }
};

/** smicro:spath=gitit.cc/social/components-service/social-activity/biz/activity/handlermgr */
export type ActivityMgrDefinition = typeof ActivityMgrDefinition;
export const ActivityMgrDefinition = {
  name: 'ActivityMgr',
  fullName: 'comm.mgr.activity.ActivityMgr',
  methods: {
    /** 新增、修改活动配置，ID不为空为修改 */
    saveActivity: {
      name: 'SaveActivity',
      requestType: SaveActivityReq,
      requestStream: false,
      responseType: SaveActivityRsp,
      responseStream: false,
      options: {}
    },
    /** 新增、修改榜单配置，ID不为空为修改 */
    saveRank: {
      name: 'SaveRank',
      requestType: SaveRankReq,
      requestStream: false,
      responseType: SaveRankRsp,
      responseStream: false,
      options: {}
    },
    /** 删除榜单配置 */
    deleteRank: {
      name: 'DeleteRank',
      requestType: DeleteRankReq,
      requestStream: false,
      responseType: DeleteRankRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取榜单 */
    getRankById: {
      name: 'GetRankById',
      requestType: GetRankByIdReq,
      requestStream: false,
      responseType: GetRankByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 新增、修改任务配置，ID不为空为修改 */
    saveTask: {
      name: 'SaveTask',
      requestType: SaveTaskReq,
      requestStream: false,
      responseType: SaveTaskRsp,
      responseStream: false,
      options: {}
    },
    /** 删除任务配置 */
    deleteTask: {
      name: 'DeleteTask',
      requestType: DeleteTaskReq,
      requestStream: false,
      responseType: DeleteTaskRsp,
      responseStream: false,
      options: {}
    },
    /** 获取任务配置 */
    getTaskById: {
      name: 'GetTaskById',
      requestType: GetTaskByIdReq,
      requestStream: false,
      responseType: GetTaskByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取任务配置 */
    batchGetTaskByIds: {
      name: 'BatchGetTaskByIds',
      requestType: BatchGetTaskByIdsReq,
      requestStream: false,
      responseType: BatchGetTaskByIdsRsp,
      responseStream: false,
      options: {}
    },
    /** 更新配置状态（上下架、删除） */
    updateStatus: {
      name: 'UpdateStatus',
      requestType: UpdateStatusReq,
      requestStream: false,
      responseType: UpdateStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 分页查询 */
    pageQuery: {
      name: 'PageQuery',
      requestType: PageQueryReq,
      requestStream: false,
      responseType: PageQueryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取奖励分类列表, 由业务决定有多少种奖励类型及其信息. */
    listRewardCategory: {
      name: 'ListRewardCategory',
      requestType: ListRewardCategoryReq,
      requestStream: false,
      responseType: ListRewardCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取子分类 */
    listRewardSubCategory: {
      name: 'ListRewardSubCategory',
      requestType: ListRewardSubCategoryReq,
      requestStream: false,
      responseType: ListRewardSubCategoryRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索指定奖励分类的奖励配置项, 实际上也是交由业务去实现, 活动中台只是中转这些请求. */
    searchRewardItem: {
      name: 'SearchRewardItem',
      requestType: SearchRewardItemReq,
      requestStream: false,
      responseType: SearchRewardItemRsp,
      responseStream: false,
      options: {}
    },
    /** 批量根据ID获取奖励配置项信息 */
    listRewardItemById: {
      name: 'ListRewardItemById',
      requestType: ListRewardItemByIdReq,
      requestStream: false,
      responseType: ListRewardItemByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 保存特殊奖励配置项 */
    saveSpecialRewardItem: {
      name: 'SaveSpecialRewardItem',
      requestType: SaveSpecialRewardItemReq,
      requestStream: false,
      responseType: SaveSpecialRewardItemRsp,
      responseStream: false,
      options: {}
    },
    /** 删除特殊奖励配置项 */
    deleteSpecialRewardItem: {
      name: 'DeleteSpecialRewardItem',
      requestType: DeleteSpecialRewardItemReq,
      requestStream: false,
      responseType: DeleteSpecialRewardItemRsp,
      responseStream: false,
      options: {}
    },
    /** 根据活动ID获取活动配置 */
    getActivityByApplicationCode: {
      name: 'GetActivityByApplicationCode',
      requestType: GetActivityByApplicationCodeReq,
      requestStream: false,
      responseType: GetActivityByApplicationCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取活动配置 */
    batchGetActivity: {
      name: 'BatchGetActivity',
      requestType: BatchGetActivityReq,
      requestStream: false,
      responseType: BatchGetActivityRsp,
      responseStream: false,
      options: {}
    },
    /** 获取黑白名单配置 */
    getBWListConfig: {
      name: 'GetBWListConfig',
      requestType: GetBWListConfigReq,
      requestStream: false,
      responseType: GetBWListConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取pk配置 */
    getPKConfigById: {
      name: 'GetPKConfigById',
      requestType: GetPKConfigByIdReq,
      requestStream: false,
      responseType: GetPKConfigByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 保存pk配置 */
    savePKConfig: {
      name: 'SavePKConfig',
      requestType: SavePKConfigReq,
      requestStream: false,
      responseType: SavePKConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 删除pk配置 */
    deletePKConfig: {
      name: 'DeletePKConfig',
      requestType: DeletePKConfigReq,
      requestStream: false,
      responseType: DeletePKConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 根据榜单 id获取晋级列表 */
    getPromotionListByRankId: {
      name: 'GetPromotionListByRankId',
      requestType: GetPromotionListByRankIdReq,
      requestStream: false,
      responseType: GetPromotionListByRankIdRsp,
      responseStream: false,
      options: {}
    },
    /** 获取标签列表 */
    listLabelConfig: {
      name: 'ListLabelConfig',
      requestType: ListLabelConfigReq,
      requestStream: false,
      responseType: ListLabelConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取公会信息 */
    batchGetGuildInfo: {
      name: 'BatchGetGuildInfo',
      requestType: BatchGetGuildInfoReq,
      requestStream: false,
      responseType: BatchGetGuildInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取家族信息 */
    batchGetFamilyInfo: {
      name: 'BatchGetFamilyInfo',
      requestType: BatchGetFamilyInfoReq,
      requestStream: false,
      responseType: BatchGetFamilyInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取抽奖配置 */
    getLotteryConfigById: {
      name: 'GetLotteryConfigById',
      requestType: GetLotteryConfigByIdReq,
      requestStream: false,
      responseType: GetLotteryConfigByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 保存抽奖配置 */
    saveLotteryConfig: {
      name: 'SaveLotteryConfig',
      requestType: SaveLotteryConfigReq,
      requestStream: false,
      responseType: SaveLotteryConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 删除抽奖配置 */
    deleteLotteryConfig: {
      name: 'DeleteLotteryConfig',
      requestType: DeleteLotteryConfigReq,
      requestStream: false,
      responseType: DeleteLotteryConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 同业务复制活动 */
    copyActivityByApplicationCode: {
      name: 'CopyActivityByApplicationCode',
      requestType: CopyActivityByApplicationCodeReq,
      requestStream: false,
      responseType: CopyActivityByApplicationCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 跨业务复制活动 */
    copyActivityToSubAnmByApplicationCode: {
      name: 'CopyActivityToSubAnmByApplicationCode',
      requestType: CopyActivityToSubAnmByApplicationCodeReq,
      requestStream: false,
      responseType: CopyActivityToSubAnmByApplicationCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取房间抽奖配置 */
    getRoomRecommendConfigById: {
      name: 'GetRoomRecommendConfigById',
      requestType: GetRoomRecommendConfigByIdReq,
      requestStream: false,
      responseType: GetRoomRecommendConfigByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 删除房间抽奖配置 */
    deleteRoomRecommendConfig: {
      name: 'DeleteRoomRecommendConfig',
      requestType: DeleteRoomRecommendConfigReq,
      requestStream: false,
      responseType: DeleteRoomRecommendConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 保存房间推荐信息 */
    saveRoomRecommendConfig: {
      name: 'SaveRoomRecommendConfig',
      requestType: SaveRoomRecommendConfigReq,
      requestStream: false,
      responseType: SaveRoomRecommendConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取用户房间id */
    batchGetUserRoomInfos: {
      name: 'BatchGetUserRoomInfos',
      requestType: BatchGetUserRoomInfosReq,
      requestStream: false,
      responseType: BatchGetUserRoomInfosRsp,
      responseStream: false,
      options: {}
    },
    /** 分页获取黑白名单用户列表 */
    listBWListItem: {
      name: 'ListBWListItem',
      requestType: ListBWListItemReq,
      requestStream: false,
      responseType: ListBWListItemRsp,
      responseStream: false,
      options: {}
    },
    /** 根据白名单id批量获取用户房间id列表 */
    batchGetUserRoomInfosByWhiteListId: {
      name: 'BatchGetUserRoomInfosByWhiteListId',
      requestType: BatchGetUserRoomInfosByWhiteListIdReq,
      requestStream: false,
      responseType: BatchGetUserRoomInfosByWhiteListIdRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取用户信息 */
    batchGetUserInfos: {
      name: 'BatchGetUserInfos',
      requestType: BatchGetUserInfosReq,
      requestStream: false,
      responseType: BatchGetUserInfosRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取展示配置 */
    getPresentationConfigById: {
      name: 'GetPresentationConfigById',
      requestType: GetPresentationConfigByIdReq,
      requestStream: false,
      responseType: GetPresentationConfigByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 保存展示配置 */
    savePresentationConfig: {
      name: 'SavePresentationConfig',
      requestType: SavePresentationConfigReq,
      requestStream: false,
      responseType: SavePresentationConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 删除展示配置 */
    deletePresentationConfig: {
      name: 'DeletePresentationConfig',
      requestType: DeletePresentationConfigReq,
      requestStream: false,
      responseType: DeletePresentationConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 新增、修改奖池配置，ID不为空为修改 */
    savePrizePool: {
      name: 'SavePrizePool',
      requestType: SavePrizePoolReq,
      requestStream: false,
      responseType: SavePrizePoolRsp,
      responseStream: false,
      options: {}
    },
    /** 删除奖池配置 */
    deletePrizePool: {
      name: 'DeletePrizePool',
      requestType: DeletePrizePoolReq,
      requestStream: false,
      responseType: DeletePrizePoolRsp,
      responseStream: false,
      options: {}
    },
    /** 获取奖池配置 */
    getPrizePoolById: {
      name: 'GetPrizePoolById',
      requestType: GetPrizePoolByIdReq,
      requestStream: false,
      responseType: GetPrizePoolByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取奖池配置 */
    batchGetPrizePoolByIds: {
      name: 'BatchGetPrizePoolByIds',
      requestType: BatchGetPrizePoolByIdsReq,
      requestStream: false,
      responseType: BatchGetPrizePoolByIdsRsp,
      responseStream: false,
      options: {}
    },
    /** 获取自定义filters，直接全量返回 */
    listCustomFilters: {
      name: 'ListCustomFilters',
      requestType: ListCustomFiltersReq,
      requestStream: false,
      responseType: ListCustomFiltersRsp,
      responseStream: false,
      options: {}
    },
    /** 根据榜单ID，任务id，抽奖id获取活动配置 */
    getActivityByRelatedID: {
      name: 'GetActivityByRelatedID',
      requestType: GetActivityByRelatedIDReq,
      requestStream: false,
      responseType: GetActivityByRelatedIDRsp,
      responseStream: false,
      options: {}
    },
    /** 批量增加榜单分数 */
    batchAddRankScore: {
      name: 'BatchAddRankScore',
      requestType: BatchAddRankScoreReq,
      requestStream: false,
      responseType: BatchAddRankScoreRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
