// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/statistic.proto

/* eslint-disable */
import { Currency, currencyFromJSON } from '../../api/adapter/model';
import { Page } from '../../api/common/common';
import {
  Activity,
  ActivityStatus,
  activityStatusFromJSON,
  LotteryConfig,
  PKConfig,
  RankConfig,
  SortType,
  sortTypeFromJSON,
  TaskConfig
} from './common';

export const protobufPackage = 'comm.mgr.activity';

export interface GetActivityStatLotteryRsp {
  list: ActivityStatLotteryItem[];
}

export interface ActivityStatLotteryItem {
  lottery_id: number;
  /** 抽奖次数用户数 */
  completed_uv: number;
  /** 抽奖次数总数 */
  completed_sum: number;
  /** 抽奖用户数 */
  lottery_uv: number;
  /** 抽奖总数 */
  lottery_sum: number;
  /** 付费抽奖信息 */
  paid_stats: ActivityStatLotteryItemPaidStat[];
}

export interface ActivityStatLotteryItemPaidStat {
  currency: Currency;
  total: number;
}

export interface GetActivityStatLotteryReq {
  activity_id: number;
  lottery_id: number;
}

export interface GetActivityStatTaskReq {
  activity_id: number;
  task_id: number;
}

export interface GetActivityStatTaskRsp {
  list: ActivityStatTaskItem[];
}

export interface ActivityStatTaskItem {
  task_id: number;
  /** 完成用户数 */
  completed_uv: number;
  /** 领取用户数 */
  reward_uv: number;
  /** 领取总数 */
  reward_sum: number;
}

export interface GetActivityStatPKReq {
  pk_id: number;
  /** 下面是过滤条件 */
  member_id: string;
  /** 房间id */
  special_room_id: number;
}

export interface GetActivityStatPKRsp {
  list: ActivityStatPKItem[];
}

export interface ActivityStatPKItem {
  /** pk_id */
  pk_id: number;
  /** 轮次, 从 1 开始 */
  round: number;
  players: ActivityStatPKPlayerItem[];
  /** pk 开始时间 */
  start_time: number;
  /** pk 结束时间 */
  end_time: number;
}

export interface ActivityStatPKPlayerItem {
  /** 公会 or 家族id， 根据 pk_type 决定 */
  member_id: string;
  /** 房间id */
  special_room_id: number;
  /** 分数 */
  score: number;
}

export interface ListActivityStatReq {
  page: Page | undefined;
  /** 活动名称 */
  name: string;
  /** 活动开始时间,时间戳 秒 */
  start_time: number;
  /** 活动结束时间,时间戳 秒 */
  end_time: number;
  /** 活动状态 */
  status: ActivityStatus;
  /** 创建人 */
  create_by: string;
  /** 活动id */
  activity_id: number;
  /** 活动code */
  activity_code: string;
}

export interface ListActivityStatRsp {
  page: Page | undefined;
  list: ActivityStatItem[];
}

export interface ActivityStatItem {
  /** 活动唯一code, 用于运营和开发沟通. */
  activity_code: string;
  /** 活动配置 */
  activity: Activity | undefined;
  /** 榜单配置 */
  rank_list: ActivityStatRankInfo[];
  /** 参与用户总数 */
  uv: number;
  /** 活动状态 */
  status: ActivityStatus;
  /** pk配置 */
  pk_list: ActivityStatPkInfo[];
  /** 任务配置 */
  task_list: ActivityStatTaskInfo[];
  /** 抽奖配置 */
  lottery_list: ActivityStatLotteryInfo[];
}

export interface ActivityStatTaskInfo {
  task_config: TaskConfig | undefined;
}

export interface ActivityStatLotteryInfo {
  lottery_config: LotteryConfig | undefined;
}

export interface ActivityStatPkInfo {
  pk_config: PKConfig | undefined;
}

export interface ActivityStatRankInfo {
  rank_config: RankConfig | undefined;
  /** 周期秒级时间戳，如果总榜，则返回空 */
  cycle_list: number[];
  /** 榜单关联的奖池榜单id */
  prize_pool_id: number;
  /** 奖励配置项key, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_category_key: string;
  /** 奖励配置项ID, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_item_id: string;
}

export interface ListActivityStatRankReq {
  page: Page | undefined;
  /** 榜单成员，string 通用点，可以为uid, room_id, guild_id, family_id */
  member: string;
  sort_type: SortType;
  rank_id: number;
  /** 周期秒级时间戳, ActivityStatRankInfo 返回的值 */
  cycle_ts: number;
  /** 子榜用 */
  rank_member: string;
}

export interface ListActivityStatRankRsp {
  page: Page | undefined;
  list: ActivityStatRankItem[];
}

export interface ActivityStatRankItem {
  score: number;
  rank: number;
  entities: ActivityStatRankItemEntity[];
  /** 子榜 */
  sub_rank_member: string;
}

export interface ActivityStatRankItemEntity {
  /** uid or room_id or guild_id or family_id */
  id: string;
  show_id: string;
  /** json 用户则用户信息，房间则房间信息，家族则家族信息 */
  extend: string;
  /** 瓜分得到的奖励数量 */
  reward_num: number;
  /** room_id/guild_id ownerid 的showid */
  owner_uid_showid: string;
}

function createBaseGetActivityStatLotteryRsp(): GetActivityStatLotteryRsp {
  return { list: [] };
}

export const GetActivityStatLotteryRsp: MessageFns<GetActivityStatLotteryRsp> = {
  fromJSON(object: any): GetActivityStatLotteryRsp {
    return {
      list: globalThis.Array.isArray(object?.list)
        ? object.list.map((e: any) => ActivityStatLotteryItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatLotteryRsp>, I>>(base?: I): GetActivityStatLotteryRsp {
    return GetActivityStatLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatLotteryRsp>, I>>(object: I): GetActivityStatLotteryRsp {
    const message = createBaseGetActivityStatLotteryRsp();
    message.list = object.list?.map(e => ActivityStatLotteryItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatLotteryItem(): ActivityStatLotteryItem {
  return { lottery_id: 0, completed_uv: 0, completed_sum: 0, lottery_uv: 0, lottery_sum: 0, paid_stats: [] };
}

export const ActivityStatLotteryItem: MessageFns<ActivityStatLotteryItem> = {
  fromJSON(object: any): ActivityStatLotteryItem {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      completed_uv: isSet(object.completed_uv) ? globalThis.Number(object.completed_uv) : 0,
      completed_sum: isSet(object.completed_sum) ? globalThis.Number(object.completed_sum) : 0,
      lottery_uv: isSet(object.lottery_uv) ? globalThis.Number(object.lottery_uv) : 0,
      lottery_sum: isSet(object.lottery_sum) ? globalThis.Number(object.lottery_sum) : 0,
      paid_stats: globalThis.Array.isArray(object?.paid_stats)
        ? object.paid_stats.map((e: any) => ActivityStatLotteryItemPaidStat.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatLotteryItem>, I>>(base?: I): ActivityStatLotteryItem {
    return ActivityStatLotteryItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatLotteryItem>, I>>(object: I): ActivityStatLotteryItem {
    const message = createBaseActivityStatLotteryItem();
    message.lottery_id = object.lottery_id ?? 0;
    message.completed_uv = object.completed_uv ?? 0;
    message.completed_sum = object.completed_sum ?? 0;
    message.lottery_uv = object.lottery_uv ?? 0;
    message.lottery_sum = object.lottery_sum ?? 0;
    message.paid_stats = object.paid_stats?.map(e => ActivityStatLotteryItemPaidStat.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatLotteryItemPaidStat(): ActivityStatLotteryItemPaidStat {
  return { currency: 0, total: 0 };
}

export const ActivityStatLotteryItemPaidStat: MessageFns<ActivityStatLotteryItemPaidStat> = {
  fromJSON(object: any): ActivityStatLotteryItemPaidStat {
    return {
      currency: isSet(object.currency) ? currencyFromJSON(object.currency) : 0,
      total: isSet(object.total) ? globalThis.Number(object.total) : 0
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatLotteryItemPaidStat>, I>>(base?: I): ActivityStatLotteryItemPaidStat {
    return ActivityStatLotteryItemPaidStat.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatLotteryItemPaidStat>, I>>(
    object: I
  ): ActivityStatLotteryItemPaidStat {
    const message = createBaseActivityStatLotteryItemPaidStat();
    message.currency = object.currency ?? 0;
    message.total = object.total ?? 0;
    return message;
  }
};

function createBaseGetActivityStatLotteryReq(): GetActivityStatLotteryReq {
  return { activity_id: 0, lottery_id: 0 };
}

export const GetActivityStatLotteryReq: MessageFns<GetActivityStatLotteryReq> = {
  fromJSON(object: any): GetActivityStatLotteryReq {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatLotteryReq>, I>>(base?: I): GetActivityStatLotteryReq {
    return GetActivityStatLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatLotteryReq>, I>>(object: I): GetActivityStatLotteryReq {
    const message = createBaseGetActivityStatLotteryReq();
    message.activity_id = object.activity_id ?? 0;
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseGetActivityStatTaskReq(): GetActivityStatTaskReq {
  return { activity_id: 0, task_id: 0 };
}

export const GetActivityStatTaskReq: MessageFns<GetActivityStatTaskReq> = {
  fromJSON(object: any): GetActivityStatTaskReq {
    return {
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatTaskReq>, I>>(base?: I): GetActivityStatTaskReq {
    return GetActivityStatTaskReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatTaskReq>, I>>(object: I): GetActivityStatTaskReq {
    const message = createBaseGetActivityStatTaskReq();
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    return message;
  }
};

function createBaseGetActivityStatTaskRsp(): GetActivityStatTaskRsp {
  return { list: [] };
}

export const GetActivityStatTaskRsp: MessageFns<GetActivityStatTaskRsp> = {
  fromJSON(object: any): GetActivityStatTaskRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ActivityStatTaskItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatTaskRsp>, I>>(base?: I): GetActivityStatTaskRsp {
    return GetActivityStatTaskRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatTaskRsp>, I>>(object: I): GetActivityStatTaskRsp {
    const message = createBaseGetActivityStatTaskRsp();
    message.list = object.list?.map(e => ActivityStatTaskItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatTaskItem(): ActivityStatTaskItem {
  return { task_id: 0, completed_uv: 0, reward_uv: 0, reward_sum: 0 };
}

export const ActivityStatTaskItem: MessageFns<ActivityStatTaskItem> = {
  fromJSON(object: any): ActivityStatTaskItem {
    return {
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      completed_uv: isSet(object.completed_uv) ? globalThis.Number(object.completed_uv) : 0,
      reward_uv: isSet(object.reward_uv) ? globalThis.Number(object.reward_uv) : 0,
      reward_sum: isSet(object.reward_sum) ? globalThis.Number(object.reward_sum) : 0
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatTaskItem>, I>>(base?: I): ActivityStatTaskItem {
    return ActivityStatTaskItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatTaskItem>, I>>(object: I): ActivityStatTaskItem {
    const message = createBaseActivityStatTaskItem();
    message.task_id = object.task_id ?? 0;
    message.completed_uv = object.completed_uv ?? 0;
    message.reward_uv = object.reward_uv ?? 0;
    message.reward_sum = object.reward_sum ?? 0;
    return message;
  }
};

function createBaseGetActivityStatPKReq(): GetActivityStatPKReq {
  return { pk_id: 0, member_id: '', special_room_id: 0 };
}

export const GetActivityStatPKReq: MessageFns<GetActivityStatPKReq> = {
  fromJSON(object: any): GetActivityStatPKReq {
    return {
      pk_id: isSet(object.pk_id) ? globalThis.Number(object.pk_id) : 0,
      member_id: isSet(object.member_id) ? globalThis.String(object.member_id) : '',
      special_room_id: isSet(object.special_room_id) ? globalThis.Number(object.special_room_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatPKReq>, I>>(base?: I): GetActivityStatPKReq {
    return GetActivityStatPKReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatPKReq>, I>>(object: I): GetActivityStatPKReq {
    const message = createBaseGetActivityStatPKReq();
    message.pk_id = object.pk_id ?? 0;
    message.member_id = object.member_id ?? '';
    message.special_room_id = object.special_room_id ?? 0;
    return message;
  }
};

function createBaseGetActivityStatPKRsp(): GetActivityStatPKRsp {
  return { list: [] };
}

export const GetActivityStatPKRsp: MessageFns<GetActivityStatPKRsp> = {
  fromJSON(object: any): GetActivityStatPKRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ActivityStatPKItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetActivityStatPKRsp>, I>>(base?: I): GetActivityStatPKRsp {
    return GetActivityStatPKRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityStatPKRsp>, I>>(object: I): GetActivityStatPKRsp {
    const message = createBaseGetActivityStatPKRsp();
    message.list = object.list?.map(e => ActivityStatPKItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatPKItem(): ActivityStatPKItem {
  return { pk_id: 0, round: 0, players: [], start_time: 0, end_time: 0 };
}

export const ActivityStatPKItem: MessageFns<ActivityStatPKItem> = {
  fromJSON(object: any): ActivityStatPKItem {
    return {
      pk_id: isSet(object.pk_id) ? globalThis.Number(object.pk_id) : 0,
      round: isSet(object.round) ? globalThis.Number(object.round) : 0,
      players: globalThis.Array.isArray(object?.players)
        ? object.players.map((e: any) => ActivityStatPKPlayerItem.fromJSON(e))
        : [],
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatPKItem>, I>>(base?: I): ActivityStatPKItem {
    return ActivityStatPKItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatPKItem>, I>>(object: I): ActivityStatPKItem {
    const message = createBaseActivityStatPKItem();
    message.pk_id = object.pk_id ?? 0;
    message.round = object.round ?? 0;
    message.players = object.players?.map(e => ActivityStatPKPlayerItem.fromPartial(e)) || [];
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBaseActivityStatPKPlayerItem(): ActivityStatPKPlayerItem {
  return { member_id: '', special_room_id: 0, score: 0 };
}

export const ActivityStatPKPlayerItem: MessageFns<ActivityStatPKPlayerItem> = {
  fromJSON(object: any): ActivityStatPKPlayerItem {
    return {
      member_id: isSet(object.member_id) ? globalThis.String(object.member_id) : '',
      special_room_id: isSet(object.special_room_id) ? globalThis.Number(object.special_room_id) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatPKPlayerItem>, I>>(base?: I): ActivityStatPKPlayerItem {
    return ActivityStatPKPlayerItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatPKPlayerItem>, I>>(object: I): ActivityStatPKPlayerItem {
    const message = createBaseActivityStatPKPlayerItem();
    message.member_id = object.member_id ?? '';
    message.special_room_id = object.special_room_id ?? 0;
    message.score = object.score ?? 0;
    return message;
  }
};

function createBaseListActivityStatReq(): ListActivityStatReq {
  return {
    page: undefined,
    name: '',
    start_time: 0,
    end_time: 0,
    status: 0,
    create_by: '',
    activity_id: 0,
    activity_code: ''
  };
}

export const ListActivityStatReq: MessageFns<ListActivityStatReq> = {
  fromJSON(object: any): ListActivityStatReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      status: isSet(object.status) ? activityStatusFromJSON(object.status) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListActivityStatReq>, I>>(base?: I): ListActivityStatReq {
    return ListActivityStatReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListActivityStatReq>, I>>(object: I): ListActivityStatReq {
    const message = createBaseListActivityStatReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.name = object.name ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.status = object.status ?? 0;
    message.create_by = object.create_by ?? '';
    message.activity_id = object.activity_id ?? 0;
    message.activity_code = object.activity_code ?? '';
    return message;
  }
};

function createBaseListActivityStatRsp(): ListActivityStatRsp {
  return { page: undefined, list: [] };
}

export const ListActivityStatRsp: MessageFns<ListActivityStatRsp> = {
  fromJSON(object: any): ListActivityStatRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ActivityStatItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListActivityStatRsp>, I>>(base?: I): ListActivityStatRsp {
    return ListActivityStatRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListActivityStatRsp>, I>>(object: I): ListActivityStatRsp {
    const message = createBaseListActivityStatRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => ActivityStatItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatItem(): ActivityStatItem {
  return {
    activity_code: '',
    activity: undefined,
    rank_list: [],
    uv: 0,
    status: 0,
    pk_list: [],
    task_list: [],
    lottery_list: []
  };
}

export const ActivityStatItem: MessageFns<ActivityStatItem> = {
  fromJSON(object: any): ActivityStatItem {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      activity: isSet(object.activity) ? Activity.fromJSON(object.activity) : undefined,
      rank_list: globalThis.Array.isArray(object?.rank_list)
        ? object.rank_list.map((e: any) => ActivityStatRankInfo.fromJSON(e))
        : [],
      uv: isSet(object.uv) ? globalThis.Number(object.uv) : 0,
      status: isSet(object.status) ? activityStatusFromJSON(object.status) : 0,
      pk_list: globalThis.Array.isArray(object?.pk_list)
        ? object.pk_list.map((e: any) => ActivityStatPkInfo.fromJSON(e))
        : [],
      task_list: globalThis.Array.isArray(object?.task_list)
        ? object.task_list.map((e: any) => ActivityStatTaskInfo.fromJSON(e))
        : [],
      lottery_list: globalThis.Array.isArray(object?.lottery_list)
        ? object.lottery_list.map((e: any) => ActivityStatLotteryInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatItem>, I>>(base?: I): ActivityStatItem {
    return ActivityStatItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatItem>, I>>(object: I): ActivityStatItem {
    const message = createBaseActivityStatItem();
    message.activity_code = object.activity_code ?? '';
    message.activity =
      object.activity !== undefined && object.activity !== null ? Activity.fromPartial(object.activity) : undefined;
    message.rank_list = object.rank_list?.map(e => ActivityStatRankInfo.fromPartial(e)) || [];
    message.uv = object.uv ?? 0;
    message.status = object.status ?? 0;
    message.pk_list = object.pk_list?.map(e => ActivityStatPkInfo.fromPartial(e)) || [];
    message.task_list = object.task_list?.map(e => ActivityStatTaskInfo.fromPartial(e)) || [];
    message.lottery_list = object.lottery_list?.map(e => ActivityStatLotteryInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatTaskInfo(): ActivityStatTaskInfo {
  return { task_config: undefined };
}

export const ActivityStatTaskInfo: MessageFns<ActivityStatTaskInfo> = {
  fromJSON(object: any): ActivityStatTaskInfo {
    return { task_config: isSet(object.task_config) ? TaskConfig.fromJSON(object.task_config) : undefined };
  },

  create<I extends Exact<DeepPartial<ActivityStatTaskInfo>, I>>(base?: I): ActivityStatTaskInfo {
    return ActivityStatTaskInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatTaskInfo>, I>>(object: I): ActivityStatTaskInfo {
    const message = createBaseActivityStatTaskInfo();
    message.task_config =
      object.task_config !== undefined && object.task_config !== null
        ? TaskConfig.fromPartial(object.task_config)
        : undefined;
    return message;
  }
};

function createBaseActivityStatLotteryInfo(): ActivityStatLotteryInfo {
  return { lottery_config: undefined };
}

export const ActivityStatLotteryInfo: MessageFns<ActivityStatLotteryInfo> = {
  fromJSON(object: any): ActivityStatLotteryInfo {
    return { lottery_config: isSet(object.lottery_config) ? LotteryConfig.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<ActivityStatLotteryInfo>, I>>(base?: I): ActivityStatLotteryInfo {
    return ActivityStatLotteryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatLotteryInfo>, I>>(object: I): ActivityStatLotteryInfo {
    const message = createBaseActivityStatLotteryInfo();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryConfig.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseActivityStatPkInfo(): ActivityStatPkInfo {
  return { pk_config: undefined };
}

export const ActivityStatPkInfo: MessageFns<ActivityStatPkInfo> = {
  fromJSON(object: any): ActivityStatPkInfo {
    return { pk_config: isSet(object.pk_config) ? PKConfig.fromJSON(object.pk_config) : undefined };
  },

  create<I extends Exact<DeepPartial<ActivityStatPkInfo>, I>>(base?: I): ActivityStatPkInfo {
    return ActivityStatPkInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatPkInfo>, I>>(object: I): ActivityStatPkInfo {
    const message = createBaseActivityStatPkInfo();
    message.pk_config =
      object.pk_config !== undefined && object.pk_config !== null ? PKConfig.fromPartial(object.pk_config) : undefined;
    return message;
  }
};

function createBaseActivityStatRankInfo(): ActivityStatRankInfo {
  return { rank_config: undefined, cycle_list: [], prize_pool_id: 0, reward_category_key: '', reward_item_id: '' };
}

export const ActivityStatRankInfo: MessageFns<ActivityStatRankInfo> = {
  fromJSON(object: any): ActivityStatRankInfo {
    return {
      rank_config: isSet(object.rank_config) ? RankConfig.fromJSON(object.rank_config) : undefined,
      cycle_list: globalThis.Array.isArray(object?.cycle_list)
        ? object.cycle_list.map((e: any) => globalThis.Number(e))
        : [],
      prize_pool_id: isSet(object.prize_pool_id) ? globalThis.Number(object.prize_pool_id) : 0,
      reward_category_key: isSet(object.reward_category_key) ? globalThis.String(object.reward_category_key) : '',
      reward_item_id: isSet(object.reward_item_id) ? globalThis.String(object.reward_item_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatRankInfo>, I>>(base?: I): ActivityStatRankInfo {
    return ActivityStatRankInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatRankInfo>, I>>(object: I): ActivityStatRankInfo {
    const message = createBaseActivityStatRankInfo();
    message.rank_config =
      object.rank_config !== undefined && object.rank_config !== null
        ? RankConfig.fromPartial(object.rank_config)
        : undefined;
    message.cycle_list = object.cycle_list?.map(e => e) || [];
    message.prize_pool_id = object.prize_pool_id ?? 0;
    message.reward_category_key = object.reward_category_key ?? '';
    message.reward_item_id = object.reward_item_id ?? '';
    return message;
  }
};

function createBaseListActivityStatRankReq(): ListActivityStatRankReq {
  return { page: undefined, member: '', sort_type: 0, rank_id: 0, cycle_ts: 0, rank_member: '' };
}

export const ListActivityStatRankReq: MessageFns<ListActivityStatRankReq> = {
  fromJSON(object: any): ListActivityStatRankReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      sort_type: isSet(object.sort_type) ? sortTypeFromJSON(object.sort_type) : 0,
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      cycle_ts: isSet(object.cycle_ts) ? globalThis.Number(object.cycle_ts) : 0,
      rank_member: isSet(object.rank_member) ? globalThis.String(object.rank_member) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListActivityStatRankReq>, I>>(base?: I): ListActivityStatRankReq {
    return ListActivityStatRankReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListActivityStatRankReq>, I>>(object: I): ListActivityStatRankReq {
    const message = createBaseListActivityStatRankReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.member = object.member ?? '';
    message.sort_type = object.sort_type ?? 0;
    message.rank_id = object.rank_id ?? 0;
    message.cycle_ts = object.cycle_ts ?? 0;
    message.rank_member = object.rank_member ?? '';
    return message;
  }
};

function createBaseListActivityStatRankRsp(): ListActivityStatRankRsp {
  return { page: undefined, list: [] };
}

export const ListActivityStatRankRsp: MessageFns<ListActivityStatRankRsp> = {
  fromJSON(object: any): ListActivityStatRankRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ActivityStatRankItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListActivityStatRankRsp>, I>>(base?: I): ListActivityStatRankRsp {
    return ListActivityStatRankRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListActivityStatRankRsp>, I>>(object: I): ListActivityStatRankRsp {
    const message = createBaseListActivityStatRankRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => ActivityStatRankItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivityStatRankItem(): ActivityStatRankItem {
  return { score: 0, rank: 0, entities: [], sub_rank_member: '' };
}

export const ActivityStatRankItem: MessageFns<ActivityStatRankItem> = {
  fromJSON(object: any): ActivityStatRankItem {
    return {
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      rank: isSet(object.rank) ? globalThis.Number(object.rank) : 0,
      entities: globalThis.Array.isArray(object?.entities)
        ? object.entities.map((e: any) => ActivityStatRankItemEntity.fromJSON(e))
        : [],
      sub_rank_member: isSet(object.sub_rank_member) ? globalThis.String(object.sub_rank_member) : ''
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatRankItem>, I>>(base?: I): ActivityStatRankItem {
    return ActivityStatRankItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatRankItem>, I>>(object: I): ActivityStatRankItem {
    const message = createBaseActivityStatRankItem();
    message.score = object.score ?? 0;
    message.rank = object.rank ?? 0;
    message.entities = object.entities?.map(e => ActivityStatRankItemEntity.fromPartial(e)) || [];
    message.sub_rank_member = object.sub_rank_member ?? '';
    return message;
  }
};

function createBaseActivityStatRankItemEntity(): ActivityStatRankItemEntity {
  return { id: '', show_id: '', extend: '', reward_num: 0, owner_uid_showid: '' };
}

export const ActivityStatRankItemEntity: MessageFns<ActivityStatRankItemEntity> = {
  fromJSON(object: any): ActivityStatRankItemEntity {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : '',
      extend: isSet(object.extend) ? globalThis.String(object.extend) : '',
      reward_num: isSet(object.reward_num) ? globalThis.Number(object.reward_num) : 0,
      owner_uid_showid: isSet(object.owner_uid_showid) ? globalThis.String(object.owner_uid_showid) : ''
    };
  },

  create<I extends Exact<DeepPartial<ActivityStatRankItemEntity>, I>>(base?: I): ActivityStatRankItemEntity {
    return ActivityStatRankItemEntity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityStatRankItemEntity>, I>>(object: I): ActivityStatRankItemEntity {
    const message = createBaseActivityStatRankItemEntity();
    message.id = object.id ?? '';
    message.show_id = object.show_id ?? '';
    message.extend = object.extend ?? '';
    message.reward_num = object.reward_num ?? 0;
    message.owner_uid_showid = object.owner_uid_showid ?? '';
    return message;
  }
};

/** smicro:spath=gitit.cc/social/components-service/social-activity/biz/activity/handleactivitystaticmgr */
export type ActivityStatisticMgrDefinition = typeof ActivityStatisticMgrDefinition;
export const ActivityStatisticMgrDefinition = {
  name: 'ActivityStatisticMgr',
  fullName: 'comm.mgr.activity.ActivityStatisticMgr',
  methods: {
    /** 批量获取数据中心活动列表 */
    listActivityStat: {
      name: 'ListActivityStat',
      requestType: ListActivityStatReq,
      requestStream: false,
      responseType: ListActivityStatRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取数据中心活动榜单列表 */
    listActivityStatRank: {
      name: 'ListActivityStatRank',
      requestType: ListActivityStatRankReq,
      requestStream: false,
      responseType: ListActivityStatRankRsp,
      responseStream: false,
      options: {}
    },
    /** 根据id获取pk统计信息 */
    getActivityStatPK: {
      name: 'GetActivityStatPK',
      requestType: GetActivityStatPKReq,
      requestStream: false,
      responseType: GetActivityStatPKRsp,
      responseStream: false,
      options: {}
    },
    /** 根据id获取任务统计信息 */
    getActivityStatTask: {
      name: 'GetActivityStatTask',
      requestType: GetActivityStatTaskReq,
      requestStream: false,
      responseType: GetActivityStatTaskRsp,
      responseStream: false,
      options: {}
    },
    /** 根据id获取抽奖统计信息 */
    getActivityStatLottery: {
      name: 'GetActivityStatLottery',
      requestType: GetActivityStatLotteryReq,
      requestStream: false,
      responseType: GetActivityStatLotteryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
