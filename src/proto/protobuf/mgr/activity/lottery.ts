// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/lottery.proto

/* eslint-disable */
import { <PERSON><PERSON> } from '../../api/sconfig/model';
import { RewardPkg } from './activity_management';

export const protobufPackage = 'comm.mgr.activity';

export interface LotteryConf {
  item: LotteryItem[];
  ext_conf: LotteryExtConfig[];
}

/** 活动概率抽奖奖池配置 */
export interface LotteryItem {
  id: string;
  /** 轮次 */
  round: string;
  /** 奖励包 id */
  reward_pkg: RewardPkg | undefined;
  /** 概率 * 100 */
  probability: number;
  /** 是否限库存 */
  stock_limit: boolean;
  /** 库存 */
  stock: number;
  /** round 可获得的次数限制，小于等于0不限制 */
  once: boolean;
  /** 描述信息 */
  desc: Lang<PERSON> | undefined;
  /** 额外信息 */
  ext: string;
  /** 状态 - 1: 有效，0: 无效 */
  status: boolean;
  /** 模版的 id */
  template_item_id: string;
}

/** 保底配置 */
export interface LotteryExtConfig {
  parent_id: string;
  /** 必中次数 */
  guarantee_count: number;
  /** 非必中次数 */
  miss_count: number;
  /** 每日最多抽出保底个数 */
  daily_guarantee_num: number;
}

function createBaseLotteryConf(): LotteryConf {
  return { item: [], ext_conf: [] };
}

export const LotteryConf: MessageFns<LotteryConf> = {
  fromJSON(object: any): LotteryConf {
    return {
      item: globalThis.Array.isArray(object?.item) ? object.item.map((e: any) => LotteryItem.fromJSON(e)) : [],
      ext_conf: globalThis.Array.isArray(object?.ext_conf)
        ? object.ext_conf.map((e: any) => LotteryExtConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LotteryConf>, I>>(base?: I): LotteryConf {
    return LotteryConf.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryConf>, I>>(object: I): LotteryConf {
    const message = createBaseLotteryConf();
    message.item = object.item?.map(e => LotteryItem.fromPartial(e)) || [];
    message.ext_conf = object.ext_conf?.map(e => LotteryExtConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLotteryItem(): LotteryItem {
  return {
    id: '',
    round: '',
    reward_pkg: undefined,
    probability: 0,
    stock_limit: false,
    stock: 0,
    once: false,
    desc: undefined,
    ext: '',
    status: false,
    template_item_id: ''
  };
}

export const LotteryItem: MessageFns<LotteryItem> = {
  fromJSON(object: any): LotteryItem {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      round: isSet(object.round) ? globalThis.String(object.round) : '',
      reward_pkg: isSet(object.reward_pkg) ? RewardPkg.fromJSON(object.reward_pkg) : undefined,
      probability: isSet(object.probability) ? globalThis.Number(object.probability) : 0,
      stock_limit: isSet(object.stock_limit) ? globalThis.Boolean(object.stock_limit) : false,
      stock: isSet(object.stock) ? globalThis.Number(object.stock) : 0,
      once: isSet(object.once) ? globalThis.Boolean(object.once) : false,
      desc: isSet(object.desc) ? Langs.fromJSON(object.desc) : undefined,
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      status: isSet(object.status) ? globalThis.Boolean(object.status) : false,
      template_item_id: isSet(object.template_item_id) ? globalThis.String(object.template_item_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryItem>, I>>(base?: I): LotteryItem {
    return LotteryItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryItem>, I>>(object: I): LotteryItem {
    const message = createBaseLotteryItem();
    message.id = object.id ?? '';
    message.round = object.round ?? '';
    message.reward_pkg =
      object.reward_pkg !== undefined && object.reward_pkg !== null
        ? RewardPkg.fromPartial(object.reward_pkg)
        : undefined;
    message.probability = object.probability ?? 0;
    message.stock_limit = object.stock_limit ?? false;
    message.stock = object.stock ?? 0;
    message.once = object.once ?? false;
    message.desc = object.desc !== undefined && object.desc !== null ? Langs.fromPartial(object.desc) : undefined;
    message.ext = object.ext ?? '';
    message.status = object.status ?? false;
    message.template_item_id = object.template_item_id ?? '';
    return message;
  }
};

function createBaseLotteryExtConfig(): LotteryExtConfig {
  return { parent_id: '', guarantee_count: 0, miss_count: 0, daily_guarantee_num: 0 };
}

export const LotteryExtConfig: MessageFns<LotteryExtConfig> = {
  fromJSON(object: any): LotteryExtConfig {
    return {
      parent_id: isSet(object.parent_id) ? globalThis.String(object.parent_id) : '',
      guarantee_count: isSet(object.guarantee_count) ? globalThis.Number(object.guarantee_count) : 0,
      miss_count: isSet(object.miss_count) ? globalThis.Number(object.miss_count) : 0,
      daily_guarantee_num: isSet(object.daily_guarantee_num) ? globalThis.Number(object.daily_guarantee_num) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryExtConfig>, I>>(base?: I): LotteryExtConfig {
    return LotteryExtConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryExtConfig>, I>>(object: I): LotteryExtConfig {
    const message = createBaseLotteryExtConfig();
    message.parent_id = object.parent_id ?? '';
    message.guarantee_count = object.guarantee_count ?? 0;
    message.miss_count = object.miss_count ?? 0;
    message.daily_guarantee_num = object.daily_guarantee_num ?? 0;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
