// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/common.proto

/* eslint-disable */
import {
  CycleType,
  cycleTypeFromJSON,
  LotteryPaidRule,
  LotteryType,
  lotteryTypeFromJSON,
  PKType,
  pKTypeFromJSON,
  PrizePoolCalType,
  prizePoolCalTypeFromJSON,
  PrizePoolWeight,
  PrizePoolWeightType,
  prizePoolWeightTypeFromJSON,
  RankSource,
  rankSourceFromJSON,
  RankType,
  rankTypeFromJSON,
  RoomRecommendDisplayInfo,
  roomRecommendDisplayInfoFromJSON,
  ScoreType,
  scoreTypeFromJSON,
  TaskCycle,
  taskCycleFromJSON,
  TaskCycleSettleType,
  taskCycleSettleTypeFromJSON,
  TaskMode,
  taskModeFromJSON,
  TaskRewardType,
  taskRewardType<PERSON>rom<PERSON><PERSON><PERSON>,
  TaskScoreType,
  taskScoreTypeFromJSON,
  TaskType,
  taskTypeFromJSON
} from '../../api/activity/common';
import {
  FamilyRoleType,
  familyRoleTypeFromJSON,
  RankRewardTargetType,
  rankRewardTargetTypeFromJSON
} from '../../api/adapter/model';
import { MemberRole, memberRoleFromJSON } from '../guild/guild';

export const protobufPackage = 'comm.mgr.activity';

export enum RoomIDType {
  /** ROOM_ID_TYPE_NONE - none */
  ROOM_ID_TYPE_NONE = 0,
  /** ROOM_ID_TYPE_NORMAL - 普通房 */
  ROOM_ID_TYPE_NORMAL = 1,
  /** ROOM_ID_TYPE_FAMILY - 家族房 */
  ROOM_ID_TYPE_FAMILY = 2,
  /** ROOM_ID_TYPE_COMMON - 秀场房 */
  ROOM_ID_TYPE_COMMON = 3,
  /** ROOM_ID_TYPE_GAME - 游戏房 */
  ROOM_ID_TYPE_GAME = 4,
  /** ROOM_ID_TYPE_CHAT - 语聊房 */
  ROOM_ID_TYPE_CHAT = 5,
  UNRECOGNIZED = -1
}

export function roomIDTypeFromJSON(object: any): RoomIDType {
  switch (object) {
    case 0:
    case 'ROOM_ID_TYPE_NONE':
      return RoomIDType.ROOM_ID_TYPE_NONE;
    case 1:
    case 'ROOM_ID_TYPE_NORMAL':
      return RoomIDType.ROOM_ID_TYPE_NORMAL;
    case 2:
    case 'ROOM_ID_TYPE_FAMILY':
      return RoomIDType.ROOM_ID_TYPE_FAMILY;
    case 3:
    case 'ROOM_ID_TYPE_COMMON':
      return RoomIDType.ROOM_ID_TYPE_COMMON;
    case 4:
    case 'ROOM_ID_TYPE_GAME':
      return RoomIDType.ROOM_ID_TYPE_GAME;
    case 5:
    case 'ROOM_ID_TYPE_CHAT':
      return RoomIDType.ROOM_ID_TYPE_CHAT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomIDType.UNRECOGNIZED;
  }
}

export enum SpecialInputType {
  /** SPECIAL_INPUT_TYPE_NONE - none */
  SPECIAL_INPUT_TYPE_NONE = 0,
  /** SPECIAL_INPUT_TYPE_ID - 公会 or 家族id */
  SPECIAL_INPUT_TYPE_ID = 1,
  /** SPECIAL_INPUT_TYPE_MEMBER_ID - 公会成员id or 家族成员id or 房间owner id */
  SPECIAL_INPUT_TYPE_MEMBER_ID = 2,
  /** SPECIAL_INPUT_TYPE_USER_LABEL - 用户标签 */
  SPECIAL_INPUT_TYPE_USER_LABEL = 3,
  UNRECOGNIZED = -1
}

export function specialInputTypeFromJSON(object: any): SpecialInputType {
  switch (object) {
    case 0:
    case 'SPECIAL_INPUT_TYPE_NONE':
      return SpecialInputType.SPECIAL_INPUT_TYPE_NONE;
    case 1:
    case 'SPECIAL_INPUT_TYPE_ID':
      return SpecialInputType.SPECIAL_INPUT_TYPE_ID;
    case 2:
    case 'SPECIAL_INPUT_TYPE_MEMBER_ID':
      return SpecialInputType.SPECIAL_INPUT_TYPE_MEMBER_ID;
    case 3:
    case 'SPECIAL_INPUT_TYPE_USER_LABEL':
      return SpecialInputType.SPECIAL_INPUT_TYPE_USER_LABEL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpecialInputType.UNRECOGNIZED;
  }
}

export enum RankShowType {
  /** RANK_SHOW_TYPE_NONE - none */
  RANK_SHOW_TYPE_NONE = 0,
  /** RANK_SHOW_TYPE_MEMBER - 家族/公会/房间成员 */
  RANK_SHOW_TYPE_MEMBER = 1,
  /** RANK_SHOW_TYPE_SENDER - 送礼者 */
  RANK_SHOW_TYPE_SENDER = 2,
  UNRECOGNIZED = -1
}

export function rankShowTypeFromJSON(object: any): RankShowType {
  switch (object) {
    case 0:
    case 'RANK_SHOW_TYPE_NONE':
      return RankShowType.RANK_SHOW_TYPE_NONE;
    case 1:
    case 'RANK_SHOW_TYPE_MEMBER':
      return RankShowType.RANK_SHOW_TYPE_MEMBER;
    case 2:
    case 'RANK_SHOW_TYPE_SENDER':
      return RankShowType.RANK_SHOW_TYPE_SENDER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RankShowType.UNRECOGNIZED;
  }
}

/** 数量类型 */
export enum AmountType {
  /** AMOUNT_TYPE_NONE - 无需数量类型的 */
  AMOUNT_TYPE_NONE = 0,
  /** AMOUNT_TYPE_DURATION - 时长型 */
  AMOUNT_TYPE_DURATION = 1,
  /** AMOUNT_TYPE_QUANTITY - 数量型 */
  AMOUNT_TYPE_QUANTITY = 2,
  /** AMOUNT_TYPE_QUANTITY_AND_DURATION - 数量+时长型 */
  AMOUNT_TYPE_QUANTITY_AND_DURATION = 3,
  UNRECOGNIZED = -1
}

export function amountTypeFromJSON(object: any): AmountType {
  switch (object) {
    case 0:
    case 'AMOUNT_TYPE_NONE':
      return AmountType.AMOUNT_TYPE_NONE;
    case 1:
    case 'AMOUNT_TYPE_DURATION':
      return AmountType.AMOUNT_TYPE_DURATION;
    case 2:
    case 'AMOUNT_TYPE_QUANTITY':
      return AmountType.AMOUNT_TYPE_QUANTITY;
    case 3:
    case 'AMOUNT_TYPE_QUANTITY_AND_DURATION':
      return AmountType.AMOUNT_TYPE_QUANTITY_AND_DURATION;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AmountType.UNRECOGNIZED;
  }
}

/** 数量单位, 时长型的奖励才需要. */
export enum AmountUnit {
  AMOUNT_UNIT_NONE = 0,
  /** AMOUNT_UNIT_SECOND - 秒 */
  AMOUNT_UNIT_SECOND = 1,
  /** AMOUNT_UNIT_MINUTE - 分 */
  AMOUNT_UNIT_MINUTE = 2,
  /** AMOUNT_UNIT_HOUR - 小时 */
  AMOUNT_UNIT_HOUR = 3,
  /** AMOUNT_UNIT_DAY - 天 */
  AMOUNT_UNIT_DAY = 4,
  /** AMOUNT_UNIT_PERMANENT - 永久 */
  AMOUNT_UNIT_PERMANENT = 10,
  UNRECOGNIZED = -1
}

export function amountUnitFromJSON(object: any): AmountUnit {
  switch (object) {
    case 0:
    case 'AMOUNT_UNIT_NONE':
      return AmountUnit.AMOUNT_UNIT_NONE;
    case 1:
    case 'AMOUNT_UNIT_SECOND':
      return AmountUnit.AMOUNT_UNIT_SECOND;
    case 2:
    case 'AMOUNT_UNIT_MINUTE':
      return AmountUnit.AMOUNT_UNIT_MINUTE;
    case 3:
    case 'AMOUNT_UNIT_HOUR':
      return AmountUnit.AMOUNT_UNIT_HOUR;
    case 4:
    case 'AMOUNT_UNIT_DAY':
      return AmountUnit.AMOUNT_UNIT_DAY;
    case 10:
    case 'AMOUNT_UNIT_PERMANENT':
      return AmountUnit.AMOUNT_UNIT_PERMANENT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AmountUnit.UNRECOGNIZED;
  }
}

/** 奖励类型 */
export enum RewardType {
  /** REWARD_TYPE_NONE - none */
  REWARD_TYPE_NONE = 0,
  /** REWARD_TYPE_EMPTY - 空奖励 */
  REWARD_TYPE_EMPTY = 1,
  /** REWARD_TYPE_PACKAGE - 奖励包 */
  REWARD_TYPE_PACKAGE = 2,
  /** REWARD_TYPE_ANIMATION_EFFECT - 动效奖励 */
  REWARD_TYPE_ANIMATION_EFFECT = 3,
  /** REWARD_TYPE_GENERIC - 通用奖励 */
  REWARD_TYPE_GENERIC = 4,
  UNRECOGNIZED = -1
}

export function rewardTypeFromJSON(object: any): RewardType {
  switch (object) {
    case 0:
    case 'REWARD_TYPE_NONE':
      return RewardType.REWARD_TYPE_NONE;
    case 1:
    case 'REWARD_TYPE_EMPTY':
      return RewardType.REWARD_TYPE_EMPTY;
    case 2:
    case 'REWARD_TYPE_PACKAGE':
      return RewardType.REWARD_TYPE_PACKAGE;
    case 3:
    case 'REWARD_TYPE_ANIMATION_EFFECT':
      return RewardType.REWARD_TYPE_ANIMATION_EFFECT;
    case 4:
    case 'REWARD_TYPE_GENERIC':
      return RewardType.REWARD_TYPE_GENERIC;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RewardType.UNRECOGNIZED;
  }
}

export enum PlayLocation {
  /** PLAY_LOCATION_NONE - NONE */
  PLAY_LOCATION_NONE = 0,
  /** PLAY_LOCATION_ALL_ROOM - 所有房间 */
  PLAY_LOCATION_ALL_ROOM = 1,
  /** PLAY_LOCATION_USER_ROOM - 用户所在房间 */
  PLAY_LOCATION_USER_ROOM = 2,
  /** PLAY_LOCATION_ALL - 所有界面 */
  PLAY_LOCATION_ALL = 3,
  UNRECOGNIZED = -1
}

export function playLocationFromJSON(object: any): PlayLocation {
  switch (object) {
    case 0:
    case 'PLAY_LOCATION_NONE':
      return PlayLocation.PLAY_LOCATION_NONE;
    case 1:
    case 'PLAY_LOCATION_ALL_ROOM':
      return PlayLocation.PLAY_LOCATION_ALL_ROOM;
    case 2:
    case 'PLAY_LOCATION_USER_ROOM':
      return PlayLocation.PLAY_LOCATION_USER_ROOM;
    case 3:
    case 'PLAY_LOCATION_ALL':
      return PlayLocation.PLAY_LOCATION_ALL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PlayLocation.UNRECOGNIZED;
  }
}

/** 指定可见人群 */
export enum SpecifyParticipants {
  /** SPECIFY_PARTICIPANTS_NONE - 不指定 */
  SPECIFY_PARTICIPANTS_NONE = 0,
  SPECIFY_PARTICIPANTS_HAS_ROOM = 1,
  SPECIFY_PARTICIPANTS_HAS_GUILD = 2,
  SPECIFY_PARTICIPANTS_HAS_FAMILY = 3,
  /** SPECIFY_PARTICIPANTS_WHITE_LIST - 指定用户 */
  SPECIFY_PARTICIPANTS_WHITE_LIST = 4,
  UNRECOGNIZED = -1
}

export function specifyParticipantsFromJSON(object: any): SpecifyParticipants {
  switch (object) {
    case 0:
    case 'SPECIFY_PARTICIPANTS_NONE':
      return SpecifyParticipants.SPECIFY_PARTICIPANTS_NONE;
    case 1:
    case 'SPECIFY_PARTICIPANTS_HAS_ROOM':
      return SpecifyParticipants.SPECIFY_PARTICIPANTS_HAS_ROOM;
    case 2:
    case 'SPECIFY_PARTICIPANTS_HAS_GUILD':
      return SpecifyParticipants.SPECIFY_PARTICIPANTS_HAS_GUILD;
    case 3:
    case 'SPECIFY_PARTICIPANTS_HAS_FAMILY':
      return SpecifyParticipants.SPECIFY_PARTICIPANTS_HAS_FAMILY;
    case 4:
    case 'SPECIFY_PARTICIPANTS_WHITE_LIST':
      return SpecifyParticipants.SPECIFY_PARTICIPANTS_WHITE_LIST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SpecifyParticipants.UNRECOGNIZED;
  }
}

/** 二进制开关，1，2，4，8 这样定义 */
export enum TaskSwitch {
  TASK_SWITCH_NONE = 0,
  TASK_SWITCH_ONLY_MY_FAMILY_ROOM = 1,
  TASK_SWITCH_ONLY_MY_NORMAL_ROOM = 2,
  UNRECOGNIZED = -1
}

export function taskSwitchFromJSON(object: any): TaskSwitch {
  switch (object) {
    case 0:
    case 'TASK_SWITCH_NONE':
      return TaskSwitch.TASK_SWITCH_NONE;
    case 1:
    case 'TASK_SWITCH_ONLY_MY_FAMILY_ROOM':
      return TaskSwitch.TASK_SWITCH_ONLY_MY_FAMILY_ROOM;
    case 2:
    case 'TASK_SWITCH_ONLY_MY_NORMAL_ROOM':
      return TaskSwitch.TASK_SWITCH_ONLY_MY_NORMAL_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskSwitch.UNRECOGNIZED;
  }
}

export enum RechargeSource {
  RECHARGE_SOURCE_NONE = 0,
  /** RECHARGE_SOURCE_ONLINE - 线上充值 */
  RECHARGE_SOURCE_ONLINE = 1,
  /** RECHARGE_SOURCE_OFFLINE - 线下充值，包括币商充值、美金钱包充值 */
  RECHARGE_SOURCE_OFFLINE = 2,
  UNRECOGNIZED = -1
}

export function rechargeSourceFromJSON(object: any): RechargeSource {
  switch (object) {
    case 0:
    case 'RECHARGE_SOURCE_NONE':
      return RechargeSource.RECHARGE_SOURCE_NONE;
    case 1:
    case 'RECHARGE_SOURCE_ONLINE':
      return RechargeSource.RECHARGE_SOURCE_ONLINE;
    case 2:
    case 'RECHARGE_SOURCE_OFFLINE':
      return RechargeSource.RECHARGE_SOURCE_OFFLINE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RechargeSource.UNRECOGNIZED;
  }
}

export enum TaskRewardRankPointsUnit {
  TASK_REWARD_RANK_POINTS_UNIT_NONE = 0,
  /** TASK_REWARD_RANK_POINTS_UNIT_MINUTE - 分钟 */
  TASK_REWARD_RANK_POINTS_UNIT_MINUTE = 1,
  UNRECOGNIZED = -1
}

export function taskRewardRankPointsUnitFromJSON(object: any): TaskRewardRankPointsUnit {
  switch (object) {
    case 0:
    case 'TASK_REWARD_RANK_POINTS_UNIT_NONE':
      return TaskRewardRankPointsUnit.TASK_REWARD_RANK_POINTS_UNIT_NONE;
    case 1:
    case 'TASK_REWARD_RANK_POINTS_UNIT_MINUTE':
      return TaskRewardRankPointsUnit.TASK_REWARD_RANK_POINTS_UNIT_MINUTE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskRewardRankPointsUnit.UNRECOGNIZED;
  }
}

/** 排序规则 */
export enum SortType {
  SORT_TYPE_NONE = 0,
  SORT_TYPE_ASC = 1,
  SORT_TYPE_DESC = 2,
  UNRECOGNIZED = -1
}

export function sortTypeFromJSON(object: any): SortType {
  switch (object) {
    case 0:
    case 'SORT_TYPE_NONE':
      return SortType.SORT_TYPE_NONE;
    case 1:
    case 'SORT_TYPE_ASC':
      return SortType.SORT_TYPE_ASC;
    case 2:
    case 'SORT_TYPE_DESC':
      return SortType.SORT_TYPE_DESC;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SortType.UNRECOGNIZED;
  }
}

export enum ActivityStatus {
  ACTIVITY_STATUS_NONE = 0,
  /** ACTIVITY_STATUS_WAITING - 未开始 */
  ACTIVITY_STATUS_WAITING = 1,
  /** ACTIVITY_STATUS_RUNNING - 进行中 */
  ACTIVITY_STATUS_RUNNING = 2,
  /** ACTIVITY_STATUS_END - 已结束 */
  ACTIVITY_STATUS_END = 3,
  UNRECOGNIZED = -1
}

export function activityStatusFromJSON(object: any): ActivityStatus {
  switch (object) {
    case 0:
    case 'ACTIVITY_STATUS_NONE':
      return ActivityStatus.ACTIVITY_STATUS_NONE;
    case 1:
    case 'ACTIVITY_STATUS_WAITING':
      return ActivityStatus.ACTIVITY_STATUS_WAITING;
    case 2:
    case 'ACTIVITY_STATUS_RUNNING':
      return ActivityStatus.ACTIVITY_STATUS_RUNNING;
    case 3:
    case 'ACTIVITY_STATUS_END':
      return ActivityStatus.ACTIVITY_STATUS_END;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ActivityStatus.UNRECOGNIZED;
  }
}

export enum PKDetailSettleStatus {
  /** PK_DETAIL_SETTLE_STATUS_NONE - 未结算 */
  PK_DETAIL_SETTLE_STATUS_NONE = 0,
  /** PK_DETAIL_SETTLE_STATUS_DONE - 已结算 */
  PK_DETAIL_SETTLE_STATUS_DONE = 1,
  UNRECOGNIZED = -1
}

export function pKDetailSettleStatusFromJSON(object: any): PKDetailSettleStatus {
  switch (object) {
    case 0:
    case 'PK_DETAIL_SETTLE_STATUS_NONE':
      return PKDetailSettleStatus.PK_DETAIL_SETTLE_STATUS_NONE;
    case 1:
    case 'PK_DETAIL_SETTLE_STATUS_DONE':
      return PKDetailSettleStatus.PK_DETAIL_SETTLE_STATUS_DONE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PKDetailSettleStatus.UNRECOGNIZED;
  }
}

export enum LotteryComponentType {
  LOTTERY_COMPONENT_TYPE_NONE = 0,
  /** LOTTERY_COMPONENT_TYPE_REWARD_SLIDE_DRAW - 奖励滑动抽奖 */
  LOTTERY_COMPONENT_TYPE_REWARD_SLIDE_DRAW = 1,
  /** LOTTERY_COMPONENT_TYPE_NINE_GRID - 九宫格抽奖 */
  LOTTERY_COMPONENT_TYPE_NINE_GRID = 2,
  /** LOTTERY_COMPONENT_TYPE_WHEEL_DRAW - 转盘抽奖 */
  LOTTERY_COMPONENT_TYPE_WHEEL_DRAW = 3,
  UNRECOGNIZED = -1
}

export function lotteryComponentTypeFromJSON(object: any): LotteryComponentType {
  switch (object) {
    case 0:
    case 'LOTTERY_COMPONENT_TYPE_NONE':
      return LotteryComponentType.LOTTERY_COMPONENT_TYPE_NONE;
    case 1:
    case 'LOTTERY_COMPONENT_TYPE_REWARD_SLIDE_DRAW':
      return LotteryComponentType.LOTTERY_COMPONENT_TYPE_REWARD_SLIDE_DRAW;
    case 2:
    case 'LOTTERY_COMPONENT_TYPE_NINE_GRID':
      return LotteryComponentType.LOTTERY_COMPONENT_TYPE_NINE_GRID;
    case 3:
    case 'LOTTERY_COMPONENT_TYPE_WHEEL_DRAW':
      return LotteryComponentType.LOTTERY_COMPONENT_TYPE_WHEEL_DRAW;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LotteryComponentType.UNRECOGNIZED;
  }
}

export enum Switch {
  SWITCH_NONE = 0,
  /** SWITCH_OPEN - 开 */
  SWITCH_OPEN = 1,
  /** SWITCH_CLOSE - 关 */
  SWITCH_CLOSE = 2,
  UNRECOGNIZED = -1
}

export function switchFromJSON(object: any): Switch {
  switch (object) {
    case 0:
    case 'SWITCH_NONE':
      return Switch.SWITCH_NONE;
    case 1:
    case 'SWITCH_OPEN':
      return Switch.SWITCH_OPEN;
    case 2:
    case 'SWITCH_CLOSE':
      return Switch.SWITCH_CLOSE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Switch.UNRECOGNIZED;
  }
}

export enum RoomRecommendComponentType {
  ROOM_RECOMMEND_COMPONENT_TYPE_NONE = 0,
  /** ROOM_RECOMMEND_COMPONENT_TYPE_CAROUSEL - 轮播 */
  ROOM_RECOMMEND_COMPONENT_TYPE_CAROUSEL = 1,
  /** ROOM_RECOMMEND_COMPONENT_TYPE_TILE_DISPLAY - 平铺 */
  ROOM_RECOMMEND_COMPONENT_TYPE_TILE_DISPLAY = 2,
  /** ROOM_RECOMMEND_COMPONENT_TYPE_WITH_INTRO - 带介绍 */
  ROOM_RECOMMEND_COMPONENT_TYPE_WITH_INTRO = 3,
  UNRECOGNIZED = -1
}

export function roomRecommendComponentTypeFromJSON(object: any): RoomRecommendComponentType {
  switch (object) {
    case 0:
    case 'ROOM_RECOMMEND_COMPONENT_TYPE_NONE':
      return RoomRecommendComponentType.ROOM_RECOMMEND_COMPONENT_TYPE_NONE;
    case 1:
    case 'ROOM_RECOMMEND_COMPONENT_TYPE_CAROUSEL':
      return RoomRecommendComponentType.ROOM_RECOMMEND_COMPONENT_TYPE_CAROUSEL;
    case 2:
    case 'ROOM_RECOMMEND_COMPONENT_TYPE_TILE_DISPLAY':
      return RoomRecommendComponentType.ROOM_RECOMMEND_COMPONENT_TYPE_TILE_DISPLAY;
    case 3:
    case 'ROOM_RECOMMEND_COMPONENT_TYPE_WITH_INTRO':
      return RoomRecommendComponentType.ROOM_RECOMMEND_COMPONENT_TYPE_WITH_INTRO;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomRecommendComponentType.UNRECOGNIZED;
  }
}

export enum RoomRecommendType {
  ROOM_RECOMMEND_TYPE_NONE = 0,
  /** ROOM_RECOMMEND_TYPE_SPECIAL_USER_WHITE_LIST - 指定用户白名单 */
  ROOM_RECOMMEND_TYPE_SPECIAL_USER_WHITE_LIST = 1,
  /** ROOM_RECOMMEND_TYPE_SPECIAL_ROOM_ID - 指定房间id */
  ROOM_RECOMMEND_TYPE_SPECIAL_ROOM_ID = 2,
  UNRECOGNIZED = -1
}

export function roomRecommendTypeFromJSON(object: any): RoomRecommendType {
  switch (object) {
    case 0:
    case 'ROOM_RECOMMEND_TYPE_NONE':
      return RoomRecommendType.ROOM_RECOMMEND_TYPE_NONE;
    case 1:
    case 'ROOM_RECOMMEND_TYPE_SPECIAL_USER_WHITE_LIST':
      return RoomRecommendType.ROOM_RECOMMEND_TYPE_SPECIAL_USER_WHITE_LIST;
    case 2:
    case 'ROOM_RECOMMEND_TYPE_SPECIAL_ROOM_ID':
      return RoomRecommendType.ROOM_RECOMMEND_TYPE_SPECIAL_ROOM_ID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RoomRecommendType.UNRECOGNIZED;
  }
}

export enum PresentationComponentType {
  PRESENTATION_COMPONENT_TYPE_NONE = 0,
  /** PRESENTATION_COMPONENT_TYPE_CP - 双人展示 */
  PRESENTATION_COMPONENT_TYPE_CP = 1,
  /** PRESENTATION_COMPONENT_TYPE_HONOR - 荣誉展示 */
  PRESENTATION_COMPONENT_TYPE_HONOR = 2,
  UNRECOGNIZED = -1
}

export function presentationComponentTypeFromJSON(object: any): PresentationComponentType {
  switch (object) {
    case 0:
    case 'PRESENTATION_COMPONENT_TYPE_NONE':
      return PresentationComponentType.PRESENTATION_COMPONENT_TYPE_NONE;
    case 1:
    case 'PRESENTATION_COMPONENT_TYPE_CP':
      return PresentationComponentType.PRESENTATION_COMPONENT_TYPE_CP;
    case 2:
    case 'PRESENTATION_COMPONENT_TYPE_HONOR':
      return PresentationComponentType.PRESENTATION_COMPONENT_TYPE_HONOR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PresentationComponentType.UNRECOGNIZED;
  }
}

/** 时间粒度 */
export enum TimeSlotType {
  TYPE_NONE = 0,
  /** TYPE_HOUR - 小时粒度 */
  TYPE_HOUR = 5,
  UNRECOGNIZED = -1
}

export function timeSlotTypeFromJSON(object: any): TimeSlotType {
  switch (object) {
    case 0:
    case 'TYPE_NONE':
      return TimeSlotType.TYPE_NONE;
    case 5:
    case 'TYPE_HOUR':
      return TimeSlotType.TYPE_HOUR;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TimeSlotType.UNRECOGNIZED;
  }
}

export interface ActivityInfo {
  /** 活动配置 */
  activity: Activity | undefined;
  /** 榜单配置，查询数据时返回 */
  rank_list: RankInfo[];
  /** 任务配置 */
  task_list: TaskInfo[];
  /** pk配置 */
  pk_list: PKInfo[];
  /** 抽奖配置 */
  lottery_list: LotteryInfo[];
  /** 房间推荐配置 */
  room_recommend_list: RoomRecommendInfo[];
  /** 展示配置 */
  presentation_list: SavePresentationConfig[];
  /** 奖池配置 */
  prize_pool_list: PrizePoolInfo[];
}

export interface Activity {
  /** 活动ID */
  id: number;
  /** 活动名称 */
  name: string;
  /** 活动开始时间 */
  start_time: number;
  /** 活动结束时间 */
  end_time: number;
  /** 展示礼物 */
  display_gifts: string[];
  /** 展示奖励 */
  display_rewards: string;
  /** 备注 */
  remark: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  create_by: string;
  /** 最后更新人 */
  update_by: string;
  /** 删除人 */
  delete_by: string;
  /** 删除时间 */
  dtime: number;
  /** 更新时间 */
  utime: number;
  /** 创建时间 */
  ctime: number;
  /** 活动url地址多语言json格式{"en":"xx","ar":"xx"} */
  url: string;
  /** 是否为模板。为模版时，在list时会排除掉这个数据 */
  is_template: boolean;
  /** 活动唯一code, 用于运营和开发沟通. */
  activity_code: string;
  /** 应用code, 用于记录积木平台的应用标识. */
  application_code: string;
  /** 活动国家编码 */
  cou: string;
  /** 活动时区, 服务端根据国家编码返回, 前端不需要填. */
  time_zone: string;
}

export interface RankInfo {
  /** 榜单配置 */
  rank_config: RankConfig | undefined;
  /** 奖励配置 */
  list_rank_rewards: ListRankRewardObject[];
  /** 子榜单奖励配置，一般是贡献榜 */
  list_supporter_rank_rewards: ListRankRewardObject[];
}

export interface RankConfig {
  /** 榜单ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 榜单周期 */
  cycle: CycleType;
  /** 榜单类型 */
  rank_type: RankType;
  /** 上榜数量（top n） */
  show_top: number;
  /** 数据来源 */
  source: RankSource;
  /** 场景（多个），不限制则留空 */
  scenes: string[];
  /** 分数累加类型 */
  score_type: ScoreType;
  /** 分数比例 */
  ratio: number;
  /** 指定币种对应的分数比例, 1: 收礼获得的货币 2: 送礼消耗的货币 3: 币商使用的货币 4: 美金钱包 */
  currency_ratio: { [key: number]: number };
  /** 榜单生效地区 */
  regions: string[];
  /** 榜单国家编码（计算时区） */
  cou: string;
  /** 榜单时区, 服务端根据国家编码返回, 前端不需要填. */
  time_zone: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 有效时段列表，对于小时榜则表示每天有效小时列表, 例如: [8, 9, 10, 20, 21, 22] 表示上午/下午的 8/9/10 点才统计. */
  valid_periods: number[];
  /** 指定礼物类型 */
  gift_types: string[];
  /** 指定房间类型 */
  room_types: string[];
  /** 指定礼物ID */
  special_gifts: string[];
  /** 指定房间ID */
  special_rooms: number[];
  /** 指定公会ID */
  special_guilds: number[];
  /** 用户黑名单，废弃，使用 user_black_bw_info 代替 */
  uid_black_list: number[];
  /** 房间黑名单 */
  room_id_black_list: number[];
  /** 公会黑名单 */
  guild_id_black_list: number[];
  /** 游戏code白名单 */
  game_code_white_list: string[];
  /** 备注 */
  remark: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  create_by: string;
  /** 最后更新人 */
  update_by: string;
  /** 更新时间 */
  utime: number;
  /** 创建时间 */
  ctime: number;
  /** 指定家族ID */
  special_families: number[];
  /** 家族ID黑名单 */
  family_id_black_list: number[];
  /** 指定家族成员角色 */
  special_family_roles: FamilyRoleType[];
  /** 指定工会成员角色 */
  special_guild_roles: MemberRole[];
  /** 榜单展示类型，目前只有公会跟家族类型会用到 */
  rank_show_type: RankShowType;
  /** 白名单原始输入 */
  special_input: number[];
  /** 白名单类型 */
  special_input_type: SpecialInputType;
  /** 展示的历史榜单周期数量，前端用 */
  show_history_period: number;
  /** 底下我的信息展示的房间类型 */
  bottom_show_room_type: RoomIDType;
  /** 指定房间模式 */
  special_room_modes: string[];
  /** 指定收礼礼物ID */
  special_rec_gifts: string[];
  /** 晋级数量 */
  promotion_num: number;
  /** 用户黑名单id列表，废弃，使用 user_black_bw_info 代替 */
  uid_black_list_ids: string[];
  /** 挂件开关 */
  pendant_switch: Switch;
  /** 挂件背景图多语言json格式{"en":"xx.jpg","ar":"aa.jpg"} */
  pendant_background_images: string;
  /** 前端组件类型，前端用 */
  component_type: string;
  /** 限制值，比如说送礼榜用来过滤一些小价值礼物，可根据rankType来决定用作什么功能 */
  limit_value: string;
  /** 最小上榜分数，小于这个分数会从榜单剔除且不发奖励 */
  min_score: number;
  /** 最大上榜分数，超过这个分数会从榜单剔除且不发奖励 */
  max_score: number;
  /** 过滤条件表达式，后端开发提供，原样保存即可 */
  filter_expression: string;
  /** 榜单名字，发奖用 */
  name: string;
  /** 用户白名单bw信息 */
  user_white_bw_info: BWInfo | undefined;
  /** 家族长白名单bw信息 */
  family_owner_bw_info: BWInfo | undefined;
  /** 指定礼物分数比例，优先级比 currency_ratio 高 */
  special_gift_currency_ratio: SpecialGiftCurrencyRatio[];
  /** 已经选择的子分类 */
  selected_gift_sub_categorys: RewardSubCategory[];
  /** 已经选择的子分类，收到礼物 */
  rec_selected_gift_subcategorys: RewardSubCategory[];
  /** 是否开启背包礼物计算，只有礼物榜单生效 */
  enable_backpack_gift: boolean;
  /** 是否排除房主贡献值，只有房间任务生效 */
  exclude_room_owner: boolean;
  /** 过滤条件表达式id */
  filter_expression_id: number;
  /** 用户黑名单bw信息 */
  user_black_bw_info: BWInfo | undefined;
}

export interface RankConfig_CurrencyRatioEntry {
  key: number;
  value: number;
}

/**
 * 奖励分类, 虽然奖励分类的标识业务可以自己定义, 但是最好参照以下的名字便于多个业务之间叫法一致和阅读理解.
 *    GIFT: 礼物
 *    ROOM_BACKGROUND: 房间背景
 *    RING: 戒指
 *    HEADWEAR: 头像框
 *    MOUNT: 座驾
 *    MIC_STYLE: 麦位光圈
 *    ON_MIC_EFFECT: 上麦特效
 *    MESSAGE_BUBBLE: 公屏消息气泡
 *    PROFILE_ANIMATION: 个人主页动画
 *    STORE_PROP: 道具
 *    INTIMACY_CARD: 亲密关系卡
 *    TITLE: 称号
 *    MEDAL: 勋章
 *    CELEBRITY_TITLE: 名人称号
 *    ...
 *
 *    __SPECIAL__: 特殊奖励, 这个奖励分类是指一些普通奖励之外的特殊奖励, 由运营在活动平台直接创建, 中台进行维护, 业务方发放时需要特殊处理.
 */
export interface RewardCategory {
  /** 奖励分类标识, 参考上面的注释. */
  category_key: string;
  /** 奖励分类名称, 例如: 礼物 / 头像框 / 房间背景 ..., 这是缺省名称, 如果 oms 需要国际化显示则需要提供 i18n_category_name 字段. */
  category_name: string;
  /** 国家化的奖励分类名称, key 的取值有 zh / en / ar / tr ... */
  i18n_category_name: { [key: string]: string };
  /** 此类奖励的数量类型, 例如礼物的数量类型是数量型, 头像框的数量类型是时长型 */
  amount_type: AmountType;
}

export interface RewardCategory_I18nCategoryNameEntry {
  key: string;
  value: string;
}

/** 奖励配置项 */
export interface RewardItem {
  /** 奖励配置项ID, 为了通用这里采用字符串形式, 例如礼物ID, 头像框ID, 房间背景ID, ... */
  item_id: string;
  /** 奖励配置项名称, 例如: 礼物的名称 / 头像框的名称. 这是缺省名称, 如果 oms 需要国际化显示则需要提供 i18n_category_name 字段. */
  item_name: string;
  /** 国家化的奖励配置项名称, key 的取值有 zh / en / ar / tr ... */
  i18n_item_name: { [key: string]: string };
  /** 奖励配置项图标, 例如: 礼物的图标 / 头像框的图标. 这是缺省图标, 如果 oms 需要国际化显示则需要提供 i18n_category_icon 字段. */
  item_icon: string;
  /** 国家化的奖励配置项图标, key 的取值有 zh / en / ar / tr ... */
  i18n_item_icon: { [key: string]: string };
  /** 奖励配置项动画资源 */
  item_animation: string;
  /** 子类型,具体业务决定, 对应 goods的 goods_type \ badge 的 badge_type \ gift 的 gift_type */
  reward_sub_type: string;
  /** 属于子分类的id */
  sub_category_ids: string[];
  /** 拓展信息, 应当设计成一个 JSON 便于后续拓展. */
  expand: string;
  /** 以下部分是活动管理后台用 */
  creator: string;
  modifier: string;
  remark: string;
  /** 奖励等级，具体业务决定, 管理后台过滤用，goods 类型对应 grade，gift类型对应 gift level */
  item_grade: string;
  /** 奖励标签，具体业务决定，管理后台过滤用 */
  item_tag: string;
  /** 数量 */
  num: number;
  /** 有效期(秒) */
  expire_secs: number;
  /** 奖励分类 */
  category_key: string;
  /** 奖励目标类型 */
  reward_type: RankRewardTargetType;
}

export interface RewardItem_I18nItemNameEntry {
  key: string;
  value: string;
}

export interface RewardItem_I18nItemIconEntry {
  key: string;
  value: string;
}

export interface RankRewardObject {
  /** 榜单奖励Id */
  id: number;
  /** 活动Id */
  activity_id: number;
  /** 榜单Id */
  rank_id: number;
  /** 奖励区间-最小值(包含) */
  min_value: number;
  /** 奖励区间-最大值(包含) */
  max_value: number;
  /** 发放此奖励所需的最低榜单得分, 0 表示无限制. */
  min_score: number;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时 */
  updated_at: number;
  /** 父奖励id */
  parent_id: number;
  /** 是否为支持者奖励 */
  is_for_supporter: boolean;
  childs: RankRewardObject[];
}

export interface RankRewardDetailObject {
  /** 榜单奖励详情Id */
  id: number;
  /** 活动Id */
  activity_id: number;
  /** 榜单Id */
  rank_id: number;
  /** 榜单奖励Id */
  rank_reward_id: number;
  /** 奖励类型(1:空奖励,2:奖励包,3:动效奖励) */
  reward_type: RewardType;
  /** 奖励内容(奖励配置的json序列化字符串;eg:RewardPackage,RewardAnimationEffect) */
  reward_object_json: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时 */
  updated_at: number;
}

export interface BatchUpdateRankReward {
  /** 榜单奖励 */
  update_rank_reward: UpdateRankReward | undefined;
  /** 修改-榜单奖励详细配置 */
  update_rank_reward_details: UpdateRankRewardDetail[];
  /** 删除-榜单奖励详情配置-榜单奖励详情Id */
  delete_rank_reward_detail_ids: number[];
  /** 支持者奖励 */
  sub_rank_rewards: BatchUpdateRankReward[];
}

export interface UpdateRankReward {
  /** 榜单奖励Id(0:新增) */
  id: number;
  /** 奖励区间-最小值(包含) */
  min_value: number;
  /** 奖励区间-最大值(包含) */
  max_value: number;
  /** 发放此奖励所需的最低榜单得分, 0 表示无限制. */
  min_score: number;
}

export interface UpdateRankRewardDetail {
  /** 榜单奖励详情Id(0:新增) */
  rank_reward_detail_id: number;
  /** 奖励类型(1:空奖励,2:奖励包,3:动效奖励) */
  reward_type: RewardType;
  /** 奖励内容 */
  reward_object_json: string;
}

export interface ListRankRewardObject {
  /** 榜单奖励, 区间 */
  rank_reward: RankRewardObject | undefined;
  /** 榜单奖励详情 */
  rank_reward_details: RankRewardDetailObject[];
  /** 支持者榜单奖励详情 */
  sub_rank_rewards: ListRankRewardObject[];
}

/** 奖励内容数据结构定义-空奖励 */
export interface RewardEmpty {}

/** 奖励内容数据结构定义-奖励包 */
export interface RewardPackage {
  /** 奖励包Id */
  package_id: string;
}

/** 奖励内容数据结构定义-动效奖励 */
export interface RewardAnimationEffect {
  /** 动效地址 */
  animation_effect_url: string;
  /** 播放位置(1.所有房间,2.用户所在房间,3.所有界面) */
  play_location: PlayLocation;
  /** 字体颜色 类似 #000000 */
  text_color: string;
  /** svga图片 */
  images: MgrSvgaImage[];
}

export interface MgrSvgaImage {
  /** 约定好的key(eg:ar_title:阿语,en_title:英语) */
  key: string;
  /** 图片地址 */
  url: string;
}

/** 活动任务 */
export interface TaskInfo {
  /** 活动任务配置 */
  task_config: TaskConfig | undefined;
  /** 活动子任务配置 */
  task_sub_configs: TaskSubConfig[];
  /** 活动任务奖励配置 */
  task_reward_configs: TaskRewardConfig[];
  /** 活动任务h5配置, 前端用，后端不用 */
  task_h5_configs: TaskH5Config[];
}

/** 活动任务配置 */
export interface TaskConfig {
  /** 任务ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务场景 */
  scenes: string[];
  /** 任务代码 */
  task_code: string;
  /** 任务名称 */
  task_name: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 任务周期 */
  task_cycle: TaskCycle;
  /** 国家编码(计算时区) */
  cou: string;
  /** 去完成任务deeplink */
  to_finish_deeplink: string;
  /** 礼物白名单 */
  gift_white_list: string[];
  /** 用户送礼白名单 */
  user_send_white_list: number[];
  /** 用户送礼黑名单 */
  user_send_black_list: number[];
  /** 是否统计给自己送礼(0:统计,1:不统计) */
  exclude_self: number;
  /** 用户收礼白名单 */
  user_receive_white_list: number[];
  /** 用户收礼黑名单 */
  user_receive_black_list: number[];
  /** 房间收礼白名单 */
  room_white_list: number[];
  /** 房间收礼黑名单 */
  room_black_list: number[];
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 版本，1是旧平台，2是新平台 */
  ver: number;
  /** 房间类型白名单 */
  special_room_type: string[];
  /** 任务模式 */
  task_mode: TaskMode;
  /** 游戏code白名单 */
  game_code_white_list: string[];
  /** 最大可完成次数 */
  max_complete_times: number;
  /** 开关位图，定义：TaskSwitch */
  switch_bits: number;
  /** 任务可见人群 */
  specify_participants: SpecifyParticipants;
  /** 任务描述 */
  task_desc: string;
  /** 收礼礼物白名单 */
  rec_gift_white_list: string[];
  /** 关联的抽奖id */
  relate_lottery_id: number;
  /** 自动领奖 */
  auto_reward: boolean;
  /** 配置状态：normal disable del */
  status: string;
  /** 禁止批量领奖 */
  disable_take_reward_batch: boolean;
  /** 前端组件类型，前端用 */
  component_type: string;
  /** 周期性发奖，周期为task_cycle，不能跟自动领奖同时开启 */
  cycle_settle: TaskCycleSettleType;
  /** 生效时间段 */
  time_slot: TimeSlot | undefined;
  /** 排序值 */
  sort: number;
  /** 前端组件id，仅前端用 */
  task_component_id: number;
  /** 用户白名单bw信息 */
  user_white_bw_info: BWInfo | undefined;
  /** 用户黑名单bw信息 */
  user_black_bw_info: BWInfo | undefined;
  /** 可见人群白名单bw信息 */
  show_user_white_bw_info: BWInfo | undefined;
  /** 可见人群黑名单bw信息 */
  show_user_black_bw_info: BWInfo | undefined;
  /** 房主白名单bw信息 */
  room_owner_white_bw_info: BWInfo | undefined;
  /** 房主黑名单bw信息 */
  room_owner_black_bw_info: BWInfo | undefined;
  /** 任务不可见人群 */
  specify_black_participants: SpecifyParticipants;
  /** 已经选择的子分类 */
  selected_gift_sub_categorys: RewardSubCategory[];
  /** 已经选择的子分类，收到礼物 */
  rec_selected_gift_subcategorys: RewardSubCategory[];
}

/** 黑白名单信息 */
export interface BWInfo {
  /** 用户id (填) */
  ids: number[];
  /** 黑白名单id列表, 人群包id列表（选） */
  bw_ids: string[];
  /** 黑白名单label列表, category = LABEL_CATEGORY_USER */
  labels: string[];
  /** 黑白名单label列表, category = LABEL_CATEGORY_USER_REALTIME */
  realtime_labels: string[];
}

/** 活动任务-子任务配置 */
export interface TaskSubConfig {
  /** 子任务ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 任务类型 */
  task_type: TaskType;
  /** 分数累加类型 */
  task_score_type: TaskScoreType;
  /** 任务名称 */
  task_name: string;
  /** 任务完成目标值 */
  task_target_value: number;
  /** 完成任务路由 */
  to_finish_deeplink: string;
  /** 扩展json， 阶段任务的时候是StageTaskExtendInfo */
  extend_json: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 版本，1是旧平台，2是新平台 */
  ver: number;
  /** 指定币种对应的分数比例, 1: 收礼获得的货币 2: 送礼消耗的货币 3: 币商使用的货币 4: 美金钱包 */
  currency_ratio: { [key: string]: number };
  /** 任务描述多语言 */
  task_desc: string;
  /** 房间模式白名单 */
  special_room_mode: string[];
  /** 任务限制值，有效上麦时长用到 */
  task_limit_value: number;
  /** ButtonH5Config jump_button = 20; // 跳转按钮配置 */
  btn_text: string;
  /** 字体颜色 */
  btn_font_color: string;
  /** 字体大小 */
  btn_font_size: number;
  /** 背景颜色 */
  btn_bg_color: string;
  /** 背景图片 */
  btn_bg_image: string;
  /** 任务场景 */
  scenes: string[];
  /** 游戏code白名单 */
  game_code_white_list: string[];
  /** 充值来源 */
  recharge_source: RechargeSource[];
  /** 过滤表达式 */
  filter_expression: string;
  /** 礼物白名单 */
  gift_white_list: string[];
  /** 收礼礼物白名单 */
  rec_gift_white_list: string[];
  /** 礼物类型白名单 */
  gift_types: string[];
  /** 指定礼物分数比例，优先级比 currency_ratio 高 */
  special_gift_currency_ratio: SpecialGiftCurrencyRatio[];
  /** 是否开启背包礼物计算，只有礼物榜单生效 */
  enable_backpack_gift: boolean;
  /** 是否排除房主贡献值，只有房间任务生效 */
  exclude_room_owner: boolean;
  /** 过滤条件表达式id */
  filter_expression_id: number;
  /** 抽奖id数组，抽奖任务用，只有抽奖任务生效 */
  lottery_ids: number[];
}

export interface TaskSubConfig_CurrencyRatioEntry {
  key: string;
  value: number;
}

export interface SpecialGiftCurrencyRatio {
  /** 礼物id */
  id: string;
  /** 货币-》比例, 注意：如果 key 为 999 则表示数量 */
  ratio: { [key: number]: number };
}

export interface SpecialGiftCurrencyRatio_RatioEntry {
  key: number;
  value: number;
}

export interface StageTaskExtendInfo {
  /** 阶段目标值 */
  target_values: number[];
}

export interface BizActivityEventTaskExtendInfo {
  /** 阶段目标值,阶段任务用 */
  target_values: number[];
  /** 活动标签id */
  event_tag_ids: number[];
}

/** 活动任务奖励配置 */
export interface TaskRewardConfig {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 奖励类型(2:奖励包) */
  reward_type: TaskRewardType;
  /** 奖励内容 */
  reward_object_json: string;
  /** 扩展json */
  extend_json: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 阶段性任务用 */
  stage: number;
  /** 每日库存数量 */
  daily_inventory: number;
  /** 如果这个不为空，发放奖励的时候会推送系统消息给用户，json存 */
  sys_msg_i18n: { [key: string]: string };
}

export interface TaskRewardConfig_SysMsgI18nEntry {
  key: string;
  value: string;
}

/** 活动任务h5配置 */
export interface TaskH5Config {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  task_id: number;
  /** 阶段性任务用 */
  stage: number;
  /** 关闭状态宝箱icon */
  icon_close: string;
  /** 打开状态宝箱icon */
  icon_open: string;
  /** 领取完毕的宝箱icon */
  icon_received: string;
  /** 主标题 */
  title: string;
  /** 副标题 */
  sub_title: string;
  /** 扩展json */
  extend_json: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
}

/** 任务奖励内容-空奖励 */
export interface TaskRewardEmpty {}

/** 任务奖励内容-奖励包 */
export interface TaskRewardPackage {
  /** 奖励包Id */
  package_id: string;
}

/** 任务奖励内容-榜单积分 */
export interface TaskRewardRankPoints {
  /** 榜单id */
  rank_id: number;
  /** 要增加的积分 */
  score: number;
  /** 积分单位，目前时长类的榜单会用到 */
  unit: TaskRewardRankPointsUnit;
}

/** 任务奖励内容-抽奖机会 */
export interface TaskRewardLotteryChance {
  /** 抽奖id */
  lottery_id: number;
  /** 要增加的抽奖次数 */
  times: number;
}

/** 通用奖励配置的外层结构 */
export interface RewardGenerics {
  items: RewardGeneric[];
}

/** 通用奖励配置项 */
export interface RewardGeneric {
  reward_category: RewardCategory | undefined;
  /** 奖励配置项 */
  reward_item: RewardItem | undefined;
  /** 发放数量 */
  amount: number;
  /** 发放数量单位, 例如: 分钟 / 小时 / 天 */
  amount_unit: AmountUnit;
  /** 发放时长, 单位: 秒, AMOUNT_TYPE_QUANTITY_AND_DURATION 类型用，其他类型不用 */
  duration: number;
  /** 发放时长, 单位: 秒 */
  duration_unit: AmountUnit;
}

export interface PKInfo {
  pk_config: PKConfig | undefined;
  details: PKDetail[];
}

export interface PKConfig {
  id: number;
  /** 活动Id */
  activity_id: number;
  title: string;
  /** 榜单国家编码（计算时区） */
  cou: string;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** pk 类型 */
  pk_type: PKType;
  /** 分数累计类型 */
  score_type: ScoreType;
  /** 指定币种对应的分数比例, 1: 收礼获得的货币 2: 送礼消耗的货币 3: 币商使用的货币 4: 美金钱包 */
  currency_ratio: { [key: number]: number };
  /** 指定送礼礼物ID */
  special_gifts: string[];
  /** 指定收礼礼物ID */
  special_rec_gifts: string[];
  /** 关联榜单id */
  relate_rank_id: number;
  /** 配置状态：normal disable del */
  status: string;
  /** 备注 */
  remark: string;
  /** 创建人 */
  create_by: string;
  /** 最后更新人 */
  update_by: string;
  /** 删除人 */
  delete_by: string;
  /** 创建时间 */
  ctime: number;
  /** 删除时间 */
  dtime: number;
  /** 更新时间 */
  utime: number;
  /** 指定房间pk模式, 房内1v1: 1v1, 房内多人：multi, 跨房多人: cross_room_multi */
  special_room_pk_modes: string[];
  /** 挂件开关 */
  pendant_switch: Switch;
  /** 挂件背景图多语言json格式{"en":"xx.jpg","ar":"aa.jpg"} */
  pendant_background_images: string;
  /** 指定礼物分数比例，优先级比 currency_ratio 高 */
  special_gift_currency_ratio: SpecialGiftCurrencyRatio[];
  /** 前端组件类型，前端用 */
  component_type: string;
  /** 分数比例 */
  ratio: number;
}

export interface PKConfig_CurrencyRatioEntry {
  key: number;
  value: number;
}

export interface PKDetail {
  id: number;
  /** 活动Id */
  activity_id: number;
  pk_id: number;
  players: PKPlayer[];
  rounds: PKRound[];
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  create_by: string;
  /** 最后更新人 */
  update_by: string;
  /** 删除人 */
  delete_by: string;
  /** 创建时间 */
  ctime: number;
  /** 删除时间 */
  dtime: number;
  /** 更新时间 */
  utime: number;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 结算时间 */
  settle_time: number;
  /** 结算状态 */
  settle_status: PKDetailSettleStatus;
}

export interface PKRound {
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 对应业务的pk id，过滤消息用 */
  biz_pk_id: string;
}

export interface PKPlayer {
  member_id: string;
  /** 指定房间，如果不指定，所有房间都算 */
  special_room_id: number;
  /** 参赛用户列表 */
  player_ids: string[];
  /** 位置 */
  position: number;
}

export interface GuildInfo {
  /** 公会ID */
  id: number;
  /** 公会名称 */
  name: string;
  /** 公会LOGO */
  avatar: string;
}

export interface FamilyInfo {
  /** 家族ID */
  id: number;
  /** 家族名称 */
  name: string;
  /** 家族头像 */
  avatar: string;
}

/** 抽奖配置 */
export interface LotteryInfo {
  config: LotteryConfig | undefined;
  reward_configs: LotteryRewardConfig[];
  relate_task_infos: TaskInfo[];
}

export interface LotteryConfig {
  id: number;
  activity_id: number;
  title: string;
  cou: string;
  start_time: number;
  end_time: number;
  /** 配置状态：normal disable del */
  status: string;
  /** 备注 */
  remark: string;
  /** 任务可见人群 */
  specify_participants: SpecifyParticipants;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 前端组件类型，前端用 */
  component_type: LotteryComponentType;
  /** 用户白名单bw信息 */
  show_user_white_bw_info: BWInfo | undefined;
  /** 用户黑名单bw信息 */
  show_user_black_bw_info: BWInfo | undefined;
  /** 任务不可见人群 */
  specify_black_participants: SpecifyParticipants;
  /** 付费抽奖规则 */
  paid_rules: LotteryPaidRule[];
  /** 抽奖类型 */
  lottery_type: LotteryType;
}

/** 抽奖奖励配置 */
export interface LotteryRewardConfig {
  /** 奖励ID */
  id: number;
  /** 活动ID */
  activity_id: number;
  /** 任务ID */
  lottery_id: number;
  /** 奖励类型(2:奖励包) */
  reward_type: TaskRewardType;
  /** 奖励内容 */
  reward_object_json: string;
  /** 扩展json */
  extend_json: string;
  /** 是否会触发客户端横幅 */
  show_banner: boolean;
  /** 是否会触发h5弹幕 */
  show_barrage: boolean;
  /** 客户端横幅配置，样式svga、字体颜色、用户昵称字体颜色、横幅展示位置等，对应下方的RewardBannerConfig */
  banner_config: RewardBannerConfig | undefined;
  /** 抽奖权重，抽奖算概率用 */
  weight: number;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 排序值，越小越前显示 */
  sort: number;
  /** 库存限制 */
  stock: number;
  /** 库存限制周期 */
  stock_cycle: CycleType;
  /** 每个人抽中次数限制 */
  limit: number;
  /** 每个人抽中次数限制周期 */
  limit_cycle: CycleType;
  /** 满多少次必抽中 */
  must_hit_times: number[];
  /** 奖励自定义标题 */
  title: string;
  /** 奖励自定义图片 */
  img: string;
}

/** 奖励横幅配置 */
export interface RewardBannerConfig {
  /** 样式svga */
  svga: { [key: string]: string };
  /** 字体颜色 */
  font_color: string;
  /** 用户昵称字体颜色 */
  nickname_font_color: string;
  /** 横幅展示位置 */
  banner_position: string;
}

export interface RewardBannerConfig_SvgaEntry {
  key: string;
  value: string;
}

export interface RoomRecommendConfig {
  id: number;
  activity_id: number;
  /** 指定房间id、指定用户白名单, = 1 的时候，需要后端转成房间id */
  recommend_type: RoomRecommendType;
  /** 个人房\家族房 */
  room_type_list: RoomIDType[];
  /** 指定白名单id */
  white_list_id: string;
  /** 房间id白名单列表 */
  room_id_white_list: number[];
  /** 组件展示对象 */
  display_info: RoomRecommendDisplayInfo;
  /** 房间介绍 */
  room_desc_map: { [key: string]: string };
  /** 组件类型，前端用 */
  component_type: RoomRecommendComponentType;
  /** 黑白名单分类，USER\ROOM，recommend_type== 1 的时候用 */
  white_list_category: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
}

export interface RoomRecommendConfig_RoomDescMapEntry {
  key: string;
  value: string;
}

/** 房间推荐配置 */
export interface RoomRecommendInfo {
  config: RoomRecommendConfig | undefined;
}

export interface UserRoomInfo {
  /** 用户id */
  uid: number;
  /** 房间id */
  room_id: number;
  /** 房间类型 */
  room_type: RoomIDType;
}

/** 展示配置 */
export interface PresentationConfig {
  id: number;
  activity_id: number;
  items: PresentationConfigItem[];
  /** 前端组件类型 */
  component_type: PresentationComponentType;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
}

export interface PresentationConfigItem {
  /** 用户id 列表 */
  uids: number[];
  /** 标题 */
  title: string;
  /** 描述 */
  desc: string;
}

export interface SavePresentationConfig {
  config: PresentationConfig | undefined;
}

/** 时间段 */
export interface TimeSlot {
  /** 时间粒度 */
  type: TimeSlotType;
  /** 如果type是小时，那么对应的是小时列表 */
  slots: number[];
}

/** 奖励子分类 RewardSubCategory */
export interface RewardSubCategory {
  /** 子分类的id */
  sub_category_id: string;
  /** 子分类的名字 */
  sub_category_name: string;
}

/** 奖池配置 */
export interface PrizePoolInfo {
  config: PrizePoolConfig | undefined;
  relate_rank_info: RankInfo | undefined;
}

export interface PrizePoolConfig {
  id: number;
  activity_id: number;
  /** 奖池累加类型 */
  cal_type: PrizePoolCalType;
  /** true：自动发奖，false：运营自己导出，然后发奖 */
  auto_reward: boolean;
  /** 奖励配置项key, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_category_key: string;
  /** 奖励配置项ID, 对应 common reward, 只支持发放个数类型的奖励 */
  reward_item_id: string;
  /** 最小榜单分,用户榜单分数大于它，才可以参加奖池总数计算 */
  min_rank_score: number;
  /** 最大奖池数量 */
  max_pool_num: number;
  /** 奖池瓜分权重类型 */
  weight_type: PrizePoolWeightType;
  /** 奖池瓜分权重列表,json 存数据库 */
  weight_config_list: PrizePoolWeight[];
  /** 关联的榜单id */
  relate_rank_id: number;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
  /** 周期 */
  cycle: CycleType;
  /** 比例值，奖池分数：所有上榜用户的榜单总分*比例值 */
  pool_ratio: number;
  /** 瓜分奖池限制，用户榜单分数大于它，才可以参加奖池瓜分 */
  prize_rank_score_limit: number;
  /** 前端组件类型 */
  component_type: string;
  /** 配置状态：normal disable del */
  status: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 前端组件id，仅前端用 */
  component_id: number;
}

function createBaseActivityInfo(): ActivityInfo {
  return {
    activity: undefined,
    rank_list: [],
    task_list: [],
    pk_list: [],
    lottery_list: [],
    room_recommend_list: [],
    presentation_list: [],
    prize_pool_list: []
  };
}

export const ActivityInfo: MessageFns<ActivityInfo> = {
  fromJSON(object: any): ActivityInfo {
    return {
      activity: isSet(object.activity) ? Activity.fromJSON(object.activity) : undefined,
      rank_list: globalThis.Array.isArray(object?.rank_list)
        ? object.rank_list.map((e: any) => RankInfo.fromJSON(e))
        : [],
      task_list: globalThis.Array.isArray(object?.task_list)
        ? object.task_list.map((e: any) => TaskInfo.fromJSON(e))
        : [],
      pk_list: globalThis.Array.isArray(object?.pk_list) ? object.pk_list.map((e: any) => PKInfo.fromJSON(e)) : [],
      lottery_list: globalThis.Array.isArray(object?.lottery_list)
        ? object.lottery_list.map((e: any) => LotteryInfo.fromJSON(e))
        : [],
      room_recommend_list: globalThis.Array.isArray(object?.room_recommend_list)
        ? object.room_recommend_list.map((e: any) => RoomRecommendInfo.fromJSON(e))
        : [],
      presentation_list: globalThis.Array.isArray(object?.presentation_list)
        ? object.presentation_list.map((e: any) => SavePresentationConfig.fromJSON(e))
        : [],
      prize_pool_list: globalThis.Array.isArray(object?.prize_pool_list)
        ? object.prize_pool_list.map((e: any) => PrizePoolInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ActivityInfo>, I>>(base?: I): ActivityInfo {
    return ActivityInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ActivityInfo>, I>>(object: I): ActivityInfo {
    const message = createBaseActivityInfo();
    message.activity =
      object.activity !== undefined && object.activity !== null ? Activity.fromPartial(object.activity) : undefined;
    message.rank_list = object.rank_list?.map(e => RankInfo.fromPartial(e)) || [];
    message.task_list = object.task_list?.map(e => TaskInfo.fromPartial(e)) || [];
    message.pk_list = object.pk_list?.map(e => PKInfo.fromPartial(e)) || [];
    message.lottery_list = object.lottery_list?.map(e => LotteryInfo.fromPartial(e)) || [];
    message.room_recommend_list = object.room_recommend_list?.map(e => RoomRecommendInfo.fromPartial(e)) || [];
    message.presentation_list = object.presentation_list?.map(e => SavePresentationConfig.fromPartial(e)) || [];
    message.prize_pool_list = object.prize_pool_list?.map(e => PrizePoolInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseActivity(): Activity {
  return {
    id: 0,
    name: '',
    start_time: 0,
    end_time: 0,
    display_gifts: [],
    display_rewards: '',
    remark: '',
    status: '',
    create_by: '',
    update_by: '',
    delete_by: '',
    dtime: 0,
    utime: 0,
    ctime: 0,
    url: '',
    is_template: false,
    activity_code: '',
    application_code: '',
    cou: '',
    time_zone: ''
  };
}

export const Activity: MessageFns<Activity> = {
  fromJSON(object: any): Activity {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      display_gifts: globalThis.Array.isArray(object?.display_gifts)
        ? object.display_gifts.map((e: any) => globalThis.String(e))
        : [],
      display_rewards: isSet(object.display_rewards) ? globalThis.String(object.display_rewards) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : '',
      dtime: isSet(object.dtime) ? globalThis.Number(object.dtime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      is_template: isSet(object.is_template) ? globalThis.Boolean(object.is_template) : false,
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      application_code: isSet(object.application_code) ? globalThis.String(object.application_code) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      time_zone: isSet(object.time_zone) ? globalThis.String(object.time_zone) : ''
    };
  },

  create<I extends Exact<DeepPartial<Activity>, I>>(base?: I): Activity {
    return Activity.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Activity>, I>>(object: I): Activity {
    const message = createBaseActivity();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.display_gifts = object.display_gifts?.map(e => e) || [];
    message.display_rewards = object.display_rewards ?? '';
    message.remark = object.remark ?? '';
    message.status = object.status ?? '';
    message.create_by = object.create_by ?? '';
    message.update_by = object.update_by ?? '';
    message.delete_by = object.delete_by ?? '';
    message.dtime = object.dtime ?? 0;
    message.utime = object.utime ?? 0;
    message.ctime = object.ctime ?? 0;
    message.url = object.url ?? '';
    message.is_template = object.is_template ?? false;
    message.activity_code = object.activity_code ?? '';
    message.application_code = object.application_code ?? '';
    message.cou = object.cou ?? '';
    message.time_zone = object.time_zone ?? '';
    return message;
  }
};

function createBaseRankInfo(): RankInfo {
  return { rank_config: undefined, list_rank_rewards: [], list_supporter_rank_rewards: [] };
}

export const RankInfo: MessageFns<RankInfo> = {
  fromJSON(object: any): RankInfo {
    return {
      rank_config: isSet(object.rank_config) ? RankConfig.fromJSON(object.rank_config) : undefined,
      list_rank_rewards: globalThis.Array.isArray(object?.list_rank_rewards)
        ? object.list_rank_rewards.map((e: any) => ListRankRewardObject.fromJSON(e))
        : [],
      list_supporter_rank_rewards: globalThis.Array.isArray(object?.list_supporter_rank_rewards)
        ? object.list_supporter_rank_rewards.map((e: any) => ListRankRewardObject.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RankInfo>, I>>(base?: I): RankInfo {
    return RankInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankInfo>, I>>(object: I): RankInfo {
    const message = createBaseRankInfo();
    message.rank_config =
      object.rank_config !== undefined && object.rank_config !== null
        ? RankConfig.fromPartial(object.rank_config)
        : undefined;
    message.list_rank_rewards = object.list_rank_rewards?.map(e => ListRankRewardObject.fromPartial(e)) || [];
    message.list_supporter_rank_rewards =
      object.list_supporter_rank_rewards?.map(e => ListRankRewardObject.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRankConfig(): RankConfig {
  return {
    id: 0,
    activity_id: 0,
    cycle: 0,
    rank_type: 0,
    show_top: 0,
    source: 0,
    scenes: [],
    score_type: 0,
    ratio: 0,
    currency_ratio: {},
    regions: [],
    cou: '',
    time_zone: '',
    start_time: 0,
    end_time: 0,
    valid_periods: [],
    gift_types: [],
    room_types: [],
    special_gifts: [],
    special_rooms: [],
    special_guilds: [],
    uid_black_list: [],
    room_id_black_list: [],
    guild_id_black_list: [],
    game_code_white_list: [],
    remark: '',
    status: '',
    create_by: '',
    update_by: '',
    utime: 0,
    ctime: 0,
    special_families: [],
    family_id_black_list: [],
    special_family_roles: [],
    special_guild_roles: [],
    rank_show_type: 0,
    special_input: [],
    special_input_type: 0,
    show_history_period: 0,
    bottom_show_room_type: 0,
    special_room_modes: [],
    special_rec_gifts: [],
    promotion_num: 0,
    uid_black_list_ids: [],
    pendant_switch: 0,
    pendant_background_images: '',
    component_type: '',
    limit_value: '',
    min_score: 0,
    max_score: 0,
    filter_expression: '',
    name: '',
    user_white_bw_info: undefined,
    family_owner_bw_info: undefined,
    special_gift_currency_ratio: [],
    selected_gift_sub_categorys: [],
    rec_selected_gift_subcategorys: [],
    enable_backpack_gift: false,
    exclude_room_owner: false,
    filter_expression_id: 0,
    user_black_bw_info: undefined
  };
}

export const RankConfig: MessageFns<RankConfig> = {
  fromJSON(object: any): RankConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      cycle: isSet(object.cycle) ? cycleTypeFromJSON(object.cycle) : 0,
      rank_type: isSet(object.rank_type) ? rankTypeFromJSON(object.rank_type) : 0,
      show_top: isSet(object.show_top) ? globalThis.Number(object.show_top) : 0,
      source: isSet(object.source) ? rankSourceFromJSON(object.source) : 0,
      scenes: globalThis.Array.isArray(object?.scenes) ? object.scenes.map((e: any) => globalThis.String(e)) : [],
      score_type: isSet(object.score_type) ? scoreTypeFromJSON(object.score_type) : 0,
      ratio: isSet(object.ratio) ? globalThis.Number(object.ratio) : 0,
      currency_ratio: isObject(object.currency_ratio)
        ? Object.entries(object.currency_ratio).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      regions: globalThis.Array.isArray(object?.regions) ? object.regions.map((e: any) => globalThis.String(e)) : [],
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      time_zone: isSet(object.time_zone) ? globalThis.String(object.time_zone) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      valid_periods: globalThis.Array.isArray(object?.valid_periods)
        ? object.valid_periods.map((e: any) => globalThis.Number(e))
        : [],
      gift_types: globalThis.Array.isArray(object?.gift_types)
        ? object.gift_types.map((e: any) => globalThis.String(e))
        : [],
      room_types: globalThis.Array.isArray(object?.room_types)
        ? object.room_types.map((e: any) => globalThis.String(e))
        : [],
      special_gifts: globalThis.Array.isArray(object?.special_gifts)
        ? object.special_gifts.map((e: any) => globalThis.String(e))
        : [],
      special_rooms: globalThis.Array.isArray(object?.special_rooms)
        ? object.special_rooms.map((e: any) => globalThis.Number(e))
        : [],
      special_guilds: globalThis.Array.isArray(object?.special_guilds)
        ? object.special_guilds.map((e: any) => globalThis.Number(e))
        : [],
      uid_black_list: globalThis.Array.isArray(object?.uid_black_list)
        ? object.uid_black_list.map((e: any) => globalThis.Number(e))
        : [],
      room_id_black_list: globalThis.Array.isArray(object?.room_id_black_list)
        ? object.room_id_black_list.map((e: any) => globalThis.Number(e))
        : [],
      guild_id_black_list: globalThis.Array.isArray(object?.guild_id_black_list)
        ? object.guild_id_black_list.map((e: any) => globalThis.Number(e))
        : [],
      game_code_white_list: globalThis.Array.isArray(object?.game_code_white_list)
        ? object.game_code_white_list.map((e: any) => globalThis.String(e))
        : [],
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      special_families: globalThis.Array.isArray(object?.special_families)
        ? object.special_families.map((e: any) => globalThis.Number(e))
        : [],
      family_id_black_list: globalThis.Array.isArray(object?.family_id_black_list)
        ? object.family_id_black_list.map((e: any) => globalThis.Number(e))
        : [],
      special_family_roles: globalThis.Array.isArray(object?.special_family_roles)
        ? object.special_family_roles.map((e: any) => familyRoleTypeFromJSON(e))
        : [],
      special_guild_roles: globalThis.Array.isArray(object?.special_guild_roles)
        ? object.special_guild_roles.map((e: any) => memberRoleFromJSON(e))
        : [],
      rank_show_type: isSet(object.rank_show_type) ? rankShowTypeFromJSON(object.rank_show_type) : 0,
      special_input: globalThis.Array.isArray(object?.special_input)
        ? object.special_input.map((e: any) => globalThis.Number(e))
        : [],
      special_input_type: isSet(object.special_input_type) ? specialInputTypeFromJSON(object.special_input_type) : 0,
      show_history_period: isSet(object.show_history_period) ? globalThis.Number(object.show_history_period) : 0,
      bottom_show_room_type: isSet(object.bottom_show_room_type) ? roomIDTypeFromJSON(object.bottom_show_room_type) : 0,
      special_room_modes: globalThis.Array.isArray(object?.special_room_modes)
        ? object.special_room_modes.map((e: any) => globalThis.String(e))
        : [],
      special_rec_gifts: globalThis.Array.isArray(object?.special_rec_gifts)
        ? object.special_rec_gifts.map((e: any) => globalThis.String(e))
        : [],
      promotion_num: isSet(object.promotion_num) ? globalThis.Number(object.promotion_num) : 0,
      uid_black_list_ids: globalThis.Array.isArray(object?.uid_black_list_ids)
        ? object.uid_black_list_ids.map((e: any) => globalThis.String(e))
        : [],
      pendant_switch: isSet(object.pendant_switch) ? switchFromJSON(object.pendant_switch) : 0,
      pendant_background_images: isSet(object.pendant_background_images)
        ? globalThis.String(object.pendant_background_images)
        : '',
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      limit_value: isSet(object.limit_value) ? globalThis.String(object.limit_value) : '',
      min_score: isSet(object.min_score) ? globalThis.Number(object.min_score) : 0,
      max_score: isSet(object.max_score) ? globalThis.Number(object.max_score) : 0,
      filter_expression: isSet(object.filter_expression) ? globalThis.String(object.filter_expression) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      user_white_bw_info: isSet(object.user_white_bw_info) ? BWInfo.fromJSON(object.user_white_bw_info) : undefined,
      family_owner_bw_info: isSet(object.family_owner_bw_info)
        ? BWInfo.fromJSON(object.family_owner_bw_info)
        : undefined,
      special_gift_currency_ratio: globalThis.Array.isArray(object?.special_gift_currency_ratio)
        ? object.special_gift_currency_ratio.map((e: any) => SpecialGiftCurrencyRatio.fromJSON(e))
        : [],
      selected_gift_sub_categorys: globalThis.Array.isArray(object?.selected_gift_sub_categorys)
        ? object.selected_gift_sub_categorys.map((e: any) => RewardSubCategory.fromJSON(e))
        : [],
      rec_selected_gift_subcategorys: globalThis.Array.isArray(object?.rec_selected_gift_subcategorys)
        ? object.rec_selected_gift_subcategorys.map((e: any) => RewardSubCategory.fromJSON(e))
        : [],
      enable_backpack_gift: isSet(object.enable_backpack_gift)
        ? globalThis.Boolean(object.enable_backpack_gift)
        : false,
      exclude_room_owner: isSet(object.exclude_room_owner) ? globalThis.Boolean(object.exclude_room_owner) : false,
      filter_expression_id: isSet(object.filter_expression_id) ? globalThis.Number(object.filter_expression_id) : 0,
      user_black_bw_info: isSet(object.user_black_bw_info) ? BWInfo.fromJSON(object.user_black_bw_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RankConfig>, I>>(base?: I): RankConfig {
    return RankConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankConfig>, I>>(object: I): RankConfig {
    const message = createBaseRankConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.cycle = object.cycle ?? 0;
    message.rank_type = object.rank_type ?? 0;
    message.show_top = object.show_top ?? 0;
    message.source = object.source ?? 0;
    message.scenes = object.scenes?.map(e => e) || [];
    message.score_type = object.score_type ?? 0;
    message.ratio = object.ratio ?? 0;
    message.currency_ratio = Object.entries(object.currency_ratio ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.regions = object.regions?.map(e => e) || [];
    message.cou = object.cou ?? '';
    message.time_zone = object.time_zone ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.valid_periods = object.valid_periods?.map(e => e) || [];
    message.gift_types = object.gift_types?.map(e => e) || [];
    message.room_types = object.room_types?.map(e => e) || [];
    message.special_gifts = object.special_gifts?.map(e => e) || [];
    message.special_rooms = object.special_rooms?.map(e => e) || [];
    message.special_guilds = object.special_guilds?.map(e => e) || [];
    message.uid_black_list = object.uid_black_list?.map(e => e) || [];
    message.room_id_black_list = object.room_id_black_list?.map(e => e) || [];
    message.guild_id_black_list = object.guild_id_black_list?.map(e => e) || [];
    message.game_code_white_list = object.game_code_white_list?.map(e => e) || [];
    message.remark = object.remark ?? '';
    message.status = object.status ?? '';
    message.create_by = object.create_by ?? '';
    message.update_by = object.update_by ?? '';
    message.utime = object.utime ?? 0;
    message.ctime = object.ctime ?? 0;
    message.special_families = object.special_families?.map(e => e) || [];
    message.family_id_black_list = object.family_id_black_list?.map(e => e) || [];
    message.special_family_roles = object.special_family_roles?.map(e => e) || [];
    message.special_guild_roles = object.special_guild_roles?.map(e => e) || [];
    message.rank_show_type = object.rank_show_type ?? 0;
    message.special_input = object.special_input?.map(e => e) || [];
    message.special_input_type = object.special_input_type ?? 0;
    message.show_history_period = object.show_history_period ?? 0;
    message.bottom_show_room_type = object.bottom_show_room_type ?? 0;
    message.special_room_modes = object.special_room_modes?.map(e => e) || [];
    message.special_rec_gifts = object.special_rec_gifts?.map(e => e) || [];
    message.promotion_num = object.promotion_num ?? 0;
    message.uid_black_list_ids = object.uid_black_list_ids?.map(e => e) || [];
    message.pendant_switch = object.pendant_switch ?? 0;
    message.pendant_background_images = object.pendant_background_images ?? '';
    message.component_type = object.component_type ?? '';
    message.limit_value = object.limit_value ?? '';
    message.min_score = object.min_score ?? 0;
    message.max_score = object.max_score ?? 0;
    message.filter_expression = object.filter_expression ?? '';
    message.name = object.name ?? '';
    message.user_white_bw_info =
      object.user_white_bw_info !== undefined && object.user_white_bw_info !== null
        ? BWInfo.fromPartial(object.user_white_bw_info)
        : undefined;
    message.family_owner_bw_info =
      object.family_owner_bw_info !== undefined && object.family_owner_bw_info !== null
        ? BWInfo.fromPartial(object.family_owner_bw_info)
        : undefined;
    message.special_gift_currency_ratio =
      object.special_gift_currency_ratio?.map(e => SpecialGiftCurrencyRatio.fromPartial(e)) || [];
    message.selected_gift_sub_categorys =
      object.selected_gift_sub_categorys?.map(e => RewardSubCategory.fromPartial(e)) || [];
    message.rec_selected_gift_subcategorys =
      object.rec_selected_gift_subcategorys?.map(e => RewardSubCategory.fromPartial(e)) || [];
    message.enable_backpack_gift = object.enable_backpack_gift ?? false;
    message.exclude_room_owner = object.exclude_room_owner ?? false;
    message.filter_expression_id = object.filter_expression_id ?? 0;
    message.user_black_bw_info =
      object.user_black_bw_info !== undefined && object.user_black_bw_info !== null
        ? BWInfo.fromPartial(object.user_black_bw_info)
        : undefined;
    return message;
  }
};

function createBaseRankConfig_CurrencyRatioEntry(): RankConfig_CurrencyRatioEntry {
  return { key: 0, value: 0 };
}

export const RankConfig_CurrencyRatioEntry: MessageFns<RankConfig_CurrencyRatioEntry> = {
  fromJSON(object: any): RankConfig_CurrencyRatioEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<RankConfig_CurrencyRatioEntry>, I>>(base?: I): RankConfig_CurrencyRatioEntry {
    return RankConfig_CurrencyRatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankConfig_CurrencyRatioEntry>, I>>(
    object: I
  ): RankConfig_CurrencyRatioEntry {
    const message = createBaseRankConfig_CurrencyRatioEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseRewardCategory(): RewardCategory {
  return { category_key: '', category_name: '', i18n_category_name: {}, amount_type: 0 };
}

export const RewardCategory: MessageFns<RewardCategory> = {
  fromJSON(object: any): RewardCategory {
    return {
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      category_name: isSet(object.category_name) ? globalThis.String(object.category_name) : '',
      i18n_category_name: isObject(object.i18n_category_name)
        ? Object.entries(object.i18n_category_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      amount_type: isSet(object.amount_type) ? amountTypeFromJSON(object.amount_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<RewardCategory>, I>>(base?: I): RewardCategory {
    return RewardCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardCategory>, I>>(object: I): RewardCategory {
    const message = createBaseRewardCategory();
    message.category_key = object.category_key ?? '';
    message.category_name = object.category_name ?? '';
    message.i18n_category_name = Object.entries(object.i18n_category_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.amount_type = object.amount_type ?? 0;
    return message;
  }
};

function createBaseRewardCategory_I18nCategoryNameEntry(): RewardCategory_I18nCategoryNameEntry {
  return { key: '', value: '' };
}

export const RewardCategory_I18nCategoryNameEntry: MessageFns<RewardCategory_I18nCategoryNameEntry> = {
  fromJSON(object: any): RewardCategory_I18nCategoryNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardCategory_I18nCategoryNameEntry>, I>>(
    base?: I
  ): RewardCategory_I18nCategoryNameEntry {
    return RewardCategory_I18nCategoryNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardCategory_I18nCategoryNameEntry>, I>>(
    object: I
  ): RewardCategory_I18nCategoryNameEntry {
    const message = createBaseRewardCategory_I18nCategoryNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRewardItem(): RewardItem {
  return {
    item_id: '',
    item_name: '',
    i18n_item_name: {},
    item_icon: '',
    i18n_item_icon: {},
    item_animation: '',
    reward_sub_type: '',
    sub_category_ids: [],
    expand: '',
    creator: '',
    modifier: '',
    remark: '',
    item_grade: '',
    item_tag: '',
    num: 0,
    expire_secs: 0,
    category_key: '',
    reward_type: 0
  };
}

export const RewardItem: MessageFns<RewardItem> = {
  fromJSON(object: any): RewardItem {
    return {
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : '',
      item_name: isSet(object.item_name) ? globalThis.String(object.item_name) : '',
      i18n_item_name: isObject(object.i18n_item_name)
        ? Object.entries(object.i18n_item_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_icon: isSet(object.item_icon) ? globalThis.String(object.item_icon) : '',
      i18n_item_icon: isObject(object.i18n_item_icon)
        ? Object.entries(object.i18n_item_icon).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      item_animation: isSet(object.item_animation) ? globalThis.String(object.item_animation) : '',
      reward_sub_type: isSet(object.reward_sub_type) ? globalThis.String(object.reward_sub_type) : '',
      sub_category_ids: globalThis.Array.isArray(object?.sub_category_ids)
        ? object.sub_category_ids.map((e: any) => globalThis.String(e))
        : [],
      expand: isSet(object.expand) ? globalThis.String(object.expand) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      modifier: isSet(object.modifier) ? globalThis.String(object.modifier) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      item_grade: isSet(object.item_grade) ? globalThis.String(object.item_grade) : '',
      item_tag: isSet(object.item_tag) ? globalThis.String(object.item_tag) : '',
      num: isSet(object.num) ? globalThis.Number(object.num) : 0,
      expire_secs: isSet(object.expire_secs) ? globalThis.Number(object.expire_secs) : 0,
      category_key: isSet(object.category_key) ? globalThis.String(object.category_key) : '',
      reward_type: isSet(object.reward_type) ? rankRewardTargetTypeFromJSON(object.reward_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<RewardItem>, I>>(base?: I): RewardItem {
    return RewardItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem>, I>>(object: I): RewardItem {
    const message = createBaseRewardItem();
    message.item_id = object.item_id ?? '';
    message.item_name = object.item_name ?? '';
    message.i18n_item_name = Object.entries(object.i18n_item_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_icon = object.item_icon ?? '';
    message.i18n_item_icon = Object.entries(object.i18n_item_icon ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.item_animation = object.item_animation ?? '';
    message.reward_sub_type = object.reward_sub_type ?? '';
    message.sub_category_ids = object.sub_category_ids?.map(e => e) || [];
    message.expand = object.expand ?? '';
    message.creator = object.creator ?? '';
    message.modifier = object.modifier ?? '';
    message.remark = object.remark ?? '';
    message.item_grade = object.item_grade ?? '';
    message.item_tag = object.item_tag ?? '';
    message.num = object.num ?? 0;
    message.expire_secs = object.expire_secs ?? 0;
    message.category_key = object.category_key ?? '';
    message.reward_type = object.reward_type ?? 0;
    return message;
  }
};

function createBaseRewardItem_I18nItemNameEntry(): RewardItem_I18nItemNameEntry {
  return { key: '', value: '' };
}

export const RewardItem_I18nItemNameEntry: MessageFns<RewardItem_I18nItemNameEntry> = {
  fromJSON(object: any): RewardItem_I18nItemNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardItem_I18nItemNameEntry>, I>>(base?: I): RewardItem_I18nItemNameEntry {
    return RewardItem_I18nItemNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem_I18nItemNameEntry>, I>>(object: I): RewardItem_I18nItemNameEntry {
    const message = createBaseRewardItem_I18nItemNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRewardItem_I18nItemIconEntry(): RewardItem_I18nItemIconEntry {
  return { key: '', value: '' };
}

export const RewardItem_I18nItemIconEntry: MessageFns<RewardItem_I18nItemIconEntry> = {
  fromJSON(object: any): RewardItem_I18nItemIconEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardItem_I18nItemIconEntry>, I>>(base?: I): RewardItem_I18nItemIconEntry {
    return RewardItem_I18nItemIconEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardItem_I18nItemIconEntry>, I>>(object: I): RewardItem_I18nItemIconEntry {
    const message = createBaseRewardItem_I18nItemIconEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRankRewardObject(): RankRewardObject {
  return {
    id: 0,
    activity_id: 0,
    rank_id: 0,
    min_value: 0,
    max_value: 0,
    min_score: 0,
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    parent_id: 0,
    is_for_supporter: false,
    childs: []
  };
}

export const RankRewardObject: MessageFns<RankRewardObject> = {
  fromJSON(object: any): RankRewardObject {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      min_value: isSet(object.min_value) ? globalThis.Number(object.min_value) : 0,
      max_value: isSet(object.max_value) ? globalThis.Number(object.max_value) : 0,
      min_score: isSet(object.min_score) ? globalThis.Number(object.min_score) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      parent_id: isSet(object.parent_id) ? globalThis.Number(object.parent_id) : 0,
      is_for_supporter: isSet(object.is_for_supporter) ? globalThis.Boolean(object.is_for_supporter) : false,
      childs: globalThis.Array.isArray(object?.childs)
        ? object.childs.map((e: any) => RankRewardObject.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<RankRewardObject>, I>>(base?: I): RankRewardObject {
    return RankRewardObject.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankRewardObject>, I>>(object: I): RankRewardObject {
    const message = createBaseRankRewardObject();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.rank_id = object.rank_id ?? 0;
    message.min_value = object.min_value ?? 0;
    message.max_value = object.max_value ?? 0;
    message.min_score = object.min_score ?? 0;
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.parent_id = object.parent_id ?? 0;
    message.is_for_supporter = object.is_for_supporter ?? false;
    message.childs = object.childs?.map(e => RankRewardObject.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRankRewardDetailObject(): RankRewardDetailObject {
  return {
    id: 0,
    activity_id: 0,
    rank_id: 0,
    rank_reward_id: 0,
    reward_type: 0,
    reward_object_json: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const RankRewardDetailObject: MessageFns<RankRewardDetailObject> = {
  fromJSON(object: any): RankRewardDetailObject {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      rank_reward_id: isSet(object.rank_reward_id) ? globalThis.Number(object.rank_reward_id) : 0,
      reward_type: isSet(object.reward_type) ? rewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<RankRewardDetailObject>, I>>(base?: I): RankRewardDetailObject {
    return RankRewardDetailObject.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankRewardDetailObject>, I>>(object: I): RankRewardDetailObject {
    const message = createBaseRankRewardDetailObject();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.rank_id = object.rank_id ?? 0;
    message.rank_reward_id = object.rank_reward_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseBatchUpdateRankReward(): BatchUpdateRankReward {
  return {
    update_rank_reward: undefined,
    update_rank_reward_details: [],
    delete_rank_reward_detail_ids: [],
    sub_rank_rewards: []
  };
}

export const BatchUpdateRankReward: MessageFns<BatchUpdateRankReward> = {
  fromJSON(object: any): BatchUpdateRankReward {
    return {
      update_rank_reward: isSet(object.update_rank_reward)
        ? UpdateRankReward.fromJSON(object.update_rank_reward)
        : undefined,
      update_rank_reward_details: globalThis.Array.isArray(object?.update_rank_reward_details)
        ? object.update_rank_reward_details.map((e: any) => UpdateRankRewardDetail.fromJSON(e))
        : [],
      delete_rank_reward_detail_ids: globalThis.Array.isArray(object?.delete_rank_reward_detail_ids)
        ? object.delete_rank_reward_detail_ids.map((e: any) => globalThis.Number(e))
        : [],
      sub_rank_rewards: globalThis.Array.isArray(object?.sub_rank_rewards)
        ? object.sub_rank_rewards.map((e: any) => BatchUpdateRankReward.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchUpdateRankReward>, I>>(base?: I): BatchUpdateRankReward {
    return BatchUpdateRankReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchUpdateRankReward>, I>>(object: I): BatchUpdateRankReward {
    const message = createBaseBatchUpdateRankReward();
    message.update_rank_reward =
      object.update_rank_reward !== undefined && object.update_rank_reward !== null
        ? UpdateRankReward.fromPartial(object.update_rank_reward)
        : undefined;
    message.update_rank_reward_details =
      object.update_rank_reward_details?.map(e => UpdateRankRewardDetail.fromPartial(e)) || [];
    message.delete_rank_reward_detail_ids = object.delete_rank_reward_detail_ids?.map(e => e) || [];
    message.sub_rank_rewards = object.sub_rank_rewards?.map(e => BatchUpdateRankReward.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUpdateRankReward(): UpdateRankReward {
  return { id: 0, min_value: 0, max_value: 0, min_score: 0 };
}

export const UpdateRankReward: MessageFns<UpdateRankReward> = {
  fromJSON(object: any): UpdateRankReward {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      min_value: isSet(object.min_value) ? globalThis.Number(object.min_value) : 0,
      max_value: isSet(object.max_value) ? globalThis.Number(object.max_value) : 0,
      min_score: isSet(object.min_score) ? globalThis.Number(object.min_score) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateRankReward>, I>>(base?: I): UpdateRankReward {
    return UpdateRankReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRankReward>, I>>(object: I): UpdateRankReward {
    const message = createBaseUpdateRankReward();
    message.id = object.id ?? 0;
    message.min_value = object.min_value ?? 0;
    message.max_value = object.max_value ?? 0;
    message.min_score = object.min_score ?? 0;
    return message;
  }
};

function createBaseUpdateRankRewardDetail(): UpdateRankRewardDetail {
  return { rank_reward_detail_id: 0, reward_type: 0, reward_object_json: '' };
}

export const UpdateRankRewardDetail: MessageFns<UpdateRankRewardDetail> = {
  fromJSON(object: any): UpdateRankRewardDetail {
    return {
      rank_reward_detail_id: isSet(object.rank_reward_detail_id) ? globalThis.Number(object.rank_reward_detail_id) : 0,
      reward_type: isSet(object.reward_type) ? rewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateRankRewardDetail>, I>>(base?: I): UpdateRankRewardDetail {
    return UpdateRankRewardDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRankRewardDetail>, I>>(object: I): UpdateRankRewardDetail {
    const message = createBaseUpdateRankRewardDetail();
    message.rank_reward_detail_id = object.rank_reward_detail_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    return message;
  }
};

function createBaseListRankRewardObject(): ListRankRewardObject {
  return { rank_reward: undefined, rank_reward_details: [], sub_rank_rewards: [] };
}

export const ListRankRewardObject: MessageFns<ListRankRewardObject> = {
  fromJSON(object: any): ListRankRewardObject {
    return {
      rank_reward: isSet(object.rank_reward) ? RankRewardObject.fromJSON(object.rank_reward) : undefined,
      rank_reward_details: globalThis.Array.isArray(object?.rank_reward_details)
        ? object.rank_reward_details.map((e: any) => RankRewardDetailObject.fromJSON(e))
        : [],
      sub_rank_rewards: globalThis.Array.isArray(object?.sub_rank_rewards)
        ? object.sub_rank_rewards.map((e: any) => ListRankRewardObject.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRankRewardObject>, I>>(base?: I): ListRankRewardObject {
    return ListRankRewardObject.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRankRewardObject>, I>>(object: I): ListRankRewardObject {
    const message = createBaseListRankRewardObject();
    message.rank_reward =
      object.rank_reward !== undefined && object.rank_reward !== null
        ? RankRewardObject.fromPartial(object.rank_reward)
        : undefined;
    message.rank_reward_details = object.rank_reward_details?.map(e => RankRewardDetailObject.fromPartial(e)) || [];
    message.sub_rank_rewards = object.sub_rank_rewards?.map(e => ListRankRewardObject.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRewardEmpty(): RewardEmpty {
  return {};
}

export const RewardEmpty: MessageFns<RewardEmpty> = {
  fromJSON(_: any): RewardEmpty {
    return {};
  },

  create<I extends Exact<DeepPartial<RewardEmpty>, I>>(base?: I): RewardEmpty {
    return RewardEmpty.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardEmpty>, I>>(_: I): RewardEmpty {
    const message = createBaseRewardEmpty();
    return message;
  }
};

function createBaseRewardPackage(): RewardPackage {
  return { package_id: '' };
}

export const RewardPackage: MessageFns<RewardPackage> = {
  fromJSON(object: any): RewardPackage {
    return { package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '' };
  },

  create<I extends Exact<DeepPartial<RewardPackage>, I>>(base?: I): RewardPackage {
    return RewardPackage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardPackage>, I>>(object: I): RewardPackage {
    const message = createBaseRewardPackage();
    message.package_id = object.package_id ?? '';
    return message;
  }
};

function createBaseRewardAnimationEffect(): RewardAnimationEffect {
  return { animation_effect_url: '', play_location: 0, text_color: '', images: [] };
}

export const RewardAnimationEffect: MessageFns<RewardAnimationEffect> = {
  fromJSON(object: any): RewardAnimationEffect {
    return {
      animation_effect_url: isSet(object.animation_effect_url) ? globalThis.String(object.animation_effect_url) : '',
      play_location: isSet(object.play_location) ? playLocationFromJSON(object.play_location) : 0,
      text_color: isSet(object.text_color) ? globalThis.String(object.text_color) : '',
      images: globalThis.Array.isArray(object?.images) ? object.images.map((e: any) => MgrSvgaImage.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RewardAnimationEffect>, I>>(base?: I): RewardAnimationEffect {
    return RewardAnimationEffect.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardAnimationEffect>, I>>(object: I): RewardAnimationEffect {
    const message = createBaseRewardAnimationEffect();
    message.animation_effect_url = object.animation_effect_url ?? '';
    message.play_location = object.play_location ?? 0;
    message.text_color = object.text_color ?? '';
    message.images = object.images?.map(e => MgrSvgaImage.fromPartial(e)) || [];
    return message;
  }
};

function createBaseMgrSvgaImage(): MgrSvgaImage {
  return { key: '', url: '' };
}

export const MgrSvgaImage: MessageFns<MgrSvgaImage> = {
  fromJSON(object: any): MgrSvgaImage {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      url: isSet(object.url) ? globalThis.String(object.url) : ''
    };
  },

  create<I extends Exact<DeepPartial<MgrSvgaImage>, I>>(base?: I): MgrSvgaImage {
    return MgrSvgaImage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MgrSvgaImage>, I>>(object: I): MgrSvgaImage {
    const message = createBaseMgrSvgaImage();
    message.key = object.key ?? '';
    message.url = object.url ?? '';
    return message;
  }
};

function createBaseTaskInfo(): TaskInfo {
  return { task_config: undefined, task_sub_configs: [], task_reward_configs: [], task_h5_configs: [] };
}

export const TaskInfo: MessageFns<TaskInfo> = {
  fromJSON(object: any): TaskInfo {
    return {
      task_config: isSet(object.task_config) ? TaskConfig.fromJSON(object.task_config) : undefined,
      task_sub_configs: globalThis.Array.isArray(object?.task_sub_configs)
        ? object.task_sub_configs.map((e: any) => TaskSubConfig.fromJSON(e))
        : [],
      task_reward_configs: globalThis.Array.isArray(object?.task_reward_configs)
        ? object.task_reward_configs.map((e: any) => TaskRewardConfig.fromJSON(e))
        : [],
      task_h5_configs: globalThis.Array.isArray(object?.task_h5_configs)
        ? object.task_h5_configs.map((e: any) => TaskH5Config.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<TaskInfo>, I>>(base?: I): TaskInfo {
    return TaskInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskInfo>, I>>(object: I): TaskInfo {
    const message = createBaseTaskInfo();
    message.task_config =
      object.task_config !== undefined && object.task_config !== null
        ? TaskConfig.fromPartial(object.task_config)
        : undefined;
    message.task_sub_configs = object.task_sub_configs?.map(e => TaskSubConfig.fromPartial(e)) || [];
    message.task_reward_configs = object.task_reward_configs?.map(e => TaskRewardConfig.fromPartial(e)) || [];
    message.task_h5_configs = object.task_h5_configs?.map(e => TaskH5Config.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTaskConfig(): TaskConfig {
  return {
    id: 0,
    activity_id: 0,
    scenes: [],
    task_code: '',
    task_name: '',
    start_time: 0,
    end_time: 0,
    task_cycle: 0,
    cou: '',
    to_finish_deeplink: '',
    gift_white_list: [],
    user_send_white_list: [],
    user_send_black_list: [],
    exclude_self: 0,
    user_receive_white_list: [],
    user_receive_black_list: [],
    room_white_list: [],
    room_black_list: [],
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    ver: 0,
    special_room_type: [],
    task_mode: 0,
    game_code_white_list: [],
    max_complete_times: 0,
    switch_bits: 0,
    specify_participants: 0,
    task_desc: '',
    rec_gift_white_list: [],
    relate_lottery_id: 0,
    auto_reward: false,
    status: '',
    disable_take_reward_batch: false,
    component_type: '',
    cycle_settle: 0,
    time_slot: undefined,
    sort: 0,
    task_component_id: 0,
    user_white_bw_info: undefined,
    user_black_bw_info: undefined,
    show_user_white_bw_info: undefined,
    show_user_black_bw_info: undefined,
    room_owner_white_bw_info: undefined,
    room_owner_black_bw_info: undefined,
    specify_black_participants: 0,
    selected_gift_sub_categorys: [],
    rec_selected_gift_subcategorys: []
  };
}

export const TaskConfig: MessageFns<TaskConfig> = {
  fromJSON(object: any): TaskConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      scenes: globalThis.Array.isArray(object?.scenes) ? object.scenes.map((e: any) => globalThis.String(e)) : [],
      task_code: isSet(object.task_code) ? globalThis.String(object.task_code) : '',
      task_name: isSet(object.task_name) ? globalThis.String(object.task_name) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      task_cycle: isSet(object.task_cycle) ? taskCycleFromJSON(object.task_cycle) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      to_finish_deeplink: isSet(object.to_finish_deeplink) ? globalThis.String(object.to_finish_deeplink) : '',
      gift_white_list: globalThis.Array.isArray(object?.gift_white_list)
        ? object.gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      user_send_white_list: globalThis.Array.isArray(object?.user_send_white_list)
        ? object.user_send_white_list.map((e: any) => globalThis.Number(e))
        : [],
      user_send_black_list: globalThis.Array.isArray(object?.user_send_black_list)
        ? object.user_send_black_list.map((e: any) => globalThis.Number(e))
        : [],
      exclude_self: isSet(object.exclude_self) ? globalThis.Number(object.exclude_self) : 0,
      user_receive_white_list: globalThis.Array.isArray(object?.user_receive_white_list)
        ? object.user_receive_white_list.map((e: any) => globalThis.Number(e))
        : [],
      user_receive_black_list: globalThis.Array.isArray(object?.user_receive_black_list)
        ? object.user_receive_black_list.map((e: any) => globalThis.Number(e))
        : [],
      room_white_list: globalThis.Array.isArray(object?.room_white_list)
        ? object.room_white_list.map((e: any) => globalThis.Number(e))
        : [],
      room_black_list: globalThis.Array.isArray(object?.room_black_list)
        ? object.room_black_list.map((e: any) => globalThis.Number(e))
        : [],
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      ver: isSet(object.ver) ? globalThis.Number(object.ver) : 0,
      special_room_type: globalThis.Array.isArray(object?.special_room_type)
        ? object.special_room_type.map((e: any) => globalThis.String(e))
        : [],
      task_mode: isSet(object.task_mode) ? taskModeFromJSON(object.task_mode) : 0,
      game_code_white_list: globalThis.Array.isArray(object?.game_code_white_list)
        ? object.game_code_white_list.map((e: any) => globalThis.String(e))
        : [],
      max_complete_times: isSet(object.max_complete_times) ? globalThis.Number(object.max_complete_times) : 0,
      switch_bits: isSet(object.switch_bits) ? globalThis.Number(object.switch_bits) : 0,
      specify_participants: isSet(object.specify_participants)
        ? specifyParticipantsFromJSON(object.specify_participants)
        : 0,
      task_desc: isSet(object.task_desc) ? globalThis.String(object.task_desc) : '',
      rec_gift_white_list: globalThis.Array.isArray(object?.rec_gift_white_list)
        ? object.rec_gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      relate_lottery_id: isSet(object.relate_lottery_id) ? globalThis.Number(object.relate_lottery_id) : 0,
      auto_reward: isSet(object.auto_reward) ? globalThis.Boolean(object.auto_reward) : false,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      disable_take_reward_batch: isSet(object.disable_take_reward_batch)
        ? globalThis.Boolean(object.disable_take_reward_batch)
        : false,
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      cycle_settle: isSet(object.cycle_settle) ? taskCycleSettleTypeFromJSON(object.cycle_settle) : 0,
      time_slot: isSet(object.time_slot) ? TimeSlot.fromJSON(object.time_slot) : undefined,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      task_component_id: isSet(object.task_component_id) ? globalThis.Number(object.task_component_id) : 0,
      user_white_bw_info: isSet(object.user_white_bw_info) ? BWInfo.fromJSON(object.user_white_bw_info) : undefined,
      user_black_bw_info: isSet(object.user_black_bw_info) ? BWInfo.fromJSON(object.user_black_bw_info) : undefined,
      show_user_white_bw_info: isSet(object.show_user_white_bw_info)
        ? BWInfo.fromJSON(object.show_user_white_bw_info)
        : undefined,
      show_user_black_bw_info: isSet(object.show_user_black_bw_info)
        ? BWInfo.fromJSON(object.show_user_black_bw_info)
        : undefined,
      room_owner_white_bw_info: isSet(object.room_owner_white_bw_info)
        ? BWInfo.fromJSON(object.room_owner_white_bw_info)
        : undefined,
      room_owner_black_bw_info: isSet(object.room_owner_black_bw_info)
        ? BWInfo.fromJSON(object.room_owner_black_bw_info)
        : undefined,
      specify_black_participants: isSet(object.specify_black_participants)
        ? specifyParticipantsFromJSON(object.specify_black_participants)
        : 0,
      selected_gift_sub_categorys: globalThis.Array.isArray(object?.selected_gift_sub_categorys)
        ? object.selected_gift_sub_categorys.map((e: any) => RewardSubCategory.fromJSON(e))
        : [],
      rec_selected_gift_subcategorys: globalThis.Array.isArray(object?.rec_selected_gift_subcategorys)
        ? object.rec_selected_gift_subcategorys.map((e: any) => RewardSubCategory.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<TaskConfig>, I>>(base?: I): TaskConfig {
    return TaskConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskConfig>, I>>(object: I): TaskConfig {
    const message = createBaseTaskConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.scenes = object.scenes?.map(e => e) || [];
    message.task_code = object.task_code ?? '';
    message.task_name = object.task_name ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.task_cycle = object.task_cycle ?? 0;
    message.cou = object.cou ?? '';
    message.to_finish_deeplink = object.to_finish_deeplink ?? '';
    message.gift_white_list = object.gift_white_list?.map(e => e) || [];
    message.user_send_white_list = object.user_send_white_list?.map(e => e) || [];
    message.user_send_black_list = object.user_send_black_list?.map(e => e) || [];
    message.exclude_self = object.exclude_self ?? 0;
    message.user_receive_white_list = object.user_receive_white_list?.map(e => e) || [];
    message.user_receive_black_list = object.user_receive_black_list?.map(e => e) || [];
    message.room_white_list = object.room_white_list?.map(e => e) || [];
    message.room_black_list = object.room_black_list?.map(e => e) || [];
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.ver = object.ver ?? 0;
    message.special_room_type = object.special_room_type?.map(e => e) || [];
    message.task_mode = object.task_mode ?? 0;
    message.game_code_white_list = object.game_code_white_list?.map(e => e) || [];
    message.max_complete_times = object.max_complete_times ?? 0;
    message.switch_bits = object.switch_bits ?? 0;
    message.specify_participants = object.specify_participants ?? 0;
    message.task_desc = object.task_desc ?? '';
    message.rec_gift_white_list = object.rec_gift_white_list?.map(e => e) || [];
    message.relate_lottery_id = object.relate_lottery_id ?? 0;
    message.auto_reward = object.auto_reward ?? false;
    message.status = object.status ?? '';
    message.disable_take_reward_batch = object.disable_take_reward_batch ?? false;
    message.component_type = object.component_type ?? '';
    message.cycle_settle = object.cycle_settle ?? 0;
    message.time_slot =
      object.time_slot !== undefined && object.time_slot !== null ? TimeSlot.fromPartial(object.time_slot) : undefined;
    message.sort = object.sort ?? 0;
    message.task_component_id = object.task_component_id ?? 0;
    message.user_white_bw_info =
      object.user_white_bw_info !== undefined && object.user_white_bw_info !== null
        ? BWInfo.fromPartial(object.user_white_bw_info)
        : undefined;
    message.user_black_bw_info =
      object.user_black_bw_info !== undefined && object.user_black_bw_info !== null
        ? BWInfo.fromPartial(object.user_black_bw_info)
        : undefined;
    message.show_user_white_bw_info =
      object.show_user_white_bw_info !== undefined && object.show_user_white_bw_info !== null
        ? BWInfo.fromPartial(object.show_user_white_bw_info)
        : undefined;
    message.show_user_black_bw_info =
      object.show_user_black_bw_info !== undefined && object.show_user_black_bw_info !== null
        ? BWInfo.fromPartial(object.show_user_black_bw_info)
        : undefined;
    message.room_owner_white_bw_info =
      object.room_owner_white_bw_info !== undefined && object.room_owner_white_bw_info !== null
        ? BWInfo.fromPartial(object.room_owner_white_bw_info)
        : undefined;
    message.room_owner_black_bw_info =
      object.room_owner_black_bw_info !== undefined && object.room_owner_black_bw_info !== null
        ? BWInfo.fromPartial(object.room_owner_black_bw_info)
        : undefined;
    message.specify_black_participants = object.specify_black_participants ?? 0;
    message.selected_gift_sub_categorys =
      object.selected_gift_sub_categorys?.map(e => RewardSubCategory.fromPartial(e)) || [];
    message.rec_selected_gift_subcategorys =
      object.rec_selected_gift_subcategorys?.map(e => RewardSubCategory.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBWInfo(): BWInfo {
  return { ids: [], bw_ids: [], labels: [], realtime_labels: [] };
}

export const BWInfo: MessageFns<BWInfo> = {
  fromJSON(object: any): BWInfo {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      bw_ids: globalThis.Array.isArray(object?.bw_ids) ? object.bw_ids.map((e: any) => globalThis.String(e)) : [],
      labels: globalThis.Array.isArray(object?.labels) ? object.labels.map((e: any) => globalThis.String(e)) : [],
      realtime_labels: globalThis.Array.isArray(object?.realtime_labels)
        ? object.realtime_labels.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BWInfo>, I>>(base?: I): BWInfo {
    return BWInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BWInfo>, I>>(object: I): BWInfo {
    const message = createBaseBWInfo();
    message.ids = object.ids?.map(e => e) || [];
    message.bw_ids = object.bw_ids?.map(e => e) || [];
    message.labels = object.labels?.map(e => e) || [];
    message.realtime_labels = object.realtime_labels?.map(e => e) || [];
    return message;
  }
};

function createBaseTaskSubConfig(): TaskSubConfig {
  return {
    id: 0,
    activity_id: 0,
    task_id: 0,
    task_type: 0,
    task_score_type: 0,
    task_name: '',
    task_target_value: 0,
    to_finish_deeplink: '',
    extend_json: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    ver: 0,
    currency_ratio: {},
    task_desc: '',
    special_room_mode: [],
    task_limit_value: 0,
    btn_text: '',
    btn_font_color: '',
    btn_font_size: 0,
    btn_bg_color: '',
    btn_bg_image: '',
    scenes: [],
    game_code_white_list: [],
    recharge_source: [],
    filter_expression: '',
    gift_white_list: [],
    rec_gift_white_list: [],
    gift_types: [],
    special_gift_currency_ratio: [],
    enable_backpack_gift: false,
    exclude_room_owner: false,
    filter_expression_id: 0,
    lottery_ids: []
  };
}

export const TaskSubConfig: MessageFns<TaskSubConfig> = {
  fromJSON(object: any): TaskSubConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      task_type: isSet(object.task_type) ? taskTypeFromJSON(object.task_type) : 0,
      task_score_type: isSet(object.task_score_type) ? taskScoreTypeFromJSON(object.task_score_type) : 0,
      task_name: isSet(object.task_name) ? globalThis.String(object.task_name) : '',
      task_target_value: isSet(object.task_target_value) ? globalThis.Number(object.task_target_value) : 0,
      to_finish_deeplink: isSet(object.to_finish_deeplink) ? globalThis.String(object.to_finish_deeplink) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      ver: isSet(object.ver) ? globalThis.Number(object.ver) : 0,
      currency_ratio: isObject(object.currency_ratio)
        ? Object.entries(object.currency_ratio).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      task_desc: isSet(object.task_desc) ? globalThis.String(object.task_desc) : '',
      special_room_mode: globalThis.Array.isArray(object?.special_room_mode)
        ? object.special_room_mode.map((e: any) => globalThis.String(e))
        : [],
      task_limit_value: isSet(object.task_limit_value) ? globalThis.Number(object.task_limit_value) : 0,
      btn_text: isSet(object.btn_text) ? globalThis.String(object.btn_text) : '',
      btn_font_color: isSet(object.btn_font_color) ? globalThis.String(object.btn_font_color) : '',
      btn_font_size: isSet(object.btn_font_size) ? globalThis.Number(object.btn_font_size) : 0,
      btn_bg_color: isSet(object.btn_bg_color) ? globalThis.String(object.btn_bg_color) : '',
      btn_bg_image: isSet(object.btn_bg_image) ? globalThis.String(object.btn_bg_image) : '',
      scenes: globalThis.Array.isArray(object?.scenes) ? object.scenes.map((e: any) => globalThis.String(e)) : [],
      game_code_white_list: globalThis.Array.isArray(object?.game_code_white_list)
        ? object.game_code_white_list.map((e: any) => globalThis.String(e))
        : [],
      recharge_source: globalThis.Array.isArray(object?.recharge_source)
        ? object.recharge_source.map((e: any) => rechargeSourceFromJSON(e))
        : [],
      filter_expression: isSet(object.filter_expression) ? globalThis.String(object.filter_expression) : '',
      gift_white_list: globalThis.Array.isArray(object?.gift_white_list)
        ? object.gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      rec_gift_white_list: globalThis.Array.isArray(object?.rec_gift_white_list)
        ? object.rec_gift_white_list.map((e: any) => globalThis.String(e))
        : [],
      gift_types: globalThis.Array.isArray(object?.gift_types)
        ? object.gift_types.map((e: any) => globalThis.String(e))
        : [],
      special_gift_currency_ratio: globalThis.Array.isArray(object?.special_gift_currency_ratio)
        ? object.special_gift_currency_ratio.map((e: any) => SpecialGiftCurrencyRatio.fromJSON(e))
        : [],
      enable_backpack_gift: isSet(object.enable_backpack_gift)
        ? globalThis.Boolean(object.enable_backpack_gift)
        : false,
      exclude_room_owner: isSet(object.exclude_room_owner) ? globalThis.Boolean(object.exclude_room_owner) : false,
      filter_expression_id: isSet(object.filter_expression_id) ? globalThis.Number(object.filter_expression_id) : 0,
      lottery_ids: globalThis.Array.isArray(object?.lottery_ids)
        ? object.lottery_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<TaskSubConfig>, I>>(base?: I): TaskSubConfig {
    return TaskSubConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskSubConfig>, I>>(object: I): TaskSubConfig {
    const message = createBaseTaskSubConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.task_type = object.task_type ?? 0;
    message.task_score_type = object.task_score_type ?? 0;
    message.task_name = object.task_name ?? '';
    message.task_target_value = object.task_target_value ?? 0;
    message.to_finish_deeplink = object.to_finish_deeplink ?? '';
    message.extend_json = object.extend_json ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.ver = object.ver ?? 0;
    message.currency_ratio = Object.entries(object.currency_ratio ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.task_desc = object.task_desc ?? '';
    message.special_room_mode = object.special_room_mode?.map(e => e) || [];
    message.task_limit_value = object.task_limit_value ?? 0;
    message.btn_text = object.btn_text ?? '';
    message.btn_font_color = object.btn_font_color ?? '';
    message.btn_font_size = object.btn_font_size ?? 0;
    message.btn_bg_color = object.btn_bg_color ?? '';
    message.btn_bg_image = object.btn_bg_image ?? '';
    message.scenes = object.scenes?.map(e => e) || [];
    message.game_code_white_list = object.game_code_white_list?.map(e => e) || [];
    message.recharge_source = object.recharge_source?.map(e => e) || [];
    message.filter_expression = object.filter_expression ?? '';
    message.gift_white_list = object.gift_white_list?.map(e => e) || [];
    message.rec_gift_white_list = object.rec_gift_white_list?.map(e => e) || [];
    message.gift_types = object.gift_types?.map(e => e) || [];
    message.special_gift_currency_ratio =
      object.special_gift_currency_ratio?.map(e => SpecialGiftCurrencyRatio.fromPartial(e)) || [];
    message.enable_backpack_gift = object.enable_backpack_gift ?? false;
    message.exclude_room_owner = object.exclude_room_owner ?? false;
    message.filter_expression_id = object.filter_expression_id ?? 0;
    message.lottery_ids = object.lottery_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseTaskSubConfig_CurrencyRatioEntry(): TaskSubConfig_CurrencyRatioEntry {
  return { key: '', value: 0 };
}

export const TaskSubConfig_CurrencyRatioEntry: MessageFns<TaskSubConfig_CurrencyRatioEntry> = {
  fromJSON(object: any): TaskSubConfig_CurrencyRatioEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskSubConfig_CurrencyRatioEntry>, I>>(
    base?: I
  ): TaskSubConfig_CurrencyRatioEntry {
    return TaskSubConfig_CurrencyRatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskSubConfig_CurrencyRatioEntry>, I>>(
    object: I
  ): TaskSubConfig_CurrencyRatioEntry {
    const message = createBaseTaskSubConfig_CurrencyRatioEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseSpecialGiftCurrencyRatio(): SpecialGiftCurrencyRatio {
  return { id: '', ratio: {} };
}

export const SpecialGiftCurrencyRatio: MessageFns<SpecialGiftCurrencyRatio> = {
  fromJSON(object: any): SpecialGiftCurrencyRatio {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      ratio: isObject(object.ratio)
        ? Object.entries(object.ratio).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<SpecialGiftCurrencyRatio>, I>>(base?: I): SpecialGiftCurrencyRatio {
    return SpecialGiftCurrencyRatio.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpecialGiftCurrencyRatio>, I>>(object: I): SpecialGiftCurrencyRatio {
    const message = createBaseSpecialGiftCurrencyRatio();
    message.id = object.id ?? '';
    message.ratio = Object.entries(object.ratio ?? {}).reduce<{ [key: number]: number }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = globalThis.Number(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseSpecialGiftCurrencyRatio_RatioEntry(): SpecialGiftCurrencyRatio_RatioEntry {
  return { key: 0, value: 0 };
}

export const SpecialGiftCurrencyRatio_RatioEntry: MessageFns<SpecialGiftCurrencyRatio_RatioEntry> = {
  fromJSON(object: any): SpecialGiftCurrencyRatio_RatioEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<SpecialGiftCurrencyRatio_RatioEntry>, I>>(
    base?: I
  ): SpecialGiftCurrencyRatio_RatioEntry {
    return SpecialGiftCurrencyRatio_RatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SpecialGiftCurrencyRatio_RatioEntry>, I>>(
    object: I
  ): SpecialGiftCurrencyRatio_RatioEntry {
    const message = createBaseSpecialGiftCurrencyRatio_RatioEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseStageTaskExtendInfo(): StageTaskExtendInfo {
  return { target_values: [] };
}

export const StageTaskExtendInfo: MessageFns<StageTaskExtendInfo> = {
  fromJSON(object: any): StageTaskExtendInfo {
    return {
      target_values: globalThis.Array.isArray(object?.target_values)
        ? object.target_values.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<StageTaskExtendInfo>, I>>(base?: I): StageTaskExtendInfo {
    return StageTaskExtendInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<StageTaskExtendInfo>, I>>(object: I): StageTaskExtendInfo {
    const message = createBaseStageTaskExtendInfo();
    message.target_values = object.target_values?.map(e => e) || [];
    return message;
  }
};

function createBaseBizActivityEventTaskExtendInfo(): BizActivityEventTaskExtendInfo {
  return { target_values: [], event_tag_ids: [] };
}

export const BizActivityEventTaskExtendInfo: MessageFns<BizActivityEventTaskExtendInfo> = {
  fromJSON(object: any): BizActivityEventTaskExtendInfo {
    return {
      target_values: globalThis.Array.isArray(object?.target_values)
        ? object.target_values.map((e: any) => globalThis.Number(e))
        : [],
      event_tag_ids: globalThis.Array.isArray(object?.event_tag_ids)
        ? object.event_tag_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BizActivityEventTaskExtendInfo>, I>>(base?: I): BizActivityEventTaskExtendInfo {
    return BizActivityEventTaskExtendInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizActivityEventTaskExtendInfo>, I>>(
    object: I
  ): BizActivityEventTaskExtendInfo {
    const message = createBaseBizActivityEventTaskExtendInfo();
    message.target_values = object.target_values?.map(e => e) || [];
    message.event_tag_ids = object.event_tag_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseTaskRewardConfig(): TaskRewardConfig {
  return {
    id: 0,
    activity_id: 0,
    task_id: 0,
    reward_type: 0,
    reward_object_json: '',
    extend_json: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    stage: 0,
    daily_inventory: 0,
    sys_msg_i18n: {}
  };
}

export const TaskRewardConfig: MessageFns<TaskRewardConfig> = {
  fromJSON(object: any): TaskRewardConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      reward_type: isSet(object.reward_type) ? taskRewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      stage: isSet(object.stage) ? globalThis.Number(object.stage) : 0,
      daily_inventory: isSet(object.daily_inventory) ? globalThis.Number(object.daily_inventory) : 0,
      sys_msg_i18n: isObject(object.sys_msg_i18n)
        ? Object.entries(object.sys_msg_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardConfig>, I>>(base?: I): TaskRewardConfig {
    return TaskRewardConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardConfig>, I>>(object: I): TaskRewardConfig {
    const message = createBaseTaskRewardConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.extend_json = object.extend_json ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.stage = object.stage ?? 0;
    message.daily_inventory = object.daily_inventory ?? 0;
    message.sys_msg_i18n = Object.entries(object.sys_msg_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseTaskRewardConfig_SysMsgI18nEntry(): TaskRewardConfig_SysMsgI18nEntry {
  return { key: '', value: '' };
}

export const TaskRewardConfig_SysMsgI18nEntry: MessageFns<TaskRewardConfig_SysMsgI18nEntry> = {
  fromJSON(object: any): TaskRewardConfig_SysMsgI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardConfig_SysMsgI18nEntry>, I>>(
    base?: I
  ): TaskRewardConfig_SysMsgI18nEntry {
    return TaskRewardConfig_SysMsgI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardConfig_SysMsgI18nEntry>, I>>(
    object: I
  ): TaskRewardConfig_SysMsgI18nEntry {
    const message = createBaseTaskRewardConfig_SysMsgI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseTaskH5Config(): TaskH5Config {
  return {
    id: 0,
    activity_id: 0,
    task_id: 0,
    stage: 0,
    icon_close: '',
    icon_open: '',
    icon_received: '',
    title: '',
    sub_title: '',
    extend_json: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const TaskH5Config: MessageFns<TaskH5Config> = {
  fromJSON(object: any): TaskH5Config {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      stage: isSet(object.stage) ? globalThis.Number(object.stage) : 0,
      icon_close: isSet(object.icon_close) ? globalThis.String(object.icon_close) : '',
      icon_open: isSet(object.icon_open) ? globalThis.String(object.icon_open) : '',
      icon_received: isSet(object.icon_received) ? globalThis.String(object.icon_received) : '',
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      sub_title: isSet(object.sub_title) ? globalThis.String(object.sub_title) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskH5Config>, I>>(base?: I): TaskH5Config {
    return TaskH5Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskH5Config>, I>>(object: I): TaskH5Config {
    const message = createBaseTaskH5Config();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.task_id = object.task_id ?? 0;
    message.stage = object.stage ?? 0;
    message.icon_close = object.icon_close ?? '';
    message.icon_open = object.icon_open ?? '';
    message.icon_received = object.icon_received ?? '';
    message.title = object.title ?? '';
    message.sub_title = object.sub_title ?? '';
    message.extend_json = object.extend_json ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseTaskRewardEmpty(): TaskRewardEmpty {
  return {};
}

export const TaskRewardEmpty: MessageFns<TaskRewardEmpty> = {
  fromJSON(_: any): TaskRewardEmpty {
    return {};
  },

  create<I extends Exact<DeepPartial<TaskRewardEmpty>, I>>(base?: I): TaskRewardEmpty {
    return TaskRewardEmpty.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardEmpty>, I>>(_: I): TaskRewardEmpty {
    const message = createBaseTaskRewardEmpty();
    return message;
  }
};

function createBaseTaskRewardPackage(): TaskRewardPackage {
  return { package_id: '' };
}

export const TaskRewardPackage: MessageFns<TaskRewardPackage> = {
  fromJSON(object: any): TaskRewardPackage {
    return { package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '' };
  },

  create<I extends Exact<DeepPartial<TaskRewardPackage>, I>>(base?: I): TaskRewardPackage {
    return TaskRewardPackage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardPackage>, I>>(object: I): TaskRewardPackage {
    const message = createBaseTaskRewardPackage();
    message.package_id = object.package_id ?? '';
    return message;
  }
};

function createBaseTaskRewardRankPoints(): TaskRewardRankPoints {
  return { rank_id: 0, score: 0, unit: 0 };
}

export const TaskRewardRankPoints: MessageFns<TaskRewardRankPoints> = {
  fromJSON(object: any): TaskRewardRankPoints {
    return {
      rank_id: isSet(object.rank_id) ? globalThis.Number(object.rank_id) : 0,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      unit: isSet(object.unit) ? taskRewardRankPointsUnitFromJSON(object.unit) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardRankPoints>, I>>(base?: I): TaskRewardRankPoints {
    return TaskRewardRankPoints.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardRankPoints>, I>>(object: I): TaskRewardRankPoints {
    const message = createBaseTaskRewardRankPoints();
    message.rank_id = object.rank_id ?? 0;
    message.score = object.score ?? 0;
    message.unit = object.unit ?? 0;
    return message;
  }
};

function createBaseTaskRewardLotteryChance(): TaskRewardLotteryChance {
  return { lottery_id: 0, times: 0 };
}

export const TaskRewardLotteryChance: MessageFns<TaskRewardLotteryChance> = {
  fromJSON(object: any): TaskRewardLotteryChance {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      times: isSet(object.times) ? globalThis.Number(object.times) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardLotteryChance>, I>>(base?: I): TaskRewardLotteryChance {
    return TaskRewardLotteryChance.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardLotteryChance>, I>>(object: I): TaskRewardLotteryChance {
    const message = createBaseTaskRewardLotteryChance();
    message.lottery_id = object.lottery_id ?? 0;
    message.times = object.times ?? 0;
    return message;
  }
};

function createBaseRewardGenerics(): RewardGenerics {
  return { items: [] };
}

export const RewardGenerics: MessageFns<RewardGenerics> = {
  fromJSON(object: any): RewardGenerics {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardGeneric.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RewardGenerics>, I>>(base?: I): RewardGenerics {
    return RewardGenerics.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardGenerics>, I>>(object: I): RewardGenerics {
    const message = createBaseRewardGenerics();
    message.items = object.items?.map(e => RewardGeneric.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRewardGeneric(): RewardGeneric {
  return {
    reward_category: undefined,
    reward_item: undefined,
    amount: 0,
    amount_unit: 0,
    duration: 0,
    duration_unit: 0
  };
}

export const RewardGeneric: MessageFns<RewardGeneric> = {
  fromJSON(object: any): RewardGeneric {
    return {
      reward_category: isSet(object.reward_category) ? RewardCategory.fromJSON(object.reward_category) : undefined,
      reward_item: isSet(object.reward_item) ? RewardItem.fromJSON(object.reward_item) : undefined,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      amount_unit: isSet(object.amount_unit) ? amountUnitFromJSON(object.amount_unit) : 0,
      duration: isSet(object.duration) ? globalThis.Number(object.duration) : 0,
      duration_unit: isSet(object.duration_unit) ? amountUnitFromJSON(object.duration_unit) : 0
    };
  },

  create<I extends Exact<DeepPartial<RewardGeneric>, I>>(base?: I): RewardGeneric {
    return RewardGeneric.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardGeneric>, I>>(object: I): RewardGeneric {
    const message = createBaseRewardGeneric();
    message.reward_category =
      object.reward_category !== undefined && object.reward_category !== null
        ? RewardCategory.fromPartial(object.reward_category)
        : undefined;
    message.reward_item =
      object.reward_item !== undefined && object.reward_item !== null
        ? RewardItem.fromPartial(object.reward_item)
        : undefined;
    message.amount = object.amount ?? 0;
    message.amount_unit = object.amount_unit ?? 0;
    message.duration = object.duration ?? 0;
    message.duration_unit = object.duration_unit ?? 0;
    return message;
  }
};

function createBasePKInfo(): PKInfo {
  return { pk_config: undefined, details: [] };
}

export const PKInfo: MessageFns<PKInfo> = {
  fromJSON(object: any): PKInfo {
    return {
      pk_config: isSet(object.pk_config) ? PKConfig.fromJSON(object.pk_config) : undefined,
      details: globalThis.Array.isArray(object?.details) ? object.details.map((e: any) => PKDetail.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<PKInfo>, I>>(base?: I): PKInfo {
    return PKInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKInfo>, I>>(object: I): PKInfo {
    const message = createBasePKInfo();
    message.pk_config =
      object.pk_config !== undefined && object.pk_config !== null ? PKConfig.fromPartial(object.pk_config) : undefined;
    message.details = object.details?.map(e => PKDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBasePKConfig(): PKConfig {
  return {
    id: 0,
    activity_id: 0,
    title: '',
    cou: '',
    start_time: 0,
    end_time: 0,
    pk_type: 0,
    score_type: 0,
    currency_ratio: {},
    special_gifts: [],
    special_rec_gifts: [],
    relate_rank_id: 0,
    status: '',
    remark: '',
    create_by: '',
    update_by: '',
    delete_by: '',
    ctime: 0,
    dtime: 0,
    utime: 0,
    special_room_pk_modes: [],
    pendant_switch: 0,
    pendant_background_images: '',
    special_gift_currency_ratio: [],
    component_type: '',
    ratio: 0
  };
}

export const PKConfig: MessageFns<PKConfig> = {
  fromJSON(object: any): PKConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      pk_type: isSet(object.pk_type) ? pKTypeFromJSON(object.pk_type) : 0,
      score_type: isSet(object.score_type) ? scoreTypeFromJSON(object.score_type) : 0,
      currency_ratio: isObject(object.currency_ratio)
        ? Object.entries(object.currency_ratio).reduce<{ [key: number]: number }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Number(value);
            return acc;
          }, {})
        : {},
      special_gifts: globalThis.Array.isArray(object?.special_gifts)
        ? object.special_gifts.map((e: any) => globalThis.String(e))
        : [],
      special_rec_gifts: globalThis.Array.isArray(object?.special_rec_gifts)
        ? object.special_rec_gifts.map((e: any) => globalThis.String(e))
        : [],
      relate_rank_id: isSet(object.relate_rank_id) ? globalThis.Number(object.relate_rank_id) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      dtime: isSet(object.dtime) ? globalThis.Number(object.dtime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      special_room_pk_modes: globalThis.Array.isArray(object?.special_room_pk_modes)
        ? object.special_room_pk_modes.map((e: any) => globalThis.String(e))
        : [],
      pendant_switch: isSet(object.pendant_switch) ? switchFromJSON(object.pendant_switch) : 0,
      pendant_background_images: isSet(object.pendant_background_images)
        ? globalThis.String(object.pendant_background_images)
        : '',
      special_gift_currency_ratio: globalThis.Array.isArray(object?.special_gift_currency_ratio)
        ? object.special_gift_currency_ratio.map((e: any) => SpecialGiftCurrencyRatio.fromJSON(e))
        : [],
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      ratio: isSet(object.ratio) ? globalThis.Number(object.ratio) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKConfig>, I>>(base?: I): PKConfig {
    return PKConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKConfig>, I>>(object: I): PKConfig {
    const message = createBasePKConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.title = object.title ?? '';
    message.cou = object.cou ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.pk_type = object.pk_type ?? 0;
    message.score_type = object.score_type ?? 0;
    message.currency_ratio = Object.entries(object.currency_ratio ?? {}).reduce<{ [key: number]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.special_gifts = object.special_gifts?.map(e => e) || [];
    message.special_rec_gifts = object.special_rec_gifts?.map(e => e) || [];
    message.relate_rank_id = object.relate_rank_id ?? 0;
    message.status = object.status ?? '';
    message.remark = object.remark ?? '';
    message.create_by = object.create_by ?? '';
    message.update_by = object.update_by ?? '';
    message.delete_by = object.delete_by ?? '';
    message.ctime = object.ctime ?? 0;
    message.dtime = object.dtime ?? 0;
    message.utime = object.utime ?? 0;
    message.special_room_pk_modes = object.special_room_pk_modes?.map(e => e) || [];
    message.pendant_switch = object.pendant_switch ?? 0;
    message.pendant_background_images = object.pendant_background_images ?? '';
    message.special_gift_currency_ratio =
      object.special_gift_currency_ratio?.map(e => SpecialGiftCurrencyRatio.fromPartial(e)) || [];
    message.component_type = object.component_type ?? '';
    message.ratio = object.ratio ?? 0;
    return message;
  }
};

function createBasePKConfig_CurrencyRatioEntry(): PKConfig_CurrencyRatioEntry {
  return { key: 0, value: 0 };
}

export const PKConfig_CurrencyRatioEntry: MessageFns<PKConfig_CurrencyRatioEntry> = {
  fromJSON(object: any): PKConfig_CurrencyRatioEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKConfig_CurrencyRatioEntry>, I>>(base?: I): PKConfig_CurrencyRatioEntry {
    return PKConfig_CurrencyRatioEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKConfig_CurrencyRatioEntry>, I>>(object: I): PKConfig_CurrencyRatioEntry {
    const message = createBasePKConfig_CurrencyRatioEntry();
    message.key = object.key ?? 0;
    message.value = object.value ?? 0;
    return message;
  }
};

function createBasePKDetail(): PKDetail {
  return {
    id: 0,
    activity_id: 0,
    pk_id: 0,
    players: [],
    rounds: [],
    status: '',
    create_by: '',
    update_by: '',
    delete_by: '',
    ctime: 0,
    dtime: 0,
    utime: 0,
    start_time: 0,
    end_time: 0,
    settle_time: 0,
    settle_status: 0
  };
}

export const PKDetail: MessageFns<PKDetail> = {
  fromJSON(object: any): PKDetail {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      pk_id: isSet(object.pk_id) ? globalThis.Number(object.pk_id) : 0,
      players: globalThis.Array.isArray(object?.players) ? object.players.map((e: any) => PKPlayer.fromJSON(e)) : [],
      rounds: globalThis.Array.isArray(object?.rounds) ? object.rounds.map((e: any) => PKRound.fromJSON(e)) : [],
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      delete_by: isSet(object.delete_by) ? globalThis.String(object.delete_by) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      dtime: isSet(object.dtime) ? globalThis.Number(object.dtime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      settle_time: isSet(object.settle_time) ? globalThis.Number(object.settle_time) : 0,
      settle_status: isSet(object.settle_status) ? pKDetailSettleStatusFromJSON(object.settle_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKDetail>, I>>(base?: I): PKDetail {
    return PKDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKDetail>, I>>(object: I): PKDetail {
    const message = createBasePKDetail();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.pk_id = object.pk_id ?? 0;
    message.players = object.players?.map(e => PKPlayer.fromPartial(e)) || [];
    message.rounds = object.rounds?.map(e => PKRound.fromPartial(e)) || [];
    message.status = object.status ?? '';
    message.create_by = object.create_by ?? '';
    message.update_by = object.update_by ?? '';
    message.delete_by = object.delete_by ?? '';
    message.ctime = object.ctime ?? 0;
    message.dtime = object.dtime ?? 0;
    message.utime = object.utime ?? 0;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.settle_time = object.settle_time ?? 0;
    message.settle_status = object.settle_status ?? 0;
    return message;
  }
};

function createBasePKRound(): PKRound {
  return { start_time: 0, end_time: 0, biz_pk_id: '' };
}

export const PKRound: MessageFns<PKRound> = {
  fromJSON(object: any): PKRound {
    return {
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      biz_pk_id: isSet(object.biz_pk_id) ? globalThis.String(object.biz_pk_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<PKRound>, I>>(base?: I): PKRound {
    return PKRound.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKRound>, I>>(object: I): PKRound {
    const message = createBasePKRound();
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.biz_pk_id = object.biz_pk_id ?? '';
    return message;
  }
};

function createBasePKPlayer(): PKPlayer {
  return { member_id: '', special_room_id: 0, player_ids: [], position: 0 };
}

export const PKPlayer: MessageFns<PKPlayer> = {
  fromJSON(object: any): PKPlayer {
    return {
      member_id: isSet(object.member_id) ? globalThis.String(object.member_id) : '',
      special_room_id: isSet(object.special_room_id) ? globalThis.Number(object.special_room_id) : 0,
      player_ids: globalThis.Array.isArray(object?.player_ids)
        ? object.player_ids.map((e: any) => globalThis.String(e))
        : [],
      position: isSet(object.position) ? globalThis.Number(object.position) : 0
    };
  },

  create<I extends Exact<DeepPartial<PKPlayer>, I>>(base?: I): PKPlayer {
    return PKPlayer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PKPlayer>, I>>(object: I): PKPlayer {
    const message = createBasePKPlayer();
    message.member_id = object.member_id ?? '';
    message.special_room_id = object.special_room_id ?? 0;
    message.player_ids = object.player_ids?.map(e => e) || [];
    message.position = object.position ?? 0;
    return message;
  }
};

function createBaseGuildInfo(): GuildInfo {
  return { id: 0, name: '', avatar: '' };
}

export const GuildInfo: MessageFns<GuildInfo> = {
  fromJSON(object: any): GuildInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<GuildInfo>, I>>(base?: I): GuildInfo {
    return GuildInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GuildInfo>, I>>(object: I): GuildInfo {
    const message = createBaseGuildInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

function createBaseFamilyInfo(): FamilyInfo {
  return { id: 0, name: '', avatar: '' };
}

export const FamilyInfo: MessageFns<FamilyInfo> = {
  fromJSON(object: any): FamilyInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<FamilyInfo>, I>>(base?: I): FamilyInfo {
    return FamilyInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FamilyInfo>, I>>(object: I): FamilyInfo {
    const message = createBaseFamilyInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

function createBaseLotteryInfo(): LotteryInfo {
  return { config: undefined, reward_configs: [], relate_task_infos: [] };
}

export const LotteryInfo: MessageFns<LotteryInfo> = {
  fromJSON(object: any): LotteryInfo {
    return {
      config: isSet(object.config) ? LotteryConfig.fromJSON(object.config) : undefined,
      reward_configs: globalThis.Array.isArray(object?.reward_configs)
        ? object.reward_configs.map((e: any) => LotteryRewardConfig.fromJSON(e))
        : [],
      relate_task_infos: globalThis.Array.isArray(object?.relate_task_infos)
        ? object.relate_task_infos.map((e: any) => TaskInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LotteryInfo>, I>>(base?: I): LotteryInfo {
    return LotteryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryInfo>, I>>(object: I): LotteryInfo {
    const message = createBaseLotteryInfo();
    message.config =
      object.config !== undefined && object.config !== null ? LotteryConfig.fromPartial(object.config) : undefined;
    message.reward_configs = object.reward_configs?.map(e => LotteryRewardConfig.fromPartial(e)) || [];
    message.relate_task_infos = object.relate_task_infos?.map(e => TaskInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLotteryConfig(): LotteryConfig {
  return {
    id: 0,
    activity_id: 0,
    title: '',
    cou: '',
    start_time: 0,
    end_time: 0,
    status: '',
    remark: '',
    specify_participants: 0,
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    component_type: 0,
    show_user_white_bw_info: undefined,
    show_user_black_bw_info: undefined,
    specify_black_participants: 0,
    paid_rules: [],
    lottery_type: 0
  };
}

export const LotteryConfig: MessageFns<LotteryConfig> = {
  fromJSON(object: any): LotteryConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      specify_participants: isSet(object.specify_participants)
        ? specifyParticipantsFromJSON(object.specify_participants)
        : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      component_type: isSet(object.component_type) ? lotteryComponentTypeFromJSON(object.component_type) : 0,
      show_user_white_bw_info: isSet(object.show_user_white_bw_info)
        ? BWInfo.fromJSON(object.show_user_white_bw_info)
        : undefined,
      show_user_black_bw_info: isSet(object.show_user_black_bw_info)
        ? BWInfo.fromJSON(object.show_user_black_bw_info)
        : undefined,
      specify_black_participants: isSet(object.specify_black_participants)
        ? specifyParticipantsFromJSON(object.specify_black_participants)
        : 0,
      paid_rules: globalThis.Array.isArray(object?.paid_rules)
        ? object.paid_rules.map((e: any) => LotteryPaidRule.fromJSON(e))
        : [],
      lottery_type: isSet(object.lottery_type) ? lotteryTypeFromJSON(object.lottery_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<LotteryConfig>, I>>(base?: I): LotteryConfig {
    return LotteryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryConfig>, I>>(object: I): LotteryConfig {
    const message = createBaseLotteryConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.title = object.title ?? '';
    message.cou = object.cou ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.status = object.status ?? '';
    message.remark = object.remark ?? '';
    message.specify_participants = object.specify_participants ?? 0;
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.component_type = object.component_type ?? 0;
    message.show_user_white_bw_info =
      object.show_user_white_bw_info !== undefined && object.show_user_white_bw_info !== null
        ? BWInfo.fromPartial(object.show_user_white_bw_info)
        : undefined;
    message.show_user_black_bw_info =
      object.show_user_black_bw_info !== undefined && object.show_user_black_bw_info !== null
        ? BWInfo.fromPartial(object.show_user_black_bw_info)
        : undefined;
    message.specify_black_participants = object.specify_black_participants ?? 0;
    message.paid_rules = object.paid_rules?.map(e => LotteryPaidRule.fromPartial(e)) || [];
    message.lottery_type = object.lottery_type ?? 0;
    return message;
  }
};

function createBaseLotteryRewardConfig(): LotteryRewardConfig {
  return {
    id: 0,
    activity_id: 0,
    lottery_id: 0,
    reward_type: 0,
    reward_object_json: '',
    extend_json: '',
    show_banner: false,
    show_barrage: false,
    banner_config: undefined,
    weight: 0,
    status: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    sort: 0,
    stock: 0,
    stock_cycle: 0,
    limit: 0,
    limit_cycle: 0,
    must_hit_times: [],
    title: '',
    img: ''
  };
}

export const LotteryRewardConfig: MessageFns<LotteryRewardConfig> = {
  fromJSON(object: any): LotteryRewardConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      reward_type: isSet(object.reward_type) ? taskRewardTypeFromJSON(object.reward_type) : 0,
      reward_object_json: isSet(object.reward_object_json) ? globalThis.String(object.reward_object_json) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      show_banner: isSet(object.show_banner) ? globalThis.Boolean(object.show_banner) : false,
      show_barrage: isSet(object.show_barrage) ? globalThis.Boolean(object.show_barrage) : false,
      banner_config: isSet(object.banner_config) ? RewardBannerConfig.fromJSON(object.banner_config) : undefined,
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      stock: isSet(object.stock) ? globalThis.Number(object.stock) : 0,
      stock_cycle: isSet(object.stock_cycle) ? cycleTypeFromJSON(object.stock_cycle) : 0,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0,
      limit_cycle: isSet(object.limit_cycle) ? cycleTypeFromJSON(object.limit_cycle) : 0,
      must_hit_times: globalThis.Array.isArray(object?.must_hit_times)
        ? object.must_hit_times.map((e: any) => globalThis.Number(e))
        : [],
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      img: isSet(object.img) ? globalThis.String(object.img) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryRewardConfig>, I>>(base?: I): LotteryRewardConfig {
    return LotteryRewardConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryRewardConfig>, I>>(object: I): LotteryRewardConfig {
    const message = createBaseLotteryRewardConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.lottery_id = object.lottery_id ?? 0;
    message.reward_type = object.reward_type ?? 0;
    message.reward_object_json = object.reward_object_json ?? '';
    message.extend_json = object.extend_json ?? '';
    message.show_banner = object.show_banner ?? false;
    message.show_barrage = object.show_barrage ?? false;
    message.banner_config =
      object.banner_config !== undefined && object.banner_config !== null
        ? RewardBannerConfig.fromPartial(object.banner_config)
        : undefined;
    message.weight = object.weight ?? 0;
    message.status = object.status ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.sort = object.sort ?? 0;
    message.stock = object.stock ?? 0;
    message.stock_cycle = object.stock_cycle ?? 0;
    message.limit = object.limit ?? 0;
    message.limit_cycle = object.limit_cycle ?? 0;
    message.must_hit_times = object.must_hit_times?.map(e => e) || [];
    message.title = object.title ?? '';
    message.img = object.img ?? '';
    return message;
  }
};

function createBaseRewardBannerConfig(): RewardBannerConfig {
  return { svga: {}, font_color: '', nickname_font_color: '', banner_position: '' };
}

export const RewardBannerConfig: MessageFns<RewardBannerConfig> = {
  fromJSON(object: any): RewardBannerConfig {
    return {
      svga: isObject(object.svga)
        ? Object.entries(object.svga).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      font_color: isSet(object.font_color) ? globalThis.String(object.font_color) : '',
      nickname_font_color: isSet(object.nickname_font_color) ? globalThis.String(object.nickname_font_color) : '',
      banner_position: isSet(object.banner_position) ? globalThis.String(object.banner_position) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardBannerConfig>, I>>(base?: I): RewardBannerConfig {
    return RewardBannerConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardBannerConfig>, I>>(object: I): RewardBannerConfig {
    const message = createBaseRewardBannerConfig();
    message.svga = Object.entries(object.svga ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.font_color = object.font_color ?? '';
    message.nickname_font_color = object.nickname_font_color ?? '';
    message.banner_position = object.banner_position ?? '';
    return message;
  }
};

function createBaseRewardBannerConfig_SvgaEntry(): RewardBannerConfig_SvgaEntry {
  return { key: '', value: '' };
}

export const RewardBannerConfig_SvgaEntry: MessageFns<RewardBannerConfig_SvgaEntry> = {
  fromJSON(object: any): RewardBannerConfig_SvgaEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardBannerConfig_SvgaEntry>, I>>(base?: I): RewardBannerConfig_SvgaEntry {
    return RewardBannerConfig_SvgaEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardBannerConfig_SvgaEntry>, I>>(object: I): RewardBannerConfig_SvgaEntry {
    const message = createBaseRewardBannerConfig_SvgaEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRoomRecommendConfig(): RoomRecommendConfig {
  return {
    id: 0,
    activity_id: 0,
    recommend_type: 0,
    room_type_list: [],
    white_list_id: '',
    room_id_white_list: [],
    display_info: 0,
    room_desc_map: {},
    component_type: 0,
    white_list_category: '',
    status: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const RoomRecommendConfig: MessageFns<RoomRecommendConfig> = {
  fromJSON(object: any): RoomRecommendConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      recommend_type: isSet(object.recommend_type) ? roomRecommendTypeFromJSON(object.recommend_type) : 0,
      room_type_list: globalThis.Array.isArray(object?.room_type_list)
        ? object.room_type_list.map((e: any) => roomIDTypeFromJSON(e))
        : [],
      white_list_id: isSet(object.white_list_id) ? globalThis.String(object.white_list_id) : '',
      room_id_white_list: globalThis.Array.isArray(object?.room_id_white_list)
        ? object.room_id_white_list.map((e: any) => globalThis.Number(e))
        : [],
      display_info: isSet(object.display_info) ? roomRecommendDisplayInfoFromJSON(object.display_info) : 0,
      room_desc_map: isObject(object.room_desc_map)
        ? Object.entries(object.room_desc_map).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      component_type: isSet(object.component_type) ? roomRecommendComponentTypeFromJSON(object.component_type) : 0,
      white_list_category: isSet(object.white_list_category) ? globalThis.String(object.white_list_category) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<RoomRecommendConfig>, I>>(base?: I): RoomRecommendConfig {
    return RoomRecommendConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendConfig>, I>>(object: I): RoomRecommendConfig {
    const message = createBaseRoomRecommendConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.recommend_type = object.recommend_type ?? 0;
    message.room_type_list = object.room_type_list?.map(e => e) || [];
    message.white_list_id = object.white_list_id ?? '';
    message.room_id_white_list = object.room_id_white_list?.map(e => e) || [];
    message.display_info = object.display_info ?? 0;
    message.room_desc_map = Object.entries(object.room_desc_map ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.component_type = object.component_type ?? 0;
    message.white_list_category = object.white_list_category ?? '';
    message.status = object.status ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseRoomRecommendConfig_RoomDescMapEntry(): RoomRecommendConfig_RoomDescMapEntry {
  return { key: '', value: '' };
}

export const RoomRecommendConfig_RoomDescMapEntry: MessageFns<RoomRecommendConfig_RoomDescMapEntry> = {
  fromJSON(object: any): RoomRecommendConfig_RoomDescMapEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<RoomRecommendConfig_RoomDescMapEntry>, I>>(
    base?: I
  ): RoomRecommendConfig_RoomDescMapEntry {
    return RoomRecommendConfig_RoomDescMapEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendConfig_RoomDescMapEntry>, I>>(
    object: I
  ): RoomRecommendConfig_RoomDescMapEntry {
    const message = createBaseRoomRecommendConfig_RoomDescMapEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseRoomRecommendInfo(): RoomRecommendInfo {
  return { config: undefined };
}

export const RoomRecommendInfo: MessageFns<RoomRecommendInfo> = {
  fromJSON(object: any): RoomRecommendInfo {
    return { config: isSet(object.config) ? RoomRecommendConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<RoomRecommendInfo>, I>>(base?: I): RoomRecommendInfo {
    return RoomRecommendInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RoomRecommendInfo>, I>>(object: I): RoomRecommendInfo {
    const message = createBaseRoomRecommendInfo();
    message.config =
      object.config !== undefined && object.config !== null
        ? RoomRecommendConfig.fromPartial(object.config)
        : undefined;
    return message;
  }
};

function createBaseUserRoomInfo(): UserRoomInfo {
  return { uid: 0, room_id: 0, room_type: 0 };
}

export const UserRoomInfo: MessageFns<UserRoomInfo> = {
  fromJSON(object: any): UserRoomInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      room_type: isSet(object.room_type) ? roomIDTypeFromJSON(object.room_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserRoomInfo>, I>>(base?: I): UserRoomInfo {
    return UserRoomInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserRoomInfo>, I>>(object: I): UserRoomInfo {
    const message = createBaseUserRoomInfo();
    message.uid = object.uid ?? 0;
    message.room_id = object.room_id ?? 0;
    message.room_type = object.room_type ?? 0;
    return message;
  }
};

function createBasePresentationConfig(): PresentationConfig {
  return {
    id: 0,
    activity_id: 0,
    items: [],
    component_type: 0,
    status: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const PresentationConfig: MessageFns<PresentationConfig> = {
  fromJSON(object: any): PresentationConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => PresentationConfigItem.fromJSON(e))
        : [],
      component_type: isSet(object.component_type) ? presentationComponentTypeFromJSON(object.component_type) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<PresentationConfig>, I>>(base?: I): PresentationConfig {
    return PresentationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationConfig>, I>>(object: I): PresentationConfig {
    const message = createBasePresentationConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.items = object.items?.map(e => PresentationConfigItem.fromPartial(e)) || [];
    message.component_type = object.component_type ?? 0;
    message.status = object.status ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBasePresentationConfigItem(): PresentationConfigItem {
  return { uids: [], title: '', desc: '' };
}

export const PresentationConfigItem: MessageFns<PresentationConfigItem> = {
  fromJSON(object: any): PresentationConfigItem {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<PresentationConfigItem>, I>>(base?: I): PresentationConfigItem {
    return PresentationConfigItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PresentationConfigItem>, I>>(object: I): PresentationConfigItem {
    const message = createBasePresentationConfigItem();
    message.uids = object.uids?.map(e => e) || [];
    message.title = object.title ?? '';
    message.desc = object.desc ?? '';
    return message;
  }
};

function createBaseSavePresentationConfig(): SavePresentationConfig {
  return { config: undefined };
}

export const SavePresentationConfig: MessageFns<SavePresentationConfig> = {
  fromJSON(object: any): SavePresentationConfig {
    return { config: isSet(object.config) ? PresentationConfig.fromJSON(object.config) : undefined };
  },

  create<I extends Exact<DeepPartial<SavePresentationConfig>, I>>(base?: I): SavePresentationConfig {
    return SavePresentationConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePresentationConfig>, I>>(object: I): SavePresentationConfig {
    const message = createBaseSavePresentationConfig();
    message.config =
      object.config !== undefined && object.config !== null ? PresentationConfig.fromPartial(object.config) : undefined;
    return message;
  }
};

function createBaseTimeSlot(): TimeSlot {
  return { type: 0, slots: [] };
}

export const TimeSlot: MessageFns<TimeSlot> = {
  fromJSON(object: any): TimeSlot {
    return {
      type: isSet(object.type) ? timeSlotTypeFromJSON(object.type) : 0,
      slots: globalThis.Array.isArray(object?.slots) ? object.slots.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<TimeSlot>, I>>(base?: I): TimeSlot {
    return TimeSlot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeSlot>, I>>(object: I): TimeSlot {
    const message = createBaseTimeSlot();
    message.type = object.type ?? 0;
    message.slots = object.slots?.map(e => e) || [];
    return message;
  }
};

function createBaseRewardSubCategory(): RewardSubCategory {
  return { sub_category_id: '', sub_category_name: '' };
}

export const RewardSubCategory: MessageFns<RewardSubCategory> = {
  fromJSON(object: any): RewardSubCategory {
    return {
      sub_category_id: isSet(object.sub_category_id) ? globalThis.String(object.sub_category_id) : '',
      sub_category_name: isSet(object.sub_category_name) ? globalThis.String(object.sub_category_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<RewardSubCategory>, I>>(base?: I): RewardSubCategory {
    return RewardSubCategory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardSubCategory>, I>>(object: I): RewardSubCategory {
    const message = createBaseRewardSubCategory();
    message.sub_category_id = object.sub_category_id ?? '';
    message.sub_category_name = object.sub_category_name ?? '';
    return message;
  }
};

function createBasePrizePoolInfo(): PrizePoolInfo {
  return { config: undefined, relate_rank_info: undefined };
}

export const PrizePoolInfo: MessageFns<PrizePoolInfo> = {
  fromJSON(object: any): PrizePoolInfo {
    return {
      config: isSet(object.config) ? PrizePoolConfig.fromJSON(object.config) : undefined,
      relate_rank_info: isSet(object.relate_rank_info) ? RankInfo.fromJSON(object.relate_rank_info) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PrizePoolInfo>, I>>(base?: I): PrizePoolInfo {
    return PrizePoolInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizePoolInfo>, I>>(object: I): PrizePoolInfo {
    const message = createBasePrizePoolInfo();
    message.config =
      object.config !== undefined && object.config !== null ? PrizePoolConfig.fromPartial(object.config) : undefined;
    message.relate_rank_info =
      object.relate_rank_info !== undefined && object.relate_rank_info !== null
        ? RankInfo.fromPartial(object.relate_rank_info)
        : undefined;
    return message;
  }
};

function createBasePrizePoolConfig(): PrizePoolConfig {
  return {
    id: 0,
    activity_id: 0,
    cal_type: 0,
    auto_reward: false,
    reward_category_key: '',
    reward_item_id: '',
    min_rank_score: 0,
    max_pool_num: 0,
    weight_type: 0,
    weight_config_list: [],
    relate_rank_id: 0,
    start_time: 0,
    end_time: 0,
    cycle: 0,
    pool_ratio: 0,
    prize_rank_score_limit: 0,
    component_type: '',
    status: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    component_id: 0
  };
}

export const PrizePoolConfig: MessageFns<PrizePoolConfig> = {
  fromJSON(object: any): PrizePoolConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      activity_id: isSet(object.activity_id) ? globalThis.Number(object.activity_id) : 0,
      cal_type: isSet(object.cal_type) ? prizePoolCalTypeFromJSON(object.cal_type) : 0,
      auto_reward: isSet(object.auto_reward) ? globalThis.Boolean(object.auto_reward) : false,
      reward_category_key: isSet(object.reward_category_key) ? globalThis.String(object.reward_category_key) : '',
      reward_item_id: isSet(object.reward_item_id) ? globalThis.String(object.reward_item_id) : '',
      min_rank_score: isSet(object.min_rank_score) ? globalThis.Number(object.min_rank_score) : 0,
      max_pool_num: isSet(object.max_pool_num) ? globalThis.Number(object.max_pool_num) : 0,
      weight_type: isSet(object.weight_type) ? prizePoolWeightTypeFromJSON(object.weight_type) : 0,
      weight_config_list: globalThis.Array.isArray(object?.weight_config_list)
        ? object.weight_config_list.map((e: any) => PrizePoolWeight.fromJSON(e))
        : [],
      relate_rank_id: isSet(object.relate_rank_id) ? globalThis.Number(object.relate_rank_id) : 0,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      cycle: isSet(object.cycle) ? cycleTypeFromJSON(object.cycle) : 0,
      pool_ratio: isSet(object.pool_ratio) ? globalThis.Number(object.pool_ratio) : 0,
      prize_rank_score_limit: isSet(object.prize_rank_score_limit)
        ? globalThis.Number(object.prize_rank_score_limit)
        : 0,
      component_type: isSet(object.component_type) ? globalThis.String(object.component_type) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      component_id: isSet(object.component_id) ? globalThis.Number(object.component_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizePoolConfig>, I>>(base?: I): PrizePoolConfig {
    return PrizePoolConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizePoolConfig>, I>>(object: I): PrizePoolConfig {
    const message = createBasePrizePoolConfig();
    message.id = object.id ?? 0;
    message.activity_id = object.activity_id ?? 0;
    message.cal_type = object.cal_type ?? 0;
    message.auto_reward = object.auto_reward ?? false;
    message.reward_category_key = object.reward_category_key ?? '';
    message.reward_item_id = object.reward_item_id ?? '';
    message.min_rank_score = object.min_rank_score ?? 0;
    message.max_pool_num = object.max_pool_num ?? 0;
    message.weight_type = object.weight_type ?? 0;
    message.weight_config_list = object.weight_config_list?.map(e => PrizePoolWeight.fromPartial(e)) || [];
    message.relate_rank_id = object.relate_rank_id ?? 0;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.cycle = object.cycle ?? 0;
    message.pool_ratio = object.pool_ratio ?? 0;
    message.prize_rank_score_limit = object.prize_rank_score_limit ?? 0;
    message.component_type = object.component_type ?? '';
    message.status = object.status ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.component_id = object.component_id ?? 0;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
