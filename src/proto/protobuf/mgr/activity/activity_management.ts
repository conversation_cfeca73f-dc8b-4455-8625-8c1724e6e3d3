// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/activity_management.proto

/* eslint-disable */
import { ActivityConfig } from '../../api/activity/activity_management';
import { Langs, Time } from '../../api/sconfig/model';
import { RewardItem } from './common';

export const protobufPackage = 'comm.mgr.activity';

/** 获取活动列表请求 */
export interface GetActivityListReq {}

/** 获取活动列表响应 */
export interface GetActivityListRsp {
  /** 活动列表 */
  activities: ActivityConfig[];
}

/** 根据 code 获取活动配置 */
export interface GetActivityByCodeReq {
  /** 活动 code */
  code: string;
}

/** 根据 code 获取活动配置 */
export interface GetActivityByCodeRsp {
  /** 活动配置 */
  activity: ActivityConfig | undefined;
}

/** 榜单容器配置，这些可能要挪到API去，或者复用现在的api定义 */
export interface RankContainerConfig {
  /** 节点(赛程)的名称 */
  title: Langs | undefined;
  start_time: Time | undefined;
  end_time: Time | undefined;
}

/**
 * 定制化开发活动的榜单配置，这里需要和积木活动的RankConfig区分开来
 * 起一个别扭的名字，先编过去
 */
export interface RankLeafConfig {
  /** 榜单的名称 */
  title: Langs | undefined;
  /** 榜单奖励 */
  rewards: RankReward[];
  ext: string;
}

export interface RankReward {
  /** 奖励区间的名称，比如Top1 */
  title: Langs | undefined;
  /** 开始过滤名次，0代表不过滤 */
  start_rank: number;
  /** 结束过滤名次，0代表不过滤 */
  stop_rank: number;
  /** 限制开始分数，0代表不过滤 */
  start_score: number;
  /** 限制结束分数，0代表不过滤 */
  end_score: number;
  /** 奖励 */
  reward: RewardPkg | undefined;
  /** 部分奖励要区分不同的分数，则放到这里来。 */
  rewards: ScoreReward[];
  /** 支持者榜单奖励 */
  sub_rewards: RankReward[];
  ext: string;
}

export interface ScoreReward {
  /** 限制开始分数，0代表不过滤 */
  start_score: number;
  /** 限制结束分数，0代表不过滤 */
  end_score: number;
  /** 对应的奖励包配置 */
  reward_pkg: RewardPkg | undefined;
}

/** IM notify的配置 */
export interface IMNotifyConfig {
  /** 目前只有这个要配置 */
  text: string;
}

/** 生成奖励包ID请求 */
export interface GenerateRewardPackageIDsReq {
  /** 活动code */
  activity_code: string;
  pkgs: RewardPkg[];
  /** 来源 oms or appnow or 自定义 */
  source: string;
}

/** 生成奖励包ID响应，信息原样返回 */
export interface GenerateRewardPackageIDsRsp {
  activity_code: string;
  pkgs: RewardPkg[];
}

/** 奖励包 */
export interface RewardPkg {
  /** 场景ID */
  sence_id: string;
  /** 0表示初次生成，否则表示修改 */
  pkg_id: string;
  /** 奖励包名称 */
  pkg_name: string;
  /** 奖励内容 */
  items: RewardItem[];
}

export interface DebugSetTimeOffsetReq {
  activity_code: string;
  /** unix timestamp, seconds，0表示清除时间偏移 */
  ts: number;
}

export interface DebugSetTimeOffsetRsp {
  /** 加上时间偏移之后当前的时间 */
  ts: number;
}

export interface DebugGetTimeOffsetReq {
  activity_code: string;
}

export interface DebugGetTimeOffsetRsp {
  ts: number;
}

/**
 * 设置机器账号ID，用来造数据的时候作为数据主体
 * 覆盖式设置
 */
export interface DebugSetRobotsReq {
  /** 机器人UID */
  uids: string[];
  /** 房间ID */
  room_ids: string[];
  /** 家族ID */
  family_ids: string[];
}

export interface DebugSetRobotsRsp {}

export interface DebugGetRobotsReq {}

export interface DebugGetRobotsRsp {
  /** 机器人UID */
  uids: string[];
  /** 房间ID */
  room_ids: string[];
  /** 家族ID */
  family_ids: string[];
}

export interface DebugMockRankMetaReq {
  activity_code: string;
  rank_key: string;
  /** 构造主榜前N名的分数 */
  main_cnt: number;
  /** 主榜前N名分数差异， */
  main_delta: number;
  /** 每个子榜构造前N名的分数，0表示不构造子榜分数 */
  sub_cnt: number;
  /** 子榜前N名分数差异 */
  sub_delta: number;
  /** 限制开始分数，0代表不限制 */
  start_limit: number;
  /** 限制结束分数，0代表不限制 */
  end_limit: number;
}

export interface DebugMockRankMetaRsp {
  items: MockRankItem[];
}

export interface MockRankItem {
  member: string;
  score: number;
  sub_items: MockRankItem[];
}

export interface DebugMockRankReq {
  activity_code: string;
  rank_key: string;
  items: MockRankItem[];
}

export interface DebugMockRankRsp {}

function createBaseGetActivityListReq(): GetActivityListReq {
  return {};
}

export const GetActivityListReq: MessageFns<GetActivityListReq> = {
  fromJSON(_: any): GetActivityListReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetActivityListReq>, I>>(base?: I): GetActivityListReq {
    return GetActivityListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityListReq>, I>>(_: I): GetActivityListReq {
    const message = createBaseGetActivityListReq();
    return message;
  }
};

function createBaseGetActivityListRsp(): GetActivityListRsp {
  return { activities: [] };
}

export const GetActivityListRsp: MessageFns<GetActivityListRsp> = {
  fromJSON(object: any): GetActivityListRsp {
    return {
      activities: globalThis.Array.isArray(object?.activities)
        ? object.activities.map((e: any) => ActivityConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetActivityListRsp>, I>>(base?: I): GetActivityListRsp {
    return GetActivityListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityListRsp>, I>>(object: I): GetActivityListRsp {
    const message = createBaseGetActivityListRsp();
    message.activities = object.activities?.map(e => ActivityConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetActivityByCodeReq(): GetActivityByCodeReq {
  return { code: '' };
}

export const GetActivityByCodeReq: MessageFns<GetActivityByCodeReq> = {
  fromJSON(object: any): GetActivityByCodeReq {
    return { code: isSet(object.code) ? globalThis.String(object.code) : '' };
  },

  create<I extends Exact<DeepPartial<GetActivityByCodeReq>, I>>(base?: I): GetActivityByCodeReq {
    return GetActivityByCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByCodeReq>, I>>(object: I): GetActivityByCodeReq {
    const message = createBaseGetActivityByCodeReq();
    message.code = object.code ?? '';
    return message;
  }
};

function createBaseGetActivityByCodeRsp(): GetActivityByCodeRsp {
  return { activity: undefined };
}

export const GetActivityByCodeRsp: MessageFns<GetActivityByCodeRsp> = {
  fromJSON(object: any): GetActivityByCodeRsp {
    return { activity: isSet(object.activity) ? ActivityConfig.fromJSON(object.activity) : undefined };
  },

  create<I extends Exact<DeepPartial<GetActivityByCodeRsp>, I>>(base?: I): GetActivityByCodeRsp {
    return GetActivityByCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetActivityByCodeRsp>, I>>(object: I): GetActivityByCodeRsp {
    const message = createBaseGetActivityByCodeRsp();
    message.activity =
      object.activity !== undefined && object.activity !== null
        ? ActivityConfig.fromPartial(object.activity)
        : undefined;
    return message;
  }
};

function createBaseRankContainerConfig(): RankContainerConfig {
  return { title: undefined, start_time: undefined, end_time: undefined };
}

export const RankContainerConfig: MessageFns<RankContainerConfig> = {
  fromJSON(object: any): RankContainerConfig {
    return {
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      start_time: isSet(object.start_time) ? Time.fromJSON(object.start_time) : undefined,
      end_time: isSet(object.end_time) ? Time.fromJSON(object.end_time) : undefined
    };
  },

  create<I extends Exact<DeepPartial<RankContainerConfig>, I>>(base?: I): RankContainerConfig {
    return RankContainerConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankContainerConfig>, I>>(object: I): RankContainerConfig {
    const message = createBaseRankContainerConfig();
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.start_time =
      object.start_time !== undefined && object.start_time !== null ? Time.fromPartial(object.start_time) : undefined;
    message.end_time =
      object.end_time !== undefined && object.end_time !== null ? Time.fromPartial(object.end_time) : undefined;
    return message;
  }
};

function createBaseRankLeafConfig(): RankLeafConfig {
  return { title: undefined, rewards: [], ext: '' };
}

export const RankLeafConfig: MessageFns<RankLeafConfig> = {
  fromJSON(object: any): RankLeafConfig {
    return {
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => RankReward.fromJSON(e)) : [],
      ext: isSet(object.ext) ? globalThis.String(object.ext) : ''
    };
  },

  create<I extends Exact<DeepPartial<RankLeafConfig>, I>>(base?: I): RankLeafConfig {
    return RankLeafConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankLeafConfig>, I>>(object: I): RankLeafConfig {
    const message = createBaseRankLeafConfig();
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.rewards = object.rewards?.map(e => RankReward.fromPartial(e)) || [];
    message.ext = object.ext ?? '';
    return message;
  }
};

function createBaseRankReward(): RankReward {
  return {
    title: undefined,
    start_rank: 0,
    stop_rank: 0,
    start_score: 0,
    end_score: 0,
    reward: undefined,
    rewards: [],
    sub_rewards: [],
    ext: ''
  };
}

export const RankReward: MessageFns<RankReward> = {
  fromJSON(object: any): RankReward {
    return {
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      start_rank: isSet(object.start_rank) ? globalThis.Number(object.start_rank) : 0,
      stop_rank: isSet(object.stop_rank) ? globalThis.Number(object.stop_rank) : 0,
      start_score: isSet(object.start_score) ? globalThis.Number(object.start_score) : 0,
      end_score: isSet(object.end_score) ? globalThis.Number(object.end_score) : 0,
      reward: isSet(object.reward) ? RewardPkg.fromJSON(object.reward) : undefined,
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => ScoreReward.fromJSON(e)) : [],
      sub_rewards: globalThis.Array.isArray(object?.sub_rewards)
        ? object.sub_rewards.map((e: any) => RankReward.fromJSON(e))
        : [],
      ext: isSet(object.ext) ? globalThis.String(object.ext) : ''
    };
  },

  create<I extends Exact<DeepPartial<RankReward>, I>>(base?: I): RankReward {
    return RankReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RankReward>, I>>(object: I): RankReward {
    const message = createBaseRankReward();
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.start_rank = object.start_rank ?? 0;
    message.stop_rank = object.stop_rank ?? 0;
    message.start_score = object.start_score ?? 0;
    message.end_score = object.end_score ?? 0;
    message.reward =
      object.reward !== undefined && object.reward !== null ? RewardPkg.fromPartial(object.reward) : undefined;
    message.rewards = object.rewards?.map(e => ScoreReward.fromPartial(e)) || [];
    message.sub_rewards = object.sub_rewards?.map(e => RankReward.fromPartial(e)) || [];
    message.ext = object.ext ?? '';
    return message;
  }
};

function createBaseScoreReward(): ScoreReward {
  return { start_score: 0, end_score: 0, reward_pkg: undefined };
}

export const ScoreReward: MessageFns<ScoreReward> = {
  fromJSON(object: any): ScoreReward {
    return {
      start_score: isSet(object.start_score) ? globalThis.Number(object.start_score) : 0,
      end_score: isSet(object.end_score) ? globalThis.Number(object.end_score) : 0,
      reward_pkg: isSet(object.reward_pkg) ? RewardPkg.fromJSON(object.reward_pkg) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ScoreReward>, I>>(base?: I): ScoreReward {
    return ScoreReward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ScoreReward>, I>>(object: I): ScoreReward {
    const message = createBaseScoreReward();
    message.start_score = object.start_score ?? 0;
    message.end_score = object.end_score ?? 0;
    message.reward_pkg =
      object.reward_pkg !== undefined && object.reward_pkg !== null
        ? RewardPkg.fromPartial(object.reward_pkg)
        : undefined;
    return message;
  }
};

function createBaseIMNotifyConfig(): IMNotifyConfig {
  return { text: '' };
}

export const IMNotifyConfig: MessageFns<IMNotifyConfig> = {
  fromJSON(object: any): IMNotifyConfig {
    return { text: isSet(object.text) ? globalThis.String(object.text) : '' };
  },

  create<I extends Exact<DeepPartial<IMNotifyConfig>, I>>(base?: I): IMNotifyConfig {
    return IMNotifyConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotifyConfig>, I>>(object: I): IMNotifyConfig {
    const message = createBaseIMNotifyConfig();
    message.text = object.text ?? '';
    return message;
  }
};

function createBaseGenerateRewardPackageIDsReq(): GenerateRewardPackageIDsReq {
  return { activity_code: '', pkgs: [], source: '' };
}

export const GenerateRewardPackageIDsReq: MessageFns<GenerateRewardPackageIDsReq> = {
  fromJSON(object: any): GenerateRewardPackageIDsReq {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => RewardPkg.fromJSON(e)) : [],
      source: isSet(object.source) ? globalThis.String(object.source) : ''
    };
  },

  create<I extends Exact<DeepPartial<GenerateRewardPackageIDsReq>, I>>(base?: I): GenerateRewardPackageIDsReq {
    return GenerateRewardPackageIDsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateRewardPackageIDsReq>, I>>(object: I): GenerateRewardPackageIDsReq {
    const message = createBaseGenerateRewardPackageIDsReq();
    message.activity_code = object.activity_code ?? '';
    message.pkgs = object.pkgs?.map(e => RewardPkg.fromPartial(e)) || [];
    message.source = object.source ?? '';
    return message;
  }
};

function createBaseGenerateRewardPackageIDsRsp(): GenerateRewardPackageIDsRsp {
  return { activity_code: '', pkgs: [] };
}

export const GenerateRewardPackageIDsRsp: MessageFns<GenerateRewardPackageIDsRsp> = {
  fromJSON(object: any): GenerateRewardPackageIDsRsp {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => RewardPkg.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GenerateRewardPackageIDsRsp>, I>>(base?: I): GenerateRewardPackageIDsRsp {
    return GenerateRewardPackageIDsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GenerateRewardPackageIDsRsp>, I>>(object: I): GenerateRewardPackageIDsRsp {
    const message = createBaseGenerateRewardPackageIDsRsp();
    message.activity_code = object.activity_code ?? '';
    message.pkgs = object.pkgs?.map(e => RewardPkg.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRewardPkg(): RewardPkg {
  return { sence_id: '', pkg_id: '', pkg_name: '', items: [] };
}

export const RewardPkg: MessageFns<RewardPkg> = {
  fromJSON(object: any): RewardPkg {
    return {
      sence_id: isSet(object.sence_id) ? globalThis.String(object.sence_id) : '',
      pkg_id: isSet(object.pkg_id) ? globalThis.String(object.pkg_id) : '',
      pkg_name: isSet(object.pkg_name) ? globalThis.String(object.pkg_name) : '',
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => RewardItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<RewardPkg>, I>>(base?: I): RewardPkg {
    return RewardPkg.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RewardPkg>, I>>(object: I): RewardPkg {
    const message = createBaseRewardPkg();
    message.sence_id = object.sence_id ?? '';
    message.pkg_id = object.pkg_id ?? '';
    message.pkg_name = object.pkg_name ?? '';
    message.items = object.items?.map(e => RewardItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDebugSetTimeOffsetReq(): DebugSetTimeOffsetReq {
  return { activity_code: '', ts: 0 };
}

export const DebugSetTimeOffsetReq: MessageFns<DebugSetTimeOffsetReq> = {
  fromJSON(object: any): DebugSetTimeOffsetReq {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0
    };
  },

  create<I extends Exact<DeepPartial<DebugSetTimeOffsetReq>, I>>(base?: I): DebugSetTimeOffsetReq {
    return DebugSetTimeOffsetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugSetTimeOffsetReq>, I>>(object: I): DebugSetTimeOffsetReq {
    const message = createBaseDebugSetTimeOffsetReq();
    message.activity_code = object.activity_code ?? '';
    message.ts = object.ts ?? 0;
    return message;
  }
};

function createBaseDebugSetTimeOffsetRsp(): DebugSetTimeOffsetRsp {
  return { ts: 0 };
}

export const DebugSetTimeOffsetRsp: MessageFns<DebugSetTimeOffsetRsp> = {
  fromJSON(object: any): DebugSetTimeOffsetRsp {
    return { ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0 };
  },

  create<I extends Exact<DeepPartial<DebugSetTimeOffsetRsp>, I>>(base?: I): DebugSetTimeOffsetRsp {
    return DebugSetTimeOffsetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugSetTimeOffsetRsp>, I>>(object: I): DebugSetTimeOffsetRsp {
    const message = createBaseDebugSetTimeOffsetRsp();
    message.ts = object.ts ?? 0;
    return message;
  }
};

function createBaseDebugGetTimeOffsetReq(): DebugGetTimeOffsetReq {
  return { activity_code: '' };
}

export const DebugGetTimeOffsetReq: MessageFns<DebugGetTimeOffsetReq> = {
  fromJSON(object: any): DebugGetTimeOffsetReq {
    return { activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '' };
  },

  create<I extends Exact<DeepPartial<DebugGetTimeOffsetReq>, I>>(base?: I): DebugGetTimeOffsetReq {
    return DebugGetTimeOffsetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugGetTimeOffsetReq>, I>>(object: I): DebugGetTimeOffsetReq {
    const message = createBaseDebugGetTimeOffsetReq();
    message.activity_code = object.activity_code ?? '';
    return message;
  }
};

function createBaseDebugGetTimeOffsetRsp(): DebugGetTimeOffsetRsp {
  return { ts: 0 };
}

export const DebugGetTimeOffsetRsp: MessageFns<DebugGetTimeOffsetRsp> = {
  fromJSON(object: any): DebugGetTimeOffsetRsp {
    return { ts: isSet(object.ts) ? globalThis.Number(object.ts) : 0 };
  },

  create<I extends Exact<DeepPartial<DebugGetTimeOffsetRsp>, I>>(base?: I): DebugGetTimeOffsetRsp {
    return DebugGetTimeOffsetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugGetTimeOffsetRsp>, I>>(object: I): DebugGetTimeOffsetRsp {
    const message = createBaseDebugGetTimeOffsetRsp();
    message.ts = object.ts ?? 0;
    return message;
  }
};

function createBaseDebugSetRobotsReq(): DebugSetRobotsReq {
  return { uids: [], room_ids: [], family_ids: [] };
}

export const DebugSetRobotsReq: MessageFns<DebugSetRobotsReq> = {
  fromJSON(object: any): DebugSetRobotsReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.String(e)) : [],
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.String(e)) : [],
      family_ids: globalThis.Array.isArray(object?.family_ids)
        ? object.family_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DebugSetRobotsReq>, I>>(base?: I): DebugSetRobotsReq {
    return DebugSetRobotsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugSetRobotsReq>, I>>(object: I): DebugSetRobotsReq {
    const message = createBaseDebugSetRobotsReq();
    message.uids = object.uids?.map(e => e) || [];
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.family_ids = object.family_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDebugSetRobotsRsp(): DebugSetRobotsRsp {
  return {};
}

export const DebugSetRobotsRsp: MessageFns<DebugSetRobotsRsp> = {
  fromJSON(_: any): DebugSetRobotsRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DebugSetRobotsRsp>, I>>(base?: I): DebugSetRobotsRsp {
    return DebugSetRobotsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugSetRobotsRsp>, I>>(_: I): DebugSetRobotsRsp {
    const message = createBaseDebugSetRobotsRsp();
    return message;
  }
};

function createBaseDebugGetRobotsReq(): DebugGetRobotsReq {
  return {};
}

export const DebugGetRobotsReq: MessageFns<DebugGetRobotsReq> = {
  fromJSON(_: any): DebugGetRobotsReq {
    return {};
  },

  create<I extends Exact<DeepPartial<DebugGetRobotsReq>, I>>(base?: I): DebugGetRobotsReq {
    return DebugGetRobotsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugGetRobotsReq>, I>>(_: I): DebugGetRobotsReq {
    const message = createBaseDebugGetRobotsReq();
    return message;
  }
};

function createBaseDebugGetRobotsRsp(): DebugGetRobotsRsp {
  return { uids: [], room_ids: [], family_ids: [] };
}

export const DebugGetRobotsRsp: MessageFns<DebugGetRobotsRsp> = {
  fromJSON(object: any): DebugGetRobotsRsp {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.String(e)) : [],
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.String(e)) : [],
      family_ids: globalThis.Array.isArray(object?.family_ids)
        ? object.family_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DebugGetRobotsRsp>, I>>(base?: I): DebugGetRobotsRsp {
    return DebugGetRobotsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugGetRobotsRsp>, I>>(object: I): DebugGetRobotsRsp {
    const message = createBaseDebugGetRobotsRsp();
    message.uids = object.uids?.map(e => e) || [];
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.family_ids = object.family_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDebugMockRankMetaReq(): DebugMockRankMetaReq {
  return {
    activity_code: '',
    rank_key: '',
    main_cnt: 0,
    main_delta: 0,
    sub_cnt: 0,
    sub_delta: 0,
    start_limit: 0,
    end_limit: 0
  };
}

export const DebugMockRankMetaReq: MessageFns<DebugMockRankMetaReq> = {
  fromJSON(object: any): DebugMockRankMetaReq {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      rank_key: isSet(object.rank_key) ? globalThis.String(object.rank_key) : '',
      main_cnt: isSet(object.main_cnt) ? globalThis.Number(object.main_cnt) : 0,
      main_delta: isSet(object.main_delta) ? globalThis.Number(object.main_delta) : 0,
      sub_cnt: isSet(object.sub_cnt) ? globalThis.Number(object.sub_cnt) : 0,
      sub_delta: isSet(object.sub_delta) ? globalThis.Number(object.sub_delta) : 0,
      start_limit: isSet(object.start_limit) ? globalThis.Number(object.start_limit) : 0,
      end_limit: isSet(object.end_limit) ? globalThis.Number(object.end_limit) : 0
    };
  },

  create<I extends Exact<DeepPartial<DebugMockRankMetaReq>, I>>(base?: I): DebugMockRankMetaReq {
    return DebugMockRankMetaReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugMockRankMetaReq>, I>>(object: I): DebugMockRankMetaReq {
    const message = createBaseDebugMockRankMetaReq();
    message.activity_code = object.activity_code ?? '';
    message.rank_key = object.rank_key ?? '';
    message.main_cnt = object.main_cnt ?? 0;
    message.main_delta = object.main_delta ?? 0;
    message.sub_cnt = object.sub_cnt ?? 0;
    message.sub_delta = object.sub_delta ?? 0;
    message.start_limit = object.start_limit ?? 0;
    message.end_limit = object.end_limit ?? 0;
    return message;
  }
};

function createBaseDebugMockRankMetaRsp(): DebugMockRankMetaRsp {
  return { items: [] };
}

export const DebugMockRankMetaRsp: MessageFns<DebugMockRankMetaRsp> = {
  fromJSON(object: any): DebugMockRankMetaRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => MockRankItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<DebugMockRankMetaRsp>, I>>(base?: I): DebugMockRankMetaRsp {
    return DebugMockRankMetaRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugMockRankMetaRsp>, I>>(object: I): DebugMockRankMetaRsp {
    const message = createBaseDebugMockRankMetaRsp();
    message.items = object.items?.map(e => MockRankItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseMockRankItem(): MockRankItem {
  return { member: '', score: 0, sub_items: [] };
}

export const MockRankItem: MessageFns<MockRankItem> = {
  fromJSON(object: any): MockRankItem {
    return {
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      sub_items: globalThis.Array.isArray(object?.sub_items)
        ? object.sub_items.map((e: any) => MockRankItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<MockRankItem>, I>>(base?: I): MockRankItem {
    return MockRankItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MockRankItem>, I>>(object: I): MockRankItem {
    const message = createBaseMockRankItem();
    message.member = object.member ?? '';
    message.score = object.score ?? 0;
    message.sub_items = object.sub_items?.map(e => MockRankItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDebugMockRankReq(): DebugMockRankReq {
  return { activity_code: '', rank_key: '', items: [] };
}

export const DebugMockRankReq: MessageFns<DebugMockRankReq> = {
  fromJSON(object: any): DebugMockRankReq {
    return {
      activity_code: isSet(object.activity_code) ? globalThis.String(object.activity_code) : '',
      rank_key: isSet(object.rank_key) ? globalThis.String(object.rank_key) : '',
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => MockRankItem.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<DebugMockRankReq>, I>>(base?: I): DebugMockRankReq {
    return DebugMockRankReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugMockRankReq>, I>>(object: I): DebugMockRankReq {
    const message = createBaseDebugMockRankReq();
    message.activity_code = object.activity_code ?? '';
    message.rank_key = object.rank_key ?? '';
    message.items = object.items?.map(e => MockRankItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDebugMockRankRsp(): DebugMockRankRsp {
  return {};
}

export const DebugMockRankRsp: MessageFns<DebugMockRankRsp> = {
  fromJSON(_: any): DebugMockRankRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DebugMockRankRsp>, I>>(base?: I): DebugMockRankRsp {
    return DebugMockRankRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DebugMockRankRsp>, I>>(_: I): DebugMockRankRsp {
    const message = createBaseDebugMockRankRsp();
    return message;
  }
};

/**
 * 活动管理平台协议
 * ServiceName: 一般是各个业务的主服务名，具体咨询业务
 * smicro:spath=gitit.cc/social/components/abase/abasemgr-handler.go
 */
export type ABaseMgrDefinition = typeof ABaseMgrDefinition;
export const ABaseMgrDefinition = {
  name: 'ABaseMgr',
  fullName: 'comm.mgr.activity.ABaseMgr',
  methods: {
    /** 获取活动列表 */
    getActivityList: {
      name: 'GetActivityList',
      requestType: GetActivityListReq,
      requestStream: false,
      responseType: GetActivityListRsp,
      responseStream: false,
      options: {}
    },
    /** 根据 code 获取活动配置 */
    getActivityByCode: {
      name: 'GetActivityByCode',
      requestType: GetActivityByCodeReq,
      requestStream: false,
      responseType: GetActivityByCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 生成奖励包ID */
    generateRewardPackageIDs: {
      name: 'GenerateRewardPackageIDs',
      requestType: GenerateRewardPackageIDsReq,
      requestStream: false,
      responseType: GenerateRewardPackageIDsRsp,
      responseStream: false,
      options: {}
    },
    /** 设置、获取时间偏移 */
    debugSetTimeOffset: {
      name: 'DebugSetTimeOffset',
      requestType: DebugSetTimeOffsetReq,
      requestStream: false,
      responseType: DebugSetTimeOffsetRsp,
      responseStream: false,
      options: {}
    },
    debugGetTimeOffset: {
      name: 'DebugGetTimeOffset',
      requestType: DebugGetTimeOffsetReq,
      requestStream: false,
      responseType: DebugGetTimeOffsetRsp,
      responseStream: false,
      options: {}
    },
    /** 设置、获取机器账号 */
    debugSetRobots: {
      name: 'DebugSetRobots',
      requestType: DebugSetRobotsReq,
      requestStream: false,
      responseType: DebugSetRobotsRsp,
      responseStream: false,
      options: {}
    },
    debugGetRobots: {
      name: 'DebugGetRobots',
      requestType: DebugGetRobotsReq,
      requestStream: false,
      responseType: DebugGetRobotsRsp,
      responseStream: false,
      options: {}
    },
    /** 构造榜单数据，先调用DebugMockRankMeta，返回一个基本信息，然后调用DebugMockRank，提交一个完整的榜单数据 */
    debugMockRankMeta: {
      name: 'DebugMockRankMeta',
      requestType: DebugMockRankMetaReq,
      requestStream: false,
      responseType: DebugMockRankMetaRsp,
      responseStream: false,
      options: {}
    },
    debugMockRank: {
      name: 'DebugMockRank',
      requestType: DebugMockRankReq,
      requestStream: false,
      responseType: DebugMockRankRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
