// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/task.proto

/* eslint-disable */
import { <PERSON><PERSON> } from '../../api/sconfig/model';
import { RewardPkg } from './activity_management';

export const protobufPackage = 'comm.mgr.activity';

/** 任务类型枚举，针对周任务分别定义具体星期 */
export enum TaskType {
  TASK_TYPE_UNSPECIFIED = 0,
  /** TASK_TYPE_ONCE - 单次任务 */
  TASK_TYPE_ONCE = 1,
  /** TASK_TYPE_WEEK_MONDAY - 周一任务 */
  TASK_TYPE_WEEK_MONDAY = 2,
  /** TASK_TYPE_WEEK_TUESDAY - 周二任务 */
  TASK_TYPE_WEEK_TUESDAY = 3,
  /** TASK_TYPE_WEEK_WEDNESDAY - 周三任务 */
  TASK_TYPE_WEEK_WEDNESDAY = 4,
  /** TASK_TYPE_WEEK_THURSDAY - 周四任务 */
  TASK_TYPE_WEEK_THURSDAY = 5,
  /** TASK_TYPE_WEEK_FRIDAY - 周五任务 */
  TASK_TYPE_WEEK_FRIDAY = 6,
  /** TASK_TYPE_WEEK_SATURDAY - 周六任务 */
  TASK_TYPE_WEEK_SATURDAY = 7,
  /** TASK_TYPE_WEEK_SUNDAY - 周日任务 */
  TASK_TYPE_WEEK_SUNDAY = 8,
  /** TASK_TYPE_MONTH - 月任务 */
  TASK_TYPE_MONTH = 9,
  /** TASK_TYPE_DAILY - 日任务 */
  TASK_TYPE_DAILY = 10,
  /** TASK_TYPE_CUSTOM - 自定义任务 */
  TASK_TYPE_CUSTOM = 11,
  UNRECOGNIZED = -1
}

export function taskTypeFromJSON(object: any): TaskType {
  switch (object) {
    case 0:
    case 'TASK_TYPE_UNSPECIFIED':
      return TaskType.TASK_TYPE_UNSPECIFIED;
    case 1:
    case 'TASK_TYPE_ONCE':
      return TaskType.TASK_TYPE_ONCE;
    case 2:
    case 'TASK_TYPE_WEEK_MONDAY':
      return TaskType.TASK_TYPE_WEEK_MONDAY;
    case 3:
    case 'TASK_TYPE_WEEK_TUESDAY':
      return TaskType.TASK_TYPE_WEEK_TUESDAY;
    case 4:
    case 'TASK_TYPE_WEEK_WEDNESDAY':
      return TaskType.TASK_TYPE_WEEK_WEDNESDAY;
    case 5:
    case 'TASK_TYPE_WEEK_THURSDAY':
      return TaskType.TASK_TYPE_WEEK_THURSDAY;
    case 6:
    case 'TASK_TYPE_WEEK_FRIDAY':
      return TaskType.TASK_TYPE_WEEK_FRIDAY;
    case 7:
    case 'TASK_TYPE_WEEK_SATURDAY':
      return TaskType.TASK_TYPE_WEEK_SATURDAY;
    case 8:
    case 'TASK_TYPE_WEEK_SUNDAY':
      return TaskType.TASK_TYPE_WEEK_SUNDAY;
    case 9:
    case 'TASK_TYPE_MONTH':
      return TaskType.TASK_TYPE_MONTH;
    case 10:
    case 'TASK_TYPE_DAILY':
      return TaskType.TASK_TYPE_DAILY;
    case 11:
    case 'TASK_TYPE_CUSTOM':
      return TaskType.TASK_TYPE_CUSTOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskType.UNRECOGNIZED;
  }
}

export enum TaskProperty {
  TASK_PROPERTY_UNSPECIFIED = 0,
  /** TASK_PROPERTY_NOTHING_DIAMONDS - 钻石消耗无关，比如分享、签到、送活动礼物n个 */
  TASK_PROPERTY_NOTHING_DIAMONDS = 1,
  /** TASK_PROPERTY_ONE_WAY_GIFT - RTP按照包内钻石价值/送礼数值算 */
  TASK_PROPERTY_ONE_WAY_GIFT = 2,
  /** TASK_PROPERTY_TWO_WAY_GIFT - TP按照包内钻石价值/（送礼数值/2）算 */
  TASK_PROPERTY_TWO_WAY_GIFT = 3,
  UNRECOGNIZED = -1
}

export function taskPropertyFromJSON(object: any): TaskProperty {
  switch (object) {
    case 0:
    case 'TASK_PROPERTY_UNSPECIFIED':
      return TaskProperty.TASK_PROPERTY_UNSPECIFIED;
    case 1:
    case 'TASK_PROPERTY_NOTHING_DIAMONDS':
      return TaskProperty.TASK_PROPERTY_NOTHING_DIAMONDS;
    case 2:
    case 'TASK_PROPERTY_ONE_WAY_GIFT':
      return TaskProperty.TASK_PROPERTY_ONE_WAY_GIFT;
    case 3:
    case 'TASK_PROPERTY_TWO_WAY_GIFT':
      return TaskProperty.TASK_PROPERTY_TWO_WAY_GIFT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return TaskProperty.UNRECOGNIZED;
  }
}

/** 活动任务配置 */
export interface TaskItem {
  /** 奖励包 */
  reward_pkg: RewardPkg | undefined;
  /** 类型 */
  task_type: TaskType;
  /** 任务分组 */
  task_tag: string;
  /** icon */
  icon: string;
  /** 唯一code */
  code: string;
  /** 展示title */
  title: Langs | undefined;
  /** 任务行为 */
  action: string;
  /** 进度次数 */
  count: number;
  /** 跳转deeplink */
  router: string;
  /** 过滤规则 */
  filter: string;
  /** 额外奖励map */
  ext_reward: string;
  /** 任务性质 */
  task_property: TaskProperty;
}

function createBaseTaskItem(): TaskItem {
  return {
    reward_pkg: undefined,
    task_type: 0,
    task_tag: '',
    icon: '',
    code: '',
    title: undefined,
    action: '',
    count: 0,
    router: '',
    filter: '',
    ext_reward: '',
    task_property: 0
  };
}

export const TaskItem: MessageFns<TaskItem> = {
  fromJSON(object: any): TaskItem {
    return {
      reward_pkg: isSet(object.reward_pkg) ? RewardPkg.fromJSON(object.reward_pkg) : undefined,
      task_type: isSet(object.task_type) ? taskTypeFromJSON(object.task_type) : 0,
      task_tag: isSet(object.task_tag) ? globalThis.String(object.task_tag) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      title: isSet(object.title) ? Langs.fromJSON(object.title) : undefined,
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      count: isSet(object.count) ? globalThis.Number(object.count) : 0,
      router: isSet(object.router) ? globalThis.String(object.router) : '',
      filter: isSet(object.filter) ? globalThis.String(object.filter) : '',
      ext_reward: isSet(object.ext_reward) ? globalThis.String(object.ext_reward) : '',
      task_property: isSet(object.task_property) ? taskPropertyFromJSON(object.task_property) : 0
    };
  },

  create<I extends Exact<DeepPartial<TaskItem>, I>>(base?: I): TaskItem {
    return TaskItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskItem>, I>>(object: I): TaskItem {
    const message = createBaseTaskItem();
    message.reward_pkg =
      object.reward_pkg !== undefined && object.reward_pkg !== null
        ? RewardPkg.fromPartial(object.reward_pkg)
        : undefined;
    message.task_type = object.task_type ?? 0;
    message.task_tag = object.task_tag ?? '';
    message.icon = object.icon ?? '';
    message.code = object.code ?? '';
    message.title = object.title !== undefined && object.title !== null ? Langs.fromPartial(object.title) : undefined;
    message.action = object.action ?? '';
    message.count = object.count ?? 0;
    message.router = object.router ?? '';
    message.filter = object.filter ?? '';
    message.ext_reward = object.ext_reward ?? '';
    message.task_property = object.task_property ?? 0;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
