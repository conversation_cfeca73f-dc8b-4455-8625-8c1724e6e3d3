// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/activity/biz.proto

/* eslint-disable */
import { RankType, rankTypeFromJSON, TaskType, taskTypeFromJSON } from '../../api/activity/common';

export const protobufPackage = 'comm.mgr.activity';

export enum CustomFilterType {
  CUSTOM_FILTER_TYPE_NONE = 0,
  /** CUSTOM_FILTER_TYPE_RANK - 榜单 */
  CUSTOM_FILTER_TYPE_RANK = 10,
  /** CUSTOM_FILTER_TYPE_TASK - 任务 */
  CUSTOM_FILTER_TYPE_TASK = 20,
  UNRECOGNIZED = -1
}

export function customFilterTypeFromJSON(object: any): CustomFilterType {
  switch (object) {
    case 0:
    case 'CUSTOM_FILTER_TYPE_NONE':
      return CustomFilterType.CUSTOM_FILTER_TYPE_NONE;
    case 10:
    case 'CUSTOM_FILTER_TYPE_RANK':
      return CustomFilterType.CUSTOM_FILTER_TYPE_RANK;
    case 20:
    case 'CUSTOM_FILTER_TYPE_TASK':
      return CustomFilterType.CUSTOM_FILTER_TYPE_TASK;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return CustomFilterType.UNRECOGNIZED;
  }
}

export interface CustomFilters {
  /** 自定义过滤ID */
  id: number;
  filter_type: CustomFilterType;
  rank_type: RankType;
  task_type: TaskType;
  /** 自定义过滤名称, 运营看 */
  name: string;
  /** 表达式 */
  expression: string;
}

function createBaseCustomFilters(): CustomFilters {
  return { id: 0, filter_type: 0, rank_type: 0, task_type: 0, name: '', expression: '' };
}

export const CustomFilters: MessageFns<CustomFilters> = {
  fromJSON(object: any): CustomFilters {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      filter_type: isSet(object.filter_type) ? customFilterTypeFromJSON(object.filter_type) : 0,
      rank_type: isSet(object.rank_type) ? rankTypeFromJSON(object.rank_type) : 0,
      task_type: isSet(object.task_type) ? taskTypeFromJSON(object.task_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      expression: isSet(object.expression) ? globalThis.String(object.expression) : ''
    };
  },

  create<I extends Exact<DeepPartial<CustomFilters>, I>>(base?: I): CustomFilters {
    return CustomFilters.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CustomFilters>, I>>(object: I): CustomFilters {
    const message = createBaseCustomFilters();
    message.id = object.id ?? 0;
    message.filter_type = object.filter_type ?? 0;
    message.rank_type = object.rank_type ?? 0;
    message.task_type = object.task_type ?? 0;
    message.name = object.name ?? '';
    message.expression = object.expression ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
