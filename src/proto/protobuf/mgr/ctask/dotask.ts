// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/ctask/dotask.proto

/* eslint-disable */
import { DoTaskRet } from '../../api/ctask/handler';

export const protobufPackage = 'comm.mgr.ctask';

export interface DoTaskReq {
  base_req: BaseReq | undefined;
  /** 谁的任务 */
  member: string;
  /** 任务id */
  task_id: string;
  /** 要加的进度值 */
  progress: number;
}

export interface DoTaskRsp {
  ret: DoTaskRet | undefined;
}

export interface BaseReq {
  /** name/template由后端提供 */
  name: string;
  /** 类似于活动id的定义 */
  template: string;
}

function createBaseDoTaskReq(): DoTaskReq {
  return { base_req: undefined, member: '', task_id: '', progress: 0 };
}

export const DoTaskReq: MessageFns<DoTaskReq> = {
  fromJSON(object: any): DoTaskReq {
    return {
      base_req: isSet(object.base_req) ? BaseReq.fromJSON(object.base_req) : undefined,
      member: isSet(object.member) ? globalThis.String(object.member) : '',
      task_id: isSet(object.task_id) ? globalThis.String(object.task_id) : '',
      progress: isSet(object.progress) ? globalThis.Number(object.progress) : 0
    };
  },

  create<I extends Exact<DeepPartial<DoTaskReq>, I>>(base?: I): DoTaskReq {
    return DoTaskReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoTaskReq>, I>>(object: I): DoTaskReq {
    const message = createBaseDoTaskReq();
    message.base_req =
      object.base_req !== undefined && object.base_req !== null ? BaseReq.fromPartial(object.base_req) : undefined;
    message.member = object.member ?? '';
    message.task_id = object.task_id ?? '';
    message.progress = object.progress ?? 0;
    return message;
  }
};

function createBaseDoTaskRsp(): DoTaskRsp {
  return { ret: undefined };
}

export const DoTaskRsp: MessageFns<DoTaskRsp> = {
  fromJSON(object: any): DoTaskRsp {
    return { ret: isSet(object.ret) ? DoTaskRet.fromJSON(object.ret) : undefined };
  },

  create<I extends Exact<DeepPartial<DoTaskRsp>, I>>(base?: I): DoTaskRsp {
    return DoTaskRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DoTaskRsp>, I>>(object: I): DoTaskRsp {
    const message = createBaseDoTaskRsp();
    message.ret = object.ret !== undefined && object.ret !== null ? DoTaskRet.fromPartial(object.ret) : undefined;
    return message;
  }
};

function createBaseBaseReq(): BaseReq {
  return { name: '', template: '' };
}

export const BaseReq: MessageFns<BaseReq> = {
  fromJSON(object: any): BaseReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      template: isSet(object.template) ? globalThis.String(object.template) : ''
    };
  },

  create<I extends Exact<DeepPartial<BaseReq>, I>>(base?: I): BaseReq {
    return BaseReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BaseReq>, I>>(object: I): BaseReq {
    const message = createBaseBaseReq();
    message.name = object.name ?? '';
    message.template = object.template ?? '';
    return message;
  }
};

export type CTaskMgrDefinition = typeof CTaskMgrDefinition;
export const CTaskMgrDefinition = {
  name: 'CTaskMgr',
  fullName: 'comm.mgr.ctask.CTaskMgr',
  methods: {
    /** 累加进度 */
    doTask: {
      name: 'DoTask',
      requestType: DoTaskReq,
      requestStream: false,
      responseType: DoTaskRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
