// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/guild/common.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.guild';

/** 时间范围 */
export interface TimeRange {
  start: number;
  end: number;
}

/** 操作信息 */
export interface OperationMeta {
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  created_by: string;
  /** 更新时间 */
  updated_at: number;
  /** 更新人 */
  updated_by: string;
  /** 删除时间 */
  deleted_at: number;
  /** 删除人 */
  deleted_by: string;
}

function createBaseTimeRange(): TimeRange {
  return { start: 0, end: 0 };
}

export const TimeRange: MessageFns<TimeRange> = {
  fromJSON(object: any): TimeRange {
    return {
      start: isSet(object.start) ? globalThis.Number(object.start) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0
    };
  },

  create<I extends Exact<DeepPartial<TimeRange>, I>>(base?: I): TimeRange {
    return TimeRange.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TimeRange>, I>>(object: I): TimeRange {
    const message = createBaseTimeRange();
    message.start = object.start ?? 0;
    message.end = object.end ?? 0;
    return message;
  }
};

function createBaseOperationMeta(): OperationMeta {
  return { created_at: 0, created_by: '', updated_at: 0, updated_by: '', deleted_at: 0, deleted_by: '' };
}

export const OperationMeta: MessageFns<OperationMeta> = {
  fromJSON(object: any): OperationMeta {
    return {
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : '',
      deleted_at: isSet(object.deleted_at) ? globalThis.Number(object.deleted_at) : 0,
      deleted_by: isSet(object.deleted_by) ? globalThis.String(object.deleted_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperationMeta>, I>>(base?: I): OperationMeta {
    return OperationMeta.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperationMeta>, I>>(object: I): OperationMeta {
    const message = createBaseOperationMeta();
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updated_by = object.updated_by ?? '';
    message.deleted_at = object.deleted_at ?? 0;
    message.deleted_by = object.deleted_by ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
