// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/guild/bd.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { BDType, bDTypeFromJSON } from '../../api/guild/bd';
import { OperationMeta, TimeRange } from './common';

export const protobufPackage = 'comm.mgr.guild';

export interface SaveBDReq {
  uid: number;
  type: BDType;
}

export interface SaveBDRsp {
  bd_id: number;
}

export interface DeleteBDReq {
  bd_id: number;
  /** 记录日志用 */
  record: BDUser | undefined;
}

export interface DeleteBDRsp {}

export interface TransferBDReq {
  bd_id: number;
  /** 原用户 */
  from_uid: number;
  /** 转让给哪个用户 */
  to_uid: number;
}

export interface TransferBDRsp {}

export interface ListBDReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 搜索参数，不传则 */
  search: ListBDSearch | undefined;
}

export interface ListBDRsp {
  page: Page | undefined;
  list: BDUser[];
}

/** BD用户信息 */
export interface BDUser {
  /** BD ID */
  id: number;
  /** BD用户 */
  uid: number;
  /** BD类型 */
  type: BDType;
  /** 管理数据 */
  stats: BDStats | undefined;
  /** oms操作信息 */
  operation_meta: OperationMeta | undefined;
}

/** 搜索项，字段默认值表示不筛选 */
export interface ListBDSearch {
  /** 根据uid搜索 */
  uid: number;
  /** 根据类型搜索 */
  type: BDType;
  /** 根据创建时间搜索 */
  created_at: TimeRange | undefined;
}

/** BD统计数据 */
export interface BDStats {
  /** 管理公会数 */
  guilds: number;
  /** 管理大R数 */
  top_payers: number;
}

function createBaseSaveBDReq(): SaveBDReq {
  return { uid: 0, type: 0 };
}

export const SaveBDReq: MessageFns<SaveBDReq> = {
  fromJSON(object: any): SaveBDReq {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      type: isSet(object.type) ? bDTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<SaveBDReq>, I>>(base?: I): SaveBDReq {
    return SaveBDReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveBDReq>, I>>(object: I): SaveBDReq {
    const message = createBaseSaveBDReq();
    message.uid = object.uid ?? 0;
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseSaveBDRsp(): SaveBDRsp {
  return { bd_id: 0 };
}

export const SaveBDRsp: MessageFns<SaveBDRsp> = {
  fromJSON(object: any): SaveBDRsp {
    return { bd_id: isSet(object.bd_id) ? globalThis.Number(object.bd_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveBDRsp>, I>>(base?: I): SaveBDRsp {
    return SaveBDRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveBDRsp>, I>>(object: I): SaveBDRsp {
    const message = createBaseSaveBDRsp();
    message.bd_id = object.bd_id ?? 0;
    return message;
  }
};

function createBaseDeleteBDReq(): DeleteBDReq {
  return { bd_id: 0, record: undefined };
}

export const DeleteBDReq: MessageFns<DeleteBDReq> = {
  fromJSON(object: any): DeleteBDReq {
    return {
      bd_id: isSet(object.bd_id) ? globalThis.Number(object.bd_id) : 0,
      record: isSet(object.record) ? BDUser.fromJSON(object.record) : undefined
    };
  },

  create<I extends Exact<DeepPartial<DeleteBDReq>, I>>(base?: I): DeleteBDReq {
    return DeleteBDReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBDReq>, I>>(object: I): DeleteBDReq {
    const message = createBaseDeleteBDReq();
    message.bd_id = object.bd_id ?? 0;
    message.record =
      object.record !== undefined && object.record !== null ? BDUser.fromPartial(object.record) : undefined;
    return message;
  }
};

function createBaseDeleteBDRsp(): DeleteBDRsp {
  return {};
}

export const DeleteBDRsp: MessageFns<DeleteBDRsp> = {
  fromJSON(_: any): DeleteBDRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteBDRsp>, I>>(base?: I): DeleteBDRsp {
    return DeleteBDRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBDRsp>, I>>(_: I): DeleteBDRsp {
    const message = createBaseDeleteBDRsp();
    return message;
  }
};

function createBaseTransferBDReq(): TransferBDReq {
  return { bd_id: 0, from_uid: 0, to_uid: 0 };
}

export const TransferBDReq: MessageFns<TransferBDReq> = {
  fromJSON(object: any): TransferBDReq {
    return {
      bd_id: isSet(object.bd_id) ? globalThis.Number(object.bd_id) : 0,
      from_uid: isSet(object.from_uid) ? globalThis.Number(object.from_uid) : 0,
      to_uid: isSet(object.to_uid) ? globalThis.Number(object.to_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<TransferBDReq>, I>>(base?: I): TransferBDReq {
    return TransferBDReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransferBDReq>, I>>(object: I): TransferBDReq {
    const message = createBaseTransferBDReq();
    message.bd_id = object.bd_id ?? 0;
    message.from_uid = object.from_uid ?? 0;
    message.to_uid = object.to_uid ?? 0;
    return message;
  }
};

function createBaseTransferBDRsp(): TransferBDRsp {
  return {};
}

export const TransferBDRsp: MessageFns<TransferBDRsp> = {
  fromJSON(_: any): TransferBDRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<TransferBDRsp>, I>>(base?: I): TransferBDRsp {
    return TransferBDRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransferBDRsp>, I>>(_: I): TransferBDRsp {
    const message = createBaseTransferBDRsp();
    return message;
  }
};

function createBaseListBDReq(): ListBDReq {
  return { page: undefined, search: undefined };
}

export const ListBDReq: MessageFns<ListBDReq> = {
  fromJSON(object: any): ListBDReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      search: isSet(object.search) ? ListBDSearch.fromJSON(object.search) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListBDReq>, I>>(base?: I): ListBDReq {
    return ListBDReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBDReq>, I>>(object: I): ListBDReq {
    const message = createBaseListBDReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.search =
      object.search !== undefined && object.search !== null ? ListBDSearch.fromPartial(object.search) : undefined;
    return message;
  }
};

function createBaseListBDRsp(): ListBDRsp {
  return { page: undefined, list: [] };
}

export const ListBDRsp: MessageFns<ListBDRsp> = {
  fromJSON(object: any): ListBDRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => BDUser.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListBDRsp>, I>>(base?: I): ListBDRsp {
    return ListBDRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBDRsp>, I>>(object: I): ListBDRsp {
    const message = createBaseListBDRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => BDUser.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBDUser(): BDUser {
  return { id: 0, uid: 0, type: 0, stats: undefined, operation_meta: undefined };
}

export const BDUser: MessageFns<BDUser> = {
  fromJSON(object: any): BDUser {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      type: isSet(object.type) ? bDTypeFromJSON(object.type) : 0,
      stats: isSet(object.stats) ? BDStats.fromJSON(object.stats) : undefined,
      operation_meta: isSet(object.operation_meta) ? OperationMeta.fromJSON(object.operation_meta) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BDUser>, I>>(base?: I): BDUser {
    return BDUser.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BDUser>, I>>(object: I): BDUser {
    const message = createBaseBDUser();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.type = object.type ?? 0;
    message.stats = object.stats !== undefined && object.stats !== null ? BDStats.fromPartial(object.stats) : undefined;
    message.operation_meta =
      object.operation_meta !== undefined && object.operation_meta !== null
        ? OperationMeta.fromPartial(object.operation_meta)
        : undefined;
    return message;
  }
};

function createBaseListBDSearch(): ListBDSearch {
  return { uid: 0, type: 0, created_at: undefined };
}

export const ListBDSearch: MessageFns<ListBDSearch> = {
  fromJSON(object: any): ListBDSearch {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      type: isSet(object.type) ? bDTypeFromJSON(object.type) : 0,
      created_at: isSet(object.created_at) ? TimeRange.fromJSON(object.created_at) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListBDSearch>, I>>(base?: I): ListBDSearch {
    return ListBDSearch.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBDSearch>, I>>(object: I): ListBDSearch {
    const message = createBaseListBDSearch();
    message.uid = object.uid ?? 0;
    message.type = object.type ?? 0;
    message.created_at =
      object.created_at !== undefined && object.created_at !== null
        ? TimeRange.fromPartial(object.created_at)
        : undefined;
    return message;
  }
};

function createBaseBDStats(): BDStats {
  return { guilds: 0, top_payers: 0 };
}

export const BDStats: MessageFns<BDStats> = {
  fromJSON(object: any): BDStats {
    return {
      guilds: isSet(object.guilds) ? globalThis.Number(object.guilds) : 0,
      top_payers: isSet(object.top_payers) ? globalThis.Number(object.top_payers) : 0
    };
  },

  create<I extends Exact<DeepPartial<BDStats>, I>>(base?: I): BDStats {
    return BDStats.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BDStats>, I>>(object: I): BDStats {
    const message = createBaseBDStats();
    message.guilds = object.guilds ?? 0;
    message.top_payers = object.top_payers ?? 0;
    return message;
  }
};

/**
 * ServiceName: rpc.micro.social.guild
 * smicro:spath=gitit.cc/social/components-service/social-guild/handler-mgr/bd
 */
export type BDMgrDefinition = typeof BDMgrDefinition;
export const BDMgrDefinition = {
  name: 'BDMgr',
  fullName: 'comm.mgr.guild.BDMgr',
  methods: {
    /** 新增 */
    saveBD: {
      name: 'SaveBD',
      requestType: SaveBDReq,
      requestStream: false,
      responseType: SaveBDRsp,
      responseStream: false,
      options: {}
    },
    /** 删除 */
    deleteBD: {
      name: 'DeleteBD',
      requestType: DeleteBDReq,
      requestStream: false,
      responseType: DeleteBDRsp,
      responseStream: false,
      options: {}
    },
    /** 转让 */
    transferBD: {
      name: 'TransferBD',
      requestType: TransferBDReq,
      requestStream: false,
      responseType: TransferBDRsp,
      responseStream: false,
      options: {}
    },
    /** 查询 */
    listBD: {
      name: 'ListBD',
      requestType: ListBDReq,
      requestStream: false,
      responseType: ListBDRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
