// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/guild/guild.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.guild';

/** 成员角色 */
export enum MemberRole {
  /** MEMBER_ROLE_NONE - 非公会成员 */
  MEMBER_ROLE_NONE = 0,
  /** MEMBER_ROLE_ANCHOR - 普通主播 */
  MEMBER_ROLE_ANCHOR = 1,
  /** MEMBER_ROLE_BROKER - 经纪人 */
  MEMBER_ROLE_BROKER = 2,
  /** MEMBER_ROLE_OWNER - 公会长 */
  MEMBER_ROLE_OWNER = 3,
  UNRECOGNIZED = -1
}

export function memberRoleFromJSON(object: any): MemberRole {
  switch (object) {
    case 0:
    case 'MEMBER_ROLE_NONE':
      return MemberRole.MEMBER_ROLE_NONE;
    case 1:
    case 'MEMBER_ROLE_ANCHOR':
      return MemberRole.MEMBER_ROLE_ANCHOR;
    case 2:
    case 'MEMBER_ROLE_BROKER':
      return MemberRole.MEMBER_ROLE_BROKER;
    case 3:
    case 'MEMBER_ROLE_OWNER':
      return MemberRole.MEMBER_ROLE_OWNER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MemberRole.UNRECOGNIZED;
  }
}

/** 工会信息 */
export interface Guild {
  /** 公会ID */
  id: number;
  /** 公会名称 */
  name: string;
  /** 公会LOGO */
  logo: string;
  /** 公会宣言 */
  profile: string;
  /** 管理员即运营ID */
  manager_id: number;
  /** 公会分类 */
  category: string;
  /** 公会等级 */
  grade: string;
  /** 公会长UID */
  owner_uid: number;
  /** 公会长姓名 */
  owner_name: string;
  /** 公会长国家 */
  owner_country: string;
  /** 公会长 whatsapp */
  owner_whatsapp: string;
  /** 公会长手机号 */
  owner_mobile: string;
  /** 公会长邮箱 */
  owner_email: string;
  /** 公会长身份证照片 */
  owner_id_cards: string[];
  /** 公会长其他 app 主播规模 */
  other_app_scale: number;
  /** 公会长其他 app 截图 */
  other_app_screenshots: string[];
  /** 是否为官方公会 */
  official: boolean;
  /** 公会是否已冻结 */
  frozen: boolean;
  /** 公会冻结时间 */
  frozen_at: number;
  /** 公会创建时间 */
  created_at: number;
  /** 公会更新时间 */
  updated_at: number;
  /** 公会拓展信息 */
  extra: string;
}

export interface UserGuild {
  uid: number;
  role: MemberRole;
  guild: Guild | undefined;
}

export interface BatchGetUserGuildReq {
  uids: number[];
}

export interface BatchGetUserGuildRsp {
  user_guilds: UserGuild[];
}

export interface BatchGetGuildByIdReq {
  guild_ids: number[];
}

export interface BatchGetGuildByIdRsp {
  guilds: { [key: number]: Guild };
}

export interface BatchGetGuildByIdRsp_GuildsEntry {
  key: number;
  value: Guild | undefined;
}

export interface BatchBindBdReq {
  /** 公会ID列表 */
  guild_ids: number[];
  /** bd ID */
  bd_id: number;
}

export interface BatchBindBdRsp {}

function createBaseGuild(): Guild {
  return {
    id: 0,
    name: '',
    logo: '',
    profile: '',
    manager_id: 0,
    category: '',
    grade: '',
    owner_uid: 0,
    owner_name: '',
    owner_country: '',
    owner_whatsapp: '',
    owner_mobile: '',
    owner_email: '',
    owner_id_cards: [],
    other_app_scale: 0,
    other_app_screenshots: [],
    official: false,
    frozen: false,
    frozen_at: 0,
    created_at: 0,
    updated_at: 0,
    extra: ''
  };
}

export const Guild: MessageFns<Guild> = {
  fromJSON(object: any): Guild {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      logo: isSet(object.logo) ? globalThis.String(object.logo) : '',
      profile: isSet(object.profile) ? globalThis.String(object.profile) : '',
      manager_id: isSet(object.manager_id) ? globalThis.Number(object.manager_id) : 0,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      grade: isSet(object.grade) ? globalThis.String(object.grade) : '',
      owner_uid: isSet(object.owner_uid) ? globalThis.Number(object.owner_uid) : 0,
      owner_name: isSet(object.owner_name) ? globalThis.String(object.owner_name) : '',
      owner_country: isSet(object.owner_country) ? globalThis.String(object.owner_country) : '',
      owner_whatsapp: isSet(object.owner_whatsapp) ? globalThis.String(object.owner_whatsapp) : '',
      owner_mobile: isSet(object.owner_mobile) ? globalThis.String(object.owner_mobile) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      owner_id_cards: globalThis.Array.isArray(object?.owner_id_cards)
        ? object.owner_id_cards.map((e: any) => globalThis.String(e))
        : [],
      other_app_scale: isSet(object.other_app_scale) ? globalThis.Number(object.other_app_scale) : 0,
      other_app_screenshots: globalThis.Array.isArray(object?.other_app_screenshots)
        ? object.other_app_screenshots.map((e: any) => globalThis.String(e))
        : [],
      official: isSet(object.official) ? globalThis.Boolean(object.official) : false,
      frozen: isSet(object.frozen) ? globalThis.Boolean(object.frozen) : false,
      frozen_at: isSet(object.frozen_at) ? globalThis.Number(object.frozen_at) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      extra: isSet(object.extra) ? globalThis.String(object.extra) : ''
    };
  },

  create<I extends Exact<DeepPartial<Guild>, I>>(base?: I): Guild {
    return Guild.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Guild>, I>>(object: I): Guild {
    const message = createBaseGuild();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.logo = object.logo ?? '';
    message.profile = object.profile ?? '';
    message.manager_id = object.manager_id ?? 0;
    message.category = object.category ?? '';
    message.grade = object.grade ?? '';
    message.owner_uid = object.owner_uid ?? 0;
    message.owner_name = object.owner_name ?? '';
    message.owner_country = object.owner_country ?? '';
    message.owner_whatsapp = object.owner_whatsapp ?? '';
    message.owner_mobile = object.owner_mobile ?? '';
    message.owner_email = object.owner_email ?? '';
    message.owner_id_cards = object.owner_id_cards?.map(e => e) || [];
    message.other_app_scale = object.other_app_scale ?? 0;
    message.other_app_screenshots = object.other_app_screenshots?.map(e => e) || [];
    message.official = object.official ?? false;
    message.frozen = object.frozen ?? false;
    message.frozen_at = object.frozen_at ?? 0;
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.extra = object.extra ?? '';
    return message;
  }
};

function createBaseUserGuild(): UserGuild {
  return { uid: 0, role: 0, guild: undefined };
}

export const UserGuild: MessageFns<UserGuild> = {
  fromJSON(object: any): UserGuild {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      role: isSet(object.role) ? memberRoleFromJSON(object.role) : 0,
      guild: isSet(object.guild) ? Guild.fromJSON(object.guild) : undefined
    };
  },

  create<I extends Exact<DeepPartial<UserGuild>, I>>(base?: I): UserGuild {
    return UserGuild.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserGuild>, I>>(object: I): UserGuild {
    const message = createBaseUserGuild();
    message.uid = object.uid ?? 0;
    message.role = object.role ?? 0;
    message.guild = object.guild !== undefined && object.guild !== null ? Guild.fromPartial(object.guild) : undefined;
    return message;
  }
};

function createBaseBatchGetUserGuildReq(): BatchGetUserGuildReq {
  return { uids: [] };
}

export const BatchGetUserGuildReq: MessageFns<BatchGetUserGuildReq> = {
  fromJSON(object: any): BatchGetUserGuildReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetUserGuildReq>, I>>(base?: I): BatchGetUserGuildReq {
    return BatchGetUserGuildReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserGuildReq>, I>>(object: I): BatchGetUserGuildReq {
    const message = createBaseBatchGetUserGuildReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserGuildRsp(): BatchGetUserGuildRsp {
  return { user_guilds: [] };
}

export const BatchGetUserGuildRsp: MessageFns<BatchGetUserGuildRsp> = {
  fromJSON(object: any): BatchGetUserGuildRsp {
    return {
      user_guilds: globalThis.Array.isArray(object?.user_guilds)
        ? object.user_guilds.map((e: any) => UserGuild.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserGuildRsp>, I>>(base?: I): BatchGetUserGuildRsp {
    return BatchGetUserGuildRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserGuildRsp>, I>>(object: I): BatchGetUserGuildRsp {
    const message = createBaseBatchGetUserGuildRsp();
    message.user_guilds = object.user_guilds?.map(e => UserGuild.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGetGuildByIdReq(): BatchGetGuildByIdReq {
  return { guild_ids: [] };
}

export const BatchGetGuildByIdReq: MessageFns<BatchGetGuildByIdReq> = {
  fromJSON(object: any): BatchGetGuildByIdReq {
    return {
      guild_ids: globalThis.Array.isArray(object?.guild_ids)
        ? object.guild_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGuildByIdReq>, I>>(base?: I): BatchGetGuildByIdReq {
    return BatchGetGuildByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGuildByIdReq>, I>>(object: I): BatchGetGuildByIdReq {
    const message = createBaseBatchGetGuildByIdReq();
    message.guild_ids = object.guild_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetGuildByIdRsp(): BatchGetGuildByIdRsp {
  return { guilds: {} };
}

export const BatchGetGuildByIdRsp: MessageFns<BatchGetGuildByIdRsp> = {
  fromJSON(object: any): BatchGetGuildByIdRsp {
    return {
      guilds: isObject(object.guilds)
        ? Object.entries(object.guilds).reduce<{ [key: number]: Guild }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = Guild.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGuildByIdRsp>, I>>(base?: I): BatchGetGuildByIdRsp {
    return BatchGetGuildByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGuildByIdRsp>, I>>(object: I): BatchGetGuildByIdRsp {
    const message = createBaseBatchGetGuildByIdRsp();
    message.guilds = Object.entries(object.guilds ?? {}).reduce<{ [key: number]: Guild }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = Guild.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetGuildByIdRsp_GuildsEntry(): BatchGetGuildByIdRsp_GuildsEntry {
  return { key: 0, value: undefined };
}

export const BatchGetGuildByIdRsp_GuildsEntry: MessageFns<BatchGetGuildByIdRsp_GuildsEntry> = {
  fromJSON(object: any): BatchGetGuildByIdRsp_GuildsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? Guild.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGuildByIdRsp_GuildsEntry>, I>>(
    base?: I
  ): BatchGetGuildByIdRsp_GuildsEntry {
    return BatchGetGuildByIdRsp_GuildsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGuildByIdRsp_GuildsEntry>, I>>(
    object: I
  ): BatchGetGuildByIdRsp_GuildsEntry {
    const message = createBaseBatchGetGuildByIdRsp_GuildsEntry();
    message.key = object.key ?? 0;
    message.value = object.value !== undefined && object.value !== null ? Guild.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseBatchBindBdReq(): BatchBindBdReq {
  return { guild_ids: [], bd_id: 0 };
}

export const BatchBindBdReq: MessageFns<BatchBindBdReq> = {
  fromJSON(object: any): BatchBindBdReq {
    return {
      guild_ids: globalThis.Array.isArray(object?.guild_ids)
        ? object.guild_ids.map((e: any) => globalThis.Number(e))
        : [],
      bd_id: isSet(object.bd_id) ? globalThis.Number(object.bd_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchBindBdReq>, I>>(base?: I): BatchBindBdReq {
    return BatchBindBdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchBindBdReq>, I>>(object: I): BatchBindBdReq {
    const message = createBaseBatchBindBdReq();
    message.guild_ids = object.guild_ids?.map(e => e) || [];
    message.bd_id = object.bd_id ?? 0;
    return message;
  }
};

function createBaseBatchBindBdRsp(): BatchBindBdRsp {
  return {};
}

export const BatchBindBdRsp: MessageFns<BatchBindBdRsp> = {
  fromJSON(_: any): BatchBindBdRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchBindBdRsp>, I>>(base?: I): BatchBindBdRsp {
    return BatchBindBdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchBindBdRsp>, I>>(_: I): BatchBindBdRsp {
    const message = createBaseBatchBindBdRsp();
    return message;
  }
};

/** smicro:spath=gitit.cc/social/components-service/social-guild/handler-mgr/guildmgr */
export type GuildMgrDefinition = typeof GuildMgrDefinition;
export const GuildMgrDefinition = {
  name: 'GuildMgr',
  fullName: 'comm.mgr.guild.GuildMgr',
  methods: {
    /** 批量获取用户公会信息 */
    batchGetUserGuild: {
      name: 'BatchGetUserGuild',
      requestType: BatchGetUserGuildReq,
      requestStream: false,
      responseType: BatchGetUserGuildRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取公会信息 */
    batchGetGuildById: {
      name: 'BatchGetGuildById',
      requestType: BatchGetGuildByIdReq,
      requestStream: false,
      responseType: BatchGetGuildByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 批量绑定bd */
    batchBindBd: {
      name: 'BatchBindBd',
      requestType: BatchBindBdReq,
      requestStream: false,
      responseType: BatchBindBdRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
