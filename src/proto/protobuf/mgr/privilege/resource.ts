// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/privilege/resource.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import {
  AssetArea,
  assetAreaFromJSON,
  CategoryID,
  categoryIDFromJSON,
  EffectType,
  effectTypeFromJSON,
  Resource,
  ResourceStatus,
  resourceStatusFromJSON
} from '../../api/privilege/v2/privilege';

export const protobufPackage = 'comm.mgr.privilege';

/** 资源信息 */
export interface MgrResource {
  /** 资源ID */
  id: number;
  /** 一级分类标识 */
  category_id: CategoryID;
  /** 二级类标识 */
  sub_category_id: CategoryID;
  /** 生效类型 */
  effect_type: EffectType;
  /** 上架状态 */
  status: ResourceStatus;
  /** 资源信息 */
  resource: Resource | undefined;
  /** 排序 大 -> 小 */
  sort: number;
  /** 备注 */
  remark: string;
  /** 创建时间 */
  ctime: number;
  /** 更新时间 */
  utime: number;
  /** 创建人 */
  creator: string;
  /** 编辑人 */
  editor: string;
  /** 拥有人数 */
  owned_cnt: number;
}

/** 资源列表请求 */
export interface ListResourceReq {
  page: Page | undefined;
  /** 一级分类标识 */
  category_id: CategoryID;
  /** 二级分类标识 */
  sub_category_id: CategoryID;
  /** 资源id */
  resource_id: number;
  /** 资源状态 */
  status: number;
  /** 资源名称 */
  name: string;
  /** 更新开始时间 */
  min_utime: number;
  /** 更新结束时间 */
  max_utime: number;
  /** 操作人 */
  editor: string;
  /** 创建开始时间 */
  min_ctime: number;
  /** 创建结束时间 */
  max_ctime: number;
  /** 创建人 */
  creator: string;
  /** 动画展示区域 */
  animation_area: AssetArea;
  /** 是否有banner 1-是 2-否 */
  with_vehicle_banner: number;
}

/** 资源列表响应 */
export interface ListResourceRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 资源列表 */
  resources: MgrResource[];
}

export interface CreateResourceReq {
  resource: MgrResource | undefined;
}

export interface CreateResourceRsp {}

export interface BatchCreateResourceReq {
  resources: MgrResource[];
}

export interface BatchCreateResourceRsp {}

export interface UpdateResourceReq {
  resource: MgrResource | undefined;
}

export interface UpdateResourceRsp {}

export interface OffShelfResourceReq {
  ids: number[];
}

export interface OffShelfResourceRsp {}

export interface OnShelfResourceReq {
  ids: number[];
}

export interface OnShelfResourceRsp {}

export interface DeleteResourceReq {
  ids: number[];
}

export interface DeleteResourceRsp {}

export interface CheckPasteResourceReq {
  /** 分类ID */
  category_id: CategoryID;
  resources: MgrResource[];
}

export interface CheckPasteResourceRsp {
  resources: MgrResource[];
}

function createBaseMgrResource(): MgrResource {
  return {
    id: 0,
    category_id: 0,
    sub_category_id: 0,
    effect_type: 0,
    status: 0,
    resource: undefined,
    sort: 0,
    remark: '',
    ctime: 0,
    utime: 0,
    creator: '',
    editor: '',
    owned_cnt: 0
  };
}

export const MgrResource: MessageFns<MgrResource> = {
  fromJSON(object: any): MgrResource {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0,
      effect_type: isSet(object.effect_type) ? effectTypeFromJSON(object.effect_type) : 0,
      status: isSet(object.status) ? resourceStatusFromJSON(object.status) : 0,
      resource: isSet(object.resource) ? Resource.fromJSON(object.resource) : undefined,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      owned_cnt: isSet(object.owned_cnt) ? globalThis.Number(object.owned_cnt) : 0
    };
  },

  create<I extends Exact<DeepPartial<MgrResource>, I>>(base?: I): MgrResource {
    return MgrResource.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MgrResource>, I>>(object: I): MgrResource {
    const message = createBaseMgrResource();
    message.id = object.id ?? 0;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.effect_type = object.effect_type ?? 0;
    message.status = object.status ?? 0;
    message.resource =
      object.resource !== undefined && object.resource !== null ? Resource.fromPartial(object.resource) : undefined;
    message.sort = object.sort ?? 0;
    message.remark = object.remark ?? '';
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    message.creator = object.creator ?? '';
    message.editor = object.editor ?? '';
    message.owned_cnt = object.owned_cnt ?? 0;
    return message;
  }
};

function createBaseListResourceReq(): ListResourceReq {
  return {
    page: undefined,
    category_id: 0,
    sub_category_id: 0,
    resource_id: 0,
    status: 0,
    name: '',
    min_utime: 0,
    max_utime: 0,
    editor: '',
    min_ctime: 0,
    max_ctime: 0,
    creator: '',
    animation_area: 0,
    with_vehicle_banner: 0
  };
}

export const ListResourceReq: MessageFns<ListResourceReq> = {
  fromJSON(object: any): ListResourceReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0,
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      min_utime: isSet(object.min_utime) ? globalThis.Number(object.min_utime) : 0,
      max_utime: isSet(object.max_utime) ? globalThis.Number(object.max_utime) : 0,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      min_ctime: isSet(object.min_ctime) ? globalThis.Number(object.min_ctime) : 0,
      max_ctime: isSet(object.max_ctime) ? globalThis.Number(object.max_ctime) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      animation_area: isSet(object.animation_area) ? assetAreaFromJSON(object.animation_area) : 0,
      with_vehicle_banner: isSet(object.with_vehicle_banner) ? globalThis.Number(object.with_vehicle_banner) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListResourceReq>, I>>(base?: I): ListResourceReq {
    return ListResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourceReq>, I>>(object: I): ListResourceReq {
    const message = createBaseListResourceReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category_id = object.category_id ?? 0;
    message.sub_category_id = object.sub_category_id ?? 0;
    message.resource_id = object.resource_id ?? 0;
    message.status = object.status ?? 0;
    message.name = object.name ?? '';
    message.min_utime = object.min_utime ?? 0;
    message.max_utime = object.max_utime ?? 0;
    message.editor = object.editor ?? '';
    message.min_ctime = object.min_ctime ?? 0;
    message.max_ctime = object.max_ctime ?? 0;
    message.creator = object.creator ?? '';
    message.animation_area = object.animation_area ?? 0;
    message.with_vehicle_banner = object.with_vehicle_banner ?? 0;
    return message;
  }
};

function createBaseListResourceRsp(): ListResourceRsp {
  return { page: undefined, resources: [] };
}

export const ListResourceRsp: MessageFns<ListResourceRsp> = {
  fromJSON(object: any): ListResourceRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      resources: globalThis.Array.isArray(object?.resources)
        ? object.resources.map((e: any) => MgrResource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourceRsp>, I>>(base?: I): ListResourceRsp {
    return ListResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourceRsp>, I>>(object: I): ListResourceRsp {
    const message = createBaseListResourceRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.resources = object.resources?.map(e => MgrResource.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateResourceReq(): CreateResourceReq {
  return { resource: undefined };
}

export const CreateResourceReq: MessageFns<CreateResourceReq> = {
  fromJSON(object: any): CreateResourceReq {
    return { resource: isSet(object.resource) ? MgrResource.fromJSON(object.resource) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateResourceReq>, I>>(base?: I): CreateResourceReq {
    return CreateResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourceReq>, I>>(object: I): CreateResourceReq {
    const message = createBaseCreateResourceReq();
    message.resource =
      object.resource !== undefined && object.resource !== null ? MgrResource.fromPartial(object.resource) : undefined;
    return message;
  }
};

function createBaseCreateResourceRsp(): CreateResourceRsp {
  return {};
}

export const CreateResourceRsp: MessageFns<CreateResourceRsp> = {
  fromJSON(_: any): CreateResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateResourceRsp>, I>>(base?: I): CreateResourceRsp {
    return CreateResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourceRsp>, I>>(_: I): CreateResourceRsp {
    const message = createBaseCreateResourceRsp();
    return message;
  }
};

function createBaseBatchCreateResourceReq(): BatchCreateResourceReq {
  return { resources: [] };
}

export const BatchCreateResourceReq: MessageFns<BatchCreateResourceReq> = {
  fromJSON(object: any): BatchCreateResourceReq {
    return {
      resources: globalThis.Array.isArray(object?.resources)
        ? object.resources.map((e: any) => MgrResource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchCreateResourceReq>, I>>(base?: I): BatchCreateResourceReq {
    return BatchCreateResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCreateResourceReq>, I>>(object: I): BatchCreateResourceReq {
    const message = createBaseBatchCreateResourceReq();
    message.resources = object.resources?.map(e => MgrResource.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchCreateResourceRsp(): BatchCreateResourceRsp {
  return {};
}

export const BatchCreateResourceRsp: MessageFns<BatchCreateResourceRsp> = {
  fromJSON(_: any): BatchCreateResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchCreateResourceRsp>, I>>(base?: I): BatchCreateResourceRsp {
    return BatchCreateResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCreateResourceRsp>, I>>(_: I): BatchCreateResourceRsp {
    const message = createBaseBatchCreateResourceRsp();
    return message;
  }
};

function createBaseUpdateResourceReq(): UpdateResourceReq {
  return { resource: undefined };
}

export const UpdateResourceReq: MessageFns<UpdateResourceReq> = {
  fromJSON(object: any): UpdateResourceReq {
    return { resource: isSet(object.resource) ? MgrResource.fromJSON(object.resource) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateResourceReq>, I>>(base?: I): UpdateResourceReq {
    return UpdateResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourceReq>, I>>(object: I): UpdateResourceReq {
    const message = createBaseUpdateResourceReq();
    message.resource =
      object.resource !== undefined && object.resource !== null ? MgrResource.fromPartial(object.resource) : undefined;
    return message;
  }
};

function createBaseUpdateResourceRsp(): UpdateResourceRsp {
  return {};
}

export const UpdateResourceRsp: MessageFns<UpdateResourceRsp> = {
  fromJSON(_: any): UpdateResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateResourceRsp>, I>>(base?: I): UpdateResourceRsp {
    return UpdateResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourceRsp>, I>>(_: I): UpdateResourceRsp {
    const message = createBaseUpdateResourceRsp();
    return message;
  }
};

function createBaseOffShelfResourceReq(): OffShelfResourceReq {
  return { ids: [] };
}

export const OffShelfResourceReq: MessageFns<OffShelfResourceReq> = {
  fromJSON(object: any): OffShelfResourceReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<OffShelfResourceReq>, I>>(base?: I): OffShelfResourceReq {
    return OffShelfResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OffShelfResourceReq>, I>>(object: I): OffShelfResourceReq {
    const message = createBaseOffShelfResourceReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseOffShelfResourceRsp(): OffShelfResourceRsp {
  return {};
}

export const OffShelfResourceRsp: MessageFns<OffShelfResourceRsp> = {
  fromJSON(_: any): OffShelfResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<OffShelfResourceRsp>, I>>(base?: I): OffShelfResourceRsp {
    return OffShelfResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OffShelfResourceRsp>, I>>(_: I): OffShelfResourceRsp {
    const message = createBaseOffShelfResourceRsp();
    return message;
  }
};

function createBaseOnShelfResourceReq(): OnShelfResourceReq {
  return { ids: [] };
}

export const OnShelfResourceReq: MessageFns<OnShelfResourceReq> = {
  fromJSON(object: any): OnShelfResourceReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<OnShelfResourceReq>, I>>(base?: I): OnShelfResourceReq {
    return OnShelfResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnShelfResourceReq>, I>>(object: I): OnShelfResourceReq {
    const message = createBaseOnShelfResourceReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseOnShelfResourceRsp(): OnShelfResourceRsp {
  return {};
}

export const OnShelfResourceRsp: MessageFns<OnShelfResourceRsp> = {
  fromJSON(_: any): OnShelfResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<OnShelfResourceRsp>, I>>(base?: I): OnShelfResourceRsp {
    return OnShelfResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OnShelfResourceRsp>, I>>(_: I): OnShelfResourceRsp {
    const message = createBaseOnShelfResourceRsp();
    return message;
  }
};

function createBaseDeleteResourceReq(): DeleteResourceReq {
  return { ids: [] };
}

export const DeleteResourceReq: MessageFns<DeleteResourceReq> = {
  fromJSON(object: any): DeleteResourceReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteResourceReq>, I>>(base?: I): DeleteResourceReq {
    return DeleteResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourceReq>, I>>(object: I): DeleteResourceReq {
    const message = createBaseDeleteResourceReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteResourceRsp(): DeleteResourceRsp {
  return {};
}

export const DeleteResourceRsp: MessageFns<DeleteResourceRsp> = {
  fromJSON(_: any): DeleteResourceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteResourceRsp>, I>>(base?: I): DeleteResourceRsp {
    return DeleteResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourceRsp>, I>>(_: I): DeleteResourceRsp {
    const message = createBaseDeleteResourceRsp();
    return message;
  }
};

function createBaseCheckPasteResourceReq(): CheckPasteResourceReq {
  return { category_id: 0, resources: [] };
}

export const CheckPasteResourceReq: MessageFns<CheckPasteResourceReq> = {
  fromJSON(object: any): CheckPasteResourceReq {
    return {
      category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
      resources: globalThis.Array.isArray(object?.resources)
        ? object.resources.map((e: any) => MgrResource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CheckPasteResourceReq>, I>>(base?: I): CheckPasteResourceReq {
    return CheckPasteResourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckPasteResourceReq>, I>>(object: I): CheckPasteResourceReq {
    const message = createBaseCheckPasteResourceReq();
    message.category_id = object.category_id ?? 0;
    message.resources = object.resources?.map(e => MgrResource.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCheckPasteResourceRsp(): CheckPasteResourceRsp {
  return { resources: [] };
}

export const CheckPasteResourceRsp: MessageFns<CheckPasteResourceRsp> = {
  fromJSON(object: any): CheckPasteResourceRsp {
    return {
      resources: globalThis.Array.isArray(object?.resources)
        ? object.resources.map((e: any) => MgrResource.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CheckPasteResourceRsp>, I>>(base?: I): CheckPasteResourceRsp {
    return CheckPasteResourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckPasteResourceRsp>, I>>(object: I): CheckPasteResourceRsp {
    const message = createBaseCheckPasteResourceRsp();
    message.resources = object.resources?.map(e => MgrResource.fromPartial(e)) || [];
    return message;
  }
};

export type ResourceMgrDefinition = typeof ResourceMgrDefinition;
export const ResourceMgrDefinition = {
  name: 'ResourceMgr',
  fullName: 'comm.mgr.privilege.ResourceMgr',
  methods: {
    /** 资源列表 */
    listResource: {
      name: 'ListResource',
      requestType: ListResourceReq,
      requestStream: false,
      responseType: ListResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 创建资源 */
    createResource: {
      name: 'CreateResource',
      requestType: CreateResourceReq,
      requestStream: false,
      responseType: CreateResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 批量创建资源 */
    batchCreateResource: {
      name: 'BatchCreateResource',
      requestType: BatchCreateResourceReq,
      requestStream: false,
      responseType: BatchCreateResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 更新资源 */
    updateResource: {
      name: 'UpdateResource',
      requestType: UpdateResourceReq,
      requestStream: false,
      responseType: UpdateResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 下架资源 */
    offShelfResource: {
      name: 'OffShelfResource',
      requestType: OffShelfResourceReq,
      requestStream: false,
      responseType: OffShelfResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 上架资源 */
    onShelfResource: {
      name: 'OnShelfResource',
      requestType: OnShelfResourceReq,
      requestStream: false,
      responseType: OnShelfResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 删除资源 */
    deleteResource: {
      name: 'DeleteResource',
      requestType: DeleteResourceReq,
      requestStream: false,
      responseType: DeleteResourceRsp,
      responseStream: false,
      options: {}
    },
    /** 检查复制粘贴的资源-用分类+资源名称判断相似, 入参资源必须是同一类型ID */
    checkPasteResource: {
      name: 'CheckPasteResource',
      requestType: CheckPasteResourceReq,
      requestStream: false,
      responseType: CheckPasteResourceRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
