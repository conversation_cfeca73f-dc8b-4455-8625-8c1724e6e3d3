// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/privilege/assets.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { PrettyNumber } from '../../api/privilege/v2/privilege';

export const protobufPackage = 'comm.mgr.privilege';

/** 资源类型 */
export enum MgrAssetType {
  /** MGR_ASSET_TYPE_NONE - 无意义 */
  MGR_ASSET_TYPE_NONE = 0,
  /** MGR_ASSET_TYPE_PRETTY_NUMBER - 靓号资源 */
  MGR_ASSET_TYPE_PRETTY_NUMBER = 1,
  UNRECOGNIZED = -1
}

export function mgrAssetTypeFromJSON(object: any): MgrAssetType {
  switch (object) {
    case 0:
    case 'MGR_ASSET_TYPE_NONE':
      return MgrAssetType.MGR_ASSET_TYPE_NONE;
    case 1:
    case 'MGR_ASSET_TYPE_PRETTY_NUMBER':
      return MgrAssetType.MGR_ASSET_TYPE_PRETTY_NUMBER;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MgrAssetType.UNRECOGNIZED;
  }
}

/** 资源状态 */
export enum MgrAssetStatus {
  /** MGR_ASSET_STATUS_NONE - 无意义 */
  MGR_ASSET_STATUS_NONE = 0,
  /** MGR_ASSET_STATUS_ON_SALE - 上架 */
  MGR_ASSET_STATUS_ON_SALE = 1,
  /** MGR_ASSET_STATUS_OFF_SALE - 下架 */
  MGR_ASSET_STATUS_OFF_SALE = 2,
  /** MGR_ASSET_STATUS_DELETED - 已删除 */
  MGR_ASSET_STATUS_DELETED = 3,
  UNRECOGNIZED = -1
}

export function mgrAssetStatusFromJSON(object: any): MgrAssetStatus {
  switch (object) {
    case 0:
    case 'MGR_ASSET_STATUS_NONE':
      return MgrAssetStatus.MGR_ASSET_STATUS_NONE;
    case 1:
    case 'MGR_ASSET_STATUS_ON_SALE':
      return MgrAssetStatus.MGR_ASSET_STATUS_ON_SALE;
    case 2:
    case 'MGR_ASSET_STATUS_OFF_SALE':
      return MgrAssetStatus.MGR_ASSET_STATUS_OFF_SALE;
    case 3:
    case 'MGR_ASSET_STATUS_DELETED':
      return MgrAssetStatus.MGR_ASSET_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MgrAssetStatus.UNRECOGNIZED;
  }
}

/** AssetsInfo 资源信息 */
export interface MgrAssetInfo {
  id: number;
  pretty_number?: PrettyNumber | undefined;
}

export interface AddAssetReq {
  type: MgrAssetType;
  asset: MgrAssetInfo | undefined;
}

export interface AddAssetRsp {}

export interface DeleteAssetReq {
  id: number;
}

export interface DeleteAssetRsp {}

export interface ListAssetsReq {
  page: Page | undefined;
  type: MgrAssetType;
}

export interface ListAssetsRsp {
  page: Page | undefined;
  assets: MgrAssetInfo[];
}

function createBaseMgrAssetInfo(): MgrAssetInfo {
  return { id: 0, pretty_number: undefined };
}

export const MgrAssetInfo: MessageFns<MgrAssetInfo> = {
  fromJSON(object: any): MgrAssetInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      pretty_number: isSet(object.pretty_number) ? PrettyNumber.fromJSON(object.pretty_number) : undefined
    };
  },

  create<I extends Exact<DeepPartial<MgrAssetInfo>, I>>(base?: I): MgrAssetInfo {
    return MgrAssetInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MgrAssetInfo>, I>>(object: I): MgrAssetInfo {
    const message = createBaseMgrAssetInfo();
    message.id = object.id ?? 0;
    message.pretty_number =
      object.pretty_number !== undefined && object.pretty_number !== null
        ? PrettyNumber.fromPartial(object.pretty_number)
        : undefined;
    return message;
  }
};

function createBaseAddAssetReq(): AddAssetReq {
  return { type: 0, asset: undefined };
}

export const AddAssetReq: MessageFns<AddAssetReq> = {
  fromJSON(object: any): AddAssetReq {
    return {
      type: isSet(object.type) ? mgrAssetTypeFromJSON(object.type) : 0,
      asset: isSet(object.asset) ? MgrAssetInfo.fromJSON(object.asset) : undefined
    };
  },

  create<I extends Exact<DeepPartial<AddAssetReq>, I>>(base?: I): AddAssetReq {
    return AddAssetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAssetReq>, I>>(object: I): AddAssetReq {
    const message = createBaseAddAssetReq();
    message.type = object.type ?? 0;
    message.asset =
      object.asset !== undefined && object.asset !== null ? MgrAssetInfo.fromPartial(object.asset) : undefined;
    return message;
  }
};

function createBaseAddAssetRsp(): AddAssetRsp {
  return {};
}

export const AddAssetRsp: MessageFns<AddAssetRsp> = {
  fromJSON(_: any): AddAssetRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddAssetRsp>, I>>(base?: I): AddAssetRsp {
    return AddAssetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAssetRsp>, I>>(_: I): AddAssetRsp {
    const message = createBaseAddAssetRsp();
    return message;
  }
};

function createBaseDeleteAssetReq(): DeleteAssetReq {
  return { id: 0 };
}

export const DeleteAssetReq: MessageFns<DeleteAssetReq> = {
  fromJSON(object: any): DeleteAssetReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteAssetReq>, I>>(base?: I): DeleteAssetReq {
    return DeleteAssetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAssetReq>, I>>(object: I): DeleteAssetReq {
    const message = createBaseDeleteAssetReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteAssetRsp(): DeleteAssetRsp {
  return {};
}

export const DeleteAssetRsp: MessageFns<DeleteAssetRsp> = {
  fromJSON(_: any): DeleteAssetRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteAssetRsp>, I>>(base?: I): DeleteAssetRsp {
    return DeleteAssetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAssetRsp>, I>>(_: I): DeleteAssetRsp {
    const message = createBaseDeleteAssetRsp();
    return message;
  }
};

function createBaseListAssetsReq(): ListAssetsReq {
  return { page: undefined, type: 0 };
}

export const ListAssetsReq: MessageFns<ListAssetsReq> = {
  fromJSON(object: any): ListAssetsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      type: isSet(object.type) ? mgrAssetTypeFromJSON(object.type) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListAssetsReq>, I>>(base?: I): ListAssetsReq {
    return ListAssetsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAssetsReq>, I>>(object: I): ListAssetsReq {
    const message = createBaseListAssetsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.type = object.type ?? 0;
    return message;
  }
};

function createBaseListAssetsRsp(): ListAssetsRsp {
  return { page: undefined, assets: [] };
}

export const ListAssetsRsp: MessageFns<ListAssetsRsp> = {
  fromJSON(object: any): ListAssetsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      assets: globalThis.Array.isArray(object?.assets) ? object.assets.map((e: any) => MgrAssetInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAssetsRsp>, I>>(base?: I): ListAssetsRsp {
    return ListAssetsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAssetsRsp>, I>>(object: I): ListAssetsRsp {
    const message = createBaseListAssetsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.assets = object.assets?.map(e => MgrAssetInfo.fromPartial(e)) || [];
    return message;
  }
};

/** 资源管理 */
export type AssetMgrDefinition = typeof AssetMgrDefinition;
export const AssetMgrDefinition = {
  name: 'AssetMgr',
  fullName: 'comm.mgr.privilege.AssetMgr',
  methods: {
    /** 新增资源 */
    addAsset: {
      name: 'AddAsset',
      requestType: AddAssetReq,
      requestStream: false,
      responseType: AddAssetRsp,
      responseStream: false,
      options: {}
    },
    /** 删除资源 */
    deleteAsset: {
      name: 'DeleteAsset',
      requestType: DeleteAssetReq,
      requestStream: false,
      responseType: DeleteAssetRsp,
      responseStream: false,
      options: {}
    },
    /** 资源列表 */
    listAssets: {
      name: 'ListAssets',
      requestType: ListAssetsReq,
      requestStream: false,
      responseType: ListAssetsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
