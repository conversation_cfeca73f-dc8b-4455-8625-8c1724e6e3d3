// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/privilege/pretty_number.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import {
  PrettyNumber,
  PrettyNumberBindStatus,
  prettyNumberBindStatusFromJSON,
  PrettyNumberUseStatus,
  prettyNumberUseStatusFromJSON,
  TargetType,
  targetTypeFromJSON
} from '../../api/privilege/v2/privilege';

export const protobufPackage = 'comm.mgr.privilege';

export interface MgrPrettyNumber {
  id: number;
  /** 靓号 */
  pretty_number: string;
  /** 归属类型 */
  target_type: TargetType;
  /** 归属id */
  target_id: number;
  /** 资源 */
  assets: PrettyNumber | undefined;
  /** 过期时间戳-秒 */
  expired_at: number;
  /** 使用状态 */
  use_status: PrettyNumberUseStatus;
  /** 绑定状态 */
  bind_status: PrettyNumberBindStatus;
  /** 备注 */
  remark: string;
  /** 创建时间 */
  ctime: number;
  /** 更新时间 */
  utime: number;
  /** 创建人 */
  creator: string;
  /** 编辑 */
  editor: string;
}

/** 靓号列表 */
export interface ListPrettyNumbersReq {
  page: Page | undefined;
  /** 归属类型 */
  target_type: TargetType;
  /** uid / room_id */
  target_id: number;
  /** 靓号ID */
  show_id: string;
  /** 靓号状态 */
  status: PrettyNumberUseStatus;
  /** 绑定状态 */
  bind_status: PrettyNumberBindStatus;
}

export interface ListPrettyNumbersRsp {
  page: Page | undefined;
  pretty_numbers: MgrPrettyNumber[];
}

/** 创建靓号 */
export interface CreatePrettyNumberReq {
  pretty_number: MgrPrettyNumber | undefined;
}

export interface CreatePrettyNumberRsp {}

/** 绑定靓号 */
export interface BindPrettyNumberReq {
  pretty_number: MgrPrettyNumber | undefined;
}

export interface BindPrettyNumberRsp {}

export interface UnBindPrettyNumberReq {
  /** 靓号记录ID */
  id: number;
}

export interface UnBindPrettyNumberRsp {}

function createBaseMgrPrettyNumber(): MgrPrettyNumber {
  return {
    id: 0,
    pretty_number: '',
    target_type: 0,
    target_id: 0,
    assets: undefined,
    expired_at: 0,
    use_status: 0,
    bind_status: 0,
    remark: '',
    ctime: 0,
    utime: 0,
    creator: '',
    editor: ''
  };
}

export const MgrPrettyNumber: MessageFns<MgrPrettyNumber> = {
  fromJSON(object: any): MgrPrettyNumber {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      pretty_number: isSet(object.pretty_number) ? globalThis.String(object.pretty_number) : '',
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      target_id: isSet(object.target_id) ? globalThis.Number(object.target_id) : 0,
      assets: isSet(object.assets) ? PrettyNumber.fromJSON(object.assets) : undefined,
      expired_at: isSet(object.expired_at) ? globalThis.Number(object.expired_at) : 0,
      use_status: isSet(object.use_status) ? prettyNumberUseStatusFromJSON(object.use_status) : 0,
      bind_status: isSet(object.bind_status) ? prettyNumberBindStatusFromJSON(object.bind_status) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      editor: isSet(object.editor) ? globalThis.String(object.editor) : ''
    };
  },

  create<I extends Exact<DeepPartial<MgrPrettyNumber>, I>>(base?: I): MgrPrettyNumber {
    return MgrPrettyNumber.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MgrPrettyNumber>, I>>(object: I): MgrPrettyNumber {
    const message = createBaseMgrPrettyNumber();
    message.id = object.id ?? 0;
    message.pretty_number = object.pretty_number ?? '';
    message.target_type = object.target_type ?? 0;
    message.target_id = object.target_id ?? 0;
    message.assets =
      object.assets !== undefined && object.assets !== null ? PrettyNumber.fromPartial(object.assets) : undefined;
    message.expired_at = object.expired_at ?? 0;
    message.use_status = object.use_status ?? 0;
    message.bind_status = object.bind_status ?? 0;
    message.remark = object.remark ?? '';
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    message.creator = object.creator ?? '';
    message.editor = object.editor ?? '';
    return message;
  }
};

function createBaseListPrettyNumbersReq(): ListPrettyNumbersReq {
  return { page: undefined, target_type: 0, target_id: 0, show_id: '', status: 0, bind_status: 0 };
}

export const ListPrettyNumbersReq: MessageFns<ListPrettyNumbersReq> = {
  fromJSON(object: any): ListPrettyNumbersReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      target_type: isSet(object.target_type) ? targetTypeFromJSON(object.target_type) : 0,
      target_id: isSet(object.target_id) ? globalThis.Number(object.target_id) : 0,
      show_id: isSet(object.show_id) ? globalThis.String(object.show_id) : '',
      status: isSet(object.status) ? prettyNumberUseStatusFromJSON(object.status) : 0,
      bind_status: isSet(object.bind_status) ? prettyNumberBindStatusFromJSON(object.bind_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListPrettyNumbersReq>, I>>(base?: I): ListPrettyNumbersReq {
    return ListPrettyNumbersReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPrettyNumbersReq>, I>>(object: I): ListPrettyNumbersReq {
    const message = createBaseListPrettyNumbersReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.target_type = object.target_type ?? 0;
    message.target_id = object.target_id ?? 0;
    message.show_id = object.show_id ?? '';
    message.status = object.status ?? 0;
    message.bind_status = object.bind_status ?? 0;
    return message;
  }
};

function createBaseListPrettyNumbersRsp(): ListPrettyNumbersRsp {
  return { page: undefined, pretty_numbers: [] };
}

export const ListPrettyNumbersRsp: MessageFns<ListPrettyNumbersRsp> = {
  fromJSON(object: any): ListPrettyNumbersRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      pretty_numbers: globalThis.Array.isArray(object?.pretty_numbers)
        ? object.pretty_numbers.map((e: any) => MgrPrettyNumber.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListPrettyNumbersRsp>, I>>(base?: I): ListPrettyNumbersRsp {
    return ListPrettyNumbersRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPrettyNumbersRsp>, I>>(object: I): ListPrettyNumbersRsp {
    const message = createBaseListPrettyNumbersRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.pretty_numbers = object.pretty_numbers?.map(e => MgrPrettyNumber.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreatePrettyNumberReq(): CreatePrettyNumberReq {
  return { pretty_number: undefined };
}

export const CreatePrettyNumberReq: MessageFns<CreatePrettyNumberReq> = {
  fromJSON(object: any): CreatePrettyNumberReq {
    return { pretty_number: isSet(object.pretty_number) ? MgrPrettyNumber.fromJSON(object.pretty_number) : undefined };
  },

  create<I extends Exact<DeepPartial<CreatePrettyNumberReq>, I>>(base?: I): CreatePrettyNumberReq {
    return CreatePrettyNumberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePrettyNumberReq>, I>>(object: I): CreatePrettyNumberReq {
    const message = createBaseCreatePrettyNumberReq();
    message.pretty_number =
      object.pretty_number !== undefined && object.pretty_number !== null
        ? MgrPrettyNumber.fromPartial(object.pretty_number)
        : undefined;
    return message;
  }
};

function createBaseCreatePrettyNumberRsp(): CreatePrettyNumberRsp {
  return {};
}

export const CreatePrettyNumberRsp: MessageFns<CreatePrettyNumberRsp> = {
  fromJSON(_: any): CreatePrettyNumberRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreatePrettyNumberRsp>, I>>(base?: I): CreatePrettyNumberRsp {
    return CreatePrettyNumberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePrettyNumberRsp>, I>>(_: I): CreatePrettyNumberRsp {
    const message = createBaseCreatePrettyNumberRsp();
    return message;
  }
};

function createBaseBindPrettyNumberReq(): BindPrettyNumberReq {
  return { pretty_number: undefined };
}

export const BindPrettyNumberReq: MessageFns<BindPrettyNumberReq> = {
  fromJSON(object: any): BindPrettyNumberReq {
    return { pretty_number: isSet(object.pretty_number) ? MgrPrettyNumber.fromJSON(object.pretty_number) : undefined };
  },

  create<I extends Exact<DeepPartial<BindPrettyNumberReq>, I>>(base?: I): BindPrettyNumberReq {
    return BindPrettyNumberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindPrettyNumberReq>, I>>(object: I): BindPrettyNumberReq {
    const message = createBaseBindPrettyNumberReq();
    message.pretty_number =
      object.pretty_number !== undefined && object.pretty_number !== null
        ? MgrPrettyNumber.fromPartial(object.pretty_number)
        : undefined;
    return message;
  }
};

function createBaseBindPrettyNumberRsp(): BindPrettyNumberRsp {
  return {};
}

export const BindPrettyNumberRsp: MessageFns<BindPrettyNumberRsp> = {
  fromJSON(_: any): BindPrettyNumberRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BindPrettyNumberRsp>, I>>(base?: I): BindPrettyNumberRsp {
    return BindPrettyNumberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindPrettyNumberRsp>, I>>(_: I): BindPrettyNumberRsp {
    const message = createBaseBindPrettyNumberRsp();
    return message;
  }
};

function createBaseUnBindPrettyNumberReq(): UnBindPrettyNumberReq {
  return { id: 0 };
}

export const UnBindPrettyNumberReq: MessageFns<UnBindPrettyNumberReq> = {
  fromJSON(object: any): UnBindPrettyNumberReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<UnBindPrettyNumberReq>, I>>(base?: I): UnBindPrettyNumberReq {
    return UnBindPrettyNumberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnBindPrettyNumberReq>, I>>(object: I): UnBindPrettyNumberReq {
    const message = createBaseUnBindPrettyNumberReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUnBindPrettyNumberRsp(): UnBindPrettyNumberRsp {
  return {};
}

export const UnBindPrettyNumberRsp: MessageFns<UnBindPrettyNumberRsp> = {
  fromJSON(_: any): UnBindPrettyNumberRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UnBindPrettyNumberRsp>, I>>(base?: I): UnBindPrettyNumberRsp {
    return UnBindPrettyNumberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UnBindPrettyNumberRsp>, I>>(_: I): UnBindPrettyNumberRsp {
    const message = createBaseUnBindPrettyNumberRsp();
    return message;
  }
};

export type PrettyNumberMgrDefinition = typeof PrettyNumberMgrDefinition;
export const PrettyNumberMgrDefinition = {
  name: 'PrettyNumberMgr',
  fullName: 'comm.mgr.privilege.PrettyNumberMgr',
  methods: {
    /** 列表 */
    listPrettyNumbers: {
      name: 'ListPrettyNumbers',
      requestType: ListPrettyNumbersReq,
      requestStream: false,
      responseType: ListPrettyNumbersRsp,
      responseStream: false,
      options: {}
    },
    /** 创建靓号 */
    createPrettyNumber: {
      name: 'CreatePrettyNumber',
      requestType: CreatePrettyNumberReq,
      requestStream: false,
      responseType: CreatePrettyNumberRsp,
      responseStream: false,
      options: {}
    },
    /** 绑定 */
    bindPrettyNumber: {
      name: 'BindPrettyNumber',
      requestType: BindPrettyNumberReq,
      requestStream: false,
      responseType: BindPrettyNumberRsp,
      responseStream: false,
      options: {}
    },
    /** 解绑 */
    unBindPrettyNumber: {
      name: 'UnBindPrettyNumber',
      requestType: UnBindPrettyNumberReq,
      requestStream: false,
      responseType: UnBindPrettyNumberRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
