// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/privilege/resource_package_distribute.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { CategoryID, categoryIDFromJSON } from '../../api/privilege/v2/privilege';

export const protobufPackage = 'comm.mgr.privilege';

export enum ResourcePackageDistributeStatus {
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_NONE = 0,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_REVIEW - 待审核 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_REVIEW = 1,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_DISTRIBUTION - 待发放 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_DISTRIBUTION = 2,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_ING - 发放中 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_ING = 3,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_SUCCESS - 成功 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_SUCCESS = 4,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_FAILURE - 失败 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_FAILURE = 5,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_CANCEL - 撤回 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_CANCEL = 6,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REJECTED - 审核拒绝 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REJECTED = 7,
  /** RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REVOKED - 审核撤回 */
  RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REVOKED = 8,
  UNRECOGNIZED = -1
}

export function resourcePackageDistributeStatusFromJSON(object: any): ResourcePackageDistributeStatus {
  switch (object) {
    case 0:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_NONE':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_NONE;
    case 1:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_REVIEW':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_REVIEW;
    case 2:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_DISTRIBUTION':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_PENDING_DISTRIBUTION;
    case 3:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_ING':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_ING;
    case 4:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_SUCCESS':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_SUCCESS;
    case 5:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_FAILURE':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_FAILURE;
    case 6:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_CANCEL':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_CANCEL;
    case 7:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REJECTED':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REJECTED;
    case 8:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REVOKED':
      return ResourcePackageDistributeStatus.RESOURCE_PACKAGE_DISTRIBUTE_STATUS_REVIEW_REVOKED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourcePackageDistributeStatus.UNRECOGNIZED;
  }
}

export enum ResourcePackageDistributeDetailStatus {
  RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_NONE = 0,
  /** RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_ING - 发放中 */
  RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_ING = 1,
  /** RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_SUCCESS - 成功 */
  RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_SUCCESS = 2,
  /** RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_FAILURE - 失败 */
  RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_FAILURE = 3,
  UNRECOGNIZED = -1
}

export function resourcePackageDistributeDetailStatusFromJSON(object: any): ResourcePackageDistributeDetailStatus {
  switch (object) {
    case 0:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_NONE':
      return ResourcePackageDistributeDetailStatus.RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_NONE;
    case 1:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_ING':
      return ResourcePackageDistributeDetailStatus.RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_ING;
    case 2:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_SUCCESS':
      return ResourcePackageDistributeDetailStatus.RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_SUCCESS;
    case 3:
    case 'RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_FAILURE':
      return ResourcePackageDistributeDetailStatus.RESOURCE_PACKAGE_DISTRIBUTE_DETAIL_STATUS_FAILURE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourcePackageDistributeDetailStatus.UNRECOGNIZED;
  }
}

/** 用户信息 */
export interface UserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
}

export interface ResourcePackageDistributeLogInfo {
  /** id */
  id: number;
  /** 资源包id */
  package_id: number;
  /** 资源包详情快照(JSON格式) */
  package_detail_snapshots: string;
  /** 接收资源包用户 */
  user_info: UserInfo | undefined;
  /** 发放原因 */
  reason: string;
  /** 计划发放时间 */
  plan_distribute_time: number;
  /** 多语言系统消息;key为:语言码,eg:en,ar,zh */
  system_messages: { [key: string]: SystemMessage };
  /** 申请Id */
  apply_id: number;
  /** 发放状态(1:待审核,2:待发放,3:成功,4:失败,5:撤回,6:审核拒绝) */
  distribute_status: ResourcePackageDistributeStatus;
  /** 实际发放时间 */
  actual_distribute_time: number;
  /** 发放失败原因 */
  failure_reason: string;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 修改时间 */
  updated_at: number;
  /** 修改人 */
  updater: string;
}

export interface ResourcePackageDistributeLogInfo_SystemMessagesEntry {
  key: string;
  value: SystemMessage | undefined;
}

export interface SystemMessage {
  /** 标题 */
  title: string;
  /** 内容 */
  content: string;
}

/** ResourcePackageDetailSnapshot 资源包详情快照 */
export interface ResourcePackageDetailSnapshot {
  /** 资源包详情ID */
  id: number;
  /** 资源类型 */
  resource_type: string;
  /** 资源ID */
  resource_id: number;
  /** 资源图片URL */
  icon_url: string;
  /** 扩展JSON */
  extend_json: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
}

export interface ResourcePackageDistributeDetailInfo {
  /** id */
  id: number;
  /** 资源包发放id */
  distribute_id: number;
  /** 资源包id */
  package_id: number;
  /** 接收资源包uid */
  uid: number;
  /** 单个资源快照 */
  package_detail_snapshot: ResourcePackageDetailSnapshot | undefined;
  /** 资源类型 */
  resource_type: string;
  /** 资源id */
  resource_id: number;
  /** 发放状态 */
  status: ResourcePackageDistributeDetailStatus;
  /** 发放时间 */
  distribute_time: number;
  /** 发放结果信息 */
  result_message: string;
}

export interface ListResourcePackageDistributeLogReq {
  page: Page | undefined;
  /** id */
  id: number;
  /** 资源包id */
  package_id: number;
  /** 接收资源包uid */
  uid: number;
  /** 发放状态(1:待审核,2:待发放,3:成功,4:失败,5:撤回,6:审核拒绝) */
  distribute_status: ResourcePackageDistributeStatus;
  /** 实际发放开始时间（时间戳） */
  actual_distribute_time_from: number;
  /** 实际发放结束时间（时间戳） */
  actual_distribute_time_to: number;
}

export interface ListResourcePackageDistributeLogRsp {
  page: Page | undefined;
  /** 资源包发放记录列表 */
  resource_package_distribute_log_infos: ResourcePackageDistributeLogInfo[];
}

export interface CreateResourcePackageDistributeApplyReq {
  /** 资源包Id */
  package_id: number;
  /** 申请原因 */
  reason: string;
  /** 计划发放时间 */
  plan_distribute_time: number;
  /** 申请下发用户id */
  uids: number[];
  /** 多语言系统消息;key为:语言码,eg:en,ar,zh */
  system_messages: { [key: string]: SystemMessage };
}

export interface CreateResourcePackageDistributeApplyReq_SystemMessagesEntry {
  key: string;
  value: SystemMessage | undefined;
}

export interface CreateResourcePackageDistributeApplyRsp {
  /** id */
  id: number;
}

export interface CancelResourcePackageDistributeReq {
  /** id */
  distribute_log_id: number;
}

export interface CancelResourcePackageDistributeRsp {}

export interface ListResourcePackageDistributeDetailReq {
  /** 资源包发放id */
  distribute_id: number;
}

export interface ListResourcePackageDistributeDetailRsp {
  /** 资源包发放详情列表 */
  detail_infos: ResourcePackageDistributeDetailInfo[];
}

/**
 * 资源包下发消息
 * Kafka-Topic: social-privilege-pkg-distribute-${anm}
 */
export interface ResourcePackageDistributeMessage {
  /** 下发用户id */
  uid: number;
  /** 资源包id */
  package_id: number;
  /** 下发的资源信息 */
  package_detail_infos: ResourcePackageDistributeMessage_PackageDetailInfo[];
  /** 多语言系统消息;key为:语言码,eg:en,ar,zh */
  system_messages: { [key: string]: SystemMessage };
}

export interface ResourcePackageDistributeMessage_PackageDetailInfo {
  /** id */
  id: number;
  /** 资源类型 */
  resource_type: string;
  /** 资源id */
  resource_id: number;
  /** 资源图片url */
  icon_url: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
  /** 背包ID */
  bag_id: number;
  /** 一级分类标识 */
  category_id: CategoryID;
  /** 二级类标识 */
  sub_category_id: CategoryID;
}

export interface ResourcePackageDistributeMessage_SystemMessagesEntry {
  key: string;
  value: SystemMessage | undefined;
}

function createBaseUserInfo(): UserInfo {
  return { uid: 0, show_uid: '', nickname: '', avatar: '' };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

function createBaseResourcePackageDistributeLogInfo(): ResourcePackageDistributeLogInfo {
  return {
    id: 0,
    package_id: 0,
    package_detail_snapshots: '',
    user_info: undefined,
    reason: '',
    plan_distribute_time: 0,
    system_messages: {},
    apply_id: 0,
    distribute_status: 0,
    actual_distribute_time: 0,
    failure_reason: '',
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: ''
  };
}

export const ResourcePackageDistributeLogInfo: MessageFns<ResourcePackageDistributeLogInfo> = {
  fromJSON(object: any): ResourcePackageDistributeLogInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      package_detail_snapshots: isSet(object.package_detail_snapshots)
        ? globalThis.String(object.package_detail_snapshots)
        : '',
      user_info: isSet(object.user_info) ? UserInfo.fromJSON(object.user_info) : undefined,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      plan_distribute_time: isSet(object.plan_distribute_time) ? globalThis.Number(object.plan_distribute_time) : 0,
      system_messages: isObject(object.system_messages)
        ? Object.entries(object.system_messages).reduce<{ [key: string]: SystemMessage }>((acc, [key, value]) => {
            acc[key] = SystemMessage.fromJSON(value);
            return acc;
          }, {})
        : {},
      apply_id: isSet(object.apply_id) ? globalThis.Number(object.apply_id) : 0,
      distribute_status: isSet(object.distribute_status)
        ? resourcePackageDistributeStatusFromJSON(object.distribute_status)
        : 0,
      actual_distribute_time: isSet(object.actual_distribute_time)
        ? globalThis.Number(object.actual_distribute_time)
        : 0,
      failure_reason: isSet(object.failure_reason) ? globalThis.String(object.failure_reason) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDistributeLogInfo>, I>>(
    base?: I
  ): ResourcePackageDistributeLogInfo {
    return ResourcePackageDistributeLogInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeLogInfo>, I>>(
    object: I
  ): ResourcePackageDistributeLogInfo {
    const message = createBaseResourcePackageDistributeLogInfo();
    message.id = object.id ?? 0;
    message.package_id = object.package_id ?? 0;
    message.package_detail_snapshots = object.package_detail_snapshots ?? '';
    message.user_info =
      object.user_info !== undefined && object.user_info !== null ? UserInfo.fromPartial(object.user_info) : undefined;
    message.reason = object.reason ?? '';
    message.plan_distribute_time = object.plan_distribute_time ?? 0;
    message.system_messages = Object.entries(object.system_messages ?? {}).reduce<{ [key: string]: SystemMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SystemMessage.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.apply_id = object.apply_id ?? 0;
    message.distribute_status = object.distribute_status ?? 0;
    message.actual_distribute_time = object.actual_distribute_time ?? 0;
    message.failure_reason = object.failure_reason ?? '';
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    return message;
  }
};

function createBaseResourcePackageDistributeLogInfo_SystemMessagesEntry(): ResourcePackageDistributeLogInfo_SystemMessagesEntry {
  return { key: '', value: undefined };
}

export const ResourcePackageDistributeLogInfo_SystemMessagesEntry: MessageFns<ResourcePackageDistributeLogInfo_SystemMessagesEntry> =
  {
    fromJSON(object: any): ResourcePackageDistributeLogInfo_SystemMessagesEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? SystemMessage.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<ResourcePackageDistributeLogInfo_SystemMessagesEntry>, I>>(
      base?: I
    ): ResourcePackageDistributeLogInfo_SystemMessagesEntry {
      return ResourcePackageDistributeLogInfo_SystemMessagesEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeLogInfo_SystemMessagesEntry>, I>>(
      object: I
    ): ResourcePackageDistributeLogInfo_SystemMessagesEntry {
      const message = createBaseResourcePackageDistributeLogInfo_SystemMessagesEntry();
      message.key = object.key ?? '';
      message.value =
        object.value !== undefined && object.value !== null ? SystemMessage.fromPartial(object.value) : undefined;
      return message;
    }
  };

function createBaseSystemMessage(): SystemMessage {
  return { title: '', content: '' };
}

export const SystemMessage: MessageFns<SystemMessage> = {
  fromJSON(object: any): SystemMessage {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : ''
    };
  },

  create<I extends Exact<DeepPartial<SystemMessage>, I>>(base?: I): SystemMessage {
    return SystemMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SystemMessage>, I>>(object: I): SystemMessage {
    const message = createBaseSystemMessage();
    message.title = object.title ?? '';
    message.content = object.content ?? '';
    return message;
  }
};

function createBaseResourcePackageDetailSnapshot(): ResourcePackageDetailSnapshot {
  return { id: 0, resource_type: '', resource_id: 0, icon_url: '', extend_json: '', amount: 0, effective_time: 0 };
}

export const ResourcePackageDetailSnapshot: MessageFns<ResourcePackageDetailSnapshot> = {
  fromJSON(object: any): ResourcePackageDetailSnapshot {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDetailSnapshot>, I>>(base?: I): ResourcePackageDetailSnapshot {
    return ResourcePackageDetailSnapshot.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDetailSnapshot>, I>>(
    object: I
  ): ResourcePackageDetailSnapshot {
    const message = createBaseResourcePackageDetailSnapshot();
    message.id = object.id ?? 0;
    message.resource_type = object.resource_type ?? '';
    message.resource_id = object.resource_id ?? 0;
    message.icon_url = object.icon_url ?? '';
    message.extend_json = object.extend_json ?? '';
    message.amount = object.amount ?? 0;
    message.effective_time = object.effective_time ?? 0;
    return message;
  }
};

function createBaseResourcePackageDistributeDetailInfo(): ResourcePackageDistributeDetailInfo {
  return {
    id: 0,
    distribute_id: 0,
    package_id: 0,
    uid: 0,
    package_detail_snapshot: undefined,
    resource_type: '',
    resource_id: 0,
    status: 0,
    distribute_time: 0,
    result_message: ''
  };
}

export const ResourcePackageDistributeDetailInfo: MessageFns<ResourcePackageDistributeDetailInfo> = {
  fromJSON(object: any): ResourcePackageDistributeDetailInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      distribute_id: isSet(object.distribute_id) ? globalThis.Number(object.distribute_id) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      package_detail_snapshot: isSet(object.package_detail_snapshot)
        ? ResourcePackageDetailSnapshot.fromJSON(object.package_detail_snapshot)
        : undefined,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      status: isSet(object.status) ? resourcePackageDistributeDetailStatusFromJSON(object.status) : 0,
      distribute_time: isSet(object.distribute_time) ? globalThis.Number(object.distribute_time) : 0,
      result_message: isSet(object.result_message) ? globalThis.String(object.result_message) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDistributeDetailInfo>, I>>(
    base?: I
  ): ResourcePackageDistributeDetailInfo {
    return ResourcePackageDistributeDetailInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeDetailInfo>, I>>(
    object: I
  ): ResourcePackageDistributeDetailInfo {
    const message = createBaseResourcePackageDistributeDetailInfo();
    message.id = object.id ?? 0;
    message.distribute_id = object.distribute_id ?? 0;
    message.package_id = object.package_id ?? 0;
    message.uid = object.uid ?? 0;
    message.package_detail_snapshot =
      object.package_detail_snapshot !== undefined && object.package_detail_snapshot !== null
        ? ResourcePackageDetailSnapshot.fromPartial(object.package_detail_snapshot)
        : undefined;
    message.resource_type = object.resource_type ?? '';
    message.resource_id = object.resource_id ?? 0;
    message.status = object.status ?? 0;
    message.distribute_time = object.distribute_time ?? 0;
    message.result_message = object.result_message ?? '';
    return message;
  }
};

function createBaseListResourcePackageDistributeLogReq(): ListResourcePackageDistributeLogReq {
  return {
    page: undefined,
    id: 0,
    package_id: 0,
    uid: 0,
    distribute_status: 0,
    actual_distribute_time_from: 0,
    actual_distribute_time_to: 0
  };
}

export const ListResourcePackageDistributeLogReq: MessageFns<ListResourcePackageDistributeLogReq> = {
  fromJSON(object: any): ListResourcePackageDistributeLogReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      distribute_status: isSet(object.distribute_status)
        ? resourcePackageDistributeStatusFromJSON(object.distribute_status)
        : 0,
      actual_distribute_time_from: isSet(object.actual_distribute_time_from)
        ? globalThis.Number(object.actual_distribute_time_from)
        : 0,
      actual_distribute_time_to: isSet(object.actual_distribute_time_to)
        ? globalThis.Number(object.actual_distribute_time_to)
        : 0
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDistributeLogReq>, I>>(
    base?: I
  ): ListResourcePackageDistributeLogReq {
    return ListResourcePackageDistributeLogReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDistributeLogReq>, I>>(
    object: I
  ): ListResourcePackageDistributeLogReq {
    const message = createBaseListResourcePackageDistributeLogReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.package_id = object.package_id ?? 0;
    message.uid = object.uid ?? 0;
    message.distribute_status = object.distribute_status ?? 0;
    message.actual_distribute_time_from = object.actual_distribute_time_from ?? 0;
    message.actual_distribute_time_to = object.actual_distribute_time_to ?? 0;
    return message;
  }
};

function createBaseListResourcePackageDistributeLogRsp(): ListResourcePackageDistributeLogRsp {
  return { page: undefined, resource_package_distribute_log_infos: [] };
}

export const ListResourcePackageDistributeLogRsp: MessageFns<ListResourcePackageDistributeLogRsp> = {
  fromJSON(object: any): ListResourcePackageDistributeLogRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      resource_package_distribute_log_infos: globalThis.Array.isArray(object?.resource_package_distribute_log_infos)
        ? object.resource_package_distribute_log_infos.map((e: any) => ResourcePackageDistributeLogInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDistributeLogRsp>, I>>(
    base?: I
  ): ListResourcePackageDistributeLogRsp {
    return ListResourcePackageDistributeLogRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDistributeLogRsp>, I>>(
    object: I
  ): ListResourcePackageDistributeLogRsp {
    const message = createBaseListResourcePackageDistributeLogRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.resource_package_distribute_log_infos =
      object.resource_package_distribute_log_infos?.map(e => ResourcePackageDistributeLogInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateResourcePackageDistributeApplyReq(): CreateResourcePackageDistributeApplyReq {
  return { package_id: 0, reason: '', plan_distribute_time: 0, uids: [], system_messages: {} };
}

export const CreateResourcePackageDistributeApplyReq: MessageFns<CreateResourcePackageDistributeApplyReq> = {
  fromJSON(object: any): CreateResourcePackageDistributeApplyReq {
    return {
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      plan_distribute_time: isSet(object.plan_distribute_time) ? globalThis.Number(object.plan_distribute_time) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      system_messages: isObject(object.system_messages)
        ? Object.entries(object.system_messages).reduce<{ [key: string]: SystemMessage }>((acc, [key, value]) => {
            acc[key] = SystemMessage.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyReq>, I>>(
    base?: I
  ): CreateResourcePackageDistributeApplyReq {
    return CreateResourcePackageDistributeApplyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyReq>, I>>(
    object: I
  ): CreateResourcePackageDistributeApplyReq {
    const message = createBaseCreateResourcePackageDistributeApplyReq();
    message.package_id = object.package_id ?? 0;
    message.reason = object.reason ?? '';
    message.plan_distribute_time = object.plan_distribute_time ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.system_messages = Object.entries(object.system_messages ?? {}).reduce<{ [key: string]: SystemMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SystemMessage.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseCreateResourcePackageDistributeApplyReq_SystemMessagesEntry(): CreateResourcePackageDistributeApplyReq_SystemMessagesEntry {
  return { key: '', value: undefined };
}

export const CreateResourcePackageDistributeApplyReq_SystemMessagesEntry: MessageFns<CreateResourcePackageDistributeApplyReq_SystemMessagesEntry> =
  {
    fromJSON(object: any): CreateResourcePackageDistributeApplyReq_SystemMessagesEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? SystemMessage.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyReq_SystemMessagesEntry>, I>>(
      base?: I
    ): CreateResourcePackageDistributeApplyReq_SystemMessagesEntry {
      return CreateResourcePackageDistributeApplyReq_SystemMessagesEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyReq_SystemMessagesEntry>, I>>(
      object: I
    ): CreateResourcePackageDistributeApplyReq_SystemMessagesEntry {
      const message = createBaseCreateResourcePackageDistributeApplyReq_SystemMessagesEntry();
      message.key = object.key ?? '';
      message.value =
        object.value !== undefined && object.value !== null ? SystemMessage.fromPartial(object.value) : undefined;
      return message;
    }
  };

function createBaseCreateResourcePackageDistributeApplyRsp(): CreateResourcePackageDistributeApplyRsp {
  return { id: 0 };
}

export const CreateResourcePackageDistributeApplyRsp: MessageFns<CreateResourcePackageDistributeApplyRsp> = {
  fromJSON(object: any): CreateResourcePackageDistributeApplyRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyRsp>, I>>(
    base?: I
  ): CreateResourcePackageDistributeApplyRsp {
    return CreateResourcePackageDistributeApplyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageDistributeApplyRsp>, I>>(
    object: I
  ): CreateResourcePackageDistributeApplyRsp {
    const message = createBaseCreateResourcePackageDistributeApplyRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseCancelResourcePackageDistributeReq(): CancelResourcePackageDistributeReq {
  return { distribute_log_id: 0 };
}

export const CancelResourcePackageDistributeReq: MessageFns<CancelResourcePackageDistributeReq> = {
  fromJSON(object: any): CancelResourcePackageDistributeReq {
    return { distribute_log_id: isSet(object.distribute_log_id) ? globalThis.Number(object.distribute_log_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CancelResourcePackageDistributeReq>, I>>(
    base?: I
  ): CancelResourcePackageDistributeReq {
    return CancelResourcePackageDistributeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelResourcePackageDistributeReq>, I>>(
    object: I
  ): CancelResourcePackageDistributeReq {
    const message = createBaseCancelResourcePackageDistributeReq();
    message.distribute_log_id = object.distribute_log_id ?? 0;
    return message;
  }
};

function createBaseCancelResourcePackageDistributeRsp(): CancelResourcePackageDistributeRsp {
  return {};
}

export const CancelResourcePackageDistributeRsp: MessageFns<CancelResourcePackageDistributeRsp> = {
  fromJSON(_: any): CancelResourcePackageDistributeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CancelResourcePackageDistributeRsp>, I>>(
    base?: I
  ): CancelResourcePackageDistributeRsp {
    return CancelResourcePackageDistributeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CancelResourcePackageDistributeRsp>, I>>(
    _: I
  ): CancelResourcePackageDistributeRsp {
    const message = createBaseCancelResourcePackageDistributeRsp();
    return message;
  }
};

function createBaseListResourcePackageDistributeDetailReq(): ListResourcePackageDistributeDetailReq {
  return { distribute_id: 0 };
}

export const ListResourcePackageDistributeDetailReq: MessageFns<ListResourcePackageDistributeDetailReq> = {
  fromJSON(object: any): ListResourcePackageDistributeDetailReq {
    return { distribute_id: isSet(object.distribute_id) ? globalThis.Number(object.distribute_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDistributeDetailReq>, I>>(
    base?: I
  ): ListResourcePackageDistributeDetailReq {
    return ListResourcePackageDistributeDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDistributeDetailReq>, I>>(
    object: I
  ): ListResourcePackageDistributeDetailReq {
    const message = createBaseListResourcePackageDistributeDetailReq();
    message.distribute_id = object.distribute_id ?? 0;
    return message;
  }
};

function createBaseListResourcePackageDistributeDetailRsp(): ListResourcePackageDistributeDetailRsp {
  return { detail_infos: [] };
}

export const ListResourcePackageDistributeDetailRsp: MessageFns<ListResourcePackageDistributeDetailRsp> = {
  fromJSON(object: any): ListResourcePackageDistributeDetailRsp {
    return {
      detail_infos: globalThis.Array.isArray(object?.detail_infos)
        ? object.detail_infos.map((e: any) => ResourcePackageDistributeDetailInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDistributeDetailRsp>, I>>(
    base?: I
  ): ListResourcePackageDistributeDetailRsp {
    return ListResourcePackageDistributeDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDistributeDetailRsp>, I>>(
    object: I
  ): ListResourcePackageDistributeDetailRsp {
    const message = createBaseListResourcePackageDistributeDetailRsp();
    message.detail_infos = object.detail_infos?.map(e => ResourcePackageDistributeDetailInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseResourcePackageDistributeMessage(): ResourcePackageDistributeMessage {
  return { uid: 0, package_id: 0, package_detail_infos: [], system_messages: {} };
}

export const ResourcePackageDistributeMessage: MessageFns<ResourcePackageDistributeMessage> = {
  fromJSON(object: any): ResourcePackageDistributeMessage {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      package_detail_infos: globalThis.Array.isArray(object?.package_detail_infos)
        ? object.package_detail_infos.map((e: any) => ResourcePackageDistributeMessage_PackageDetailInfo.fromJSON(e))
        : [],
      system_messages: isObject(object.system_messages)
        ? Object.entries(object.system_messages).reduce<{ [key: string]: SystemMessage }>((acc, [key, value]) => {
            acc[key] = SystemMessage.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDistributeMessage>, I>>(
    base?: I
  ): ResourcePackageDistributeMessage {
    return ResourcePackageDistributeMessage.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeMessage>, I>>(
    object: I
  ): ResourcePackageDistributeMessage {
    const message = createBaseResourcePackageDistributeMessage();
    message.uid = object.uid ?? 0;
    message.package_id = object.package_id ?? 0;
    message.package_detail_infos =
      object.package_detail_infos?.map(e => ResourcePackageDistributeMessage_PackageDetailInfo.fromPartial(e)) || [];
    message.system_messages = Object.entries(object.system_messages ?? {}).reduce<{ [key: string]: SystemMessage }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = SystemMessage.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseResourcePackageDistributeMessage_PackageDetailInfo(): ResourcePackageDistributeMessage_PackageDetailInfo {
  return {
    id: 0,
    resource_type: '',
    resource_id: 0,
    icon_url: '',
    amount: 0,
    effective_time: 0,
    bag_id: 0,
    category_id: 0,
    sub_category_id: 0
  };
}

export const ResourcePackageDistributeMessage_PackageDetailInfo: MessageFns<ResourcePackageDistributeMessage_PackageDetailInfo> =
  {
    fromJSON(object: any): ResourcePackageDistributeMessage_PackageDetailInfo {
      return {
        id: isSet(object.id) ? globalThis.Number(object.id) : 0,
        resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
        resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
        icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
        amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
        effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0,
        bag_id: isSet(object.bag_id) ? globalThis.Number(object.bag_id) : 0,
        category_id: isSet(object.category_id) ? categoryIDFromJSON(object.category_id) : 0,
        sub_category_id: isSet(object.sub_category_id) ? categoryIDFromJSON(object.sub_category_id) : 0
      };
    },

    create<I extends Exact<DeepPartial<ResourcePackageDistributeMessage_PackageDetailInfo>, I>>(
      base?: I
    ): ResourcePackageDistributeMessage_PackageDetailInfo {
      return ResourcePackageDistributeMessage_PackageDetailInfo.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeMessage_PackageDetailInfo>, I>>(
      object: I
    ): ResourcePackageDistributeMessage_PackageDetailInfo {
      const message = createBaseResourcePackageDistributeMessage_PackageDetailInfo();
      message.id = object.id ?? 0;
      message.resource_type = object.resource_type ?? '';
      message.resource_id = object.resource_id ?? 0;
      message.icon_url = object.icon_url ?? '';
      message.amount = object.amount ?? 0;
      message.effective_time = object.effective_time ?? 0;
      message.bag_id = object.bag_id ?? 0;
      message.category_id = object.category_id ?? 0;
      message.sub_category_id = object.sub_category_id ?? 0;
      return message;
    }
  };

function createBaseResourcePackageDistributeMessage_SystemMessagesEntry(): ResourcePackageDistributeMessage_SystemMessagesEntry {
  return { key: '', value: undefined };
}

export const ResourcePackageDistributeMessage_SystemMessagesEntry: MessageFns<ResourcePackageDistributeMessage_SystemMessagesEntry> =
  {
    fromJSON(object: any): ResourcePackageDistributeMessage_SystemMessagesEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? SystemMessage.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<ResourcePackageDistributeMessage_SystemMessagesEntry>, I>>(
      base?: I
    ): ResourcePackageDistributeMessage_SystemMessagesEntry {
      return ResourcePackageDistributeMessage_SystemMessagesEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<ResourcePackageDistributeMessage_SystemMessagesEntry>, I>>(
      object: I
    ): ResourcePackageDistributeMessage_SystemMessagesEntry {
      const message = createBaseResourcePackageDistributeMessage_SystemMessagesEntry();
      message.key = object.key ?? '';
      message.value =
        object.value !== undefined && object.value !== null ? SystemMessage.fromPartial(object.value) : undefined;
      return message;
    }
  };

export type ResourcePackageDistributeDefinition = typeof ResourcePackageDistributeDefinition;
export const ResourcePackageDistributeDefinition = {
  name: 'ResourcePackageDistribute',
  fullName: 'comm.mgr.privilege.ResourcePackageDistribute',
  methods: {
    /** 资源包发放记录列表 */
    listResourcePackageDistributeLog: {
      name: 'ListResourcePackageDistributeLog',
      requestType: ListResourcePackageDistributeLogReq,
      requestStream: false,
      responseType: ListResourcePackageDistributeLogRsp,
      responseStream: false,
      options: {}
    },
    /** 新建资源包发放申请 */
    createResourcePackageDistributeApply: {
      name: 'CreateResourcePackageDistributeApply',
      requestType: CreateResourcePackageDistributeApplyReq,
      requestStream: false,
      responseType: CreateResourcePackageDistributeApplyRsp,
      responseStream: false,
      options: {}
    },
    /** 取消资源包发放 */
    cancelResourcePackageDistribute: {
      name: 'CancelResourcePackageDistribute',
      requestType: CancelResourcePackageDistributeReq,
      requestStream: false,
      responseType: CancelResourcePackageDistributeRsp,
      responseStream: false,
      options: {}
    },
    /** 资源包发放详情列表 */
    listResourcePackageDistributeDetail: {
      name: 'ListResourcePackageDistributeDetail',
      requestType: ListResourcePackageDistributeDetailReq,
      requestStream: false,
      responseType: ListResourcePackageDistributeDetailRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
