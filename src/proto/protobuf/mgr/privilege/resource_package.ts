// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/privilege/resource_package.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.privilege';

export enum ResourcePackageApprovalType {
  RESOURCE_PACKAGE_APPROVAL_TYPE_NONE = 0,
  /** RESOURCE_PACKAGE_APPROVAL_TYPE_NO_APPROVAL - 无需审批 */
  RESOURCE_PACKAGE_APPROVAL_TYPE_NO_APPROVAL = 1,
  /** RESOURCE_PACKAGE_APPROVAL_TYPE_MUST_APPROVAL - 需要审批 */
  RESOURCE_PACKAGE_APPROVAL_TYPE_MUST_APPROVAL = 2,
  UNRECOGNIZED = -1
}

export function resourcePackageApprovalTypeFromJSON(object: any): ResourcePackageApprovalType {
  switch (object) {
    case 0:
    case 'RESOURCE_PACKAGE_APPROVAL_TYPE_NONE':
      return ResourcePackageApprovalType.RESOURCE_PACKAGE_APPROVAL_TYPE_NONE;
    case 1:
    case 'RESOURCE_PACKAGE_APPROVAL_TYPE_NO_APPROVAL':
      return ResourcePackageApprovalType.RESOURCE_PACKAGE_APPROVAL_TYPE_NO_APPROVAL;
    case 2:
    case 'RESOURCE_PACKAGE_APPROVAL_TYPE_MUST_APPROVAL':
      return ResourcePackageApprovalType.RESOURCE_PACKAGE_APPROVAL_TYPE_MUST_APPROVAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ResourcePackageApprovalType.UNRECOGNIZED;
  }
}

export interface ResourcePackageAndDetail {
  resource_package_info: ResourcePackageInfo | undefined;
  resource_package_detail_infos: ResourcePackageDetailInfo[];
}

export interface ResourcePackageInfo {
  /** id */
  id: number;
  /** 资源包名称 */
  name: string;
  /** 资源包图标URL */
  icon_url: string;
  /** 审批类型 (1:无需审批, 2:需要审批) */
  approval_type: ResourcePackageApprovalType;
  /** 备注 */
  remark: string;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 修改时间 */
  updated_at: number;
  /** 修改人 */
  updater: string;
}

export interface ListResourcePackageReq {
  page: Page | undefined;
  /** id */
  id: number;
  /** 资源包名称 */
  name: string;
  /** 审批类型 (1:无需审批, 2:需要审批) */
  approval_type: ResourcePackageApprovalType;
  /** 创建时间 */
  created_at_start: number;
  /** 创建时间 */
  created_at_end: number;
  /** 创建人 */
  creator: string;
  /** 修改时间 */
  updated_at_start: number;
  /** 修改时间 */
  updated_at_end: number;
  /** 修改人 */
  updater: string;
}

export interface ListResourcePackageRsp {
  page: Page | undefined;
  /** 资源包列表 */
  items: ResourcePackageAndDetail[];
}

export interface CreateResourcePackageReq {
  /** 资源包名称 */
  name: string;
  /** 资源包图标URL */
  icon_url: string;
  /** 审批类型 (1:无需审批, 2:需要审批) */
  approval_type: ResourcePackageApprovalType;
  /** 备注 */
  remark: string;
}

export interface CreateResourcePackageRsp {
  /** id */
  id: number;
}

export interface UpdateResourcePackageReq {
  /** id */
  id: number;
  /** 资源包名称 */
  name: string;
  /** 资源包图标URL */
  icon_url: string;
  /** 审批类型 (1:无需审批, 2:需要审批) */
  approval_type: ResourcePackageApprovalType;
  /** 备注 */
  remark: string;
}

export interface UpdateResourcePackageRsp {}

export interface DeleteResourcePackageReq {
  /** id */
  id: number;
}

export interface DeleteResourcePackageRsp {}

/** 资源包详情 */
export interface ResourcePackageDetailInfo {
  /** id */
  id: number;
  /** 资源包id */
  package_id: number;
  /** 资源类型 */
  resource_type: string;
  /** 资源id */
  resource_id: number;
  /** 资源图片url */
  icon_url: string;
  /** 扩展json */
  extend_json: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  creator: string;
  /** 修改时间 */
  updated_at: number;
  /** 修改人 */
  updater: string;
}

export interface ListResourcePackageDetailReq {
  page: Page | undefined;
  /** 资源包id */
  package_id: number;
}

export interface ListResourcePackageDetailRsp {
  page: Page | undefined;
  /** 资源包详情列表 */
  resource_package_detail_infos: ResourcePackageDetailInfo[];
}

export interface CreateResourcePackageDetailReq {
  /** 资源包id */
  package_id: number;
  /** 资源类型 */
  resource_type: string;
  /** 资源id */
  resource_id: number;
  /** 资源图片url */
  icon_url: string;
  /** 扩展json */
  extend_json: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
}

export interface CreateResourcePackageDetailRsp {
  /** id */
  id: number;
}

export interface UpdateResourcePackageDetailReq {
  /** id */
  id: number;
  /** 资源类型(coin:金币,gift:礼物) */
  resource_type: string;
  /** 资源id */
  resource_id: number;
  /** 资源图片url */
  icon_url: string;
  /** 扩展json */
  extend_json: string;
  /** 数量 */
  amount: number;
  /** 有效时间(单位:秒,值<=0:永久) */
  effective_time: number;
}

export interface UpdateResourcePackageDetailRsp {}

export interface DeleteResourcePackageDetailReq {
  /** id */
  id: number;
}

export interface DeleteResourcePackageDetailRsp {}

function createBaseResourcePackageAndDetail(): ResourcePackageAndDetail {
  return { resource_package_info: undefined, resource_package_detail_infos: [] };
}

export const ResourcePackageAndDetail: MessageFns<ResourcePackageAndDetail> = {
  fromJSON(object: any): ResourcePackageAndDetail {
    return {
      resource_package_info: isSet(object.resource_package_info)
        ? ResourcePackageInfo.fromJSON(object.resource_package_info)
        : undefined,
      resource_package_detail_infos: globalThis.Array.isArray(object?.resource_package_detail_infos)
        ? object.resource_package_detail_infos.map((e: any) => ResourcePackageDetailInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageAndDetail>, I>>(base?: I): ResourcePackageAndDetail {
    return ResourcePackageAndDetail.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageAndDetail>, I>>(object: I): ResourcePackageAndDetail {
    const message = createBaseResourcePackageAndDetail();
    message.resource_package_info =
      object.resource_package_info !== undefined && object.resource_package_info !== null
        ? ResourcePackageInfo.fromPartial(object.resource_package_info)
        : undefined;
    message.resource_package_detail_infos =
      object.resource_package_detail_infos?.map(e => ResourcePackageDetailInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseResourcePackageInfo(): ResourcePackageInfo {
  return {
    id: 0,
    name: '',
    icon_url: '',
    approval_type: 0,
    remark: '',
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: ''
  };
}

export const ResourcePackageInfo: MessageFns<ResourcePackageInfo> = {
  fromJSON(object: any): ResourcePackageInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      approval_type: isSet(object.approval_type) ? resourcePackageApprovalTypeFromJSON(object.approval_type) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageInfo>, I>>(base?: I): ResourcePackageInfo {
    return ResourcePackageInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageInfo>, I>>(object: I): ResourcePackageInfo {
    const message = createBaseResourcePackageInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.icon_url = object.icon_url ?? '';
    message.approval_type = object.approval_type ?? 0;
    message.remark = object.remark ?? '';
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    return message;
  }
};

function createBaseListResourcePackageReq(): ListResourcePackageReq {
  return {
    page: undefined,
    id: 0,
    name: '',
    approval_type: 0,
    created_at_start: 0,
    created_at_end: 0,
    creator: '',
    updated_at_start: 0,
    updated_at_end: 0,
    updater: ''
  };
}

export const ListResourcePackageReq: MessageFns<ListResourcePackageReq> = {
  fromJSON(object: any): ListResourcePackageReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      approval_type: isSet(object.approval_type) ? resourcePackageApprovalTypeFromJSON(object.approval_type) : 0,
      created_at_start: isSet(object.created_at_start) ? globalThis.Number(object.created_at_start) : 0,
      created_at_end: isSet(object.created_at_end) ? globalThis.Number(object.created_at_end) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at_start: isSet(object.updated_at_start) ? globalThis.Number(object.updated_at_start) : 0,
      updated_at_end: isSet(object.updated_at_end) ? globalThis.Number(object.updated_at_end) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageReq>, I>>(base?: I): ListResourcePackageReq {
    return ListResourcePackageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageReq>, I>>(object: I): ListResourcePackageReq {
    const message = createBaseListResourcePackageReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.approval_type = object.approval_type ?? 0;
    message.created_at_start = object.created_at_start ?? 0;
    message.created_at_end = object.created_at_end ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at_start = object.updated_at_start ?? 0;
    message.updated_at_end = object.updated_at_end ?? 0;
    message.updater = object.updater ?? '';
    return message;
  }
};

function createBaseListResourcePackageRsp(): ListResourcePackageRsp {
  return { page: undefined, items: [] };
}

export const ListResourcePackageRsp: MessageFns<ListResourcePackageRsp> = {
  fromJSON(object: any): ListResourcePackageRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => ResourcePackageAndDetail.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageRsp>, I>>(base?: I): ListResourcePackageRsp {
    return ListResourcePackageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageRsp>, I>>(object: I): ListResourcePackageRsp {
    const message = createBaseListResourcePackageRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => ResourcePackageAndDetail.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateResourcePackageReq(): CreateResourcePackageReq {
  return { name: '', icon_url: '', approval_type: 0, remark: '' };
}

export const CreateResourcePackageReq: MessageFns<CreateResourcePackageReq> = {
  fromJSON(object: any): CreateResourcePackageReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      approval_type: isSet(object.approval_type) ? resourcePackageApprovalTypeFromJSON(object.approval_type) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageReq>, I>>(base?: I): CreateResourcePackageReq {
    return CreateResourcePackageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageReq>, I>>(object: I): CreateResourcePackageReq {
    const message = createBaseCreateResourcePackageReq();
    message.name = object.name ?? '';
    message.icon_url = object.icon_url ?? '';
    message.approval_type = object.approval_type ?? 0;
    message.remark = object.remark ?? '';
    return message;
  }
};

function createBaseCreateResourcePackageRsp(): CreateResourcePackageRsp {
  return { id: 0 };
}

export const CreateResourcePackageRsp: MessageFns<CreateResourcePackageRsp> = {
  fromJSON(object: any): CreateResourcePackageRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageRsp>, I>>(base?: I): CreateResourcePackageRsp {
    return CreateResourcePackageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageRsp>, I>>(object: I): CreateResourcePackageRsp {
    const message = createBaseCreateResourcePackageRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateResourcePackageReq(): UpdateResourcePackageReq {
  return { id: 0, name: '', icon_url: '', approval_type: 0, remark: '' };
}

export const UpdateResourcePackageReq: MessageFns<UpdateResourcePackageReq> = {
  fromJSON(object: any): UpdateResourcePackageReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      approval_type: isSet(object.approval_type) ? resourcePackageApprovalTypeFromJSON(object.approval_type) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateResourcePackageReq>, I>>(base?: I): UpdateResourcePackageReq {
    return UpdateResourcePackageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourcePackageReq>, I>>(object: I): UpdateResourcePackageReq {
    const message = createBaseUpdateResourcePackageReq();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.icon_url = object.icon_url ?? '';
    message.approval_type = object.approval_type ?? 0;
    message.remark = object.remark ?? '';
    return message;
  }
};

function createBaseUpdateResourcePackageRsp(): UpdateResourcePackageRsp {
  return {};
}

export const UpdateResourcePackageRsp: MessageFns<UpdateResourcePackageRsp> = {
  fromJSON(_: any): UpdateResourcePackageRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateResourcePackageRsp>, I>>(base?: I): UpdateResourcePackageRsp {
    return UpdateResourcePackageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourcePackageRsp>, I>>(_: I): UpdateResourcePackageRsp {
    const message = createBaseUpdateResourcePackageRsp();
    return message;
  }
};

function createBaseDeleteResourcePackageReq(): DeleteResourcePackageReq {
  return { id: 0 };
}

export const DeleteResourcePackageReq: MessageFns<DeleteResourcePackageReq> = {
  fromJSON(object: any): DeleteResourcePackageReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteResourcePackageReq>, I>>(base?: I): DeleteResourcePackageReq {
    return DeleteResourcePackageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourcePackageReq>, I>>(object: I): DeleteResourcePackageReq {
    const message = createBaseDeleteResourcePackageReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteResourcePackageRsp(): DeleteResourcePackageRsp {
  return {};
}

export const DeleteResourcePackageRsp: MessageFns<DeleteResourcePackageRsp> = {
  fromJSON(_: any): DeleteResourcePackageRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteResourcePackageRsp>, I>>(base?: I): DeleteResourcePackageRsp {
    return DeleteResourcePackageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourcePackageRsp>, I>>(_: I): DeleteResourcePackageRsp {
    const message = createBaseDeleteResourcePackageRsp();
    return message;
  }
};

function createBaseResourcePackageDetailInfo(): ResourcePackageDetailInfo {
  return {
    id: 0,
    package_id: 0,
    resource_type: '',
    resource_id: 0,
    icon_url: '',
    extend_json: '',
    amount: 0,
    effective_time: 0,
    created_at: 0,
    creator: '',
    updated_at: 0,
    updater: ''
  };
}

export const ResourcePackageDetailInfo: MessageFns<ResourcePackageDetailInfo> = {
  fromJSON(object: any): ResourcePackageDetailInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : ''
    };
  },

  create<I extends Exact<DeepPartial<ResourcePackageDetailInfo>, I>>(base?: I): ResourcePackageDetailInfo {
    return ResourcePackageDetailInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ResourcePackageDetailInfo>, I>>(object: I): ResourcePackageDetailInfo {
    const message = createBaseResourcePackageDetailInfo();
    message.id = object.id ?? 0;
    message.package_id = object.package_id ?? 0;
    message.resource_type = object.resource_type ?? '';
    message.resource_id = object.resource_id ?? 0;
    message.icon_url = object.icon_url ?? '';
    message.extend_json = object.extend_json ?? '';
    message.amount = object.amount ?? 0;
    message.effective_time = object.effective_time ?? 0;
    message.created_at = object.created_at ?? 0;
    message.creator = object.creator ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updater = object.updater ?? '';
    return message;
  }
};

function createBaseListResourcePackageDetailReq(): ListResourcePackageDetailReq {
  return { page: undefined, package_id: 0 };
}

export const ListResourcePackageDetailReq: MessageFns<ListResourcePackageDetailReq> = {
  fromJSON(object: any): ListResourcePackageDetailReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDetailReq>, I>>(base?: I): ListResourcePackageDetailReq {
    return ListResourcePackageDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDetailReq>, I>>(object: I): ListResourcePackageDetailReq {
    const message = createBaseListResourcePackageDetailReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.package_id = object.package_id ?? 0;
    return message;
  }
};

function createBaseListResourcePackageDetailRsp(): ListResourcePackageDetailRsp {
  return { page: undefined, resource_package_detail_infos: [] };
}

export const ListResourcePackageDetailRsp: MessageFns<ListResourcePackageDetailRsp> = {
  fromJSON(object: any): ListResourcePackageDetailRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      resource_package_detail_infos: globalThis.Array.isArray(object?.resource_package_detail_infos)
        ? object.resource_package_detail_infos.map((e: any) => ResourcePackageDetailInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListResourcePackageDetailRsp>, I>>(base?: I): ListResourcePackageDetailRsp {
    return ListResourcePackageDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListResourcePackageDetailRsp>, I>>(object: I): ListResourcePackageDetailRsp {
    const message = createBaseListResourcePackageDetailRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.resource_package_detail_infos =
      object.resource_package_detail_infos?.map(e => ResourcePackageDetailInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateResourcePackageDetailReq(): CreateResourcePackageDetailReq {
  return {
    package_id: 0,
    resource_type: '',
    resource_id: 0,
    icon_url: '',
    extend_json: '',
    amount: 0,
    effective_time: 0
  };
}

export const CreateResourcePackageDetailReq: MessageFns<CreateResourcePackageDetailReq> = {
  fromJSON(object: any): CreateResourcePackageDetailReq {
    return {
      package_id: isSet(object.package_id) ? globalThis.Number(object.package_id) : 0,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageDetailReq>, I>>(base?: I): CreateResourcePackageDetailReq {
    return CreateResourcePackageDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageDetailReq>, I>>(
    object: I
  ): CreateResourcePackageDetailReq {
    const message = createBaseCreateResourcePackageDetailReq();
    message.package_id = object.package_id ?? 0;
    message.resource_type = object.resource_type ?? '';
    message.resource_id = object.resource_id ?? 0;
    message.icon_url = object.icon_url ?? '';
    message.extend_json = object.extend_json ?? '';
    message.amount = object.amount ?? 0;
    message.effective_time = object.effective_time ?? 0;
    return message;
  }
};

function createBaseCreateResourcePackageDetailRsp(): CreateResourcePackageDetailRsp {
  return { id: 0 };
}

export const CreateResourcePackageDetailRsp: MessageFns<CreateResourcePackageDetailRsp> = {
  fromJSON(object: any): CreateResourcePackageDetailRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateResourcePackageDetailRsp>, I>>(base?: I): CreateResourcePackageDetailRsp {
    return CreateResourcePackageDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateResourcePackageDetailRsp>, I>>(
    object: I
  ): CreateResourcePackageDetailRsp {
    const message = createBaseCreateResourcePackageDetailRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateResourcePackageDetailReq(): UpdateResourcePackageDetailReq {
  return { id: 0, resource_type: '', resource_id: 0, icon_url: '', extend_json: '', amount: 0, effective_time: 0 };
}

export const UpdateResourcePackageDetailReq: MessageFns<UpdateResourcePackageDetailReq> = {
  fromJSON(object: any): UpdateResourcePackageDetailReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      resource_type: isSet(object.resource_type) ? globalThis.String(object.resource_type) : '',
      resource_id: isSet(object.resource_id) ? globalThis.Number(object.resource_id) : 0,
      icon_url: isSet(object.icon_url) ? globalThis.String(object.icon_url) : '',
      extend_json: isSet(object.extend_json) ? globalThis.String(object.extend_json) : '',
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_time: isSet(object.effective_time) ? globalThis.Number(object.effective_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateResourcePackageDetailReq>, I>>(base?: I): UpdateResourcePackageDetailReq {
    return UpdateResourcePackageDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourcePackageDetailReq>, I>>(
    object: I
  ): UpdateResourcePackageDetailReq {
    const message = createBaseUpdateResourcePackageDetailReq();
    message.id = object.id ?? 0;
    message.resource_type = object.resource_type ?? '';
    message.resource_id = object.resource_id ?? 0;
    message.icon_url = object.icon_url ?? '';
    message.extend_json = object.extend_json ?? '';
    message.amount = object.amount ?? 0;
    message.effective_time = object.effective_time ?? 0;
    return message;
  }
};

function createBaseUpdateResourcePackageDetailRsp(): UpdateResourcePackageDetailRsp {
  return {};
}

export const UpdateResourcePackageDetailRsp: MessageFns<UpdateResourcePackageDetailRsp> = {
  fromJSON(_: any): UpdateResourcePackageDetailRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateResourcePackageDetailRsp>, I>>(base?: I): UpdateResourcePackageDetailRsp {
    return UpdateResourcePackageDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateResourcePackageDetailRsp>, I>>(_: I): UpdateResourcePackageDetailRsp {
    const message = createBaseUpdateResourcePackageDetailRsp();
    return message;
  }
};

function createBaseDeleteResourcePackageDetailReq(): DeleteResourcePackageDetailReq {
  return { id: 0 };
}

export const DeleteResourcePackageDetailReq: MessageFns<DeleteResourcePackageDetailReq> = {
  fromJSON(object: any): DeleteResourcePackageDetailReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteResourcePackageDetailReq>, I>>(base?: I): DeleteResourcePackageDetailReq {
    return DeleteResourcePackageDetailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourcePackageDetailReq>, I>>(
    object: I
  ): DeleteResourcePackageDetailReq {
    const message = createBaseDeleteResourcePackageDetailReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteResourcePackageDetailRsp(): DeleteResourcePackageDetailRsp {
  return {};
}

export const DeleteResourcePackageDetailRsp: MessageFns<DeleteResourcePackageDetailRsp> = {
  fromJSON(_: any): DeleteResourcePackageDetailRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteResourcePackageDetailRsp>, I>>(base?: I): DeleteResourcePackageDetailRsp {
    return DeleteResourcePackageDetailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteResourcePackageDetailRsp>, I>>(_: I): DeleteResourcePackageDetailRsp {
    const message = createBaseDeleteResourcePackageDetailRsp();
    return message;
  }
};

export type ResourcePackageDefinition = typeof ResourcePackageDefinition;
export const ResourcePackageDefinition = {
  name: 'ResourcePackage',
  fullName: 'comm.mgr.privilege.ResourcePackage',
  methods: {
    /** 资源包列表 */
    listResourcePackage: {
      name: 'ListResourcePackage',
      requestType: ListResourcePackageReq,
      requestStream: false,
      responseType: ListResourcePackageRsp,
      responseStream: false,
      options: {}
    },
    /** 新建资源包 */
    createResourcePackage: {
      name: 'CreateResourcePackage',
      requestType: CreateResourcePackageReq,
      requestStream: false,
      responseType: CreateResourcePackageRsp,
      responseStream: false,
      options: {}
    },
    /** 更新资源包 */
    updateResourcePackage: {
      name: 'UpdateResourcePackage',
      requestType: UpdateResourcePackageReq,
      requestStream: false,
      responseType: UpdateResourcePackageRsp,
      responseStream: false,
      options: {}
    },
    /** 删除资源包 */
    deleteResourcePackage: {
      name: 'DeleteResourcePackage',
      requestType: DeleteResourcePackageReq,
      requestStream: false,
      responseType: DeleteResourcePackageRsp,
      responseStream: false,
      options: {}
    },
    /** 资源包详情列表 */
    listResourcePackageDetail: {
      name: 'ListResourcePackageDetail',
      requestType: ListResourcePackageDetailReq,
      requestStream: false,
      responseType: ListResourcePackageDetailRsp,
      responseStream: false,
      options: {}
    },
    /** 新建资源包详情 */
    createResourcePackageDetail: {
      name: 'CreateResourcePackageDetail',
      requestType: CreateResourcePackageDetailReq,
      requestStream: false,
      responseType: CreateResourcePackageDetailRsp,
      responseStream: false,
      options: {}
    },
    /** 更新资源包详情 */
    updateResourcePackageDetail: {
      name: 'UpdateResourcePackageDetail',
      requestType: UpdateResourcePackageDetailReq,
      requestStream: false,
      responseType: UpdateResourcePackageDetailRsp,
      responseStream: false,
      options: {}
    },
    /** 删除资源包详情 */
    deleteResourcePackageDetail: {
      name: 'DeleteResourcePackageDetail',
      requestType: DeleteResourcePackageDetailReq,
      requestStream: false,
      responseType: DeleteResourcePackageDetailRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
