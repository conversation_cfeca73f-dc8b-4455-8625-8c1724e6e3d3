// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/config/common.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.config';

export enum ConfigType {
  /** CONFIG_TYPE_BIZ - 业务配置 */
  CONFIG_TYPE_BIZ = 0,
  /** CONFIG_TYPE_PC - 程序配置 */
  CONFIG_TYPE_PC = 1,
  /** CONFIG_TYPE_APP - APP配置 */
  CONFIG_TYPE_APP = 2,
  /** CONFIG_TYPE_ABTEST - ABTest配置 */
  CONFIG_TYPE_ABTEST = 3,
  UNRECOGNIZED = -1
}

export function configTypeFromJSON(object: any): ConfigType {
  switch (object) {
    case 0:
    case 'CONFIG_TYPE_BIZ':
      return ConfigType.CONFIG_TYPE_BIZ;
    case 1:
    case 'CONFIG_TYPE_PC':
      return ConfigType.CONFIG_TYPE_PC;
    case 2:
    case 'CONFIG_TYPE_APP':
      return ConfigType.CONFIG_TYPE_APP;
    case 3:
    case 'CONFIG_TYPE_ABTEST':
      return ConfigType.CONFIG_TYPE_ABTEST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ConfigType.UNRECOGNIZED;
  }
}

export interface ConfigBiz {
  /** 业务标识 */
  biz_key: string;
  /** 业务名称 */
  biz_name: string;
}

export interface ConfigSchema {
  /** 业务标识 */
  biz_key: string;
  /** 配置Schema */
  schema: string;
}

export interface ConfigKey {
  /** 配置业务标识 */
  biz_key: string;
  /** 配置项标识 */
  item_key: string;
}

export interface ConfigValue {
  /** 配置key */
  key: string;
  /** 配置值 */
  value: string;
  /** 备注 */
  remark: string;
  /** 提交时间 */
  submit_at: number;
  /** 提交人 */
  submit_by: string;
  /** 发布时间 */
  publish_at: number;
  /** 发布人 */
  publish_by: string;
  /** 失效时间 */
  invalid_at: number;
  /** 失效人 */
  invalid_by: string;
}

function createBaseConfigBiz(): ConfigBiz {
  return { biz_key: '', biz_name: '' };
}

export const ConfigBiz: MessageFns<ConfigBiz> = {
  fromJSON(object: any): ConfigBiz {
    return {
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '',
      biz_name: isSet(object.biz_name) ? globalThis.String(object.biz_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<ConfigBiz>, I>>(base?: I): ConfigBiz {
    return ConfigBiz.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigBiz>, I>>(object: I): ConfigBiz {
    const message = createBaseConfigBiz();
    message.biz_key = object.biz_key ?? '';
    message.biz_name = object.biz_name ?? '';
    return message;
  }
};

function createBaseConfigSchema(): ConfigSchema {
  return { biz_key: '', schema: '' };
}

export const ConfigSchema: MessageFns<ConfigSchema> = {
  fromJSON(object: any): ConfigSchema {
    return {
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '',
      schema: isSet(object.schema) ? globalThis.String(object.schema) : ''
    };
  },

  create<I extends Exact<DeepPartial<ConfigSchema>, I>>(base?: I): ConfigSchema {
    return ConfigSchema.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigSchema>, I>>(object: I): ConfigSchema {
    const message = createBaseConfigSchema();
    message.biz_key = object.biz_key ?? '';
    message.schema = object.schema ?? '';
    return message;
  }
};

function createBaseConfigKey(): ConfigKey {
  return { biz_key: '', item_key: '' };
}

export const ConfigKey: MessageFns<ConfigKey> = {
  fromJSON(object: any): ConfigKey {
    return {
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '',
      item_key: isSet(object.item_key) ? globalThis.String(object.item_key) : ''
    };
  },

  create<I extends Exact<DeepPartial<ConfigKey>, I>>(base?: I): ConfigKey {
    return ConfigKey.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigKey>, I>>(object: I): ConfigKey {
    const message = createBaseConfigKey();
    message.biz_key = object.biz_key ?? '';
    message.item_key = object.item_key ?? '';
    return message;
  }
};

function createBaseConfigValue(): ConfigValue {
  return {
    key: '',
    value: '',
    remark: '',
    submit_at: 0,
    submit_by: '',
    publish_at: 0,
    publish_by: '',
    invalid_at: 0,
    invalid_by: ''
  };
}

export const ConfigValue: MessageFns<ConfigValue> = {
  fromJSON(object: any): ConfigValue {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      submit_at: isSet(object.submit_at) ? globalThis.Number(object.submit_at) : 0,
      submit_by: isSet(object.submit_by) ? globalThis.String(object.submit_by) : '',
      publish_at: isSet(object.publish_at) ? globalThis.Number(object.publish_at) : 0,
      publish_by: isSet(object.publish_by) ? globalThis.String(object.publish_by) : '',
      invalid_at: isSet(object.invalid_at) ? globalThis.Number(object.invalid_at) : 0,
      invalid_by: isSet(object.invalid_by) ? globalThis.String(object.invalid_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<ConfigValue>, I>>(base?: I): ConfigValue {
    return ConfigValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigValue>, I>>(object: I): ConfigValue {
    const message = createBaseConfigValue();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    message.remark = object.remark ?? '';
    message.submit_at = object.submit_at ?? 0;
    message.submit_by = object.submit_by ?? '';
    message.publish_at = object.publish_at ?? 0;
    message.publish_by = object.publish_by ?? '';
    message.invalid_at = object.invalid_at ?? 0;
    message.invalid_by = object.invalid_by ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
