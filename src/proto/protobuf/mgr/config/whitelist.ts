// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/config/whitelist.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.config';

/** smicro:spath=gitit.cc/social/components-service/social-config/biz/whitelist/handlermgr/ */

/** 白名单分组类型 */
export enum WhitelistType {
  TypeNone = 0,
  /** TypeUID - 用户ID */
  TypeUID = 1,
  /** TypeDID - 设备ID */
  TypeDID = 2,
  UNRECOGNIZED = -1
}

export function whitelistTypeFromJSON(object: any): WhitelistType {
  switch (object) {
    case 0:
    case 'TypeNone':
      return WhitelistType.TypeNone;
    case 1:
    case 'TypeUID':
      return WhitelistType.TypeUID;
    case 2:
    case 'TypeDID':
      return WhitelistType.TypeDID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return WhitelistType.UNRECOGNIZED;
  }
}

export interface Group {
  /** 全局唯一，分组名称，建议用英文 */
  name: string;
  /** 分组描述 */
  description: string;
  /** 分组类型，如：白名单、黑名单 */
  type: WhitelistType;
  /** 创建者 */
  creator: string;
  /** 最后更新者 */
  editor: string;
  /** 创建时间 */
  ctime: number;
  /** 最后更新时间 */
  utime: number;
}

/** 添加分组 */
export interface AddGroupReq {
  group: Group | undefined;
}

export interface AddGroupResp {
  group_name: string;
}

/** 获取分组列表 */
export interface GetGroupListReq {
  page: Page | undefined;
  group_name: string;
}

export interface GetGroupListResp {
  page: Page | undefined;
  groups: Group[];
}

/** 添加分组成员 */
export interface AddGroupMemberReq {
  group_name: string;
  members: string[];
}

export interface AddGroupMemberResp {}

/** 分组成员信息 */
export interface MemberInfo {
  member_id: string;
  ctime: number;
  editor: string;
}

/** 获取分组成员 */
export interface GetGroupMembersReq {
  page: Page | undefined;
  group_name: string;
}

/** 获取分组成员 */
export interface GetGroupMembersResp {
  page: Page | undefined;
  members: MemberInfo[];
}

/** 删除分组成员 */
export interface DelGroupMemberReq {
  group_name: string;
  members: string[];
}

export interface EditGroupReq {
  description: string;
  group_name: string;
}

export interface EditGroupResp {}

/** 批量检查分组成员 */
export interface BatchGroupMemberCheckReq {
  group_name: string;
  members: string[];
}

export interface BatchGroupMemberCheckResp {
  match: string[];
  not_match: string[];
}

function createBaseGroup(): Group {
  return { name: '', description: '', type: 0, creator: '', editor: '', ctime: 0, utime: 0 };
}

export const Group: MessageFns<Group> = {
  fromJSON(object: any): Group {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      type: isSet(object.type) ? whitelistTypeFromJSON(object.type) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0
    };
  },

  create<I extends Exact<DeepPartial<Group>, I>>(base?: I): Group {
    return Group.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Group>, I>>(object: I): Group {
    const message = createBaseGroup();
    message.name = object.name ?? '';
    message.description = object.description ?? '';
    message.type = object.type ?? 0;
    message.creator = object.creator ?? '';
    message.editor = object.editor ?? '';
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    return message;
  }
};

function createBaseAddGroupReq(): AddGroupReq {
  return { group: undefined };
}

export const AddGroupReq: MessageFns<AddGroupReq> = {
  fromJSON(object: any): AddGroupReq {
    return { group: isSet(object.group) ? Group.fromJSON(object.group) : undefined };
  },

  create<I extends Exact<DeepPartial<AddGroupReq>, I>>(base?: I): AddGroupReq {
    return AddGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGroupReq>, I>>(object: I): AddGroupReq {
    const message = createBaseAddGroupReq();
    message.group = object.group !== undefined && object.group !== null ? Group.fromPartial(object.group) : undefined;
    return message;
  }
};

function createBaseAddGroupResp(): AddGroupResp {
  return { group_name: '' };
}

export const AddGroupResp: MessageFns<AddGroupResp> = {
  fromJSON(object: any): AddGroupResp {
    return { group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '' };
  },

  create<I extends Exact<DeepPartial<AddGroupResp>, I>>(base?: I): AddGroupResp {
    return AddGroupResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGroupResp>, I>>(object: I): AddGroupResp {
    const message = createBaseAddGroupResp();
    message.group_name = object.group_name ?? '';
    return message;
  }
};

function createBaseGetGroupListReq(): GetGroupListReq {
  return { page: undefined, group_name: '' };
}

export const GetGroupListReq: MessageFns<GetGroupListReq> = {
  fromJSON(object: any): GetGroupListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetGroupListReq>, I>>(base?: I): GetGroupListReq {
    return GetGroupListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGroupListReq>, I>>(object: I): GetGroupListReq {
    const message = createBaseGetGroupListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.group_name = object.group_name ?? '';
    return message;
  }
};

function createBaseGetGroupListResp(): GetGroupListResp {
  return { page: undefined, groups: [] };
}

export const GetGroupListResp: MessageFns<GetGroupListResp> = {
  fromJSON(object: any): GetGroupListResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      groups: globalThis.Array.isArray(object?.groups) ? object.groups.map((e: any) => Group.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetGroupListResp>, I>>(base?: I): GetGroupListResp {
    return GetGroupListResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGroupListResp>, I>>(object: I): GetGroupListResp {
    const message = createBaseGetGroupListResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.groups = object.groups?.map(e => Group.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAddGroupMemberReq(): AddGroupMemberReq {
  return { group_name: '', members: [] };
}

export const AddGroupMemberReq: MessageFns<AddGroupMemberReq> = {
  fromJSON(object: any): AddGroupMemberReq {
    return {
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      members: globalThis.Array.isArray(object?.members) ? object.members.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<AddGroupMemberReq>, I>>(base?: I): AddGroupMemberReq {
    return AddGroupMemberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGroupMemberReq>, I>>(object: I): AddGroupMemberReq {
    const message = createBaseAddGroupMemberReq();
    message.group_name = object.group_name ?? '';
    message.members = object.members?.map(e => e) || [];
    return message;
  }
};

function createBaseAddGroupMemberResp(): AddGroupMemberResp {
  return {};
}

export const AddGroupMemberResp: MessageFns<AddGroupMemberResp> = {
  fromJSON(_: any): AddGroupMemberResp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddGroupMemberResp>, I>>(base?: I): AddGroupMemberResp {
    return AddGroupMemberResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGroupMemberResp>, I>>(_: I): AddGroupMemberResp {
    const message = createBaseAddGroupMemberResp();
    return message;
  }
};

function createBaseMemberInfo(): MemberInfo {
  return { member_id: '', ctime: 0, editor: '' };
}

export const MemberInfo: MessageFns<MemberInfo> = {
  fromJSON(object: any): MemberInfo {
    return {
      member_id: isSet(object.member_id) ? globalThis.String(object.member_id) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : ''
    };
  },

  create<I extends Exact<DeepPartial<MemberInfo>, I>>(base?: I): MemberInfo {
    return MemberInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MemberInfo>, I>>(object: I): MemberInfo {
    const message = createBaseMemberInfo();
    message.member_id = object.member_id ?? '';
    message.ctime = object.ctime ?? 0;
    message.editor = object.editor ?? '';
    return message;
  }
};

function createBaseGetGroupMembersReq(): GetGroupMembersReq {
  return { page: undefined, group_name: '' };
}

export const GetGroupMembersReq: MessageFns<GetGroupMembersReq> = {
  fromJSON(object: any): GetGroupMembersReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetGroupMembersReq>, I>>(base?: I): GetGroupMembersReq {
    return GetGroupMembersReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGroupMembersReq>, I>>(object: I): GetGroupMembersReq {
    const message = createBaseGetGroupMembersReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.group_name = object.group_name ?? '';
    return message;
  }
};

function createBaseGetGroupMembersResp(): GetGroupMembersResp {
  return { page: undefined, members: [] };
}

export const GetGroupMembersResp: MessageFns<GetGroupMembersResp> = {
  fromJSON(object: any): GetGroupMembersResp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      members: globalThis.Array.isArray(object?.members) ? object.members.map((e: any) => MemberInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetGroupMembersResp>, I>>(base?: I): GetGroupMembersResp {
    return GetGroupMembersResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGroupMembersResp>, I>>(object: I): GetGroupMembersResp {
    const message = createBaseGetGroupMembersResp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.members = object.members?.map(e => MemberInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDelGroupMemberReq(): DelGroupMemberReq {
  return { group_name: '', members: [] };
}

export const DelGroupMemberReq: MessageFns<DelGroupMemberReq> = {
  fromJSON(object: any): DelGroupMemberReq {
    return {
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      members: globalThis.Array.isArray(object?.members) ? object.members.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<DelGroupMemberReq>, I>>(base?: I): DelGroupMemberReq {
    return DelGroupMemberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelGroupMemberReq>, I>>(object: I): DelGroupMemberReq {
    const message = createBaseDelGroupMemberReq();
    message.group_name = object.group_name ?? '';
    message.members = object.members?.map(e => e) || [];
    return message;
  }
};

function createBaseEditGroupReq(): EditGroupReq {
  return { description: '', group_name: '' };
}

export const EditGroupReq: MessageFns<EditGroupReq> = {
  fromJSON(object: any): EditGroupReq {
    return {
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<EditGroupReq>, I>>(base?: I): EditGroupReq {
    return EditGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditGroupReq>, I>>(object: I): EditGroupReq {
    const message = createBaseEditGroupReq();
    message.description = object.description ?? '';
    message.group_name = object.group_name ?? '';
    return message;
  }
};

function createBaseEditGroupResp(): EditGroupResp {
  return {};
}

export const EditGroupResp: MessageFns<EditGroupResp> = {
  fromJSON(_: any): EditGroupResp {
    return {};
  },

  create<I extends Exact<DeepPartial<EditGroupResp>, I>>(base?: I): EditGroupResp {
    return EditGroupResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditGroupResp>, I>>(_: I): EditGroupResp {
    const message = createBaseEditGroupResp();
    return message;
  }
};

function createBaseBatchGroupMemberCheckReq(): BatchGroupMemberCheckReq {
  return { group_name: '', members: [] };
}

export const BatchGroupMemberCheckReq: MessageFns<BatchGroupMemberCheckReq> = {
  fromJSON(object: any): BatchGroupMemberCheckReq {
    return {
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      members: globalThis.Array.isArray(object?.members) ? object.members.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGroupMemberCheckReq>, I>>(base?: I): BatchGroupMemberCheckReq {
    return BatchGroupMemberCheckReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGroupMemberCheckReq>, I>>(object: I): BatchGroupMemberCheckReq {
    const message = createBaseBatchGroupMemberCheckReq();
    message.group_name = object.group_name ?? '';
    message.members = object.members?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGroupMemberCheckResp(): BatchGroupMemberCheckResp {
  return { match: [], not_match: [] };
}

export const BatchGroupMemberCheckResp: MessageFns<BatchGroupMemberCheckResp> = {
  fromJSON(object: any): BatchGroupMemberCheckResp {
    return {
      match: globalThis.Array.isArray(object?.match) ? object.match.map((e: any) => globalThis.String(e)) : [],
      not_match: globalThis.Array.isArray(object?.not_match)
        ? object.not_match.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGroupMemberCheckResp>, I>>(base?: I): BatchGroupMemberCheckResp {
    return BatchGroupMemberCheckResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGroupMemberCheckResp>, I>>(object: I): BatchGroupMemberCheckResp {
    const message = createBaseBatchGroupMemberCheckResp();
    message.match = object.match?.map(e => e) || [];
    message.not_match = object.not_match?.map(e => e) || [];
    return message;
  }
};

/** 白名单管理 */
export type WhitelistDefinition = typeof WhitelistDefinition;
export const WhitelistDefinition = {
  name: 'Whitelist',
  fullName: 'comm.mgr.config.Whitelist',
  methods: {
    /** 添加分组 */
    addGroup: {
      name: 'AddGroup',
      requestType: AddGroupReq,
      requestStream: false,
      responseType: AddGroupResp,
      responseStream: false,
      options: {}
    },
    /** 获取分组列表 */
    getGroupList: {
      name: 'GetGroupList',
      requestType: GetGroupListReq,
      requestStream: false,
      responseType: GetGroupListResp,
      responseStream: false,
      options: {}
    },
    /** 添加分组成员 */
    addGroupMember: {
      name: 'AddGroupMember',
      requestType: AddGroupMemberReq,
      requestStream: false,
      responseType: AddGroupMemberResp,
      responseStream: false,
      options: {}
    },
    /** 获取分组成员 */
    getGroupMembers: {
      name: 'GetGroupMembers',
      requestType: GetGroupMembersReq,
      requestStream: false,
      responseType: GetGroupMembersResp,
      responseStream: false,
      options: {}
    },
    /** 删除分组成员 */
    delGroupMember: {
      name: 'DelGroupMember',
      requestType: DelGroupMemberReq,
      requestStream: false,
      responseType: AddGroupMemberResp,
      responseStream: false,
      options: {}
    },
    /** 编辑分组 */
    editGroup: {
      name: 'EditGroup',
      requestType: EditGroupReq,
      requestStream: false,
      responseType: AddGroupResp,
      responseStream: false,
      options: {}
    },
    /** 批量查询用户是否在分组内 */
    batchGroupMemberCheck: {
      name: 'BatchGroupMemberCheck',
      requestType: BatchGroupMemberCheckReq,
      requestStream: false,
      responseType: BatchGroupMemberCheckResp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
