// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/config/permission.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.config';

/** smicro:spath=gitit.cc/social/components-service/social-config/biz/permission/handlermgr/ */

export enum PermissionRole {
  /** RoleNone - 无权限 */
  RoleNone = 0,
  /** Admin - 超级管理员 */
  Admin = 1,
  /** Staff - 普通管理员 */
  Staff = 2,
  UNRECOGNIZED = -1
}

export function permissionRoleFromJSON(object: any): PermissionRole {
  switch (object) {
    case 0:
    case 'RoleNone':
      return PermissionRole.RoleNone;
    case 1:
    case 'Admin':
      return PermissionRole.Admin;
    case 2:
    case 'Staff':
      return PermissionRole.Staff;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PermissionRole.UNRECOGNIZED;
  }
}

/** 获取用户角色 */
export interface GetRoleReq {
  /** 业务key */
  biz_key: string;
}

export interface GetRoleRsp {
  role: PermissionRole;
}

/** 授权接口(全量授权) */
export interface GrantReq {
  /** 被授权用户union_id */
  staff_ids: string[];
  /** 权限组id集合 */
  permission_group_ids: number[];
  /** 业务key */
  biz_key: string;
}

export interface GrantRsp {}

/** 获取业务下员工列表 */
export interface GetBizStaffListReq {
  /** 业务key */
  biz_key: string;
}

export interface GetBizStaffListRsp {
  /** 被授权用户union_id */
  staff_ids: string[];
  /** 权限组列表 */
  permission_groups: PermissionGroupInfo[];
}

/** 批量授权(增量授权) */
export interface BatchGrantReq {
  /** 被授权用户union_id */
  staff_ids: string[];
  /** 权限组id集合 */
  permission_group_ids: number[];
  /** 业务key */
  biz_keys: string[];
}

export interface BatchGrantRsp {}

export interface CreatePermissionGroupReq {
  /** 权限组名称 */
  group_name: string;
  /** 权限组描述 */
  description: string;
  /** 员工UnionID */
  staff_union_ids: string[];
}

export interface CreatePermissionGroupRsp {
  /** 新建的权限组ID */
  id: number;
}

export interface UpdatePermissionGroupReq {
  /** 权限组ID */
  id: number;
  /** 权限组名称 */
  group_name: string;
  /** 权限组描述 */
  description: string;
  /** 员工UnionID（全量覆盖） */
  staff_union_ids: string[];
}

export interface UpdatePermissionGroupRsp {}

export interface DeletePermissionGroupReq {
  /** 权限组ID */
  id: number;
}

export interface DeletePermissionGroupRsp {}

export interface ListPermissionGroupReq {
  page: Page | undefined;
  /** 权限组ID */
  id: number;
  /** 权限组名称 */
  group_name: string;
  /** 角色(0:无,1:超管,2:管理员) */
  role: PermissionRole;
}

export interface ListPermissionGroupRsp {
  page: Page | undefined;
  /** 权限组列表 */
  groups: PermissionGroupInfo[];
}

export interface PermissionGroupInfo {
  /** 权限组ID */
  id: number;
  /** 业务名 */
  anm: string;
  /** 权限组名称 */
  group_name: string;
  /** 角色(0:无,1:超管,2:管理员) */
  role: PermissionRole;
  /** 权限组描述 */
  description: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 修改人 */
  updater: string;
  /** 修改时间 */
  updated_at: number;
  /** 成员列表 */
  members: PermissionGroupMemberInfo[];
}

export interface AddPermissionGroupMemberReq {
  /** 权限组ID */
  group_id: number;
  /** 员工UnionID */
  staff_union_ids: string[];
}

export interface AddPermissionGroupMemberRsp {}

export interface DeletePermissionGroupMemberReq {
  /** 权限组成员Ids */
  ids: number[];
}

export interface DeletePermissionGroupMemberRsp {}

export interface ListPermissionGroupMemberReq {
  page: Page | undefined;
  /** 权限组ID */
  group_ids: number[];
}

export interface ListPermissionGroupMemberRsp {
  page: Page | undefined;
  /** 成员列表 */
  members: PermissionGroupMemberInfo[];
}

export interface PermissionGroupMemberInfo {
  /** 权限组成员Id */
  id: number;
  /** 权限组ID */
  group_id: number;
  /** 员工UnionID */
  staff_union_id: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
}

function createBaseGetRoleReq(): GetRoleReq {
  return { biz_key: '' };
}

export const GetRoleReq: MessageFns<GetRoleReq> = {
  fromJSON(object: any): GetRoleReq {
    return { biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '' };
  },

  create<I extends Exact<DeepPartial<GetRoleReq>, I>>(base?: I): GetRoleReq {
    return GetRoleReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoleReq>, I>>(object: I): GetRoleReq {
    const message = createBaseGetRoleReq();
    message.biz_key = object.biz_key ?? '';
    return message;
  }
};

function createBaseGetRoleRsp(): GetRoleRsp {
  return { role: 0 };
}

export const GetRoleRsp: MessageFns<GetRoleRsp> = {
  fromJSON(object: any): GetRoleRsp {
    return { role: isSet(object.role) ? permissionRoleFromJSON(object.role) : 0 };
  },

  create<I extends Exact<DeepPartial<GetRoleRsp>, I>>(base?: I): GetRoleRsp {
    return GetRoleRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRoleRsp>, I>>(object: I): GetRoleRsp {
    const message = createBaseGetRoleRsp();
    message.role = object.role ?? 0;
    return message;
  }
};

function createBaseGrantReq(): GrantReq {
  return { staff_ids: [], permission_group_ids: [], biz_key: '' };
}

export const GrantReq: MessageFns<GrantReq> = {
  fromJSON(object: any): GrantReq {
    return {
      staff_ids: globalThis.Array.isArray(object?.staff_ids)
        ? object.staff_ids.map((e: any) => globalThis.String(e))
        : [],
      permission_group_ids: globalThis.Array.isArray(object?.permission_group_ids)
        ? object.permission_group_ids.map((e: any) => globalThis.Number(e))
        : [],
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : ''
    };
  },

  create<I extends Exact<DeepPartial<GrantReq>, I>>(base?: I): GrantReq {
    return GrantReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GrantReq>, I>>(object: I): GrantReq {
    const message = createBaseGrantReq();
    message.staff_ids = object.staff_ids?.map(e => e) || [];
    message.permission_group_ids = object.permission_group_ids?.map(e => e) || [];
    message.biz_key = object.biz_key ?? '';
    return message;
  }
};

function createBaseGrantRsp(): GrantRsp {
  return {};
}

export const GrantRsp: MessageFns<GrantRsp> = {
  fromJSON(_: any): GrantRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<GrantRsp>, I>>(base?: I): GrantRsp {
    return GrantRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GrantRsp>, I>>(_: I): GrantRsp {
    const message = createBaseGrantRsp();
    return message;
  }
};

function createBaseGetBizStaffListReq(): GetBizStaffListReq {
  return { biz_key: '' };
}

export const GetBizStaffListReq: MessageFns<GetBizStaffListReq> = {
  fromJSON(object: any): GetBizStaffListReq {
    return { biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '' };
  },

  create<I extends Exact<DeepPartial<GetBizStaffListReq>, I>>(base?: I): GetBizStaffListReq {
    return GetBizStaffListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBizStaffListReq>, I>>(object: I): GetBizStaffListReq {
    const message = createBaseGetBizStaffListReq();
    message.biz_key = object.biz_key ?? '';
    return message;
  }
};

function createBaseGetBizStaffListRsp(): GetBizStaffListRsp {
  return { staff_ids: [], permission_groups: [] };
}

export const GetBizStaffListRsp: MessageFns<GetBizStaffListRsp> = {
  fromJSON(object: any): GetBizStaffListRsp {
    return {
      staff_ids: globalThis.Array.isArray(object?.staff_ids)
        ? object.staff_ids.map((e: any) => globalThis.String(e))
        : [],
      permission_groups: globalThis.Array.isArray(object?.permission_groups)
        ? object.permission_groups.map((e: any) => PermissionGroupInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetBizStaffListRsp>, I>>(base?: I): GetBizStaffListRsp {
    return GetBizStaffListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBizStaffListRsp>, I>>(object: I): GetBizStaffListRsp {
    const message = createBaseGetBizStaffListRsp();
    message.staff_ids = object.staff_ids?.map(e => e) || [];
    message.permission_groups = object.permission_groups?.map(e => PermissionGroupInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchGrantReq(): BatchGrantReq {
  return { staff_ids: [], permission_group_ids: [], biz_keys: [] };
}

export const BatchGrantReq: MessageFns<BatchGrantReq> = {
  fromJSON(object: any): BatchGrantReq {
    return {
      staff_ids: globalThis.Array.isArray(object?.staff_ids)
        ? object.staff_ids.map((e: any) => globalThis.String(e))
        : [],
      permission_group_ids: globalThis.Array.isArray(object?.permission_group_ids)
        ? object.permission_group_ids.map((e: any) => globalThis.Number(e))
        : [],
      biz_keys: globalThis.Array.isArray(object?.biz_keys) ? object.biz_keys.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGrantReq>, I>>(base?: I): BatchGrantReq {
    return BatchGrantReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGrantReq>, I>>(object: I): BatchGrantReq {
    const message = createBaseBatchGrantReq();
    message.staff_ids = object.staff_ids?.map(e => e) || [];
    message.permission_group_ids = object.permission_group_ids?.map(e => e) || [];
    message.biz_keys = object.biz_keys?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGrantRsp(): BatchGrantRsp {
  return {};
}

export const BatchGrantRsp: MessageFns<BatchGrantRsp> = {
  fromJSON(_: any): BatchGrantRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchGrantRsp>, I>>(base?: I): BatchGrantRsp {
    return BatchGrantRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGrantRsp>, I>>(_: I): BatchGrantRsp {
    const message = createBaseBatchGrantRsp();
    return message;
  }
};

function createBaseCreatePermissionGroupReq(): CreatePermissionGroupReq {
  return { group_name: '', description: '', staff_union_ids: [] };
}

export const CreatePermissionGroupReq: MessageFns<CreatePermissionGroupReq> = {
  fromJSON(object: any): CreatePermissionGroupReq {
    return {
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      staff_union_ids: globalThis.Array.isArray(object?.staff_union_ids)
        ? object.staff_union_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CreatePermissionGroupReq>, I>>(base?: I): CreatePermissionGroupReq {
    return CreatePermissionGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePermissionGroupReq>, I>>(object: I): CreatePermissionGroupReq {
    const message = createBaseCreatePermissionGroupReq();
    message.group_name = object.group_name ?? '';
    message.description = object.description ?? '';
    message.staff_union_ids = object.staff_union_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseCreatePermissionGroupRsp(): CreatePermissionGroupRsp {
  return { id: 0 };
}

export const CreatePermissionGroupRsp: MessageFns<CreatePermissionGroupRsp> = {
  fromJSON(object: any): CreatePermissionGroupRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreatePermissionGroupRsp>, I>>(base?: I): CreatePermissionGroupRsp {
    return CreatePermissionGroupRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePermissionGroupRsp>, I>>(object: I): CreatePermissionGroupRsp {
    const message = createBaseCreatePermissionGroupRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdatePermissionGroupReq(): UpdatePermissionGroupReq {
  return { id: 0, group_name: '', description: '', staff_union_ids: [] };
}

export const UpdatePermissionGroupReq: MessageFns<UpdatePermissionGroupReq> = {
  fromJSON(object: any): UpdatePermissionGroupReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      staff_union_ids: globalThis.Array.isArray(object?.staff_union_ids)
        ? object.staff_union_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<UpdatePermissionGroupReq>, I>>(base?: I): UpdatePermissionGroupReq {
    return UpdatePermissionGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePermissionGroupReq>, I>>(object: I): UpdatePermissionGroupReq {
    const message = createBaseUpdatePermissionGroupReq();
    message.id = object.id ?? 0;
    message.group_name = object.group_name ?? '';
    message.description = object.description ?? '';
    message.staff_union_ids = object.staff_union_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseUpdatePermissionGroupRsp(): UpdatePermissionGroupRsp {
  return {};
}

export const UpdatePermissionGroupRsp: MessageFns<UpdatePermissionGroupRsp> = {
  fromJSON(_: any): UpdatePermissionGroupRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdatePermissionGroupRsp>, I>>(base?: I): UpdatePermissionGroupRsp {
    return UpdatePermissionGroupRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePermissionGroupRsp>, I>>(_: I): UpdatePermissionGroupRsp {
    const message = createBaseUpdatePermissionGroupRsp();
    return message;
  }
};

function createBaseDeletePermissionGroupReq(): DeletePermissionGroupReq {
  return { id: 0 };
}

export const DeletePermissionGroupReq: MessageFns<DeletePermissionGroupReq> = {
  fromJSON(object: any): DeletePermissionGroupReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeletePermissionGroupReq>, I>>(base?: I): DeletePermissionGroupReq {
    return DeletePermissionGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePermissionGroupReq>, I>>(object: I): DeletePermissionGroupReq {
    const message = createBaseDeletePermissionGroupReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeletePermissionGroupRsp(): DeletePermissionGroupRsp {
  return {};
}

export const DeletePermissionGroupRsp: MessageFns<DeletePermissionGroupRsp> = {
  fromJSON(_: any): DeletePermissionGroupRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePermissionGroupRsp>, I>>(base?: I): DeletePermissionGroupRsp {
    return DeletePermissionGroupRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePermissionGroupRsp>, I>>(_: I): DeletePermissionGroupRsp {
    const message = createBaseDeletePermissionGroupRsp();
    return message;
  }
};

function createBaseListPermissionGroupReq(): ListPermissionGroupReq {
  return { page: undefined, id: 0, group_name: '', role: 0 };
}

export const ListPermissionGroupReq: MessageFns<ListPermissionGroupReq> = {
  fromJSON(object: any): ListPermissionGroupReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      role: isSet(object.role) ? permissionRoleFromJSON(object.role) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListPermissionGroupReq>, I>>(base?: I): ListPermissionGroupReq {
    return ListPermissionGroupReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPermissionGroupReq>, I>>(object: I): ListPermissionGroupReq {
    const message = createBaseListPermissionGroupReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.group_name = object.group_name ?? '';
    message.role = object.role ?? 0;
    return message;
  }
};

function createBaseListPermissionGroupRsp(): ListPermissionGroupRsp {
  return { page: undefined, groups: [] };
}

export const ListPermissionGroupRsp: MessageFns<ListPermissionGroupRsp> = {
  fromJSON(object: any): ListPermissionGroupRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      groups: globalThis.Array.isArray(object?.groups)
        ? object.groups.map((e: any) => PermissionGroupInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListPermissionGroupRsp>, I>>(base?: I): ListPermissionGroupRsp {
    return ListPermissionGroupRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPermissionGroupRsp>, I>>(object: I): ListPermissionGroupRsp {
    const message = createBaseListPermissionGroupRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.groups = object.groups?.map(e => PermissionGroupInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBasePermissionGroupInfo(): PermissionGroupInfo {
  return {
    id: 0,
    anm: '',
    group_name: '',
    role: 0,
    description: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    members: []
  };
}

export const PermissionGroupInfo: MessageFns<PermissionGroupInfo> = {
  fromJSON(object: any): PermissionGroupInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : '',
      group_name: isSet(object.group_name) ? globalThis.String(object.group_name) : '',
      role: isSet(object.role) ? permissionRoleFromJSON(object.role) : 0,
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      members: globalThis.Array.isArray(object?.members)
        ? object.members.map((e: any) => PermissionGroupMemberInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<PermissionGroupInfo>, I>>(base?: I): PermissionGroupInfo {
    return PermissionGroupInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PermissionGroupInfo>, I>>(object: I): PermissionGroupInfo {
    const message = createBasePermissionGroupInfo();
    message.id = object.id ?? 0;
    message.anm = object.anm ?? '';
    message.group_name = object.group_name ?? '';
    message.role = object.role ?? 0;
    message.description = object.description ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.members = object.members?.map(e => PermissionGroupMemberInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAddPermissionGroupMemberReq(): AddPermissionGroupMemberReq {
  return { group_id: 0, staff_union_ids: [] };
}

export const AddPermissionGroupMemberReq: MessageFns<AddPermissionGroupMemberReq> = {
  fromJSON(object: any): AddPermissionGroupMemberReq {
    return {
      group_id: isSet(object.group_id) ? globalThis.Number(object.group_id) : 0,
      staff_union_ids: globalThis.Array.isArray(object?.staff_union_ids)
        ? object.staff_union_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<AddPermissionGroupMemberReq>, I>>(base?: I): AddPermissionGroupMemberReq {
    return AddPermissionGroupMemberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddPermissionGroupMemberReq>, I>>(object: I): AddPermissionGroupMemberReq {
    const message = createBaseAddPermissionGroupMemberReq();
    message.group_id = object.group_id ?? 0;
    message.staff_union_ids = object.staff_union_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseAddPermissionGroupMemberRsp(): AddPermissionGroupMemberRsp {
  return {};
}

export const AddPermissionGroupMemberRsp: MessageFns<AddPermissionGroupMemberRsp> = {
  fromJSON(_: any): AddPermissionGroupMemberRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddPermissionGroupMemberRsp>, I>>(base?: I): AddPermissionGroupMemberRsp {
    return AddPermissionGroupMemberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddPermissionGroupMemberRsp>, I>>(_: I): AddPermissionGroupMemberRsp {
    const message = createBaseAddPermissionGroupMemberRsp();
    return message;
  }
};

function createBaseDeletePermissionGroupMemberReq(): DeletePermissionGroupMemberReq {
  return { ids: [] };
}

export const DeletePermissionGroupMemberReq: MessageFns<DeletePermissionGroupMemberReq> = {
  fromJSON(object: any): DeletePermissionGroupMemberReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeletePermissionGroupMemberReq>, I>>(base?: I): DeletePermissionGroupMemberReq {
    return DeletePermissionGroupMemberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePermissionGroupMemberReq>, I>>(
    object: I
  ): DeletePermissionGroupMemberReq {
    const message = createBaseDeletePermissionGroupMemberReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeletePermissionGroupMemberRsp(): DeletePermissionGroupMemberRsp {
  return {};
}

export const DeletePermissionGroupMemberRsp: MessageFns<DeletePermissionGroupMemberRsp> = {
  fromJSON(_: any): DeletePermissionGroupMemberRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePermissionGroupMemberRsp>, I>>(base?: I): DeletePermissionGroupMemberRsp {
    return DeletePermissionGroupMemberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePermissionGroupMemberRsp>, I>>(_: I): DeletePermissionGroupMemberRsp {
    const message = createBaseDeletePermissionGroupMemberRsp();
    return message;
  }
};

function createBaseListPermissionGroupMemberReq(): ListPermissionGroupMemberReq {
  return { page: undefined, group_ids: [] };
}

export const ListPermissionGroupMemberReq: MessageFns<ListPermissionGroupMemberReq> = {
  fromJSON(object: any): ListPermissionGroupMemberReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      group_ids: globalThis.Array.isArray(object?.group_ids)
        ? object.group_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListPermissionGroupMemberReq>, I>>(base?: I): ListPermissionGroupMemberReq {
    return ListPermissionGroupMemberReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPermissionGroupMemberReq>, I>>(object: I): ListPermissionGroupMemberReq {
    const message = createBaseListPermissionGroupMemberReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.group_ids = object.group_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseListPermissionGroupMemberRsp(): ListPermissionGroupMemberRsp {
  return { page: undefined, members: [] };
}

export const ListPermissionGroupMemberRsp: MessageFns<ListPermissionGroupMemberRsp> = {
  fromJSON(object: any): ListPermissionGroupMemberRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      members: globalThis.Array.isArray(object?.members)
        ? object.members.map((e: any) => PermissionGroupMemberInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListPermissionGroupMemberRsp>, I>>(base?: I): ListPermissionGroupMemberRsp {
    return ListPermissionGroupMemberRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPermissionGroupMemberRsp>, I>>(object: I): ListPermissionGroupMemberRsp {
    const message = createBaseListPermissionGroupMemberRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.members = object.members?.map(e => PermissionGroupMemberInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBasePermissionGroupMemberInfo(): PermissionGroupMemberInfo {
  return { id: 0, group_id: 0, staff_union_id: '', creator: '', created_at: 0 };
}

export const PermissionGroupMemberInfo: MessageFns<PermissionGroupMemberInfo> = {
  fromJSON(object: any): PermissionGroupMemberInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      group_id: isSet(object.group_id) ? globalThis.Number(object.group_id) : 0,
      staff_union_id: isSet(object.staff_union_id) ? globalThis.String(object.staff_union_id) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<PermissionGroupMemberInfo>, I>>(base?: I): PermissionGroupMemberInfo {
    return PermissionGroupMemberInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PermissionGroupMemberInfo>, I>>(object: I): PermissionGroupMemberInfo {
    const message = createBasePermissionGroupMemberInfo();
    message.id = object.id ?? 0;
    message.group_id = object.group_id ?? 0;
    message.staff_union_id = object.staff_union_id ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    return message;
  }
};

export type PermissionDefinition = typeof PermissionDefinition;
export const PermissionDefinition = {
  name: 'Permission',
  fullName: 'comm.mgr.config.Permission',
  methods: {
    /** 获取员工角色 */
    getRole: {
      name: 'GetRole',
      requestType: GetRoleReq,
      requestStream: false,
      responseType: GetRoleRsp,
      responseStream: false,
      options: {}
    },
    /** 授权接口(管理员给员工授权, 是全量授权, 会把没提交的员工权限回收) */
    grant: {
      name: 'Grant',
      requestType: GrantReq,
      requestStream: false,
      responseType: GrantRsp,
      responseStream: false,
      options: {}
    },
    /** 获取业务下员工列表 */
    getBizStaffList: {
      name: 'GetBizStaffList',
      requestType: GetBizStaffListReq,
      requestStream: false,
      responseType: GetBizStaffListRsp,
      responseStream: false,
      options: {}
    },
    /** 批量授权(管理员给员工授权, 是增量授权, 只把未授权的员工授予权限) */
    batchGrant: {
      name: 'BatchGrant',
      requestType: BatchGrantReq,
      requestStream: false,
      responseType: BatchGrantRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组-创建 */
    createPermissionGroup: {
      name: 'CreatePermissionGroup',
      requestType: CreatePermissionGroupReq,
      requestStream: false,
      responseType: CreatePermissionGroupRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组-修改 */
    updatePermissionGroup: {
      name: 'UpdatePermissionGroup',
      requestType: UpdatePermissionGroupReq,
      requestStream: false,
      responseType: UpdatePermissionGroupRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组-删除 */
    deletePermissionGroup: {
      name: 'DeletePermissionGroup',
      requestType: DeletePermissionGroupReq,
      requestStream: false,
      responseType: DeletePermissionGroupRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组-查询 */
    listPermissionGroup: {
      name: 'ListPermissionGroup',
      requestType: ListPermissionGroupReq,
      requestStream: false,
      responseType: ListPermissionGroupRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组成员-添加 */
    addPermissionGroupMember: {
      name: 'AddPermissionGroupMember',
      requestType: AddPermissionGroupMemberReq,
      requestStream: false,
      responseType: AddPermissionGroupMemberRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组成员-删除 */
    deletePermissionGroupMember: {
      name: 'DeletePermissionGroupMember',
      requestType: DeletePermissionGroupMemberReq,
      requestStream: false,
      responseType: DeletePermissionGroupMemberRsp,
      responseStream: false,
      options: {}
    },
    /** 权限组成员-查询 */
    listPermissionGroupMember: {
      name: 'ListPermissionGroupMember',
      requestType: ListPermissionGroupMemberReq,
      requestStream: false,
      responseType: ListPermissionGroupMemberRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
