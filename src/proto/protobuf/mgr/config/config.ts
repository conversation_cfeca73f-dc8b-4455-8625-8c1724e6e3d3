// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/config/config.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { ConfigBiz, ConfigKey, ConfigSchema, ConfigType, configTypeFromJSON, ConfigValue } from './common';

export const protobufPackage = 'comm.mgr.config';

/** smicro:spath=gitit.cc/social/components-service/social-config/biz/config/handlermgr/ */

export enum SubmitStrategy {
  /** SUBMIT_STRATEGY_NONE - 不传默认是 SUBMIT_STRATEGY_UPSERT */
  SUBMIT_STRATEGY_NONE = 0,
  /** SUBMIT_STRATEGY_CREATE - 不存在则创建, 存在则失败. */
  SUBMIT_STRATEGY_CREATE = 10,
  /** SUBMIT_STRATEGY_UPSERT - 不存在则创建, 存在则更新. */
  SUBMIT_STRATEGY_UPSERT = 20,
  UNRECOGNIZED = -1
}

export function submitStrategyFromJSON(object: any): SubmitStrategy {
  switch (object) {
    case 0:
    case 'SUBMIT_STRATEGY_NONE':
      return SubmitStrategy.SUBMIT_STRATEGY_NONE;
    case 10:
    case 'SUBMIT_STRATEGY_CREATE':
      return SubmitStrategy.SUBMIT_STRATEGY_CREATE;
    case 20:
    case 'SUBMIT_STRATEGY_UPSERT':
      return SubmitStrategy.SUBMIT_STRATEGY_UPSERT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SubmitStrategy.UNRECOGNIZED;
  }
}

export interface ListConfigBizReq {
  /** 配置类型 */
  type: ConfigType;
  /** 进程名 */
  process: string;
}

export interface ListConfigBizRsp {
  /** 配置业务列表 */
  biz_list: ConfigBiz[];
}

export interface GetConfigSchemaReq {
  /** 配置业务标识 */
  biz_key: string;
}

export interface GetConfigSchemaRsp {
  /** 配置Schema */
  schema: ConfigSchema | undefined;
}

export interface GetEffectiveConfigValueReq {
  /** 配置标识 */
  key: ConfigKey | undefined;
}

export interface GetEffectiveConfigValueRsp {
  /** 配置值 */
  value: ConfigValue | undefined;
}

export interface PageHistoricalConfigValueReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 配置标识 */
  key: ConfigKey | undefined;
}

export interface PageHistoricalConfigValueRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 配置值 */
  values: ConfigValue[];
}

export interface SubmitConfigValueReq {
  /** 配置标识 */
  key: ConfigKey | undefined;
  /** 配置值，不同的端不一样，后端、业务端是值的json格式，客户端是APPConfigItem的json */
  value: string;
  /** 备注 */
  remark: string;
  /** 版本号 */
  version: number;
  /** 配置的Schema */
  schema: string;
  /** 提交策略 */
  strategy: SubmitStrategy;
}

export interface SubmitConfigValueRsp {}

export interface RegConfigSchemaReq {
  /** 配置类型 */
  type: ConfigType;
  /** 进程名 */
  process: string;
  /** 配置业务标识 */
  biz_key: string;
  /** 配置业务名称 */
  biz_name: string;
  /** 配置Schema */
  schema: string;
}

export interface RegConfigSchemaRsp {}

export interface ListConfigValueReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 配置业务标识 */
  biz_key: string;
  /** 配置项标识(模糊搜索) */
  item_key: string;
}

export interface ListConfigValueRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 配置值 */
  values: ConfigValue[];
}

export interface DeleteConfigValueReq {
  keys: ConfigKey[];
}

export interface DeleteConfigValueRsp {}

/** 客户端配置项约束条件 */
export interface APPConfigLimits {
  /** 平台列表 */
  os_list: number[];
  /** 版本范围 */
  version_range: APPConfigLimits_Range[];
  /** 国家列表 */
  country_list: string[];
  /** 灰度范围 */
  abslot_range: APPConfigLimits_Range[];
  /** 白名单组 */
  white_group: string[];
  /** 语言列表 */
  lan_list: string[];
  /** 发布方式 */
  rels: string[];
  /** 包名过滤 */
  pkgs: string[];
}

/** 定义 Range */
export interface APPConfigLimits_Range {
  start: number;
  end: number;
}

/** 每个value的配置项 */
export interface APPConfigSubItem {
  /** 约束项，没有就是默认项 */
  limits: APPConfigLimits | undefined;
  /** 配置的数据，json格式 */
  data: string;
  /** 是否开启这一个配置项 */
  disabled: boolean;
}

export interface APPConfigData {
  /** 无需鉴权 */
  no_auth: boolean;
  sub_items: APPConfigSubItem[];
}

/** AB测试实验 */
export interface ABTestExperiment {
  /** 实验名称 */
  name: string;
  /** 参与实验的包名, 空表示全部的包. */
  pkgs: string[];
  /** 实验组百分比 */
  experimental_percent: number;
  /** 分桶 Key */
  hash_salt: string;
  /** 实验备注 */
  remark: string;
  /** 实验组白名单 */
  experimental_white_list: string[];
  /** 对照组白名单 */
  contrasted_white_list: string[];
}

function createBaseListConfigBizReq(): ListConfigBizReq {
  return { type: 0, process: '' };
}

export const ListConfigBizReq: MessageFns<ListConfigBizReq> = {
  fromJSON(object: any): ListConfigBizReq {
    return {
      type: isSet(object.type) ? configTypeFromJSON(object.type) : 0,
      process: isSet(object.process) ? globalThis.String(object.process) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListConfigBizReq>, I>>(base?: I): ListConfigBizReq {
    return ListConfigBizReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigBizReq>, I>>(object: I): ListConfigBizReq {
    const message = createBaseListConfigBizReq();
    message.type = object.type ?? 0;
    message.process = object.process ?? '';
    return message;
  }
};

function createBaseListConfigBizRsp(): ListConfigBizRsp {
  return { biz_list: [] };
}

export const ListConfigBizRsp: MessageFns<ListConfigBizRsp> = {
  fromJSON(object: any): ListConfigBizRsp {
    return {
      biz_list: globalThis.Array.isArray(object?.biz_list) ? object.biz_list.map((e: any) => ConfigBiz.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListConfigBizRsp>, I>>(base?: I): ListConfigBizRsp {
    return ListConfigBizRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigBizRsp>, I>>(object: I): ListConfigBizRsp {
    const message = createBaseListConfigBizRsp();
    message.biz_list = object.biz_list?.map(e => ConfigBiz.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetConfigSchemaReq(): GetConfigSchemaReq {
  return { biz_key: '' };
}

export const GetConfigSchemaReq: MessageFns<GetConfigSchemaReq> = {
  fromJSON(object: any): GetConfigSchemaReq {
    return { biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '' };
  },

  create<I extends Exact<DeepPartial<GetConfigSchemaReq>, I>>(base?: I): GetConfigSchemaReq {
    return GetConfigSchemaReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigSchemaReq>, I>>(object: I): GetConfigSchemaReq {
    const message = createBaseGetConfigSchemaReq();
    message.biz_key = object.biz_key ?? '';
    return message;
  }
};

function createBaseGetConfigSchemaRsp(): GetConfigSchemaRsp {
  return { schema: undefined };
}

export const GetConfigSchemaRsp: MessageFns<GetConfigSchemaRsp> = {
  fromJSON(object: any): GetConfigSchemaRsp {
    return { schema: isSet(object.schema) ? ConfigSchema.fromJSON(object.schema) : undefined };
  },

  create<I extends Exact<DeepPartial<GetConfigSchemaRsp>, I>>(base?: I): GetConfigSchemaRsp {
    return GetConfigSchemaRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetConfigSchemaRsp>, I>>(object: I): GetConfigSchemaRsp {
    const message = createBaseGetConfigSchemaRsp();
    message.schema =
      object.schema !== undefined && object.schema !== null ? ConfigSchema.fromPartial(object.schema) : undefined;
    return message;
  }
};

function createBaseGetEffectiveConfigValueReq(): GetEffectiveConfigValueReq {
  return { key: undefined };
}

export const GetEffectiveConfigValueReq: MessageFns<GetEffectiveConfigValueReq> = {
  fromJSON(object: any): GetEffectiveConfigValueReq {
    return { key: isSet(object.key) ? ConfigKey.fromJSON(object.key) : undefined };
  },

  create<I extends Exact<DeepPartial<GetEffectiveConfigValueReq>, I>>(base?: I): GetEffectiveConfigValueReq {
    return GetEffectiveConfigValueReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEffectiveConfigValueReq>, I>>(object: I): GetEffectiveConfigValueReq {
    const message = createBaseGetEffectiveConfigValueReq();
    message.key = object.key !== undefined && object.key !== null ? ConfigKey.fromPartial(object.key) : undefined;
    return message;
  }
};

function createBaseGetEffectiveConfigValueRsp(): GetEffectiveConfigValueRsp {
  return { value: undefined };
}

export const GetEffectiveConfigValueRsp: MessageFns<GetEffectiveConfigValueRsp> = {
  fromJSON(object: any): GetEffectiveConfigValueRsp {
    return { value: isSet(object.value) ? ConfigValue.fromJSON(object.value) : undefined };
  },

  create<I extends Exact<DeepPartial<GetEffectiveConfigValueRsp>, I>>(base?: I): GetEffectiveConfigValueRsp {
    return GetEffectiveConfigValueRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEffectiveConfigValueRsp>, I>>(object: I): GetEffectiveConfigValueRsp {
    const message = createBaseGetEffectiveConfigValueRsp();
    message.value =
      object.value !== undefined && object.value !== null ? ConfigValue.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePageHistoricalConfigValueReq(): PageHistoricalConfigValueReq {
  return { page: undefined, key: undefined };
}

export const PageHistoricalConfigValueReq: MessageFns<PageHistoricalConfigValueReq> = {
  fromJSON(object: any): PageHistoricalConfigValueReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      key: isSet(object.key) ? ConfigKey.fromJSON(object.key) : undefined
    };
  },

  create<I extends Exact<DeepPartial<PageHistoricalConfigValueReq>, I>>(base?: I): PageHistoricalConfigValueReq {
    return PageHistoricalConfigValueReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageHistoricalConfigValueReq>, I>>(object: I): PageHistoricalConfigValueReq {
    const message = createBasePageHistoricalConfigValueReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.key = object.key !== undefined && object.key !== null ? ConfigKey.fromPartial(object.key) : undefined;
    return message;
  }
};

function createBasePageHistoricalConfigValueRsp(): PageHistoricalConfigValueRsp {
  return { page: undefined, values: [] };
}

export const PageHistoricalConfigValueRsp: MessageFns<PageHistoricalConfigValueRsp> = {
  fromJSON(object: any): PageHistoricalConfigValueRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => ConfigValue.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<PageHistoricalConfigValueRsp>, I>>(base?: I): PageHistoricalConfigValueRsp {
    return PageHistoricalConfigValueRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PageHistoricalConfigValueRsp>, I>>(object: I): PageHistoricalConfigValueRsp {
    const message = createBasePageHistoricalConfigValueRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.values = object.values?.map(e => ConfigValue.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSubmitConfigValueReq(): SubmitConfigValueReq {
  return { key: undefined, value: '', remark: '', version: 0, schema: '', strategy: 0 };
}

export const SubmitConfigValueReq: MessageFns<SubmitConfigValueReq> = {
  fromJSON(object: any): SubmitConfigValueReq {
    return {
      key: isSet(object.key) ? ConfigKey.fromJSON(object.key) : undefined,
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      schema: isSet(object.schema) ? globalThis.String(object.schema) : '',
      strategy: isSet(object.strategy) ? submitStrategyFromJSON(object.strategy) : 0
    };
  },

  create<I extends Exact<DeepPartial<SubmitConfigValueReq>, I>>(base?: I): SubmitConfigValueReq {
    return SubmitConfigValueReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitConfigValueReq>, I>>(object: I): SubmitConfigValueReq {
    const message = createBaseSubmitConfigValueReq();
    message.key = object.key !== undefined && object.key !== null ? ConfigKey.fromPartial(object.key) : undefined;
    message.value = object.value ?? '';
    message.remark = object.remark ?? '';
    message.version = object.version ?? 0;
    message.schema = object.schema ?? '';
    message.strategy = object.strategy ?? 0;
    return message;
  }
};

function createBaseSubmitConfigValueRsp(): SubmitConfigValueRsp {
  return {};
}

export const SubmitConfigValueRsp: MessageFns<SubmitConfigValueRsp> = {
  fromJSON(_: any): SubmitConfigValueRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SubmitConfigValueRsp>, I>>(base?: I): SubmitConfigValueRsp {
    return SubmitConfigValueRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SubmitConfigValueRsp>, I>>(_: I): SubmitConfigValueRsp {
    const message = createBaseSubmitConfigValueRsp();
    return message;
  }
};

function createBaseRegConfigSchemaReq(): RegConfigSchemaReq {
  return { type: 0, process: '', biz_key: '', biz_name: '', schema: '' };
}

export const RegConfigSchemaReq: MessageFns<RegConfigSchemaReq> = {
  fromJSON(object: any): RegConfigSchemaReq {
    return {
      type: isSet(object.type) ? configTypeFromJSON(object.type) : 0,
      process: isSet(object.process) ? globalThis.String(object.process) : '',
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '',
      biz_name: isSet(object.biz_name) ? globalThis.String(object.biz_name) : '',
      schema: isSet(object.schema) ? globalThis.String(object.schema) : ''
    };
  },

  create<I extends Exact<DeepPartial<RegConfigSchemaReq>, I>>(base?: I): RegConfigSchemaReq {
    return RegConfigSchemaReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegConfigSchemaReq>, I>>(object: I): RegConfigSchemaReq {
    const message = createBaseRegConfigSchemaReq();
    message.type = object.type ?? 0;
    message.process = object.process ?? '';
    message.biz_key = object.biz_key ?? '';
    message.biz_name = object.biz_name ?? '';
    message.schema = object.schema ?? '';
    return message;
  }
};

function createBaseRegConfigSchemaRsp(): RegConfigSchemaRsp {
  return {};
}

export const RegConfigSchemaRsp: MessageFns<RegConfigSchemaRsp> = {
  fromJSON(_: any): RegConfigSchemaRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<RegConfigSchemaRsp>, I>>(base?: I): RegConfigSchemaRsp {
    return RegConfigSchemaRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegConfigSchemaRsp>, I>>(_: I): RegConfigSchemaRsp {
    const message = createBaseRegConfigSchemaRsp();
    return message;
  }
};

function createBaseListConfigValueReq(): ListConfigValueReq {
  return { page: undefined, biz_key: '', item_key: '' };
}

export const ListConfigValueReq: MessageFns<ListConfigValueReq> = {
  fromJSON(object: any): ListConfigValueReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      biz_key: isSet(object.biz_key) ? globalThis.String(object.biz_key) : '',
      item_key: isSet(object.item_key) ? globalThis.String(object.item_key) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListConfigValueReq>, I>>(base?: I): ListConfigValueReq {
    return ListConfigValueReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigValueReq>, I>>(object: I): ListConfigValueReq {
    const message = createBaseListConfigValueReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.biz_key = object.biz_key ?? '';
    message.item_key = object.item_key ?? '';
    return message;
  }
};

function createBaseListConfigValueRsp(): ListConfigValueRsp {
  return { page: undefined, values: [] };
}

export const ListConfigValueRsp: MessageFns<ListConfigValueRsp> = {
  fromJSON(object: any): ListConfigValueRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => ConfigValue.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListConfigValueRsp>, I>>(base?: I): ListConfigValueRsp {
    return ListConfigValueRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigValueRsp>, I>>(object: I): ListConfigValueRsp {
    const message = createBaseListConfigValueRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.values = object.values?.map(e => ConfigValue.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeleteConfigValueReq(): DeleteConfigValueReq {
  return { keys: [] };
}

export const DeleteConfigValueReq: MessageFns<DeleteConfigValueReq> = {
  fromJSON(object: any): DeleteConfigValueReq {
    return { keys: globalThis.Array.isArray(object?.keys) ? object.keys.map((e: any) => ConfigKey.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteConfigValueReq>, I>>(base?: I): DeleteConfigValueReq {
    return DeleteConfigValueReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteConfigValueReq>, I>>(object: I): DeleteConfigValueReq {
    const message = createBaseDeleteConfigValueReq();
    message.keys = object.keys?.map(e => ConfigKey.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeleteConfigValueRsp(): DeleteConfigValueRsp {
  return {};
}

export const DeleteConfigValueRsp: MessageFns<DeleteConfigValueRsp> = {
  fromJSON(_: any): DeleteConfigValueRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteConfigValueRsp>, I>>(base?: I): DeleteConfigValueRsp {
    return DeleteConfigValueRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteConfigValueRsp>, I>>(_: I): DeleteConfigValueRsp {
    const message = createBaseDeleteConfigValueRsp();
    return message;
  }
};

function createBaseAPPConfigLimits(): APPConfigLimits {
  return {
    os_list: [],
    version_range: [],
    country_list: [],
    abslot_range: [],
    white_group: [],
    lan_list: [],
    rels: [],
    pkgs: []
  };
}

export const APPConfigLimits: MessageFns<APPConfigLimits> = {
  fromJSON(object: any): APPConfigLimits {
    return {
      os_list: globalThis.Array.isArray(object?.os_list) ? object.os_list.map((e: any) => globalThis.Number(e)) : [],
      version_range: globalThis.Array.isArray(object?.version_range)
        ? object.version_range.map((e: any) => APPConfigLimits_Range.fromJSON(e))
        : [],
      country_list: globalThis.Array.isArray(object?.country_list)
        ? object.country_list.map((e: any) => globalThis.String(e))
        : [],
      abslot_range: globalThis.Array.isArray(object?.abslot_range)
        ? object.abslot_range.map((e: any) => APPConfigLimits_Range.fromJSON(e))
        : [],
      white_group: globalThis.Array.isArray(object?.white_group)
        ? object.white_group.map((e: any) => globalThis.String(e))
        : [],
      lan_list: globalThis.Array.isArray(object?.lan_list) ? object.lan_list.map((e: any) => globalThis.String(e)) : [],
      rels: globalThis.Array.isArray(object?.rels) ? object.rels.map((e: any) => globalThis.String(e)) : [],
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<APPConfigLimits>, I>>(base?: I): APPConfigLimits {
    return APPConfigLimits.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<APPConfigLimits>, I>>(object: I): APPConfigLimits {
    const message = createBaseAPPConfigLimits();
    message.os_list = object.os_list?.map(e => e) || [];
    message.version_range = object.version_range?.map(e => APPConfigLimits_Range.fromPartial(e)) || [];
    message.country_list = object.country_list?.map(e => e) || [];
    message.abslot_range = object.abslot_range?.map(e => APPConfigLimits_Range.fromPartial(e)) || [];
    message.white_group = object.white_group?.map(e => e) || [];
    message.lan_list = object.lan_list?.map(e => e) || [];
    message.rels = object.rels?.map(e => e) || [];
    message.pkgs = object.pkgs?.map(e => e) || [];
    return message;
  }
};

function createBaseAPPConfigLimits_Range(): APPConfigLimits_Range {
  return { start: 0, end: 0 };
}

export const APPConfigLimits_Range: MessageFns<APPConfigLimits_Range> = {
  fromJSON(object: any): APPConfigLimits_Range {
    return {
      start: isSet(object.start) ? globalThis.Number(object.start) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0
    };
  },

  create<I extends Exact<DeepPartial<APPConfigLimits_Range>, I>>(base?: I): APPConfigLimits_Range {
    return APPConfigLimits_Range.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<APPConfigLimits_Range>, I>>(object: I): APPConfigLimits_Range {
    const message = createBaseAPPConfigLimits_Range();
    message.start = object.start ?? 0;
    message.end = object.end ?? 0;
    return message;
  }
};

function createBaseAPPConfigSubItem(): APPConfigSubItem {
  return { limits: undefined, data: '', disabled: false };
}

export const APPConfigSubItem: MessageFns<APPConfigSubItem> = {
  fromJSON(object: any): APPConfigSubItem {
    return {
      limits: isSet(object.limits) ? APPConfigLimits.fromJSON(object.limits) : undefined,
      data: isSet(object.data) ? globalThis.String(object.data) : '',
      disabled: isSet(object.disabled) ? globalThis.Boolean(object.disabled) : false
    };
  },

  create<I extends Exact<DeepPartial<APPConfigSubItem>, I>>(base?: I): APPConfigSubItem {
    return APPConfigSubItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<APPConfigSubItem>, I>>(object: I): APPConfigSubItem {
    const message = createBaseAPPConfigSubItem();
    message.limits =
      object.limits !== undefined && object.limits !== null ? APPConfigLimits.fromPartial(object.limits) : undefined;
    message.data = object.data ?? '';
    message.disabled = object.disabled ?? false;
    return message;
  }
};

function createBaseAPPConfigData(): APPConfigData {
  return { no_auth: false, sub_items: [] };
}

export const APPConfigData: MessageFns<APPConfigData> = {
  fromJSON(object: any): APPConfigData {
    return {
      no_auth: isSet(object.no_auth) ? globalThis.Boolean(object.no_auth) : false,
      sub_items: globalThis.Array.isArray(object?.sub_items)
        ? object.sub_items.map((e: any) => APPConfigSubItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<APPConfigData>, I>>(base?: I): APPConfigData {
    return APPConfigData.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<APPConfigData>, I>>(object: I): APPConfigData {
    const message = createBaseAPPConfigData();
    message.no_auth = object.no_auth ?? false;
    message.sub_items = object.sub_items?.map(e => APPConfigSubItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseABTestExperiment(): ABTestExperiment {
  return {
    name: '',
    pkgs: [],
    experimental_percent: 0,
    hash_salt: '',
    remark: '',
    experimental_white_list: [],
    contrasted_white_list: []
  };
}

export const ABTestExperiment: MessageFns<ABTestExperiment> = {
  fromJSON(object: any): ABTestExperiment {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => globalThis.String(e)) : [],
      experimental_percent: isSet(object.experimental_percent) ? globalThis.Number(object.experimental_percent) : 0,
      hash_salt: isSet(object.hash_salt) ? globalThis.String(object.hash_salt) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      experimental_white_list: globalThis.Array.isArray(object?.experimental_white_list)
        ? object.experimental_white_list.map((e: any) => globalThis.String(e))
        : [],
      contrasted_white_list: globalThis.Array.isArray(object?.contrasted_white_list)
        ? object.contrasted_white_list.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ABTestExperiment>, I>>(base?: I): ABTestExperiment {
    return ABTestExperiment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ABTestExperiment>, I>>(object: I): ABTestExperiment {
    const message = createBaseABTestExperiment();
    message.name = object.name ?? '';
    message.pkgs = object.pkgs?.map(e => e) || [];
    message.experimental_percent = object.experimental_percent ?? 0;
    message.hash_salt = object.hash_salt ?? '';
    message.remark = object.remark ?? '';
    message.experimental_white_list = object.experimental_white_list?.map(e => e) || [];
    message.contrasted_white_list = object.contrasted_white_list?.map(e => e) || [];
    return message;
  }
};

export type ConfigDefinition = typeof ConfigDefinition;
export const ConfigDefinition = {
  name: 'Config',
  fullName: 'comm.mgr.config.Config',
  methods: {
    /** 获取配置业务列表 */
    listConfigBiz: {
      name: 'ListConfigBiz',
      requestType: ListConfigBizReq,
      requestStream: false,
      responseType: ListConfigBizRsp,
      responseStream: false,
      options: {}
    },
    /** 获取配置Schema(根据配置业务标识) */
    getConfigSchema: {
      name: 'GetConfigSchema',
      requestType: GetConfigSchemaReq,
      requestStream: false,
      responseType: GetConfigSchemaRsp,
      responseStream: false,
      options: {}
    },
    /** 获取生效中的配置值(根据配置业务标识 + 配置项标识) */
    getEffectiveConfigValue: {
      name: 'GetEffectiveConfigValue',
      requestType: GetEffectiveConfigValueReq,
      requestStream: false,
      responseType: GetEffectiveConfigValueRsp,
      responseStream: false,
      options: {}
    },
    /** 分页获取历史配置值(根据配置业务标识 + 配置项标识) */
    pageHistoricalConfigValue: {
      name: 'PageHistoricalConfigValue',
      requestType: PageHistoricalConfigValueReq,
      requestStream: false,
      responseType: PageHistoricalConfigValueRsp,
      responseStream: false,
      options: {}
    },
    /** 提交配置值(目前提交即发布, 后续可能会增加发布环节) */
    submitConfigValue: {
      name: 'SubmitConfigValue',
      requestType: SubmitConfigValueReq,
      requestStream: false,
      responseType: SubmitConfigValueRsp,
      responseStream: false,
      options: {}
    },
    /** 注册配置Schema */
    regConfigSchema: {
      name: 'RegConfigSchema',
      requestType: RegConfigSchemaReq,
      requestStream: false,
      responseType: RegConfigSchemaRsp,
      responseStream: false,
      options: {}
    },
    /** 获取配置值列表(对于表格类型的) */
    listConfigValue: {
      name: 'ListConfigValue',
      requestType: ListConfigValueReq,
      requestStream: false,
      responseType: ListConfigValueRsp,
      responseStream: false,
      options: {}
    },
    /** 删除配置值(对于表格类型的) */
    deleteConfigValue: {
      name: 'DeleteConfigValue',
      requestType: DeleteConfigValueReq,
      requestStream: false,
      responseType: DeleteConfigValueRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
