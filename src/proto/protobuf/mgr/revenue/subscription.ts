// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/subscription.proto

/* eslint-disable */
import { StringList } from '../../api/pay/common';
import { Subscription as Subscription1 } from '../../api/pay/subscription';

export const protobufPackage = 'mgr.subscription';

export interface SaveSubscriptionReq {
  /** 订阅项ID，空标识新增，非空标识修改 */
  subscription_id: string;
  /** 订阅项内容 */
  subscription: Subscription | undefined;
}

export interface SaveSubscriptionRsp {
  subscription: Subscription | undefined;
}

/** 订阅项 */
export interface Subscription {
  /** 核心配置 */
  base_info: Subscription1 | undefined;
  /** 过滤参数配置，多个参数是与的关系 */
  param_filter: { [key: string]: StringList };
  /** 备注 */
  remark: string;
  /** 是否上架 */
  published: boolean;
}

export interface Subscription_ParamFilterEntry {
  key: string;
  value: StringList | undefined;
}

export interface UpdateSubscriptionStatusReq {
  /** 要修改的订阅项ID列表 */
  subscription_ids: string[];
  /** 是否上架 */
  published: boolean;
  /** 是否删除 */
  deleted: boolean;
}

export interface UpdateSubscriptionStatusRsp {
  /** 修改成功的ID列表，批量修改不用事务，所以可能只是部分成功 */
  success_ids: string[];
}

function createBaseSaveSubscriptionReq(): SaveSubscriptionReq {
  return { subscription_id: '', subscription: undefined };
}

export const SaveSubscriptionReq: MessageFns<SaveSubscriptionReq> = {
  fromJSON(object: any): SaveSubscriptionReq {
    return {
      subscription_id: isSet(object.subscription_id) ? globalThis.String(object.subscription_id) : '',
      subscription: isSet(object.subscription) ? Subscription.fromJSON(object.subscription) : undefined
    };
  },

  create<I extends Exact<DeepPartial<SaveSubscriptionReq>, I>>(base?: I): SaveSubscriptionReq {
    return SaveSubscriptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveSubscriptionReq>, I>>(object: I): SaveSubscriptionReq {
    const message = createBaseSaveSubscriptionReq();
    message.subscription_id = object.subscription_id ?? '';
    message.subscription =
      object.subscription !== undefined && object.subscription !== null
        ? Subscription.fromPartial(object.subscription)
        : undefined;
    return message;
  }
};

function createBaseSaveSubscriptionRsp(): SaveSubscriptionRsp {
  return { subscription: undefined };
}

export const SaveSubscriptionRsp: MessageFns<SaveSubscriptionRsp> = {
  fromJSON(object: any): SaveSubscriptionRsp {
    return { subscription: isSet(object.subscription) ? Subscription.fromJSON(object.subscription) : undefined };
  },

  create<I extends Exact<DeepPartial<SaveSubscriptionRsp>, I>>(base?: I): SaveSubscriptionRsp {
    return SaveSubscriptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveSubscriptionRsp>, I>>(object: I): SaveSubscriptionRsp {
    const message = createBaseSaveSubscriptionRsp();
    message.subscription =
      object.subscription !== undefined && object.subscription !== null
        ? Subscription.fromPartial(object.subscription)
        : undefined;
    return message;
  }
};

function createBaseSubscription(): Subscription {
  return { base_info: undefined, param_filter: {}, remark: '', published: false };
}

export const Subscription: MessageFns<Subscription> = {
  fromJSON(object: any): Subscription {
    return {
      base_info: isSet(object.base_info) ? Subscription1.fromJSON(object.base_info) : undefined,
      param_filter: isObject(object.param_filter)
        ? Object.entries(object.param_filter).reduce<{ [key: string]: StringList }>((acc, [key, value]) => {
            acc[key] = StringList.fromJSON(value);
            return acc;
          }, {})
        : {},
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false
    };
  },

  create<I extends Exact<DeepPartial<Subscription>, I>>(base?: I): Subscription {
    return Subscription.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Subscription>, I>>(object: I): Subscription {
    const message = createBaseSubscription();
    message.base_info =
      object.base_info !== undefined && object.base_info !== null
        ? Subscription1.fromPartial(object.base_info)
        : undefined;
    message.param_filter = Object.entries(object.param_filter ?? {}).reduce<{ [key: string]: StringList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = StringList.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.remark = object.remark ?? '';
    message.published = object.published ?? false;
    return message;
  }
};

function createBaseSubscription_ParamFilterEntry(): Subscription_ParamFilterEntry {
  return { key: '', value: undefined };
}

export const Subscription_ParamFilterEntry: MessageFns<Subscription_ParamFilterEntry> = {
  fromJSON(object: any): Subscription_ParamFilterEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? StringList.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Subscription_ParamFilterEntry>, I>>(base?: I): Subscription_ParamFilterEntry {
    return Subscription_ParamFilterEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Subscription_ParamFilterEntry>, I>>(
    object: I
  ): Subscription_ParamFilterEntry {
    const message = createBaseSubscription_ParamFilterEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? StringList.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseUpdateSubscriptionStatusReq(): UpdateSubscriptionStatusReq {
  return { subscription_ids: [], published: false, deleted: false };
}

export const UpdateSubscriptionStatusReq: MessageFns<UpdateSubscriptionStatusReq> = {
  fromJSON(object: any): UpdateSubscriptionStatusReq {
    return {
      subscription_ids: globalThis.Array.isArray(object?.subscription_ids)
        ? object.subscription_ids.map((e: any) => globalThis.String(e))
        : [],
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false
    };
  },

  create<I extends Exact<DeepPartial<UpdateSubscriptionStatusReq>, I>>(base?: I): UpdateSubscriptionStatusReq {
    return UpdateSubscriptionStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSubscriptionStatusReq>, I>>(object: I): UpdateSubscriptionStatusReq {
    const message = createBaseUpdateSubscriptionStatusReq();
    message.subscription_ids = object.subscription_ids?.map(e => e) || [];
    message.published = object.published ?? false;
    message.deleted = object.deleted ?? false;
    return message;
  }
};

function createBaseUpdateSubscriptionStatusRsp(): UpdateSubscriptionStatusRsp {
  return { success_ids: [] };
}

export const UpdateSubscriptionStatusRsp: MessageFns<UpdateSubscriptionStatusRsp> = {
  fromJSON(object: any): UpdateSubscriptionStatusRsp {
    return {
      success_ids: globalThis.Array.isArray(object?.success_ids)
        ? object.success_ids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<UpdateSubscriptionStatusRsp>, I>>(base?: I): UpdateSubscriptionStatusRsp {
    return UpdateSubscriptionStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSubscriptionStatusRsp>, I>>(object: I): UpdateSubscriptionStatusRsp {
    const message = createBaseUpdateSubscriptionStatusRsp();
    message.success_ids = object.success_ids?.map(e => e) || [];
    return message;
  }
};

/** 支付订阅管理 */
export type SubscriptionMgrDefinition = typeof SubscriptionMgrDefinition;
export const SubscriptionMgrDefinition = {
  name: 'SubscriptionMgr',
  fullName: 'mgr.subscription.SubscriptionMgr',
  methods: {
    /** 保存订阅项 */
    saveSubscription: {
      name: 'SaveSubscription',
      requestType: SaveSubscriptionReq,
      requestStream: false,
      responseType: SaveSubscriptionRsp,
      responseStream: false,
      options: {}
    },
    /** 修改状态 */
    updateSubscriptionStatus: {
      name: 'UpdateSubscriptionStatus',
      requestType: UpdateSubscriptionStatusReq,
      requestStream: false,
      responseType: UpdateSubscriptionStatusRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
