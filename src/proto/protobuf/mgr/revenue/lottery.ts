// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/lottery.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { LotteryType, lotteryTypeFromJSON, PrizeType, prizeTypeFromJSON } from '../../api/revenue/lottery';

export const protobufPackage = 'mgr.lottery';

/** 概率池 */
export enum ProbabilityPool {
  PROBABILITY_POOL_NONE = 0,
  /** PROBABILITY_POOL_NEWER - 新手池 */
  PROBABILITY_POOL_NEWER = 10,
  /** PROBABILITY_POOL_NORMAL - 普通池 */
  PROBABILITY_POOL_NORMAL = 20,
  /** PROBABILITY_POOL_RECOVERY - 回血池 */
  PROBABILITY_POOL_RECOVERY = 30,
  UNRECOGNIZED = -1
}

export function probabilityPoolFromJSON(object: any): ProbabilityPool {
  switch (object) {
    case 0:
    case 'PROBABILITY_POOL_NONE':
      return ProbabilityPool.PROBABILITY_POOL_NONE;
    case 10:
    case 'PROBABILITY_POOL_NEWER':
      return ProbabilityPool.PROBABILITY_POOL_NEWER;
    case 20:
    case 'PROBABILITY_POOL_NORMAL':
      return ProbabilityPool.PROBABILITY_POOL_NORMAL;
    case 30:
    case 'PROBABILITY_POOL_RECOVERY':
      return ProbabilityPool.PROBABILITY_POOL_RECOVERY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ProbabilityPool.UNRECOGNIZED;
  }
}

export interface CreateLotteryReq {
  lottery_config: LotteryConfig | undefined;
}

export interface CreateLotteryRsp {
  lottery_config: LotteryConfig | undefined;
}

export interface UpdateLotteryReq {
  lottery_config: LotteryConfig | undefined;
}

export interface UpdateLotteryRsp {
  lottery_config: LotteryConfig | undefined;
}

export interface DeleteLotteryReq {
  lottery_ids: number[];
}

export interface DeleteLotteryRsp {}

export interface SearchLotteryReq {
  page: Page | undefined;
  lottery_types: LotteryType[];
  lottery_ids: number[];
}

export interface SearchLotteryRsp {
  page: Page | undefined;
  lottery_configs: LotteryConfig[];
}

export interface CopyLotteryReq {
  lottery_id: number;
  name: string;
}

export interface CopyLotteryRsp {
  new_lottery_id: number;
}

export interface ListUnboundLotteryReq {}

export interface ListUnboundLotteryRsp {
  lottery_configs: LotteryConfig[];
}

export interface ListPrizeReq {
  lottery_id: number;
}

export interface ListPrizeRsp {
  lottery_id: number;
  lottery_version: number;
  prize_configs: PrizeConfig[];
}

export interface SavePrizeReq {
  lottery_id: number;
  lottery_version: number;
  prize_configs: PrizeConfig[];
}

export interface SavePrizeRsp {}

/** 添加黑名单 */
export interface AddBlacklistReq {
  lottery_id: number;
  pool: string;
  uids: number[];
}

export interface AddBlacklistRsp {}

/** 删除黑名单 */
export interface DeleteBlacklistReq {
  lottery_id: number;
  pool: string;
  uids: number[];
}

export interface DeleteBlacklistRsp {}

/** 搜索黑名单 */
export interface SearchBlacklistReq {
  page: Page | undefined;
  lottery_id: number;
  pools: string[];
  uids: number[];
}

export interface SearchBlacklistRsp {
  page: Page | undefined;
  blacklists: PoolBlacklist[];
}

/** 抽奖配置 */
export interface LotteryConfig {
  lottery_id: number;
  lottery_type: LotteryType;
  name: string;
  remark: string;
  version: number;
  pool_configs: { [key: string]: PoolConfig };
  created_at: number;
  created_by: string;
  updated_at: number;
  updated_by: string;
}

export interface LotteryConfig_PoolConfigsEntry {
  key: string;
  value: PoolConfig | undefined;
}

/** 奖池配置 */
export interface PoolConfig {
  /** 最小投入 */
  min_investment: number;
  /** 最大投入 */
  max_investment: number;
  /** 最小投入产出比 */
  min_roi: number;
  /** 最大投入产出比 */
  max_roi: number;
}

/** 奖品配置 */
export interface PrizeConfig {
  prize_id: number;
  icon: string;
  i18n_name: { [key: string]: string };
  prize_type: PrizeType;
  prize_value: string;
  /** map<概率池, 概率> */
  probability: { [key: string]: number };
  sequence: number;
}

export interface PrizeConfig_I18nNameEntry {
  key: string;
  value: string;
}

export interface PrizeConfig_ProbabilityEntry {
  key: string;
  value: number;
}

/** 奖池黑名单 */
export interface PoolBlacklist {
  blacklist_id: number;
  lottery_id: number;
  pool: string;
  uid: number;
  created_at: number;
  created_by: string;
}

function createBaseCreateLotteryReq(): CreateLotteryReq {
  return { lottery_config: undefined };
}

export const CreateLotteryReq: MessageFns<CreateLotteryReq> = {
  fromJSON(object: any): CreateLotteryReq {
    return { lottery_config: isSet(object.lottery_config) ? LotteryConfig.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateLotteryReq>, I>>(base?: I): CreateLotteryReq {
    return CreateLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateLotteryReq>, I>>(object: I): CreateLotteryReq {
    const message = createBaseCreateLotteryReq();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryConfig.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseCreateLotteryRsp(): CreateLotteryRsp {
  return { lottery_config: undefined };
}

export const CreateLotteryRsp: MessageFns<CreateLotteryRsp> = {
  fromJSON(object: any): CreateLotteryRsp {
    return { lottery_config: isSet(object.lottery_config) ? LotteryConfig.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateLotteryRsp>, I>>(base?: I): CreateLotteryRsp {
    return CreateLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateLotteryRsp>, I>>(object: I): CreateLotteryRsp {
    const message = createBaseCreateLotteryRsp();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryConfig.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseUpdateLotteryReq(): UpdateLotteryReq {
  return { lottery_config: undefined };
}

export const UpdateLotteryReq: MessageFns<UpdateLotteryReq> = {
  fromJSON(object: any): UpdateLotteryReq {
    return { lottery_config: isSet(object.lottery_config) ? LotteryConfig.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateLotteryReq>, I>>(base?: I): UpdateLotteryReq {
    return UpdateLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateLotteryReq>, I>>(object: I): UpdateLotteryReq {
    const message = createBaseUpdateLotteryReq();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryConfig.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseUpdateLotteryRsp(): UpdateLotteryRsp {
  return { lottery_config: undefined };
}

export const UpdateLotteryRsp: MessageFns<UpdateLotteryRsp> = {
  fromJSON(object: any): UpdateLotteryRsp {
    return { lottery_config: isSet(object.lottery_config) ? LotteryConfig.fromJSON(object.lottery_config) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateLotteryRsp>, I>>(base?: I): UpdateLotteryRsp {
    return UpdateLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateLotteryRsp>, I>>(object: I): UpdateLotteryRsp {
    const message = createBaseUpdateLotteryRsp();
    message.lottery_config =
      object.lottery_config !== undefined && object.lottery_config !== null
        ? LotteryConfig.fromPartial(object.lottery_config)
        : undefined;
    return message;
  }
};

function createBaseDeleteLotteryReq(): DeleteLotteryReq {
  return { lottery_ids: [] };
}

export const DeleteLotteryReq: MessageFns<DeleteLotteryReq> = {
  fromJSON(object: any): DeleteLotteryReq {
    return {
      lottery_ids: globalThis.Array.isArray(object?.lottery_ids)
        ? object.lottery_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteLotteryReq>, I>>(base?: I): DeleteLotteryReq {
    return DeleteLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteLotteryReq>, I>>(object: I): DeleteLotteryReq {
    const message = createBaseDeleteLotteryReq();
    message.lottery_ids = object.lottery_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteLotteryRsp(): DeleteLotteryRsp {
  return {};
}

export const DeleteLotteryRsp: MessageFns<DeleteLotteryRsp> = {
  fromJSON(_: any): DeleteLotteryRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteLotteryRsp>, I>>(base?: I): DeleteLotteryRsp {
    return DeleteLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteLotteryRsp>, I>>(_: I): DeleteLotteryRsp {
    const message = createBaseDeleteLotteryRsp();
    return message;
  }
};

function createBaseSearchLotteryReq(): SearchLotteryReq {
  return { page: undefined, lottery_types: [], lottery_ids: [] };
}

export const SearchLotteryReq: MessageFns<SearchLotteryReq> = {
  fromJSON(object: any): SearchLotteryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      lottery_types: globalThis.Array.isArray(object?.lottery_types)
        ? object.lottery_types.map((e: any) => lotteryTypeFromJSON(e))
        : [],
      lottery_ids: globalThis.Array.isArray(object?.lottery_ids)
        ? object.lottery_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchLotteryReq>, I>>(base?: I): SearchLotteryReq {
    return SearchLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchLotteryReq>, I>>(object: I): SearchLotteryReq {
    const message = createBaseSearchLotteryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.lottery_types = object.lottery_types?.map(e => e) || [];
    message.lottery_ids = object.lottery_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchLotteryRsp(): SearchLotteryRsp {
  return { page: undefined, lottery_configs: [] };
}

export const SearchLotteryRsp: MessageFns<SearchLotteryRsp> = {
  fromJSON(object: any): SearchLotteryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      lottery_configs: globalThis.Array.isArray(object?.lottery_configs)
        ? object.lottery_configs.map((e: any) => LotteryConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchLotteryRsp>, I>>(base?: I): SearchLotteryRsp {
    return SearchLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchLotteryRsp>, I>>(object: I): SearchLotteryRsp {
    const message = createBaseSearchLotteryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.lottery_configs = object.lottery_configs?.map(e => LotteryConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCopyLotteryReq(): CopyLotteryReq {
  return { lottery_id: 0, name: '' };
}

export const CopyLotteryReq: MessageFns<CopyLotteryReq> = {
  fromJSON(object: any): CopyLotteryReq {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<CopyLotteryReq>, I>>(base?: I): CopyLotteryReq {
    return CopyLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyLotteryReq>, I>>(object: I): CopyLotteryReq {
    const message = createBaseCopyLotteryReq();
    message.lottery_id = object.lottery_id ?? 0;
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseCopyLotteryRsp(): CopyLotteryRsp {
  return { new_lottery_id: 0 };
}

export const CopyLotteryRsp: MessageFns<CopyLotteryRsp> = {
  fromJSON(object: any): CopyLotteryRsp {
    return { new_lottery_id: isSet(object.new_lottery_id) ? globalThis.Number(object.new_lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CopyLotteryRsp>, I>>(base?: I): CopyLotteryRsp {
    return CopyLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyLotteryRsp>, I>>(object: I): CopyLotteryRsp {
    const message = createBaseCopyLotteryRsp();
    message.new_lottery_id = object.new_lottery_id ?? 0;
    return message;
  }
};

function createBaseListUnboundLotteryReq(): ListUnboundLotteryReq {
  return {};
}

export const ListUnboundLotteryReq: MessageFns<ListUnboundLotteryReq> = {
  fromJSON(_: any): ListUnboundLotteryReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListUnboundLotteryReq>, I>>(base?: I): ListUnboundLotteryReq {
    return ListUnboundLotteryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListUnboundLotteryReq>, I>>(_: I): ListUnboundLotteryReq {
    const message = createBaseListUnboundLotteryReq();
    return message;
  }
};

function createBaseListUnboundLotteryRsp(): ListUnboundLotteryRsp {
  return { lottery_configs: [] };
}

export const ListUnboundLotteryRsp: MessageFns<ListUnboundLotteryRsp> = {
  fromJSON(object: any): ListUnboundLotteryRsp {
    return {
      lottery_configs: globalThis.Array.isArray(object?.lottery_configs)
        ? object.lottery_configs.map((e: any) => LotteryConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListUnboundLotteryRsp>, I>>(base?: I): ListUnboundLotteryRsp {
    return ListUnboundLotteryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListUnboundLotteryRsp>, I>>(object: I): ListUnboundLotteryRsp {
    const message = createBaseListUnboundLotteryRsp();
    message.lottery_configs = object.lottery_configs?.map(e => LotteryConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListPrizeReq(): ListPrizeReq {
  return { lottery_id: 0 };
}

export const ListPrizeReq: MessageFns<ListPrizeReq> = {
  fromJSON(object: any): ListPrizeReq {
    return { lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListPrizeReq>, I>>(base?: I): ListPrizeReq {
    return ListPrizeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPrizeReq>, I>>(object: I): ListPrizeReq {
    const message = createBaseListPrizeReq();
    message.lottery_id = object.lottery_id ?? 0;
    return message;
  }
};

function createBaseListPrizeRsp(): ListPrizeRsp {
  return { lottery_id: 0, lottery_version: 0, prize_configs: [] };
}

export const ListPrizeRsp: MessageFns<ListPrizeRsp> = {
  fromJSON(object: any): ListPrizeRsp {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      lottery_version: isSet(object.lottery_version) ? globalThis.Number(object.lottery_version) : 0,
      prize_configs: globalThis.Array.isArray(object?.prize_configs)
        ? object.prize_configs.map((e: any) => PrizeConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListPrizeRsp>, I>>(base?: I): ListPrizeRsp {
    return ListPrizeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPrizeRsp>, I>>(object: I): ListPrizeRsp {
    const message = createBaseListPrizeRsp();
    message.lottery_id = object.lottery_id ?? 0;
    message.lottery_version = object.lottery_version ?? 0;
    message.prize_configs = object.prize_configs?.map(e => PrizeConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSavePrizeReq(): SavePrizeReq {
  return { lottery_id: 0, lottery_version: 0, prize_configs: [] };
}

export const SavePrizeReq: MessageFns<SavePrizeReq> = {
  fromJSON(object: any): SavePrizeReq {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      lottery_version: isSet(object.lottery_version) ? globalThis.Number(object.lottery_version) : 0,
      prize_configs: globalThis.Array.isArray(object?.prize_configs)
        ? object.prize_configs.map((e: any) => PrizeConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SavePrizeReq>, I>>(base?: I): SavePrizeReq {
    return SavePrizeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePrizeReq>, I>>(object: I): SavePrizeReq {
    const message = createBaseSavePrizeReq();
    message.lottery_id = object.lottery_id ?? 0;
    message.lottery_version = object.lottery_version ?? 0;
    message.prize_configs = object.prize_configs?.map(e => PrizeConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSavePrizeRsp(): SavePrizeRsp {
  return {};
}

export const SavePrizeRsp: MessageFns<SavePrizeRsp> = {
  fromJSON(_: any): SavePrizeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SavePrizeRsp>, I>>(base?: I): SavePrizeRsp {
    return SavePrizeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SavePrizeRsp>, I>>(_: I): SavePrizeRsp {
    const message = createBaseSavePrizeRsp();
    return message;
  }
};

function createBaseAddBlacklistReq(): AddBlacklistReq {
  return { lottery_id: 0, pool: '', uids: [] };
}

export const AddBlacklistReq: MessageFns<AddBlacklistReq> = {
  fromJSON(object: any): AddBlacklistReq {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      pool: isSet(object.pool) ? globalThis.String(object.pool) : '',
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<AddBlacklistReq>, I>>(base?: I): AddBlacklistReq {
    return AddBlacklistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddBlacklistReq>, I>>(object: I): AddBlacklistReq {
    const message = createBaseAddBlacklistReq();
    message.lottery_id = object.lottery_id ?? 0;
    message.pool = object.pool ?? '';
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseAddBlacklistRsp(): AddBlacklistRsp {
  return {};
}

export const AddBlacklistRsp: MessageFns<AddBlacklistRsp> = {
  fromJSON(_: any): AddBlacklistRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddBlacklistRsp>, I>>(base?: I): AddBlacklistRsp {
    return AddBlacklistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddBlacklistRsp>, I>>(_: I): AddBlacklistRsp {
    const message = createBaseAddBlacklistRsp();
    return message;
  }
};

function createBaseDeleteBlacklistReq(): DeleteBlacklistReq {
  return { lottery_id: 0, pool: '', uids: [] };
}

export const DeleteBlacklistReq: MessageFns<DeleteBlacklistReq> = {
  fromJSON(object: any): DeleteBlacklistReq {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      pool: isSet(object.pool) ? globalThis.String(object.pool) : '',
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteBlacklistReq>, I>>(base?: I): DeleteBlacklistReq {
    return DeleteBlacklistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBlacklistReq>, I>>(object: I): DeleteBlacklistReq {
    const message = createBaseDeleteBlacklistReq();
    message.lottery_id = object.lottery_id ?? 0;
    message.pool = object.pool ?? '';
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteBlacklistRsp(): DeleteBlacklistRsp {
  return {};
}

export const DeleteBlacklistRsp: MessageFns<DeleteBlacklistRsp> = {
  fromJSON(_: any): DeleteBlacklistRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteBlacklistRsp>, I>>(base?: I): DeleteBlacklistRsp {
    return DeleteBlacklistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBlacklistRsp>, I>>(_: I): DeleteBlacklistRsp {
    const message = createBaseDeleteBlacklistRsp();
    return message;
  }
};

function createBaseSearchBlacklistReq(): SearchBlacklistReq {
  return { page: undefined, lottery_id: 0, pools: [], uids: [] };
}

export const SearchBlacklistReq: MessageFns<SearchBlacklistReq> = {
  fromJSON(object: any): SearchBlacklistReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      pools: globalThis.Array.isArray(object?.pools) ? object.pools.map((e: any) => globalThis.String(e)) : [],
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchBlacklistReq>, I>>(base?: I): SearchBlacklistReq {
    return SearchBlacklistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchBlacklistReq>, I>>(object: I): SearchBlacklistReq {
    const message = createBaseSearchBlacklistReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.lottery_id = object.lottery_id ?? 0;
    message.pools = object.pools?.map(e => e) || [];
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchBlacklistRsp(): SearchBlacklistRsp {
  return { page: undefined, blacklists: [] };
}

export const SearchBlacklistRsp: MessageFns<SearchBlacklistRsp> = {
  fromJSON(object: any): SearchBlacklistRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      blacklists: globalThis.Array.isArray(object?.blacklists)
        ? object.blacklists.map((e: any) => PoolBlacklist.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchBlacklistRsp>, I>>(base?: I): SearchBlacklistRsp {
    return SearchBlacklistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchBlacklistRsp>, I>>(object: I): SearchBlacklistRsp {
    const message = createBaseSearchBlacklistRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.blacklists = object.blacklists?.map(e => PoolBlacklist.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLotteryConfig(): LotteryConfig {
  return {
    lottery_id: 0,
    lottery_type: 0,
    name: '',
    remark: '',
    version: 0,
    pool_configs: {},
    created_at: 0,
    created_by: '',
    updated_at: 0,
    updated_by: ''
  };
}

export const LotteryConfig: MessageFns<LotteryConfig> = {
  fromJSON(object: any): LotteryConfig {
    return {
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      lottery_type: isSet(object.lottery_type) ? lotteryTypeFromJSON(object.lottery_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      pool_configs: isObject(object.pool_configs)
        ? Object.entries(object.pool_configs).reduce<{ [key: string]: PoolConfig }>((acc, [key, value]) => {
            acc[key] = PoolConfig.fromJSON(value);
            return acc;
          }, {})
        : {},
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<LotteryConfig>, I>>(base?: I): LotteryConfig {
    return LotteryConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryConfig>, I>>(object: I): LotteryConfig {
    const message = createBaseLotteryConfig();
    message.lottery_id = object.lottery_id ?? 0;
    message.lottery_type = object.lottery_type ?? 0;
    message.name = object.name ?? '';
    message.remark = object.remark ?? '';
    message.version = object.version ?? 0;
    message.pool_configs = Object.entries(object.pool_configs ?? {}).reduce<{ [key: string]: PoolConfig }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = PoolConfig.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updated_by = object.updated_by ?? '';
    return message;
  }
};

function createBaseLotteryConfig_PoolConfigsEntry(): LotteryConfig_PoolConfigsEntry {
  return { key: '', value: undefined };
}

export const LotteryConfig_PoolConfigsEntry: MessageFns<LotteryConfig_PoolConfigsEntry> = {
  fromJSON(object: any): LotteryConfig_PoolConfigsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? PoolConfig.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<LotteryConfig_PoolConfigsEntry>, I>>(base?: I): LotteryConfig_PoolConfigsEntry {
    return LotteryConfig_PoolConfigsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LotteryConfig_PoolConfigsEntry>, I>>(
    object: I
  ): LotteryConfig_PoolConfigsEntry {
    const message = createBaseLotteryConfig_PoolConfigsEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? PoolConfig.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePoolConfig(): PoolConfig {
  return { min_investment: 0, max_investment: 0, min_roi: 0, max_roi: 0 };
}

export const PoolConfig: MessageFns<PoolConfig> = {
  fromJSON(object: any): PoolConfig {
    return {
      min_investment: isSet(object.min_investment) ? globalThis.Number(object.min_investment) : 0,
      max_investment: isSet(object.max_investment) ? globalThis.Number(object.max_investment) : 0,
      min_roi: isSet(object.min_roi) ? globalThis.Number(object.min_roi) : 0,
      max_roi: isSet(object.max_roi) ? globalThis.Number(object.max_roi) : 0
    };
  },

  create<I extends Exact<DeepPartial<PoolConfig>, I>>(base?: I): PoolConfig {
    return PoolConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PoolConfig>, I>>(object: I): PoolConfig {
    const message = createBasePoolConfig();
    message.min_investment = object.min_investment ?? 0;
    message.max_investment = object.max_investment ?? 0;
    message.min_roi = object.min_roi ?? 0;
    message.max_roi = object.max_roi ?? 0;
    return message;
  }
};

function createBasePrizeConfig(): PrizeConfig {
  return { prize_id: 0, icon: '', i18n_name: {}, prize_type: 0, prize_value: '', probability: {}, sequence: 0 };
}

export const PrizeConfig: MessageFns<PrizeConfig> = {
  fromJSON(object: any): PrizeConfig {
    return {
      prize_id: isSet(object.prize_id) ? globalThis.Number(object.prize_id) : 0,
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      i18n_name: isObject(object.i18n_name)
        ? Object.entries(object.i18n_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      prize_type: isSet(object.prize_type) ? prizeTypeFromJSON(object.prize_type) : 0,
      prize_value: isSet(object.prize_value) ? globalThis.String(object.prize_value) : '',
      probability: isObject(object.probability)
        ? Object.entries(object.probability).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizeConfig>, I>>(base?: I): PrizeConfig {
    return PrizeConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizeConfig>, I>>(object: I): PrizeConfig {
    const message = createBasePrizeConfig();
    message.prize_id = object.prize_id ?? 0;
    message.icon = object.icon ?? '';
    message.i18n_name = Object.entries(object.i18n_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.prize_type = object.prize_type ?? 0;
    message.prize_value = object.prize_value ?? '';
    message.probability = Object.entries(object.probability ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.sequence = object.sequence ?? 0;
    return message;
  }
};

function createBasePrizeConfig_I18nNameEntry(): PrizeConfig_I18nNameEntry {
  return { key: '', value: '' };
}

export const PrizeConfig_I18nNameEntry: MessageFns<PrizeConfig_I18nNameEntry> = {
  fromJSON(object: any): PrizeConfig_I18nNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<PrizeConfig_I18nNameEntry>, I>>(base?: I): PrizeConfig_I18nNameEntry {
    return PrizeConfig_I18nNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizeConfig_I18nNameEntry>, I>>(object: I): PrizeConfig_I18nNameEntry {
    const message = createBasePrizeConfig_I18nNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBasePrizeConfig_ProbabilityEntry(): PrizeConfig_ProbabilityEntry {
  return { key: '', value: 0 };
}

export const PrizeConfig_ProbabilityEntry: MessageFns<PrizeConfig_ProbabilityEntry> = {
  fromJSON(object: any): PrizeConfig_ProbabilityEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<PrizeConfig_ProbabilityEntry>, I>>(base?: I): PrizeConfig_ProbabilityEntry {
    return PrizeConfig_ProbabilityEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PrizeConfig_ProbabilityEntry>, I>>(object: I): PrizeConfig_ProbabilityEntry {
    const message = createBasePrizeConfig_ProbabilityEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBasePoolBlacklist(): PoolBlacklist {
  return { blacklist_id: 0, lottery_id: 0, pool: '', uid: 0, created_at: 0, created_by: '' };
}

export const PoolBlacklist: MessageFns<PoolBlacklist> = {
  fromJSON(object: any): PoolBlacklist {
    return {
      blacklist_id: isSet(object.blacklist_id) ? globalThis.Number(object.blacklist_id) : 0,
      lottery_id: isSet(object.lottery_id) ? globalThis.Number(object.lottery_id) : 0,
      pool: isSet(object.pool) ? globalThis.String(object.pool) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<PoolBlacklist>, I>>(base?: I): PoolBlacklist {
    return PoolBlacklist.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PoolBlacklist>, I>>(object: I): PoolBlacklist {
    const message = createBasePoolBlacklist();
    message.blacklist_id = object.blacklist_id ?? 0;
    message.lottery_id = object.lottery_id ?? 0;
    message.pool = object.pool ?? '';
    message.uid = object.uid ?? 0;
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    return message;
  }
};

/**
 * 抽奖配置
 * smicro:spath=gitit.cc/social/components-service/social-revenue/lottery/handlermgr
 */
export type LotteryMgrDefinition = typeof LotteryMgrDefinition;
export const LotteryMgrDefinition = {
  name: 'LotteryMgr',
  fullName: 'mgr.lottery.LotteryMgr',
  methods: {
    /** 创建抽奖配置 */
    createLottery: {
      name: 'CreateLottery',
      requestType: CreateLotteryReq,
      requestStream: false,
      responseType: CreateLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 更新抽奖配置 */
    updateLottery: {
      name: 'UpdateLottery',
      requestType: UpdateLotteryReq,
      requestStream: false,
      responseType: UpdateLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 删除抽奖配置 */
    deleteLottery: {
      name: 'DeleteLottery',
      requestType: DeleteLotteryReq,
      requestStream: false,
      responseType: DeleteLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索抽奖配置 */
    searchLottery: {
      name: 'SearchLottery',
      requestType: SearchLotteryReq,
      requestStream: false,
      responseType: SearchLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 复制抽奖配置 */
    copyLottery: {
      name: 'CopyLottery',
      requestType: CopyLotteryReq,
      requestStream: false,
      responseType: CopyLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 查询未绑定抽奖配置 */
    listUnboundLottery: {
      name: 'ListUnboundLottery',
      requestType: ListUnboundLotteryReq,
      requestStream: false,
      responseType: ListUnboundLotteryRsp,
      responseStream: false,
      options: {}
    },
    /** 获取奖励列表 */
    listPrize: {
      name: 'ListPrize',
      requestType: ListPrizeReq,
      requestStream: false,
      responseType: ListPrizeRsp,
      responseStream: false,
      options: {}
    },
    /** 保存奖励列表 */
    savePrize: {
      name: 'SavePrize',
      requestType: SavePrizeReq,
      requestStream: false,
      responseType: SavePrizeRsp,
      responseStream: false,
      options: {}
    },
    /** 添加黑名单 */
    addBlacklist: {
      name: 'AddBlacklist',
      requestType: AddBlacklistReq,
      requestStream: false,
      responseType: AddBlacklistRsp,
      responseStream: false,
      options: {}
    },
    /** 删除黑名单 */
    deleteBlacklist: {
      name: 'DeleteBlacklist',
      requestType: DeleteBlacklistReq,
      requestStream: false,
      responseType: DeleteBlacklistRsp,
      responseStream: false,
      options: {}
    },
    /** 搜索黑名单 */
    searchBlacklist: {
      name: 'SearchBlacklist',
      requestType: SearchBlacklistReq,
      requestStream: false,
      responseType: SearchBlacklistRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
