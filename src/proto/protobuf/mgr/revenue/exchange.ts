// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/exchange.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { CurrencyType, currencyTypeFromJSON, UserInfo } from '../../api/revenue/common';

export const protobufPackage = 'mgr.pbrevenue';

export enum ExchangeConfigStatus {
  /** EXCHANGE_CONFIG_STATUS_NORMAL - 正常 */
  EXCHANGE_CONFIG_STATUS_NORMAL = 0,
  /** EXCHANGE_CONFIG_STATUS_OFFLINE - 下线 */
  EXCHANGE_CONFIG_STATUS_OFFLINE = 1,
  /** EXCHANGE_CONFIG_STATUS_DELETED - 删除（删除后记录作废不可再修改） */
  EXCHANGE_CONFIG_STATUS_DELETED = 2,
  UNRECOGNIZED = -1
}

export function exchangeConfigStatusFromJSON(object: any): ExchangeConfigStatus {
  switch (object) {
    case 0:
    case 'EXCHANGE_CONFIG_STATUS_NORMAL':
      return ExchangeConfigStatus.EXCHANGE_CONFIG_STATUS_NORMAL;
    case 1:
    case 'EXCHANGE_CONFIG_STATUS_OFFLINE':
      return ExchangeConfigStatus.EXCHANGE_CONFIG_STATUS_OFFLINE;
    case 2:
    case 'EXCHANGE_CONFIG_STATUS_DELETED':
      return ExchangeConfigStatus.EXCHANGE_CONFIG_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ExchangeConfigStatus.UNRECOGNIZED;
  }
}

export interface ListExchangeRateReq {
  page: Page | undefined;
  source_currency_types: CurrencyType[];
  target_currency_types: CurrencyType[];
}

export interface ListExchangeRateRsp {
  page: Page | undefined;
  exchange_rates: ExchangeRate[];
}

export interface GetExchangeRateReq {
  id: number;
}

export interface GetExchangeRateRsp {
  exchange_rate: ExchangeRate | undefined;
}

export interface CreateExchangeRateReq {
  exchange_rate: ExchangeRate | undefined;
}

export interface CreateExchangeRateRsp {}

export interface UpdateExchangeRateReq {
  exchange_rate: ExchangeRate | undefined;
}

export interface UpdateExchangeRateRsp {}

export interface DeleteExchangeRateReq {
  id: number;
}

export interface DeleteExchangeRateRsp {}

export interface UpdateStatusRateReq {
  ids: number[];
  status: ExchangeConfigStatus;
}

export interface UpdateStatusRateRsp {}

export interface ListExchangeOptionReq {
  page: Page | undefined;
  source_currency_types: CurrencyType[];
  target_currency_types: CurrencyType[];
}

export interface ListExchangeOptionRsp {
  page: Page | undefined;
  exchange_option: ExchangeOption[];
}

export interface GetExchangeOptionReq {
  id: number;
}

export interface GetExchangeOptionRsp {
  exchange_option: ExchangeOption | undefined;
}

export interface CreateExchangeOptionReq {
  exchange_option: ExchangeOption | undefined;
}

export interface CreateExchangeOptionRsp {}

export interface UpdateExchangeOptionReq {
  exchange_option: ExchangeOption | undefined;
}

export interface UpdateExchangeOptionRsp {}

export interface DeleteExchangeOptionReq {
  id: number;
}

export interface DeleteExchangeOptionRsp {}

/** 兑换选项 */
export interface ExchangeOption {
  /** 兑换汇率ID */
  exchange_option_id: number;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 可以获得多少数量货币 */
  target_quantity: number;
  /** 序号 */
  sequence: number;
  /** 更新时间 */
  utime: number;
  /** 创建时间 */
  ctime: number;
  /** 备注 */
  remark: string;
  /** 状态 */
  status: ExchangeConfigStatus;
}

/** 兑换汇率 */
export interface ExchangeRate {
  /** 兑换汇率ID */
  exchange_rate_id: number;
  /** 消耗币种 */
  source_currency_type: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency_type: CurrencyType;
  /** 可以获得多少数量货币 */
  target_quantity: number;
  /** 可以获得多少数量货币 */
  rate_key: string;
  /** 序号 */
  sequence: number;
  /** 更新时间 */
  utime: number;
  /** 创建时间 */
  ctime: number;
  /** 备注 */
  remark: string;
  /** 状态 */
  status: ExchangeConfigStatus;
}

export interface UpdateStatusOptionReq {
  exchange_option_ids: number[];
  status: ExchangeConfigStatus;
}

export interface UpdateStatusOptionRsp {}

/** 查询记录列表请求 */
export interface ListExchangeRecordReq {
  page: Page | undefined;
  query_args: QueryExchangeRecordArgs | undefined;
}

/** 查询筛选条件 */
export interface QueryExchangeRecordArgs {
  /** 兑换发起者 */
  uid: string;
  /** 兑换交易对象 */
  target_uid: string;
  /** 消耗币种 */
  source_currency: CurrencyType;
  /** 获得币种 */
  target_currency: CurrencyType;
  /** 开始时间 */
  start_time: number;
  /** 结束时间 */
  end_time: number;
}

/** 查询记录列表响应 */
export interface ListExchangeRecordRsp {
  page: Page | undefined;
  records: ExchangeRecord[];
}

/** 兑换记录 */
export interface ExchangeRecord {
  /** 兑换记录ID */
  id: number;
  /** 兑换发起者 */
  uid: number;
  /** 兑换交易对象 */
  target_uid: number;
  /** 消耗币种 */
  source_currency: CurrencyType;
  /** 消耗多少数量货币 */
  source_quantity: number;
  /** 获得币种 */
  target_currency: CurrencyType;
  /** 获得多少数量货币 */
  target_quantity: number;
  /** 秒级时间戳 */
  create_at: number;
  /** 兑换发起者信息 */
  user: UserInfo | undefined;
  /** 兑换交易对象信息 */
  target_user: UserInfo | undefined;
}

/** 查询合计请求 */
export interface GetExchangeRecordSumReq {
  query_args: QueryExchangeRecordArgs | undefined;
}

/** 查询合计响应 */
export interface GetExchangeRecordSumRsp {
  /** 消耗数量汇总 */
  source_quantity: number;
  /** 获得数量汇总 */
  target_quantity: number;
}

function createBaseListExchangeRateReq(): ListExchangeRateReq {
  return { page: undefined, source_currency_types: [], target_currency_types: [] };
}

export const ListExchangeRateReq: MessageFns<ListExchangeRateReq> = {
  fromJSON(object: any): ListExchangeRateReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      source_currency_types: globalThis.Array.isArray(object?.source_currency_types)
        ? object.source_currency_types.map((e: any) => currencyTypeFromJSON(e))
        : [],
      target_currency_types: globalThis.Array.isArray(object?.target_currency_types)
        ? object.target_currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeRateReq>, I>>(base?: I): ListExchangeRateReq {
    return ListExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeRateReq>, I>>(object: I): ListExchangeRateReq {
    const message = createBaseListExchangeRateReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.source_currency_types = object.source_currency_types?.map(e => e) || [];
    message.target_currency_types = object.target_currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseListExchangeRateRsp(): ListExchangeRateRsp {
  return { page: undefined, exchange_rates: [] };
}

export const ListExchangeRateRsp: MessageFns<ListExchangeRateRsp> = {
  fromJSON(object: any): ListExchangeRateRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      exchange_rates: globalThis.Array.isArray(object?.exchange_rates)
        ? object.exchange_rates.map((e: any) => ExchangeRate.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeRateRsp>, I>>(base?: I): ListExchangeRateRsp {
    return ListExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeRateRsp>, I>>(object: I): ListExchangeRateRsp {
    const message = createBaseListExchangeRateRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.exchange_rates = object.exchange_rates?.map(e => ExchangeRate.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetExchangeRateReq(): GetExchangeRateReq {
  return { id: 0 };
}

export const GetExchangeRateReq: MessageFns<GetExchangeRateReq> = {
  fromJSON(object: any): GetExchangeRateReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetExchangeRateReq>, I>>(base?: I): GetExchangeRateReq {
    return GetExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRateReq>, I>>(object: I): GetExchangeRateReq {
    const message = createBaseGetExchangeRateReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetExchangeRateRsp(): GetExchangeRateRsp {
  return { exchange_rate: undefined };
}

export const GetExchangeRateRsp: MessageFns<GetExchangeRateRsp> = {
  fromJSON(object: any): GetExchangeRateRsp {
    return { exchange_rate: isSet(object.exchange_rate) ? ExchangeRate.fromJSON(object.exchange_rate) : undefined };
  },

  create<I extends Exact<DeepPartial<GetExchangeRateRsp>, I>>(base?: I): GetExchangeRateRsp {
    return GetExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRateRsp>, I>>(object: I): GetExchangeRateRsp {
    const message = createBaseGetExchangeRateRsp();
    message.exchange_rate =
      object.exchange_rate !== undefined && object.exchange_rate !== null
        ? ExchangeRate.fromPartial(object.exchange_rate)
        : undefined;
    return message;
  }
};

function createBaseCreateExchangeRateReq(): CreateExchangeRateReq {
  return { exchange_rate: undefined };
}

export const CreateExchangeRateReq: MessageFns<CreateExchangeRateReq> = {
  fromJSON(object: any): CreateExchangeRateReq {
    return { exchange_rate: isSet(object.exchange_rate) ? ExchangeRate.fromJSON(object.exchange_rate) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateExchangeRateReq>, I>>(base?: I): CreateExchangeRateReq {
    return CreateExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateExchangeRateReq>, I>>(object: I): CreateExchangeRateReq {
    const message = createBaseCreateExchangeRateReq();
    message.exchange_rate =
      object.exchange_rate !== undefined && object.exchange_rate !== null
        ? ExchangeRate.fromPartial(object.exchange_rate)
        : undefined;
    return message;
  }
};

function createBaseCreateExchangeRateRsp(): CreateExchangeRateRsp {
  return {};
}

export const CreateExchangeRateRsp: MessageFns<CreateExchangeRateRsp> = {
  fromJSON(_: any): CreateExchangeRateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateExchangeRateRsp>, I>>(base?: I): CreateExchangeRateRsp {
    return CreateExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateExchangeRateRsp>, I>>(_: I): CreateExchangeRateRsp {
    const message = createBaseCreateExchangeRateRsp();
    return message;
  }
};

function createBaseUpdateExchangeRateReq(): UpdateExchangeRateReq {
  return { exchange_rate: undefined };
}

export const UpdateExchangeRateReq: MessageFns<UpdateExchangeRateReq> = {
  fromJSON(object: any): UpdateExchangeRateReq {
    return { exchange_rate: isSet(object.exchange_rate) ? ExchangeRate.fromJSON(object.exchange_rate) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateExchangeRateReq>, I>>(base?: I): UpdateExchangeRateReq {
    return UpdateExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateExchangeRateReq>, I>>(object: I): UpdateExchangeRateReq {
    const message = createBaseUpdateExchangeRateReq();
    message.exchange_rate =
      object.exchange_rate !== undefined && object.exchange_rate !== null
        ? ExchangeRate.fromPartial(object.exchange_rate)
        : undefined;
    return message;
  }
};

function createBaseUpdateExchangeRateRsp(): UpdateExchangeRateRsp {
  return {};
}

export const UpdateExchangeRateRsp: MessageFns<UpdateExchangeRateRsp> = {
  fromJSON(_: any): UpdateExchangeRateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateExchangeRateRsp>, I>>(base?: I): UpdateExchangeRateRsp {
    return UpdateExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateExchangeRateRsp>, I>>(_: I): UpdateExchangeRateRsp {
    const message = createBaseUpdateExchangeRateRsp();
    return message;
  }
};

function createBaseDeleteExchangeRateReq(): DeleteExchangeRateReq {
  return { id: 0 };
}

export const DeleteExchangeRateReq: MessageFns<DeleteExchangeRateReq> = {
  fromJSON(object: any): DeleteExchangeRateReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteExchangeRateReq>, I>>(base?: I): DeleteExchangeRateReq {
    return DeleteExchangeRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteExchangeRateReq>, I>>(object: I): DeleteExchangeRateReq {
    const message = createBaseDeleteExchangeRateReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteExchangeRateRsp(): DeleteExchangeRateRsp {
  return {};
}

export const DeleteExchangeRateRsp: MessageFns<DeleteExchangeRateRsp> = {
  fromJSON(_: any): DeleteExchangeRateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteExchangeRateRsp>, I>>(base?: I): DeleteExchangeRateRsp {
    return DeleteExchangeRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteExchangeRateRsp>, I>>(_: I): DeleteExchangeRateRsp {
    const message = createBaseDeleteExchangeRateRsp();
    return message;
  }
};

function createBaseUpdateStatusRateReq(): UpdateStatusRateReq {
  return { ids: [], status: 0 };
}

export const UpdateStatusRateReq: MessageFns<UpdateStatusRateReq> = {
  fromJSON(object: any): UpdateStatusRateReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      status: isSet(object.status) ? exchangeConfigStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateStatusRateReq>, I>>(base?: I): UpdateStatusRateReq {
    return UpdateStatusRateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusRateReq>, I>>(object: I): UpdateStatusRateReq {
    const message = createBaseUpdateStatusRateReq();
    message.ids = object.ids?.map(e => e) || [];
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseUpdateStatusRateRsp(): UpdateStatusRateRsp {
  return {};
}

export const UpdateStatusRateRsp: MessageFns<UpdateStatusRateRsp> = {
  fromJSON(_: any): UpdateStatusRateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateStatusRateRsp>, I>>(base?: I): UpdateStatusRateRsp {
    return UpdateStatusRateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusRateRsp>, I>>(_: I): UpdateStatusRateRsp {
    const message = createBaseUpdateStatusRateRsp();
    return message;
  }
};

function createBaseListExchangeOptionReq(): ListExchangeOptionReq {
  return { page: undefined, source_currency_types: [], target_currency_types: [] };
}

export const ListExchangeOptionReq: MessageFns<ListExchangeOptionReq> = {
  fromJSON(object: any): ListExchangeOptionReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      source_currency_types: globalThis.Array.isArray(object?.source_currency_types)
        ? object.source_currency_types.map((e: any) => currencyTypeFromJSON(e))
        : [],
      target_currency_types: globalThis.Array.isArray(object?.target_currency_types)
        ? object.target_currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeOptionReq>, I>>(base?: I): ListExchangeOptionReq {
    return ListExchangeOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeOptionReq>, I>>(object: I): ListExchangeOptionReq {
    const message = createBaseListExchangeOptionReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.source_currency_types = object.source_currency_types?.map(e => e) || [];
    message.target_currency_types = object.target_currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseListExchangeOptionRsp(): ListExchangeOptionRsp {
  return { page: undefined, exchange_option: [] };
}

export const ListExchangeOptionRsp: MessageFns<ListExchangeOptionRsp> = {
  fromJSON(object: any): ListExchangeOptionRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      exchange_option: globalThis.Array.isArray(object?.exchange_option)
        ? object.exchange_option.map((e: any) => ExchangeOption.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeOptionRsp>, I>>(base?: I): ListExchangeOptionRsp {
    return ListExchangeOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeOptionRsp>, I>>(object: I): ListExchangeOptionRsp {
    const message = createBaseListExchangeOptionRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.exchange_option = object.exchange_option?.map(e => ExchangeOption.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetExchangeOptionReq(): GetExchangeOptionReq {
  return { id: 0 };
}

export const GetExchangeOptionReq: MessageFns<GetExchangeOptionReq> = {
  fromJSON(object: any): GetExchangeOptionReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetExchangeOptionReq>, I>>(base?: I): GetExchangeOptionReq {
    return GetExchangeOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeOptionReq>, I>>(object: I): GetExchangeOptionReq {
    const message = createBaseGetExchangeOptionReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetExchangeOptionRsp(): GetExchangeOptionRsp {
  return { exchange_option: undefined };
}

export const GetExchangeOptionRsp: MessageFns<GetExchangeOptionRsp> = {
  fromJSON(object: any): GetExchangeOptionRsp {
    return {
      exchange_option: isSet(object.exchange_option) ? ExchangeOption.fromJSON(object.exchange_option) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GetExchangeOptionRsp>, I>>(base?: I): GetExchangeOptionRsp {
    return GetExchangeOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeOptionRsp>, I>>(object: I): GetExchangeOptionRsp {
    const message = createBaseGetExchangeOptionRsp();
    message.exchange_option =
      object.exchange_option !== undefined && object.exchange_option !== null
        ? ExchangeOption.fromPartial(object.exchange_option)
        : undefined;
    return message;
  }
};

function createBaseCreateExchangeOptionReq(): CreateExchangeOptionReq {
  return { exchange_option: undefined };
}

export const CreateExchangeOptionReq: MessageFns<CreateExchangeOptionReq> = {
  fromJSON(object: any): CreateExchangeOptionReq {
    return {
      exchange_option: isSet(object.exchange_option) ? ExchangeOption.fromJSON(object.exchange_option) : undefined
    };
  },

  create<I extends Exact<DeepPartial<CreateExchangeOptionReq>, I>>(base?: I): CreateExchangeOptionReq {
    return CreateExchangeOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateExchangeOptionReq>, I>>(object: I): CreateExchangeOptionReq {
    const message = createBaseCreateExchangeOptionReq();
    message.exchange_option =
      object.exchange_option !== undefined && object.exchange_option !== null
        ? ExchangeOption.fromPartial(object.exchange_option)
        : undefined;
    return message;
  }
};

function createBaseCreateExchangeOptionRsp(): CreateExchangeOptionRsp {
  return {};
}

export const CreateExchangeOptionRsp: MessageFns<CreateExchangeOptionRsp> = {
  fromJSON(_: any): CreateExchangeOptionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateExchangeOptionRsp>, I>>(base?: I): CreateExchangeOptionRsp {
    return CreateExchangeOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateExchangeOptionRsp>, I>>(_: I): CreateExchangeOptionRsp {
    const message = createBaseCreateExchangeOptionRsp();
    return message;
  }
};

function createBaseUpdateExchangeOptionReq(): UpdateExchangeOptionReq {
  return { exchange_option: undefined };
}

export const UpdateExchangeOptionReq: MessageFns<UpdateExchangeOptionReq> = {
  fromJSON(object: any): UpdateExchangeOptionReq {
    return {
      exchange_option: isSet(object.exchange_option) ? ExchangeOption.fromJSON(object.exchange_option) : undefined
    };
  },

  create<I extends Exact<DeepPartial<UpdateExchangeOptionReq>, I>>(base?: I): UpdateExchangeOptionReq {
    return UpdateExchangeOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateExchangeOptionReq>, I>>(object: I): UpdateExchangeOptionReq {
    const message = createBaseUpdateExchangeOptionReq();
    message.exchange_option =
      object.exchange_option !== undefined && object.exchange_option !== null
        ? ExchangeOption.fromPartial(object.exchange_option)
        : undefined;
    return message;
  }
};

function createBaseUpdateExchangeOptionRsp(): UpdateExchangeOptionRsp {
  return {};
}

export const UpdateExchangeOptionRsp: MessageFns<UpdateExchangeOptionRsp> = {
  fromJSON(_: any): UpdateExchangeOptionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateExchangeOptionRsp>, I>>(base?: I): UpdateExchangeOptionRsp {
    return UpdateExchangeOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateExchangeOptionRsp>, I>>(_: I): UpdateExchangeOptionRsp {
    const message = createBaseUpdateExchangeOptionRsp();
    return message;
  }
};

function createBaseDeleteExchangeOptionReq(): DeleteExchangeOptionReq {
  return { id: 0 };
}

export const DeleteExchangeOptionReq: MessageFns<DeleteExchangeOptionReq> = {
  fromJSON(object: any): DeleteExchangeOptionReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteExchangeOptionReq>, I>>(base?: I): DeleteExchangeOptionReq {
    return DeleteExchangeOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteExchangeOptionReq>, I>>(object: I): DeleteExchangeOptionReq {
    const message = createBaseDeleteExchangeOptionReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteExchangeOptionRsp(): DeleteExchangeOptionRsp {
  return {};
}

export const DeleteExchangeOptionRsp: MessageFns<DeleteExchangeOptionRsp> = {
  fromJSON(_: any): DeleteExchangeOptionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteExchangeOptionRsp>, I>>(base?: I): DeleteExchangeOptionRsp {
    return DeleteExchangeOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteExchangeOptionRsp>, I>>(_: I): DeleteExchangeOptionRsp {
    const message = createBaseDeleteExchangeOptionRsp();
    return message;
  }
};

function createBaseExchangeOption(): ExchangeOption {
  return {
    exchange_option_id: 0,
    source_currency_type: 0,
    source_quantity: 0,
    target_currency_type: 0,
    target_quantity: 0,
    sequence: 0,
    utime: 0,
    ctime: 0,
    remark: '',
    status: 0
  };
}

export const ExchangeOption: MessageFns<ExchangeOption> = {
  fromJSON(object: any): ExchangeOption {
    return {
      exchange_option_id: isSet(object.exchange_option_id) ? globalThis.Number(object.exchange_option_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? exchangeConfigStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeOption>, I>>(base?: I): ExchangeOption {
    return ExchangeOption.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeOption>, I>>(object: I): ExchangeOption {
    const message = createBaseExchangeOption();
    message.exchange_option_id = object.exchange_option_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.sequence = object.sequence ?? 0;
    message.utime = object.utime ?? 0;
    message.ctime = object.ctime ?? 0;
    message.remark = object.remark ?? '';
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseExchangeRate(): ExchangeRate {
  return {
    exchange_rate_id: 0,
    source_currency_type: 0,
    source_quantity: 0,
    target_currency_type: 0,
    target_quantity: 0,
    rate_key: '',
    sequence: 0,
    utime: 0,
    ctime: 0,
    remark: '',
    status: 0
  };
}

export const ExchangeRate: MessageFns<ExchangeRate> = {
  fromJSON(object: any): ExchangeRate {
    return {
      exchange_rate_id: isSet(object.exchange_rate_id) ? globalThis.Number(object.exchange_rate_id) : 0,
      source_currency_type: isSet(object.source_currency_type) ? currencyTypeFromJSON(object.source_currency_type) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency_type: isSet(object.target_currency_type) ? currencyTypeFromJSON(object.target_currency_type) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      rate_key: isSet(object.rate_key) ? globalThis.String(object.rate_key) : '',
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? exchangeConfigStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRate>, I>>(base?: I): ExchangeRate {
    return ExchangeRate.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRate>, I>>(object: I): ExchangeRate {
    const message = createBaseExchangeRate();
    message.exchange_rate_id = object.exchange_rate_id ?? 0;
    message.source_currency_type = object.source_currency_type ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency_type = object.target_currency_type ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.rate_key = object.rate_key ?? '';
    message.sequence = object.sequence ?? 0;
    message.utime = object.utime ?? 0;
    message.ctime = object.ctime ?? 0;
    message.remark = object.remark ?? '';
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseUpdateStatusOptionReq(): UpdateStatusOptionReq {
  return { exchange_option_ids: [], status: 0 };
}

export const UpdateStatusOptionReq: MessageFns<UpdateStatusOptionReq> = {
  fromJSON(object: any): UpdateStatusOptionReq {
    return {
      exchange_option_ids: globalThis.Array.isArray(object?.exchange_option_ids)
        ? object.exchange_option_ids.map((e: any) => globalThis.Number(e))
        : [],
      status: isSet(object.status) ? exchangeConfigStatusFromJSON(object.status) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateStatusOptionReq>, I>>(base?: I): UpdateStatusOptionReq {
    return UpdateStatusOptionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusOptionReq>, I>>(object: I): UpdateStatusOptionReq {
    const message = createBaseUpdateStatusOptionReq();
    message.exchange_option_ids = object.exchange_option_ids?.map(e => e) || [];
    message.status = object.status ?? 0;
    return message;
  }
};

function createBaseUpdateStatusOptionRsp(): UpdateStatusOptionRsp {
  return {};
}

export const UpdateStatusOptionRsp: MessageFns<UpdateStatusOptionRsp> = {
  fromJSON(_: any): UpdateStatusOptionRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateStatusOptionRsp>, I>>(base?: I): UpdateStatusOptionRsp {
    return UpdateStatusOptionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateStatusOptionRsp>, I>>(_: I): UpdateStatusOptionRsp {
    const message = createBaseUpdateStatusOptionRsp();
    return message;
  }
};

function createBaseListExchangeRecordReq(): ListExchangeRecordReq {
  return { page: undefined, query_args: undefined };
}

export const ListExchangeRecordReq: MessageFns<ListExchangeRecordReq> = {
  fromJSON(object: any): ListExchangeRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      query_args: isSet(object.query_args) ? QueryExchangeRecordArgs.fromJSON(object.query_args) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeRecordReq>, I>>(base?: I): ListExchangeRecordReq {
    return ListExchangeRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeRecordReq>, I>>(object: I): ListExchangeRecordReq {
    const message = createBaseListExchangeRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.query_args =
      object.query_args !== undefined && object.query_args !== null
        ? QueryExchangeRecordArgs.fromPartial(object.query_args)
        : undefined;
    return message;
  }
};

function createBaseQueryExchangeRecordArgs(): QueryExchangeRecordArgs {
  return { uid: '', target_uid: '', source_currency: 0, target_currency: 0, start_time: 0, end_time: 0 };
}

export const QueryExchangeRecordArgs: MessageFns<QueryExchangeRecordArgs> = {
  fromJSON(object: any): QueryExchangeRecordArgs {
    return {
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      target_uid: isSet(object.target_uid) ? globalThis.String(object.target_uid) : '',
      source_currency: isSet(object.source_currency) ? currencyTypeFromJSON(object.source_currency) : 0,
      target_currency: isSet(object.target_currency) ? currencyTypeFromJSON(object.target_currency) : 0,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<QueryExchangeRecordArgs>, I>>(base?: I): QueryExchangeRecordArgs {
    return QueryExchangeRecordArgs.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryExchangeRecordArgs>, I>>(object: I): QueryExchangeRecordArgs {
    const message = createBaseQueryExchangeRecordArgs();
    message.uid = object.uid ?? '';
    message.target_uid = object.target_uid ?? '';
    message.source_currency = object.source_currency ?? 0;
    message.target_currency = object.target_currency ?? 0;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBaseListExchangeRecordRsp(): ListExchangeRecordRsp {
  return { page: undefined, records: [] };
}

export const ListExchangeRecordRsp: MessageFns<ListExchangeRecordRsp> = {
  fromJSON(object: any): ListExchangeRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      records: globalThis.Array.isArray(object?.records)
        ? object.records.map((e: any) => ExchangeRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListExchangeRecordRsp>, I>>(base?: I): ListExchangeRecordRsp {
    return ListExchangeRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListExchangeRecordRsp>, I>>(object: I): ListExchangeRecordRsp {
    const message = createBaseListExchangeRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.records = object.records?.map(e => ExchangeRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseExchangeRecord(): ExchangeRecord {
  return {
    id: 0,
    uid: 0,
    target_uid: 0,
    source_currency: 0,
    source_quantity: 0,
    target_currency: 0,
    target_quantity: 0,
    create_at: 0,
    user: undefined,
    target_user: undefined
  };
}

export const ExchangeRecord: MessageFns<ExchangeRecord> = {
  fromJSON(object: any): ExchangeRecord {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      target_uid: isSet(object.target_uid) ? globalThis.Number(object.target_uid) : 0,
      source_currency: isSet(object.source_currency) ? currencyTypeFromJSON(object.source_currency) : 0,
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_currency: isSet(object.target_currency) ? currencyTypeFromJSON(object.target_currency) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      user: isSet(object.user) ? UserInfo.fromJSON(object.user) : undefined,
      target_user: isSet(object.target_user) ? UserInfo.fromJSON(object.target_user) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ExchangeRecord>, I>>(base?: I): ExchangeRecord {
    return ExchangeRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ExchangeRecord>, I>>(object: I): ExchangeRecord {
    const message = createBaseExchangeRecord();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.target_uid = object.target_uid ?? 0;
    message.source_currency = object.source_currency ?? 0;
    message.source_quantity = object.source_quantity ?? 0;
    message.target_currency = object.target_currency ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    message.create_at = object.create_at ?? 0;
    message.user = object.user !== undefined && object.user !== null ? UserInfo.fromPartial(object.user) : undefined;
    message.target_user =
      object.target_user !== undefined && object.target_user !== null
        ? UserInfo.fromPartial(object.target_user)
        : undefined;
    return message;
  }
};

function createBaseGetExchangeRecordSumReq(): GetExchangeRecordSumReq {
  return { query_args: undefined };
}

export const GetExchangeRecordSumReq: MessageFns<GetExchangeRecordSumReq> = {
  fromJSON(object: any): GetExchangeRecordSumReq {
    return { query_args: isSet(object.query_args) ? QueryExchangeRecordArgs.fromJSON(object.query_args) : undefined };
  },

  create<I extends Exact<DeepPartial<GetExchangeRecordSumReq>, I>>(base?: I): GetExchangeRecordSumReq {
    return GetExchangeRecordSumReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRecordSumReq>, I>>(object: I): GetExchangeRecordSumReq {
    const message = createBaseGetExchangeRecordSumReq();
    message.query_args =
      object.query_args !== undefined && object.query_args !== null
        ? QueryExchangeRecordArgs.fromPartial(object.query_args)
        : undefined;
    return message;
  }
};

function createBaseGetExchangeRecordSumRsp(): GetExchangeRecordSumRsp {
  return { source_quantity: 0, target_quantity: 0 };
}

export const GetExchangeRecordSumRsp: MessageFns<GetExchangeRecordSumRsp> = {
  fromJSON(object: any): GetExchangeRecordSumRsp {
    return {
      source_quantity: isSet(object.source_quantity) ? globalThis.Number(object.source_quantity) : 0,
      target_quantity: isSet(object.target_quantity) ? globalThis.Number(object.target_quantity) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetExchangeRecordSumRsp>, I>>(base?: I): GetExchangeRecordSumRsp {
    return GetExchangeRecordSumRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetExchangeRecordSumRsp>, I>>(object: I): GetExchangeRecordSumRsp {
    const message = createBaseGetExchangeRecordSumRsp();
    message.source_quantity = object.source_quantity ?? 0;
    message.target_quantity = object.target_quantity ?? 0;
    return message;
  }
};

/**
 * 兑换服务
 * smicro:spath=gitit.cc/social/components-service/social-revenue/exchangemgr/exchange.go
 */
export type ExchangeMgrDefinition = typeof ExchangeMgrDefinition;
export const ExchangeMgrDefinition = {
  name: 'ExchangeMgr',
  fullName: 'mgr.pbrevenue.ExchangeMgr',
  methods: {
    /** 获取兑换配置 */
    listExchangeRate: {
      name: 'ListExchangeRate',
      requestType: ListExchangeRateReq,
      requestStream: false,
      responseType: ListExchangeRateRsp,
      responseStream: false,
      options: {}
    },
    /** 获取兑换配置 */
    getExchangeRate: {
      name: 'GetExchangeRate',
      requestType: GetExchangeRateReq,
      requestStream: false,
      responseType: GetExchangeRateRsp,
      responseStream: false,
      options: {}
    },
    /** 新增兑换配置 */
    createExchangeRate: {
      name: 'CreateExchangeRate',
      requestType: CreateExchangeRateReq,
      requestStream: false,
      responseType: CreateExchangeRateRsp,
      responseStream: false,
      options: {}
    },
    /** 更新兑换配置 */
    updateExchangeRate: {
      name: 'UpdateExchangeRate',
      requestType: UpdateExchangeRateReq,
      requestStream: false,
      responseType: UpdateExchangeRateRsp,
      responseStream: false,
      options: {}
    },
    /** 删除兑换配置 */
    deleteExchangeRate: {
      name: 'DeleteExchangeRate',
      requestType: DeleteExchangeRateReq,
      requestStream: false,
      responseType: DeleteExchangeRateRsp,
      responseStream: false,
      options: {}
    },
    /** 更新配置状态 */
    updateStatusRate: {
      name: 'UpdateStatusRate',
      requestType: UpdateStatusRateReq,
      requestStream: false,
      responseType: UpdateStatusRateRsp,
      responseStream: false,
      options: {}
    },
    /** 获取兑换选项 */
    listExchangeOption: {
      name: 'ListExchangeOption',
      requestType: ListExchangeOptionReq,
      requestStream: false,
      responseType: ListExchangeOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 获取兑换配置 */
    getExchangeOption: {
      name: 'GetExchangeOption',
      requestType: GetExchangeOptionReq,
      requestStream: false,
      responseType: GetExchangeOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 新增兑换配置 */
    createExchangeOption: {
      name: 'CreateExchangeOption',
      requestType: CreateExchangeOptionReq,
      requestStream: false,
      responseType: CreateExchangeOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 更新兑换配置 */
    updateExchangeOption: {
      name: 'UpdateExchangeOption',
      requestType: UpdateExchangeOptionReq,
      requestStream: false,
      responseType: UpdateExchangeOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 删除兑换配置 */
    deleteExchangeOption: {
      name: 'DeleteExchangeOption',
      requestType: DeleteExchangeOptionReq,
      requestStream: false,
      responseType: DeleteExchangeOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 更新配置状态 */
    updateStatusOption: {
      name: 'UpdateStatusOption',
      requestType: UpdateStatusOptionReq,
      requestStream: false,
      responseType: UpdateStatusOptionRsp,
      responseStream: false,
      options: {}
    },
    /** 查询兑换记录 */
    listExchangeRecord: {
      name: 'ListExchangeRecord',
      requestType: ListExchangeRecordReq,
      requestStream: false,
      responseType: ListExchangeRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 查询兑换合计 */
    getExchangeRecordSum: {
      name: 'GetExchangeRecordSum',
      requestType: GetExchangeRecordSumReq,
      requestStream: false,
      responseType: GetExchangeRecordSumRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
