// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/wallet.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { Balance, CurrencyType, currencyTypeFromJSON } from '../../api/revenue/common';
import { TransactionType, transactionTypeFromJSON } from '../../api/revenue/wallet';

export const protobufPackage = 'mgr.pbrevenue';

/** 操作类型 */
export enum BalanceOperateKind {
  /** BALANCE_OPERATE_KIND_NONE - 无意义 */
  BALANCE_OPERATE_KIND_NONE = 0,
  /** BALANCE_OPERATE_KIND_GRANT - 发放 */
  BALANCE_OPERATE_KIND_GRANT = 1,
  /** BALANCE_OPERATE_KIND_DEDUCT - 扣减 */
  BALANCE_OPERATE_KIND_DEDUCT = 2,
  /** BALANCE_OPERATE_KIND_FREEZE - 冻结 */
  BALANCE_OPERATE_KIND_FREEZE = 3,
  /** BALANCE_OPERATE_KIND_UNFREEZE - 解冻 */
  BALANCE_OPERATE_KIND_UNFREEZE = 4,
  UNRECOGNIZED = -1
}

export function balanceOperateKindFromJSON(object: any): BalanceOperateKind {
  switch (object) {
    case 0:
    case 'BALANCE_OPERATE_KIND_NONE':
      return BalanceOperateKind.BALANCE_OPERATE_KIND_NONE;
    case 1:
    case 'BALANCE_OPERATE_KIND_GRANT':
      return BalanceOperateKind.BALANCE_OPERATE_KIND_GRANT;
    case 2:
    case 'BALANCE_OPERATE_KIND_DEDUCT':
      return BalanceOperateKind.BALANCE_OPERATE_KIND_DEDUCT;
    case 3:
    case 'BALANCE_OPERATE_KIND_FREEZE':
      return BalanceOperateKind.BALANCE_OPERATE_KIND_FREEZE;
    case 4:
    case 'BALANCE_OPERATE_KIND_UNFREEZE':
      return BalanceOperateKind.BALANCE_OPERATE_KIND_UNFREEZE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return BalanceOperateKind.UNRECOGNIZED;
  }
}

/** 审批状态 */
export enum AuditStatus {
  /** AUDIT_STATUS_NONE - 无意义 */
  AUDIT_STATUS_NONE = 0,
  /** AUDIT_STATUS_PENDING - 审批中 */
  AUDIT_STATUS_PENDING = 1,
  /** AUDIT_STATUS_APPROVED - 已通过 */
  AUDIT_STATUS_APPROVED = 2,
  /** AUDIT_STATUS_REJECTED - 已驳回 */
  AUDIT_STATUS_REJECTED = 3,
  UNRECOGNIZED = -1
}

export function auditStatusFromJSON(object: any): AuditStatus {
  switch (object) {
    case 0:
    case 'AUDIT_STATUS_NONE':
      return AuditStatus.AUDIT_STATUS_NONE;
    case 1:
    case 'AUDIT_STATUS_PENDING':
      return AuditStatus.AUDIT_STATUS_PENDING;
    case 2:
    case 'AUDIT_STATUS_APPROVED':
      return AuditStatus.AUDIT_STATUS_APPROVED;
    case 3:
    case 'AUDIT_STATUS_REJECTED':
      return AuditStatus.AUDIT_STATUS_REJECTED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AuditStatus.UNRECOGNIZED;
  }
}

export interface BatchDeductBalanceReq {
  deduct_balance: DeductBalanceReq[];
}

export interface BatchDeductBalanceRsp {
  /** 账号余额 */
  balance: Balance[];
}

export interface DeductBalanceReq {
  /** 客户端seq */
  seq_id: string;
  currency_type: CurrencyType;
  amount: number;
  /** 扣费业务类型文案 */
  business_type_text: string;
  /** 业务类型 */
  origin_business_type: string;
  /** 用户uid */
  uid: number;
}

export interface BatchGetUserBalanceReq {
  get_balance: GetUserBalanceReq[];
}

export interface GetUserBalanceReq {
  uid: number;
  currency_types: CurrencyType[];
}

export interface BatchGetUserBalanceRsp {
  user_balances: GetUserBalanceRsp[];
}

export interface GetUserBalanceRsp {
  uid: number;
  balances: Balance[];
}

export interface TransactionRecord {
  id: number;
  /** UID */
  uid: number;
  /** 房间ID */
  room_id: string;
  /** 交易类型 */
  type: TransactionType;
  /** 交易标题 消费类型文案 对应-business_type */
  title: string;
  /** 币种 */
  currency_type: CurrencyType;
  /** 本次交易总变化数量(包括可用余额和保证金) */
  amount: number;
  /** 本次交易免费余额变化数量 */
  free_amount: number;
  /** 本次交易后总可用余额 */
  balance: number;
  /** 本次交易保证金变化数量(用 Amount 减去 GuaranteeAmount 就是可用余额的变化数量) */
  guarantee_amount: number;
  /** 本次交易后保证金余额 */
  guarantee_balance: number;
  /** 交易发生的时间戳, unix-timestamp 精确到秒. */
  time_transacted: number;
  /** 业务类型 */
  origin_business_type: string;
  /** 交易对方用户ID */
  opposite_uid: number;
}

export interface ListTransactionRecordReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 用户ID */
  uids: number[];
  /** 房间ID */
  room_ids: string[];
  /** 货币类型 */
  currency_types: CurrencyType[];
  /** 流水类型 */
  origin_business_types: string[];
  min_time_transacted: number;
  max_time_transacted: number;
  /** 交易流水类型 */
  types: TransactionType[];
  /** 交易对方用户ID */
  opposite_uids: number[];
}

export interface ListTransactionRecordRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 流水记录 */
  records: TransactionRecord[];
}

/** 扣除的用户和金额 */
export interface BatchBalanceOperateItem {
  uid: number;
  amount: number;
  paid_amount: number;
  /** 实收美金, 实收美金大于 0 表示是付费货币. */
  real_dollor: number;
}

export interface BatchBalanceOperateReq {
  operate_kind: BalanceOperateKind;
  uids: number[];
  currency_type: CurrencyType;
  amount: number;
  reason: string;
  uidAmounts: BatchBalanceOperateItem[];
  paid_amount: number;
  /** 实收美金, 实收美金大于 0 表示是付费货币. */
  real_dollor: number;
}

export interface BatchBalanceOperateRsp {}

/** 余额操作记录 */
export interface BalanceOperateRecord {
  id: number;
  operate_kind: BalanceOperateKind;
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
  currency_type: CurrencyType;
  amount: number;
  reason: string;
  paid_amount: number;
  /** 实收美金, 实收美金大于 0 表示是付费货币. */
  real_dollor: number;
  audit_status: AuditStatus;
  audit_reason: string;
  audit_at: number;
  audit_by: string;
  submit_at: number;
  submit_by: string;
}

export interface ListBalanceOperateRecordReq {
  page: Page | undefined;
  operate_kind: BalanceOperateKind;
  uids: number[];
  currency_type: CurrencyType;
  submit_at_start: number;
  submit_at_end: number;
  audit_status: AuditStatus;
  operator: string;
  audit_at_start: number;
  audit_at_end: number;
}

export interface ListBalanceOperateRecordRsp {
  page: Page | undefined;
  operate_records: BalanceOperateRecord[];
}

export interface ListBalanceOperatorReq {}

export interface ListBalanceOperatorRsp {
  operators: string[];
}

function createBaseBatchDeductBalanceReq(): BatchDeductBalanceReq {
  return { deduct_balance: [] };
}

export const BatchDeductBalanceReq: MessageFns<BatchDeductBalanceReq> = {
  fromJSON(object: any): BatchDeductBalanceReq {
    return {
      deduct_balance: globalThis.Array.isArray(object?.deduct_balance)
        ? object.deduct_balance.map((e: any) => DeductBalanceReq.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchDeductBalanceReq>, I>>(base?: I): BatchDeductBalanceReq {
    return BatchDeductBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchDeductBalanceReq>, I>>(object: I): BatchDeductBalanceReq {
    const message = createBaseBatchDeductBalanceReq();
    message.deduct_balance = object.deduct_balance?.map(e => DeductBalanceReq.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchDeductBalanceRsp(): BatchDeductBalanceRsp {
  return { balance: [] };
}

export const BatchDeductBalanceRsp: MessageFns<BatchDeductBalanceRsp> = {
  fromJSON(object: any): BatchDeductBalanceRsp {
    return {
      balance: globalThis.Array.isArray(object?.balance) ? object.balance.map((e: any) => Balance.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BatchDeductBalanceRsp>, I>>(base?: I): BatchDeductBalanceRsp {
    return BatchDeductBalanceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchDeductBalanceRsp>, I>>(object: I): BatchDeductBalanceRsp {
    const message = createBaseBatchDeductBalanceRsp();
    message.balance = object.balance?.map(e => Balance.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeductBalanceReq(): DeductBalanceReq {
  return { seq_id: '', currency_type: 0, amount: 0, business_type_text: '', origin_business_type: '', uid: 0 };
}

export const DeductBalanceReq: MessageFns<DeductBalanceReq> = {
  fromJSON(object: any): DeductBalanceReq {
    return {
      seq_id: isSet(object.seq_id) ? globalThis.String(object.seq_id) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      business_type_text: isSet(object.business_type_text) ? globalThis.String(object.business_type_text) : '',
      origin_business_type: isSet(object.origin_business_type) ? globalThis.String(object.origin_business_type) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<DeductBalanceReq>, I>>(base?: I): DeductBalanceReq {
    return DeductBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeductBalanceReq>, I>>(object: I): DeductBalanceReq {
    const message = createBaseDeductBalanceReq();
    message.seq_id = object.seq_id ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.business_type_text = object.business_type_text ?? '';
    message.origin_business_type = object.origin_business_type ?? '';
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseBatchGetUserBalanceReq(): BatchGetUserBalanceReq {
  return { get_balance: [] };
}

export const BatchGetUserBalanceReq: MessageFns<BatchGetUserBalanceReq> = {
  fromJSON(object: any): BatchGetUserBalanceReq {
    return {
      get_balance: globalThis.Array.isArray(object?.get_balance)
        ? object.get_balance.map((e: any) => GetUserBalanceReq.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserBalanceReq>, I>>(base?: I): BatchGetUserBalanceReq {
    return BatchGetUserBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserBalanceReq>, I>>(object: I): BatchGetUserBalanceReq {
    const message = createBaseBatchGetUserBalanceReq();
    message.get_balance = object.get_balance?.map(e => GetUserBalanceReq.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetUserBalanceReq(): GetUserBalanceReq {
  return { uid: 0, currency_types: [] };
}

export const GetUserBalanceReq: MessageFns<GetUserBalanceReq> = {
  fromJSON(object: any): GetUserBalanceReq {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      currency_types: globalThis.Array.isArray(object?.currency_types)
        ? object.currency_types.map((e: any) => currencyTypeFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserBalanceReq>, I>>(base?: I): GetUserBalanceReq {
    return GetUserBalanceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserBalanceReq>, I>>(object: I): GetUserBalanceReq {
    const message = createBaseGetUserBalanceReq();
    message.uid = object.uid ?? 0;
    message.currency_types = object.currency_types?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserBalanceRsp(): BatchGetUserBalanceRsp {
  return { user_balances: [] };
}

export const BatchGetUserBalanceRsp: MessageFns<BatchGetUserBalanceRsp> = {
  fromJSON(object: any): BatchGetUserBalanceRsp {
    return {
      user_balances: globalThis.Array.isArray(object?.user_balances)
        ? object.user_balances.map((e: any) => GetUserBalanceRsp.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserBalanceRsp>, I>>(base?: I): BatchGetUserBalanceRsp {
    return BatchGetUserBalanceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserBalanceRsp>, I>>(object: I): BatchGetUserBalanceRsp {
    const message = createBaseBatchGetUserBalanceRsp();
    message.user_balances = object.user_balances?.map(e => GetUserBalanceRsp.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetUserBalanceRsp(): GetUserBalanceRsp {
  return { uid: 0, balances: [] };
}

export const GetUserBalanceRsp: MessageFns<GetUserBalanceRsp> = {
  fromJSON(object: any): GetUserBalanceRsp {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      balances: globalThis.Array.isArray(object?.balances) ? object.balances.map((e: any) => Balance.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetUserBalanceRsp>, I>>(base?: I): GetUserBalanceRsp {
    return GetUserBalanceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUserBalanceRsp>, I>>(object: I): GetUserBalanceRsp {
    const message = createBaseGetUserBalanceRsp();
    message.uid = object.uid ?? 0;
    message.balances = object.balances?.map(e => Balance.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTransactionRecord(): TransactionRecord {
  return {
    id: 0,
    uid: 0,
    room_id: '',
    type: 0,
    title: '',
    currency_type: 0,
    amount: 0,
    free_amount: 0,
    balance: 0,
    guarantee_amount: 0,
    guarantee_balance: 0,
    time_transacted: 0,
    origin_business_type: '',
    opposite_uid: 0
  };
}

export const TransactionRecord: MessageFns<TransactionRecord> = {
  fromJSON(object: any): TransactionRecord {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      type: isSet(object.type) ? transactionTypeFromJSON(object.type) : 0,
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      free_amount: isSet(object.free_amount) ? globalThis.Number(object.free_amount) : 0,
      balance: isSet(object.balance) ? globalThis.Number(object.balance) : 0,
      guarantee_amount: isSet(object.guarantee_amount) ? globalThis.Number(object.guarantee_amount) : 0,
      guarantee_balance: isSet(object.guarantee_balance) ? globalThis.Number(object.guarantee_balance) : 0,
      time_transacted: isSet(object.time_transacted) ? globalThis.Number(object.time_transacted) : 0,
      origin_business_type: isSet(object.origin_business_type) ? globalThis.String(object.origin_business_type) : '',
      opposite_uid: isSet(object.opposite_uid) ? globalThis.Number(object.opposite_uid) : 0
    };
  },

  create<I extends Exact<DeepPartial<TransactionRecord>, I>>(base?: I): TransactionRecord {
    return TransactionRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TransactionRecord>, I>>(object: I): TransactionRecord {
    const message = createBaseTransactionRecord();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.room_id = object.room_id ?? '';
    message.type = object.type ?? 0;
    message.title = object.title ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.free_amount = object.free_amount ?? 0;
    message.balance = object.balance ?? 0;
    message.guarantee_amount = object.guarantee_amount ?? 0;
    message.guarantee_balance = object.guarantee_balance ?? 0;
    message.time_transacted = object.time_transacted ?? 0;
    message.origin_business_type = object.origin_business_type ?? '';
    message.opposite_uid = object.opposite_uid ?? 0;
    return message;
  }
};

function createBaseListTransactionRecordReq(): ListTransactionRecordReq {
  return {
    page: undefined,
    uids: [],
    room_ids: [],
    currency_types: [],
    origin_business_types: [],
    min_time_transacted: 0,
    max_time_transacted: 0,
    types: [],
    opposite_uids: []
  };
}

export const ListTransactionRecordReq: MessageFns<ListTransactionRecordReq> = {
  fromJSON(object: any): ListTransactionRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.String(e)) : [],
      currency_types: globalThis.Array.isArray(object?.currency_types)
        ? object.currency_types.map((e: any) => currencyTypeFromJSON(e))
        : [],
      origin_business_types: globalThis.Array.isArray(object?.origin_business_types)
        ? object.origin_business_types.map((e: any) => globalThis.String(e))
        : [],
      min_time_transacted: isSet(object.min_time_transacted) ? globalThis.Number(object.min_time_transacted) : 0,
      max_time_transacted: isSet(object.max_time_transacted) ? globalThis.Number(object.max_time_transacted) : 0,
      types: globalThis.Array.isArray(object?.types) ? object.types.map((e: any) => transactionTypeFromJSON(e)) : [],
      opposite_uids: globalThis.Array.isArray(object?.opposite_uids)
        ? object.opposite_uids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListTransactionRecordReq>, I>>(base?: I): ListTransactionRecordReq {
    return ListTransactionRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionRecordReq>, I>>(object: I): ListTransactionRecordReq {
    const message = createBaseListTransactionRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.uids = object.uids?.map(e => e) || [];
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.currency_types = object.currency_types?.map(e => e) || [];
    message.origin_business_types = object.origin_business_types?.map(e => e) || [];
    message.min_time_transacted = object.min_time_transacted ?? 0;
    message.max_time_transacted = object.max_time_transacted ?? 0;
    message.types = object.types?.map(e => e) || [];
    message.opposite_uids = object.opposite_uids?.map(e => e) || [];
    return message;
  }
};

function createBaseListTransactionRecordRsp(): ListTransactionRecordRsp {
  return { page: undefined, records: [] };
}

export const ListTransactionRecordRsp: MessageFns<ListTransactionRecordRsp> = {
  fromJSON(object: any): ListTransactionRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      records: globalThis.Array.isArray(object?.records)
        ? object.records.map((e: any) => TransactionRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListTransactionRecordRsp>, I>>(base?: I): ListTransactionRecordRsp {
    return ListTransactionRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListTransactionRecordRsp>, I>>(object: I): ListTransactionRecordRsp {
    const message = createBaseListTransactionRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.records = object.records?.map(e => TransactionRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchBalanceOperateItem(): BatchBalanceOperateItem {
  return { uid: 0, amount: 0, paid_amount: 0, real_dollor: 0 };
}

export const BatchBalanceOperateItem: MessageFns<BatchBalanceOperateItem> = {
  fromJSON(object: any): BatchBalanceOperateItem {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      paid_amount: isSet(object.paid_amount) ? globalThis.Number(object.paid_amount) : 0,
      real_dollor: isSet(object.real_dollor) ? globalThis.Number(object.real_dollor) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchBalanceOperateItem>, I>>(base?: I): BatchBalanceOperateItem {
    return BatchBalanceOperateItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchBalanceOperateItem>, I>>(object: I): BatchBalanceOperateItem {
    const message = createBaseBatchBalanceOperateItem();
    message.uid = object.uid ?? 0;
    message.amount = object.amount ?? 0;
    message.paid_amount = object.paid_amount ?? 0;
    message.real_dollor = object.real_dollor ?? 0;
    return message;
  }
};

function createBaseBatchBalanceOperateReq(): BatchBalanceOperateReq {
  return {
    operate_kind: 0,
    uids: [],
    currency_type: 0,
    amount: 0,
    reason: '',
    uidAmounts: [],
    paid_amount: 0,
    real_dollor: 0
  };
}

export const BatchBalanceOperateReq: MessageFns<BatchBalanceOperateReq> = {
  fromJSON(object: any): BatchBalanceOperateReq {
    return {
      operate_kind: isSet(object.operate_kind) ? balanceOperateKindFromJSON(object.operate_kind) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      uidAmounts: globalThis.Array.isArray(object?.uidAmounts)
        ? object.uidAmounts.map((e: any) => BatchBalanceOperateItem.fromJSON(e))
        : [],
      paid_amount: isSet(object.paid_amount) ? globalThis.Number(object.paid_amount) : 0,
      real_dollor: isSet(object.real_dollor) ? globalThis.Number(object.real_dollor) : 0
    };
  },

  create<I extends Exact<DeepPartial<BatchBalanceOperateReq>, I>>(base?: I): BatchBalanceOperateReq {
    return BatchBalanceOperateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchBalanceOperateReq>, I>>(object: I): BatchBalanceOperateReq {
    const message = createBaseBatchBalanceOperateReq();
    message.operate_kind = object.operate_kind ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.reason = object.reason ?? '';
    message.uidAmounts = object.uidAmounts?.map(e => BatchBalanceOperateItem.fromPartial(e)) || [];
    message.paid_amount = object.paid_amount ?? 0;
    message.real_dollor = object.real_dollor ?? 0;
    return message;
  }
};

function createBaseBatchBalanceOperateRsp(): BatchBalanceOperateRsp {
  return {};
}

export const BatchBalanceOperateRsp: MessageFns<BatchBalanceOperateRsp> = {
  fromJSON(_: any): BatchBalanceOperateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchBalanceOperateRsp>, I>>(base?: I): BatchBalanceOperateRsp {
    return BatchBalanceOperateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchBalanceOperateRsp>, I>>(_: I): BatchBalanceOperateRsp {
    const message = createBaseBatchBalanceOperateRsp();
    return message;
  }
};

function createBaseBalanceOperateRecord(): BalanceOperateRecord {
  return {
    id: 0,
    operate_kind: 0,
    uid: 0,
    show_uid: '',
    nickname: '',
    avatar: '',
    currency_type: 0,
    amount: 0,
    reason: '',
    paid_amount: 0,
    real_dollor: 0,
    audit_status: 0,
    audit_reason: '',
    audit_at: 0,
    audit_by: '',
    submit_at: 0,
    submit_by: ''
  };
}

export const BalanceOperateRecord: MessageFns<BalanceOperateRecord> = {
  fromJSON(object: any): BalanceOperateRecord {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      operate_kind: isSet(object.operate_kind) ? balanceOperateKindFromJSON(object.operate_kind) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      paid_amount: isSet(object.paid_amount) ? globalThis.Number(object.paid_amount) : 0,
      real_dollor: isSet(object.real_dollor) ? globalThis.Number(object.real_dollor) : 0,
      audit_status: isSet(object.audit_status) ? auditStatusFromJSON(object.audit_status) : 0,
      audit_reason: isSet(object.audit_reason) ? globalThis.String(object.audit_reason) : '',
      audit_at: isSet(object.audit_at) ? globalThis.Number(object.audit_at) : 0,
      audit_by: isSet(object.audit_by) ? globalThis.String(object.audit_by) : '',
      submit_at: isSet(object.submit_at) ? globalThis.Number(object.submit_at) : 0,
      submit_by: isSet(object.submit_by) ? globalThis.String(object.submit_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<BalanceOperateRecord>, I>>(base?: I): BalanceOperateRecord {
    return BalanceOperateRecord.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BalanceOperateRecord>, I>>(object: I): BalanceOperateRecord {
    const message = createBaseBalanceOperateRecord();
    message.id = object.id ?? 0;
    message.operate_kind = object.operate_kind ?? 0;
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.currency_type = object.currency_type ?? 0;
    message.amount = object.amount ?? 0;
    message.reason = object.reason ?? '';
    message.paid_amount = object.paid_amount ?? 0;
    message.real_dollor = object.real_dollor ?? 0;
    message.audit_status = object.audit_status ?? 0;
    message.audit_reason = object.audit_reason ?? '';
    message.audit_at = object.audit_at ?? 0;
    message.audit_by = object.audit_by ?? '';
    message.submit_at = object.submit_at ?? 0;
    message.submit_by = object.submit_by ?? '';
    return message;
  }
};

function createBaseListBalanceOperateRecordReq(): ListBalanceOperateRecordReq {
  return {
    page: undefined,
    operate_kind: 0,
    uids: [],
    currency_type: 0,
    submit_at_start: 0,
    submit_at_end: 0,
    audit_status: 0,
    operator: '',
    audit_at_start: 0,
    audit_at_end: 0
  };
}

export const ListBalanceOperateRecordReq: MessageFns<ListBalanceOperateRecordReq> = {
  fromJSON(object: any): ListBalanceOperateRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      operate_kind: isSet(object.operate_kind) ? balanceOperateKindFromJSON(object.operate_kind) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      submit_at_start: isSet(object.submit_at_start) ? globalThis.Number(object.submit_at_start) : 0,
      submit_at_end: isSet(object.submit_at_end) ? globalThis.Number(object.submit_at_end) : 0,
      audit_status: isSet(object.audit_status) ? auditStatusFromJSON(object.audit_status) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      audit_at_start: isSet(object.audit_at_start) ? globalThis.Number(object.audit_at_start) : 0,
      audit_at_end: isSet(object.audit_at_end) ? globalThis.Number(object.audit_at_end) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBalanceOperateRecordReq>, I>>(base?: I): ListBalanceOperateRecordReq {
    return ListBalanceOperateRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBalanceOperateRecordReq>, I>>(object: I): ListBalanceOperateRecordReq {
    const message = createBaseListBalanceOperateRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.operate_kind = object.operate_kind ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.currency_type = object.currency_type ?? 0;
    message.submit_at_start = object.submit_at_start ?? 0;
    message.submit_at_end = object.submit_at_end ?? 0;
    message.audit_status = object.audit_status ?? 0;
    message.operator = object.operator ?? '';
    message.audit_at_start = object.audit_at_start ?? 0;
    message.audit_at_end = object.audit_at_end ?? 0;
    return message;
  }
};

function createBaseListBalanceOperateRecordRsp(): ListBalanceOperateRecordRsp {
  return { page: undefined, operate_records: [] };
}

export const ListBalanceOperateRecordRsp: MessageFns<ListBalanceOperateRecordRsp> = {
  fromJSON(object: any): ListBalanceOperateRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      operate_records: globalThis.Array.isArray(object?.operate_records)
        ? object.operate_records.map((e: any) => BalanceOperateRecord.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListBalanceOperateRecordRsp>, I>>(base?: I): ListBalanceOperateRecordRsp {
    return ListBalanceOperateRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBalanceOperateRecordRsp>, I>>(object: I): ListBalanceOperateRecordRsp {
    const message = createBaseListBalanceOperateRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.operate_records = object.operate_records?.map(e => BalanceOperateRecord.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListBalanceOperatorReq(): ListBalanceOperatorReq {
  return {};
}

export const ListBalanceOperatorReq: MessageFns<ListBalanceOperatorReq> = {
  fromJSON(_: any): ListBalanceOperatorReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListBalanceOperatorReq>, I>>(base?: I): ListBalanceOperatorReq {
    return ListBalanceOperatorReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBalanceOperatorReq>, I>>(_: I): ListBalanceOperatorReq {
    const message = createBaseListBalanceOperatorReq();
    return message;
  }
};

function createBaseListBalanceOperatorRsp(): ListBalanceOperatorRsp {
  return { operators: [] };
}

export const ListBalanceOperatorRsp: MessageFns<ListBalanceOperatorRsp> = {
  fromJSON(object: any): ListBalanceOperatorRsp {
    return {
      operators: globalThis.Array.isArray(object?.operators)
        ? object.operators.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListBalanceOperatorRsp>, I>>(base?: I): ListBalanceOperatorRsp {
    return ListBalanceOperatorRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBalanceOperatorRsp>, I>>(object: I): ListBalanceOperatorRsp {
    const message = createBaseListBalanceOperatorRsp();
    message.operators = object.operators?.map(e => e) || [];
    return message;
  }
};

/** smicro:spath=gitit.cc/social/components-service/social-revenue/walletmgr/wallet.go */
export type WalletMgrDefinition = typeof WalletMgrDefinition;
export const WalletMgrDefinition = {
  name: 'WalletMgr',
  fullName: 'mgr.pbrevenue.WalletMgr',
  methods: {
    /** 批量获取余额, 支持多个币种同时查询. */
    batchGetUserBalance: {
      name: 'BatchGetUserBalance',
      requestType: BatchGetUserBalanceReq,
      requestStream: false,
      responseType: BatchGetUserBalanceRsp,
      responseStream: false,
      options: {}
    },
    /** 批量账户扣款, 多币种 */
    batchDeductBalance: {
      name: 'BatchDeductBalance',
      requestType: BatchDeductBalanceReq,
      requestStream: false,
      responseType: BatchDeductBalanceRsp,
      responseStream: false,
      options: {}
    },
    /** 查询交易流水记录 */
    listTransactionRecord: {
      name: 'ListTransactionRecord',
      requestType: ListTransactionRecordReq,
      requestStream: false,
      responseType: ListTransactionRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 批量操作账户余额 */
    batchBalanceOperate: {
      name: 'BatchBalanceOperate',
      requestType: BatchBalanceOperateReq,
      requestStream: false,
      responseType: BatchBalanceOperateRsp,
      responseStream: false,
      options: {}
    },
    /** 获取账户余额操作记录 */
    listBalanceOperateRecord: {
      name: 'ListBalanceOperateRecord',
      requestType: ListBalanceOperateRecordReq,
      requestStream: false,
      responseType: ListBalanceOperateRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 获取账户余额操作操作人列表 */
    listBalanceOperator: {
      name: 'ListBalanceOperator',
      requestType: ListBalanceOperatorReq,
      requestStream: false,
      responseType: ListBalanceOperatorRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
