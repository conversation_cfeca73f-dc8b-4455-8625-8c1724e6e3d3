// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/gift.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import {
  CurrencyType,
  currencyTypeFromJSON,
  JumpType,
  jumpTypeFromJSON,
  WishGiftType,
  wishGiftTypeFromJSON
} from '../../api/revenue/common';
import {
  GiftAssets,
  GiftBanner as GiftBanner2,
  GiftBatchType,
  giftBatchTypeFromJSON,
  GiftDuration,
  GiftHiddenSwitch,
  GiftJump,
  GiftKind,
  giftKindFromJSON,
  GiftLevel,
  giftLevelFromJSON,
  GiftLimit,
  GiftLimitType,
  giftLimitTypeFromJSON,
  GiftName,
  GiftPlay,
  GiftPrice,
  GiftRequirement,
  GiftScene,
  giftSceneFromJSON,
  GiftTag as GiftTag1,
  GiftType,
  giftTypeFromJSON
} from '../../api/revenue/gift';

export const protobufPackage = 'mgr.gift';

export interface GiftInfo {
  id: number;
  /** 版本 */
  version: number;
  /** 礼物类型 0 - 普通礼物 */
  type: GiftType;
  /** 礼物标签, 例如: 限时, 七夕 ... */
  tag: GiftTag1 | undefined;
  sequence: number;
  /** 礼物后台显示名称 */
  name: string;
  /** 礼物多语言名称 包含国际化支持 */
  i18n: GiftName | undefined;
  /** 礼物价格类型 */
  currency_type: CurrencyType;
  /** 礼物价格 */
  price: GiftPrice | undefined;
  /** 礼物样式 */
  assets: GiftAssets | undefined;
  /** 礼物banner信息 */
  banner: GiftBanner2 | undefined;
  /** 礼物限制条件 */
  limitation: GiftLimit | undefined;
  batch_type: GiftBatchType;
  tags: GiftTag1[];
  /** duration 有效时间 */
  duration: GiftDuration | undefined;
  jump: GiftJump | undefined;
  level: GiftLevel;
  published: boolean;
  hidden: boolean;
  deleted: boolean;
  create_at: number;
  update_at: number;
  tab_id: number;
  tag_ids: number[];
  editor: string;
  /** 生效类型 1 - 永久有效 2 - 按时间范围生效 */
  effect_type: number;
  /** 生效开始时间 */
  valid_from: number;
  /** 生效结束时间 */
  valid_to: number;
  /** 0 - 全部 1 - 生效中 2 - 已过期 3 - 未开始 */
  isValid: number;
  /** 礼物标识，手动配置，测试环境和正式环境保持一致 */
  sid: string;
  /** 礼物分成比例百分比，即：1 表示 1% */
  income_rate: number;
  /** 玩法配置 */
  gift_play: GiftPlay | undefined;
  /** 备注 */
  remark: string;
  /** banner id */
  banner_id: number;
  /** 阶级 */
  tier: number;
  /** 业务拓展信息 JSON 字符串, 根据业务名找到对应的结构体 GiftBizExtXXX 来解析. */
  biz_ext: string;
}

export interface ListGiftsReq {
  page: Page | undefined;
  id: number;
  name: string;
  type: GiftType;
  price_min: number;
  price_max: number;
  /** 0 - 全部 1 - 已发布 2- 未发布 */
  published: number;
  /** 0 - 全部 1 - 生效中 2 - 已过期 3 - 未开始 */
  isValid: number;
  /** 礼物导航栏 */
  tab_id: number;
  /** 上传资源状态 0 - 全部 1 - 已上传 2 - 未上传 */
  upload_res_status: number;
  /** 限制条件 */
  limitation: ListGiftLimitation | undefined;
  /** 业务拓展信息(biz_ext)字段的查询 */
  biz_ext_args: BizExtArgs | undefined;
  /** banner id */
  banner_id: number;
}

export interface BizExtArgs {
  /** 是否为心愿礼物 */
  wish_gift_type: WishGiftType;
}

export interface ListGiftsRsp {
  list: GiftInfo[];
  page: Page | undefined;
}

export interface AddGiftInfoReq {
  info: GiftInfo | undefined;
}

export interface AddGiftInfoRsp {}

export interface BatchAddGiftInfoReq {
  infos: GiftInfo[];
}

export interface BatchAddGiftInfoRsp {}

export interface UpdateGiftInfoReq {
  info: GiftInfo | undefined;
}

export interface UpdateGiftInfoRsp {}

export interface GiftTab {
  id: number;
  scenes: GiftScene[];
  name: string;
  i18n: { [key: string]: string };
  sequence: number;
  published: boolean;
  hidden: boolean;
  deleted: boolean;
  create_at: number;
  update_at: number;
  editor: string;
  gift_num: number;
  /** 礼物显示的必要条件 */
  requirements: GiftRequirement[];
  /** 礼物隐藏开关 */
  hidden_switches: GiftHiddenSwitch[];
}

export interface GiftTab_I18nEntry {
  key: string;
  value: string;
}

export interface ListGiftTabReq {
  id: number;
  page: Page | undefined;
}

export interface ListGiftTabRsp {
  list: GiftTab[];
  page: Page | undefined;
}

export interface UpdateGiftTabReq {
  info: GiftTab | undefined;
}

export interface UpdateGiftTabRsp {}

export interface AddGiftTabReq {
  info: GiftTab | undefined;
}

export interface AddGiftTabRsp {}

export interface GiftTag {
  id: number;
  name: string;
  icon: string;
  sequence: number;
  deleted: boolean;
  create_at: number;
  update_at: number;
  editor: string;
  /** <语言, 图标> */
  icon_i18n: { [key: string]: string };
}

export interface GiftTag_IconI18nEntry {
  key: string;
  value: string;
}

export interface ListGiftTagReq {
  id: number;
  page: Page | undefined;
}

export interface ListGiftTagRsp {
  list: GiftTag[];
  page: Page | undefined;
}

export interface UpdateGiftTagReq {
  info: GiftTag | undefined;
}

export interface UpdateGiftTagRsp {}

export interface AddGiftTagReq {
  info: GiftTag | undefined;
}

export interface AddGiftTagRsp {}

export interface UpdatePublishReq {
  id: number;
  published: boolean;
  editor: string;
}

export interface UpdatePublishRsp {}

export interface DeleteGiftReq {
  id: number;
  editor: string;
}

export interface DeleteGiftRsp {}

export interface BatchGetGiftReq {
  ids: number[];
}

export interface BatchGetGiftRsp {
  gifts: { [key: number]: GiftInfo };
}

export interface BatchGetGiftRsp_GiftsEntry {
  key: number;
  value: GiftInfo | undefined;
}

export interface BatchGetGiftByLotteryIdReq {
  lottery_ids: number[];
}

export interface BatchGetGiftByLotteryIdRsp {
  gifts: { [key: number]: GiftInfo };
}

export interface BatchGetGiftByLotteryIdRsp_GiftsEntry {
  key: number;
  value: GiftInfo | undefined;
}

export interface GiftRecordInfo {
  /** 记录 ID */
  id: number;
  /** 礼物信息 */
  gift_info: GiftInfo | undefined;
  /** 礼物数量 */
  quantity: number;
  /** 送礼人 */
  sender_uid: number;
  /** 收礼人 */
  receiver_uid: number;
  /** 送礼时间 */
  time_sent: number;
  /** 房间ID */
  room_id: number;
  /** 赠送类型 */
  batch_type: GiftBatchType;
  /** 返利币种 */
  profit_currency_type: CurrencyType;
  /** 返利金额 */
  profit_amount: number;
}

export interface SearchGiftRecordReq {
  page: Page | undefined;
  gift_kind: GiftKind;
  uids: number[];
  room_ids: string[];
  gift_ids: number[];
  min_time_sent: number;
  max_time_sent: number;
}

export interface SearchGiftRecordRsp {
  page: Page | undefined;
  record_infos: GiftRecordInfo[];
}

export interface GiftBanner {
  /** 中文备注名 */
  name: string;
  /** 跳转链接 */
  link: string;
  /** 背景图json例如{"en":"aa.jpg","ar":"bb.jpg"} */
  images: string;
  /** 宽度 */
  width: number;
  /** 高度 */
  height: number;
  /** 是否删除1:已删除2:未删除 */
  is_delete: number;
  /** 开始时间,单位秒 */
  start_time: number;
  /** 结束时间,单位秒 */
  end_time: number;
  /** 上下架1:上架2:下架 */
  is_online: number;
  /** 备注 */
  remark: string;
  /** 权重 */
  weight: number;
  /** 创建时间,单位秒 */
  create_at: number;
  /** 结束时间,单位秒 */
  update_at: number;
  /** 创建者 */
  creator: string;
  /** 更新者 */
  modifier: string;
  /** banner id(创建不传,更新传) */
  id: number;
  /** 关联礼物ID */
  gift_id: number[];
  /** 跳转方式 */
  jump_type: JumpType;
  /** <语言码，图片URL> */
  image_i18n: { [key: string]: string };
}

export interface GiftBanner_ImageI18nEntry {
  key: string;
  value: string;
}

export interface CreateBannerReq {
  data: GiftBanner | undefined;
}

export interface CreateBannerRsp {
  id: number;
}

export interface UpdateBannerReq {
  data: GiftBanner | undefined;
}

export interface UpdateBannerRsp {}

export interface DeleteBannerReq {
  id: number;
}

export interface DeleteBannerRsp {}

export interface ListBannerReq {
  page: Page | undefined;
  /** 名称 */
  name: string;
  /** 上下架1:上架2:下架 */
  is_online: number;
  /** 是否有效1:有效2:无效 */
  is_valid: number;
  banner_id: number;
}

export interface ListBannerRsp {
  page: Page | undefined;
  list: GiftBanner[];
}

/** 离线资源信息查询 */
export interface GetGiftOfflineResInfoReq {}

/** 离线资源信息查询 */
export interface GetGiftOfflineResInfoRsp {
  /** 离线资源总数 */
  total: number;
}

export interface ListGiftLimitation {
  /** 限制类型 */
  type: GiftLimitType;
  /** 最小限制等级，<0不查询 */
  min_level: number;
  /** 最大限制等级，<0不查询 */
  max_level: number;
}

export interface CheckPasteSimilarGiftReq {
  gifts: GiftInfo[];
}

export interface CheckPasteSimilarGiftRsp {
  gifts: GiftInfo[];
}

function createBaseGiftInfo(): GiftInfo {
  return {
    id: 0,
    version: 0,
    type: 0,
    tag: undefined,
    sequence: 0,
    name: '',
    i18n: undefined,
    currency_type: 0,
    price: undefined,
    assets: undefined,
    banner: undefined,
    limitation: undefined,
    batch_type: 0,
    tags: [],
    duration: undefined,
    jump: undefined,
    level: 0,
    published: false,
    hidden: false,
    deleted: false,
    create_at: 0,
    update_at: 0,
    tab_id: 0,
    tag_ids: [],
    editor: '',
    effect_type: 0,
    valid_from: 0,
    valid_to: 0,
    isValid: 0,
    sid: '',
    income_rate: 0,
    gift_play: undefined,
    remark: '',
    banner_id: 0,
    tier: 0,
    biz_ext: ''
  };
}

export const GiftInfo: MessageFns<GiftInfo> = {
  fromJSON(object: any): GiftInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      type: isSet(object.type) ? giftTypeFromJSON(object.type) : 0,
      tag: isSet(object.tag) ? GiftTag1.fromJSON(object.tag) : undefined,
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n: isSet(object.i18n) ? GiftName.fromJSON(object.i18n) : undefined,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      price: isSet(object.price) ? GiftPrice.fromJSON(object.price) : undefined,
      assets: isSet(object.assets) ? GiftAssets.fromJSON(object.assets) : undefined,
      banner: isSet(object.banner) ? GiftBanner2.fromJSON(object.banner) : undefined,
      limitation: isSet(object.limitation) ? GiftLimit.fromJSON(object.limitation) : undefined,
      batch_type: isSet(object.batch_type) ? giftBatchTypeFromJSON(object.batch_type) : 0,
      tags: globalThis.Array.isArray(object?.tags) ? object.tags.map((e: any) => GiftTag1.fromJSON(e)) : [],
      duration: isSet(object.duration) ? GiftDuration.fromJSON(object.duration) : undefined,
      jump: isSet(object.jump) ? GiftJump.fromJSON(object.jump) : undefined,
      level: isSet(object.level) ? giftLevelFromJSON(object.level) : 0,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      hidden: isSet(object.hidden) ? globalThis.Boolean(object.hidden) : false,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      tab_id: isSet(object.tab_id) ? globalThis.Number(object.tab_id) : 0,
      tag_ids: globalThis.Array.isArray(object?.tag_ids) ? object.tag_ids.map((e: any) => globalThis.Number(e)) : [],
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      effect_type: isSet(object.effect_type) ? globalThis.Number(object.effect_type) : 0,
      valid_from: isSet(object.valid_from) ? globalThis.Number(object.valid_from) : 0,
      valid_to: isSet(object.valid_to) ? globalThis.Number(object.valid_to) : 0,
      isValid: isSet(object.isValid) ? globalThis.Number(object.isValid) : 0,
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      income_rate: isSet(object.income_rate) ? globalThis.Number(object.income_rate) : 0,
      gift_play: isSet(object.gift_play) ? GiftPlay.fromJSON(object.gift_play) : undefined,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      banner_id: isSet(object.banner_id) ? globalThis.Number(object.banner_id) : 0,
      tier: isSet(object.tier) ? globalThis.Number(object.tier) : 0,
      biz_ext: isSet(object.biz_ext) ? globalThis.String(object.biz_ext) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftInfo>, I>>(base?: I): GiftInfo {
    return GiftInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftInfo>, I>>(object: I): GiftInfo {
    const message = createBaseGiftInfo();
    message.id = object.id ?? 0;
    message.version = object.version ?? 0;
    message.type = object.type ?? 0;
    message.tag = object.tag !== undefined && object.tag !== null ? GiftTag1.fromPartial(object.tag) : undefined;
    message.sequence = object.sequence ?? 0;
    message.name = object.name ?? '';
    message.i18n = object.i18n !== undefined && object.i18n !== null ? GiftName.fromPartial(object.i18n) : undefined;
    message.currency_type = object.currency_type ?? 0;
    message.price =
      object.price !== undefined && object.price !== null ? GiftPrice.fromPartial(object.price) : undefined;
    message.assets =
      object.assets !== undefined && object.assets !== null ? GiftAssets.fromPartial(object.assets) : undefined;
    message.banner =
      object.banner !== undefined && object.banner !== null ? GiftBanner2.fromPartial(object.banner) : undefined;
    message.limitation =
      object.limitation !== undefined && object.limitation !== null
        ? GiftLimit.fromPartial(object.limitation)
        : undefined;
    message.batch_type = object.batch_type ?? 0;
    message.tags = object.tags?.map(e => GiftTag1.fromPartial(e)) || [];
    message.duration =
      object.duration !== undefined && object.duration !== null ? GiftDuration.fromPartial(object.duration) : undefined;
    message.jump = object.jump !== undefined && object.jump !== null ? GiftJump.fromPartial(object.jump) : undefined;
    message.level = object.level ?? 0;
    message.published = object.published ?? false;
    message.hidden = object.hidden ?? false;
    message.deleted = object.deleted ?? false;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    message.tab_id = object.tab_id ?? 0;
    message.tag_ids = object.tag_ids?.map(e => e) || [];
    message.editor = object.editor ?? '';
    message.effect_type = object.effect_type ?? 0;
    message.valid_from = object.valid_from ?? 0;
    message.valid_to = object.valid_to ?? 0;
    message.isValid = object.isValid ?? 0;
    message.sid = object.sid ?? '';
    message.income_rate = object.income_rate ?? 0;
    message.gift_play =
      object.gift_play !== undefined && object.gift_play !== null ? GiftPlay.fromPartial(object.gift_play) : undefined;
    message.remark = object.remark ?? '';
    message.banner_id = object.banner_id ?? 0;
    message.tier = object.tier ?? 0;
    message.biz_ext = object.biz_ext ?? '';
    return message;
  }
};

function createBaseListGiftsReq(): ListGiftsReq {
  return {
    page: undefined,
    id: 0,
    name: '',
    type: 0,
    price_min: 0,
    price_max: 0,
    published: 0,
    isValid: 0,
    tab_id: 0,
    upload_res_status: 0,
    limitation: undefined,
    biz_ext_args: undefined,
    banner_id: 0
  };
}

export const ListGiftsReq: MessageFns<ListGiftsReq> = {
  fromJSON(object: any): ListGiftsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      type: isSet(object.type) ? giftTypeFromJSON(object.type) : 0,
      price_min: isSet(object.price_min) ? globalThis.Number(object.price_min) : 0,
      price_max: isSet(object.price_max) ? globalThis.Number(object.price_max) : 0,
      published: isSet(object.published) ? globalThis.Number(object.published) : 0,
      isValid: isSet(object.isValid) ? globalThis.Number(object.isValid) : 0,
      tab_id: isSet(object.tab_id) ? globalThis.Number(object.tab_id) : 0,
      upload_res_status: isSet(object.upload_res_status) ? globalThis.Number(object.upload_res_status) : 0,
      limitation: isSet(object.limitation) ? ListGiftLimitation.fromJSON(object.limitation) : undefined,
      biz_ext_args: isSet(object.biz_ext_args) ? BizExtArgs.fromJSON(object.biz_ext_args) : undefined,
      banner_id: isSet(object.banner_id) ? globalThis.Number(object.banner_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListGiftsReq>, I>>(base?: I): ListGiftsReq {
    return ListGiftsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftsReq>, I>>(object: I): ListGiftsReq {
    const message = createBaseListGiftsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.type = object.type ?? 0;
    message.price_min = object.price_min ?? 0;
    message.price_max = object.price_max ?? 0;
    message.published = object.published ?? 0;
    message.isValid = object.isValid ?? 0;
    message.tab_id = object.tab_id ?? 0;
    message.upload_res_status = object.upload_res_status ?? 0;
    message.limitation =
      object.limitation !== undefined && object.limitation !== null
        ? ListGiftLimitation.fromPartial(object.limitation)
        : undefined;
    message.biz_ext_args =
      object.biz_ext_args !== undefined && object.biz_ext_args !== null
        ? BizExtArgs.fromPartial(object.biz_ext_args)
        : undefined;
    message.banner_id = object.banner_id ?? 0;
    return message;
  }
};

function createBaseBizExtArgs(): BizExtArgs {
  return { wish_gift_type: 0 };
}

export const BizExtArgs: MessageFns<BizExtArgs> = {
  fromJSON(object: any): BizExtArgs {
    return { wish_gift_type: isSet(object.wish_gift_type) ? wishGiftTypeFromJSON(object.wish_gift_type) : 0 };
  },

  create<I extends Exact<DeepPartial<BizExtArgs>, I>>(base?: I): BizExtArgs {
    return BizExtArgs.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BizExtArgs>, I>>(object: I): BizExtArgs {
    const message = createBaseBizExtArgs();
    message.wish_gift_type = object.wish_gift_type ?? 0;
    return message;
  }
};

function createBaseListGiftsRsp(): ListGiftsRsp {
  return { list: [], page: undefined };
}

export const ListGiftsRsp: MessageFns<ListGiftsRsp> = {
  fromJSON(object: any): ListGiftsRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GiftInfo.fromJSON(e)) : [],
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftsRsp>, I>>(base?: I): ListGiftsRsp {
    return ListGiftsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftsRsp>, I>>(object: I): ListGiftsRsp {
    const message = createBaseListGiftsRsp();
    message.list = object.list?.map(e => GiftInfo.fromPartial(e)) || [];
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseAddGiftInfoReq(): AddGiftInfoReq {
  return { info: undefined };
}

export const AddGiftInfoReq: MessageFns<AddGiftInfoReq> = {
  fromJSON(object: any): AddGiftInfoReq {
    return { info: isSet(object.info) ? GiftInfo.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<AddGiftInfoReq>, I>>(base?: I): AddGiftInfoReq {
    return AddGiftInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftInfoReq>, I>>(object: I): AddGiftInfoReq {
    const message = createBaseAddGiftInfoReq();
    message.info = object.info !== undefined && object.info !== null ? GiftInfo.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseAddGiftInfoRsp(): AddGiftInfoRsp {
  return {};
}

export const AddGiftInfoRsp: MessageFns<AddGiftInfoRsp> = {
  fromJSON(_: any): AddGiftInfoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddGiftInfoRsp>, I>>(base?: I): AddGiftInfoRsp {
    return AddGiftInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftInfoRsp>, I>>(_: I): AddGiftInfoRsp {
    const message = createBaseAddGiftInfoRsp();
    return message;
  }
};

function createBaseBatchAddGiftInfoReq(): BatchAddGiftInfoReq {
  return { infos: [] };
}

export const BatchAddGiftInfoReq: MessageFns<BatchAddGiftInfoReq> = {
  fromJSON(object: any): BatchAddGiftInfoReq {
    return { infos: globalThis.Array.isArray(object?.infos) ? object.infos.map((e: any) => GiftInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchAddGiftInfoReq>, I>>(base?: I): BatchAddGiftInfoReq {
    return BatchAddGiftInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchAddGiftInfoReq>, I>>(object: I): BatchAddGiftInfoReq {
    const message = createBaseBatchAddGiftInfoReq();
    message.infos = object.infos?.map(e => GiftInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBatchAddGiftInfoRsp(): BatchAddGiftInfoRsp {
  return {};
}

export const BatchAddGiftInfoRsp: MessageFns<BatchAddGiftInfoRsp> = {
  fromJSON(_: any): BatchAddGiftInfoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchAddGiftInfoRsp>, I>>(base?: I): BatchAddGiftInfoRsp {
    return BatchAddGiftInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchAddGiftInfoRsp>, I>>(_: I): BatchAddGiftInfoRsp {
    const message = createBaseBatchAddGiftInfoRsp();
    return message;
  }
};

function createBaseUpdateGiftInfoReq(): UpdateGiftInfoReq {
  return { info: undefined };
}

export const UpdateGiftInfoReq: MessageFns<UpdateGiftInfoReq> = {
  fromJSON(object: any): UpdateGiftInfoReq {
    return { info: isSet(object.info) ? GiftInfo.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateGiftInfoReq>, I>>(base?: I): UpdateGiftInfoReq {
    return UpdateGiftInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftInfoReq>, I>>(object: I): UpdateGiftInfoReq {
    const message = createBaseUpdateGiftInfoReq();
    message.info = object.info !== undefined && object.info !== null ? GiftInfo.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseUpdateGiftInfoRsp(): UpdateGiftInfoRsp {
  return {};
}

export const UpdateGiftInfoRsp: MessageFns<UpdateGiftInfoRsp> = {
  fromJSON(_: any): UpdateGiftInfoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateGiftInfoRsp>, I>>(base?: I): UpdateGiftInfoRsp {
    return UpdateGiftInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftInfoRsp>, I>>(_: I): UpdateGiftInfoRsp {
    const message = createBaseUpdateGiftInfoRsp();
    return message;
  }
};

function createBaseGiftTab(): GiftTab {
  return {
    id: 0,
    scenes: [],
    name: '',
    i18n: {},
    sequence: 0,
    published: false,
    hidden: false,
    deleted: false,
    create_at: 0,
    update_at: 0,
    editor: '',
    gift_num: 0,
    requirements: [],
    hidden_switches: []
  };
}

export const GiftTab: MessageFns<GiftTab> = {
  fromJSON(object: any): GiftTab {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      scenes: globalThis.Array.isArray(object?.scenes) ? object.scenes.map((e: any) => giftSceneFromJSON(e)) : [],
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n: isObject(object.i18n)
        ? Object.entries(object.i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      hidden: isSet(object.hidden) ? globalThis.Boolean(object.hidden) : false,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      gift_num: isSet(object.gift_num) ? globalThis.Number(object.gift_num) : 0,
      requirements: globalThis.Array.isArray(object?.requirements)
        ? object.requirements.map((e: any) => GiftRequirement.fromJSON(e))
        : [],
      hidden_switches: globalThis.Array.isArray(object?.hidden_switches)
        ? object.hidden_switches.map((e: any) => GiftHiddenSwitch.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GiftTab>, I>>(base?: I): GiftTab {
    return GiftTab.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTab>, I>>(object: I): GiftTab {
    const message = createBaseGiftTab();
    message.id = object.id ?? 0;
    message.scenes = object.scenes?.map(e => e) || [];
    message.name = object.name ?? '';
    message.i18n = Object.entries(object.i18n ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.sequence = object.sequence ?? 0;
    message.published = object.published ?? false;
    message.hidden = object.hidden ?? false;
    message.deleted = object.deleted ?? false;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    message.editor = object.editor ?? '';
    message.gift_num = object.gift_num ?? 0;
    message.requirements = object.requirements?.map(e => GiftRequirement.fromPartial(e)) || [];
    message.hidden_switches = object.hidden_switches?.map(e => GiftHiddenSwitch.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGiftTab_I18nEntry(): GiftTab_I18nEntry {
  return { key: '', value: '' };
}

export const GiftTab_I18nEntry: MessageFns<GiftTab_I18nEntry> = {
  fromJSON(object: any): GiftTab_I18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftTab_I18nEntry>, I>>(base?: I): GiftTab_I18nEntry {
    return GiftTab_I18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTab_I18nEntry>, I>>(object: I): GiftTab_I18nEntry {
    const message = createBaseGiftTab_I18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListGiftTabReq(): ListGiftTabReq {
  return { id: 0, page: undefined };
}

export const ListGiftTabReq: MessageFns<ListGiftTabReq> = {
  fromJSON(object: any): ListGiftTabReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftTabReq>, I>>(base?: I): ListGiftTabReq {
    return ListGiftTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftTabReq>, I>>(object: I): ListGiftTabReq {
    const message = createBaseListGiftTabReq();
    message.id = object.id ?? 0;
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseListGiftTabRsp(): ListGiftTabRsp {
  return { list: [], page: undefined };
}

export const ListGiftTabRsp: MessageFns<ListGiftTabRsp> = {
  fromJSON(object: any): ListGiftTabRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GiftTab.fromJSON(e)) : [],
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftTabRsp>, I>>(base?: I): ListGiftTabRsp {
    return ListGiftTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftTabRsp>, I>>(object: I): ListGiftTabRsp {
    const message = createBaseListGiftTabRsp();
    message.list = object.list?.map(e => GiftTab.fromPartial(e)) || [];
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseUpdateGiftTabReq(): UpdateGiftTabReq {
  return { info: undefined };
}

export const UpdateGiftTabReq: MessageFns<UpdateGiftTabReq> = {
  fromJSON(object: any): UpdateGiftTabReq {
    return { info: isSet(object.info) ? GiftTab.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateGiftTabReq>, I>>(base?: I): UpdateGiftTabReq {
    return UpdateGiftTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftTabReq>, I>>(object: I): UpdateGiftTabReq {
    const message = createBaseUpdateGiftTabReq();
    message.info = object.info !== undefined && object.info !== null ? GiftTab.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseUpdateGiftTabRsp(): UpdateGiftTabRsp {
  return {};
}

export const UpdateGiftTabRsp: MessageFns<UpdateGiftTabRsp> = {
  fromJSON(_: any): UpdateGiftTabRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateGiftTabRsp>, I>>(base?: I): UpdateGiftTabRsp {
    return UpdateGiftTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftTabRsp>, I>>(_: I): UpdateGiftTabRsp {
    const message = createBaseUpdateGiftTabRsp();
    return message;
  }
};

function createBaseAddGiftTabReq(): AddGiftTabReq {
  return { info: undefined };
}

export const AddGiftTabReq: MessageFns<AddGiftTabReq> = {
  fromJSON(object: any): AddGiftTabReq {
    return { info: isSet(object.info) ? GiftTab.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<AddGiftTabReq>, I>>(base?: I): AddGiftTabReq {
    return AddGiftTabReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftTabReq>, I>>(object: I): AddGiftTabReq {
    const message = createBaseAddGiftTabReq();
    message.info = object.info !== undefined && object.info !== null ? GiftTab.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseAddGiftTabRsp(): AddGiftTabRsp {
  return {};
}

export const AddGiftTabRsp: MessageFns<AddGiftTabRsp> = {
  fromJSON(_: any): AddGiftTabRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddGiftTabRsp>, I>>(base?: I): AddGiftTabRsp {
    return AddGiftTabRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftTabRsp>, I>>(_: I): AddGiftTabRsp {
    const message = createBaseAddGiftTabRsp();
    return message;
  }
};

function createBaseGiftTag(): GiftTag {
  return {
    id: 0,
    name: '',
    icon: '',
    sequence: 0,
    deleted: false,
    create_at: 0,
    update_at: 0,
    editor: '',
    icon_i18n: {}
  };
}

export const GiftTag: MessageFns<GiftTag> = {
  fromJSON(object: any): GiftTag {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      icon: isSet(object.icon) ? globalThis.String(object.icon) : '',
      sequence: isSet(object.sequence) ? globalThis.Number(object.sequence) : 0,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : '',
      icon_i18n: isObject(object.icon_i18n)
        ? Object.entries(object.icon_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GiftTag>, I>>(base?: I): GiftTag {
    return GiftTag.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTag>, I>>(object: I): GiftTag {
    const message = createBaseGiftTag();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.icon = object.icon ?? '';
    message.sequence = object.sequence ?? 0;
    message.deleted = object.deleted ?? false;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    message.editor = object.editor ?? '';
    message.icon_i18n = Object.entries(object.icon_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGiftTag_IconI18nEntry(): GiftTag_IconI18nEntry {
  return { key: '', value: '' };
}

export const GiftTag_IconI18nEntry: MessageFns<GiftTag_IconI18nEntry> = {
  fromJSON(object: any): GiftTag_IconI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftTag_IconI18nEntry>, I>>(base?: I): GiftTag_IconI18nEntry {
    return GiftTag_IconI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftTag_IconI18nEntry>, I>>(object: I): GiftTag_IconI18nEntry {
    const message = createBaseGiftTag_IconI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListGiftTagReq(): ListGiftTagReq {
  return { id: 0, page: undefined };
}

export const ListGiftTagReq: MessageFns<ListGiftTagReq> = {
  fromJSON(object: any): ListGiftTagReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftTagReq>, I>>(base?: I): ListGiftTagReq {
    return ListGiftTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftTagReq>, I>>(object: I): ListGiftTagReq {
    const message = createBaseListGiftTagReq();
    message.id = object.id ?? 0;
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseListGiftTagRsp(): ListGiftTagRsp {
  return { list: [], page: undefined };
}

export const ListGiftTagRsp: MessageFns<ListGiftTagRsp> = {
  fromJSON(object: any): ListGiftTagRsp {
    return {
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GiftTag.fromJSON(e)) : [],
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ListGiftTagRsp>, I>>(base?: I): ListGiftTagRsp {
    return ListGiftTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftTagRsp>, I>>(object: I): ListGiftTagRsp {
    const message = createBaseListGiftTagRsp();
    message.list = object.list?.map(e => GiftTag.fromPartial(e)) || [];
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseUpdateGiftTagReq(): UpdateGiftTagReq {
  return { info: undefined };
}

export const UpdateGiftTagReq: MessageFns<UpdateGiftTagReq> = {
  fromJSON(object: any): UpdateGiftTagReq {
    return { info: isSet(object.info) ? GiftTag.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateGiftTagReq>, I>>(base?: I): UpdateGiftTagReq {
    return UpdateGiftTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftTagReq>, I>>(object: I): UpdateGiftTagReq {
    const message = createBaseUpdateGiftTagReq();
    message.info = object.info !== undefined && object.info !== null ? GiftTag.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseUpdateGiftTagRsp(): UpdateGiftTagRsp {
  return {};
}

export const UpdateGiftTagRsp: MessageFns<UpdateGiftTagRsp> = {
  fromJSON(_: any): UpdateGiftTagRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateGiftTagRsp>, I>>(base?: I): UpdateGiftTagRsp {
    return UpdateGiftTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGiftTagRsp>, I>>(_: I): UpdateGiftTagRsp {
    const message = createBaseUpdateGiftTagRsp();
    return message;
  }
};

function createBaseAddGiftTagReq(): AddGiftTagReq {
  return { info: undefined };
}

export const AddGiftTagReq: MessageFns<AddGiftTagReq> = {
  fromJSON(object: any): AddGiftTagReq {
    return { info: isSet(object.info) ? GiftTag.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<AddGiftTagReq>, I>>(base?: I): AddGiftTagReq {
    return AddGiftTagReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftTagReq>, I>>(object: I): AddGiftTagReq {
    const message = createBaseAddGiftTagReq();
    message.info = object.info !== undefined && object.info !== null ? GiftTag.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseAddGiftTagRsp(): AddGiftTagRsp {
  return {};
}

export const AddGiftTagRsp: MessageFns<AddGiftTagRsp> = {
  fromJSON(_: any): AddGiftTagRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddGiftTagRsp>, I>>(base?: I): AddGiftTagRsp {
    return AddGiftTagRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddGiftTagRsp>, I>>(_: I): AddGiftTagRsp {
    const message = createBaseAddGiftTagRsp();
    return message;
  }
};

function createBaseUpdatePublishReq(): UpdatePublishReq {
  return { id: 0, published: false, editor: '' };
}

export const UpdatePublishReq: MessageFns<UpdatePublishReq> = {
  fromJSON(object: any): UpdatePublishReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdatePublishReq>, I>>(base?: I): UpdatePublishReq {
    return UpdatePublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePublishReq>, I>>(object: I): UpdatePublishReq {
    const message = createBaseUpdatePublishReq();
    message.id = object.id ?? 0;
    message.published = object.published ?? false;
    message.editor = object.editor ?? '';
    return message;
  }
};

function createBaseUpdatePublishRsp(): UpdatePublishRsp {
  return {};
}

export const UpdatePublishRsp: MessageFns<UpdatePublishRsp> = {
  fromJSON(_: any): UpdatePublishRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdatePublishRsp>, I>>(base?: I): UpdatePublishRsp {
    return UpdatePublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePublishRsp>, I>>(_: I): UpdatePublishRsp {
    const message = createBaseUpdatePublishRsp();
    return message;
  }
};

function createBaseDeleteGiftReq(): DeleteGiftReq {
  return { id: 0, editor: '' };
}

export const DeleteGiftReq: MessageFns<DeleteGiftReq> = {
  fromJSON(object: any): DeleteGiftReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      editor: isSet(object.editor) ? globalThis.String(object.editor) : ''
    };
  },

  create<I extends Exact<DeepPartial<DeleteGiftReq>, I>>(base?: I): DeleteGiftReq {
    return DeleteGiftReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteGiftReq>, I>>(object: I): DeleteGiftReq {
    const message = createBaseDeleteGiftReq();
    message.id = object.id ?? 0;
    message.editor = object.editor ?? '';
    return message;
  }
};

function createBaseDeleteGiftRsp(): DeleteGiftRsp {
  return {};
}

export const DeleteGiftRsp: MessageFns<DeleteGiftRsp> = {
  fromJSON(_: any): DeleteGiftRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteGiftRsp>, I>>(base?: I): DeleteGiftRsp {
    return DeleteGiftRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteGiftRsp>, I>>(_: I): DeleteGiftRsp {
    const message = createBaseDeleteGiftRsp();
    return message;
  }
};

function createBaseBatchGetGiftReq(): BatchGetGiftReq {
  return { ids: [] };
}

export const BatchGetGiftReq: MessageFns<BatchGetGiftReq> = {
  fromJSON(object: any): BatchGetGiftReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftReq>, I>>(base?: I): BatchGetGiftReq {
    return BatchGetGiftReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftReq>, I>>(object: I): BatchGetGiftReq {
    const message = createBaseBatchGetGiftReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetGiftRsp(): BatchGetGiftRsp {
  return { gifts: {} };
}

export const BatchGetGiftRsp: MessageFns<BatchGetGiftRsp> = {
  fromJSON(object: any): BatchGetGiftRsp {
    return {
      gifts: isObject(object.gifts)
        ? Object.entries(object.gifts).reduce<{ [key: number]: GiftInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = GiftInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftRsp>, I>>(base?: I): BatchGetGiftRsp {
    return BatchGetGiftRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftRsp>, I>>(object: I): BatchGetGiftRsp {
    const message = createBaseBatchGetGiftRsp();
    message.gifts = Object.entries(object.gifts ?? {}).reduce<{ [key: number]: GiftInfo }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = GiftInfo.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetGiftRsp_GiftsEntry(): BatchGetGiftRsp_GiftsEntry {
  return { key: 0, value: undefined };
}

export const BatchGetGiftRsp_GiftsEntry: MessageFns<BatchGetGiftRsp_GiftsEntry> = {
  fromJSON(object: any): BatchGetGiftRsp_GiftsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? GiftInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftRsp_GiftsEntry>, I>>(base?: I): BatchGetGiftRsp_GiftsEntry {
    return BatchGetGiftRsp_GiftsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftRsp_GiftsEntry>, I>>(object: I): BatchGetGiftRsp_GiftsEntry {
    const message = createBaseBatchGetGiftRsp_GiftsEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? GiftInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseBatchGetGiftByLotteryIdReq(): BatchGetGiftByLotteryIdReq {
  return { lottery_ids: [] };
}

export const BatchGetGiftByLotteryIdReq: MessageFns<BatchGetGiftByLotteryIdReq> = {
  fromJSON(object: any): BatchGetGiftByLotteryIdReq {
    return {
      lottery_ids: globalThis.Array.isArray(object?.lottery_ids)
        ? object.lottery_ids.map((e: any) => globalThis.Number(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdReq>, I>>(base?: I): BatchGetGiftByLotteryIdReq {
    return BatchGetGiftByLotteryIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdReq>, I>>(object: I): BatchGetGiftByLotteryIdReq {
    const message = createBaseBatchGetGiftByLotteryIdReq();
    message.lottery_ids = object.lottery_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetGiftByLotteryIdRsp(): BatchGetGiftByLotteryIdRsp {
  return { gifts: {} };
}

export const BatchGetGiftByLotteryIdRsp: MessageFns<BatchGetGiftByLotteryIdRsp> = {
  fromJSON(object: any): BatchGetGiftByLotteryIdRsp {
    return {
      gifts: isObject(object.gifts)
        ? Object.entries(object.gifts).reduce<{ [key: number]: GiftInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = GiftInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdRsp>, I>>(base?: I): BatchGetGiftByLotteryIdRsp {
    return BatchGetGiftByLotteryIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdRsp>, I>>(object: I): BatchGetGiftByLotteryIdRsp {
    const message = createBaseBatchGetGiftByLotteryIdRsp();
    message.gifts = Object.entries(object.gifts ?? {}).reduce<{ [key: number]: GiftInfo }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[globalThis.Number(key)] = GiftInfo.fromPartial(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseBatchGetGiftByLotteryIdRsp_GiftsEntry(): BatchGetGiftByLotteryIdRsp_GiftsEntry {
  return { key: 0, value: undefined };
}

export const BatchGetGiftByLotteryIdRsp_GiftsEntry: MessageFns<BatchGetGiftByLotteryIdRsp_GiftsEntry> = {
  fromJSON(object: any): BatchGetGiftByLotteryIdRsp_GiftsEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? GiftInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdRsp_GiftsEntry>, I>>(
    base?: I
  ): BatchGetGiftByLotteryIdRsp_GiftsEntry {
    return BatchGetGiftByLotteryIdRsp_GiftsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetGiftByLotteryIdRsp_GiftsEntry>, I>>(
    object: I
  ): BatchGetGiftByLotteryIdRsp_GiftsEntry {
    const message = createBaseBatchGetGiftByLotteryIdRsp_GiftsEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? GiftInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseGiftRecordInfo(): GiftRecordInfo {
  return {
    id: 0,
    gift_info: undefined,
    quantity: 0,
    sender_uid: 0,
    receiver_uid: 0,
    time_sent: 0,
    room_id: 0,
    batch_type: 0,
    profit_currency_type: 0,
    profit_amount: 0
  };
}

export const GiftRecordInfo: MessageFns<GiftRecordInfo> = {
  fromJSON(object: any): GiftRecordInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      gift_info: isSet(object.gift_info) ? GiftInfo.fromJSON(object.gift_info) : undefined,
      quantity: isSet(object.quantity) ? globalThis.Number(object.quantity) : 0,
      sender_uid: isSet(object.sender_uid) ? globalThis.Number(object.sender_uid) : 0,
      receiver_uid: isSet(object.receiver_uid) ? globalThis.Number(object.receiver_uid) : 0,
      time_sent: isSet(object.time_sent) ? globalThis.Number(object.time_sent) : 0,
      room_id: isSet(object.room_id) ? globalThis.Number(object.room_id) : 0,
      batch_type: isSet(object.batch_type) ? giftBatchTypeFromJSON(object.batch_type) : 0,
      profit_currency_type: isSet(object.profit_currency_type) ? currencyTypeFromJSON(object.profit_currency_type) : 0,
      profit_amount: isSet(object.profit_amount) ? globalThis.Number(object.profit_amount) : 0
    };
  },

  create<I extends Exact<DeepPartial<GiftRecordInfo>, I>>(base?: I): GiftRecordInfo {
    return GiftRecordInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftRecordInfo>, I>>(object: I): GiftRecordInfo {
    const message = createBaseGiftRecordInfo();
    message.id = object.id ?? 0;
    message.gift_info =
      object.gift_info !== undefined && object.gift_info !== null ? GiftInfo.fromPartial(object.gift_info) : undefined;
    message.quantity = object.quantity ?? 0;
    message.sender_uid = object.sender_uid ?? 0;
    message.receiver_uid = object.receiver_uid ?? 0;
    message.time_sent = object.time_sent ?? 0;
    message.room_id = object.room_id ?? 0;
    message.batch_type = object.batch_type ?? 0;
    message.profit_currency_type = object.profit_currency_type ?? 0;
    message.profit_amount = object.profit_amount ?? 0;
    return message;
  }
};

function createBaseSearchGiftRecordReq(): SearchGiftRecordReq {
  return { page: undefined, gift_kind: 0, uids: [], room_ids: [], gift_ids: [], min_time_sent: 0, max_time_sent: 0 };
}

export const SearchGiftRecordReq: MessageFns<SearchGiftRecordReq> = {
  fromJSON(object: any): SearchGiftRecordReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      gift_kind: isSet(object.gift_kind) ? giftKindFromJSON(object.gift_kind) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      room_ids: globalThis.Array.isArray(object?.room_ids) ? object.room_ids.map((e: any) => globalThis.String(e)) : [],
      gift_ids: globalThis.Array.isArray(object?.gift_ids) ? object.gift_ids.map((e: any) => globalThis.Number(e)) : [],
      min_time_sent: isSet(object.min_time_sent) ? globalThis.Number(object.min_time_sent) : 0,
      max_time_sent: isSet(object.max_time_sent) ? globalThis.Number(object.max_time_sent) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchGiftRecordReq>, I>>(base?: I): SearchGiftRecordReq {
    return SearchGiftRecordReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchGiftRecordReq>, I>>(object: I): SearchGiftRecordReq {
    const message = createBaseSearchGiftRecordReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.gift_kind = object.gift_kind ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.room_ids = object.room_ids?.map(e => e) || [];
    message.gift_ids = object.gift_ids?.map(e => e) || [];
    message.min_time_sent = object.min_time_sent ?? 0;
    message.max_time_sent = object.max_time_sent ?? 0;
    return message;
  }
};

function createBaseSearchGiftRecordRsp(): SearchGiftRecordRsp {
  return { page: undefined, record_infos: [] };
}

export const SearchGiftRecordRsp: MessageFns<SearchGiftRecordRsp> = {
  fromJSON(object: any): SearchGiftRecordRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      record_infos: globalThis.Array.isArray(object?.record_infos)
        ? object.record_infos.map((e: any) => GiftRecordInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchGiftRecordRsp>, I>>(base?: I): SearchGiftRecordRsp {
    return SearchGiftRecordRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchGiftRecordRsp>, I>>(object: I): SearchGiftRecordRsp {
    const message = createBaseSearchGiftRecordRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.record_infos = object.record_infos?.map(e => GiftRecordInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGiftBanner(): GiftBanner {
  return {
    name: '',
    link: '',
    images: '',
    width: 0,
    height: 0,
    is_delete: 0,
    start_time: 0,
    end_time: 0,
    is_online: 0,
    remark: '',
    weight: 0,
    create_at: 0,
    update_at: 0,
    creator: '',
    modifier: '',
    id: 0,
    gift_id: [],
    jump_type: 0,
    image_i18n: {}
  };
}

export const GiftBanner: MessageFns<GiftBanner> = {
  fromJSON(object: any): GiftBanner {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      link: isSet(object.link) ? globalThis.String(object.link) : '',
      images: isSet(object.images) ? globalThis.String(object.images) : '',
      width: isSet(object.width) ? globalThis.Number(object.width) : 0,
      height: isSet(object.height) ? globalThis.Number(object.height) : 0,
      is_delete: isSet(object.is_delete) ? globalThis.Number(object.is_delete) : 0,
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      is_online: isSet(object.is_online) ? globalThis.Number(object.is_online) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      weight: isSet(object.weight) ? globalThis.Number(object.weight) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      modifier: isSet(object.modifier) ? globalThis.String(object.modifier) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      gift_id: globalThis.Array.isArray(object?.gift_id) ? object.gift_id.map((e: any) => globalThis.Number(e)) : [],
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      image_i18n: isObject(object.image_i18n)
        ? Object.entries(object.image_i18n).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GiftBanner>, I>>(base?: I): GiftBanner {
    return GiftBanner.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBanner>, I>>(object: I): GiftBanner {
    const message = createBaseGiftBanner();
    message.name = object.name ?? '';
    message.link = object.link ?? '';
    message.images = object.images ?? '';
    message.width = object.width ?? 0;
    message.height = object.height ?? 0;
    message.is_delete = object.is_delete ?? 0;
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.is_online = object.is_online ?? 0;
    message.remark = object.remark ?? '';
    message.weight = object.weight ?? 0;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    message.creator = object.creator ?? '';
    message.modifier = object.modifier ?? '';
    message.id = object.id ?? 0;
    message.gift_id = object.gift_id?.map(e => e) || [];
    message.jump_type = object.jump_type ?? 0;
    message.image_i18n = Object.entries(object.image_i18n ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGiftBanner_ImageI18nEntry(): GiftBanner_ImageI18nEntry {
  return { key: '', value: '' };
}

export const GiftBanner_ImageI18nEntry: MessageFns<GiftBanner_ImageI18nEntry> = {
  fromJSON(object: any): GiftBanner_ImageI18nEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GiftBanner_ImageI18nEntry>, I>>(base?: I): GiftBanner_ImageI18nEntry {
    return GiftBanner_ImageI18nEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GiftBanner_ImageI18nEntry>, I>>(object: I): GiftBanner_ImageI18nEntry {
    const message = createBaseGiftBanner_ImageI18nEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCreateBannerReq(): CreateBannerReq {
  return { data: undefined };
}

export const CreateBannerReq: MessageFns<CreateBannerReq> = {
  fromJSON(object: any): CreateBannerReq {
    return { data: isSet(object.data) ? GiftBanner.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateBannerReq>, I>>(base?: I): CreateBannerReq {
    return CreateBannerReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBannerReq>, I>>(object: I): CreateBannerReq {
    const message = createBaseCreateBannerReq();
    message.data = object.data !== undefined && object.data !== null ? GiftBanner.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseCreateBannerRsp(): CreateBannerRsp {
  return { id: 0 };
}

export const CreateBannerRsp: MessageFns<CreateBannerRsp> = {
  fromJSON(object: any): CreateBannerRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateBannerRsp>, I>>(base?: I): CreateBannerRsp {
    return CreateBannerRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBannerRsp>, I>>(object: I): CreateBannerRsp {
    const message = createBaseCreateBannerRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateBannerReq(): UpdateBannerReq {
  return { data: undefined };
}

export const UpdateBannerReq: MessageFns<UpdateBannerReq> = {
  fromJSON(object: any): UpdateBannerReq {
    return { data: isSet(object.data) ? GiftBanner.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateBannerReq>, I>>(base?: I): UpdateBannerReq {
    return UpdateBannerReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBannerReq>, I>>(object: I): UpdateBannerReq {
    const message = createBaseUpdateBannerReq();
    message.data = object.data !== undefined && object.data !== null ? GiftBanner.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseUpdateBannerRsp(): UpdateBannerRsp {
  return {};
}

export const UpdateBannerRsp: MessageFns<UpdateBannerRsp> = {
  fromJSON(_: any): UpdateBannerRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateBannerRsp>, I>>(base?: I): UpdateBannerRsp {
    return UpdateBannerRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateBannerRsp>, I>>(_: I): UpdateBannerRsp {
    const message = createBaseUpdateBannerRsp();
    return message;
  }
};

function createBaseDeleteBannerReq(): DeleteBannerReq {
  return { id: 0 };
}

export const DeleteBannerReq: MessageFns<DeleteBannerReq> = {
  fromJSON(object: any): DeleteBannerReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteBannerReq>, I>>(base?: I): DeleteBannerReq {
    return DeleteBannerReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBannerReq>, I>>(object: I): DeleteBannerReq {
    const message = createBaseDeleteBannerReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteBannerRsp(): DeleteBannerRsp {
  return {};
}

export const DeleteBannerRsp: MessageFns<DeleteBannerRsp> = {
  fromJSON(_: any): DeleteBannerRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteBannerRsp>, I>>(base?: I): DeleteBannerRsp {
    return DeleteBannerRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteBannerRsp>, I>>(_: I): DeleteBannerRsp {
    const message = createBaseDeleteBannerRsp();
    return message;
  }
};

function createBaseListBannerReq(): ListBannerReq {
  return { page: undefined, name: '', is_online: 0, is_valid: 0, banner_id: 0 };
}

export const ListBannerReq: MessageFns<ListBannerReq> = {
  fromJSON(object: any): ListBannerReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      is_online: isSet(object.is_online) ? globalThis.Number(object.is_online) : 0,
      is_valid: isSet(object.is_valid) ? globalThis.Number(object.is_valid) : 0,
      banner_id: isSet(object.banner_id) ? globalThis.Number(object.banner_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBannerReq>, I>>(base?: I): ListBannerReq {
    return ListBannerReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBannerReq>, I>>(object: I): ListBannerReq {
    const message = createBaseListBannerReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.name = object.name ?? '';
    message.is_online = object.is_online ?? 0;
    message.is_valid = object.is_valid ?? 0;
    message.banner_id = object.banner_id ?? 0;
    return message;
  }
};

function createBaseListBannerRsp(): ListBannerRsp {
  return { page: undefined, list: [] };
}

export const ListBannerRsp: MessageFns<ListBannerRsp> = {
  fromJSON(object: any): ListBannerRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GiftBanner.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListBannerRsp>, I>>(base?: I): ListBannerRsp {
    return ListBannerRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBannerRsp>, I>>(object: I): ListBannerRsp {
    const message = createBaseListBannerRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GiftBanner.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetGiftOfflineResInfoReq(): GetGiftOfflineResInfoReq {
  return {};
}

export const GetGiftOfflineResInfoReq: MessageFns<GetGiftOfflineResInfoReq> = {
  fromJSON(_: any): GetGiftOfflineResInfoReq {
    return {};
  },

  create<I extends Exact<DeepPartial<GetGiftOfflineResInfoReq>, I>>(base?: I): GetGiftOfflineResInfoReq {
    return GetGiftOfflineResInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftOfflineResInfoReq>, I>>(_: I): GetGiftOfflineResInfoReq {
    const message = createBaseGetGiftOfflineResInfoReq();
    return message;
  }
};

function createBaseGetGiftOfflineResInfoRsp(): GetGiftOfflineResInfoRsp {
  return { total: 0 };
}

export const GetGiftOfflineResInfoRsp: MessageFns<GetGiftOfflineResInfoRsp> = {
  fromJSON(object: any): GetGiftOfflineResInfoRsp {
    return { total: isSet(object.total) ? globalThis.Number(object.total) : 0 };
  },

  create<I extends Exact<DeepPartial<GetGiftOfflineResInfoRsp>, I>>(base?: I): GetGiftOfflineResInfoRsp {
    return GetGiftOfflineResInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetGiftOfflineResInfoRsp>, I>>(object: I): GetGiftOfflineResInfoRsp {
    const message = createBaseGetGiftOfflineResInfoRsp();
    message.total = object.total ?? 0;
    return message;
  }
};

function createBaseListGiftLimitation(): ListGiftLimitation {
  return { type: 0, min_level: 0, max_level: 0 };
}

export const ListGiftLimitation: MessageFns<ListGiftLimitation> = {
  fromJSON(object: any): ListGiftLimitation {
    return {
      type: isSet(object.type) ? giftLimitTypeFromJSON(object.type) : 0,
      min_level: isSet(object.min_level) ? globalThis.Number(object.min_level) : 0,
      max_level: isSet(object.max_level) ? globalThis.Number(object.max_level) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListGiftLimitation>, I>>(base?: I): ListGiftLimitation {
    return ListGiftLimitation.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListGiftLimitation>, I>>(object: I): ListGiftLimitation {
    const message = createBaseListGiftLimitation();
    message.type = object.type ?? 0;
    message.min_level = object.min_level ?? 0;
    message.max_level = object.max_level ?? 0;
    return message;
  }
};

function createBaseCheckPasteSimilarGiftReq(): CheckPasteSimilarGiftReq {
  return { gifts: [] };
}

export const CheckPasteSimilarGiftReq: MessageFns<CheckPasteSimilarGiftReq> = {
  fromJSON(object: any): CheckPasteSimilarGiftReq {
    return { gifts: globalThis.Array.isArray(object?.gifts) ? object.gifts.map((e: any) => GiftInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<CheckPasteSimilarGiftReq>, I>>(base?: I): CheckPasteSimilarGiftReq {
    return CheckPasteSimilarGiftReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckPasteSimilarGiftReq>, I>>(object: I): CheckPasteSimilarGiftReq {
    const message = createBaseCheckPasteSimilarGiftReq();
    message.gifts = object.gifts?.map(e => GiftInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCheckPasteSimilarGiftRsp(): CheckPasteSimilarGiftRsp {
  return { gifts: [] };
}

export const CheckPasteSimilarGiftRsp: MessageFns<CheckPasteSimilarGiftRsp> = {
  fromJSON(object: any): CheckPasteSimilarGiftRsp {
    return { gifts: globalThis.Array.isArray(object?.gifts) ? object.gifts.map((e: any) => GiftInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<CheckPasteSimilarGiftRsp>, I>>(base?: I): CheckPasteSimilarGiftRsp {
    return CheckPasteSimilarGiftRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CheckPasteSimilarGiftRsp>, I>>(object: I): CheckPasteSimilarGiftRsp {
    const message = createBaseCheckPasteSimilarGiftRsp();
    message.gifts = object.gifts?.map(e => GiftInfo.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 礼物服务
 * smicro:spath=gitit.cc/social/components-service/social-revenue/handler-mgr/mgrgift
 */
export type MgrGiftDefinition = typeof MgrGiftDefinition;
export const MgrGiftDefinition = {
  name: 'MgrGift',
  fullName: 'mgr.gift.MgrGift',
  methods: {
    /** 获取礼物列表 */
    listGifts: {
      name: 'ListGifts',
      requestType: ListGiftsReq,
      requestStream: false,
      responseType: ListGiftsRsp,
      responseStream: false,
      options: {}
    },
    addGift: {
      name: 'AddGift',
      requestType: AddGiftInfoReq,
      requestStream: false,
      responseType: AddGiftInfoRsp,
      responseStream: false,
      options: {}
    },
    batchAddGift: {
      name: 'BatchAddGift',
      requestType: BatchAddGiftInfoReq,
      requestStream: false,
      responseType: BatchAddGiftInfoRsp,
      responseStream: false,
      options: {}
    },
    updateGift: {
      name: 'UpdateGift',
      requestType: UpdateGiftInfoReq,
      requestStream: false,
      responseType: UpdateGiftInfoRsp,
      responseStream: false,
      options: {}
    },
    listGiftTab: {
      name: 'ListGiftTab',
      requestType: ListGiftTabReq,
      requestStream: false,
      responseType: ListGiftTabRsp,
      responseStream: false,
      options: {}
    },
    updateGiftTab: {
      name: 'UpdateGiftTab',
      requestType: UpdateGiftTabReq,
      requestStream: false,
      responseType: UpdateGiftTabRsp,
      responseStream: false,
      options: {}
    },
    addGiftTab: {
      name: 'AddGiftTab',
      requestType: AddGiftTabReq,
      requestStream: false,
      responseType: AddGiftTabRsp,
      responseStream: false,
      options: {}
    },
    listGiftTag: {
      name: 'ListGiftTag',
      requestType: ListGiftTagReq,
      requestStream: false,
      responseType: ListGiftTagRsp,
      responseStream: false,
      options: {}
    },
    addGiftTag: {
      name: 'AddGiftTag',
      requestType: AddGiftTagReq,
      requestStream: false,
      responseType: AddGiftTagRsp,
      responseStream: false,
      options: {}
    },
    updateGiftTag: {
      name: 'UpdateGiftTag',
      requestType: UpdateGiftTagReq,
      requestStream: false,
      responseType: UpdateGiftTagRsp,
      responseStream: false,
      options: {}
    },
    updatePublish: {
      name: 'UpdatePublish',
      requestType: UpdatePublishReq,
      requestStream: false,
      responseType: UpdatePublishRsp,
      responseStream: false,
      options: {}
    },
    deleteGift: {
      name: 'DeleteGift',
      requestType: DeleteGiftReq,
      requestStream: false,
      responseType: DeleteGiftRsp,
      responseStream: false,
      options: {}
    },
    batchGetGift: {
      name: 'BatchGetGift',
      requestType: BatchGetGiftReq,
      requestStream: false,
      responseType: BatchGetGiftRsp,
      responseStream: false,
      options: {}
    },
    batchGetGiftByLotteryId: {
      name: 'BatchGetGiftByLotteryId',
      requestType: BatchGetGiftByLotteryIdReq,
      requestStream: false,
      responseType: BatchGetGiftByLotteryIdRsp,
      responseStream: false,
      options: {}
    },
    searchGiftRecord: {
      name: 'SearchGiftRecord',
      requestType: SearchGiftRecordReq,
      requestStream: false,
      responseType: SearchGiftRecordRsp,
      responseStream: false,
      options: {}
    },
    /** 创建礼物banner */
    createBanner: {
      name: 'CreateBanner',
      requestType: CreateBannerReq,
      requestStream: false,
      responseType: CreateBannerRsp,
      responseStream: false,
      options: {}
    },
    /** 更新礼物banner */
    updateBanner: {
      name: 'UpdateBanner',
      requestType: UpdateBannerReq,
      requestStream: false,
      responseType: UpdateBannerRsp,
      responseStream: false,
      options: {}
    },
    /** 删除礼物banner */
    deleteBanner: {
      name: 'DeleteBanner',
      requestType: DeleteBannerReq,
      requestStream: false,
      responseType: DeleteBannerRsp,
      responseStream: false,
      options: {}
    },
    /** 礼物banner */
    listBanner: {
      name: 'ListBanner',
      requestType: ListBannerReq,
      requestStream: false,
      responseType: ListBannerRsp,
      responseStream: false,
      options: {}
    },
    /** 离线资源信息查询 */
    getGiftOfflineResInfo: {
      name: 'GetGiftOfflineResInfo',
      requestType: GetGiftOfflineResInfoReq,
      requestStream: false,
      responseType: GetGiftOfflineResInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 检查复制粘贴相识礼物-用礼物名称判断相似 */
    checkPasteSimilarGift: {
      name: 'CheckPasteSimilarGift',
      requestType: CheckPasteSimilarGiftReq,
      requestStream: false,
      responseType: CheckPasteSimilarGiftRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
