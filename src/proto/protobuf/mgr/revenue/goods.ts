// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/goods.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import {
  ClientType,
  clientTypeFromJSON,
  CurrencyType,
  currencyTypeFromJSON,
  GoodsLimitType,
  goodsLimitTypeFromJSON,
  GoodsStyle,
  GoodsType,
  goodsTypeFromJSON,
  PackageItem,
  StringList,
  Unit,
  unitFromJSON
} from '../../api/revenue/common';

export const protobufPackage = 'mgr.pbrevenue';

export interface SaveGoodsReq {
  goods_info: GoodsInfoMgr | undefined;
  /** 删除的支付渠道 */
  del_pay_types: PayType[];
  /** 删除的支付子渠道 */
  del_pay_type_subs: PayTypeSub[];
}

export interface SaveGoodsRsp {
  /** 新增的商品ID */
  goods_id: number;
}

export interface UpdateGoodsStatusReq {
  /** 商品ID */
  goods_id: number;
  /** 更新状态 */
  status: string;
  /** 支持批量操作，兼容单个id的协议，这里用了另外一个字段 */
  goods_ids: number[];
  /** 操作人 */
  operator: string;
}

export interface UpdateGoodsStatusRsp {}

export interface GoodsPageQueryReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 商品id，支持模糊搜索 */
  goods_id: string;
  /** 支付方式 */
  pay_type: string;
  /** 商品类型，如：虚拟币，包裹 */
  goods_type: GoodsType;
  /** 根据sku_id查询 */
  sku_id: string;
  /** disable-下架，normal-上架 */
  status: string;
  /** 过滤参数配置，多个参数是与的关系 */
  param_filter: { [key: string]: StringList };
}

export interface GoodsPageQueryReq_ParamFilterEntry {
  key: string;
  value: StringList | undefined;
}

export interface GoodsPageQueryRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 数据列表 */
  list: GoodsInfoMgr[];
}

export interface GoodsInfoMgr {
  /** 商品ID */
  goods_id: number;
  /** 商品类型，如：虚拟币，包裹 */
  goods_type: GoodsType;
  /** deprecated */
  cou: string;
  /** deprecated */
  client_type: ClientType;
  /** deprecated */
  pay_type: string;
  /** deprecated */
  pay_code: string;
  /** deprecated */
  sku_id: string;
  /** 商品名称，多语言 */
  name_lang: { [key: string]: string };
  /** 商品描述，多语言 */
  desc_lang: { [key: string]: string };
  /** 必须，商品图片 */
  images: string;
  /** 非必须，角标图片 */
  tag_images: string;
  /** 非必须，角标 */
  tag_lang: { [key: string]: string };
  /** 必须，支付金额（分，美元就是美分），注意坐下换算 */
  price: number;
  /** 支付金额单位，如：美元（USD），印尼盾(IDR)，新的单位需要增加对应枚举 */
  unit: Unit;
  /** 获得虚拟币金额 */
  amount: number;
  /** 获得金额货币类型 */
  currency_type: CurrencyType;
  /** 非必须，备注 */
  remark: string;
  /** 状态：normal disable del */
  status: string;
  /** 创建时间，查询数据时返回，新增修改不用传 */
  create_at: number;
  /** 创建人，查询数据时返回，新增修改不用传 */
  create_by: string;
  /** 更新时间，查询数据时返回，新增修改不用传 */
  update_at: number;
  /** 更新人，查询数据时返回，新增修改不用传 */
  update_by: string;
  /** 折扣百分比，eg: 10 即 10%，即1折，等于0为无折扣，非 0% */
  discount_rate: number;
  /** 奖励包ID */
  package_id: string;
  /** 限制购买类型 */
  limit_type: GoodsLimitType;
  /** 显示样式 */
  style: GoodsStyle | undefined;
  /** 礼包信息 */
  package_item: PackageItem | undefined;
  /** 额外赠送数量 */
  additional_num: number;
  /** deprecated */
  target_price: number;
  /** deprecated */
  target_unit: Unit;
  /** 支付渠道配置 */
  pay_types: PayType[];
  /** 白名单，指定用户可见 */
  white_list: number[];
  /** 白名单开关, 0:关, 1:开 */
  white_list_switch: boolean;
  /** 币商等级列表 */
  agency_levels: number[];
  /** e.g. key: scene, values: ["vip1", "vip2"] */
  param_filter: { [key: string]: StringList };
}

export interface GoodsInfoMgr_NameLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfoMgr_DescLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfoMgr_TagLangEntry {
  key: string;
  value: string;
}

export interface GoodsInfoMgr_ParamFilterEntry {
  key: string;
  value: StringList | undefined;
}

export interface PayType {
  /** id为空表征新增，不为空表征修改 */
  id: number;
  /** 客户端类型 */
  client_type: ClientType;
  /** 支付渠道枚举，google_pay apple_pay quarkpay 等 */
  pay_type: string;
  /** 必须，支付渠道定义的 sku_id，需要产品在支付平台配置的sku一致 */
  sku_id: string;
  /** 支付子渠道，用来配置某个支付渠道下属的子渠道信息 */
  pay_type_subs: PayTypeSub[];
  /** 排序，权重的概念，大值优先 */
  sort: number;
}

export interface PayTypeSub {
  /** id为空表征新增，不为空表征修改 */
  id: number;
  /** 国家 */
  cou: string;
  /** 支付子渠道，e.g. CARD（银行卡），STCPAY（钱包），中台支付接口定义，参考：https://nemo.yuque.com/be/mmszip/mb0hzzhhgr1zhy98#qqPCn */
  pay_code: string;
  /** 实际付款金额单位 */
  target_unit: Unit;
  /** 实际付款金额，如果配置了则使用该值，否则使用 GoodsInfo.Price */
  target_price: number;
  /** 排序，权重的概念，大值优先 */
  sort: number;
}

function createBaseSaveGoodsReq(): SaveGoodsReq {
  return { goods_info: undefined, del_pay_types: [], del_pay_type_subs: [] };
}

export const SaveGoodsReq: MessageFns<SaveGoodsReq> = {
  fromJSON(object: any): SaveGoodsReq {
    return {
      goods_info: isSet(object.goods_info) ? GoodsInfoMgr.fromJSON(object.goods_info) : undefined,
      del_pay_types: globalThis.Array.isArray(object?.del_pay_types)
        ? object.del_pay_types.map((e: any) => PayType.fromJSON(e))
        : [],
      del_pay_type_subs: globalThis.Array.isArray(object?.del_pay_type_subs)
        ? object.del_pay_type_subs.map((e: any) => PayTypeSub.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SaveGoodsReq>, I>>(base?: I): SaveGoodsReq {
    return SaveGoodsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveGoodsReq>, I>>(object: I): SaveGoodsReq {
    const message = createBaseSaveGoodsReq();
    message.goods_info =
      object.goods_info !== undefined && object.goods_info !== null
        ? GoodsInfoMgr.fromPartial(object.goods_info)
        : undefined;
    message.del_pay_types = object.del_pay_types?.map(e => PayType.fromPartial(e)) || [];
    message.del_pay_type_subs = object.del_pay_type_subs?.map(e => PayTypeSub.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSaveGoodsRsp(): SaveGoodsRsp {
  return { goods_id: 0 };
}

export const SaveGoodsRsp: MessageFns<SaveGoodsRsp> = {
  fromJSON(object: any): SaveGoodsRsp {
    return { goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0 };
  },

  create<I extends Exact<DeepPartial<SaveGoodsRsp>, I>>(base?: I): SaveGoodsRsp {
    return SaveGoodsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SaveGoodsRsp>, I>>(object: I): SaveGoodsRsp {
    const message = createBaseSaveGoodsRsp();
    message.goods_id = object.goods_id ?? 0;
    return message;
  }
};

function createBaseUpdateGoodsStatusReq(): UpdateGoodsStatusReq {
  return { goods_id: 0, status: '', goods_ids: [], operator: '' };
}

export const UpdateGoodsStatusReq: MessageFns<UpdateGoodsStatusReq> = {
  fromJSON(object: any): UpdateGoodsStatusReq {
    return {
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      goods_ids: globalThis.Array.isArray(object?.goods_ids)
        ? object.goods_ids.map((e: any) => globalThis.Number(e))
        : [],
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateGoodsStatusReq>, I>>(base?: I): UpdateGoodsStatusReq {
    return UpdateGoodsStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGoodsStatusReq>, I>>(object: I): UpdateGoodsStatusReq {
    const message = createBaseUpdateGoodsStatusReq();
    message.goods_id = object.goods_id ?? 0;
    message.status = object.status ?? '';
    message.goods_ids = object.goods_ids?.map(e => e) || [];
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseUpdateGoodsStatusRsp(): UpdateGoodsStatusRsp {
  return {};
}

export const UpdateGoodsStatusRsp: MessageFns<UpdateGoodsStatusRsp> = {
  fromJSON(_: any): UpdateGoodsStatusRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateGoodsStatusRsp>, I>>(base?: I): UpdateGoodsStatusRsp {
    return UpdateGoodsStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateGoodsStatusRsp>, I>>(_: I): UpdateGoodsStatusRsp {
    const message = createBaseUpdateGoodsStatusRsp();
    return message;
  }
};

function createBaseGoodsPageQueryReq(): GoodsPageQueryReq {
  return { page: undefined, goods_id: '', pay_type: '', goods_type: 0, sku_id: '', status: '', param_filter: {} };
}

export const GoodsPageQueryReq: MessageFns<GoodsPageQueryReq> = {
  fromJSON(object: any): GoodsPageQueryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      goods_id: isSet(object.goods_id) ? globalThis.String(object.goods_id) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      goods_type: isSet(object.goods_type) ? goodsTypeFromJSON(object.goods_type) : 0,
      sku_id: isSet(object.sku_id) ? globalThis.String(object.sku_id) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      param_filter: isObject(object.param_filter)
        ? Object.entries(object.param_filter).reduce<{ [key: string]: StringList }>((acc, [key, value]) => {
            acc[key] = StringList.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GoodsPageQueryReq>, I>>(base?: I): GoodsPageQueryReq {
    return GoodsPageQueryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsPageQueryReq>, I>>(object: I): GoodsPageQueryReq {
    const message = createBaseGoodsPageQueryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.goods_id = object.goods_id ?? '';
    message.pay_type = object.pay_type ?? '';
    message.goods_type = object.goods_type ?? 0;
    message.sku_id = object.sku_id ?? '';
    message.status = object.status ?? '';
    message.param_filter = Object.entries(object.param_filter ?? {}).reduce<{ [key: string]: StringList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = StringList.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGoodsPageQueryReq_ParamFilterEntry(): GoodsPageQueryReq_ParamFilterEntry {
  return { key: '', value: undefined };
}

export const GoodsPageQueryReq_ParamFilterEntry: MessageFns<GoodsPageQueryReq_ParamFilterEntry> = {
  fromJSON(object: any): GoodsPageQueryReq_ParamFilterEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? StringList.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GoodsPageQueryReq_ParamFilterEntry>, I>>(
    base?: I
  ): GoodsPageQueryReq_ParamFilterEntry {
    return GoodsPageQueryReq_ParamFilterEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsPageQueryReq_ParamFilterEntry>, I>>(
    object: I
  ): GoodsPageQueryReq_ParamFilterEntry {
    const message = createBaseGoodsPageQueryReq_ParamFilterEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? StringList.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseGoodsPageQueryRsp(): GoodsPageQueryRsp {
  return { page: undefined, list: [] };
}

export const GoodsPageQueryRsp: MessageFns<GoodsPageQueryRsp> = {
  fromJSON(object: any): GoodsPageQueryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => GoodsInfoMgr.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GoodsPageQueryRsp>, I>>(base?: I): GoodsPageQueryRsp {
    return GoodsPageQueryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsPageQueryRsp>, I>>(object: I): GoodsPageQueryRsp {
    const message = createBaseGoodsPageQueryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => GoodsInfoMgr.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGoodsInfoMgr(): GoodsInfoMgr {
  return {
    goods_id: 0,
    goods_type: 0,
    cou: '',
    client_type: 0,
    pay_type: '',
    pay_code: '',
    sku_id: '',
    name_lang: {},
    desc_lang: {},
    images: '',
    tag_images: '',
    tag_lang: {},
    price: 0,
    unit: 0,
    amount: 0,
    currency_type: 0,
    remark: '',
    status: '',
    create_at: 0,
    create_by: '',
    update_at: 0,
    update_by: '',
    discount_rate: 0,
    package_id: '',
    limit_type: 0,
    style: undefined,
    package_item: undefined,
    additional_num: 0,
    target_price: 0,
    target_unit: 0,
    pay_types: [],
    white_list: [],
    white_list_switch: false,
    agency_levels: [],
    param_filter: {}
  };
}

export const GoodsInfoMgr: MessageFns<GoodsInfoMgr> = {
  fromJSON(object: any): GoodsInfoMgr {
    return {
      goods_id: isSet(object.goods_id) ? globalThis.Number(object.goods_id) : 0,
      goods_type: isSet(object.goods_type) ? goodsTypeFromJSON(object.goods_type) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      client_type: isSet(object.client_type) ? clientTypeFromJSON(object.client_type) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      sku_id: isSet(object.sku_id) ? globalThis.String(object.sku_id) : '',
      name_lang: isObject(object.name_lang)
        ? Object.entries(object.name_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      desc_lang: isObject(object.desc_lang)
        ? Object.entries(object.desc_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      images: isSet(object.images) ? globalThis.String(object.images) : '',
      tag_images: isSet(object.tag_images) ? globalThis.String(object.tag_images) : '',
      tag_lang: isObject(object.tag_lang)
        ? Object.entries(object.tag_lang).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      price: isSet(object.price) ? globalThis.Number(object.price) : 0,
      unit: isSet(object.unit) ? unitFromJSON(object.unit) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      currency_type: isSet(object.currency_type) ? currencyTypeFromJSON(object.currency_type) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      discount_rate: isSet(object.discount_rate) ? globalThis.Number(object.discount_rate) : 0,
      package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '',
      limit_type: isSet(object.limit_type) ? goodsLimitTypeFromJSON(object.limit_type) : 0,
      style: isSet(object.style) ? GoodsStyle.fromJSON(object.style) : undefined,
      package_item: isSet(object.package_item) ? PackageItem.fromJSON(object.package_item) : undefined,
      additional_num: isSet(object.additional_num) ? globalThis.Number(object.additional_num) : 0,
      target_price: isSet(object.target_price) ? globalThis.Number(object.target_price) : 0,
      target_unit: isSet(object.target_unit) ? unitFromJSON(object.target_unit) : 0,
      pay_types: globalThis.Array.isArray(object?.pay_types)
        ? object.pay_types.map((e: any) => PayType.fromJSON(e))
        : [],
      white_list: globalThis.Array.isArray(object?.white_list)
        ? object.white_list.map((e: any) => globalThis.Number(e))
        : [],
      white_list_switch: isSet(object.white_list_switch) ? globalThis.Boolean(object.white_list_switch) : false,
      agency_levels: globalThis.Array.isArray(object?.agency_levels)
        ? object.agency_levels.map((e: any) => globalThis.Number(e))
        : [],
      param_filter: isObject(object.param_filter)
        ? Object.entries(object.param_filter).reduce<{ [key: string]: StringList }>((acc, [key, value]) => {
            acc[key] = StringList.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfoMgr>, I>>(base?: I): GoodsInfoMgr {
    return GoodsInfoMgr.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoMgr>, I>>(object: I): GoodsInfoMgr {
    const message = createBaseGoodsInfoMgr();
    message.goods_id = object.goods_id ?? 0;
    message.goods_type = object.goods_type ?? 0;
    message.cou = object.cou ?? '';
    message.client_type = object.client_type ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.pay_code = object.pay_code ?? '';
    message.sku_id = object.sku_id ?? '';
    message.name_lang = Object.entries(object.name_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.desc_lang = Object.entries(object.desc_lang ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.images = object.images ?? '';
    message.tag_images = object.tag_images ?? '';
    message.tag_lang = Object.entries(object.tag_lang ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.price = object.price ?? 0;
    message.unit = object.unit ?? 0;
    message.amount = object.amount ?? 0;
    message.currency_type = object.currency_type ?? 0;
    message.remark = object.remark ?? '';
    message.status = object.status ?? '';
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.discount_rate = object.discount_rate ?? 0;
    message.package_id = object.package_id ?? '';
    message.limit_type = object.limit_type ?? 0;
    message.style =
      object.style !== undefined && object.style !== null ? GoodsStyle.fromPartial(object.style) : undefined;
    message.package_item =
      object.package_item !== undefined && object.package_item !== null
        ? PackageItem.fromPartial(object.package_item)
        : undefined;
    message.additional_num = object.additional_num ?? 0;
    message.target_price = object.target_price ?? 0;
    message.target_unit = object.target_unit ?? 0;
    message.pay_types = object.pay_types?.map(e => PayType.fromPartial(e)) || [];
    message.white_list = object.white_list?.map(e => e) || [];
    message.white_list_switch = object.white_list_switch ?? false;
    message.agency_levels = object.agency_levels?.map(e => e) || [];
    message.param_filter = Object.entries(object.param_filter ?? {}).reduce<{ [key: string]: StringList }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = StringList.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseGoodsInfoMgr_NameLangEntry(): GoodsInfoMgr_NameLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfoMgr_NameLangEntry: MessageFns<GoodsInfoMgr_NameLangEntry> = {
  fromJSON(object: any): GoodsInfoMgr_NameLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfoMgr_NameLangEntry>, I>>(base?: I): GoodsInfoMgr_NameLangEntry {
    return GoodsInfoMgr_NameLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoMgr_NameLangEntry>, I>>(object: I): GoodsInfoMgr_NameLangEntry {
    const message = createBaseGoodsInfoMgr_NameLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfoMgr_DescLangEntry(): GoodsInfoMgr_DescLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfoMgr_DescLangEntry: MessageFns<GoodsInfoMgr_DescLangEntry> = {
  fromJSON(object: any): GoodsInfoMgr_DescLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfoMgr_DescLangEntry>, I>>(base?: I): GoodsInfoMgr_DescLangEntry {
    return GoodsInfoMgr_DescLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoMgr_DescLangEntry>, I>>(object: I): GoodsInfoMgr_DescLangEntry {
    const message = createBaseGoodsInfoMgr_DescLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfoMgr_TagLangEntry(): GoodsInfoMgr_TagLangEntry {
  return { key: '', value: '' };
}

export const GoodsInfoMgr_TagLangEntry: MessageFns<GoodsInfoMgr_TagLangEntry> = {
  fromJSON(object: any): GoodsInfoMgr_TagLangEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfoMgr_TagLangEntry>, I>>(base?: I): GoodsInfoMgr_TagLangEntry {
    return GoodsInfoMgr_TagLangEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoMgr_TagLangEntry>, I>>(object: I): GoodsInfoMgr_TagLangEntry {
    const message = createBaseGoodsInfoMgr_TagLangEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGoodsInfoMgr_ParamFilterEntry(): GoodsInfoMgr_ParamFilterEntry {
  return { key: '', value: undefined };
}

export const GoodsInfoMgr_ParamFilterEntry: MessageFns<GoodsInfoMgr_ParamFilterEntry> = {
  fromJSON(object: any): GoodsInfoMgr_ParamFilterEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? StringList.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<GoodsInfoMgr_ParamFilterEntry>, I>>(base?: I): GoodsInfoMgr_ParamFilterEntry {
    return GoodsInfoMgr_ParamFilterEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GoodsInfoMgr_ParamFilterEntry>, I>>(
    object: I
  ): GoodsInfoMgr_ParamFilterEntry {
    const message = createBaseGoodsInfoMgr_ParamFilterEntry();
    message.key = object.key ?? '';
    message.value =
      object.value !== undefined && object.value !== null ? StringList.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBasePayType(): PayType {
  return { id: 0, client_type: 0, pay_type: '', sku_id: '', pay_type_subs: [], sort: 0 };
}

export const PayType: MessageFns<PayType> = {
  fromJSON(object: any): PayType {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      client_type: isSet(object.client_type) ? clientTypeFromJSON(object.client_type) : 0,
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      sku_id: isSet(object.sku_id) ? globalThis.String(object.sku_id) : '',
      pay_type_subs: globalThis.Array.isArray(object?.pay_type_subs)
        ? object.pay_type_subs.map((e: any) => PayTypeSub.fromJSON(e))
        : [],
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0
    };
  },

  create<I extends Exact<DeepPartial<PayType>, I>>(base?: I): PayType {
    return PayType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayType>, I>>(object: I): PayType {
    const message = createBasePayType();
    message.id = object.id ?? 0;
    message.client_type = object.client_type ?? 0;
    message.pay_type = object.pay_type ?? '';
    message.sku_id = object.sku_id ?? '';
    message.pay_type_subs = object.pay_type_subs?.map(e => PayTypeSub.fromPartial(e)) || [];
    message.sort = object.sort ?? 0;
    return message;
  }
};

function createBasePayTypeSub(): PayTypeSub {
  return { id: 0, cou: '', pay_code: '', target_unit: 0, target_price: 0, sort: 0 };
}

export const PayTypeSub: MessageFns<PayTypeSub> = {
  fromJSON(object: any): PayTypeSub {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      pay_code: isSet(object.pay_code) ? globalThis.String(object.pay_code) : '',
      target_unit: isSet(object.target_unit) ? unitFromJSON(object.target_unit) : 0,
      target_price: isSet(object.target_price) ? globalThis.Number(object.target_price) : 0,
      sort: isSet(object.sort) ? globalThis.Number(object.sort) : 0
    };
  },

  create<I extends Exact<DeepPartial<PayTypeSub>, I>>(base?: I): PayTypeSub {
    return PayTypeSub.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PayTypeSub>, I>>(object: I): PayTypeSub {
    const message = createBasePayTypeSub();
    message.id = object.id ?? 0;
    message.cou = object.cou ?? '';
    message.pay_code = object.pay_code ?? '';
    message.target_unit = object.target_unit ?? 0;
    message.target_price = object.target_price ?? 0;
    message.sort = object.sort ?? 0;
    return message;
  }
};

export type GoodsMgrDefinition = typeof GoodsMgrDefinition;
export const GoodsMgrDefinition = {
  name: 'GoodsMgr',
  fullName: 'mgr.pbrevenue.GoodsMgr',
  methods: {
    /** 新增、修改，id 不为空标识修改 */
    saveGoods: {
      name: 'SaveGoods',
      requestType: SaveGoodsReq,
      requestStream: false,
      responseType: SaveGoodsRsp,
      responseStream: false,
      options: {}
    },
    /** 上下架、删除 */
    updateGoodsStatus: {
      name: 'UpdateGoodsStatus',
      requestType: UpdateGoodsStatusReq,
      requestStream: false,
      responseType: UpdateGoodsStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 分页查询 */
    goodsPageQuery: {
      name: 'GoodsPageQuery',
      requestType: GoodsPageQueryReq,
      requestStream: false,
      responseType: GoodsPageQueryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
