// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/revenue/order.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { GoodsOrderInfo, PayStatus, payStatusFromJSON } from '../../api/revenue/common';

export const protobufPackage = 'mgr.pbrevenue';

export interface ListUserPayOrderReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 充值到账用户ID */
  uid: number;
  /** 客户端包名 */
  pkg: string;
  /** 充值订单订单号 */
  order_no: string;
  /** 支付中台订单ID */
  pay_order_id: string;
  /** 支付方式 */
  pay_type: string;
  /** 支付状态 */
  statuses: PayStatus[];
  /** 创建订单-开始时间 */
  ctime_start: number;
  /** 创建订单-结束时间 */
  ctime_end: number;
}

export interface ListUserPayOrderRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 充值列表 */
  items: GoodsOrderInfo[];
}

function createBaseListUserPayOrderReq(): ListUserPayOrderReq {
  return {
    page: undefined,
    uid: 0,
    pkg: '',
    order_no: '',
    pay_order_id: '',
    pay_type: '',
    statuses: [],
    ctime_start: 0,
    ctime_end: 0
  };
}

export const ListUserPayOrderReq: MessageFns<ListUserPayOrderReq> = {
  fromJSON(object: any): ListUserPayOrderReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      order_no: isSet(object.order_no) ? globalThis.String(object.order_no) : '',
      pay_order_id: isSet(object.pay_order_id) ? globalThis.String(object.pay_order_id) : '',
      pay_type: isSet(object.pay_type) ? globalThis.String(object.pay_type) : '',
      statuses: globalThis.Array.isArray(object?.statuses) ? object.statuses.map((e: any) => payStatusFromJSON(e)) : [],
      ctime_start: isSet(object.ctime_start) ? globalThis.Number(object.ctime_start) : 0,
      ctime_end: isSet(object.ctime_end) ? globalThis.Number(object.ctime_end) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListUserPayOrderReq>, I>>(base?: I): ListUserPayOrderReq {
    return ListUserPayOrderReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListUserPayOrderReq>, I>>(object: I): ListUserPayOrderReq {
    const message = createBaseListUserPayOrderReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.uid = object.uid ?? 0;
    message.pkg = object.pkg ?? '';
    message.order_no = object.order_no ?? '';
    message.pay_order_id = object.pay_order_id ?? '';
    message.pay_type = object.pay_type ?? '';
    message.statuses = object.statuses?.map(e => e) || [];
    message.ctime_start = object.ctime_start ?? 0;
    message.ctime_end = object.ctime_end ?? 0;
    return message;
  }
};

function createBaseListUserPayOrderRsp(): ListUserPayOrderRsp {
  return { page: undefined, items: [] };
}

export const ListUserPayOrderRsp: MessageFns<ListUserPayOrderRsp> = {
  fromJSON(object: any): ListUserPayOrderRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => GoodsOrderInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListUserPayOrderRsp>, I>>(base?: I): ListUserPayOrderRsp {
    return ListUserPayOrderRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListUserPayOrderRsp>, I>>(object: I): ListUserPayOrderRsp {
    const message = createBaseListUserPayOrderRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => GoodsOrderInfo.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 充值订单
 * service_name: api.micro.social.revenue
 * smicro:spath=gitit.cc/social/components-service/social-revenue/goods/internal/handlermgr
 */
export type OrderMgrDefinition = typeof OrderMgrDefinition;
export const OrderMgrDefinition = {
  name: 'OrderMgr',
  fullName: 'mgr.pbrevenue.OrderMgr',
  methods: {
    /** 用户充值订单查询 */
    listUserPayOrder: {
      name: 'ListUserPayOrder',
      requestType: ListUserPayOrderReq,
      requestStream: false,
      responseType: ListUserPayOrderRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
