// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/offical/oaccount.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.offical';

/** smicro:spath=gitit.cc/social/components-service/social-offical/handler-mgr/oaccount */

/** 错误码 */
export enum AccountErrCode {
  ACCOUNT_ERR_CODE_NONE = 0,
  /** ACCOUNT_ERR_CODE_USER_ALREADY_BINDED - 用户已经绑定了员工代号 */
  ACCOUNT_ERR_CODE_USER_ALREADY_BINDED = 100000,
  /** ACCOUNT_ERR_CODE_CODE_ALREADY_BINDED - 员工代号已经被绑定了 */
  ACCOUNT_ERR_CODE_CODE_ALREADY_BINDED = 100001,
  UNRECOGNIZED = -1
}

export function accountErrCodeFromJSON(object: any): AccountErrCode {
  switch (object) {
    case 0:
    case 'ACCOUNT_ERR_CODE_NONE':
      return AccountErrCode.ACCOUNT_ERR_CODE_NONE;
    case 100000:
    case 'ACCOUNT_ERR_CODE_USER_ALREADY_BINDED':
      return AccountErrCode.ACCOUNT_ERR_CODE_USER_ALREADY_BINDED;
    case 100001:
    case 'ACCOUNT_ERR_CODE_CODE_ALREADY_BINDED':
      return AccountErrCode.ACCOUNT_ERR_CODE_CODE_ALREADY_BINDED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountErrCode.UNRECOGNIZED;
  }
}

export interface User {
  /** 邮箱 */
  email: string;
  /** 昵称 */
  nickName: string;
  /** 员工代号 */
  userCode: string;
}

export interface BindUserCodeReq {
  /** 申请绑定的员工代号 */
  userCode: string;
}

export interface BindUserCodeRsp {}

export interface QueryBindUserCodeReq {}

export interface QueryBindUserCodeRsp {
  /** 员工代号,返回空表示未绑定 */
  userCode: string;
}

export interface QueryBindEmailReq {
  /** 员工代号 */
  userCode: string;
}

export interface QueryBindEmailRsp {
  /** 邮箱,返回空表示未绑定 */
  email: string;
  /** 昵称 */
  nickName: string;
  /** 员工代号 */
  userCode: string;
  /** 绑定时间 */
  createdAt: number;
}

function createBaseUser(): User {
  return { email: '', nickName: '', userCode: '' };
}

export const User: MessageFns<User> = {
  fromJSON(object: any): User {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      nickName: isSet(object.nickName) ? globalThis.String(object.nickName) : '',
      userCode: isSet(object.userCode) ? globalThis.String(object.userCode) : ''
    };
  },

  create<I extends Exact<DeepPartial<User>, I>>(base?: I): User {
    return User.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<User>, I>>(object: I): User {
    const message = createBaseUser();
    message.email = object.email ?? '';
    message.nickName = object.nickName ?? '';
    message.userCode = object.userCode ?? '';
    return message;
  }
};

function createBaseBindUserCodeReq(): BindUserCodeReq {
  return { userCode: '' };
}

export const BindUserCodeReq: MessageFns<BindUserCodeReq> = {
  fromJSON(object: any): BindUserCodeReq {
    return { userCode: isSet(object.userCode) ? globalThis.String(object.userCode) : '' };
  },

  create<I extends Exact<DeepPartial<BindUserCodeReq>, I>>(base?: I): BindUserCodeReq {
    return BindUserCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindUserCodeReq>, I>>(object: I): BindUserCodeReq {
    const message = createBaseBindUserCodeReq();
    message.userCode = object.userCode ?? '';
    return message;
  }
};

function createBaseBindUserCodeRsp(): BindUserCodeRsp {
  return {};
}

export const BindUserCodeRsp: MessageFns<BindUserCodeRsp> = {
  fromJSON(_: any): BindUserCodeRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BindUserCodeRsp>, I>>(base?: I): BindUserCodeRsp {
    return BindUserCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindUserCodeRsp>, I>>(_: I): BindUserCodeRsp {
    const message = createBaseBindUserCodeRsp();
    return message;
  }
};

function createBaseQueryBindUserCodeReq(): QueryBindUserCodeReq {
  return {};
}

export const QueryBindUserCodeReq: MessageFns<QueryBindUserCodeReq> = {
  fromJSON(_: any): QueryBindUserCodeReq {
    return {};
  },

  create<I extends Exact<DeepPartial<QueryBindUserCodeReq>, I>>(base?: I): QueryBindUserCodeReq {
    return QueryBindUserCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryBindUserCodeReq>, I>>(_: I): QueryBindUserCodeReq {
    const message = createBaseQueryBindUserCodeReq();
    return message;
  }
};

function createBaseQueryBindUserCodeRsp(): QueryBindUserCodeRsp {
  return { userCode: '' };
}

export const QueryBindUserCodeRsp: MessageFns<QueryBindUserCodeRsp> = {
  fromJSON(object: any): QueryBindUserCodeRsp {
    return { userCode: isSet(object.userCode) ? globalThis.String(object.userCode) : '' };
  },

  create<I extends Exact<DeepPartial<QueryBindUserCodeRsp>, I>>(base?: I): QueryBindUserCodeRsp {
    return QueryBindUserCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryBindUserCodeRsp>, I>>(object: I): QueryBindUserCodeRsp {
    const message = createBaseQueryBindUserCodeRsp();
    message.userCode = object.userCode ?? '';
    return message;
  }
};

function createBaseQueryBindEmailReq(): QueryBindEmailReq {
  return { userCode: '' };
}

export const QueryBindEmailReq: MessageFns<QueryBindEmailReq> = {
  fromJSON(object: any): QueryBindEmailReq {
    return { userCode: isSet(object.userCode) ? globalThis.String(object.userCode) : '' };
  },

  create<I extends Exact<DeepPartial<QueryBindEmailReq>, I>>(base?: I): QueryBindEmailReq {
    return QueryBindEmailReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryBindEmailReq>, I>>(object: I): QueryBindEmailReq {
    const message = createBaseQueryBindEmailReq();
    message.userCode = object.userCode ?? '';
    return message;
  }
};

function createBaseQueryBindEmailRsp(): QueryBindEmailRsp {
  return { email: '', nickName: '', userCode: '', createdAt: 0 };
}

export const QueryBindEmailRsp: MessageFns<QueryBindEmailRsp> = {
  fromJSON(object: any): QueryBindEmailRsp {
    return {
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      nickName: isSet(object.nickName) ? globalThis.String(object.nickName) : '',
      userCode: isSet(object.userCode) ? globalThis.String(object.userCode) : '',
      createdAt: isSet(object.createdAt) ? globalThis.Number(object.createdAt) : 0
    };
  },

  create<I extends Exact<DeepPartial<QueryBindEmailRsp>, I>>(base?: I): QueryBindEmailRsp {
    return QueryBindEmailRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QueryBindEmailRsp>, I>>(object: I): QueryBindEmailRsp {
    const message = createBaseQueryBindEmailRsp();
    message.email = object.email ?? '';
    message.nickName = object.nickName ?? '';
    message.userCode = object.userCode ?? '';
    message.createdAt = object.createdAt ?? 0;
    return message;
  }
};

/**
 * OAccount 内部账号管理
 * serviceName: social.offical
 */
export type OAccountDefinition = typeof OAccountDefinition;
export const OAccountDefinition = {
  name: 'OAccount',
  fullName: 'comm.mgr.offical.OAccount',
  methods: {
    /** 绑定员工代号 */
    bindUserCode: {
      name: 'BindUserCode',
      requestType: BindUserCodeReq,
      requestStream: false,
      responseType: BindUserCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 查询自己绑定的员工代号 */
    queryBindUserCode: {
      name: 'QueryBindUserCode',
      requestType: QueryBindUserCodeReq,
      requestStream: false,
      responseType: QueryBindUserCodeRsp,
      responseStream: false,
      options: {}
    },
    /** 根据员工代号查询绑定的email */
    queryBindEmail: {
      name: 'QueryBindEmail',
      requestType: QueryBindEmailReq,
      requestStream: false,
      responseType: QueryBindEmailRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
