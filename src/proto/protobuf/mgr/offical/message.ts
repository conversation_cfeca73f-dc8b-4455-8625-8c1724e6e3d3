// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/offical/message.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.offical';

/** smicro:spath=gitit.cc/social/components-service/social-offical/handler-mgr/dingtalkmessage */

/** 错误码 */
export enum MessageErrCode {
  MESSAGE_ERR_CODE_NONE = 0,
  /** MESSAGE_ERR_CODE_MOBILE_INVALID - 手机号码无效 */
  MESSAGE_ERR_CODE_MOBILE_INVALID = 100000,
  /** MESSAGE_ERR_CODE_MOBILE_NOT_MATCH_USER - 手机号码没有匹配到用户 */
  MESSAGE_ERR_CODE_MOBILE_NOT_MATCH_USER = 100001,
  /** MESSAGE_ERR_CODE_MESSAGE_SEND_FAIL - 发送消息失败 */
  MESSAGE_ERR_CODE_MESSAGE_SEND_FAIL = 100002,
  UNRECOGNIZED = -1
}

export function messageErrCodeFromJSON(object: any): MessageErrCode {
  switch (object) {
    case 0:
    case 'MESSAGE_ERR_CODE_NONE':
      return MessageErrCode.MESSAGE_ERR_CODE_NONE;
    case 100000:
    case 'MESSAGE_ERR_CODE_MOBILE_INVALID':
      return MessageErrCode.MESSAGE_ERR_CODE_MOBILE_INVALID;
    case 100001:
    case 'MESSAGE_ERR_CODE_MOBILE_NOT_MATCH_USER':
      return MessageErrCode.MESSAGE_ERR_CODE_MOBILE_NOT_MATCH_USER;
    case 100002:
    case 'MESSAGE_ERR_CODE_MESSAGE_SEND_FAIL':
      return MessageErrCode.MESSAGE_ERR_CODE_MESSAGE_SEND_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MessageErrCode.UNRECOGNIZED;
  }
}

export interface SendMessageByMobileReq {
  /** 手机号码 */
  mobile: string;
  /** 消息 */
  msgText: string;
}

export interface SendMessageByMobileRsp {}

export interface GetLatestVerifyCodeReq {
  /** 手机号码 */
  mobile: string;
}

export interface GetLatestVerifyCodeRsp {
  /** 验证码 */
  verify_code: string;
}

function createBaseSendMessageByMobileReq(): SendMessageByMobileReq {
  return { mobile: '', msgText: '' };
}

export const SendMessageByMobileReq: MessageFns<SendMessageByMobileReq> = {
  fromJSON(object: any): SendMessageByMobileReq {
    return {
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : '',
      msgText: isSet(object.msgText) ? globalThis.String(object.msgText) : ''
    };
  },

  create<I extends Exact<DeepPartial<SendMessageByMobileReq>, I>>(base?: I): SendMessageByMobileReq {
    return SendMessageByMobileReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendMessageByMobileReq>, I>>(object: I): SendMessageByMobileReq {
    const message = createBaseSendMessageByMobileReq();
    message.mobile = object.mobile ?? '';
    message.msgText = object.msgText ?? '';
    return message;
  }
};

function createBaseSendMessageByMobileRsp(): SendMessageByMobileRsp {
  return {};
}

export const SendMessageByMobileRsp: MessageFns<SendMessageByMobileRsp> = {
  fromJSON(_: any): SendMessageByMobileRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SendMessageByMobileRsp>, I>>(base?: I): SendMessageByMobileRsp {
    return SendMessageByMobileRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SendMessageByMobileRsp>, I>>(_: I): SendMessageByMobileRsp {
    const message = createBaseSendMessageByMobileRsp();
    return message;
  }
};

function createBaseGetLatestVerifyCodeReq(): GetLatestVerifyCodeReq {
  return { mobile: '' };
}

export const GetLatestVerifyCodeReq: MessageFns<GetLatestVerifyCodeReq> = {
  fromJSON(object: any): GetLatestVerifyCodeReq {
    return { mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : '' };
  },

  create<I extends Exact<DeepPartial<GetLatestVerifyCodeReq>, I>>(base?: I): GetLatestVerifyCodeReq {
    return GetLatestVerifyCodeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLatestVerifyCodeReq>, I>>(object: I): GetLatestVerifyCodeReq {
    const message = createBaseGetLatestVerifyCodeReq();
    message.mobile = object.mobile ?? '';
    return message;
  }
};

function createBaseGetLatestVerifyCodeRsp(): GetLatestVerifyCodeRsp {
  return { verify_code: '' };
}

export const GetLatestVerifyCodeRsp: MessageFns<GetLatestVerifyCodeRsp> = {
  fromJSON(object: any): GetLatestVerifyCodeRsp {
    return { verify_code: isSet(object.verify_code) ? globalThis.String(object.verify_code) : '' };
  },

  create<I extends Exact<DeepPartial<GetLatestVerifyCodeRsp>, I>>(base?: I): GetLatestVerifyCodeRsp {
    return GetLatestVerifyCodeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLatestVerifyCodeRsp>, I>>(object: I): GetLatestVerifyCodeRsp {
    const message = createBaseGetLatestVerifyCodeRsp();
    message.verify_code = object.verify_code ?? '';
    return message;
  }
};

/** DingtalkMessage 钉钉消息 */
export type DingtalkMessageDefinition = typeof DingtalkMessageDefinition;
export const DingtalkMessageDefinition = {
  name: 'DingtalkMessage',
  fullName: 'comm.mgr.offical.DingtalkMessage',
  methods: {
    /** 发送消息 */
    sendMessageByMobile: {
      name: 'SendMessageByMobile',
      requestType: SendMessageByMobileReq,
      requestStream: false,
      responseType: SendMessageByMobileRsp,
      responseStream: false,
      options: {}
    },
    /** 获取最新的验证码 */
    getLatestVerifyCode: {
      name: 'GetLatestVerifyCode',
      requestType: GetLatestVerifyCodeReq,
      requestStream: false,
      responseType: GetLatestVerifyCodeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
