// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/offical/account_type.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.offical';

/** smicro:spath=gitit.cc/social/components-service/social-offical/handler-mgr/accounttypemgr */

/** 错误码 */
export enum AccountTypeErrCode {
  ACCOUNT_TYPE_ERR_CODE_NONE = 0,
  UNRECOGNIZED = -1
}

export function accountTypeErrCodeFromJSON(object: any): AccountTypeErrCode {
  switch (object) {
    case 0:
    case 'ACCOUNT_TYPE_ERR_CODE_NONE':
      return AccountTypeErrCode.ACCOUNT_TYPE_ERR_CODE_NONE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountTypeErrCode.UNRECOGNIZED;
  }
}

/** 账号类型 */
export enum AccountTypeCode {
  /** ACCOUNT_TYPE_NONE - 无 */
  ACCOUNT_TYPE_NONE = 0,
  /** ACCOUNT_TYPE_TEST - 测试账号 */
  ACCOUNT_TYPE_TEST = 1,
  /** ACCOUNT_TYPE_OPERATE - 运营账号 */
  ACCOUNT_TYPE_OPERATE = 2,
  /** ACCOUNT_TYPE_EXP - 体验账号 */
  ACCOUNT_TYPE_EXP = 3,
  UNRECOGNIZED = -1
}

export function accountTypeCodeFromJSON(object: any): AccountTypeCode {
  switch (object) {
    case 0:
    case 'ACCOUNT_TYPE_NONE':
      return AccountTypeCode.ACCOUNT_TYPE_NONE;
    case 1:
    case 'ACCOUNT_TYPE_TEST':
      return AccountTypeCode.ACCOUNT_TYPE_TEST;
    case 2:
    case 'ACCOUNT_TYPE_OPERATE':
      return AccountTypeCode.ACCOUNT_TYPE_OPERATE;
    case 3:
    case 'ACCOUNT_TYPE_EXP':
      return AccountTypeCode.ACCOUNT_TYPE_EXP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountTypeCode.UNRECOGNIZED;
  }
}

export interface AccountType {
  /** id */
  id: number;
  /** app名称 */
  app_name: string;
  /** 用户Id */
  uid: number;
  /** 邮箱 */
  email: string;
  /** 账号类型(0:无,1:测试,2:运营,3:体验) */
  account_type: number;
}

export interface ListAccountTypeReq {
  page: Page | undefined;
  /** id */
  id: number;
  /** app名称 */
  app_name: string;
  /** 用户Id */
  uid: number;
  /** 邮箱 */
  email: string;
}

export interface ListAccountTypeRsp {
  page: Page | undefined;
  items: AccountType[];
}

function createBaseAccountType(): AccountType {
  return { id: 0, app_name: '', uid: 0, email: '', account_type: 0 };
}

export const AccountType: MessageFns<AccountType> = {
  fromJSON(object: any): AccountType {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      account_type: isSet(object.account_type) ? globalThis.Number(object.account_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<AccountType>, I>>(base?: I): AccountType {
    return AccountType.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountType>, I>>(object: I): AccountType {
    const message = createBaseAccountType();
    message.id = object.id ?? 0;
    message.app_name = object.app_name ?? '';
    message.uid = object.uid ?? 0;
    message.email = object.email ?? '';
    message.account_type = object.account_type ?? 0;
    return message;
  }
};

function createBaseListAccountTypeReq(): ListAccountTypeReq {
  return { page: undefined, id: 0, app_name: '', uid: 0, email: '' };
}

export const ListAccountTypeReq: MessageFns<ListAccountTypeReq> = {
  fromJSON(object: any): ListAccountTypeReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : '',
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      email: isSet(object.email) ? globalThis.String(object.email) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAccountTypeReq>, I>>(base?: I): ListAccountTypeReq {
    return ListAccountTypeReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAccountTypeReq>, I>>(object: I): ListAccountTypeReq {
    const message = createBaseListAccountTypeReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.app_name = object.app_name ?? '';
    message.uid = object.uid ?? 0;
    message.email = object.email ?? '';
    return message;
  }
};

function createBaseListAccountTypeRsp(): ListAccountTypeRsp {
  return { page: undefined, items: [] };
}

export const ListAccountTypeRsp: MessageFns<ListAccountTypeRsp> = {
  fromJSON(object: any): ListAccountTypeRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => AccountType.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAccountTypeRsp>, I>>(base?: I): ListAccountTypeRsp {
    return ListAccountTypeRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAccountTypeRsp>, I>>(object: I): ListAccountTypeRsp {
    const message = createBaseListAccountTypeRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => AccountType.fromPartial(e)) || [];
    return message;
  }
};

/**
 * AccountType 绑定账号管理
 * serviceName: social.offical
 */
export type AccountTypeMgrDefinition = typeof AccountTypeMgrDefinition;
export const AccountTypeMgrDefinition = {
  name: 'AccountTypeMgr',
  fullName: 'comm.mgr.offical.AccountTypeMgr',
  methods: {
    /** 查询 绑定账号 */
    listAccountType: {
      name: 'ListAccountType',
      requestType: ListAccountTypeReq,
      requestStream: false,
      responseType: ListAccountTypeRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
