// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/offical/employee_account.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.offical';

/** smicro:spath=gitit.cc/social/components-service/social-offical/handler-mgr/employeeaccount */

/** 账号类型 */
export enum EmployeeAccountAccountType {
  /** ACCOUNT_ACCOUNT_ACCOUNT_TYPE_NONE - 无账号类型 */
  ACCOUNT_ACCOUNT_ACCOUNT_TYPE_NONE = 0,
  /** ACCOUNT_ACCOUNT_ACCOUNT_TYPE_PHONE_NUM - 手机号码（包含国家代码，例如：+*************） */
  ACCOUNT_ACCOUNT_ACCOUNT_TYPE_PHONE_NUM = 1,
  /** ACCOUNT_ACCOUNT_ACCOUNT_TYPE_EMAIL - 邮箱 */
  ACCOUNT_ACCOUNT_ACCOUNT_TYPE_EMAIL = 2,
  /** ACCOUNT_ACCOUNT_ACCOUNT_TYPE_DID - 设备Id */
  ACCOUNT_ACCOUNT_ACCOUNT_TYPE_DID = 3,
  UNRECOGNIZED = -1
}

export function employeeAccountAccountTypeFromJSON(object: any): EmployeeAccountAccountType {
  switch (object) {
    case 0:
    case 'ACCOUNT_ACCOUNT_ACCOUNT_TYPE_NONE':
      return EmployeeAccountAccountType.ACCOUNT_ACCOUNT_ACCOUNT_TYPE_NONE;
    case 1:
    case 'ACCOUNT_ACCOUNT_ACCOUNT_TYPE_PHONE_NUM':
      return EmployeeAccountAccountType.ACCOUNT_ACCOUNT_ACCOUNT_TYPE_PHONE_NUM;
    case 2:
    case 'ACCOUNT_ACCOUNT_ACCOUNT_TYPE_EMAIL':
      return EmployeeAccountAccountType.ACCOUNT_ACCOUNT_ACCOUNT_TYPE_EMAIL;
    case 3:
    case 'ACCOUNT_ACCOUNT_ACCOUNT_TYPE_DID':
      return EmployeeAccountAccountType.ACCOUNT_ACCOUNT_ACCOUNT_TYPE_DID;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return EmployeeAccountAccountType.UNRECOGNIZED;
  }
}

/** 错误码 */
export enum ErrCodeEmployeeAccount {
  ERR_CODE_EMPLOYEE_ACCOUNT_NONE = 0,
  /** ERR_CODE_EMPLOYEE_ACCOUNT_ACCOUNT_ID_ALREADY_BINDED - 账号已经被绑定了 */
  ERR_CODE_EMPLOYEE_ACCOUNT_ACCOUNT_ID_ALREADY_BINDED = 200000,
  UNRECOGNIZED = -1
}

export function errCodeEmployeeAccountFromJSON(object: any): ErrCodeEmployeeAccount {
  switch (object) {
    case 0:
    case 'ERR_CODE_EMPLOYEE_ACCOUNT_NONE':
      return ErrCodeEmployeeAccount.ERR_CODE_EMPLOYEE_ACCOUNT_NONE;
    case 200000:
    case 'ERR_CODE_EMPLOYEE_ACCOUNT_ACCOUNT_ID_ALREADY_BINDED':
      return ErrCodeEmployeeAccount.ERR_CODE_EMPLOYEE_ACCOUNT_ACCOUNT_ID_ALREADY_BINDED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCodeEmployeeAccount.UNRECOGNIZED;
  }
}

export interface EmployeeAccount {
  /** id */
  id: number;
  /** 账号类型 */
  account_type: EmployeeAccountAccountType;
  /** 账号标识 */
  account_id: string;
  /** 员工昵称 */
  nick_name: string;
  /** 员工邮箱 */
  email: string;
  /** 钉钉unionId */
  dingtalk_union_id: string;
  /** 钉钉userId */
  dingtalk_user_id: string;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  created_by: string;
}

export interface BindAccountIdReq {
  /** 账号类型 */
  account_type: EmployeeAccountAccountType;
  /** 账号id(手机号码/第三方账号) */
  account_id: string;
}

export interface BindAccountIdRsp {}

export interface ListEmployeeAccountReq {
  page: Page | undefined;
  /** 账号类型 */
  account_type: EmployeeAccountAccountType;
  /** 账号标识 */
  account_id: string;
  /** 员工昵称 */
  nick_name: string;
  /** 员工邮箱 */
  email: string;
}

export interface ListEmployeeAccountRsp {
  page: Page | undefined;
  employee_accounts: EmployeeAccount[];
}

function createBaseEmployeeAccount(): EmployeeAccount {
  return {
    id: 0,
    account_type: 0,
    account_id: '',
    nick_name: '',
    email: '',
    dingtalk_union_id: '',
    dingtalk_user_id: '',
    created_at: 0,
    created_by: ''
  };
}

export const EmployeeAccount: MessageFns<EmployeeAccount> = {
  fromJSON(object: any): EmployeeAccount {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      account_type: isSet(object.account_type) ? employeeAccountAccountTypeFromJSON(object.account_type) : 0,
      account_id: isSet(object.account_id) ? globalThis.String(object.account_id) : '',
      nick_name: isSet(object.nick_name) ? globalThis.String(object.nick_name) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      dingtalk_union_id: isSet(object.dingtalk_union_id) ? globalThis.String(object.dingtalk_union_id) : '',
      dingtalk_user_id: isSet(object.dingtalk_user_id) ? globalThis.String(object.dingtalk_user_id) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<EmployeeAccount>, I>>(base?: I): EmployeeAccount {
    return EmployeeAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EmployeeAccount>, I>>(object: I): EmployeeAccount {
    const message = createBaseEmployeeAccount();
    message.id = object.id ?? 0;
    message.account_type = object.account_type ?? 0;
    message.account_id = object.account_id ?? '';
    message.nick_name = object.nick_name ?? '';
    message.email = object.email ?? '';
    message.dingtalk_union_id = object.dingtalk_union_id ?? '';
    message.dingtalk_user_id = object.dingtalk_user_id ?? '';
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    return message;
  }
};

function createBaseBindAccountIdReq(): BindAccountIdReq {
  return { account_type: 0, account_id: '' };
}

export const BindAccountIdReq: MessageFns<BindAccountIdReq> = {
  fromJSON(object: any): BindAccountIdReq {
    return {
      account_type: isSet(object.account_type) ? employeeAccountAccountTypeFromJSON(object.account_type) : 0,
      account_id: isSet(object.account_id) ? globalThis.String(object.account_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<BindAccountIdReq>, I>>(base?: I): BindAccountIdReq {
    return BindAccountIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindAccountIdReq>, I>>(object: I): BindAccountIdReq {
    const message = createBaseBindAccountIdReq();
    message.account_type = object.account_type ?? 0;
    message.account_id = object.account_id ?? '';
    return message;
  }
};

function createBaseBindAccountIdRsp(): BindAccountIdRsp {
  return {};
}

export const BindAccountIdRsp: MessageFns<BindAccountIdRsp> = {
  fromJSON(_: any): BindAccountIdRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BindAccountIdRsp>, I>>(base?: I): BindAccountIdRsp {
    return BindAccountIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindAccountIdRsp>, I>>(_: I): BindAccountIdRsp {
    const message = createBaseBindAccountIdRsp();
    return message;
  }
};

function createBaseListEmployeeAccountReq(): ListEmployeeAccountReq {
  return { page: undefined, account_type: 0, account_id: '', nick_name: '', email: '' };
}

export const ListEmployeeAccountReq: MessageFns<ListEmployeeAccountReq> = {
  fromJSON(object: any): ListEmployeeAccountReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      account_type: isSet(object.account_type) ? employeeAccountAccountTypeFromJSON(object.account_type) : 0,
      account_id: isSet(object.account_id) ? globalThis.String(object.account_id) : '',
      nick_name: isSet(object.nick_name) ? globalThis.String(object.nick_name) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListEmployeeAccountReq>, I>>(base?: I): ListEmployeeAccountReq {
    return ListEmployeeAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListEmployeeAccountReq>, I>>(object: I): ListEmployeeAccountReq {
    const message = createBaseListEmployeeAccountReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.account_type = object.account_type ?? 0;
    message.account_id = object.account_id ?? '';
    message.nick_name = object.nick_name ?? '';
    message.email = object.email ?? '';
    return message;
  }
};

function createBaseListEmployeeAccountRsp(): ListEmployeeAccountRsp {
  return { page: undefined, employee_accounts: [] };
}

export const ListEmployeeAccountRsp: MessageFns<ListEmployeeAccountRsp> = {
  fromJSON(object: any): ListEmployeeAccountRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      employee_accounts: globalThis.Array.isArray(object?.employee_accounts)
        ? object.employee_accounts.map((e: any) => EmployeeAccount.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListEmployeeAccountRsp>, I>>(base?: I): ListEmployeeAccountRsp {
    return ListEmployeeAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListEmployeeAccountRsp>, I>>(object: I): ListEmployeeAccountRsp {
    const message = createBaseListEmployeeAccountRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.employee_accounts = object.employee_accounts?.map(e => EmployeeAccount.fromPartial(e)) || [];
    return message;
  }
};

/**
 * EmployeeAccountMgr 员工账号管理
 * serviceName: social.offical
 */
export type EmployeeAccountMgrDefinition = typeof EmployeeAccountMgrDefinition;
export const EmployeeAccountMgrDefinition = {
  name: 'EmployeeAccountMgr',
  fullName: 'comm.mgr.offical.EmployeeAccountMgr',
  methods: {
    /** 绑定账号 */
    bindAccountId: {
      name: 'BindAccountId',
      requestType: BindAccountIdReq,
      requestStream: false,
      responseType: BindAccountIdRsp,
      responseStream: false,
      options: {}
    },
    /** 列表 */
    listEmployeeAccount: {
      name: 'ListEmployeeAccount',
      requestType: ListEmployeeAccountReq,
      requestStream: false,
      responseType: ListEmployeeAccountRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
