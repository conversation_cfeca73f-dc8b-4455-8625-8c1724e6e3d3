// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/offical/dingtalk_group_webhook.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.offical';

/** 钉钉群Webhook配置 */
export interface DingtalkGroupWebhookConfig {
  /** id */
  id: number;
  /** 员工代号 */
  user_code: string;
  /** app名称 */
  app_name: string;
  /** 钉钉群-Webhook地址 */
  dingtalk_group_webhook: string;
  /** 钉钉群-Webhook密钥 */
  dingtalk_group_secret: string;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  created_by: string;
  /** 修改时间 */
  updated_at: number;
  /** 修改人 */
  updated_by: string;
}

export interface ListDingtalkGroupWebhookReq {
  /** id */
  id: number;
  /** 员工代号 */
  user_code: string;
  /** app名称 */
  app_name: string;
}

export interface ListDingtalkGroupWebhookRsp {
  /** DingtalkGroupWebhookInfo 列表 */
  items: DingtalkGroupWebhookConfig[];
}

export interface CreateDingtalkGroupWebhookReq {
  /** 员工代号 */
  user_code: string;
  /** app名称 */
  app_name: string;
  /** 钉钉群-Webhook地址 */
  dingtalk_group_webhook: string;
  /** 钉钉群-Webhook密钥 */
  dingtalk_group_secret: string;
}

export interface CreateDingtalkGroupWebhookRsp {
  /** id */
  id: number;
}

export interface UpdateDingtalkGroupWebhookReq {
  /** id */
  id: number;
  /** app名称 */
  app_name: string;
  /** 钉钉群-Webhook地址 */
  dingtalk_group_webhook: string;
  /** 钉钉群-Webhook密钥 */
  dingtalk_group_secret: string;
}

export interface UpdateDingtalkGroupWebhookRsp {}

export interface DeleteDingtalkGroupWebhookReq {
  /** id */
  id: number;
}

export interface DeleteDingtalkGroupWebhookRsp {}

export interface ListAppNameReq {}

export interface ListAppNameRsp {
  app_names: string[];
}

function createBaseDingtalkGroupWebhookConfig(): DingtalkGroupWebhookConfig {
  return {
    id: 0,
    user_code: '',
    app_name: '',
    dingtalk_group_webhook: '',
    dingtalk_group_secret: '',
    created_at: 0,
    created_by: '',
    updated_at: 0,
    updated_by: ''
  };
}

export const DingtalkGroupWebhookConfig: MessageFns<DingtalkGroupWebhookConfig> = {
  fromJSON(object: any): DingtalkGroupWebhookConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      user_code: isSet(object.user_code) ? globalThis.String(object.user_code) : '',
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : '',
      dingtalk_group_webhook: isSet(object.dingtalk_group_webhook)
        ? globalThis.String(object.dingtalk_group_webhook)
        : '',
      dingtalk_group_secret: isSet(object.dingtalk_group_secret) ? globalThis.String(object.dingtalk_group_secret) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      updated_by: isSet(object.updated_by) ? globalThis.String(object.updated_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<DingtalkGroupWebhookConfig>, I>>(base?: I): DingtalkGroupWebhookConfig {
    return DingtalkGroupWebhookConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DingtalkGroupWebhookConfig>, I>>(object: I): DingtalkGroupWebhookConfig {
    const message = createBaseDingtalkGroupWebhookConfig();
    message.id = object.id ?? 0;
    message.user_code = object.user_code ?? '';
    message.app_name = object.app_name ?? '';
    message.dingtalk_group_webhook = object.dingtalk_group_webhook ?? '';
    message.dingtalk_group_secret = object.dingtalk_group_secret ?? '';
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.updated_by = object.updated_by ?? '';
    return message;
  }
};

function createBaseListDingtalkGroupWebhookReq(): ListDingtalkGroupWebhookReq {
  return { id: 0, user_code: '', app_name: '' };
}

export const ListDingtalkGroupWebhookReq: MessageFns<ListDingtalkGroupWebhookReq> = {
  fromJSON(object: any): ListDingtalkGroupWebhookReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      user_code: isSet(object.user_code) ? globalThis.String(object.user_code) : '',
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListDingtalkGroupWebhookReq>, I>>(base?: I): ListDingtalkGroupWebhookReq {
    return ListDingtalkGroupWebhookReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDingtalkGroupWebhookReq>, I>>(object: I): ListDingtalkGroupWebhookReq {
    const message = createBaseListDingtalkGroupWebhookReq();
    message.id = object.id ?? 0;
    message.user_code = object.user_code ?? '';
    message.app_name = object.app_name ?? '';
    return message;
  }
};

function createBaseListDingtalkGroupWebhookRsp(): ListDingtalkGroupWebhookRsp {
  return { items: [] };
}

export const ListDingtalkGroupWebhookRsp: MessageFns<ListDingtalkGroupWebhookRsp> = {
  fromJSON(object: any): ListDingtalkGroupWebhookRsp {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => DingtalkGroupWebhookConfig.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListDingtalkGroupWebhookRsp>, I>>(base?: I): ListDingtalkGroupWebhookRsp {
    return ListDingtalkGroupWebhookRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDingtalkGroupWebhookRsp>, I>>(object: I): ListDingtalkGroupWebhookRsp {
    const message = createBaseListDingtalkGroupWebhookRsp();
    message.items = object.items?.map(e => DingtalkGroupWebhookConfig.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCreateDingtalkGroupWebhookReq(): CreateDingtalkGroupWebhookReq {
  return { user_code: '', app_name: '', dingtalk_group_webhook: '', dingtalk_group_secret: '' };
}

export const CreateDingtalkGroupWebhookReq: MessageFns<CreateDingtalkGroupWebhookReq> = {
  fromJSON(object: any): CreateDingtalkGroupWebhookReq {
    return {
      user_code: isSet(object.user_code) ? globalThis.String(object.user_code) : '',
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : '',
      dingtalk_group_webhook: isSet(object.dingtalk_group_webhook)
        ? globalThis.String(object.dingtalk_group_webhook)
        : '',
      dingtalk_group_secret: isSet(object.dingtalk_group_secret) ? globalThis.String(object.dingtalk_group_secret) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateDingtalkGroupWebhookReq>, I>>(base?: I): CreateDingtalkGroupWebhookReq {
    return CreateDingtalkGroupWebhookReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateDingtalkGroupWebhookReq>, I>>(
    object: I
  ): CreateDingtalkGroupWebhookReq {
    const message = createBaseCreateDingtalkGroupWebhookReq();
    message.user_code = object.user_code ?? '';
    message.app_name = object.app_name ?? '';
    message.dingtalk_group_webhook = object.dingtalk_group_webhook ?? '';
    message.dingtalk_group_secret = object.dingtalk_group_secret ?? '';
    return message;
  }
};

function createBaseCreateDingtalkGroupWebhookRsp(): CreateDingtalkGroupWebhookRsp {
  return { id: 0 };
}

export const CreateDingtalkGroupWebhookRsp: MessageFns<CreateDingtalkGroupWebhookRsp> = {
  fromJSON(object: any): CreateDingtalkGroupWebhookRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateDingtalkGroupWebhookRsp>, I>>(base?: I): CreateDingtalkGroupWebhookRsp {
    return CreateDingtalkGroupWebhookRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateDingtalkGroupWebhookRsp>, I>>(
    object: I
  ): CreateDingtalkGroupWebhookRsp {
    const message = createBaseCreateDingtalkGroupWebhookRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateDingtalkGroupWebhookReq(): UpdateDingtalkGroupWebhookReq {
  return { id: 0, app_name: '', dingtalk_group_webhook: '', dingtalk_group_secret: '' };
}

export const UpdateDingtalkGroupWebhookReq: MessageFns<UpdateDingtalkGroupWebhookReq> = {
  fromJSON(object: any): UpdateDingtalkGroupWebhookReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      app_name: isSet(object.app_name) ? globalThis.String(object.app_name) : '',
      dingtalk_group_webhook: isSet(object.dingtalk_group_webhook)
        ? globalThis.String(object.dingtalk_group_webhook)
        : '',
      dingtalk_group_secret: isSet(object.dingtalk_group_secret) ? globalThis.String(object.dingtalk_group_secret) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateDingtalkGroupWebhookReq>, I>>(base?: I): UpdateDingtalkGroupWebhookReq {
    return UpdateDingtalkGroupWebhookReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDingtalkGroupWebhookReq>, I>>(
    object: I
  ): UpdateDingtalkGroupWebhookReq {
    const message = createBaseUpdateDingtalkGroupWebhookReq();
    message.id = object.id ?? 0;
    message.app_name = object.app_name ?? '';
    message.dingtalk_group_webhook = object.dingtalk_group_webhook ?? '';
    message.dingtalk_group_secret = object.dingtalk_group_secret ?? '';
    return message;
  }
};

function createBaseUpdateDingtalkGroupWebhookRsp(): UpdateDingtalkGroupWebhookRsp {
  return {};
}

export const UpdateDingtalkGroupWebhookRsp: MessageFns<UpdateDingtalkGroupWebhookRsp> = {
  fromJSON(_: any): UpdateDingtalkGroupWebhookRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateDingtalkGroupWebhookRsp>, I>>(base?: I): UpdateDingtalkGroupWebhookRsp {
    return UpdateDingtalkGroupWebhookRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDingtalkGroupWebhookRsp>, I>>(_: I): UpdateDingtalkGroupWebhookRsp {
    const message = createBaseUpdateDingtalkGroupWebhookRsp();
    return message;
  }
};

function createBaseDeleteDingtalkGroupWebhookReq(): DeleteDingtalkGroupWebhookReq {
  return { id: 0 };
}

export const DeleteDingtalkGroupWebhookReq: MessageFns<DeleteDingtalkGroupWebhookReq> = {
  fromJSON(object: any): DeleteDingtalkGroupWebhookReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteDingtalkGroupWebhookReq>, I>>(base?: I): DeleteDingtalkGroupWebhookReq {
    return DeleteDingtalkGroupWebhookReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteDingtalkGroupWebhookReq>, I>>(
    object: I
  ): DeleteDingtalkGroupWebhookReq {
    const message = createBaseDeleteDingtalkGroupWebhookReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteDingtalkGroupWebhookRsp(): DeleteDingtalkGroupWebhookRsp {
  return {};
}

export const DeleteDingtalkGroupWebhookRsp: MessageFns<DeleteDingtalkGroupWebhookRsp> = {
  fromJSON(_: any): DeleteDingtalkGroupWebhookRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteDingtalkGroupWebhookRsp>, I>>(base?: I): DeleteDingtalkGroupWebhookRsp {
    return DeleteDingtalkGroupWebhookRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteDingtalkGroupWebhookRsp>, I>>(_: I): DeleteDingtalkGroupWebhookRsp {
    const message = createBaseDeleteDingtalkGroupWebhookRsp();
    return message;
  }
};

function createBaseListAppNameReq(): ListAppNameReq {
  return {};
}

export const ListAppNameReq: MessageFns<ListAppNameReq> = {
  fromJSON(_: any): ListAppNameReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListAppNameReq>, I>>(base?: I): ListAppNameReq {
    return ListAppNameReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAppNameReq>, I>>(_: I): ListAppNameReq {
    const message = createBaseListAppNameReq();
    return message;
  }
};

function createBaseListAppNameRsp(): ListAppNameRsp {
  return { app_names: [] };
}

export const ListAppNameRsp: MessageFns<ListAppNameRsp> = {
  fromJSON(object: any): ListAppNameRsp {
    return {
      app_names: globalThis.Array.isArray(object?.app_names)
        ? object.app_names.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListAppNameRsp>, I>>(base?: I): ListAppNameRsp {
    return ListAppNameRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAppNameRsp>, I>>(object: I): ListAppNameRsp {
    const message = createBaseListAppNameRsp();
    message.app_names = object.app_names?.map(e => e) || [];
    return message;
  }
};

/**
 * serviceName: social.offical
 * smicro:spath=gitit.cc/social/components-service/social-offical/handler-mgr/dingtalkgroupwebhook
 */
export type DingtalkGroupWebhookDefinition = typeof DingtalkGroupWebhookDefinition;
export const DingtalkGroupWebhookDefinition = {
  name: 'DingtalkGroupWebhook',
  fullName: 'comm.mgr.offical.DingtalkGroupWebhook',
  methods: {
    /** 列表 */
    listDingtalkGroupWebhook: {
      name: 'ListDingtalkGroupWebhook',
      requestType: ListDingtalkGroupWebhookReq,
      requestStream: false,
      responseType: ListDingtalkGroupWebhookRsp,
      responseStream: false,
      options: {}
    },
    /** 新增 */
    createDingtalkGroupWebhook: {
      name: 'CreateDingtalkGroupWebhook',
      requestType: CreateDingtalkGroupWebhookReq,
      requestStream: false,
      responseType: CreateDingtalkGroupWebhookRsp,
      responseStream: false,
      options: {}
    },
    /** 更新 */
    updateDingtalkGroupWebhook: {
      name: 'UpdateDingtalkGroupWebhook',
      requestType: UpdateDingtalkGroupWebhookReq,
      requestStream: false,
      responseType: UpdateDingtalkGroupWebhookRsp,
      responseStream: false,
      options: {}
    },
    /** 删除 */
    deleteDingtalkGroupWebhook: {
      name: 'DeleteDingtalkGroupWebhook',
      requestType: DeleteDingtalkGroupWebhookReq,
      requestStream: false,
      responseType: DeleteDingtalkGroupWebhookRsp,
      responseStream: false,
      options: {}
    },
    /** 获取支持的 app name 列表 */
    listAppName: {
      name: 'ListAppName',
      requestType: ListAppNameReq,
      requestStream: false,
      responseType: ListAppNameRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
