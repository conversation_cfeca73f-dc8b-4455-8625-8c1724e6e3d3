// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/im_notification.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { MessageKind, messageKindFromJSON, UserSelector, userSelectorFromJSON, Version } from './notification';
import { JumpType, jumpTypeFromJSON } from './notification_source';

export const protobufPackage = 'comm.mgr.platform.notification';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/im_notification/handlermgr */

export enum IMPublishStatus {
  IM_PUBLISH_STATUS_NONE = 0,
  /** IM_PUBLISH_STATUS_WATING - 等待发布 */
  IM_PUBLISH_STATUS_WATING = 1,
  /** IM_PUBLISH_STATUS_PUBLISHING - 发布中 */
  IM_PUBLISH_STATUS_PUBLISHING = 2,
  /** IM_PUBLISH_STATUS_SUCC - 发布成功 */
  IM_PUBLISH_STATUS_SUCC = 3,
  /** IM_PUBLISH_STATUS_FAIL - 发布失败 */
  IM_PUBLISH_STATUS_FAIL = 4,
  /** IM_PUBLISH_STATUS_TIMEOUT - 发布过期 */
  IM_PUBLISH_STATUS_TIMEOUT = 5,
  UNRECOGNIZED = -1
}

export function iMPublishStatusFromJSON(object: any): IMPublishStatus {
  switch (object) {
    case 0:
    case 'IM_PUBLISH_STATUS_NONE':
      return IMPublishStatus.IM_PUBLISH_STATUS_NONE;
    case 1:
    case 'IM_PUBLISH_STATUS_WATING':
      return IMPublishStatus.IM_PUBLISH_STATUS_WATING;
    case 2:
    case 'IM_PUBLISH_STATUS_PUBLISHING':
      return IMPublishStatus.IM_PUBLISH_STATUS_PUBLISHING;
    case 3:
    case 'IM_PUBLISH_STATUS_SUCC':
      return IMPublishStatus.IM_PUBLISH_STATUS_SUCC;
    case 4:
    case 'IM_PUBLISH_STATUS_FAIL':
      return IMPublishStatus.IM_PUBLISH_STATUS_FAIL;
    case 5:
    case 'IM_PUBLISH_STATUS_TIMEOUT':
      return IMPublishStatus.IM_PUBLISH_STATUS_TIMEOUT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return IMPublishStatus.UNRECOGNIZED;
  }
}

export enum IMDeleteStatus {
  IM_DELETE_STSTUS_NONE = 0,
  /** IM_DELETE_STSTUS_VALID - 有效 */
  IM_DELETE_STSTUS_VALID = 10,
  /** IM_DELETE_STSTUS_DELETED - 已删除 */
  IM_DELETE_STSTUS_DELETED = 20,
  UNRECOGNIZED = -1
}

export function iMDeleteStatusFromJSON(object: any): IMDeleteStatus {
  switch (object) {
    case 0:
    case 'IM_DELETE_STSTUS_NONE':
      return IMDeleteStatus.IM_DELETE_STSTUS_NONE;
    case 10:
    case 'IM_DELETE_STSTUS_VALID':
      return IMDeleteStatus.IM_DELETE_STSTUS_VALID;
    case 20:
    case 'IM_DELETE_STSTUS_DELETED':
      return IMDeleteStatus.IM_DELETE_STSTUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return IMDeleteStatus.UNRECOGNIZED;
  }
}

export interface IMNotification {
  /** 唯一标识,ADD时为0，其他操作不能为0 */
  id: number;
  /** en: 英文标题, zh: 中文标题 ar: 阿拉伯标题 */
  title: { [key: string]: string };
  /** en: 英文body, zh: 中文body ar: 阿拉伯标题 */
  body: { [key: string]: string };
  /** en: 英文图, zh: 中文图 ar: 阿拉伯图 */
  img: { [key: string]: string };
  /** en: 高, zh: 高 ar: 高 */
  img_height: { [key: string]: number };
  /** en: 宽, zh: 宽 ar: 宽 */
  img_width: { [key: string]: number };
  /** 跳转类型 */
  jump_type: JumpType;
  /** 跳转链接 */
  jump_link: string;
  /** 发给的用户类型 */
  user_selector: UserSelector;
  /** 用户id，或者用户包id */
  target_ids: string[];
  /** 推送时间 */
  plan_push_time: number;
  /** 推送时间的时区 */
  timezone: string;
  /** 消息类型 */
  message_kind: MessageKind;
  /** app版本区间 */
  app_version: Version | undefined;
  /** ios版本区间 */
  ios_version: Version | undefined;
  /** android版本区间 */
  android_version: Version | undefined;
  /** 备注 */
  comment: string;
  /** update时，以下字段无效 */
  actual_push_time: number;
  /** 推送状态 */
  publish_status: IMPublishStatus;
  /** 创建时间 */
  create_at: number;
  /** 修改时间 */
  update_at: number;
  /** 操作者 */
  operator: string;
  /** 操作人unionid */
  operator_uid: string;
  /** 删除状态 */
  delete_status: IMDeleteStatus;
  /** 开关是否打开 */
  up: boolean;
}

export interface IMNotification_TitleEntry {
  key: string;
  value: string;
}

export interface IMNotification_BodyEntry {
  key: string;
  value: string;
}

export interface IMNotification_ImgEntry {
  key: string;
  value: string;
}

export interface IMNotification_ImgHeightEntry {
  key: string;
  value: number;
}

export interface IMNotification_ImgWidthEntry {
  key: string;
  value: number;
}

export interface AddIMNotificationReq {
  data: IMNotification | undefined;
}

export interface AddIMNotificationRsp {
  id: number;
}

export interface DelIMNotificationReq {
  ids: number[];
}

export interface DelIMNotificationRsp {}

export interface UpdateIMNotificationReq {
  data: IMNotification | undefined;
  /** 把当前行的update_at时间带过来，防止没刷新的情况 */
  curr_update_at: number;
  /** 把当前状态带过来，防止没刷新的情况 */
  curr_publish_status: IMPublishStatus;
}

export interface UpdateIMNotificationRsp {
  data: IMNotification | undefined;
}

export interface ListIMNotificationsReq {
  page: Page | undefined;
  id: number;
  publish_status: IMPublishStatus;
  /** 跳转类型 */
  jump_type: JumpType;
  actual_push_begin: number;
  actual_push_end: number;
  create_begin: number;
  create_end: number;
  delete_status: IMDeleteStatus;
  operators: string[];
  operator_unionids: string[];
}

export interface ListIMNotificationsRsp {
  page: Page | undefined;
  data: IMNotification[];
}

export interface UpIMNotificationReq {
  id: number;
  up: boolean;
  /** 把当前状态带过来，防止没刷新的情况 */
  curr_publish_status: IMPublishStatus;
  /** 把当前行的update_at时间带过来，防止没刷新的情况 */
  curr_update_at: number;
}

export interface UpIMNotificationRsp {}

export interface NotificationCrowd {
  /** 人群包对应的id，根据id，可以直接对应着多个用户 */
  id: string;
  /** 人群包对应的tag信息，用于展示 */
  tag: string;
  /** 人群包的描述 */
  desc: string;
}

export interface GetNotificationCrowdReq {
  page: Page | undefined;
}

export interface GetNotificationCrowdRsp {
  page: Page | undefined;
  /** 人群包列表 */
  crowds: NotificationCrowd[];
}

export interface NotificationUserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
}

export interface GetNotificationUserInfoReq {
  /** 包含了uid和show_uid,最多为500个 */
  all_uids: string[];
}

export interface GetNotificationUserInfoRsp {
  users: NotificationUserInfo[];
}

function createBaseIMNotification(): IMNotification {
  return {
    id: 0,
    title: {},
    body: {},
    img: {},
    img_height: {},
    img_width: {},
    jump_type: 0,
    jump_link: '',
    user_selector: 0,
    target_ids: [],
    plan_push_time: 0,
    timezone: '',
    message_kind: 0,
    app_version: undefined,
    ios_version: undefined,
    android_version: undefined,
    comment: '',
    actual_push_time: 0,
    publish_status: 0,
    create_at: 0,
    update_at: 0,
    operator: '',
    operator_uid: '',
    delete_status: 0,
    up: false
  };
}

export const IMNotification: MessageFns<IMNotification> = {
  fromJSON(object: any): IMNotification {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      title: isObject(object.title)
        ? Object.entries(object.title).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      body: isObject(object.body)
        ? Object.entries(object.body).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      img: isObject(object.img)
        ? Object.entries(object.img).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      img_height: isObject(object.img_height)
        ? Object.entries(object.img_height).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      img_width: isObject(object.img_width)
        ? Object.entries(object.img_width).reduce<{ [key: string]: number }>((acc, [key, value]) => {
            acc[key] = Number(value);
            return acc;
          }, {})
        : {},
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      jump_link: isSet(object.jump_link) ? globalThis.String(object.jump_link) : '',
      user_selector: isSet(object.user_selector) ? userSelectorFromJSON(object.user_selector) : 0,
      target_ids: globalThis.Array.isArray(object?.target_ids)
        ? object.target_ids.map((e: any) => globalThis.String(e))
        : [],
      plan_push_time: isSet(object.plan_push_time) ? globalThis.Number(object.plan_push_time) : 0,
      timezone: isSet(object.timezone) ? globalThis.String(object.timezone) : '',
      message_kind: isSet(object.message_kind) ? messageKindFromJSON(object.message_kind) : 0,
      app_version: isSet(object.app_version) ? Version.fromJSON(object.app_version) : undefined,
      ios_version: isSet(object.ios_version) ? Version.fromJSON(object.ios_version) : undefined,
      android_version: isSet(object.android_version) ? Version.fromJSON(object.android_version) : undefined,
      comment: isSet(object.comment) ? globalThis.String(object.comment) : '',
      actual_push_time: isSet(object.actual_push_time) ? globalThis.Number(object.actual_push_time) : 0,
      publish_status: isSet(object.publish_status) ? iMPublishStatusFromJSON(object.publish_status) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      operator_uid: isSet(object.operator_uid) ? globalThis.String(object.operator_uid) : '',
      delete_status: isSet(object.delete_status) ? iMDeleteStatusFromJSON(object.delete_status) : 0,
      up: isSet(object.up) ? globalThis.Boolean(object.up) : false
    };
  },

  create<I extends Exact<DeepPartial<IMNotification>, I>>(base?: I): IMNotification {
    return IMNotification.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification>, I>>(object: I): IMNotification {
    const message = createBaseIMNotification();
    message.id = object.id ?? 0;
    message.title = Object.entries(object.title ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.body = Object.entries(object.body ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.img = Object.entries(object.img ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.img_height = Object.entries(object.img_height ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.img_width = Object.entries(object.img_width ?? {}).reduce<{ [key: string]: number }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.Number(value);
        }
        return acc;
      },
      {}
    );
    message.jump_type = object.jump_type ?? 0;
    message.jump_link = object.jump_link ?? '';
    message.user_selector = object.user_selector ?? 0;
    message.target_ids = object.target_ids?.map(e => e) || [];
    message.plan_push_time = object.plan_push_time ?? 0;
    message.timezone = object.timezone ?? '';
    message.message_kind = object.message_kind ?? 0;
    message.app_version =
      object.app_version !== undefined && object.app_version !== null
        ? Version.fromPartial(object.app_version)
        : undefined;
    message.ios_version =
      object.ios_version !== undefined && object.ios_version !== null
        ? Version.fromPartial(object.ios_version)
        : undefined;
    message.android_version =
      object.android_version !== undefined && object.android_version !== null
        ? Version.fromPartial(object.android_version)
        : undefined;
    message.comment = object.comment ?? '';
    message.actual_push_time = object.actual_push_time ?? 0;
    message.publish_status = object.publish_status ?? 0;
    message.create_at = object.create_at ?? 0;
    message.update_at = object.update_at ?? 0;
    message.operator = object.operator ?? '';
    message.operator_uid = object.operator_uid ?? '';
    message.delete_status = object.delete_status ?? 0;
    message.up = object.up ?? false;
    return message;
  }
};

function createBaseIMNotification_TitleEntry(): IMNotification_TitleEntry {
  return { key: '', value: '' };
}

export const IMNotification_TitleEntry: MessageFns<IMNotification_TitleEntry> = {
  fromJSON(object: any): IMNotification_TitleEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<IMNotification_TitleEntry>, I>>(base?: I): IMNotification_TitleEntry {
    return IMNotification_TitleEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification_TitleEntry>, I>>(object: I): IMNotification_TitleEntry {
    const message = createBaseIMNotification_TitleEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseIMNotification_BodyEntry(): IMNotification_BodyEntry {
  return { key: '', value: '' };
}

export const IMNotification_BodyEntry: MessageFns<IMNotification_BodyEntry> = {
  fromJSON(object: any): IMNotification_BodyEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<IMNotification_BodyEntry>, I>>(base?: I): IMNotification_BodyEntry {
    return IMNotification_BodyEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification_BodyEntry>, I>>(object: I): IMNotification_BodyEntry {
    const message = createBaseIMNotification_BodyEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseIMNotification_ImgEntry(): IMNotification_ImgEntry {
  return { key: '', value: '' };
}

export const IMNotification_ImgEntry: MessageFns<IMNotification_ImgEntry> = {
  fromJSON(object: any): IMNotification_ImgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<IMNotification_ImgEntry>, I>>(base?: I): IMNotification_ImgEntry {
    return IMNotification_ImgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification_ImgEntry>, I>>(object: I): IMNotification_ImgEntry {
    const message = createBaseIMNotification_ImgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseIMNotification_ImgHeightEntry(): IMNotification_ImgHeightEntry {
  return { key: '', value: 0 };
}

export const IMNotification_ImgHeightEntry: MessageFns<IMNotification_ImgHeightEntry> = {
  fromJSON(object: any): IMNotification_ImgHeightEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<IMNotification_ImgHeightEntry>, I>>(base?: I): IMNotification_ImgHeightEntry {
    return IMNotification_ImgHeightEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification_ImgHeightEntry>, I>>(
    object: I
  ): IMNotification_ImgHeightEntry {
    const message = createBaseIMNotification_ImgHeightEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseIMNotification_ImgWidthEntry(): IMNotification_ImgWidthEntry {
  return { key: '', value: 0 };
}

export const IMNotification_ImgWidthEntry: MessageFns<IMNotification_ImgWidthEntry> = {
  fromJSON(object: any): IMNotification_ImgWidthEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.Number(object.value) : 0
    };
  },

  create<I extends Exact<DeepPartial<IMNotification_ImgWidthEntry>, I>>(base?: I): IMNotification_ImgWidthEntry {
    return IMNotification_ImgWidthEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<IMNotification_ImgWidthEntry>, I>>(object: I): IMNotification_ImgWidthEntry {
    const message = createBaseIMNotification_ImgWidthEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? 0;
    return message;
  }
};

function createBaseAddIMNotificationReq(): AddIMNotificationReq {
  return { data: undefined };
}

export const AddIMNotificationReq: MessageFns<AddIMNotificationReq> = {
  fromJSON(object: any): AddIMNotificationReq {
    return { data: isSet(object.data) ? IMNotification.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<AddIMNotificationReq>, I>>(base?: I): AddIMNotificationReq {
    return AddIMNotificationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddIMNotificationReq>, I>>(object: I): AddIMNotificationReq {
    const message = createBaseAddIMNotificationReq();
    message.data =
      object.data !== undefined && object.data !== null ? IMNotification.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseAddIMNotificationRsp(): AddIMNotificationRsp {
  return { id: 0 };
}

export const AddIMNotificationRsp: MessageFns<AddIMNotificationRsp> = {
  fromJSON(object: any): AddIMNotificationRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddIMNotificationRsp>, I>>(base?: I): AddIMNotificationRsp {
    return AddIMNotificationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddIMNotificationRsp>, I>>(object: I): AddIMNotificationRsp {
    const message = createBaseAddIMNotificationRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDelIMNotificationReq(): DelIMNotificationReq {
  return { ids: [] };
}

export const DelIMNotificationReq: MessageFns<DelIMNotificationReq> = {
  fromJSON(object: any): DelIMNotificationReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DelIMNotificationReq>, I>>(base?: I): DelIMNotificationReq {
    return DelIMNotificationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelIMNotificationReq>, I>>(object: I): DelIMNotificationReq {
    const message = createBaseDelIMNotificationReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDelIMNotificationRsp(): DelIMNotificationRsp {
  return {};
}

export const DelIMNotificationRsp: MessageFns<DelIMNotificationRsp> = {
  fromJSON(_: any): DelIMNotificationRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DelIMNotificationRsp>, I>>(base?: I): DelIMNotificationRsp {
    return DelIMNotificationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelIMNotificationRsp>, I>>(_: I): DelIMNotificationRsp {
    const message = createBaseDelIMNotificationRsp();
    return message;
  }
};

function createBaseUpdateIMNotificationReq(): UpdateIMNotificationReq {
  return { data: undefined, curr_update_at: 0, curr_publish_status: 0 };
}

export const UpdateIMNotificationReq: MessageFns<UpdateIMNotificationReq> = {
  fromJSON(object: any): UpdateIMNotificationReq {
    return {
      data: isSet(object.data) ? IMNotification.fromJSON(object.data) : undefined,
      curr_update_at: isSet(object.curr_update_at) ? globalThis.Number(object.curr_update_at) : 0,
      curr_publish_status: isSet(object.curr_publish_status) ? iMPublishStatusFromJSON(object.curr_publish_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpdateIMNotificationReq>, I>>(base?: I): UpdateIMNotificationReq {
    return UpdateIMNotificationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateIMNotificationReq>, I>>(object: I): UpdateIMNotificationReq {
    const message = createBaseUpdateIMNotificationReq();
    message.data =
      object.data !== undefined && object.data !== null ? IMNotification.fromPartial(object.data) : undefined;
    message.curr_update_at = object.curr_update_at ?? 0;
    message.curr_publish_status = object.curr_publish_status ?? 0;
    return message;
  }
};

function createBaseUpdateIMNotificationRsp(): UpdateIMNotificationRsp {
  return { data: undefined };
}

export const UpdateIMNotificationRsp: MessageFns<UpdateIMNotificationRsp> = {
  fromJSON(object: any): UpdateIMNotificationRsp {
    return { data: isSet(object.data) ? IMNotification.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateIMNotificationRsp>, I>>(base?: I): UpdateIMNotificationRsp {
    return UpdateIMNotificationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateIMNotificationRsp>, I>>(object: I): UpdateIMNotificationRsp {
    const message = createBaseUpdateIMNotificationRsp();
    message.data =
      object.data !== undefined && object.data !== null ? IMNotification.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseListIMNotificationsReq(): ListIMNotificationsReq {
  return {
    page: undefined,
    id: 0,
    publish_status: 0,
    jump_type: 0,
    actual_push_begin: 0,
    actual_push_end: 0,
    create_begin: 0,
    create_end: 0,
    delete_status: 0,
    operators: [],
    operator_unionids: []
  };
}

export const ListIMNotificationsReq: MessageFns<ListIMNotificationsReq> = {
  fromJSON(object: any): ListIMNotificationsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      publish_status: isSet(object.publish_status) ? iMPublishStatusFromJSON(object.publish_status) : 0,
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      actual_push_begin: isSet(object.actual_push_begin) ? globalThis.Number(object.actual_push_begin) : 0,
      actual_push_end: isSet(object.actual_push_end) ? globalThis.Number(object.actual_push_end) : 0,
      create_begin: isSet(object.create_begin) ? globalThis.Number(object.create_begin) : 0,
      create_end: isSet(object.create_end) ? globalThis.Number(object.create_end) : 0,
      delete_status: isSet(object.delete_status) ? iMDeleteStatusFromJSON(object.delete_status) : 0,
      operators: globalThis.Array.isArray(object?.operators)
        ? object.operators.map((e: any) => globalThis.String(e))
        : [],
      operator_unionids: globalThis.Array.isArray(object?.operator_unionids)
        ? object.operator_unionids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListIMNotificationsReq>, I>>(base?: I): ListIMNotificationsReq {
    return ListIMNotificationsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListIMNotificationsReq>, I>>(object: I): ListIMNotificationsReq {
    const message = createBaseListIMNotificationsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.publish_status = object.publish_status ?? 0;
    message.jump_type = object.jump_type ?? 0;
    message.actual_push_begin = object.actual_push_begin ?? 0;
    message.actual_push_end = object.actual_push_end ?? 0;
    message.create_begin = object.create_begin ?? 0;
    message.create_end = object.create_end ?? 0;
    message.delete_status = object.delete_status ?? 0;
    message.operators = object.operators?.map(e => e) || [];
    message.operator_unionids = object.operator_unionids?.map(e => e) || [];
    return message;
  }
};

function createBaseListIMNotificationsRsp(): ListIMNotificationsRsp {
  return { page: undefined, data: [] };
}

export const ListIMNotificationsRsp: MessageFns<ListIMNotificationsRsp> = {
  fromJSON(object: any): ListIMNotificationsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => IMNotification.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListIMNotificationsRsp>, I>>(base?: I): ListIMNotificationsRsp {
    return ListIMNotificationsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListIMNotificationsRsp>, I>>(object: I): ListIMNotificationsRsp {
    const message = createBaseListIMNotificationsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => IMNotification.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUpIMNotificationReq(): UpIMNotificationReq {
  return { id: 0, up: false, curr_publish_status: 0, curr_update_at: 0 };
}

export const UpIMNotificationReq: MessageFns<UpIMNotificationReq> = {
  fromJSON(object: any): UpIMNotificationReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      up: isSet(object.up) ? globalThis.Boolean(object.up) : false,
      curr_publish_status: isSet(object.curr_publish_status) ? iMPublishStatusFromJSON(object.curr_publish_status) : 0,
      curr_update_at: isSet(object.curr_update_at) ? globalThis.Number(object.curr_update_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<UpIMNotificationReq>, I>>(base?: I): UpIMNotificationReq {
    return UpIMNotificationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpIMNotificationReq>, I>>(object: I): UpIMNotificationReq {
    const message = createBaseUpIMNotificationReq();
    message.id = object.id ?? 0;
    message.up = object.up ?? false;
    message.curr_publish_status = object.curr_publish_status ?? 0;
    message.curr_update_at = object.curr_update_at ?? 0;
    return message;
  }
};

function createBaseUpIMNotificationRsp(): UpIMNotificationRsp {
  return {};
}

export const UpIMNotificationRsp: MessageFns<UpIMNotificationRsp> = {
  fromJSON(_: any): UpIMNotificationRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpIMNotificationRsp>, I>>(base?: I): UpIMNotificationRsp {
    return UpIMNotificationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpIMNotificationRsp>, I>>(_: I): UpIMNotificationRsp {
    const message = createBaseUpIMNotificationRsp();
    return message;
  }
};

function createBaseNotificationCrowd(): NotificationCrowd {
  return { id: '', tag: '', desc: '' };
}

export const NotificationCrowd: MessageFns<NotificationCrowd> = {
  fromJSON(object: any): NotificationCrowd {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      tag: isSet(object.tag) ? globalThis.String(object.tag) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotificationCrowd>, I>>(base?: I): NotificationCrowd {
    return NotificationCrowd.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotificationCrowd>, I>>(object: I): NotificationCrowd {
    const message = createBaseNotificationCrowd();
    message.id = object.id ?? '';
    message.tag = object.tag ?? '';
    message.desc = object.desc ?? '';
    return message;
  }
};

function createBaseGetNotificationCrowdReq(): GetNotificationCrowdReq {
  return { page: undefined };
}

export const GetNotificationCrowdReq: MessageFns<GetNotificationCrowdReq> = {
  fromJSON(object: any): GetNotificationCrowdReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetNotificationCrowdReq>, I>>(base?: I): GetNotificationCrowdReq {
    return GetNotificationCrowdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNotificationCrowdReq>, I>>(object: I): GetNotificationCrowdReq {
    const message = createBaseGetNotificationCrowdReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetNotificationCrowdRsp(): GetNotificationCrowdRsp {
  return { page: undefined, crowds: [] };
}

export const GetNotificationCrowdRsp: MessageFns<GetNotificationCrowdRsp> = {
  fromJSON(object: any): GetNotificationCrowdRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      crowds: globalThis.Array.isArray(object?.crowds)
        ? object.crowds.map((e: any) => NotificationCrowd.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetNotificationCrowdRsp>, I>>(base?: I): GetNotificationCrowdRsp {
    return GetNotificationCrowdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNotificationCrowdRsp>, I>>(object: I): GetNotificationCrowdRsp {
    const message = createBaseGetNotificationCrowdRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.crowds = object.crowds?.map(e => NotificationCrowd.fromPartial(e)) || [];
    return message;
  }
};

function createBaseNotificationUserInfo(): NotificationUserInfo {
  return { uid: 0, show_uid: '', nickname: '', avatar: '' };
}

export const NotificationUserInfo: MessageFns<NotificationUserInfo> = {
  fromJSON(object: any): NotificationUserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : ''
    };
  },

  create<I extends Exact<DeepPartial<NotificationUserInfo>, I>>(base?: I): NotificationUserInfo {
    return NotificationUserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<NotificationUserInfo>, I>>(object: I): NotificationUserInfo {
    const message = createBaseNotificationUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    return message;
  }
};

function createBaseGetNotificationUserInfoReq(): GetNotificationUserInfoReq {
  return { all_uids: [] };
}

export const GetNotificationUserInfoReq: MessageFns<GetNotificationUserInfoReq> = {
  fromJSON(object: any): GetNotificationUserInfoReq {
    return {
      all_uids: globalThis.Array.isArray(object?.all_uids) ? object.all_uids.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetNotificationUserInfoReq>, I>>(base?: I): GetNotificationUserInfoReq {
    return GetNotificationUserInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNotificationUserInfoReq>, I>>(object: I): GetNotificationUserInfoReq {
    const message = createBaseGetNotificationUserInfoReq();
    message.all_uids = object.all_uids?.map(e => e) || [];
    return message;
  }
};

function createBaseGetNotificationUserInfoRsp(): GetNotificationUserInfoRsp {
  return { users: [] };
}

export const GetNotificationUserInfoRsp: MessageFns<GetNotificationUserInfoRsp> = {
  fromJSON(object: any): GetNotificationUserInfoRsp {
    return {
      users: globalThis.Array.isArray(object?.users)
        ? object.users.map((e: any) => NotificationUserInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<GetNotificationUserInfoRsp>, I>>(base?: I): GetNotificationUserInfoRsp {
    return GetNotificationUserInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetNotificationUserInfoRsp>, I>>(object: I): GetNotificationUserInfoRsp {
    const message = createBaseGetNotificationUserInfoRsp();
    message.users = object.users?.map(e => NotificationUserInfo.fromPartial(e)) || [];
    return message;
  }
};

export type IMNotificationMgrDefinition = typeof IMNotificationMgrDefinition;
export const IMNotificationMgrDefinition = {
  name: 'IMNotificationMgr',
  fullName: 'comm.mgr.platform.notification.IMNotificationMgr',
  methods: {
    add: {
      name: 'Add',
      requestType: AddIMNotificationReq,
      requestStream: false,
      responseType: AddIMNotificationRsp,
      responseStream: false,
      options: {}
    },
    del: {
      name: 'Del',
      requestType: DelIMNotificationReq,
      requestStream: false,
      responseType: DelIMNotificationRsp,
      responseStream: false,
      options: {}
    },
    update: {
      name: 'Update',
      requestType: UpdateIMNotificationReq,
      requestStream: false,
      responseType: UpdateIMNotificationRsp,
      responseStream: false,
      options: {}
    },
    list: {
      name: 'List',
      requestType: ListIMNotificationsReq,
      requestStream: false,
      responseType: ListIMNotificationsRsp,
      responseStream: false,
      options: {}
    },
    /** 上下架 */
    up: {
      name: 'Up',
      requestType: UpIMNotificationReq,
      requestStream: false,
      responseType: UpIMNotificationRsp,
      responseStream: false,
      options: {}
    },
    /** 获取人群包列表 */
    getNotificationCrowd: {
      name: 'GetNotificationCrowd',
      requestType: GetNotificationCrowdReq,
      requestStream: false,
      responseType: GetNotificationCrowdRsp,
      responseStream: false,
      options: {}
    },
    /** 获取用户信息 */
    getNotificationUserInfo: {
      name: 'GetNotificationUserInfo',
      requestType: GetNotificationUserInfoReq,
      requestStream: false,
      responseType: GetNotificationUserInfoRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
