// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/notification_doc.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.notification';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/notification/handlermgr/notification_doc.go */

export interface Doc {
  /** en: 英文标题, zh: 中文标题 ar: 阿拉伯标题 */
  title: { [key: string]: string };
  /** "en": 英文body, "zh": 中文body "ar": 阿拉伯标题 */
  body: { [key: string]: string };
  /** 备注 */
  comment: string;
  /** 操作者 */
  operator: string;
  /** 唯一标识 */
  id: number;
  /** 创建时间 */
  create_time: number;
  /** 修改时间 */
  update_time: number;
  /** 是否已经删除 */
  deleted: boolean;
}

export interface Doc_TitleEntry {
  key: string;
  value: string;
}

export interface Doc_BodyEntry {
  key: string;
  value: string;
}

export interface AddDocReq {
  /** en: 英文标题, zh: 中文标题 ar: 阿拉伯标题 */
  title: { [key: string]: string };
  /** "en": 英文body, "zh": 中文body "ar": 阿拉伯标题 */
  body: { [key: string]: string };
  /** 备注 */
  comment: string;
  /** 操作者 */
  operator: string;
}

export interface AddDocReq_TitleEntry {
  key: string;
  value: string;
}

export interface AddDocReq_BodyEntry {
  key: string;
  value: string;
}

export interface AddDocRsp {
  id: number;
}

export interface DelDocReq {
  ids: number[];
  operator: string;
}

export interface DelDocRsp {
  count: number;
}

export interface UpdateDocReq {
  req: Doc | undefined;
  operator: string;
}

export interface UpdateDocRsp {
  rsp: Doc | undefined;
}

export interface GetDocReq {
  id: number;
}

export interface GetDocRsp {
  resp: Doc | undefined;
}

export interface ListDocsReq {
  page: Page | undefined;
  id: number;
  operator: string;
  begin: number;
  end: number;
}

export interface ListDocsRsp {
  page: Page | undefined;
  list: Doc[];
}

function createBaseDoc(): Doc {
  return { title: {}, body: {}, comment: '', operator: '', id: 0, create_time: 0, update_time: 0, deleted: false };
}

export const Doc: MessageFns<Doc> = {
  fromJSON(object: any): Doc {
    return {
      title: isObject(object.title)
        ? Object.entries(object.title).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      body: isObject(object.body)
        ? Object.entries(object.body).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      comment: isSet(object.comment) ? globalThis.String(object.comment) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      create_time: isSet(object.create_time) ? globalThis.Number(object.create_time) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false
    };
  },

  create<I extends Exact<DeepPartial<Doc>, I>>(base?: I): Doc {
    return Doc.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Doc>, I>>(object: I): Doc {
    const message = createBaseDoc();
    message.title = Object.entries(object.title ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.body = Object.entries(object.body ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.comment = object.comment ?? '';
    message.operator = object.operator ?? '';
    message.id = object.id ?? 0;
    message.create_time = object.create_time ?? 0;
    message.update_time = object.update_time ?? 0;
    message.deleted = object.deleted ?? false;
    return message;
  }
};

function createBaseDoc_TitleEntry(): Doc_TitleEntry {
  return { key: '', value: '' };
}

export const Doc_TitleEntry: MessageFns<Doc_TitleEntry> = {
  fromJSON(object: any): Doc_TitleEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Doc_TitleEntry>, I>>(base?: I): Doc_TitleEntry {
    return Doc_TitleEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Doc_TitleEntry>, I>>(object: I): Doc_TitleEntry {
    const message = createBaseDoc_TitleEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseDoc_BodyEntry(): Doc_BodyEntry {
  return { key: '', value: '' };
}

export const Doc_BodyEntry: MessageFns<Doc_BodyEntry> = {
  fromJSON(object: any): Doc_BodyEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Doc_BodyEntry>, I>>(base?: I): Doc_BodyEntry {
    return Doc_BodyEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Doc_BodyEntry>, I>>(object: I): Doc_BodyEntry {
    const message = createBaseDoc_BodyEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddDocReq(): AddDocReq {
  return { title: {}, body: {}, comment: '', operator: '' };
}

export const AddDocReq: MessageFns<AddDocReq> = {
  fromJSON(object: any): AddDocReq {
    return {
      title: isObject(object.title)
        ? Object.entries(object.title).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      body: isObject(object.body)
        ? Object.entries(object.body).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      comment: isSet(object.comment) ? globalThis.String(object.comment) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddDocReq>, I>>(base?: I): AddDocReq {
    return AddDocReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDocReq>, I>>(object: I): AddDocReq {
    const message = createBaseAddDocReq();
    message.title = Object.entries(object.title ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.body = Object.entries(object.body ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.comment = object.comment ?? '';
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseAddDocReq_TitleEntry(): AddDocReq_TitleEntry {
  return { key: '', value: '' };
}

export const AddDocReq_TitleEntry: MessageFns<AddDocReq_TitleEntry> = {
  fromJSON(object: any): AddDocReq_TitleEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddDocReq_TitleEntry>, I>>(base?: I): AddDocReq_TitleEntry {
    return AddDocReq_TitleEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDocReq_TitleEntry>, I>>(object: I): AddDocReq_TitleEntry {
    const message = createBaseAddDocReq_TitleEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddDocReq_BodyEntry(): AddDocReq_BodyEntry {
  return { key: '', value: '' };
}

export const AddDocReq_BodyEntry: MessageFns<AddDocReq_BodyEntry> = {
  fromJSON(object: any): AddDocReq_BodyEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddDocReq_BodyEntry>, I>>(base?: I): AddDocReq_BodyEntry {
    return AddDocReq_BodyEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDocReq_BodyEntry>, I>>(object: I): AddDocReq_BodyEntry {
    const message = createBaseAddDocReq_BodyEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddDocRsp(): AddDocRsp {
  return { id: 0 };
}

export const AddDocRsp: MessageFns<AddDocRsp> = {
  fromJSON(object: any): AddDocRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddDocRsp>, I>>(base?: I): AddDocRsp {
    return AddDocRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddDocRsp>, I>>(object: I): AddDocRsp {
    const message = createBaseAddDocRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDelDocReq(): DelDocReq {
  return { ids: [], operator: '' };
}

export const DelDocReq: MessageFns<DelDocReq> = {
  fromJSON(object: any): DelDocReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<DelDocReq>, I>>(base?: I): DelDocReq {
    return DelDocReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelDocReq>, I>>(object: I): DelDocReq {
    const message = createBaseDelDocReq();
    message.ids = object.ids?.map(e => e) || [];
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseDelDocRsp(): DelDocRsp {
  return { count: 0 };
}

export const DelDocRsp: MessageFns<DelDocRsp> = {
  fromJSON(object: any): DelDocRsp {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  create<I extends Exact<DeepPartial<DelDocRsp>, I>>(base?: I): DelDocRsp {
    return DelDocRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelDocRsp>, I>>(object: I): DelDocRsp {
    const message = createBaseDelDocRsp();
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseUpdateDocReq(): UpdateDocReq {
  return { req: undefined, operator: '' };
}

export const UpdateDocReq: MessageFns<UpdateDocReq> = {
  fromJSON(object: any): UpdateDocReq {
    return {
      req: isSet(object.req) ? Doc.fromJSON(object.req) : undefined,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateDocReq>, I>>(base?: I): UpdateDocReq {
    return UpdateDocReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDocReq>, I>>(object: I): UpdateDocReq {
    const message = createBaseUpdateDocReq();
    message.req = object.req !== undefined && object.req !== null ? Doc.fromPartial(object.req) : undefined;
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseUpdateDocRsp(): UpdateDocRsp {
  return { rsp: undefined };
}

export const UpdateDocRsp: MessageFns<UpdateDocRsp> = {
  fromJSON(object: any): UpdateDocRsp {
    return { rsp: isSet(object.rsp) ? Doc.fromJSON(object.rsp) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateDocRsp>, I>>(base?: I): UpdateDocRsp {
    return UpdateDocRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDocRsp>, I>>(object: I): UpdateDocRsp {
    const message = createBaseUpdateDocRsp();
    message.rsp = object.rsp !== undefined && object.rsp !== null ? Doc.fromPartial(object.rsp) : undefined;
    return message;
  }
};

function createBaseGetDocReq(): GetDocReq {
  return { id: 0 };
}

export const GetDocReq: MessageFns<GetDocReq> = {
  fromJSON(object: any): GetDocReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetDocReq>, I>>(base?: I): GetDocReq {
    return GetDocReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDocReq>, I>>(object: I): GetDocReq {
    const message = createBaseGetDocReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetDocRsp(): GetDocRsp {
  return { resp: undefined };
}

export const GetDocRsp: MessageFns<GetDocRsp> = {
  fromJSON(object: any): GetDocRsp {
    return { resp: isSet(object.resp) ? Doc.fromJSON(object.resp) : undefined };
  },

  create<I extends Exact<DeepPartial<GetDocRsp>, I>>(base?: I): GetDocRsp {
    return GetDocRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDocRsp>, I>>(object: I): GetDocRsp {
    const message = createBaseGetDocRsp();
    message.resp = object.resp !== undefined && object.resp !== null ? Doc.fromPartial(object.resp) : undefined;
    return message;
  }
};

function createBaseListDocsReq(): ListDocsReq {
  return { page: undefined, id: 0, operator: '', begin: 0, end: 0 };
}

export const ListDocsReq: MessageFns<ListDocsReq> = {
  fromJSON(object: any): ListDocsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      begin: isSet(object.begin) ? globalThis.Number(object.begin) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListDocsReq>, I>>(base?: I): ListDocsReq {
    return ListDocsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDocsReq>, I>>(object: I): ListDocsReq {
    const message = createBaseListDocsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.operator = object.operator ?? '';
    message.begin = object.begin ?? 0;
    message.end = object.end ?? 0;
    return message;
  }
};

function createBaseListDocsRsp(): ListDocsRsp {
  return { page: undefined, list: [] };
}

export const ListDocsRsp: MessageFns<ListDocsRsp> = {
  fromJSON(object: any): ListDocsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => Doc.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListDocsRsp>, I>>(base?: I): ListDocsRsp {
    return ListDocsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDocsRsp>, I>>(object: I): ListDocsRsp {
    const message = createBaseListDocsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => Doc.fromPartial(e)) || [];
    return message;
  }
};

export type NotificationDocMgrDefinition = typeof NotificationDocMgrDefinition;
export const NotificationDocMgrDefinition = {
  name: 'NotificationDocMgr',
  fullName: 'comm.mgr.platform.notification.NotificationDocMgr',
  methods: {
    addDoc: {
      name: 'AddDoc',
      requestType: AddDocReq,
      requestStream: false,
      responseType: AddDocRsp,
      responseStream: false,
      options: {}
    },
    delDoc: {
      name: 'DelDoc',
      requestType: DelDocReq,
      requestStream: false,
      responseType: DelDocRsp,
      responseStream: false,
      options: {}
    },
    updateDoc: {
      name: 'UpdateDoc',
      requestType: UpdateDocReq,
      requestStream: false,
      responseType: UpdateDocRsp,
      responseStream: false,
      options: {}
    },
    getDoc: {
      name: 'GetDoc',
      requestType: GetDocReq,
      requestStream: false,
      responseType: GetDocRsp,
      responseStream: false,
      options: {}
    },
    listDocs: {
      name: 'ListDocs',
      requestType: ListDocsReq,
      requestStream: false,
      responseType: ListDocsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
