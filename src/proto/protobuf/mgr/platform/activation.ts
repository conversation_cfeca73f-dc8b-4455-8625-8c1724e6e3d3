// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/activation.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.platform.activation';

export interface DelResultForDidReq {
  /** 多个版本号 */
  dids: string[];
}

export interface DelResultForDidRsp {}

function createBaseDelResultForDidReq(): DelResultForDidReq {
  return { dids: [] };
}

export const DelResultForDidReq: MessageFns<DelResultForDidReq> = {
  fromJSON(object: any): DelResultForDidReq {
    return { dids: globalThis.Array.isArray(object?.dids) ? object.dids.map((e: any) => globalThis.String(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DelResultForDidReq>, I>>(base?: I): DelResultForDidReq {
    return DelResultForDidReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelResultForDidReq>, I>>(object: I): DelResultForDidReq {
    const message = createBaseDelResultForDidReq();
    message.dids = object.dids?.map(e => e) || [];
    return message;
  }
};

function createBaseDelResultForDidRsp(): DelResultForDidRsp {
  return {};
}

export const DelResultForDidRsp: MessageFns<DelResultForDidRsp> = {
  fromJSON(_: any): DelResultForDidRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DelResultForDidRsp>, I>>(base?: I): DelResultForDidRsp {
    return DelResultForDidRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelResultForDidRsp>, I>>(_: I): DelResultForDidRsp {
    const message = createBaseDelResultForDidRsp();
    return message;
  }
};

/** 归因MGR */
export type ActivationMgrDefinition = typeof ActivationMgrDefinition;
export const ActivationMgrDefinition = {
  name: 'ActivationMgr',
  fullName: 'comm.mgr.platform.activation.ActivationMgr',
  methods: {
    delResultForDid: {
      name: 'DelResultForDid',
      requestType: DelResultForDidReq,
      requestStream: false,
      responseType: DelResultForDidRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
