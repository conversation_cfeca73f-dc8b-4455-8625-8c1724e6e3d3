// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/notification.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { Doc } from './notification_doc';
import { Source } from './notification_source';

export const protobufPackage = 'comm.mgr.platform.notification';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/notification/handlermgr/notification.go */

export enum MessageKind {
  MSSAGE_KIND_NONE = 0,
  /** MSSAGE_KIND_SYSTEM - 系统 */
  MSSAGE_KIND_SYSTEM = 1,
  /** MSSAGE_KIND_OPERATION - 运营 */
  MSSAGE_KIND_OPERATION = 2,
  /** MSSAGE_KIND_OFFICIAL - 官方 */
  MSSAGE_KIND_OFFICIAL = 3,
  UNRECOGNIZED = -1
}

export function messageKind<PERSON>romJSON(object: any): MessageKind {
  switch (object) {
    case 0:
    case 'MSSAGE_KIND_NONE':
      return MessageKind.MSSAGE_KIND_NONE;
    case 1:
    case 'MSSAGE_KIND_SYSTEM':
      return MessageKind.MSSAGE_KIND_SYSTEM;
    case 2:
    case 'MSSAGE_KIND_OPERATION':
      return MessageKind.MSSAGE_KIND_OPERATION;
    case 3:
    case 'MSSAGE_KIND_OFFICIAL':
      return MessageKind.MSSAGE_KIND_OFFICIAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return MessageKind.UNRECOGNIZED;
  }
}

export enum UserSelector {
  USER_SELECTOR_NONE = 0,
  USER_SELECTOR_ALL = 1,
  USER_SELECTOR_IDS = 2,
  USER_SELECTOR_CROWD = 3,
  UNRECOGNIZED = -1
}

export function userSelectorFromJSON(object: any): UserSelector {
  switch (object) {
    case 0:
    case 'USER_SELECTOR_NONE':
      return UserSelector.USER_SELECTOR_NONE;
    case 1:
    case 'USER_SELECTOR_ALL':
      return UserSelector.USER_SELECTOR_ALL;
    case 2:
    case 'USER_SELECTOR_IDS':
      return UserSelector.USER_SELECTOR_IDS;
    case 3:
    case 'USER_SELECTOR_CROWD':
      return UserSelector.USER_SELECTOR_CROWD;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserSelector.UNRECOGNIZED;
  }
}

export enum PushStatus {
  /** PUSH_STATUS_NONE - 未启动 */
  PUSH_STATUS_NONE = 0,
  /** PUSH_STATUS_WATING - 等待发布 */
  PUSH_STATUS_WATING = 1,
  /** PUSH_STATUS_SUCC - 发布成功 */
  PUSH_STATUS_SUCC = 2,
  /** PUSH_STATUS_FAIL - 发布失败 */
  PUSH_STATUS_FAIL = 3,
  UNRECOGNIZED = -1
}

export function pushStatusFromJSON(object: any): PushStatus {
  switch (object) {
    case 0:
    case 'PUSH_STATUS_NONE':
      return PushStatus.PUSH_STATUS_NONE;
    case 1:
    case 'PUSH_STATUS_WATING':
      return PushStatus.PUSH_STATUS_WATING;
    case 2:
    case 'PUSH_STATUS_SUCC':
      return PushStatus.PUSH_STATUS_SUCC;
    case 3:
    case 'PUSH_STATUS_FAIL':
      return PushStatus.PUSH_STATUS_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PushStatus.UNRECOGNIZED;
  }
}

export interface Version {
  /** 最小版本号 */
  begin: number;
  /** 最大版本号 */
  end: number;
  /** 0- 部分 1- 所有 */
  is_all: boolean;
}

export interface Notification {
  /** 关联的文本id */
  doc_id: number;
  /** 关联的内容源id */
  source_id: number;
  /** 发给的用户 1- 所有 2- 白名单用户 */
  user_selector: UserSelector;
  /** 部分用户id */
  uids: number[];
  /** push 时间 */
  push_timer: PushTimer | undefined;
  /** 消息类型 */
  msg_kind: MessageKind;
  /** app版本 */
  app_version: Version | undefined;
  /** ios版本 */
  ios_version: Version | undefined;
  /** android 版本 */
  android_version: Version | undefined;
  /** 操作者 */
  operator: string;
  /** 唯一标识 */
  id: number;
  create_time: number;
  update_time: number;
  /** 是否已经删除 */
  deleted: boolean;
  /** 是否已经发布，0- 未发布 1- 已发布 */
  published: boolean;
  /** 关联的文本 */
  doc: Doc | undefined;
  /** 关联的内容源 */
  source: Source | undefined;
  /** 推送次数 */
  notify_times: number;
  /** 推送状态 */
  push_status: PushStatus;
  /** 推送动作时间 */
  push_action_time: number;
}

export interface PushTimer {
  /** 0- 等待到push_tm再发送，1- 马上发送 */
  push_now: boolean;
  /** 推送时间 */
  push_tm: number;
  /** 推送时间的时区 */
  timezone: string;
}

export interface AddReq {
  /** 关联的文本id */
  doc_id: number;
  /** 关联的内容源id */
  source_id: number;
  /** 发给的用户 1- 所有 2- 白名单用户 */
  user_selector: UserSelector;
  /** 部分用户id */
  uids: number[];
  /** push 时间 */
  push_timer: PushTimer | undefined;
  /** 消息类型 */
  msg_kind: MessageKind;
  /** app版本 */
  app_version: Version | undefined;
  /** ios版本 */
  ios_version: Version | undefined;
  /** android 版本 */
  android_version: Version | undefined;
  /** 操作者 */
  operator: string;
}

export interface AddRsp {
  id: number;
}

export interface DelReq {
  ids: number[];
  operator: string;
}

export interface DelRsp {
  count: number;
}

export interface UpdateReq {
  req: Notification | undefined;
  operator: string;
}

export interface UpdateRsp {
  rsp: Notification | undefined;
}

export interface GetReq {
  id: number;
}

export interface GetRsp {
  resp: Notification | undefined;
}

export interface ListReq {
  page: Page | undefined;
  id: number;
  doc_id: number;
  source_id: number;
  notify_begin: number;
  notify_end: number;
  push_status: PushStatus;
  manual_pub: boolean;
}

export interface ListRsp {
  page: Page | undefined;
  list: Notification[];
}

export interface PublishReq {
  id: number;
  published: boolean;
  operator: string;
}

export interface PublishRsp {}

function createBaseVersion(): Version {
  return { begin: 0, end: 0, is_all: false };
}

export const Version: MessageFns<Version> = {
  fromJSON(object: any): Version {
    return {
      begin: isSet(object.begin) ? globalThis.Number(object.begin) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0,
      is_all: isSet(object.is_all) ? globalThis.Boolean(object.is_all) : false
    };
  },

  create<I extends Exact<DeepPartial<Version>, I>>(base?: I): Version {
    return Version.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Version>, I>>(object: I): Version {
    const message = createBaseVersion();
    message.begin = object.begin ?? 0;
    message.end = object.end ?? 0;
    message.is_all = object.is_all ?? false;
    return message;
  }
};

function createBaseNotification(): Notification {
  return {
    doc_id: 0,
    source_id: 0,
    user_selector: 0,
    uids: [],
    push_timer: undefined,
    msg_kind: 0,
    app_version: undefined,
    ios_version: undefined,
    android_version: undefined,
    operator: '',
    id: 0,
    create_time: 0,
    update_time: 0,
    deleted: false,
    published: false,
    doc: undefined,
    source: undefined,
    notify_times: 0,
    push_status: 0,
    push_action_time: 0
  };
}

export const Notification: MessageFns<Notification> = {
  fromJSON(object: any): Notification {
    return {
      doc_id: isSet(object.doc_id) ? globalThis.Number(object.doc_id) : 0,
      source_id: isSet(object.source_id) ? globalThis.Number(object.source_id) : 0,
      user_selector: isSet(object.user_selector) ? userSelectorFromJSON(object.user_selector) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      push_timer: isSet(object.push_timer) ? PushTimer.fromJSON(object.push_timer) : undefined,
      msg_kind: isSet(object.msg_kind) ? messageKindFromJSON(object.msg_kind) : 0,
      app_version: isSet(object.app_version) ? Version.fromJSON(object.app_version) : undefined,
      ios_version: isSet(object.ios_version) ? Version.fromJSON(object.ios_version) : undefined,
      android_version: isSet(object.android_version) ? Version.fromJSON(object.android_version) : undefined,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      create_time: isSet(object.create_time) ? globalThis.Number(object.create_time) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      doc: isSet(object.doc) ? Doc.fromJSON(object.doc) : undefined,
      source: isSet(object.source) ? Source.fromJSON(object.source) : undefined,
      notify_times: isSet(object.notify_times) ? globalThis.Number(object.notify_times) : 0,
      push_status: isSet(object.push_status) ? pushStatusFromJSON(object.push_status) : 0,
      push_action_time: isSet(object.push_action_time) ? globalThis.Number(object.push_action_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<Notification>, I>>(base?: I): Notification {
    return Notification.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Notification>, I>>(object: I): Notification {
    const message = createBaseNotification();
    message.doc_id = object.doc_id ?? 0;
    message.source_id = object.source_id ?? 0;
    message.user_selector = object.user_selector ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.push_timer =
      object.push_timer !== undefined && object.push_timer !== null
        ? PushTimer.fromPartial(object.push_timer)
        : undefined;
    message.msg_kind = object.msg_kind ?? 0;
    message.app_version =
      object.app_version !== undefined && object.app_version !== null
        ? Version.fromPartial(object.app_version)
        : undefined;
    message.ios_version =
      object.ios_version !== undefined && object.ios_version !== null
        ? Version.fromPartial(object.ios_version)
        : undefined;
    message.android_version =
      object.android_version !== undefined && object.android_version !== null
        ? Version.fromPartial(object.android_version)
        : undefined;
    message.operator = object.operator ?? '';
    message.id = object.id ?? 0;
    message.create_time = object.create_time ?? 0;
    message.update_time = object.update_time ?? 0;
    message.deleted = object.deleted ?? false;
    message.published = object.published ?? false;
    message.doc = object.doc !== undefined && object.doc !== null ? Doc.fromPartial(object.doc) : undefined;
    message.source =
      object.source !== undefined && object.source !== null ? Source.fromPartial(object.source) : undefined;
    message.notify_times = object.notify_times ?? 0;
    message.push_status = object.push_status ?? 0;
    message.push_action_time = object.push_action_time ?? 0;
    return message;
  }
};

function createBasePushTimer(): PushTimer {
  return { push_now: false, push_tm: 0, timezone: '' };
}

export const PushTimer: MessageFns<PushTimer> = {
  fromJSON(object: any): PushTimer {
    return {
      push_now: isSet(object.push_now) ? globalThis.Boolean(object.push_now) : false,
      push_tm: isSet(object.push_tm) ? globalThis.Number(object.push_tm) : 0,
      timezone: isSet(object.timezone) ? globalThis.String(object.timezone) : ''
    };
  },

  create<I extends Exact<DeepPartial<PushTimer>, I>>(base?: I): PushTimer {
    return PushTimer.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PushTimer>, I>>(object: I): PushTimer {
    const message = createBasePushTimer();
    message.push_now = object.push_now ?? false;
    message.push_tm = object.push_tm ?? 0;
    message.timezone = object.timezone ?? '';
    return message;
  }
};

function createBaseAddReq(): AddReq {
  return {
    doc_id: 0,
    source_id: 0,
    user_selector: 0,
    uids: [],
    push_timer: undefined,
    msg_kind: 0,
    app_version: undefined,
    ios_version: undefined,
    android_version: undefined,
    operator: ''
  };
}

export const AddReq: MessageFns<AddReq> = {
  fromJSON(object: any): AddReq {
    return {
      doc_id: isSet(object.doc_id) ? globalThis.Number(object.doc_id) : 0,
      source_id: isSet(object.source_id) ? globalThis.Number(object.source_id) : 0,
      user_selector: isSet(object.user_selector) ? userSelectorFromJSON(object.user_selector) : 0,
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      push_timer: isSet(object.push_timer) ? PushTimer.fromJSON(object.push_timer) : undefined,
      msg_kind: isSet(object.msg_kind) ? messageKindFromJSON(object.msg_kind) : 0,
      app_version: isSet(object.app_version) ? Version.fromJSON(object.app_version) : undefined,
      ios_version: isSet(object.ios_version) ? Version.fromJSON(object.ios_version) : undefined,
      android_version: isSet(object.android_version) ? Version.fromJSON(object.android_version) : undefined,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddReq>, I>>(base?: I): AddReq {
    return AddReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddReq>, I>>(object: I): AddReq {
    const message = createBaseAddReq();
    message.doc_id = object.doc_id ?? 0;
    message.source_id = object.source_id ?? 0;
    message.user_selector = object.user_selector ?? 0;
    message.uids = object.uids?.map(e => e) || [];
    message.push_timer =
      object.push_timer !== undefined && object.push_timer !== null
        ? PushTimer.fromPartial(object.push_timer)
        : undefined;
    message.msg_kind = object.msg_kind ?? 0;
    message.app_version =
      object.app_version !== undefined && object.app_version !== null
        ? Version.fromPartial(object.app_version)
        : undefined;
    message.ios_version =
      object.ios_version !== undefined && object.ios_version !== null
        ? Version.fromPartial(object.ios_version)
        : undefined;
    message.android_version =
      object.android_version !== undefined && object.android_version !== null
        ? Version.fromPartial(object.android_version)
        : undefined;
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseAddRsp(): AddRsp {
  return { id: 0 };
}

export const AddRsp: MessageFns<AddRsp> = {
  fromJSON(object: any): AddRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddRsp>, I>>(base?: I): AddRsp {
    return AddRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddRsp>, I>>(object: I): AddRsp {
    const message = createBaseAddRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDelReq(): DelReq {
  return { ids: [], operator: '' };
}

export const DelReq: MessageFns<DelReq> = {
  fromJSON(object: any): DelReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<DelReq>, I>>(base?: I): DelReq {
    return DelReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelReq>, I>>(object: I): DelReq {
    const message = createBaseDelReq();
    message.ids = object.ids?.map(e => e) || [];
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseDelRsp(): DelRsp {
  return { count: 0 };
}

export const DelRsp: MessageFns<DelRsp> = {
  fromJSON(object: any): DelRsp {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  create<I extends Exact<DeepPartial<DelRsp>, I>>(base?: I): DelRsp {
    return DelRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelRsp>, I>>(object: I): DelRsp {
    const message = createBaseDelRsp();
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseUpdateReq(): UpdateReq {
  return { req: undefined, operator: '' };
}

export const UpdateReq: MessageFns<UpdateReq> = {
  fromJSON(object: any): UpdateReq {
    return {
      req: isSet(object.req) ? Notification.fromJSON(object.req) : undefined,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateReq>, I>>(base?: I): UpdateReq {
    return UpdateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateReq>, I>>(object: I): UpdateReq {
    const message = createBaseUpdateReq();
    message.req = object.req !== undefined && object.req !== null ? Notification.fromPartial(object.req) : undefined;
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseUpdateRsp(): UpdateRsp {
  return { rsp: undefined };
}

export const UpdateRsp: MessageFns<UpdateRsp> = {
  fromJSON(object: any): UpdateRsp {
    return { rsp: isSet(object.rsp) ? Notification.fromJSON(object.rsp) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateRsp>, I>>(base?: I): UpdateRsp {
    return UpdateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRsp>, I>>(object: I): UpdateRsp {
    const message = createBaseUpdateRsp();
    message.rsp = object.rsp !== undefined && object.rsp !== null ? Notification.fromPartial(object.rsp) : undefined;
    return message;
  }
};

function createBaseGetReq(): GetReq {
  return { id: 0 };
}

export const GetReq: MessageFns<GetReq> = {
  fromJSON(object: any): GetReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetReq>, I>>(base?: I): GetReq {
    return GetReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetReq>, I>>(object: I): GetReq {
    const message = createBaseGetReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetRsp(): GetRsp {
  return { resp: undefined };
}

export const GetRsp: MessageFns<GetRsp> = {
  fromJSON(object: any): GetRsp {
    return { resp: isSet(object.resp) ? Notification.fromJSON(object.resp) : undefined };
  },

  create<I extends Exact<DeepPartial<GetRsp>, I>>(base?: I): GetRsp {
    return GetRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetRsp>, I>>(object: I): GetRsp {
    const message = createBaseGetRsp();
    message.resp =
      object.resp !== undefined && object.resp !== null ? Notification.fromPartial(object.resp) : undefined;
    return message;
  }
};

function createBaseListReq(): ListReq {
  return {
    page: undefined,
    id: 0,
    doc_id: 0,
    source_id: 0,
    notify_begin: 0,
    notify_end: 0,
    push_status: 0,
    manual_pub: false
  };
}

export const ListReq: MessageFns<ListReq> = {
  fromJSON(object: any): ListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      doc_id: isSet(object.doc_id) ? globalThis.Number(object.doc_id) : 0,
      source_id: isSet(object.source_id) ? globalThis.Number(object.source_id) : 0,
      notify_begin: isSet(object.notify_begin) ? globalThis.Number(object.notify_begin) : 0,
      notify_end: isSet(object.notify_end) ? globalThis.Number(object.notify_end) : 0,
      push_status: isSet(object.push_status) ? pushStatusFromJSON(object.push_status) : 0,
      manual_pub: isSet(object.manual_pub) ? globalThis.Boolean(object.manual_pub) : false
    };
  },

  create<I extends Exact<DeepPartial<ListReq>, I>>(base?: I): ListReq {
    return ListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListReq>, I>>(object: I): ListReq {
    const message = createBaseListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.doc_id = object.doc_id ?? 0;
    message.source_id = object.source_id ?? 0;
    message.notify_begin = object.notify_begin ?? 0;
    message.notify_end = object.notify_end ?? 0;
    message.push_status = object.push_status ?? 0;
    message.manual_pub = object.manual_pub ?? false;
    return message;
  }
};

function createBaseListRsp(): ListRsp {
  return { page: undefined, list: [] };
}

export const ListRsp: MessageFns<ListRsp> = {
  fromJSON(object: any): ListRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => Notification.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRsp>, I>>(base?: I): ListRsp {
    return ListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRsp>, I>>(object: I): ListRsp {
    const message = createBaseListRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => Notification.fromPartial(e)) || [];
    return message;
  }
};

function createBasePublishReq(): PublishReq {
  return { id: 0, published: false, operator: '' };
}

export const PublishReq: MessageFns<PublishReq> = {
  fromJSON(object: any): PublishReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      published: isSet(object.published) ? globalThis.Boolean(object.published) : false,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<PublishReq>, I>>(base?: I): PublishReq {
    return PublishReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishReq>, I>>(object: I): PublishReq {
    const message = createBasePublishReq();
    message.id = object.id ?? 0;
    message.published = object.published ?? false;
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBasePublishRsp(): PublishRsp {
  return {};
}

export const PublishRsp: MessageFns<PublishRsp> = {
  fromJSON(_: any): PublishRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<PublishRsp>, I>>(base?: I): PublishRsp {
    return PublishRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PublishRsp>, I>>(_: I): PublishRsp {
    const message = createBasePublishRsp();
    return message;
  }
};

export type NotificationMgrDefinition = typeof NotificationMgrDefinition;
export const NotificationMgrDefinition = {
  name: 'NotificationMgr',
  fullName: 'comm.mgr.platform.notification.NotificationMgr',
  methods: {
    add: {
      name: 'Add',
      requestType: AddReq,
      requestStream: false,
      responseType: AddRsp,
      responseStream: false,
      options: {}
    },
    del: {
      name: 'Del',
      requestType: DelReq,
      requestStream: false,
      responseType: DelRsp,
      responseStream: false,
      options: {}
    },
    update: {
      name: 'Update',
      requestType: UpdateReq,
      requestStream: false,
      responseType: UpdateRsp,
      responseStream: false,
      options: {}
    },
    get: {
      name: 'Get',
      requestType: GetReq,
      requestStream: false,
      responseType: GetRsp,
      responseStream: false,
      options: {}
    },
    list: {
      name: 'List',
      requestType: ListReq,
      requestStream: false,
      responseType: ListRsp,
      responseStream: false,
      options: {}
    },
    publish: {
      name: 'Publish',
      requestType: PublishReq,
      requestStream: false,
      responseType: PublishRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
