// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/feature_whitelist.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { OSType, oSTypeFromJSON } from '../../api/common/common-net';

export const protobufPackage = 'comm.mgr.platform.feature';

/** AppFeature 白名单实体 */
export interface AppFeatureWhitelist {
  id: number;
  os_type: OSType;
  /** 属性名: uid,did,ip,aid,gaid... */
  name: string;
  /** 对应属性名的值 */
  value: string;
  /** 创建时间 */
  create_at: number;
  /** 创建人 */
  creator: string;
  /** 修改人 */
  updater: string;
  /** 创建时间 */
  updated_at: number;
}

export interface CreateFeatureWhitelistBatchReq {
  items: FeatureWhitelistItem[];
}

export interface FeatureWhitelistItem {
  /** 系统类型 */
  os_type: OSType;
  /** 属性名;支持的名称:uid,did */
  name: string;
  /** 对应属性名的值 */
  value: string;
}

export interface CreateFeatureWhitelistBatchResp {
  ids: number[];
}

export interface DeleteAppFeatureWhitelistBatchReq {
  /** 根据ID删除 */
  ids: number[];
}

export interface DeleteAppFeatureWhitelistBatchRsp {
  /** 删除成功的ID */
  success_ids: number[];
  /** 删除失败的ID */
  fail_ids: number[];
}

export interface UpdateAppFeatureWhitelistReq {
  id: number;
  /** 系统类型 */
  os_type: OSType;
  /** 属性名;支持的名称:uid,did */
  name: string;
  /** 对应属性名的值 */
  value: string;
}

export interface UpdateAppFeatureWhitelistRsp {}

export interface ListAppFeatureWhitelistReq {
  page: Page | undefined;
  /** 可选，按OS类型过滤 */
  os_type: OSType;
  /** 可选，按属性名过滤 */
  name: string;
  /** 可选，按属性值过滤 */
  value: string;
}

export interface ListAppFeatureWhitelistRsp {
  page: Page | undefined;
  items: AppFeatureWhitelist[];
}

function createBaseAppFeatureWhitelist(): AppFeatureWhitelist {
  return { id: 0, os_type: 0, name: '', value: '', create_at: 0, creator: '', updater: '', updated_at: 0 };
}

export const AppFeatureWhitelist: MessageFns<AppFeatureWhitelist> = {
  fromJSON(object: any): AppFeatureWhitelist {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<AppFeatureWhitelist>, I>>(base?: I): AppFeatureWhitelist {
    return AppFeatureWhitelist.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppFeatureWhitelist>, I>>(object: I): AppFeatureWhitelist {
    const message = createBaseAppFeatureWhitelist();
    message.id = object.id ?? 0;
    message.os_type = object.os_type ?? 0;
    message.name = object.name ?? '';
    message.value = object.value ?? '';
    message.create_at = object.create_at ?? 0;
    message.creator = object.creator ?? '';
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseCreateFeatureWhitelistBatchReq(): CreateFeatureWhitelistBatchReq {
  return { items: [] };
}

export const CreateFeatureWhitelistBatchReq: MessageFns<CreateFeatureWhitelistBatchReq> = {
  fromJSON(object: any): CreateFeatureWhitelistBatchReq {
    return {
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => FeatureWhitelistItem.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CreateFeatureWhitelistBatchReq>, I>>(base?: I): CreateFeatureWhitelistBatchReq {
    return CreateFeatureWhitelistBatchReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFeatureWhitelistBatchReq>, I>>(
    object: I
  ): CreateFeatureWhitelistBatchReq {
    const message = createBaseCreateFeatureWhitelistBatchReq();
    message.items = object.items?.map(e => FeatureWhitelistItem.fromPartial(e)) || [];
    return message;
  }
};

function createBaseFeatureWhitelistItem(): FeatureWhitelistItem {
  return { os_type: 0, name: '', value: '' };
}

export const FeatureWhitelistItem: MessageFns<FeatureWhitelistItem> = {
  fromJSON(object: any): FeatureWhitelistItem {
    return {
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<FeatureWhitelistItem>, I>>(base?: I): FeatureWhitelistItem {
    return FeatureWhitelistItem.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<FeatureWhitelistItem>, I>>(object: I): FeatureWhitelistItem {
    const message = createBaseFeatureWhitelistItem();
    message.os_type = object.os_type ?? 0;
    message.name = object.name ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCreateFeatureWhitelistBatchResp(): CreateFeatureWhitelistBatchResp {
  return { ids: [] };
}

export const CreateFeatureWhitelistBatchResp: MessageFns<CreateFeatureWhitelistBatchResp> = {
  fromJSON(object: any): CreateFeatureWhitelistBatchResp {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<CreateFeatureWhitelistBatchResp>, I>>(base?: I): CreateFeatureWhitelistBatchResp {
    return CreateFeatureWhitelistBatchResp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateFeatureWhitelistBatchResp>, I>>(
    object: I
  ): CreateFeatureWhitelistBatchResp {
    const message = createBaseCreateFeatureWhitelistBatchResp();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteAppFeatureWhitelistBatchReq(): DeleteAppFeatureWhitelistBatchReq {
  return { ids: [] };
}

export const DeleteAppFeatureWhitelistBatchReq: MessageFns<DeleteAppFeatureWhitelistBatchReq> = {
  fromJSON(object: any): DeleteAppFeatureWhitelistBatchReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteAppFeatureWhitelistBatchReq>, I>>(
    base?: I
  ): DeleteAppFeatureWhitelistBatchReq {
    return DeleteAppFeatureWhitelistBatchReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAppFeatureWhitelistBatchReq>, I>>(
    object: I
  ): DeleteAppFeatureWhitelistBatchReq {
    const message = createBaseDeleteAppFeatureWhitelistBatchReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteAppFeatureWhitelistBatchRsp(): DeleteAppFeatureWhitelistBatchRsp {
  return { success_ids: [], fail_ids: [] };
}

export const DeleteAppFeatureWhitelistBatchRsp: MessageFns<DeleteAppFeatureWhitelistBatchRsp> = {
  fromJSON(object: any): DeleteAppFeatureWhitelistBatchRsp {
    return {
      success_ids: globalThis.Array.isArray(object?.success_ids)
        ? object.success_ids.map((e: any) => globalThis.Number(e))
        : [],
      fail_ids: globalThis.Array.isArray(object?.fail_ids) ? object.fail_ids.map((e: any) => globalThis.Number(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<DeleteAppFeatureWhitelistBatchRsp>, I>>(
    base?: I
  ): DeleteAppFeatureWhitelistBatchRsp {
    return DeleteAppFeatureWhitelistBatchRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAppFeatureWhitelistBatchRsp>, I>>(
    object: I
  ): DeleteAppFeatureWhitelistBatchRsp {
    const message = createBaseDeleteAppFeatureWhitelistBatchRsp();
    message.success_ids = object.success_ids?.map(e => e) || [];
    message.fail_ids = object.fail_ids?.map(e => e) || [];
    return message;
  }
};

function createBaseUpdateAppFeatureWhitelistReq(): UpdateAppFeatureWhitelistReq {
  return { id: 0, os_type: 0, name: '', value: '' };
}

export const UpdateAppFeatureWhitelistReq: MessageFns<UpdateAppFeatureWhitelistReq> = {
  fromJSON(object: any): UpdateAppFeatureWhitelistReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateAppFeatureWhitelistReq>, I>>(base?: I): UpdateAppFeatureWhitelistReq {
    return UpdateAppFeatureWhitelistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAppFeatureWhitelistReq>, I>>(object: I): UpdateAppFeatureWhitelistReq {
    const message = createBaseUpdateAppFeatureWhitelistReq();
    message.id = object.id ?? 0;
    message.os_type = object.os_type ?? 0;
    message.name = object.name ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUpdateAppFeatureWhitelistRsp(): UpdateAppFeatureWhitelistRsp {
  return {};
}

export const UpdateAppFeatureWhitelistRsp: MessageFns<UpdateAppFeatureWhitelistRsp> = {
  fromJSON(_: any): UpdateAppFeatureWhitelistRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateAppFeatureWhitelistRsp>, I>>(base?: I): UpdateAppFeatureWhitelistRsp {
    return UpdateAppFeatureWhitelistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAppFeatureWhitelistRsp>, I>>(_: I): UpdateAppFeatureWhitelistRsp {
    const message = createBaseUpdateAppFeatureWhitelistRsp();
    return message;
  }
};

function createBaseListAppFeatureWhitelistReq(): ListAppFeatureWhitelistReq {
  return { page: undefined, os_type: 0, name: '', value: '' };
}

export const ListAppFeatureWhitelistReq: MessageFns<ListAppFeatureWhitelistReq> = {
  fromJSON(object: any): ListAppFeatureWhitelistReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAppFeatureWhitelistReq>, I>>(base?: I): ListAppFeatureWhitelistReq {
    return ListAppFeatureWhitelistReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAppFeatureWhitelistReq>, I>>(object: I): ListAppFeatureWhitelistReq {
    const message = createBaseListAppFeatureWhitelistReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.os_type = object.os_type ?? 0;
    message.name = object.name ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseListAppFeatureWhitelistRsp(): ListAppFeatureWhitelistRsp {
  return { page: undefined, items: [] };
}

export const ListAppFeatureWhitelistRsp: MessageFns<ListAppFeatureWhitelistRsp> = {
  fromJSON(object: any): ListAppFeatureWhitelistRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items)
        ? object.items.map((e: any) => AppFeatureWhitelist.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListAppFeatureWhitelistRsp>, I>>(base?: I): ListAppFeatureWhitelistRsp {
    return ListAppFeatureWhitelistRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAppFeatureWhitelistRsp>, I>>(object: I): ListAppFeatureWhitelistRsp {
    const message = createBaseListAppFeatureWhitelistRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => AppFeatureWhitelist.fromPartial(e)) || [];
    return message;
  }
};

/**
 * app特征白名单服务
 * serviceName: platform-api
 */
export type AppFeatureWhitelistMgrDefinition = typeof AppFeatureWhitelistMgrDefinition;
export const AppFeatureWhitelistMgrDefinition = {
  name: 'AppFeatureWhitelistMgr',
  fullName: 'comm.mgr.platform.feature.AppFeatureWhitelistMgr',
  methods: {
    /** 创建白名单记录（批量） */
    createFeatureWhitelistBatch: {
      name: 'CreateFeatureWhitelistBatch',
      requestType: CreateFeatureWhitelistBatchReq,
      requestStream: false,
      responseType: CreateFeatureWhitelistBatchResp,
      responseStream: false,
      options: {}
    },
    /** 删除白名单记录（批量） */
    deleteAppFeatureWhitelistBatch: {
      name: 'DeleteAppFeatureWhitelistBatch',
      requestType: DeleteAppFeatureWhitelistBatchReq,
      requestStream: false,
      responseType: DeleteAppFeatureWhitelistBatchRsp,
      responseStream: false,
      options: {}
    },
    /** 更新白名单记录 */
    updateAppFeatureWhitelist: {
      name: 'UpdateAppFeatureWhitelist',
      requestType: UpdateAppFeatureWhitelistReq,
      requestStream: false,
      responseType: UpdateAppFeatureWhitelistRsp,
      responseStream: false,
      options: {}
    },
    /** 获取白名单列表 */
    listAppFeatureWhitelist: {
      name: 'ListAppFeatureWhitelist',
      requestType: ListAppFeatureWhitelistReq,
      requestStream: false,
      responseType: ListAppFeatureWhitelistRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
