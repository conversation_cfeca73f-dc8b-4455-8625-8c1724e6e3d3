// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/apm.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.apm';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/apm/handlermgr/apm.go */

export interface ListOperatorReq {
  page: Page | undefined;
}

export interface ListOperatorRsp {
  page: Page | undefined;
  list: Operator[];
}

export interface Operator {
  nickname: string;
}

export interface DeleteRetrievalByIdReq {
  task_id: number;
}

export interface DeleteRetrievalByIdRsp {}

export interface ListApmLogsReq {
  page: Page | undefined;
  task_id: number;
  msg: string;
  feedback_id: number;
}

export interface ListApmLogsRsp {
  page: Page | undefined;
  list: ApmLog[];
}

export interface ApmLog {
  id: number;
  uid: string;
  did: string;
  feedback_id: string;
  sid: string;
  ver: string;
  verc: string;
  msg: string;
  content: string;
  cou: string;
  lan: string;
  ctime: number;
  mod: string;
  os: string;
  pf: string;
  status: number;
  proc_status: string;
  aid: string;
  pkg: string;
  slan: string;
  net: string;
  brd: string;
  anm: string;
  report: ApmLog | undefined;
}

export interface AddLogRetrievalReq {
  data: LogRetrieval | undefined;
}

export interface AddLogRetrievalRsp {}

export interface ListLogRetrievalReq {
  page: Page | undefined;
  /** 平台 */
  plats: string[];
  /** 任务名，模糊搜索 */
  name: string;
  /** 任务id */
  id: number;
  /** 创建人 */
  creator: string;
  start_time: number;
  end_time: number;
  remark: string;
  send_uids: string;
}

export interface ListLogRetrievalRsp {
  page: Page | undefined;
  list: LogRetrieval[];
}

export interface LogRetrieval {
  id: number;
  plat: string;
  name: string;
  creator: string;
  progress: string;
  remark: string;
  send_uids: string;
  /** 回捞开始时间 */
  start_time: number;
  /** 回捞结束时间 */
  end_time: number;
  anm: string;
  /** LogRetrievalExtend 的 json */
  extend: string;
  /** 添加的时候 */
  file_name_list: string;
  ctime: number;
  utime: number;
  /** 查询列表的时候返回 */
  feedback_list: ClientFeedback[];
}

export interface ClientFeedback {
  uid: number;
  status: string;
  /** 透传客户端的值 */
  info: string;
}

export interface LogRetrievalExtend {
  file_name_list: string[];
}

export interface ListOperateLogReq {
  page: Page | undefined;
  item_id: number;
  operator: string;
  action: string;
  start_time: number;
  end_time: number;
  id: number;
}

export interface ListOperateLogRsp {
  page: Page | undefined;
  list: OperateLog[];
}

export interface OperateLog {
  id: number;
  item_id: number;
  action: string;
  record: string;
  old_record: string;
  table_name: string;
  uid: string;
  operator: string;
  /** 时间戳，秒级别 */
  ctime: number;
  /** 时间戳，秒级别 */
  url: string;
  /** 时间戳，秒级别 */
  pathname: string;
  ip: string;
}

function createBaseListOperatorReq(): ListOperatorReq {
  return { page: undefined };
}

export const ListOperatorReq: MessageFns<ListOperatorReq> = {
  fromJSON(object: any): ListOperatorReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<ListOperatorReq>, I>>(base?: I): ListOperatorReq {
    return ListOperatorReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperatorReq>, I>>(object: I): ListOperatorReq {
    const message = createBaseListOperatorReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseListOperatorRsp(): ListOperatorRsp {
  return { page: undefined, list: [] };
}

export const ListOperatorRsp: MessageFns<ListOperatorRsp> = {
  fromJSON(object: any): ListOperatorRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => Operator.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListOperatorRsp>, I>>(base?: I): ListOperatorRsp {
    return ListOperatorRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperatorRsp>, I>>(object: I): ListOperatorRsp {
    const message = createBaseListOperatorRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => Operator.fromPartial(e)) || [];
    return message;
  }
};

function createBaseOperator(): Operator {
  return { nickname: '' };
}

export const Operator: MessageFns<Operator> = {
  fromJSON(object: any): Operator {
    return { nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '' };
  },

  create<I extends Exact<DeepPartial<Operator>, I>>(base?: I): Operator {
    return Operator.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Operator>, I>>(object: I): Operator {
    const message = createBaseOperator();
    message.nickname = object.nickname ?? '';
    return message;
  }
};

function createBaseDeleteRetrievalByIdReq(): DeleteRetrievalByIdReq {
  return { task_id: 0 };
}

export const DeleteRetrievalByIdReq: MessageFns<DeleteRetrievalByIdReq> = {
  fromJSON(object: any): DeleteRetrievalByIdReq {
    return { task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteRetrievalByIdReq>, I>>(base?: I): DeleteRetrievalByIdReq {
    return DeleteRetrievalByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRetrievalByIdReq>, I>>(object: I): DeleteRetrievalByIdReq {
    const message = createBaseDeleteRetrievalByIdReq();
    message.task_id = object.task_id ?? 0;
    return message;
  }
};

function createBaseDeleteRetrievalByIdRsp(): DeleteRetrievalByIdRsp {
  return {};
}

export const DeleteRetrievalByIdRsp: MessageFns<DeleteRetrievalByIdRsp> = {
  fromJSON(_: any): DeleteRetrievalByIdRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRetrievalByIdRsp>, I>>(base?: I): DeleteRetrievalByIdRsp {
    return DeleteRetrievalByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRetrievalByIdRsp>, I>>(_: I): DeleteRetrievalByIdRsp {
    const message = createBaseDeleteRetrievalByIdRsp();
    return message;
  }
};

function createBaseListApmLogsReq(): ListApmLogsReq {
  return { page: undefined, task_id: 0, msg: '', feedback_id: 0 };
}

export const ListApmLogsReq: MessageFns<ListApmLogsReq> = {
  fromJSON(object: any): ListApmLogsReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      task_id: isSet(object.task_id) ? globalThis.Number(object.task_id) : 0,
      msg: isSet(object.msg) ? globalThis.String(object.msg) : '',
      feedback_id: isSet(object.feedback_id) ? globalThis.Number(object.feedback_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListApmLogsReq>, I>>(base?: I): ListApmLogsReq {
    return ListApmLogsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListApmLogsReq>, I>>(object: I): ListApmLogsReq {
    const message = createBaseListApmLogsReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.task_id = object.task_id ?? 0;
    message.msg = object.msg ?? '';
    message.feedback_id = object.feedback_id ?? 0;
    return message;
  }
};

function createBaseListApmLogsRsp(): ListApmLogsRsp {
  return { page: undefined, list: [] };
}

export const ListApmLogsRsp: MessageFns<ListApmLogsRsp> = {
  fromJSON(object: any): ListApmLogsRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => ApmLog.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListApmLogsRsp>, I>>(base?: I): ListApmLogsRsp {
    return ListApmLogsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListApmLogsRsp>, I>>(object: I): ListApmLogsRsp {
    const message = createBaseListApmLogsRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => ApmLog.fromPartial(e)) || [];
    return message;
  }
};

function createBaseApmLog(): ApmLog {
  return {
    id: 0,
    uid: '',
    did: '',
    feedback_id: '',
    sid: '',
    ver: '',
    verc: '',
    msg: '',
    content: '',
    cou: '',
    lan: '',
    ctime: 0,
    mod: '',
    os: '',
    pf: '',
    status: 0,
    proc_status: '',
    aid: '',
    pkg: '',
    slan: '',
    net: '',
    brd: '',
    anm: '',
    report: undefined
  };
}

export const ApmLog: MessageFns<ApmLog> = {
  fromJSON(object: any): ApmLog {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      feedback_id: isSet(object.feedback_id) ? globalThis.String(object.feedback_id) : '',
      sid: isSet(object.sid) ? globalThis.String(object.sid) : '',
      ver: isSet(object.ver) ? globalThis.String(object.ver) : '',
      verc: isSet(object.verc) ? globalThis.String(object.verc) : '',
      msg: isSet(object.msg) ? globalThis.String(object.msg) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      os: isSet(object.os) ? globalThis.String(object.os) : '',
      pf: isSet(object.pf) ? globalThis.String(object.pf) : '',
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      proc_status: isSet(object.proc_status) ? globalThis.String(object.proc_status) : '',
      aid: isSet(object.aid) ? globalThis.String(object.aid) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      slan: isSet(object.slan) ? globalThis.String(object.slan) : '',
      net: isSet(object.net) ? globalThis.String(object.net) : '',
      brd: isSet(object.brd) ? globalThis.String(object.brd) : '',
      anm: isSet(object.anm) ? globalThis.String(object.anm) : '',
      report: isSet(object.report) ? ApmLog.fromJSON(object.report) : undefined
    };
  },

  create<I extends Exact<DeepPartial<ApmLog>, I>>(base?: I): ApmLog {
    return ApmLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ApmLog>, I>>(object: I): ApmLog {
    const message = createBaseApmLog();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? '';
    message.did = object.did ?? '';
    message.feedback_id = object.feedback_id ?? '';
    message.sid = object.sid ?? '';
    message.ver = object.ver ?? '';
    message.verc = object.verc ?? '';
    message.msg = object.msg ?? '';
    message.content = object.content ?? '';
    message.cou = object.cou ?? '';
    message.lan = object.lan ?? '';
    message.ctime = object.ctime ?? 0;
    message.mod = object.mod ?? '';
    message.os = object.os ?? '';
    message.pf = object.pf ?? '';
    message.status = object.status ?? 0;
    message.proc_status = object.proc_status ?? '';
    message.aid = object.aid ?? '';
    message.pkg = object.pkg ?? '';
    message.slan = object.slan ?? '';
    message.net = object.net ?? '';
    message.brd = object.brd ?? '';
    message.anm = object.anm ?? '';
    message.report =
      object.report !== undefined && object.report !== null ? ApmLog.fromPartial(object.report) : undefined;
    return message;
  }
};

function createBaseAddLogRetrievalReq(): AddLogRetrievalReq {
  return { data: undefined };
}

export const AddLogRetrievalReq: MessageFns<AddLogRetrievalReq> = {
  fromJSON(object: any): AddLogRetrievalReq {
    return { data: isSet(object.data) ? LogRetrieval.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<AddLogRetrievalReq>, I>>(base?: I): AddLogRetrievalReq {
    return AddLogRetrievalReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddLogRetrievalReq>, I>>(object: I): AddLogRetrievalReq {
    const message = createBaseAddLogRetrievalReq();
    message.data =
      object.data !== undefined && object.data !== null ? LogRetrieval.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseAddLogRetrievalRsp(): AddLogRetrievalRsp {
  return {};
}

export const AddLogRetrievalRsp: MessageFns<AddLogRetrievalRsp> = {
  fromJSON(_: any): AddLogRetrievalRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AddLogRetrievalRsp>, I>>(base?: I): AddLogRetrievalRsp {
    return AddLogRetrievalRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddLogRetrievalRsp>, I>>(_: I): AddLogRetrievalRsp {
    const message = createBaseAddLogRetrievalRsp();
    return message;
  }
};

function createBaseListLogRetrievalReq(): ListLogRetrievalReq {
  return {
    page: undefined,
    plats: [],
    name: '',
    id: 0,
    creator: '',
    start_time: 0,
    end_time: 0,
    remark: '',
    send_uids: ''
  };
}

export const ListLogRetrievalReq: MessageFns<ListLogRetrievalReq> = {
  fromJSON(object: any): ListLogRetrievalReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      plats: globalThis.Array.isArray(object?.plats) ? object.plats.map((e: any) => globalThis.String(e)) : [],
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      send_uids: isSet(object.send_uids) ? globalThis.String(object.send_uids) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListLogRetrievalReq>, I>>(base?: I): ListLogRetrievalReq {
    return ListLogRetrievalReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLogRetrievalReq>, I>>(object: I): ListLogRetrievalReq {
    const message = createBaseListLogRetrievalReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.plats = object.plats?.map(e => e) || [];
    message.name = object.name ?? '';
    message.id = object.id ?? 0;
    message.creator = object.creator ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.remark = object.remark ?? '';
    message.send_uids = object.send_uids ?? '';
    return message;
  }
};

function createBaseListLogRetrievalRsp(): ListLogRetrievalRsp {
  return { page: undefined, list: [] };
}

export const ListLogRetrievalRsp: MessageFns<ListLogRetrievalRsp> = {
  fromJSON(object: any): ListLogRetrievalRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => LogRetrieval.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListLogRetrievalRsp>, I>>(base?: I): ListLogRetrievalRsp {
    return ListLogRetrievalRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListLogRetrievalRsp>, I>>(object: I): ListLogRetrievalRsp {
    const message = createBaseListLogRetrievalRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => LogRetrieval.fromPartial(e)) || [];
    return message;
  }
};

function createBaseLogRetrieval(): LogRetrieval {
  return {
    id: 0,
    plat: '',
    name: '',
    creator: '',
    progress: '',
    remark: '',
    send_uids: '',
    start_time: 0,
    end_time: 0,
    anm: '',
    extend: '',
    file_name_list: '',
    ctime: 0,
    utime: 0,
    feedback_list: []
  };
}

export const LogRetrieval: MessageFns<LogRetrieval> = {
  fromJSON(object: any): LogRetrieval {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      plat: isSet(object.plat) ? globalThis.String(object.plat) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      progress: isSet(object.progress) ? globalThis.String(object.progress) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      send_uids: isSet(object.send_uids) ? globalThis.String(object.send_uids) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : '',
      extend: isSet(object.extend) ? globalThis.String(object.extend) : '',
      file_name_list: isSet(object.file_name_list) ? globalThis.String(object.file_name_list) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      feedback_list: globalThis.Array.isArray(object?.feedback_list)
        ? object.feedback_list.map((e: any) => ClientFeedback.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LogRetrieval>, I>>(base?: I): LogRetrieval {
    return LogRetrieval.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogRetrieval>, I>>(object: I): LogRetrieval {
    const message = createBaseLogRetrieval();
    message.id = object.id ?? 0;
    message.plat = object.plat ?? '';
    message.name = object.name ?? '';
    message.creator = object.creator ?? '';
    message.progress = object.progress ?? '';
    message.remark = object.remark ?? '';
    message.send_uids = object.send_uids ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.anm = object.anm ?? '';
    message.extend = object.extend ?? '';
    message.file_name_list = object.file_name_list ?? '';
    message.ctime = object.ctime ?? 0;
    message.utime = object.utime ?? 0;
    message.feedback_list = object.feedback_list?.map(e => ClientFeedback.fromPartial(e)) || [];
    return message;
  }
};

function createBaseClientFeedback(): ClientFeedback {
  return { uid: 0, status: '', info: '' };
}

export const ClientFeedback: MessageFns<ClientFeedback> = {
  fromJSON(object: any): ClientFeedback {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      info: isSet(object.info) ? globalThis.String(object.info) : ''
    };
  },

  create<I extends Exact<DeepPartial<ClientFeedback>, I>>(base?: I): ClientFeedback {
    return ClientFeedback.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ClientFeedback>, I>>(object: I): ClientFeedback {
    const message = createBaseClientFeedback();
    message.uid = object.uid ?? 0;
    message.status = object.status ?? '';
    message.info = object.info ?? '';
    return message;
  }
};

function createBaseLogRetrievalExtend(): LogRetrievalExtend {
  return { file_name_list: [] };
}

export const LogRetrievalExtend: MessageFns<LogRetrievalExtend> = {
  fromJSON(object: any): LogRetrievalExtend {
    return {
      file_name_list: globalThis.Array.isArray(object?.file_name_list)
        ? object.file_name_list.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<LogRetrievalExtend>, I>>(base?: I): LogRetrievalExtend {
    return LogRetrievalExtend.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<LogRetrievalExtend>, I>>(object: I): LogRetrievalExtend {
    const message = createBaseLogRetrievalExtend();
    message.file_name_list = object.file_name_list?.map(e => e) || [];
    return message;
  }
};

function createBaseListOperateLogReq(): ListOperateLogReq {
  return { page: undefined, item_id: 0, operator: '', action: '', start_time: 0, end_time: 0, id: 0 };
}

export const ListOperateLogReq: MessageFns<ListOperateLogReq> = {
  fromJSON(object: any): ListOperateLogReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      item_id: isSet(object.item_id) ? globalThis.Number(object.item_id) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      start_time: isSet(object.start_time) ? globalThis.Number(object.start_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListOperateLogReq>, I>>(base?: I): ListOperateLogReq {
    return ListOperateLogReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperateLogReq>, I>>(object: I): ListOperateLogReq {
    const message = createBaseListOperateLogReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.item_id = object.item_id ?? 0;
    message.operator = object.operator ?? '';
    message.action = object.action ?? '';
    message.start_time = object.start_time ?? 0;
    message.end_time = object.end_time ?? 0;
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListOperateLogRsp(): ListOperateLogRsp {
  return { page: undefined, list: [] };
}

export const ListOperateLogRsp: MessageFns<ListOperateLogRsp> = {
  fromJSON(object: any): ListOperateLogRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => OperateLog.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListOperateLogRsp>, I>>(base?: I): ListOperateLogRsp {
    return ListOperateLogRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperateLogRsp>, I>>(object: I): ListOperateLogRsp {
    const message = createBaseListOperateLogRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => OperateLog.fromPartial(e)) || [];
    return message;
  }
};

function createBaseOperateLog(): OperateLog {
  return {
    id: 0,
    item_id: 0,
    action: '',
    record: '',
    old_record: '',
    table_name: '',
    uid: '',
    operator: '',
    ctime: 0,
    url: '',
    pathname: '',
    ip: ''
  };
}

export const OperateLog: MessageFns<OperateLog> = {
  fromJSON(object: any): OperateLog {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      item_id: isSet(object.item_id) ? globalThis.Number(object.item_id) : 0,
      action: isSet(object.action) ? globalThis.String(object.action) : '',
      record: isSet(object.record) ? globalThis.String(object.record) : '',
      old_record: isSet(object.old_record) ? globalThis.String(object.old_record) : '',
      table_name: isSet(object.table_name) ? globalThis.String(object.table_name) : '',
      uid: isSet(object.uid) ? globalThis.String(object.uid) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      url: isSet(object.url) ? globalThis.String(object.url) : '',
      pathname: isSet(object.pathname) ? globalThis.String(object.pathname) : '',
      ip: isSet(object.ip) ? globalThis.String(object.ip) : ''
    };
  },

  create<I extends Exact<DeepPartial<OperateLog>, I>>(base?: I): OperateLog {
    return OperateLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<OperateLog>, I>>(object: I): OperateLog {
    const message = createBaseOperateLog();
    message.id = object.id ?? 0;
    message.item_id = object.item_id ?? 0;
    message.action = object.action ?? '';
    message.record = object.record ?? '';
    message.old_record = object.old_record ?? '';
    message.table_name = object.table_name ?? '';
    message.uid = object.uid ?? '';
    message.operator = object.operator ?? '';
    message.ctime = object.ctime ?? 0;
    message.url = object.url ?? '';
    message.pathname = object.pathname ?? '';
    message.ip = object.ip ?? '';
    return message;
  }
};

export type ApmMgrDefinition = typeof ApmMgrDefinition;
export const ApmMgrDefinition = {
  name: 'ApmMgr',
  fullName: 'comm.mgr.platform.apm.ApmMgr',
  methods: {
    /** 回捞日志列表 */
    listLogRetrieval: {
      name: 'ListLogRetrieval',
      requestType: ListLogRetrievalReq,
      requestStream: false,
      responseType: ListLogRetrievalRsp,
      responseStream: false,
      options: {}
    },
    /** 写入并且通知业务回捞日志 */
    addLogRetrieval: {
      name: 'AddLogRetrieval',
      requestType: AddLogRetrievalReq,
      requestStream: false,
      responseType: AddLogRetrievalRsp,
      responseStream: false,
      options: {}
    },
    /** 删除回捞日志 */
    deleteRetrievalById: {
      name: 'DeleteRetrievalById',
      requestType: DeleteRetrievalByIdReq,
      requestStream: false,
      responseType: DeleteRetrievalByIdRsp,
      responseStream: false,
      options: {}
    },
    /** apm日志列表 */
    listApmLogs: {
      name: 'ListApmLogs',
      requestType: ListApmLogsReq,
      requestStream: false,
      responseType: ListApmLogsRsp,
      responseStream: false,
      options: {}
    },
    /** oms操作日志 */
    listOperateLog: {
      name: 'ListOperateLog',
      requestType: ListOperateLogReq,
      requestStream: false,
      responseType: ListOperateLogRsp,
      responseStream: false,
      options: {}
    },
    /** 获取操作人列表 */
    listOperator: {
      name: 'ListOperator',
      requestType: ListOperatorReq,
      requestStream: false,
      responseType: ListOperatorRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
