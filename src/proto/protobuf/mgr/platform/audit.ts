// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/audit.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.audit';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/audit/handlermgr */

export enum AuditStatus {
  AUDIT_STATUS_NONE = 0,
  /** AUDIT_STATUS_AUDITING - 待审批 */
  AUDIT_STATUS_AUDITING = 10,
  /** AUDIT_STATUS_APPROVED - 已通过 */
  AUDIT_STATUS_APPROVED = 20,
  /** AUDIT_STATUS_REJECTED - 已拒绝 */
  AUDIT_STATUS_REJECTED = 30,
  /** AUDIT_STATUS_REVOKED - 已撤回 */
  AUDIT_STATUS_REVOKED = 40,
  /** AUDIT_STATUS_FAILED - 失败 */
  AUDIT_STATUS_FAILED = 50,
  UNRECOGNIZED = -1
}

export function auditStatusFromJSON(object: any): AuditStatus {
  switch (object) {
    case 0:
    case 'AUDIT_STATUS_NONE':
      return AuditStatus.AUDIT_STATUS_NONE;
    case 10:
    case 'AUDIT_STATUS_AUDITING':
      return AuditStatus.AUDIT_STATUS_AUDITING;
    case 20:
    case 'AUDIT_STATUS_APPROVED':
      return AuditStatus.AUDIT_STATUS_APPROVED;
    case 30:
    case 'AUDIT_STATUS_REJECTED':
      return AuditStatus.AUDIT_STATUS_REJECTED;
    case 40:
    case 'AUDIT_STATUS_REVOKED':
      return AuditStatus.AUDIT_STATUS_REVOKED;
    case 50:
    case 'AUDIT_STATUS_FAILED':
      return AuditStatus.AUDIT_STATUS_FAILED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AuditStatus.UNRECOGNIZED;
  }
}

export interface Auditor {
  user_id: string;
  union_id: string;
  name: string;
  nick_name: string;
  email: string;
}

export interface AuditLevel {
  /** 审批层级, 从 1 开始. */
  level: number;
  /** 审批人员 */
  auditors: Auditor[];
  /** 实际操作审批的审批人员 */
  auditor: Auditor | undefined;
  /** 审批状态 */
  audit_status: AuditStatus;
  /** 审批理由 */
  audit_reason: string;
  /** 审批时间 */
  audit_at: number;
  /** 有图有真相 url */
  audit_picture: string;
}

export interface AuditOrder {
  id: number;
  /** 审批工单分类, 业务自行将审批工单分门别类. */
  category: string;
  /** 审批工单透传数据, 中台不关心此数据, 只是简单透传给前端显示. */
  data: string;
  /** 申请原因 */
  reason: string;
  /** 提交人 */
  submit_by: string;
  /** 提交时间 */
  submit_at: number;
  /** 提交人 union_id */
  submit_id: string;
  /** 审批层级, 用于实现多级审批. */
  audit_levels: AuditLevel[];
  /** 当前流转到哪个层级 */
  current_level: number;
  /** 审批工单整体审批状态 */
  audit_status: AuditStatus;
  /** 审批工单最终审批理由 */
  audit_reason: string;
  /** 审批工单最终审批时间 */
  audit_at: number;
  /** 备注 */
  remark: string;
  /** 操作人 */
  operate_by: string;
  /** 操作时间 */
  operate_at: number;
}

export interface SearchRep {
  page: Page | undefined;
  category: string;
  /** 提交人 */
  submit_by: string;
  /** 提交时间 */
  submit_at_start: number;
  /** 提交时间 */
  submit_at_end: number;
  /** 提交人id列表 */
  submit_ids: string[];
  /** 操作人 */
  operate_by: string;
  /** 操作时间 */
  operate_at_start: number;
  /** 操作时间 */
  operate_at_end: number;
  /** 操作者id列表 */
  operate_ids: string[];
  /** 审批状态列表 */
  audit_statuss: AuditStatus[];
}

export interface SearchRsp {
  page: Page | undefined;
  data: AuditOrder[];
}

export interface AuditReq {
  /** 审批工单 id */
  id: number;
  /** 审批状态 */
  audit_status: AuditStatus;
  /** 备注，200个字节 */
  remark: string;
  /** 有图有真相 url */
  truth_picture: string;
}

export interface AuditRsp {}

function createBaseAuditor(): Auditor {
  return { user_id: '', union_id: '', name: '', nick_name: '', email: '' };
}

export const Auditor: MessageFns<Auditor> = {
  fromJSON(object: any): Auditor {
    return {
      user_id: isSet(object.user_id) ? globalThis.String(object.user_id) : '',
      union_id: isSet(object.union_id) ? globalThis.String(object.union_id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      nick_name: isSet(object.nick_name) ? globalThis.String(object.nick_name) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : ''
    };
  },

  create<I extends Exact<DeepPartial<Auditor>, I>>(base?: I): Auditor {
    return Auditor.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Auditor>, I>>(object: I): Auditor {
    const message = createBaseAuditor();
    message.user_id = object.user_id ?? '';
    message.union_id = object.union_id ?? '';
    message.name = object.name ?? '';
    message.nick_name = object.nick_name ?? '';
    message.email = object.email ?? '';
    return message;
  }
};

function createBaseAuditLevel(): AuditLevel {
  return {
    level: 0,
    auditors: [],
    auditor: undefined,
    audit_status: 0,
    audit_reason: '',
    audit_at: 0,
    audit_picture: ''
  };
}

export const AuditLevel: MessageFns<AuditLevel> = {
  fromJSON(object: any): AuditLevel {
    return {
      level: isSet(object.level) ? globalThis.Number(object.level) : 0,
      auditors: globalThis.Array.isArray(object?.auditors) ? object.auditors.map((e: any) => Auditor.fromJSON(e)) : [],
      auditor: isSet(object.auditor) ? Auditor.fromJSON(object.auditor) : undefined,
      audit_status: isSet(object.audit_status) ? auditStatusFromJSON(object.audit_status) : 0,
      audit_reason: isSet(object.audit_reason) ? globalThis.String(object.audit_reason) : '',
      audit_at: isSet(object.audit_at) ? globalThis.Number(object.audit_at) : 0,
      audit_picture: isSet(object.audit_picture) ? globalThis.String(object.audit_picture) : ''
    };
  },

  create<I extends Exact<DeepPartial<AuditLevel>, I>>(base?: I): AuditLevel {
    return AuditLevel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditLevel>, I>>(object: I): AuditLevel {
    const message = createBaseAuditLevel();
    message.level = object.level ?? 0;
    message.auditors = object.auditors?.map(e => Auditor.fromPartial(e)) || [];
    message.auditor =
      object.auditor !== undefined && object.auditor !== null ? Auditor.fromPartial(object.auditor) : undefined;
    message.audit_status = object.audit_status ?? 0;
    message.audit_reason = object.audit_reason ?? '';
    message.audit_at = object.audit_at ?? 0;
    message.audit_picture = object.audit_picture ?? '';
    return message;
  }
};

function createBaseAuditOrder(): AuditOrder {
  return {
    id: 0,
    category: '',
    data: '',
    reason: '',
    submit_by: '',
    submit_at: 0,
    submit_id: '',
    audit_levels: [],
    current_level: 0,
    audit_status: 0,
    audit_reason: '',
    audit_at: 0,
    remark: '',
    operate_by: '',
    operate_at: 0
  };
}

export const AuditOrder: MessageFns<AuditOrder> = {
  fromJSON(object: any): AuditOrder {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      data: isSet(object.data) ? globalThis.String(object.data) : '',
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      submit_by: isSet(object.submit_by) ? globalThis.String(object.submit_by) : '',
      submit_at: isSet(object.submit_at) ? globalThis.Number(object.submit_at) : 0,
      submit_id: isSet(object.submit_id) ? globalThis.String(object.submit_id) : '',
      audit_levels: globalThis.Array.isArray(object?.audit_levels)
        ? object.audit_levels.map((e: any) => AuditLevel.fromJSON(e))
        : [],
      current_level: isSet(object.current_level) ? globalThis.Number(object.current_level) : 0,
      audit_status: isSet(object.audit_status) ? auditStatusFromJSON(object.audit_status) : 0,
      audit_reason: isSet(object.audit_reason) ? globalThis.String(object.audit_reason) : '',
      audit_at: isSet(object.audit_at) ? globalThis.Number(object.audit_at) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      operate_by: isSet(object.operate_by) ? globalThis.String(object.operate_by) : '',
      operate_at: isSet(object.operate_at) ? globalThis.Number(object.operate_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<AuditOrder>, I>>(base?: I): AuditOrder {
    return AuditOrder.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditOrder>, I>>(object: I): AuditOrder {
    const message = createBaseAuditOrder();
    message.id = object.id ?? 0;
    message.category = object.category ?? '';
    message.data = object.data ?? '';
    message.reason = object.reason ?? '';
    message.submit_by = object.submit_by ?? '';
    message.submit_at = object.submit_at ?? 0;
    message.submit_id = object.submit_id ?? '';
    message.audit_levels = object.audit_levels?.map(e => AuditLevel.fromPartial(e)) || [];
    message.current_level = object.current_level ?? 0;
    message.audit_status = object.audit_status ?? 0;
    message.audit_reason = object.audit_reason ?? '';
    message.audit_at = object.audit_at ?? 0;
    message.remark = object.remark ?? '';
    message.operate_by = object.operate_by ?? '';
    message.operate_at = object.operate_at ?? 0;
    return message;
  }
};

function createBaseSearchRep(): SearchRep {
  return {
    page: undefined,
    category: '',
    submit_by: '',
    submit_at_start: 0,
    submit_at_end: 0,
    submit_ids: [],
    operate_by: '',
    operate_at_start: 0,
    operate_at_end: 0,
    operate_ids: [],
    audit_statuss: []
  };
}

export const SearchRep: MessageFns<SearchRep> = {
  fromJSON(object: any): SearchRep {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      submit_by: isSet(object.submit_by) ? globalThis.String(object.submit_by) : '',
      submit_at_start: isSet(object.submit_at_start) ? globalThis.Number(object.submit_at_start) : 0,
      submit_at_end: isSet(object.submit_at_end) ? globalThis.Number(object.submit_at_end) : 0,
      submit_ids: globalThis.Array.isArray(object?.submit_ids)
        ? object.submit_ids.map((e: any) => globalThis.String(e))
        : [],
      operate_by: isSet(object.operate_by) ? globalThis.String(object.operate_by) : '',
      operate_at_start: isSet(object.operate_at_start) ? globalThis.Number(object.operate_at_start) : 0,
      operate_at_end: isSet(object.operate_at_end) ? globalThis.Number(object.operate_at_end) : 0,
      operate_ids: globalThis.Array.isArray(object?.operate_ids)
        ? object.operate_ids.map((e: any) => globalThis.String(e))
        : [],
      audit_statuss: globalThis.Array.isArray(object?.audit_statuss)
        ? object.audit_statuss.map((e: any) => auditStatusFromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRep>, I>>(base?: I): SearchRep {
    return SearchRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRep>, I>>(object: I): SearchRep {
    const message = createBaseSearchRep();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category = object.category ?? '';
    message.submit_by = object.submit_by ?? '';
    message.submit_at_start = object.submit_at_start ?? 0;
    message.submit_at_end = object.submit_at_end ?? 0;
    message.submit_ids = object.submit_ids?.map(e => e) || [];
    message.operate_by = object.operate_by ?? '';
    message.operate_at_start = object.operate_at_start ?? 0;
    message.operate_at_end = object.operate_at_end ?? 0;
    message.operate_ids = object.operate_ids?.map(e => e) || [];
    message.audit_statuss = object.audit_statuss?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRsp(): SearchRsp {
  return { page: undefined, data: [] };
}

export const SearchRsp: MessageFns<SearchRsp> = {
  fromJSON(object: any): SearchRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AuditOrder.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRsp>, I>>(base?: I): SearchRsp {
    return SearchRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRsp>, I>>(object: I): SearchRsp {
    const message = createBaseSearchRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AuditOrder.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAuditReq(): AuditReq {
  return { id: 0, audit_status: 0, remark: '', truth_picture: '' };
}

export const AuditReq: MessageFns<AuditReq> = {
  fromJSON(object: any): AuditReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      audit_status: isSet(object.audit_status) ? auditStatusFromJSON(object.audit_status) : 0,
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      truth_picture: isSet(object.truth_picture) ? globalThis.String(object.truth_picture) : ''
    };
  },

  create<I extends Exact<DeepPartial<AuditReq>, I>>(base?: I): AuditReq {
    return AuditReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditReq>, I>>(object: I): AuditReq {
    const message = createBaseAuditReq();
    message.id = object.id ?? 0;
    message.audit_status = object.audit_status ?? 0;
    message.remark = object.remark ?? '';
    message.truth_picture = object.truth_picture ?? '';
    return message;
  }
};

function createBaseAuditRsp(): AuditRsp {
  return {};
}

export const AuditRsp: MessageFns<AuditRsp> = {
  fromJSON(_: any): AuditRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<AuditRsp>, I>>(base?: I): AuditRsp {
    return AuditRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AuditRsp>, I>>(_: I): AuditRsp {
    const message = createBaseAuditRsp();
    return message;
  }
};

export type AuditMgrDefinition = typeof AuditMgrDefinition;
export const AuditMgrDefinition = {
  name: 'AuditMgr',
  fullName: 'comm.mgr.platform.audit.AuditMgr',
  methods: {
    search: {
      name: 'Search',
      requestType: SearchRep,
      requestStream: false,
      responseType: SearchRsp,
      responseStream: false,
      options: {}
    },
    audit: {
      name: 'Audit',
      requestType: AuditReq,
      requestStream: false,
      responseType: AuditRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
