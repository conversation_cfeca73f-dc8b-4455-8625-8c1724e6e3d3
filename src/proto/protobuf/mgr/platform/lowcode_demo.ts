// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/lowcode_demo.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.lowcode';

export enum Enum {
  ENUM_NONE = 0,
  ENUM_FOO = 10,
  ENUM_BAR = 20,
  ENUM_BAZ = 30,
  ENUM_QUX = 40,
  UNRECOGNIZED = -1
}

export function enumFromJSON(object: any): Enum {
  switch (object) {
    case 0:
    case 'ENUM_NONE':
      return Enum.ENUM_NONE;
    case 10:
    case 'ENUM_FOO':
      return Enum.ENUM_FOO;
    case 20:
    case 'ENUM_BAR':
      return Enum.ENUM_BAR;
    case 30:
    case 'ENUM_BAZ':
      return Enum.ENUM_BAZ;
    case 40:
    case 'ENUM_QUX':
      return Enum.ENUM_QUX;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Enum.UNRECOGNIZED;
  }
}

export interface CreateDemoReq {
  /** 默认字段名: data */
  data: Demo | undefined;
}

export interface CreateDemoRsp {}

export interface UpdateDemoReq {
  /** 自定义字段名: demo */
  demo: Demo | undefined;
}

export interface UpdateDemoRsp {}

export interface DeleteDemoReq {
  /** 支持批量删除 */
  ids: number[];
}

export interface DeleteDemoRsp {}

export interface SearchDemoReq {
  page: Page | undefined;
  string_field: string;
  string_fields: string[];
  int_field: number;
  int_fields: number[];
  float_field: number;
  float_fields: number[];
  bool_field: boolean;
  bool_fields: boolean[];
  enum_field: Enum;
  enum_fields: Enum[];
  start_date_field: string;
  end_date_field: string;
  start_date_time_field: number;
  end_date_time_field: number;
}

export interface SearchDemoRsp {
  page: Page | undefined;
  datas: Demo[];
}

/** 下拉框选项 */
export interface Option {
  value: string;
  name: string;
}

export interface GetDemoSelectorReq {
  foo: string;
  bar: number;
}

export interface GetDemoSelectorRsp {
  options: Option[];
}

export interface SingleOperateDemoReq {
  id: number;
  /** 自定义参数 */
  bool_field: boolean;
}

export interface SingleOperateDemoRsp {}

export interface BatchOperateDemoReq {
  ids: number[];
  /** 自定义参数 */
  bool_field: boolean;
}

export interface BatchOperateDemoRsp {}

export interface File {
  url: string;
}

export interface Privilege {
  type: string;
  id: number;
  use_time: boolean;
}

export interface Reward {
  privilege: Privilege | undefined;
  number: number;
}

export interface Color {
  color: string;
}

export interface Staff {
  union_id: string;
  name: string;
  nick_name: string;
  email: string;
  user_id: string;
}

export interface Demo {
  id: number;
  /** 单值类型 */
  string_field: string;
  int_field: number;
  float_field: number;
  bool_field: boolean;
  enum_field: Enum;
  nest_field: Demo | undefined;
  date_field: string;
  date_time_field: number;
  image_field: File | undefined;
  file_field: File | undefined;
  reward_field: Reward | undefined;
  color_field: Color | undefined;
  /** 数组类型 */
  string_fields: string[];
  int_fields: number[];
  float_fields: number[];
  bool_fields: boolean[];
  enum_fields: Enum[];
  nest_fields: Demo[];
  date_fields: string[];
  date_time_fields: number[];
  image_fields: File[];
  file_fields: File[];
  reward_fields: Reward[];
  color_fields: Color[];
}

function createBaseCreateDemoReq(): CreateDemoReq {
  return { data: undefined };
}

export const CreateDemoReq: MessageFns<CreateDemoReq> = {
  fromJSON(object: any): CreateDemoReq {
    return { data: isSet(object.data) ? Demo.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<CreateDemoReq>, I>>(base?: I): CreateDemoReq {
    return CreateDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateDemoReq>, I>>(object: I): CreateDemoReq {
    const message = createBaseCreateDemoReq();
    message.data = object.data !== undefined && object.data !== null ? Demo.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseCreateDemoRsp(): CreateDemoRsp {
  return {};
}

export const CreateDemoRsp: MessageFns<CreateDemoRsp> = {
  fromJSON(_: any): CreateDemoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<CreateDemoRsp>, I>>(base?: I): CreateDemoRsp {
    return CreateDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateDemoRsp>, I>>(_: I): CreateDemoRsp {
    const message = createBaseCreateDemoRsp();
    return message;
  }
};

function createBaseUpdateDemoReq(): UpdateDemoReq {
  return { demo: undefined };
}

export const UpdateDemoReq: MessageFns<UpdateDemoReq> = {
  fromJSON(object: any): UpdateDemoReq {
    return { demo: isSet(object.demo) ? Demo.fromJSON(object.demo) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateDemoReq>, I>>(base?: I): UpdateDemoReq {
    return UpdateDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDemoReq>, I>>(object: I): UpdateDemoReq {
    const message = createBaseUpdateDemoReq();
    message.demo = object.demo !== undefined && object.demo !== null ? Demo.fromPartial(object.demo) : undefined;
    return message;
  }
};

function createBaseUpdateDemoRsp(): UpdateDemoRsp {
  return {};
}

export const UpdateDemoRsp: MessageFns<UpdateDemoRsp> = {
  fromJSON(_: any): UpdateDemoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateDemoRsp>, I>>(base?: I): UpdateDemoRsp {
    return UpdateDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateDemoRsp>, I>>(_: I): UpdateDemoRsp {
    const message = createBaseUpdateDemoRsp();
    return message;
  }
};

function createBaseDeleteDemoReq(): DeleteDemoReq {
  return { ids: [] };
}

export const DeleteDemoReq: MessageFns<DeleteDemoReq> = {
  fromJSON(object: any): DeleteDemoReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteDemoReq>, I>>(base?: I): DeleteDemoReq {
    return DeleteDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteDemoReq>, I>>(object: I): DeleteDemoReq {
    const message = createBaseDeleteDemoReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteDemoRsp(): DeleteDemoRsp {
  return {};
}

export const DeleteDemoRsp: MessageFns<DeleteDemoRsp> = {
  fromJSON(_: any): DeleteDemoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteDemoRsp>, I>>(base?: I): DeleteDemoRsp {
    return DeleteDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteDemoRsp>, I>>(_: I): DeleteDemoRsp {
    const message = createBaseDeleteDemoRsp();
    return message;
  }
};

function createBaseSearchDemoReq(): SearchDemoReq {
  return {
    page: undefined,
    string_field: '',
    string_fields: [],
    int_field: 0,
    int_fields: [],
    float_field: 0,
    float_fields: [],
    bool_field: false,
    bool_fields: [],
    enum_field: 0,
    enum_fields: [],
    start_date_field: '',
    end_date_field: '',
    start_date_time_field: 0,
    end_date_time_field: 0
  };
}

export const SearchDemoReq: MessageFns<SearchDemoReq> = {
  fromJSON(object: any): SearchDemoReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      string_field: isSet(object.string_field) ? globalThis.String(object.string_field) : '',
      string_fields: globalThis.Array.isArray(object?.string_fields)
        ? object.string_fields.map((e: any) => globalThis.String(e))
        : [],
      int_field: isSet(object.int_field) ? globalThis.Number(object.int_field) : 0,
      int_fields: globalThis.Array.isArray(object?.int_fields)
        ? object.int_fields.map((e: any) => globalThis.Number(e))
        : [],
      float_field: isSet(object.float_field) ? globalThis.Number(object.float_field) : 0,
      float_fields: globalThis.Array.isArray(object?.float_fields)
        ? object.float_fields.map((e: any) => globalThis.Number(e))
        : [],
      bool_field: isSet(object.bool_field) ? globalThis.Boolean(object.bool_field) : false,
      bool_fields: globalThis.Array.isArray(object?.bool_fields)
        ? object.bool_fields.map((e: any) => globalThis.Boolean(e))
        : [],
      enum_field: isSet(object.enum_field) ? enumFromJSON(object.enum_field) : 0,
      enum_fields: globalThis.Array.isArray(object?.enum_fields)
        ? object.enum_fields.map((e: any) => enumFromJSON(e))
        : [],
      start_date_field: isSet(object.start_date_field) ? globalThis.String(object.start_date_field) : '',
      end_date_field: isSet(object.end_date_field) ? globalThis.String(object.end_date_field) : '',
      start_date_time_field: isSet(object.start_date_time_field) ? globalThis.Number(object.start_date_time_field) : 0,
      end_date_time_field: isSet(object.end_date_time_field) ? globalThis.Number(object.end_date_time_field) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchDemoReq>, I>>(base?: I): SearchDemoReq {
    return SearchDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchDemoReq>, I>>(object: I): SearchDemoReq {
    const message = createBaseSearchDemoReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.string_field = object.string_field ?? '';
    message.string_fields = object.string_fields?.map(e => e) || [];
    message.int_field = object.int_field ?? 0;
    message.int_fields = object.int_fields?.map(e => e) || [];
    message.float_field = object.float_field ?? 0;
    message.float_fields = object.float_fields?.map(e => e) || [];
    message.bool_field = object.bool_field ?? false;
    message.bool_fields = object.bool_fields?.map(e => e) || [];
    message.enum_field = object.enum_field ?? 0;
    message.enum_fields = object.enum_fields?.map(e => e) || [];
    message.start_date_field = object.start_date_field ?? '';
    message.end_date_field = object.end_date_field ?? '';
    message.start_date_time_field = object.start_date_time_field ?? 0;
    message.end_date_time_field = object.end_date_time_field ?? 0;
    return message;
  }
};

function createBaseSearchDemoRsp(): SearchDemoRsp {
  return { page: undefined, datas: [] };
}

export const SearchDemoRsp: MessageFns<SearchDemoRsp> = {
  fromJSON(object: any): SearchDemoRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      datas: globalThis.Array.isArray(object?.datas) ? object.datas.map((e: any) => Demo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchDemoRsp>, I>>(base?: I): SearchDemoRsp {
    return SearchDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchDemoRsp>, I>>(object: I): SearchDemoRsp {
    const message = createBaseSearchDemoRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.datas = object.datas?.map(e => Demo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseOption(): Option {
  return { value: '', name: '' };
}

export const Option: MessageFns<Option> = {
  fromJSON(object: any): Option {
    return {
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<Option>, I>>(base?: I): Option {
    return Option.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Option>, I>>(object: I): Option {
    const message = createBaseOption();
    message.value = object.value ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseGetDemoSelectorReq(): GetDemoSelectorReq {
  return { foo: '', bar: 0 };
}

export const GetDemoSelectorReq: MessageFns<GetDemoSelectorReq> = {
  fromJSON(object: any): GetDemoSelectorReq {
    return {
      foo: isSet(object.foo) ? globalThis.String(object.foo) : '',
      bar: isSet(object.bar) ? globalThis.Number(object.bar) : 0
    };
  },

  create<I extends Exact<DeepPartial<GetDemoSelectorReq>, I>>(base?: I): GetDemoSelectorReq {
    return GetDemoSelectorReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDemoSelectorReq>, I>>(object: I): GetDemoSelectorReq {
    const message = createBaseGetDemoSelectorReq();
    message.foo = object.foo ?? '';
    message.bar = object.bar ?? 0;
    return message;
  }
};

function createBaseGetDemoSelectorRsp(): GetDemoSelectorRsp {
  return { options: [] };
}

export const GetDemoSelectorRsp: MessageFns<GetDemoSelectorRsp> = {
  fromJSON(object: any): GetDemoSelectorRsp {
    return {
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => Option.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetDemoSelectorRsp>, I>>(base?: I): GetDemoSelectorRsp {
    return GetDemoSelectorRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetDemoSelectorRsp>, I>>(object: I): GetDemoSelectorRsp {
    const message = createBaseGetDemoSelectorRsp();
    message.options = object.options?.map(e => Option.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSingleOperateDemoReq(): SingleOperateDemoReq {
  return { id: 0, bool_field: false };
}

export const SingleOperateDemoReq: MessageFns<SingleOperateDemoReq> = {
  fromJSON(object: any): SingleOperateDemoReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      bool_field: isSet(object.bool_field) ? globalThis.Boolean(object.bool_field) : false
    };
  },

  create<I extends Exact<DeepPartial<SingleOperateDemoReq>, I>>(base?: I): SingleOperateDemoReq {
    return SingleOperateDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SingleOperateDemoReq>, I>>(object: I): SingleOperateDemoReq {
    const message = createBaseSingleOperateDemoReq();
    message.id = object.id ?? 0;
    message.bool_field = object.bool_field ?? false;
    return message;
  }
};

function createBaseSingleOperateDemoRsp(): SingleOperateDemoRsp {
  return {};
}

export const SingleOperateDemoRsp: MessageFns<SingleOperateDemoRsp> = {
  fromJSON(_: any): SingleOperateDemoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SingleOperateDemoRsp>, I>>(base?: I): SingleOperateDemoRsp {
    return SingleOperateDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SingleOperateDemoRsp>, I>>(_: I): SingleOperateDemoRsp {
    const message = createBaseSingleOperateDemoRsp();
    return message;
  }
};

function createBaseBatchOperateDemoReq(): BatchOperateDemoReq {
  return { ids: [], bool_field: false };
}

export const BatchOperateDemoReq: MessageFns<BatchOperateDemoReq> = {
  fromJSON(object: any): BatchOperateDemoReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      bool_field: isSet(object.bool_field) ? globalThis.Boolean(object.bool_field) : false
    };
  },

  create<I extends Exact<DeepPartial<BatchOperateDemoReq>, I>>(base?: I): BatchOperateDemoReq {
    return BatchOperateDemoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchOperateDemoReq>, I>>(object: I): BatchOperateDemoReq {
    const message = createBaseBatchOperateDemoReq();
    message.ids = object.ids?.map(e => e) || [];
    message.bool_field = object.bool_field ?? false;
    return message;
  }
};

function createBaseBatchOperateDemoRsp(): BatchOperateDemoRsp {
  return {};
}

export const BatchOperateDemoRsp: MessageFns<BatchOperateDemoRsp> = {
  fromJSON(_: any): BatchOperateDemoRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BatchOperateDemoRsp>, I>>(base?: I): BatchOperateDemoRsp {
    return BatchOperateDemoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchOperateDemoRsp>, I>>(_: I): BatchOperateDemoRsp {
    const message = createBaseBatchOperateDemoRsp();
    return message;
  }
};

function createBaseFile(): File {
  return { url: '' };
}

export const File: MessageFns<File> = {
  fromJSON(object: any): File {
    return { url: isSet(object.url) ? globalThis.String(object.url) : '' };
  },

  create<I extends Exact<DeepPartial<File>, I>>(base?: I): File {
    return File.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<File>, I>>(object: I): File {
    const message = createBaseFile();
    message.url = object.url ?? '';
    return message;
  }
};

function createBasePrivilege(): Privilege {
  return { type: '', id: 0, use_time: false };
}

export const Privilege: MessageFns<Privilege> = {
  fromJSON(object: any): Privilege {
    return {
      type: isSet(object.type) ? globalThis.String(object.type) : '',
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      use_time: isSet(object.use_time) ? globalThis.Boolean(object.use_time) : false
    };
  },

  create<I extends Exact<DeepPartial<Privilege>, I>>(base?: I): Privilege {
    return Privilege.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Privilege>, I>>(object: I): Privilege {
    const message = createBasePrivilege();
    message.type = object.type ?? '';
    message.id = object.id ?? 0;
    message.use_time = object.use_time ?? false;
    return message;
  }
};

function createBaseReward(): Reward {
  return { privilege: undefined, number: 0 };
}

export const Reward: MessageFns<Reward> = {
  fromJSON(object: any): Reward {
    return {
      privilege: isSet(object.privilege) ? Privilege.fromJSON(object.privilege) : undefined,
      number: isSet(object.number) ? globalThis.Number(object.number) : 0
    };
  },

  create<I extends Exact<DeepPartial<Reward>, I>>(base?: I): Reward {
    return Reward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reward>, I>>(object: I): Reward {
    const message = createBaseReward();
    message.privilege =
      object.privilege !== undefined && object.privilege !== null ? Privilege.fromPartial(object.privilege) : undefined;
    message.number = object.number ?? 0;
    return message;
  }
};

function createBaseColor(): Color {
  return { color: '' };
}

export const Color: MessageFns<Color> = {
  fromJSON(object: any): Color {
    return { color: isSet(object.color) ? globalThis.String(object.color) : '' };
  },

  create<I extends Exact<DeepPartial<Color>, I>>(base?: I): Color {
    return Color.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Color>, I>>(object: I): Color {
    const message = createBaseColor();
    message.color = object.color ?? '';
    return message;
  }
};

function createBaseStaff(): Staff {
  return { union_id: '', name: '', nick_name: '', email: '', user_id: '' };
}

export const Staff: MessageFns<Staff> = {
  fromJSON(object: any): Staff {
    return {
      union_id: isSet(object.union_id) ? globalThis.String(object.union_id) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      nick_name: isSet(object.nick_name) ? globalThis.String(object.nick_name) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      user_id: isSet(object.user_id) ? globalThis.String(object.user_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<Staff>, I>>(base?: I): Staff {
    return Staff.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Staff>, I>>(object: I): Staff {
    const message = createBaseStaff();
    message.union_id = object.union_id ?? '';
    message.name = object.name ?? '';
    message.nick_name = object.nick_name ?? '';
    message.email = object.email ?? '';
    message.user_id = object.user_id ?? '';
    return message;
  }
};

function createBaseDemo(): Demo {
  return {
    id: 0,
    string_field: '',
    int_field: 0,
    float_field: 0,
    bool_field: false,
    enum_field: 0,
    nest_field: undefined,
    date_field: '',
    date_time_field: 0,
    image_field: undefined,
    file_field: undefined,
    reward_field: undefined,
    color_field: undefined,
    string_fields: [],
    int_fields: [],
    float_fields: [],
    bool_fields: [],
    enum_fields: [],
    nest_fields: [],
    date_fields: [],
    date_time_fields: [],
    image_fields: [],
    file_fields: [],
    reward_fields: [],
    color_fields: []
  };
}

export const Demo: MessageFns<Demo> = {
  fromJSON(object: any): Demo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      string_field: isSet(object.string_field) ? globalThis.String(object.string_field) : '',
      int_field: isSet(object.int_field) ? globalThis.Number(object.int_field) : 0,
      float_field: isSet(object.float_field) ? globalThis.Number(object.float_field) : 0,
      bool_field: isSet(object.bool_field) ? globalThis.Boolean(object.bool_field) : false,
      enum_field: isSet(object.enum_field) ? enumFromJSON(object.enum_field) : 0,
      nest_field: isSet(object.nest_field) ? Demo.fromJSON(object.nest_field) : undefined,
      date_field: isSet(object.date_field) ? globalThis.String(object.date_field) : '',
      date_time_field: isSet(object.date_time_field) ? globalThis.Number(object.date_time_field) : 0,
      image_field: isSet(object.image_field) ? File.fromJSON(object.image_field) : undefined,
      file_field: isSet(object.file_field) ? File.fromJSON(object.file_field) : undefined,
      reward_field: isSet(object.reward_field) ? Reward.fromJSON(object.reward_field) : undefined,
      color_field: isSet(object.color_field) ? Color.fromJSON(object.color_field) : undefined,
      string_fields: globalThis.Array.isArray(object?.string_fields)
        ? object.string_fields.map((e: any) => globalThis.String(e))
        : [],
      int_fields: globalThis.Array.isArray(object?.int_fields)
        ? object.int_fields.map((e: any) => globalThis.Number(e))
        : [],
      float_fields: globalThis.Array.isArray(object?.float_fields)
        ? object.float_fields.map((e: any) => globalThis.Number(e))
        : [],
      bool_fields: globalThis.Array.isArray(object?.bool_fields)
        ? object.bool_fields.map((e: any) => globalThis.Boolean(e))
        : [],
      enum_fields: globalThis.Array.isArray(object?.enum_fields)
        ? object.enum_fields.map((e: any) => enumFromJSON(e))
        : [],
      nest_fields: globalThis.Array.isArray(object?.nest_fields)
        ? object.nest_fields.map((e: any) => Demo.fromJSON(e))
        : [],
      date_fields: globalThis.Array.isArray(object?.date_fields)
        ? object.date_fields.map((e: any) => globalThis.String(e))
        : [],
      date_time_fields: globalThis.Array.isArray(object?.date_time_fields)
        ? object.date_time_fields.map((e: any) => globalThis.Number(e))
        : [],
      image_fields: globalThis.Array.isArray(object?.image_fields)
        ? object.image_fields.map((e: any) => File.fromJSON(e))
        : [],
      file_fields: globalThis.Array.isArray(object?.file_fields)
        ? object.file_fields.map((e: any) => File.fromJSON(e))
        : [],
      reward_fields: globalThis.Array.isArray(object?.reward_fields)
        ? object.reward_fields.map((e: any) => Reward.fromJSON(e))
        : [],
      color_fields: globalThis.Array.isArray(object?.color_fields)
        ? object.color_fields.map((e: any) => Color.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<Demo>, I>>(base?: I): Demo {
    return Demo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Demo>, I>>(object: I): Demo {
    const message = createBaseDemo();
    message.id = object.id ?? 0;
    message.string_field = object.string_field ?? '';
    message.int_field = object.int_field ?? 0;
    message.float_field = object.float_field ?? 0;
    message.bool_field = object.bool_field ?? false;
    message.enum_field = object.enum_field ?? 0;
    message.nest_field =
      object.nest_field !== undefined && object.nest_field !== null ? Demo.fromPartial(object.nest_field) : undefined;
    message.date_field = object.date_field ?? '';
    message.date_time_field = object.date_time_field ?? 0;
    message.image_field =
      object.image_field !== undefined && object.image_field !== null
        ? File.fromPartial(object.image_field)
        : undefined;
    message.file_field =
      object.file_field !== undefined && object.file_field !== null ? File.fromPartial(object.file_field) : undefined;
    message.reward_field =
      object.reward_field !== undefined && object.reward_field !== null
        ? Reward.fromPartial(object.reward_field)
        : undefined;
    message.color_field =
      object.color_field !== undefined && object.color_field !== null
        ? Color.fromPartial(object.color_field)
        : undefined;
    message.string_fields = object.string_fields?.map(e => e) || [];
    message.int_fields = object.int_fields?.map(e => e) || [];
    message.float_fields = object.float_fields?.map(e => e) || [];
    message.bool_fields = object.bool_fields?.map(e => e) || [];
    message.enum_fields = object.enum_fields?.map(e => e) || [];
    message.nest_fields = object.nest_fields?.map(e => Demo.fromPartial(e)) || [];
    message.date_fields = object.date_fields?.map(e => e) || [];
    message.date_time_fields = object.date_time_fields?.map(e => e) || [];
    message.image_fields = object.image_fields?.map(e => File.fromPartial(e)) || [];
    message.file_fields = object.file_fields?.map(e => File.fromPartial(e)) || [];
    message.reward_fields = object.reward_fields?.map(e => Reward.fromPartial(e)) || [];
    message.color_fields = object.color_fields?.map(e => Color.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 低代码平台Demo管理接口
 * smicro:spath=gitit.cc/social/components-service/social-platform/biz/lowcode/handlermgr
 */
export type LowcodeDemoMgrDefinition = typeof LowcodeDemoMgrDefinition;
export const LowcodeDemoMgrDefinition = {
  name: 'LowcodeDemoMgr',
  fullName: 'comm.mgr.platform.lowcode.LowcodeDemoMgr',
  methods: {
    createDemo: {
      name: 'CreateDemo',
      requestType: CreateDemoReq,
      requestStream: false,
      responseType: CreateDemoRsp,
      responseStream: false,
      options: {}
    },
    updateDemo: {
      name: 'UpdateDemo',
      requestType: UpdateDemoReq,
      requestStream: false,
      responseType: UpdateDemoRsp,
      responseStream: false,
      options: {}
    },
    deleteDemo: {
      name: 'DeleteDemo',
      requestType: DeleteDemoReq,
      requestStream: false,
      responseType: DeleteDemoRsp,
      responseStream: false,
      options: {}
    },
    searchDemo: {
      name: 'SearchDemo',
      requestType: SearchDemoReq,
      requestStream: false,
      responseType: SearchDemoRsp,
      responseStream: false,
      options: {}
    },
    /** 下拉框数据源 */
    getDemoSelector: {
      name: 'GetDemoSelector',
      requestType: GetDemoSelectorReq,
      requestStream: false,
      responseType: GetDemoSelectorRsp,
      responseStream: false,
      options: {}
    },
    /** 自定义单个操作 */
    singleOperateDemo: {
      name: 'SingleOperateDemo',
      requestType: SingleOperateDemoReq,
      requestStream: false,
      responseType: SingleOperateDemoRsp,
      responseStream: false,
      options: {}
    },
    /** 自定义批量操作 */
    batchOperateDemo: {
      name: 'BatchOperateDemo',
      requestType: BatchOperateDemoReq,
      requestStream: false,
      responseType: BatchOperateDemoRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
