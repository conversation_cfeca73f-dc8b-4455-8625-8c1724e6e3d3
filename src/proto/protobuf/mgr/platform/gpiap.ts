// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/gpiap.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.platform.gpiap';

/** 数据格式 */
export enum DataFormat {
  /** DATA_FORMAT_NONE - 未定义格式 */
  DATA_FORMAT_NONE = 0,
  /** DATA_FORMAT_CSV - CSV 格式 */
  DATA_FORMAT_CSV = 1,
  UNRECOGNIZED = -1
}

export function dataFormatFromJSON(object: any): DataFormat {
  switch (object) {
    case 0:
    case 'DATA_FORMAT_NONE':
      return DataFormat.DATA_FORMAT_NONE;
    case 1:
    case 'DATA_FORMAT_CSV':
      return DataFormat.DATA_FORMAT_CSV;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return DataFormat.UNRECOGNIZED;
  }
}

export interface Application {
  /** 应用名称 */
  application_name: string;
  /** 应用包名 */
  package_name: string;
  /** 默认货币码 */
  default_currency: string;
  /** 默认语言码 */
  default_language: string;
}

export interface ListApplicationReq {}

export interface ListApplicationRsp {
  applications: Application[];
}

/** 商品价格 */
export interface Price {
  /** 以货币基本单位的百万分之一表示的价格，采用字符串形式表示。 */
  priceMicros: string;
  /** ISO 4217 所定义的货币代码（由 3 个字母组成） */
  currency: string;
}

/** 应用内商品的商品详情 */
export interface Listing {
  /** 商品详情的标题。 */
  title: string;
  /** 商品详情的说明。 */
  description: string;
  /** 订阅的已本地化权益福利。 */
  benefits: string[];
}

/** 有关指定地理区域税费方面的详细信息 */
export interface RegionalTaxRateInfo {
  /**
   * 税费档位，用于指定较低的税率。在各个地区销售数字新闻、杂志、报纸、图书或有声读物的开发者可能有资格享受较低的税率。
   * TAX_TIER_UNSPECIFIED
   * TAX_TIER_BOOKS_1
   * TAX_TIER_NEWS_1
   * TAX_TIER_NEWS_2
   * TAX_TIER_MUSIC_OR_AUDIO_1
   * TAX_TIER_LIVE_OR_BROADCAST_1
   */
  tax_tier: string;
  /** 您必须指明您的应用是否包含在线播放产品，以便系统正确地收取美国的州销售税和地方销售税。此字段仅在美国受支持。 */
  eligible_for_streaming_service_tax_rate: boolean;
  /**
   * 如要在美国收取通信税或娱乐税，请选择适当的税种。
   * STREAMING_TAX_TYPE_UNSPECIFIED	不征收电信税。
   * STREAMING_TAX_TYPE_TELCO_VIDEO_RENTAL	美国特有的电信税费档位，适用于视频在线播放、点播、租借 / 订阅 / 按观看次数付费。
   * STREAMING_TAX_TYPE_TELCO_VIDEO_SALES	美国特有的电信税费档位，适用于预先录制的内容（例如影片、电视节目）的视频串流。
   * STREAMING_TAX_TYPE_TELCO_VIDEO_MULTI_CHANNEL	美国特有的电信税费档位，适用于多频道节目的视频串流。
   * STREAMING_TAX_TYPE_TELCO_AUDIO_RENTAL	美国特有的电信税费档位，适用于音频串流、租借 / 订阅。
   * STREAMING_TAX_TYPE_TELCO_AUDIO_SALES	美国特有的电信税费档位，适用于音频串流、销售 / 永久下载。
   * STREAMING_TAX_TYPE_TELCO_AUDIO_MULTI_CHANNEL	美国特有的电信税费档位，适用于多频道音频串流，例如电台。
   */
  streaming_tax_type: string;
}

/** 有关税费和法规遵从方面的详细信息 */
export interface TaxAndComplianceSettings {
  /**
   * 对于在欧洲经济区 (EEA) 分发的商品，表示商品是归类为数字内容还是服务。
   * WITHDRAWAL_RIGHT_TYPE_UNSPECIFIED
   * WITHDRAWAL_RIGHT_DIGITAL_CONTENT
   * WITHDRAWAL_RIGHT_SERVICE
   */
  eea_withdrawal_right_type: string;
  /**
   * 从地区代码到税率详细信息的映射。键是由 Unicode 的“CLDR”定义的地区代码。
   * 包含一系列 "key": value 对的对象。示例：{ "name": "wrench", "mass": "1.3kg", "count": "3" }。
   */
  tax_rate_info_by_region_code: { [key: string]: RegionalTaxRateInfo };
  /** 相应的应用内商品是否已声明为表示代币化数字资产的商品。 */
  is_tokenized_digital_asset: boolean;
}

export interface TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry {
  key: string;
  value: RegionalTaxRateInfo | undefined;
}

/** 应用内商品 */
export interface Product {
  /** 导入数据, CSV 格式时使用. */
  data: { [key: string]: string };
  /** 商品详细字段 */
  package_name: string;
  /** 商品的库存单元 (SKU), 在应用内具有唯一性. */
  sku: string;
  /** 商品状态, statusUnspecified: 未指定状态, active: 商品已发布并在商店中上架, inactive: 商品未发布，因此未在商店中上架. */
  status: string;
  /** 购买类型, purchaseTypeUnspecified: 未指定的购买类型, managedUser: 默认商品类型(一次性购买), subscription: 定期续订的应用内商品. */
  purchase_type: string;
  /** 默认价格。不能为零，因为应用内商品一律不免费。始终采用开发者的 Google Checkout 商家账号所用币种。 */
  default_price: Price | undefined;
  /** 在各个买家地区的价格。这些价格均不能为零，因为应用内商品一律不免费。映射键是 ISO 3166-2 定义的地区代码。 */
  prices: { [key: string]: Price };
  /** 已本地化的应用内商品名和说明数据的列表。映射键是已本地化的数据所使用的语言，其代码由 BCP-47 定义，例如“en-US”。 */
  listings: { [key: string]: Listing };
  /** 已本地化的数据所使用的默认语言，其代码由 BCP-47 定义，例如“en-US”。 */
  default_language: string;
  /** 订阅期，采用 ISO 8601 格式指定。可接受的值为 P1W（1 周）、P1M（1 个月）、P3M（3 个月）、P6M（6 个月）和 P1Y（1 年）。 */
  subscription_period: string;
  /** 试用期，采用 ISO 8601 格式指定。可接受 P7D（7 天）到 P999D（999 天）之间的任何值。 */
  trial_period: string;
  /** 订阅宽限期，采用 ISO 8601 格式指定。让开发者能够在订阅者拒绝为新续订周期付款时，为其提供一个宽限期。可接受的值为 P0D（零天）、P3D（3 天）、P7D（7 天）、P14D（14 天）和 P30D（30 天）。 */
  grace_period: string;
  /** 有关税费和法规遵从方面的详细信息。仅适用于订阅商品。 */
  subscription_taxes_and_compliance_settings: TaxAndComplianceSettings | undefined;
  /** 有关税费和法规遵从方面的详细信息。仅适用于受管理的商品。 */
  managed_product_taxes_and_compliance_settings: TaxAndComplianceSettings | undefined;
}

export interface Product_DataEntry {
  key: string;
  value: string;
}

export interface Product_PricesEntry {
  key: string;
  value: Price | undefined;
}

export interface Product_ListingsEntry {
  key: string;
  value: Listing | undefined;
}

export interface ProductUpdateRequest {
  /** 如果设置为 true，且不存在具有指定 packageName 和 sku 的应用内商品，则系统将创建该应用内商品。 */
  allow_missing: boolean;
  /** 如果设置为 true，则对于父级应用定位的所有地区，当未为相应应用内商品指定价格时，系统会根据默认价格自动转换为目标货币。默认值为 false。 */
  auto_convert_missing_prices: boolean;
  /** 新的应用内商品。 */
  inappproduct: Product | undefined;
  /**
   * PRODUCT_UPDATE_LATENCY_TOLERANCE_UNSPECIFIED	默认值为 PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE。
   * PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_SENSITIVE	更新平均在几分钟内传播到客户端，在极少数情况下则长达数小时。吞吐量上限为每个应用每小时 7200 次更新。
   * PRODUCT_UPDATE_LATENCY_TOLERANCE_LATENCY_TOLERANT	更新将在 24 小时内传播到各客户端。使用批量修改方法支持高吞吐量，每个应用每小时最多执行 72 万次更新。
   */
  latency_tolerance: string;
  /** 应用的软件包名称。 */
  package_name: string;
  /** 应用内商品的唯一标识符。 */
  sku: string;
}

export interface ProductBatchUpdateRequest {
  /** 一个或多个单独的更新请求。至少需要有一个请求。最多可包含 100 个请求。所有请求必须分别对应于不同的应用内商品。 */
  requests: ProductUpdateRequest[];
}

export interface ProductBatchUpdateResponse {
  /** 已更新或已插入的应用内商品。 */
  inappproducts: Product[];
}

export interface ImportProductReq {
  /** 包名 */
  package_name: string;
  /** 数据格式 */
  data_format: DataFormat;
  /** 商品列表 */
  products: Product[];
}

export interface ImportProductRsp {
  /** 商品列表 */
  products: Product[];
}

export interface TokenPagination {
  /** 下一页令牌 */
  next_page_token: string;
  /** 上一页令牌 */
  prev_page_token: string;
}

export interface ListProductReq {
  /** 包名 */
  package_name: string;
  /** 分页令牌 */
  page_token: string;
}

export interface ListProductRsp {
  /** 令牌分页 */
  token_pagination: TokenPagination | undefined;
  /** 应用内商品列表 */
  products: Product[];
}

function createBaseApplication(): Application {
  return { application_name: '', package_name: '', default_currency: '', default_language: '' };
}

export const Application: MessageFns<Application> = {
  fromJSON(object: any): Application {
    return {
      application_name: isSet(object.application_name) ? globalThis.String(object.application_name) : '',
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      default_currency: isSet(object.default_currency) ? globalThis.String(object.default_currency) : '',
      default_language: isSet(object.default_language) ? globalThis.String(object.default_language) : ''
    };
  },

  create<I extends Exact<DeepPartial<Application>, I>>(base?: I): Application {
    return Application.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Application>, I>>(object: I): Application {
    const message = createBaseApplication();
    message.application_name = object.application_name ?? '';
    message.package_name = object.package_name ?? '';
    message.default_currency = object.default_currency ?? '';
    message.default_language = object.default_language ?? '';
    return message;
  }
};

function createBaseListApplicationReq(): ListApplicationReq {
  return {};
}

export const ListApplicationReq: MessageFns<ListApplicationReq> = {
  fromJSON(_: any): ListApplicationReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListApplicationReq>, I>>(base?: I): ListApplicationReq {
    return ListApplicationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListApplicationReq>, I>>(_: I): ListApplicationReq {
    const message = createBaseListApplicationReq();
    return message;
  }
};

function createBaseListApplicationRsp(): ListApplicationRsp {
  return { applications: [] };
}

export const ListApplicationRsp: MessageFns<ListApplicationRsp> = {
  fromJSON(object: any): ListApplicationRsp {
    return {
      applications: globalThis.Array.isArray(object?.applications)
        ? object.applications.map((e: any) => Application.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListApplicationRsp>, I>>(base?: I): ListApplicationRsp {
    return ListApplicationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListApplicationRsp>, I>>(object: I): ListApplicationRsp {
    const message = createBaseListApplicationRsp();
    message.applications = object.applications?.map(e => Application.fromPartial(e)) || [];
    return message;
  }
};

function createBasePrice(): Price {
  return { priceMicros: '', currency: '' };
}

export const Price: MessageFns<Price> = {
  fromJSON(object: any): Price {
    return {
      priceMicros: isSet(object.priceMicros) ? globalThis.String(object.priceMicros) : '',
      currency: isSet(object.currency) ? globalThis.String(object.currency) : ''
    };
  },

  create<I extends Exact<DeepPartial<Price>, I>>(base?: I): Price {
    return Price.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Price>, I>>(object: I): Price {
    const message = createBasePrice();
    message.priceMicros = object.priceMicros ?? '';
    message.currency = object.currency ?? '';
    return message;
  }
};

function createBaseListing(): Listing {
  return { title: '', description: '', benefits: [] };
}

export const Listing: MessageFns<Listing> = {
  fromJSON(object: any): Listing {
    return {
      title: isSet(object.title) ? globalThis.String(object.title) : '',
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      benefits: globalThis.Array.isArray(object?.benefits) ? object.benefits.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Listing>, I>>(base?: I): Listing {
    return Listing.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Listing>, I>>(object: I): Listing {
    const message = createBaseListing();
    message.title = object.title ?? '';
    message.description = object.description ?? '';
    message.benefits = object.benefits?.map(e => e) || [];
    return message;
  }
};

function createBaseRegionalTaxRateInfo(): RegionalTaxRateInfo {
  return { tax_tier: '', eligible_for_streaming_service_tax_rate: false, streaming_tax_type: '' };
}

export const RegionalTaxRateInfo: MessageFns<RegionalTaxRateInfo> = {
  fromJSON(object: any): RegionalTaxRateInfo {
    return {
      tax_tier: isSet(object.tax_tier) ? globalThis.String(object.tax_tier) : '',
      eligible_for_streaming_service_tax_rate: isSet(object.eligible_for_streaming_service_tax_rate)
        ? globalThis.Boolean(object.eligible_for_streaming_service_tax_rate)
        : false,
      streaming_tax_type: isSet(object.streaming_tax_type) ? globalThis.String(object.streaming_tax_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<RegionalTaxRateInfo>, I>>(base?: I): RegionalTaxRateInfo {
    return RegionalTaxRateInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RegionalTaxRateInfo>, I>>(object: I): RegionalTaxRateInfo {
    const message = createBaseRegionalTaxRateInfo();
    message.tax_tier = object.tax_tier ?? '';
    message.eligible_for_streaming_service_tax_rate = object.eligible_for_streaming_service_tax_rate ?? false;
    message.streaming_tax_type = object.streaming_tax_type ?? '';
    return message;
  }
};

function createBaseTaxAndComplianceSettings(): TaxAndComplianceSettings {
  return { eea_withdrawal_right_type: '', tax_rate_info_by_region_code: {}, is_tokenized_digital_asset: false };
}

export const TaxAndComplianceSettings: MessageFns<TaxAndComplianceSettings> = {
  fromJSON(object: any): TaxAndComplianceSettings {
    return {
      eea_withdrawal_right_type: isSet(object.eea_withdrawal_right_type)
        ? globalThis.String(object.eea_withdrawal_right_type)
        : '',
      tax_rate_info_by_region_code: isObject(object.tax_rate_info_by_region_code)
        ? Object.entries(object.tax_rate_info_by_region_code).reduce<{ [key: string]: RegionalTaxRateInfo }>(
            (acc, [key, value]) => {
              acc[key] = RegionalTaxRateInfo.fromJSON(value);
              return acc;
            },
            {}
          )
        : {},
      is_tokenized_digital_asset: isSet(object.is_tokenized_digital_asset)
        ? globalThis.Boolean(object.is_tokenized_digital_asset)
        : false
    };
  },

  create<I extends Exact<DeepPartial<TaxAndComplianceSettings>, I>>(base?: I): TaxAndComplianceSettings {
    return TaxAndComplianceSettings.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaxAndComplianceSettings>, I>>(object: I): TaxAndComplianceSettings {
    const message = createBaseTaxAndComplianceSettings();
    message.eea_withdrawal_right_type = object.eea_withdrawal_right_type ?? '';
    message.tax_rate_info_by_region_code = Object.entries(object.tax_rate_info_by_region_code ?? {}).reduce<{
      [key: string]: RegionalTaxRateInfo;
    }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = RegionalTaxRateInfo.fromPartial(value);
      }
      return acc;
    }, {});
    message.is_tokenized_digital_asset = object.is_tokenized_digital_asset ?? false;
    return message;
  }
};

function createBaseTaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry(): TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry {
  return { key: '', value: undefined };
}

export const TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry: MessageFns<TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry> =
  {
    fromJSON(object: any): TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry {
      return {
        key: isSet(object.key) ? globalThis.String(object.key) : '',
        value: isSet(object.value) ? RegionalTaxRateInfo.fromJSON(object.value) : undefined
      };
    },

    create<I extends Exact<DeepPartial<TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry>, I>>(
      base?: I
    ): TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry {
      return TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry.fromPartial(base ?? ({} as any));
    },
    fromPartial<I extends Exact<DeepPartial<TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry>, I>>(
      object: I
    ): TaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry {
      const message = createBaseTaxAndComplianceSettings_TaxRateInfoByRegionCodeEntry();
      message.key = object.key ?? '';
      message.value =
        object.value !== undefined && object.value !== null ? RegionalTaxRateInfo.fromPartial(object.value) : undefined;
      return message;
    }
  };

function createBaseProduct(): Product {
  return {
    data: {},
    package_name: '',
    sku: '',
    status: '',
    purchase_type: '',
    default_price: undefined,
    prices: {},
    listings: {},
    default_language: '',
    subscription_period: '',
    trial_period: '',
    grace_period: '',
    subscription_taxes_and_compliance_settings: undefined,
    managed_product_taxes_and_compliance_settings: undefined
  };
}

export const Product: MessageFns<Product> = {
  fromJSON(object: any): Product {
    return {
      data: isObject(object.data)
        ? Object.entries(object.data).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      sku: isSet(object.sku) ? globalThis.String(object.sku) : '',
      status: isSet(object.status) ? globalThis.String(object.status) : '',
      purchase_type: isSet(object.purchase_type) ? globalThis.String(object.purchase_type) : '',
      default_price: isSet(object.default_price) ? Price.fromJSON(object.default_price) : undefined,
      prices: isObject(object.prices)
        ? Object.entries(object.prices).reduce<{ [key: string]: Price }>((acc, [key, value]) => {
            acc[key] = Price.fromJSON(value);
            return acc;
          }, {})
        : {},
      listings: isObject(object.listings)
        ? Object.entries(object.listings).reduce<{ [key: string]: Listing }>((acc, [key, value]) => {
            acc[key] = Listing.fromJSON(value);
            return acc;
          }, {})
        : {},
      default_language: isSet(object.default_language) ? globalThis.String(object.default_language) : '',
      subscription_period: isSet(object.subscription_period) ? globalThis.String(object.subscription_period) : '',
      trial_period: isSet(object.trial_period) ? globalThis.String(object.trial_period) : '',
      grace_period: isSet(object.grace_period) ? globalThis.String(object.grace_period) : '',
      subscription_taxes_and_compliance_settings: isSet(object.subscription_taxes_and_compliance_settings)
        ? TaxAndComplianceSettings.fromJSON(object.subscription_taxes_and_compliance_settings)
        : undefined,
      managed_product_taxes_and_compliance_settings: isSet(object.managed_product_taxes_and_compliance_settings)
        ? TaxAndComplianceSettings.fromJSON(object.managed_product_taxes_and_compliance_settings)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<Product>, I>>(base?: I): Product {
    return Product.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Product>, I>>(object: I): Product {
    const message = createBaseProduct();
    message.data = Object.entries(object.data ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.package_name = object.package_name ?? '';
    message.sku = object.sku ?? '';
    message.status = object.status ?? '';
    message.purchase_type = object.purchase_type ?? '';
    message.default_price =
      object.default_price !== undefined && object.default_price !== null
        ? Price.fromPartial(object.default_price)
        : undefined;
    message.prices = Object.entries(object.prices ?? {}).reduce<{ [key: string]: Price }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Price.fromPartial(value);
      }
      return acc;
    }, {});
    message.listings = Object.entries(object.listings ?? {}).reduce<{ [key: string]: Listing }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = Listing.fromPartial(value);
      }
      return acc;
    }, {});
    message.default_language = object.default_language ?? '';
    message.subscription_period = object.subscription_period ?? '';
    message.trial_period = object.trial_period ?? '';
    message.grace_period = object.grace_period ?? '';
    message.subscription_taxes_and_compliance_settings =
      object.subscription_taxes_and_compliance_settings !== undefined &&
      object.subscription_taxes_and_compliance_settings !== null
        ? TaxAndComplianceSettings.fromPartial(object.subscription_taxes_and_compliance_settings)
        : undefined;
    message.managed_product_taxes_and_compliance_settings =
      object.managed_product_taxes_and_compliance_settings !== undefined &&
      object.managed_product_taxes_and_compliance_settings !== null
        ? TaxAndComplianceSettings.fromPartial(object.managed_product_taxes_and_compliance_settings)
        : undefined;
    return message;
  }
};

function createBaseProduct_DataEntry(): Product_DataEntry {
  return { key: '', value: '' };
}

export const Product_DataEntry: MessageFns<Product_DataEntry> = {
  fromJSON(object: any): Product_DataEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Product_DataEntry>, I>>(base?: I): Product_DataEntry {
    return Product_DataEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Product_DataEntry>, I>>(object: I): Product_DataEntry {
    const message = createBaseProduct_DataEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseProduct_PricesEntry(): Product_PricesEntry {
  return { key: '', value: undefined };
}

export const Product_PricesEntry: MessageFns<Product_PricesEntry> = {
  fromJSON(object: any): Product_PricesEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Price.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Product_PricesEntry>, I>>(base?: I): Product_PricesEntry {
    return Product_PricesEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Product_PricesEntry>, I>>(object: I): Product_PricesEntry {
    const message = createBaseProduct_PricesEntry();
    message.key = object.key ?? '';
    message.value = object.value !== undefined && object.value !== null ? Price.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseProduct_ListingsEntry(): Product_ListingsEntry {
  return { key: '', value: undefined };
}

export const Product_ListingsEntry: MessageFns<Product_ListingsEntry> = {
  fromJSON(object: any): Product_ListingsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? Listing.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Product_ListingsEntry>, I>>(base?: I): Product_ListingsEntry {
    return Product_ListingsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Product_ListingsEntry>, I>>(object: I): Product_ListingsEntry {
    const message = createBaseProduct_ListingsEntry();
    message.key = object.key ?? '';
    message.value = object.value !== undefined && object.value !== null ? Listing.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseProductUpdateRequest(): ProductUpdateRequest {
  return {
    allow_missing: false,
    auto_convert_missing_prices: false,
    inappproduct: undefined,
    latency_tolerance: '',
    package_name: '',
    sku: ''
  };
}

export const ProductUpdateRequest: MessageFns<ProductUpdateRequest> = {
  fromJSON(object: any): ProductUpdateRequest {
    return {
      allow_missing: isSet(object.allow_missing) ? globalThis.Boolean(object.allow_missing) : false,
      auto_convert_missing_prices: isSet(object.auto_convert_missing_prices)
        ? globalThis.Boolean(object.auto_convert_missing_prices)
        : false,
      inappproduct: isSet(object.inappproduct) ? Product.fromJSON(object.inappproduct) : undefined,
      latency_tolerance: isSet(object.latency_tolerance) ? globalThis.String(object.latency_tolerance) : '',
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      sku: isSet(object.sku) ? globalThis.String(object.sku) : ''
    };
  },

  create<I extends Exact<DeepPartial<ProductUpdateRequest>, I>>(base?: I): ProductUpdateRequest {
    return ProductUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductUpdateRequest>, I>>(object: I): ProductUpdateRequest {
    const message = createBaseProductUpdateRequest();
    message.allow_missing = object.allow_missing ?? false;
    message.auto_convert_missing_prices = object.auto_convert_missing_prices ?? false;
    message.inappproduct =
      object.inappproduct !== undefined && object.inappproduct !== null
        ? Product.fromPartial(object.inappproduct)
        : undefined;
    message.latency_tolerance = object.latency_tolerance ?? '';
    message.package_name = object.package_name ?? '';
    message.sku = object.sku ?? '';
    return message;
  }
};

function createBaseProductBatchUpdateRequest(): ProductBatchUpdateRequest {
  return { requests: [] };
}

export const ProductBatchUpdateRequest: MessageFns<ProductBatchUpdateRequest> = {
  fromJSON(object: any): ProductBatchUpdateRequest {
    return {
      requests: globalThis.Array.isArray(object?.requests)
        ? object.requests.map((e: any) => ProductUpdateRequest.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ProductBatchUpdateRequest>, I>>(base?: I): ProductBatchUpdateRequest {
    return ProductBatchUpdateRequest.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductBatchUpdateRequest>, I>>(object: I): ProductBatchUpdateRequest {
    const message = createBaseProductBatchUpdateRequest();
    message.requests = object.requests?.map(e => ProductUpdateRequest.fromPartial(e)) || [];
    return message;
  }
};

function createBaseProductBatchUpdateResponse(): ProductBatchUpdateResponse {
  return { inappproducts: [] };
}

export const ProductBatchUpdateResponse: MessageFns<ProductBatchUpdateResponse> = {
  fromJSON(object: any): ProductBatchUpdateResponse {
    return {
      inappproducts: globalThis.Array.isArray(object?.inappproducts)
        ? object.inappproducts.map((e: any) => Product.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ProductBatchUpdateResponse>, I>>(base?: I): ProductBatchUpdateResponse {
    return ProductBatchUpdateResponse.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ProductBatchUpdateResponse>, I>>(object: I): ProductBatchUpdateResponse {
    const message = createBaseProductBatchUpdateResponse();
    message.inappproducts = object.inappproducts?.map(e => Product.fromPartial(e)) || [];
    return message;
  }
};

function createBaseImportProductReq(): ImportProductReq {
  return { package_name: '', data_format: 0, products: [] };
}

export const ImportProductReq: MessageFns<ImportProductReq> = {
  fromJSON(object: any): ImportProductReq {
    return {
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      data_format: isSet(object.data_format) ? dataFormatFromJSON(object.data_format) : 0,
      products: globalThis.Array.isArray(object?.products) ? object.products.map((e: any) => Product.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ImportProductReq>, I>>(base?: I): ImportProductReq {
    return ImportProductReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImportProductReq>, I>>(object: I): ImportProductReq {
    const message = createBaseImportProductReq();
    message.package_name = object.package_name ?? '';
    message.data_format = object.data_format ?? 0;
    message.products = object.products?.map(e => Product.fromPartial(e)) || [];
    return message;
  }
};

function createBaseImportProductRsp(): ImportProductRsp {
  return { products: [] };
}

export const ImportProductRsp: MessageFns<ImportProductRsp> = {
  fromJSON(object: any): ImportProductRsp {
    return {
      products: globalThis.Array.isArray(object?.products) ? object.products.map((e: any) => Product.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ImportProductRsp>, I>>(base?: I): ImportProductRsp {
    return ImportProductRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ImportProductRsp>, I>>(object: I): ImportProductRsp {
    const message = createBaseImportProductRsp();
    message.products = object.products?.map(e => Product.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTokenPagination(): TokenPagination {
  return { next_page_token: '', prev_page_token: '' };
}

export const TokenPagination: MessageFns<TokenPagination> = {
  fromJSON(object: any): TokenPagination {
    return {
      next_page_token: isSet(object.next_page_token) ? globalThis.String(object.next_page_token) : '',
      prev_page_token: isSet(object.prev_page_token) ? globalThis.String(object.prev_page_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<TokenPagination>, I>>(base?: I): TokenPagination {
    return TokenPagination.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TokenPagination>, I>>(object: I): TokenPagination {
    const message = createBaseTokenPagination();
    message.next_page_token = object.next_page_token ?? '';
    message.prev_page_token = object.prev_page_token ?? '';
    return message;
  }
};

function createBaseListProductReq(): ListProductReq {
  return { package_name: '', page_token: '' };
}

export const ListProductReq: MessageFns<ListProductReq> = {
  fromJSON(object: any): ListProductReq {
    return {
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      page_token: isSet(object.page_token) ? globalThis.String(object.page_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListProductReq>, I>>(base?: I): ListProductReq {
    return ListProductReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListProductReq>, I>>(object: I): ListProductReq {
    const message = createBaseListProductReq();
    message.package_name = object.package_name ?? '';
    message.page_token = object.page_token ?? '';
    return message;
  }
};

function createBaseListProductRsp(): ListProductRsp {
  return { token_pagination: undefined, products: [] };
}

export const ListProductRsp: MessageFns<ListProductRsp> = {
  fromJSON(object: any): ListProductRsp {
    return {
      token_pagination: isSet(object.token_pagination) ? TokenPagination.fromJSON(object.token_pagination) : undefined,
      products: globalThis.Array.isArray(object?.products) ? object.products.map((e: any) => Product.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListProductRsp>, I>>(base?: I): ListProductRsp {
    return ListProductRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListProductRsp>, I>>(object: I): ListProductRsp {
    const message = createBaseListProductRsp();
    message.token_pagination =
      object.token_pagination !== undefined && object.token_pagination !== null
        ? TokenPagination.fromPartial(object.token_pagination)
        : undefined;
    message.products = object.products?.map(e => Product.fromPartial(e)) || [];
    return message;
  }
};

/**
 * Google Play In App Products 谷歌应用内商品管理接口
 * smicro:spath=gitit.cc/social/components-service/social-platform/biz/gpiap/handlermgr
 */
export type GPIAPMgrDefinition = typeof GPIAPMgrDefinition;
export const GPIAPMgrDefinition = {
  name: 'GPIAPMgr',
  fullName: 'comm.mgr.platform.gpiap.GPIAPMgr',
  methods: {
    /** 列出所有应用 */
    listApplication: {
      name: 'ListApplication',
      requestType: ListApplicationReq,
      requestStream: false,
      responseType: ListApplicationRsp,
      responseStream: false,
      options: {}
    },
    /** 导入应用内商品 */
    importProduct: {
      name: 'ImportProduct',
      requestType: ImportProductReq,
      requestStream: false,
      responseType: ImportProductRsp,
      responseStream: false,
      options: {}
    },
    /** 列出应用内商品 */
    listProduct: {
      name: 'ListProduct',
      requestType: ListProductReq,
      requestStream: false,
      responseType: ListProductRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
