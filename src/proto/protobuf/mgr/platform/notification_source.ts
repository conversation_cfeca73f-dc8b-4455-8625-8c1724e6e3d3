// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/notification_source.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.notification';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/notification/handlermgr/notification_source.go */

export enum JumpType {
  /** JUMP_TYPE_NONE - 不跳转 */
  JUMP_TYPE_NONE = 0,
  /** JUMP_TYPE_ROUTE - 路由跳转 */
  JUMP_TYPE_ROUTE = 1,
  /** JUMP_TYPE_H5 - H5跳转 */
  JUMP_TYPE_H5 = 2,
  /** JUMP_TYPE_ROOM - 房间跳转 */
  JUMP_TYPE_ROOM = 3,
  UNRECOGNIZED = -1
}

export function jumpTypeFromJSON(object: any): JumpType {
  switch (object) {
    case 0:
    case 'JUMP_TYPE_NONE':
      return JumpType.JUMP_TYPE_NONE;
    case 1:
    case 'JUMP_TYPE_ROUTE':
      return JumpType.JUMP_TYPE_ROUTE;
    case 2:
    case 'JUMP_TYPE_H5':
      return JumpType.JUMP_TYPE_H5;
    case 3:
    case 'JUMP_TYPE_ROOM':
      return JumpType.JUMP_TYPE_ROOM;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return JumpType.UNRECOGNIZED;
  }
}

export interface Source {
  /** 是否有大图 */
  has_img: boolean;
  /** en: 英文图, zh: 中文图 ar: 阿拉伯图 */
  img: { [key: string]: string };
  /** en: 英文按钮, zh: 中文按钮 ar: 阿拉伯按钮 */
  btn: { [key: string]: string };
  /** 跳转类型 */
  jump_type: JumpType;
  /** 跳转链接 */
  jump_link: string;
  /** 备注 */
  comment: string;
  /** 操作者 */
  operator: string;
  /** 是否有跳转按钮 */
  has_btn: boolean;
  id: number;
  create_time: number;
  update_time: number;
  deleted: boolean;
}

export interface Source_ImgEntry {
  key: string;
  value: string;
}

export interface Source_BtnEntry {
  key: string;
  value: string;
}

export interface AddSourceReq {
  /** 是否有大图 */
  has_img: boolean;
  /** en: 英文图, zh: 中文图 ar: 阿拉伯图 */
  img: { [key: string]: string };
  /** en: 英文按钮, zh: 中文按钮 ar: 阿拉伯按钮 */
  btn: { [key: string]: string };
  /** 跳转类型 */
  jump_type: JumpType;
  /** 跳转链接 */
  jump_link: string;
  /** 备注 */
  comment: string;
  /** 操作者 */
  operator: string;
  /** 是否有跳转按钮 */
  has_btn: boolean;
}

export interface AddSourceReq_ImgEntry {
  key: string;
  value: string;
}

export interface AddSourceReq_BtnEntry {
  key: string;
  value: string;
}

export interface AddSourceRsp {
  id: number;
}

export interface DelSourceReq {
  ids: number[];
  operator: string;
}

export interface DelSourceRsp {
  count: number;
}

export interface UpdateSourceReq {
  req: Source | undefined;
  operator: string;
}

export interface UpdateSourceRsp {
  rsp: Source | undefined;
}

export interface GetSourceReq {
  id: number;
}

export interface GetSourceRsp {
  resp: Source | undefined;
}

export interface ListSourceReq {
  page: Page | undefined;
  id: number;
  operator: string;
  begin: number;
  end: number;
}

export interface ListSourceRsp {
  page: Page | undefined;
  list: Source[];
}

function createBaseSource(): Source {
  return {
    has_img: false,
    img: {},
    btn: {},
    jump_type: 0,
    jump_link: '',
    comment: '',
    operator: '',
    has_btn: false,
    id: 0,
    create_time: 0,
    update_time: 0,
    deleted: false
  };
}

export const Source: MessageFns<Source> = {
  fromJSON(object: any): Source {
    return {
      has_img: isSet(object.has_img) ? globalThis.Boolean(object.has_img) : false,
      img: isObject(object.img)
        ? Object.entries(object.img).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      btn: isObject(object.btn)
        ? Object.entries(object.btn).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      jump_link: isSet(object.jump_link) ? globalThis.String(object.jump_link) : '',
      comment: isSet(object.comment) ? globalThis.String(object.comment) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      has_btn: isSet(object.has_btn) ? globalThis.Boolean(object.has_btn) : false,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      create_time: isSet(object.create_time) ? globalThis.Number(object.create_time) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0,
      deleted: isSet(object.deleted) ? globalThis.Boolean(object.deleted) : false
    };
  },

  create<I extends Exact<DeepPartial<Source>, I>>(base?: I): Source {
    return Source.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Source>, I>>(object: I): Source {
    const message = createBaseSource();
    message.has_img = object.has_img ?? false;
    message.img = Object.entries(object.img ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.btn = Object.entries(object.btn ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.jump_type = object.jump_type ?? 0;
    message.jump_link = object.jump_link ?? '';
    message.comment = object.comment ?? '';
    message.operator = object.operator ?? '';
    message.has_btn = object.has_btn ?? false;
    message.id = object.id ?? 0;
    message.create_time = object.create_time ?? 0;
    message.update_time = object.update_time ?? 0;
    message.deleted = object.deleted ?? false;
    return message;
  }
};

function createBaseSource_ImgEntry(): Source_ImgEntry {
  return { key: '', value: '' };
}

export const Source_ImgEntry: MessageFns<Source_ImgEntry> = {
  fromJSON(object: any): Source_ImgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Source_ImgEntry>, I>>(base?: I): Source_ImgEntry {
    return Source_ImgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Source_ImgEntry>, I>>(object: I): Source_ImgEntry {
    const message = createBaseSource_ImgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSource_BtnEntry(): Source_BtnEntry {
  return { key: '', value: '' };
}

export const Source_BtnEntry: MessageFns<Source_BtnEntry> = {
  fromJSON(object: any): Source_BtnEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Source_BtnEntry>, I>>(base?: I): Source_BtnEntry {
    return Source_BtnEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Source_BtnEntry>, I>>(object: I): Source_BtnEntry {
    const message = createBaseSource_BtnEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddSourceReq(): AddSourceReq {
  return { has_img: false, img: {}, btn: {}, jump_type: 0, jump_link: '', comment: '', operator: '', has_btn: false };
}

export const AddSourceReq: MessageFns<AddSourceReq> = {
  fromJSON(object: any): AddSourceReq {
    return {
      has_img: isSet(object.has_img) ? globalThis.Boolean(object.has_img) : false,
      img: isObject(object.img)
        ? Object.entries(object.img).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      btn: isObject(object.btn)
        ? Object.entries(object.btn).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      jump_type: isSet(object.jump_type) ? jumpTypeFromJSON(object.jump_type) : 0,
      jump_link: isSet(object.jump_link) ? globalThis.String(object.jump_link) : '',
      comment: isSet(object.comment) ? globalThis.String(object.comment) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      has_btn: isSet(object.has_btn) ? globalThis.Boolean(object.has_btn) : false
    };
  },

  create<I extends Exact<DeepPartial<AddSourceReq>, I>>(base?: I): AddSourceReq {
    return AddSourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSourceReq>, I>>(object: I): AddSourceReq {
    const message = createBaseAddSourceReq();
    message.has_img = object.has_img ?? false;
    message.img = Object.entries(object.img ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.btn = Object.entries(object.btn ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    message.jump_type = object.jump_type ?? 0;
    message.jump_link = object.jump_link ?? '';
    message.comment = object.comment ?? '';
    message.operator = object.operator ?? '';
    message.has_btn = object.has_btn ?? false;
    return message;
  }
};

function createBaseAddSourceReq_ImgEntry(): AddSourceReq_ImgEntry {
  return { key: '', value: '' };
}

export const AddSourceReq_ImgEntry: MessageFns<AddSourceReq_ImgEntry> = {
  fromJSON(object: any): AddSourceReq_ImgEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddSourceReq_ImgEntry>, I>>(base?: I): AddSourceReq_ImgEntry {
    return AddSourceReq_ImgEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSourceReq_ImgEntry>, I>>(object: I): AddSourceReq_ImgEntry {
    const message = createBaseAddSourceReq_ImgEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddSourceReq_BtnEntry(): AddSourceReq_BtnEntry {
  return { key: '', value: '' };
}

export const AddSourceReq_BtnEntry: MessageFns<AddSourceReq_BtnEntry> = {
  fromJSON(object: any): AddSourceReq_BtnEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddSourceReq_BtnEntry>, I>>(base?: I): AddSourceReq_BtnEntry {
    return AddSourceReq_BtnEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSourceReq_BtnEntry>, I>>(object: I): AddSourceReq_BtnEntry {
    const message = createBaseAddSourceReq_BtnEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseAddSourceRsp(): AddSourceRsp {
  return { id: 0 };
}

export const AddSourceRsp: MessageFns<AddSourceRsp> = {
  fromJSON(object: any): AddSourceRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddSourceRsp>, I>>(base?: I): AddSourceRsp {
    return AddSourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSourceRsp>, I>>(object: I): AddSourceRsp {
    const message = createBaseAddSourceRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDelSourceReq(): DelSourceReq {
  return { ids: [], operator: '' };
}

export const DelSourceReq: MessageFns<DelSourceReq> = {
  fromJSON(object: any): DelSourceReq {
    return {
      ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [],
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<DelSourceReq>, I>>(base?: I): DelSourceReq {
    return DelSourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelSourceReq>, I>>(object: I): DelSourceReq {
    const message = createBaseDelSourceReq();
    message.ids = object.ids?.map(e => e) || [];
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseDelSourceRsp(): DelSourceRsp {
  return { count: 0 };
}

export const DelSourceRsp: MessageFns<DelSourceRsp> = {
  fromJSON(object: any): DelSourceRsp {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  create<I extends Exact<DeepPartial<DelSourceRsp>, I>>(base?: I): DelSourceRsp {
    return DelSourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelSourceRsp>, I>>(object: I): DelSourceRsp {
    const message = createBaseDelSourceRsp();
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseUpdateSourceReq(): UpdateSourceReq {
  return { req: undefined, operator: '' };
}

export const UpdateSourceReq: MessageFns<UpdateSourceReq> = {
  fromJSON(object: any): UpdateSourceReq {
    return {
      req: isSet(object.req) ? Source.fromJSON(object.req) : undefined,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdateSourceReq>, I>>(base?: I): UpdateSourceReq {
    return UpdateSourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSourceReq>, I>>(object: I): UpdateSourceReq {
    const message = createBaseUpdateSourceReq();
    message.req = object.req !== undefined && object.req !== null ? Source.fromPartial(object.req) : undefined;
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseUpdateSourceRsp(): UpdateSourceRsp {
  return { rsp: undefined };
}

export const UpdateSourceRsp: MessageFns<UpdateSourceRsp> = {
  fromJSON(object: any): UpdateSourceRsp {
    return { rsp: isSet(object.rsp) ? Source.fromJSON(object.rsp) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateSourceRsp>, I>>(base?: I): UpdateSourceRsp {
    return UpdateSourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSourceRsp>, I>>(object: I): UpdateSourceRsp {
    const message = createBaseUpdateSourceRsp();
    message.rsp = object.rsp !== undefined && object.rsp !== null ? Source.fromPartial(object.rsp) : undefined;
    return message;
  }
};

function createBaseGetSourceReq(): GetSourceReq {
  return { id: 0 };
}

export const GetSourceReq: MessageFns<GetSourceReq> = {
  fromJSON(object: any): GetSourceReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetSourceReq>, I>>(base?: I): GetSourceReq {
    return GetSourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSourceReq>, I>>(object: I): GetSourceReq {
    const message = createBaseGetSourceReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetSourceRsp(): GetSourceRsp {
  return { resp: undefined };
}

export const GetSourceRsp: MessageFns<GetSourceRsp> = {
  fromJSON(object: any): GetSourceRsp {
    return { resp: isSet(object.resp) ? Source.fromJSON(object.resp) : undefined };
  },

  create<I extends Exact<DeepPartial<GetSourceRsp>, I>>(base?: I): GetSourceRsp {
    return GetSourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetSourceRsp>, I>>(object: I): GetSourceRsp {
    const message = createBaseGetSourceRsp();
    message.resp = object.resp !== undefined && object.resp !== null ? Source.fromPartial(object.resp) : undefined;
    return message;
  }
};

function createBaseListSourceReq(): ListSourceReq {
  return { page: undefined, id: 0, operator: '', begin: 0, end: 0 };
}

export const ListSourceReq: MessageFns<ListSourceReq> = {
  fromJSON(object: any): ListSourceReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      begin: isSet(object.begin) ? globalThis.Number(object.begin) : 0,
      end: isSet(object.end) ? globalThis.Number(object.end) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListSourceReq>, I>>(base?: I): ListSourceReq {
    return ListSourceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSourceReq>, I>>(object: I): ListSourceReq {
    const message = createBaseListSourceReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.operator = object.operator ?? '';
    message.begin = object.begin ?? 0;
    message.end = object.end ?? 0;
    return message;
  }
};

function createBaseListSourceRsp(): ListSourceRsp {
  return { page: undefined, list: [] };
}

export const ListSourceRsp: MessageFns<ListSourceRsp> = {
  fromJSON(object: any): ListSourceRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      list: globalThis.Array.isArray(object?.list) ? object.list.map((e: any) => Source.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListSourceRsp>, I>>(base?: I): ListSourceRsp {
    return ListSourceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSourceRsp>, I>>(object: I): ListSourceRsp {
    const message = createBaseListSourceRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.list = object.list?.map(e => Source.fromPartial(e)) || [];
    return message;
  }
};

export type NotificationSourceMgrDefinition = typeof NotificationSourceMgrDefinition;
export const NotificationSourceMgrDefinition = {
  name: 'NotificationSourceMgr',
  fullName: 'comm.mgr.platform.notification.NotificationSourceMgr',
  methods: {
    addSource: {
      name: 'AddSource',
      requestType: AddSourceReq,
      requestStream: false,
      responseType: AddSourceRsp,
      responseStream: false,
      options: {}
    },
    delSource: {
      name: 'DelSource',
      requestType: DelSourceReq,
      requestStream: false,
      responseType: DelSourceRsp,
      responseStream: false,
      options: {}
    },
    updateSource: {
      name: 'UpdateSource',
      requestType: UpdateSourceReq,
      requestStream: false,
      responseType: UpdateSourceRsp,
      responseStream: false,
      options: {}
    },
    getSource: {
      name: 'GetSource',
      requestType: GetSourceReq,
      requestStream: false,
      responseType: GetSourceRsp,
      responseStream: false,
      options: {}
    },
    listSource: {
      name: 'ListSource',
      requestType: ListSourceReq,
      requestStream: false,
      responseType: ListSourceRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
