// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/oplog.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.oplog';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/oplog/handlermgr */

/** 操作人 */
export interface Operator {
  /** 操作人类型, 运营: ADMIN, 用户: USER, 外部人员: OUTSIDER */
  operator_type: string;
  /** 操作人ID */
  operator_id: string;
  /** 操作人名称 */
  operator_name: string;
}

export interface ListOperatorReq {
  /** 目标类型 */
  target_type: string;
}

export interface ListOperatorRsp {
  /** 操作人列表 */
  operators: Operator[];
}

export interface ListOperationReq {
  /** 目标类型 */
  target_type: string;
}

export interface ListOperationRsp {
  /** 操作类型列表 */
  operation: string[];
}

/** 操作记录 */
export interface Oplog {
  /** 业务名 */
  anm: string;
  /** 操作人类型, 运营: ADMIN, 用户: USER, 外部人员: OUTSIDER */
  operator_type: string;
  /** 操作人ID */
  operator_id: string;
  /** 操作人名称 */
  operator_name: string;
  /** 操作人代理信息, 例如浏览器的 User-Agent */
  operator_agent: string;
  /** 操作类型, 由业务与前端自定义, 创建: CREATE, 修改: UPDATE, 移除: DELETE, ... */
  operation: string;
  /** 操作时间, 采用 Unix 时间戳, 精确到秒. */
  operate_at: number;
  /** 操作参数, 通常是请求参数的 JSON 字符串. */
  operate_args: string;
  /** 操作记录版次, 某个操作目标的特定操作已经有存量的操作记录, 后续操作记录进行升级时通过此字段进行区分, 主要是方便前端根据版本来解析操作记录. */
  operate_edition: number;
  /** 操作目标类型, 业务对操作对象的分类, 例如, 用户: user, 主播: anchor, ... */
  target_type: string;
  /** 操作目标ID */
  target_id: string;
  /** 操作目标旧内容 */
  target_old: string;
  /** 操作目标新内容 */
  target_new: string;
  /** 拓展信息, 采用 JSON 格式方便后续拓展. */
  extension: string;
  /** 日志ID, 不保证全局唯一. */
  log_id: string;
}

export interface SearchOplogReq {
  /** 分页 */
  page: Page | undefined;
  /** 目标类型 */
  target_type: string;
  /** 目标ID(可选) */
  target_id: string;
  /** 操作人类型(可选) */
  operator_type: string;
  /** 操作人ID(可选) */
  operator_id: string;
  /** 操作类型(可选) */
  operation: string;
  /** 关键字(可选) */
  keyword: string;
  /** 日志记录开始时间 */
  begin_time: number;
  /** 日志记录结束时间 */
  end_time: number;
}

export interface SearchOplogRsp {
  /** 分页 */
  page: Page | undefined;
  /** 操作记录列表 */
  oplogs: Oplog[];
}

function createBaseOperator(): Operator {
  return { operator_type: '', operator_id: '', operator_name: '' };
}

export const Operator: MessageFns<Operator> = {
  fromJSON(object: any): Operator {
    return {
      operator_type: isSet(object.operator_type) ? globalThis.String(object.operator_type) : '',
      operator_id: isSet(object.operator_id) ? globalThis.String(object.operator_id) : '',
      operator_name: isSet(object.operator_name) ? globalThis.String(object.operator_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<Operator>, I>>(base?: I): Operator {
    return Operator.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Operator>, I>>(object: I): Operator {
    const message = createBaseOperator();
    message.operator_type = object.operator_type ?? '';
    message.operator_id = object.operator_id ?? '';
    message.operator_name = object.operator_name ?? '';
    return message;
  }
};

function createBaseListOperatorReq(): ListOperatorReq {
  return { target_type: '' };
}

export const ListOperatorReq: MessageFns<ListOperatorReq> = {
  fromJSON(object: any): ListOperatorReq {
    return { target_type: isSet(object.target_type) ? globalThis.String(object.target_type) : '' };
  },

  create<I extends Exact<DeepPartial<ListOperatorReq>, I>>(base?: I): ListOperatorReq {
    return ListOperatorReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperatorReq>, I>>(object: I): ListOperatorReq {
    const message = createBaseListOperatorReq();
    message.target_type = object.target_type ?? '';
    return message;
  }
};

function createBaseListOperatorRsp(): ListOperatorRsp {
  return { operators: [] };
}

export const ListOperatorRsp: MessageFns<ListOperatorRsp> = {
  fromJSON(object: any): ListOperatorRsp {
    return {
      operators: globalThis.Array.isArray(object?.operators)
        ? object.operators.map((e: any) => Operator.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListOperatorRsp>, I>>(base?: I): ListOperatorRsp {
    return ListOperatorRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperatorRsp>, I>>(object: I): ListOperatorRsp {
    const message = createBaseListOperatorRsp();
    message.operators = object.operators?.map(e => Operator.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListOperationReq(): ListOperationReq {
  return { target_type: '' };
}

export const ListOperationReq: MessageFns<ListOperationReq> = {
  fromJSON(object: any): ListOperationReq {
    return { target_type: isSet(object.target_type) ? globalThis.String(object.target_type) : '' };
  },

  create<I extends Exact<DeepPartial<ListOperationReq>, I>>(base?: I): ListOperationReq {
    return ListOperationReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperationReq>, I>>(object: I): ListOperationReq {
    const message = createBaseListOperationReq();
    message.target_type = object.target_type ?? '';
    return message;
  }
};

function createBaseListOperationRsp(): ListOperationRsp {
  return { operation: [] };
}

export const ListOperationRsp: MessageFns<ListOperationRsp> = {
  fromJSON(object: any): ListOperationRsp {
    return {
      operation: globalThis.Array.isArray(object?.operation)
        ? object.operation.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListOperationRsp>, I>>(base?: I): ListOperationRsp {
    return ListOperationRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListOperationRsp>, I>>(object: I): ListOperationRsp {
    const message = createBaseListOperationRsp();
    message.operation = object.operation?.map(e => e) || [];
    return message;
  }
};

function createBaseOplog(): Oplog {
  return {
    anm: '',
    operator_type: '',
    operator_id: '',
    operator_name: '',
    operator_agent: '',
    operation: '',
    operate_at: 0,
    operate_args: '',
    operate_edition: 0,
    target_type: '',
    target_id: '',
    target_old: '',
    target_new: '',
    extension: '',
    log_id: ''
  };
}

export const Oplog: MessageFns<Oplog> = {
  fromJSON(object: any): Oplog {
    return {
      anm: isSet(object.anm) ? globalThis.String(object.anm) : '',
      operator_type: isSet(object.operator_type) ? globalThis.String(object.operator_type) : '',
      operator_id: isSet(object.operator_id) ? globalThis.String(object.operator_id) : '',
      operator_name: isSet(object.operator_name) ? globalThis.String(object.operator_name) : '',
      operator_agent: isSet(object.operator_agent) ? globalThis.String(object.operator_agent) : '',
      operation: isSet(object.operation) ? globalThis.String(object.operation) : '',
      operate_at: isSet(object.operate_at) ? globalThis.Number(object.operate_at) : 0,
      operate_args: isSet(object.operate_args) ? globalThis.String(object.operate_args) : '',
      operate_edition: isSet(object.operate_edition) ? globalThis.Number(object.operate_edition) : 0,
      target_type: isSet(object.target_type) ? globalThis.String(object.target_type) : '',
      target_id: isSet(object.target_id) ? globalThis.String(object.target_id) : '',
      target_old: isSet(object.target_old) ? globalThis.String(object.target_old) : '',
      target_new: isSet(object.target_new) ? globalThis.String(object.target_new) : '',
      extension: isSet(object.extension) ? globalThis.String(object.extension) : '',
      log_id: isSet(object.log_id) ? globalThis.String(object.log_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<Oplog>, I>>(base?: I): Oplog {
    return Oplog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Oplog>, I>>(object: I): Oplog {
    const message = createBaseOplog();
    message.anm = object.anm ?? '';
    message.operator_type = object.operator_type ?? '';
    message.operator_id = object.operator_id ?? '';
    message.operator_name = object.operator_name ?? '';
    message.operator_agent = object.operator_agent ?? '';
    message.operation = object.operation ?? '';
    message.operate_at = object.operate_at ?? 0;
    message.operate_args = object.operate_args ?? '';
    message.operate_edition = object.operate_edition ?? 0;
    message.target_type = object.target_type ?? '';
    message.target_id = object.target_id ?? '';
    message.target_old = object.target_old ?? '';
    message.target_new = object.target_new ?? '';
    message.extension = object.extension ?? '';
    message.log_id = object.log_id ?? '';
    return message;
  }
};

function createBaseSearchOplogReq(): SearchOplogReq {
  return {
    page: undefined,
    target_type: '',
    target_id: '',
    operator_type: '',
    operator_id: '',
    operation: '',
    keyword: '',
    begin_time: 0,
    end_time: 0
  };
}

export const SearchOplogReq: MessageFns<SearchOplogReq> = {
  fromJSON(object: any): SearchOplogReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      target_type: isSet(object.target_type) ? globalThis.String(object.target_type) : '',
      target_id: isSet(object.target_id) ? globalThis.String(object.target_id) : '',
      operator_type: isSet(object.operator_type) ? globalThis.String(object.operator_type) : '',
      operator_id: isSet(object.operator_id) ? globalThis.String(object.operator_id) : '',
      operation: isSet(object.operation) ? globalThis.String(object.operation) : '',
      keyword: isSet(object.keyword) ? globalThis.String(object.keyword) : '',
      begin_time: isSet(object.begin_time) ? globalThis.Number(object.begin_time) : 0,
      end_time: isSet(object.end_time) ? globalThis.Number(object.end_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchOplogReq>, I>>(base?: I): SearchOplogReq {
    return SearchOplogReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchOplogReq>, I>>(object: I): SearchOplogReq {
    const message = createBaseSearchOplogReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.target_type = object.target_type ?? '';
    message.target_id = object.target_id ?? '';
    message.operator_type = object.operator_type ?? '';
    message.operator_id = object.operator_id ?? '';
    message.operation = object.operation ?? '';
    message.keyword = object.keyword ?? '';
    message.begin_time = object.begin_time ?? 0;
    message.end_time = object.end_time ?? 0;
    return message;
  }
};

function createBaseSearchOplogRsp(): SearchOplogRsp {
  return { page: undefined, oplogs: [] };
}

export const SearchOplogRsp: MessageFns<SearchOplogRsp> = {
  fromJSON(object: any): SearchOplogRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      oplogs: globalThis.Array.isArray(object?.oplogs) ? object.oplogs.map((e: any) => Oplog.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchOplogRsp>, I>>(base?: I): SearchOplogRsp {
    return SearchOplogRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchOplogRsp>, I>>(object: I): SearchOplogRsp {
    const message = createBaseSearchOplogRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.oplogs = object.oplogs?.map(e => Oplog.fromPartial(e)) || [];
    return message;
  }
};

/** 操作记录 */
export type OplogMgrDefinition = typeof OplogMgrDefinition;
export const OplogMgrDefinition = {
  name: 'OplogMgr',
  fullName: 'comm.mgr.platform.oplog.OplogMgr',
  methods: {
    /** 获取操作人列表 */
    listOperator: {
      name: 'ListOperator',
      requestType: ListOperatorReq,
      requestStream: false,
      responseType: ListOperatorRsp,
      responseStream: false,
      options: {}
    },
    /** 获取操作类型列表 */
    listOperation: {
      name: 'ListOperation',
      requestType: ListOperationReq,
      requestStream: false,
      responseType: ListOperationRsp,
      responseStream: false,
      options: {}
    },
    /** 获取操作记录列表 */
    searchOplog: {
      name: 'SearchOplog',
      requestType: SearchOplogReq,
      requestStream: false,
      responseType: SearchOplogRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
