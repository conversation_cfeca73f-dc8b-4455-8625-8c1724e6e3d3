// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/langs.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.langs';

/** 哪个端的 */
export enum Site {
  SITE_NONE = 0,
  SITE_SERVER = 1,
  SITE_APP = 2,
  UNRECOGNIZED = -1
}

export function siteFromJSON(object: any): Site {
  switch (object) {
    case 0:
    case 'SITE_NONE':
      return Site.SITE_NONE;
    case 1:
    case 'SITE_SERVER':
      return Site.SITE_SERVER;
    case 2:
    case 'SITE_APP':
      return Site.SITE_APP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Site.UNRECOGNIZED;
  }
}

export enum LangType {
  LANG_TYPE_NONE = 0,
  /** LANG_TYPE_KV - 直接 key-value */
  LANG_TYPE_KV = 1,
  /** LANG_TYPE_JSON - json 文件 */
  LANG_TYPE_JSON = 10,
  /** LANG_TYPE_XSL - excel 文件 */
  LANG_TYPE_XSL = 11,
  UNRECOGNIZED = -1
}

export function langTypeFromJSON(object: any): LangType {
  switch (object) {
    case 0:
    case 'LANG_TYPE_NONE':
      return LangType.LANG_TYPE_NONE;
    case 1:
    case 'LANG_TYPE_KV':
      return LangType.LANG_TYPE_KV;
    case 10:
    case 'LANG_TYPE_JSON':
      return LangType.LANG_TYPE_JSON;
    case 11:
    case 'LANG_TYPE_XSL':
      return LangType.LANG_TYPE_XSL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return LangType.UNRECOGNIZED;
  }
}

export interface CreateReq {
  site: Site;
  code: string;
  type: LangType;
  desc: string;
  /** 如果type是 LANG_TYPE_KV 才有意义 */
  kvs: KV[];
  /** 如果type是文件类型才有意义 */
  file_id: string;
}

export interface CreateRsp {
  id: number;
}

export interface UpdateReq {
  /** 更新的值，id必须要带，site不需要 */
  data: Langs | undefined;
}

export interface UpdateRsp {}

export interface DeleteReq {
  /** 不需要site */
  id: number;
}

export interface DeleteRsp {}

export interface ListReq {
  page: Page | undefined;
  site: Site;
  /** 如果是搜索功能 */
  search_text: string;
}

export interface ListRsp {
  page: Page | undefined;
  data: Langs[];
}

export interface GetKVsReq {
  id: number;
}

export interface GetKVsRsp {
  data: KV[];
}

export interface SyncReq {
  id: number;
  /** 文件类型才需要传，lang-->url */
  urls: { [key: string]: string };
}

export interface SyncReq_UrlsEntry {
  key: string;
  value: string;
}

export interface SyncRsp {}

export interface Langs {
  id: number;
  code: string;
  type: LangType;
  /** 说明 */
  desc: string;
  /** 如果type是 LANG_TYPE_KV 才有意义 */
  kvs: KV[];
  /** 如果type是文件类型才有意义 */
  file_id: string;
  creator: string;
  /** 创建时间 unix timestamp, seconds */
  ctime: number;
  modifier: string;
  /** 更新时间，unix timestamp, seconds */
  utime: number;
  /** 同步人 */
  syncer: string;
  /** 同步时间，unix timestamp, seconds */
  stime: number;
}

export interface KV {
  key: string;
  /** lang-->content */
  langs: { [key: string]: string };
}

export interface KV_LangsEntry {
  key: string;
  value: string;
}

function createBaseCreateReq(): CreateReq {
  return { site: 0, code: '', type: 0, desc: '', kvs: [], file_id: '' };
}

export const CreateReq: MessageFns<CreateReq> = {
  fromJSON(object: any): CreateReq {
    return {
      site: isSet(object.site) ? siteFromJSON(object.site) : 0,
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      type: isSet(object.type) ? langTypeFromJSON(object.type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      kvs: globalThis.Array.isArray(object?.kvs) ? object.kvs.map((e: any) => KV.fromJSON(e)) : [],
      file_id: isSet(object.file_id) ? globalThis.String(object.file_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateReq>, I>>(base?: I): CreateReq {
    return CreateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateReq>, I>>(object: I): CreateReq {
    const message = createBaseCreateReq();
    message.site = object.site ?? 0;
    message.code = object.code ?? '';
    message.type = object.type ?? 0;
    message.desc = object.desc ?? '';
    message.kvs = object.kvs?.map(e => KV.fromPartial(e)) || [];
    message.file_id = object.file_id ?? '';
    return message;
  }
};

function createBaseCreateRsp(): CreateRsp {
  return { id: 0 };
}

export const CreateRsp: MessageFns<CreateRsp> = {
  fromJSON(object: any): CreateRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateRsp>, I>>(base?: I): CreateRsp {
    return CreateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateRsp>, I>>(object: I): CreateRsp {
    const message = createBaseCreateRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateReq(): UpdateReq {
  return { data: undefined };
}

export const UpdateReq: MessageFns<UpdateReq> = {
  fromJSON(object: any): UpdateReq {
    return { data: isSet(object.data) ? Langs.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateReq>, I>>(base?: I): UpdateReq {
    return UpdateReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateReq>, I>>(object: I): UpdateReq {
    const message = createBaseUpdateReq();
    message.data = object.data !== undefined && object.data !== null ? Langs.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseUpdateRsp(): UpdateRsp {
  return {};
}

export const UpdateRsp: MessageFns<UpdateRsp> = {
  fromJSON(_: any): UpdateRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateRsp>, I>>(base?: I): UpdateRsp {
    return UpdateRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateRsp>, I>>(_: I): UpdateRsp {
    const message = createBaseUpdateRsp();
    return message;
  }
};

function createBaseDeleteReq(): DeleteReq {
  return { id: 0 };
}

export const DeleteReq: MessageFns<DeleteReq> = {
  fromJSON(object: any): DeleteReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeleteReq>, I>>(base?: I): DeleteReq {
    return DeleteReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteReq>, I>>(object: I): DeleteReq {
    const message = createBaseDeleteReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDeleteRsp(): DeleteRsp {
  return {};
}

export const DeleteRsp: MessageFns<DeleteRsp> = {
  fromJSON(_: any): DeleteRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteRsp>, I>>(base?: I): DeleteRsp {
    return DeleteRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteRsp>, I>>(_: I): DeleteRsp {
    const message = createBaseDeleteRsp();
    return message;
  }
};

function createBaseListReq(): ListReq {
  return { page: undefined, site: 0, search_text: '' };
}

export const ListReq: MessageFns<ListReq> = {
  fromJSON(object: any): ListReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      site: isSet(object.site) ? siteFromJSON(object.site) : 0,
      search_text: isSet(object.search_text) ? globalThis.String(object.search_text) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListReq>, I>>(base?: I): ListReq {
    return ListReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListReq>, I>>(object: I): ListReq {
    const message = createBaseListReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.site = object.site ?? 0;
    message.search_text = object.search_text ?? '';
    return message;
  }
};

function createBaseListRsp(): ListRsp {
  return { page: undefined, data: [] };
}

export const ListRsp: MessageFns<ListRsp> = {
  fromJSON(object: any): ListRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => Langs.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListRsp>, I>>(base?: I): ListRsp {
    return ListRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRsp>, I>>(object: I): ListRsp {
    const message = createBaseListRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => Langs.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetKVsReq(): GetKVsReq {
  return { id: 0 };
}

export const GetKVsReq: MessageFns<GetKVsReq> = {
  fromJSON(object: any): GetKVsReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetKVsReq>, I>>(base?: I): GetKVsReq {
    return GetKVsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetKVsReq>, I>>(object: I): GetKVsReq {
    const message = createBaseGetKVsReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetKVsRsp(): GetKVsRsp {
  return { data: [] };
}

export const GetKVsRsp: MessageFns<GetKVsRsp> = {
  fromJSON(object: any): GetKVsRsp {
    return { data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => KV.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetKVsRsp>, I>>(base?: I): GetKVsRsp {
    return GetKVsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetKVsRsp>, I>>(object: I): GetKVsRsp {
    const message = createBaseGetKVsRsp();
    message.data = object.data?.map(e => KV.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSyncReq(): SyncReq {
  return { id: 0, urls: {} };
}

export const SyncReq: MessageFns<SyncReq> = {
  fromJSON(object: any): SyncReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      urls: isObject(object.urls)
        ? Object.entries(object.urls).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<SyncReq>, I>>(base?: I): SyncReq {
    return SyncReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SyncReq>, I>>(object: I): SyncReq {
    const message = createBaseSyncReq();
    message.id = object.id ?? 0;
    message.urls = Object.entries(object.urls ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseSyncReq_UrlsEntry(): SyncReq_UrlsEntry {
  return { key: '', value: '' };
}

export const SyncReq_UrlsEntry: MessageFns<SyncReq_UrlsEntry> = {
  fromJSON(object: any): SyncReq_UrlsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<SyncReq_UrlsEntry>, I>>(base?: I): SyncReq_UrlsEntry {
    return SyncReq_UrlsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SyncReq_UrlsEntry>, I>>(object: I): SyncReq_UrlsEntry {
    const message = createBaseSyncReq_UrlsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseSyncRsp(): SyncRsp {
  return {};
}

export const SyncRsp: MessageFns<SyncRsp> = {
  fromJSON(_: any): SyncRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<SyncRsp>, I>>(base?: I): SyncRsp {
    return SyncRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SyncRsp>, I>>(_: I): SyncRsp {
    const message = createBaseSyncRsp();
    return message;
  }
};

function createBaseLangs(): Langs {
  return {
    id: 0,
    code: '',
    type: 0,
    desc: '',
    kvs: [],
    file_id: '',
    creator: '',
    ctime: 0,
    modifier: '',
    utime: 0,
    syncer: '',
    stime: 0
  };
}

export const Langs: MessageFns<Langs> = {
  fromJSON(object: any): Langs {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      type: isSet(object.type) ? langTypeFromJSON(object.type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      kvs: globalThis.Array.isArray(object?.kvs) ? object.kvs.map((e: any) => KV.fromJSON(e)) : [],
      file_id: isSet(object.file_id) ? globalThis.String(object.file_id) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      ctime: isSet(object.ctime) ? globalThis.Number(object.ctime) : 0,
      modifier: isSet(object.modifier) ? globalThis.String(object.modifier) : '',
      utime: isSet(object.utime) ? globalThis.Number(object.utime) : 0,
      syncer: isSet(object.syncer) ? globalThis.String(object.syncer) : '',
      stime: isSet(object.stime) ? globalThis.Number(object.stime) : 0
    };
  },

  create<I extends Exact<DeepPartial<Langs>, I>>(base?: I): Langs {
    return Langs.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Langs>, I>>(object: I): Langs {
    const message = createBaseLangs();
    message.id = object.id ?? 0;
    message.code = object.code ?? '';
    message.type = object.type ?? 0;
    message.desc = object.desc ?? '';
    message.kvs = object.kvs?.map(e => KV.fromPartial(e)) || [];
    message.file_id = object.file_id ?? '';
    message.creator = object.creator ?? '';
    message.ctime = object.ctime ?? 0;
    message.modifier = object.modifier ?? '';
    message.utime = object.utime ?? 0;
    message.syncer = object.syncer ?? '';
    message.stime = object.stime ?? 0;
    return message;
  }
};

function createBaseKV(): KV {
  return { key: '', langs: {} };
}

export const KV: MessageFns<KV> = {
  fromJSON(object: any): KV {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      langs: isObject(object.langs)
        ? Object.entries(object.langs).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<KV>, I>>(base?: I): KV {
    return KV.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KV>, I>>(object: I): KV {
    const message = createBaseKV();
    message.key = object.key ?? '';
    message.langs = Object.entries(object.langs ?? {}).reduce<{ [key: string]: string }>((acc, [key, value]) => {
      if (value !== undefined) {
        acc[key] = globalThis.String(value);
      }
      return acc;
    }, {});
    return message;
  }
};

function createBaseKV_LangsEntry(): KV_LangsEntry {
  return { key: '', value: '' };
}

export const KV_LangsEntry: MessageFns<KV_LangsEntry> = {
  fromJSON(object: any): KV_LangsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<KV_LangsEntry>, I>>(base?: I): KV_LangsEntry {
    return KV_LangsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<KV_LangsEntry>, I>>(object: I): KV_LangsEntry {
    const message = createBaseKV_LangsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

export type LangsMgrDefinition = typeof LangsMgrDefinition;
export const LangsMgrDefinition = {
  name: 'LangsMgr',
  fullName: 'comm.mgr.platform.langs.LangsMgr',
  methods: {
    /** 创建一个多语言文案 */
    create: {
      name: 'Create',
      requestType: CreateReq,
      requestStream: false,
      responseType: CreateRsp,
      responseStream: false,
      options: {}
    },
    /** 更新多语言文案 */
    update: {
      name: 'Update',
      requestType: UpdateReq,
      requestStream: false,
      responseType: UpdateRsp,
      responseStream: false,
      options: {}
    },
    /** 删除多语言文案 */
    delete: {
      name: 'Delete',
      requestType: DeleteReq,
      requestStream: false,
      responseType: DeleteRsp,
      responseStream: false,
      options: {}
    },
    /** 列出多语言文案，不返回kvs */
    list: {
      name: 'List',
      requestType: ListReq,
      requestStream: false,
      responseType: ListRsp,
      responseStream: false,
      options: {}
    },
    /** 获取kvs */
    getKVs: {
      name: 'GetKVs',
      requestType: GetKVsReq,
      requestStream: false,
      responseType: GetKVsRsp,
      responseStream: false,
      options: {}
    },
    /** 同步多语言文案 */
    sync: {
      name: 'Sync',
      requestType: SyncReq,
      requestStream: false,
      responseType: SyncRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
