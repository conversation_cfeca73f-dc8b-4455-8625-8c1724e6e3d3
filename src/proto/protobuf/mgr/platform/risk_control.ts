// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/risk_control.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.riskcontrol';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/riskcontrol/handlermgr */

/** 风控审核类别 */
export enum RiskControlType {
  /** RISKCONTROL_TYPE_NONE - none */
  RISKCONTROL_TYPE_NONE = 0,
  /** RISKCONTROL_TYPE_PICTURE - 图片 */
  RISKCONTROL_TYPE_PICTURE = 10,
  /** RISKCONTROL_TYPE_TEXT - 文字 */
  RISKCONTROL_TYPE_TEXT = 20,
  /** RISKCONTROL_TYPE_VIDEO - 视频 */
  RISKCONTROL_TYPE_VIDEO = 30,
  /** RISKCONTROL_TYPE_MIXED - 混合内容类型, 即同时有文本/图片/视频, 例如用户发布的动态. 所以 */
  RISKCONTROL_TYPE_MIXED = 999,
  UNRECOGNIZED = -1
}

export function riskControlTypeFromJSON(object: any): RiskControlType {
  switch (object) {
    case 0:
    case 'RISKCONTROL_TYPE_NONE':
      return RiskControlType.RISKCONTROL_TYPE_NONE;
    case 10:
    case 'RISKCONTROL_TYPE_PICTURE':
      return RiskControlType.RISKCONTROL_TYPE_PICTURE;
    case 20:
    case 'RISKCONTROL_TYPE_TEXT':
      return RiskControlType.RISKCONTROL_TYPE_TEXT;
    case 30:
    case 'RISKCONTROL_TYPE_VIDEO':
      return RiskControlType.RISKCONTROL_TYPE_VIDEO;
    case 999:
    case 'RISKCONTROL_TYPE_MIXED':
      return RiskControlType.RISKCONTROL_TYPE_MIXED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RiskControlType.UNRECOGNIZED;
  }
}

/** 风控结果 */
export enum RiskControlResult {
  /** RISKCONTROL_RESULT_NONE - none */
  RISKCONTROL_RESULT_NONE = 0,
  /** RISKCONTROL_RESULT_PASS - 通过 */
  RISKCONTROL_RESULT_PASS = 10,
  /** RISKCONTROL_RESULT_HOLD - 不通过 */
  RISKCONTROL_RESULT_HOLD = 20,
  /** RISKCONTROL_RESULT_DOUBT - 存疑,已丢弃 */
  RISKCONTROL_RESULT_DOUBT = 30,
  /** RISKCONTROL_RESULT_FAIL - 失败 */
  RISKCONTROL_RESULT_FAIL = 40,
  UNRECOGNIZED = -1
}

export function riskControlResultFromJSON(object: any): RiskControlResult {
  switch (object) {
    case 0:
    case 'RISKCONTROL_RESULT_NONE':
      return RiskControlResult.RISKCONTROL_RESULT_NONE;
    case 10:
    case 'RISKCONTROL_RESULT_PASS':
      return RiskControlResult.RISKCONTROL_RESULT_PASS;
    case 20:
    case 'RISKCONTROL_RESULT_HOLD':
      return RiskControlResult.RISKCONTROL_RESULT_HOLD;
    case 30:
    case 'RISKCONTROL_RESULT_DOUBT':
      return RiskControlResult.RISKCONTROL_RESULT_DOUBT;
    case 40:
    case 'RISKCONTROL_RESULT_FAIL':
      return RiskControlResult.RISKCONTROL_RESULT_FAIL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RiskControlResult.UNRECOGNIZED;
  }
}

export enum SearchFieldOperator {
  SEARCH_FIELD_OPERATOR_NONE = 0,
  /** SEARCH_FIELD_OPERATOR_EQ - == */
  SEARCH_FIELD_OPERATOR_EQ = 1,
  /** SEARCH_FIELD_OPERATOR_NEQ - != */
  SEARCH_FIELD_OPERATOR_NEQ = 2,
  /** SEARCH_FIELD_OPERATOR_LIKE - like %v0% */
  SEARCH_FIELD_OPERATOR_LIKE = 3,
  /** SEARCH_FIELD_OPERATOR_IN - in (v0,v1,v2...) */
  SEARCH_FIELD_OPERATOR_IN = 4,
  /** SEARCH_FIELD_OPERATOR_GT - > v0 */
  SEARCH_FIELD_OPERATOR_GT = 5,
  /** SEARCH_FIELD_OPERATOR_GTE - >= v0 */
  SEARCH_FIELD_OPERATOR_GTE = 6,
  /** SEARCH_FIELD_OPERATOR_LT - < v0 */
  SEARCH_FIELD_OPERATOR_LT = 7,
  /** SEARCH_FIELD_OPERATOR_LTE - <= v0 */
  SEARCH_FIELD_OPERATOR_LTE = 8,
  UNRECOGNIZED = -1
}

export function searchFieldOperatorFromJSON(object: any): SearchFieldOperator {
  switch (object) {
    case 0:
    case 'SEARCH_FIELD_OPERATOR_NONE':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_NONE;
    case 1:
    case 'SEARCH_FIELD_OPERATOR_EQ':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_EQ;
    case 2:
    case 'SEARCH_FIELD_OPERATOR_NEQ':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_NEQ;
    case 3:
    case 'SEARCH_FIELD_OPERATOR_LIKE':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_LIKE;
    case 4:
    case 'SEARCH_FIELD_OPERATOR_IN':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_IN;
    case 5:
    case 'SEARCH_FIELD_OPERATOR_GT':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_GT;
    case 6:
    case 'SEARCH_FIELD_OPERATOR_GTE':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_GTE;
    case 7:
    case 'SEARCH_FIELD_OPERATOR_LT':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_LT;
    case 8:
    case 'SEARCH_FIELD_OPERATOR_LTE':
      return SearchFieldOperator.SEARCH_FIELD_OPERATOR_LTE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return SearchFieldOperator.UNRECOGNIZED;
  }
}

/** 风控审核详细结果 */
export interface RiskControlAuditResult {
  /** 审核结果 */
  audit_result: RiskControlResult;
  /** 审核原因 */
  audit_reason: string;
  /** 审核时间戳 unix */
  audit_at: number;
  /** 审核人名字 */
  audit_name: string;
  /** 审核人id */
  audit_id: string;
}

/** 风控内容 */
export interface RiskControl {
  /** 唯一自增 */
  id: number;
  /** 大类，由业务后端定义 */
  category: string;
  /** 风险类型，由业务后端定义 */
  risk_type: string;
  /** 改变内容，如果内容类型为图片则为url */
  content: string;
  /** 内容类型 */
  content_type: RiskControlType;
  /** 改变内容关联的id，如：房间id，uid */
  relation_id: number;
  /** 前端展示内容，json串 */
  frontend_data: string;
  /** 备注 */
  remark: string;
  /** 提交人 */
  submit_by: string;
  /** 提交人id */
  submit_uid: number;
  /** 提交的时间戳 unix */
  submit_at: number;
  /** 文本检测出来的级别 */
  text_check_level: string;
  /** 图片检测打分,如果没有检测出来，则为空 */
  picture_check_rate: string;
  /** 人工审核时打标签 */
  label: string;
  /** 机审结果 */
  auto_audit_result: RiskControlAuditResult | undefined;
  /** 人工审核结果 */
  manual_audit_result: RiskControlAuditResult | undefined;
}

export interface SearchField {
  /** 字段 */
  field: string;
  /** 操作符 */
  operator: SearchFieldOperator;
  /** 值，基于操作符来决定如何使用 */
  values: string[];
}

export interface SearchRiskControlMgrReq {
  page: Page | undefined;
  /** 大类，由业务后端定义 */
  category: string;
  /** 风险类型，由业务后端定义 */
  risk_type: string;
  /** 关联id */
  relation_id: number;
  /** 提交时间戳开始 unix */
  submit_at_start: number;
  /** 提交时间戳结束 unix */
  submit_at_end: number;
  /** 机审结果 */
  auto_audit_result: RiskControlResult;
  /** 人审结果 */
  manual_audit_result: RiskControlResult;
  /** 人审时间戳开始 unix */
  manual_audit_at_start: number;
  /** 人审时间戳结束 unix */
  manual_audit_at_end: number;
  /** 人审结果数组查询：待定（RISKCONTROL_RESULT_NONE）/通过/不通过 */
  manual_results: RiskControlResult[];
  /** 人审结果原因 */
  manual_audit_reason: string;
  /** 标签查询 */
  label: string;
  /** 业务搜索字段，每个出现的字段用 and 来进行合并搜索 */
  frontend_data: SearchField[];
}

export interface SearchRiskControlMgrRsp {
  page: Page | undefined;
  data: RiskControl[];
}

export interface MannualAuditReq {
  id: number;
  /** 人工审核结果 */
  manual_audit_result: RiskControlResult;
  /** 人工审核原因 */
  manual_audit_reason: string;
  /** 人工审核时打标签 */
  label: string;
}

export interface MannualAuditRsp {}

export interface ListAuditLabel {
  id: number;
  /** 标签 */
  label: string;
}

export interface ListAuditLabelsReq {}

export interface ListAuditLabelsRsp {
  /** 标签列表 */
  labels: ListAuditLabel[];
}

function createBaseRiskControlAuditResult(): RiskControlAuditResult {
  return { audit_result: 0, audit_reason: '', audit_at: 0, audit_name: '', audit_id: '' };
}

export const RiskControlAuditResult: MessageFns<RiskControlAuditResult> = {
  fromJSON(object: any): RiskControlAuditResult {
    return {
      audit_result: isSet(object.audit_result) ? riskControlResultFromJSON(object.audit_result) : 0,
      audit_reason: isSet(object.audit_reason) ? globalThis.String(object.audit_reason) : '',
      audit_at: isSet(object.audit_at) ? globalThis.Number(object.audit_at) : 0,
      audit_name: isSet(object.audit_name) ? globalThis.String(object.audit_name) : '',
      audit_id: isSet(object.audit_id) ? globalThis.String(object.audit_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<RiskControlAuditResult>, I>>(base?: I): RiskControlAuditResult {
    return RiskControlAuditResult.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RiskControlAuditResult>, I>>(object: I): RiskControlAuditResult {
    const message = createBaseRiskControlAuditResult();
    message.audit_result = object.audit_result ?? 0;
    message.audit_reason = object.audit_reason ?? '';
    message.audit_at = object.audit_at ?? 0;
    message.audit_name = object.audit_name ?? '';
    message.audit_id = object.audit_id ?? '';
    return message;
  }
};

function createBaseRiskControl(): RiskControl {
  return {
    id: 0,
    category: '',
    risk_type: '',
    content: '',
    content_type: 0,
    relation_id: 0,
    frontend_data: '',
    remark: '',
    submit_by: '',
    submit_uid: 0,
    submit_at: 0,
    text_check_level: '',
    picture_check_rate: '',
    label: '',
    auto_audit_result: undefined,
    manual_audit_result: undefined
  };
}

export const RiskControl: MessageFns<RiskControl> = {
  fromJSON(object: any): RiskControl {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      risk_type: isSet(object.risk_type) ? globalThis.String(object.risk_type) : '',
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      content_type: isSet(object.content_type) ? riskControlTypeFromJSON(object.content_type) : 0,
      relation_id: isSet(object.relation_id) ? globalThis.Number(object.relation_id) : 0,
      frontend_data: isSet(object.frontend_data) ? globalThis.String(object.frontend_data) : '',
      remark: isSet(object.remark) ? globalThis.String(object.remark) : '',
      submit_by: isSet(object.submit_by) ? globalThis.String(object.submit_by) : '',
      submit_uid: isSet(object.submit_uid) ? globalThis.Number(object.submit_uid) : 0,
      submit_at: isSet(object.submit_at) ? globalThis.Number(object.submit_at) : 0,
      text_check_level: isSet(object.text_check_level) ? globalThis.String(object.text_check_level) : '',
      picture_check_rate: isSet(object.picture_check_rate) ? globalThis.String(object.picture_check_rate) : '',
      label: isSet(object.label) ? globalThis.String(object.label) : '',
      auto_audit_result: isSet(object.auto_audit_result)
        ? RiskControlAuditResult.fromJSON(object.auto_audit_result)
        : undefined,
      manual_audit_result: isSet(object.manual_audit_result)
        ? RiskControlAuditResult.fromJSON(object.manual_audit_result)
        : undefined
    };
  },

  create<I extends Exact<DeepPartial<RiskControl>, I>>(base?: I): RiskControl {
    return RiskControl.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RiskControl>, I>>(object: I): RiskControl {
    const message = createBaseRiskControl();
    message.id = object.id ?? 0;
    message.category = object.category ?? '';
    message.risk_type = object.risk_type ?? '';
    message.content = object.content ?? '';
    message.content_type = object.content_type ?? 0;
    message.relation_id = object.relation_id ?? 0;
    message.frontend_data = object.frontend_data ?? '';
    message.remark = object.remark ?? '';
    message.submit_by = object.submit_by ?? '';
    message.submit_uid = object.submit_uid ?? 0;
    message.submit_at = object.submit_at ?? 0;
    message.text_check_level = object.text_check_level ?? '';
    message.picture_check_rate = object.picture_check_rate ?? '';
    message.label = object.label ?? '';
    message.auto_audit_result =
      object.auto_audit_result !== undefined && object.auto_audit_result !== null
        ? RiskControlAuditResult.fromPartial(object.auto_audit_result)
        : undefined;
    message.manual_audit_result =
      object.manual_audit_result !== undefined && object.manual_audit_result !== null
        ? RiskControlAuditResult.fromPartial(object.manual_audit_result)
        : undefined;
    return message;
  }
};

function createBaseSearchField(): SearchField {
  return { field: '', operator: 0, values: [] };
}

export const SearchField: MessageFns<SearchField> = {
  fromJSON(object: any): SearchField {
    return {
      field: isSet(object.field) ? globalThis.String(object.field) : '',
      operator: isSet(object.operator) ? searchFieldOperatorFromJSON(object.operator) : 0,
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchField>, I>>(base?: I): SearchField {
    return SearchField.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchField>, I>>(object: I): SearchField {
    const message = createBaseSearchField();
    message.field = object.field ?? '';
    message.operator = object.operator ?? 0;
    message.values = object.values?.map(e => e) || [];
    return message;
  }
};

function createBaseSearchRiskControlMgrReq(): SearchRiskControlMgrReq {
  return {
    page: undefined,
    category: '',
    risk_type: '',
    relation_id: 0,
    submit_at_start: 0,
    submit_at_end: 0,
    auto_audit_result: 0,
    manual_audit_result: 0,
    manual_audit_at_start: 0,
    manual_audit_at_end: 0,
    manual_results: [],
    manual_audit_reason: '',
    label: '',
    frontend_data: []
  };
}

export const SearchRiskControlMgrReq: MessageFns<SearchRiskControlMgrReq> = {
  fromJSON(object: any): SearchRiskControlMgrReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      category: isSet(object.category) ? globalThis.String(object.category) : '',
      risk_type: isSet(object.risk_type) ? globalThis.String(object.risk_type) : '',
      relation_id: isSet(object.relation_id) ? globalThis.Number(object.relation_id) : 0,
      submit_at_start: isSet(object.submit_at_start) ? globalThis.Number(object.submit_at_start) : 0,
      submit_at_end: isSet(object.submit_at_end) ? globalThis.Number(object.submit_at_end) : 0,
      auto_audit_result: isSet(object.auto_audit_result) ? riskControlResultFromJSON(object.auto_audit_result) : 0,
      manual_audit_result: isSet(object.manual_audit_result)
        ? riskControlResultFromJSON(object.manual_audit_result)
        : 0,
      manual_audit_at_start: isSet(object.manual_audit_at_start) ? globalThis.Number(object.manual_audit_at_start) : 0,
      manual_audit_at_end: isSet(object.manual_audit_at_end) ? globalThis.Number(object.manual_audit_at_end) : 0,
      manual_results: globalThis.Array.isArray(object?.manual_results)
        ? object.manual_results.map((e: any) => riskControlResultFromJSON(e))
        : [],
      manual_audit_reason: isSet(object.manual_audit_reason) ? globalThis.String(object.manual_audit_reason) : '',
      label: isSet(object.label) ? globalThis.String(object.label) : '',
      frontend_data: globalThis.Array.isArray(object?.frontend_data)
        ? object.frontend_data.map((e: any) => SearchField.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRiskControlMgrReq>, I>>(base?: I): SearchRiskControlMgrReq {
    return SearchRiskControlMgrReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRiskControlMgrReq>, I>>(object: I): SearchRiskControlMgrReq {
    const message = createBaseSearchRiskControlMgrReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.category = object.category ?? '';
    message.risk_type = object.risk_type ?? '';
    message.relation_id = object.relation_id ?? 0;
    message.submit_at_start = object.submit_at_start ?? 0;
    message.submit_at_end = object.submit_at_end ?? 0;
    message.auto_audit_result = object.auto_audit_result ?? 0;
    message.manual_audit_result = object.manual_audit_result ?? 0;
    message.manual_audit_at_start = object.manual_audit_at_start ?? 0;
    message.manual_audit_at_end = object.manual_audit_at_end ?? 0;
    message.manual_results = object.manual_results?.map(e => e) || [];
    message.manual_audit_reason = object.manual_audit_reason ?? '';
    message.label = object.label ?? '';
    message.frontend_data = object.frontend_data?.map(e => SearchField.fromPartial(e)) || [];
    return message;
  }
};

function createBaseSearchRiskControlMgrRsp(): SearchRiskControlMgrRsp {
  return { page: undefined, data: [] };
}

export const SearchRiskControlMgrRsp: MessageFns<SearchRiskControlMgrRsp> = {
  fromJSON(object: any): SearchRiskControlMgrRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => RiskControl.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchRiskControlMgrRsp>, I>>(base?: I): SearchRiskControlMgrRsp {
    return SearchRiskControlMgrRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchRiskControlMgrRsp>, I>>(object: I): SearchRiskControlMgrRsp {
    const message = createBaseSearchRiskControlMgrRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => RiskControl.fromPartial(e)) || [];
    return message;
  }
};

function createBaseMannualAuditReq(): MannualAuditReq {
  return { id: 0, manual_audit_result: 0, manual_audit_reason: '', label: '' };
}

export const MannualAuditReq: MessageFns<MannualAuditReq> = {
  fromJSON(object: any): MannualAuditReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      manual_audit_result: isSet(object.manual_audit_result)
        ? riskControlResultFromJSON(object.manual_audit_result)
        : 0,
      manual_audit_reason: isSet(object.manual_audit_reason) ? globalThis.String(object.manual_audit_reason) : '',
      label: isSet(object.label) ? globalThis.String(object.label) : ''
    };
  },

  create<I extends Exact<DeepPartial<MannualAuditReq>, I>>(base?: I): MannualAuditReq {
    return MannualAuditReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MannualAuditReq>, I>>(object: I): MannualAuditReq {
    const message = createBaseMannualAuditReq();
    message.id = object.id ?? 0;
    message.manual_audit_result = object.manual_audit_result ?? 0;
    message.manual_audit_reason = object.manual_audit_reason ?? '';
    message.label = object.label ?? '';
    return message;
  }
};

function createBaseMannualAuditRsp(): MannualAuditRsp {
  return {};
}

export const MannualAuditRsp: MessageFns<MannualAuditRsp> = {
  fromJSON(_: any): MannualAuditRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<MannualAuditRsp>, I>>(base?: I): MannualAuditRsp {
    return MannualAuditRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MannualAuditRsp>, I>>(_: I): MannualAuditRsp {
    const message = createBaseMannualAuditRsp();
    return message;
  }
};

function createBaseListAuditLabel(): ListAuditLabel {
  return { id: 0, label: '' };
}

export const ListAuditLabel: MessageFns<ListAuditLabel> = {
  fromJSON(object: any): ListAuditLabel {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      label: isSet(object.label) ? globalThis.String(object.label) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAuditLabel>, I>>(base?: I): ListAuditLabel {
    return ListAuditLabel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditLabel>, I>>(object: I): ListAuditLabel {
    const message = createBaseListAuditLabel();
    message.id = object.id ?? 0;
    message.label = object.label ?? '';
    return message;
  }
};

function createBaseListAuditLabelsReq(): ListAuditLabelsReq {
  return {};
}

export const ListAuditLabelsReq: MessageFns<ListAuditLabelsReq> = {
  fromJSON(_: any): ListAuditLabelsReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListAuditLabelsReq>, I>>(base?: I): ListAuditLabelsReq {
    return ListAuditLabelsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditLabelsReq>, I>>(_: I): ListAuditLabelsReq {
    const message = createBaseListAuditLabelsReq();
    return message;
  }
};

function createBaseListAuditLabelsRsp(): ListAuditLabelsRsp {
  return { labels: [] };
}

export const ListAuditLabelsRsp: MessageFns<ListAuditLabelsRsp> = {
  fromJSON(object: any): ListAuditLabelsRsp {
    return {
      labels: globalThis.Array.isArray(object?.labels) ? object.labels.map((e: any) => ListAuditLabel.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAuditLabelsRsp>, I>>(base?: I): ListAuditLabelsRsp {
    return ListAuditLabelsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditLabelsRsp>, I>>(object: I): ListAuditLabelsRsp {
    const message = createBaseListAuditLabelsRsp();
    message.labels = object.labels?.map(e => ListAuditLabel.fromPartial(e)) || [];
    return message;
  }
};

export type RiskControlMgrDefinition = typeof RiskControlMgrDefinition;
export const RiskControlMgrDefinition = {
  name: 'RiskControlMgr',
  fullName: 'comm.mgr.platform.riskcontrol.RiskControlMgr',
  methods: {
    /** 风控查询 */
    search: {
      name: 'Search',
      requestType: SearchRiskControlMgrReq,
      requestStream: false,
      responseType: SearchRiskControlMgrRsp,
      responseStream: false,
      options: {}
    },
    /** 人工审核 */
    mannualAudit: {
      name: 'MannualAudit',
      requestType: MannualAuditReq,
      requestStream: false,
      responseType: MannualAuditRsp,
      responseStream: false,
      options: {}
    },
    /** 审核标签查询 */
    listAuditLabels: {
      name: 'ListAuditLabels',
      requestType: ListAuditLabelsReq,
      requestStream: false,
      responseType: ListAuditLabelsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
