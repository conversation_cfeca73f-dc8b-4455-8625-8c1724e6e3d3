// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/lowcode.proto

/* eslint-disable */
import { Page as Page1 } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.platform.lowcode';

export interface CreatePageReq {
  /** 所属工程 */
  project: string;
  /** 页面路由 */
  route: string;
  /** 默认名称 */
  name: string;
  /** 国际化名称 */
  i18n_name: { [key: string]: string };
  /** 拓展信息 */
  ext: string;
  /** 元数据 */
  schema: string;
}

export interface CreatePageReq_I18nNameEntry {
  key: string;
  value: string;
}

export interface CreatePageRsp {
  /** 页面ID */
  page_id: number;
  /** 页面代号 */
  page_code: string;
}

export interface UpdatePageReq {
  /** 页面ID */
  page_id: number;
  /** 页面路由 */
  route: string;
  /** 默认名称 */
  name: string;
  /** 国际化名称 */
  i18n_name: { [key: string]: string };
  /** 拓展信息 */
  ext: string;
  /** 原信息 */
  schema: string;
}

export interface UpdatePageReq_I18nNameEntry {
  key: string;
  value: string;
}

export interface UpdatePageRsp {
  /** 页面的新ID, 更新操作实际上是将旧页面标记为删除再查询新页面, 方便后续回滚. */
  new_page_id: number;
  /** 页面代号 */
  page_code: string;
}

export interface DeletePageReq {
  page_id: number;
}

export interface DeletePageRsp {}

export interface Page {
  /** 页面ID */
  page_id: number;
  /** 页面代号 */
  page_code: string;
  /** 页面路由 */
  route: string;
  /** 默认名称 */
  name: string;
  /** 国际化名称 */
  i18n_name: { [key: string]: string };
  /** 拓展信息 */
  ext: string;
  /** 原信息 */
  schema: string;
  /** 创建时间 */
  created_at: number;
  /** 创建人 */
  created_by: string;
  /** 删除时间, 若此值 > 0 则表示已删除, 页面更新并不会直接更新原记录而是将原记录标记为已删除再插入新纪录. */
  deleted_at: number;
  /** 删除人 */
  deleted_by: string;
}

export interface Page_I18nNameEntry {
  key: string;
  value: string;
}

export interface GetPageByIdReq {
  /** 页面ID */
  page_id: number;
}

export interface GetPageByIdRsp {
  /** 页面 */
  page: Page | undefined;
}

export interface GetEffectivePageReq {
  /** 页面代号 */
  page_code: string;
}

export interface GetEffectivePageRsp {
  /** 页面 */
  page: Page | undefined;
}

export interface ListPageReq {
  /** 分页 */
  page: Page1 | undefined;
  /** 所属工程 */
  project: string;
  /** 页面路由 */
  route: string;
  /** 页面名称 */
  name: string;
}

export interface ListPageRsp {
  /** 分页 */
  page: Page1 | undefined;
  /** 页面 */
  pages: Page[];
}

function createBaseCreatePageReq(): CreatePageReq {
  return { project: '', route: '', name: '', i18n_name: {}, ext: '', schema: '' };
}

export const CreatePageReq: MessageFns<CreatePageReq> = {
  fromJSON(object: any): CreatePageReq {
    return {
      project: isSet(object.project) ? globalThis.String(object.project) : '',
      route: isSet(object.route) ? globalThis.String(object.route) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_name: isObject(object.i18n_name)
        ? Object.entries(object.i18n_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      schema: isSet(object.schema) ? globalThis.String(object.schema) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreatePageReq>, I>>(base?: I): CreatePageReq {
    return CreatePageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePageReq>, I>>(object: I): CreatePageReq {
    const message = createBaseCreatePageReq();
    message.project = object.project ?? '';
    message.route = object.route ?? '';
    message.name = object.name ?? '';
    message.i18n_name = Object.entries(object.i18n_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.ext = object.ext ?? '';
    message.schema = object.schema ?? '';
    return message;
  }
};

function createBaseCreatePageReq_I18nNameEntry(): CreatePageReq_I18nNameEntry {
  return { key: '', value: '' };
}

export const CreatePageReq_I18nNameEntry: MessageFns<CreatePageReq_I18nNameEntry> = {
  fromJSON(object: any): CreatePageReq_I18nNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreatePageReq_I18nNameEntry>, I>>(base?: I): CreatePageReq_I18nNameEntry {
    return CreatePageReq_I18nNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePageReq_I18nNameEntry>, I>>(object: I): CreatePageReq_I18nNameEntry {
    const message = createBaseCreatePageReq_I18nNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseCreatePageRsp(): CreatePageRsp {
  return { page_id: 0, page_code: '' };
}

export const CreatePageRsp: MessageFns<CreatePageRsp> = {
  fromJSON(object: any): CreatePageRsp {
    return {
      page_id: isSet(object.page_id) ? globalThis.Number(object.page_id) : 0,
      page_code: isSet(object.page_code) ? globalThis.String(object.page_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreatePageRsp>, I>>(base?: I): CreatePageRsp {
    return CreatePageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreatePageRsp>, I>>(object: I): CreatePageRsp {
    const message = createBaseCreatePageRsp();
    message.page_id = object.page_id ?? 0;
    message.page_code = object.page_code ?? '';
    return message;
  }
};

function createBaseUpdatePageReq(): UpdatePageReq {
  return { page_id: 0, route: '', name: '', i18n_name: {}, ext: '', schema: '' };
}

export const UpdatePageReq: MessageFns<UpdatePageReq> = {
  fromJSON(object: any): UpdatePageReq {
    return {
      page_id: isSet(object.page_id) ? globalThis.Number(object.page_id) : 0,
      route: isSet(object.route) ? globalThis.String(object.route) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_name: isObject(object.i18n_name)
        ? Object.entries(object.i18n_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      schema: isSet(object.schema) ? globalThis.String(object.schema) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdatePageReq>, I>>(base?: I): UpdatePageReq {
    return UpdatePageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePageReq>, I>>(object: I): UpdatePageReq {
    const message = createBaseUpdatePageReq();
    message.page_id = object.page_id ?? 0;
    message.route = object.route ?? '';
    message.name = object.name ?? '';
    message.i18n_name = Object.entries(object.i18n_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.ext = object.ext ?? '';
    message.schema = object.schema ?? '';
    return message;
  }
};

function createBaseUpdatePageReq_I18nNameEntry(): UpdatePageReq_I18nNameEntry {
  return { key: '', value: '' };
}

export const UpdatePageReq_I18nNameEntry: MessageFns<UpdatePageReq_I18nNameEntry> = {
  fromJSON(object: any): UpdatePageReq_I18nNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdatePageReq_I18nNameEntry>, I>>(base?: I): UpdatePageReq_I18nNameEntry {
    return UpdatePageReq_I18nNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePageReq_I18nNameEntry>, I>>(object: I): UpdatePageReq_I18nNameEntry {
    const message = createBaseUpdatePageReq_I18nNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseUpdatePageRsp(): UpdatePageRsp {
  return { new_page_id: 0, page_code: '' };
}

export const UpdatePageRsp: MessageFns<UpdatePageRsp> = {
  fromJSON(object: any): UpdatePageRsp {
    return {
      new_page_id: isSet(object.new_page_id) ? globalThis.Number(object.new_page_id) : 0,
      page_code: isSet(object.page_code) ? globalThis.String(object.page_code) : ''
    };
  },

  create<I extends Exact<DeepPartial<UpdatePageRsp>, I>>(base?: I): UpdatePageRsp {
    return UpdatePageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdatePageRsp>, I>>(object: I): UpdatePageRsp {
    const message = createBaseUpdatePageRsp();
    message.new_page_id = object.new_page_id ?? 0;
    message.page_code = object.page_code ?? '';
    return message;
  }
};

function createBaseDeletePageReq(): DeletePageReq {
  return { page_id: 0 };
}

export const DeletePageReq: MessageFns<DeletePageReq> = {
  fromJSON(object: any): DeletePageReq {
    return { page_id: isSet(object.page_id) ? globalThis.Number(object.page_id) : 0 };
  },

  create<I extends Exact<DeepPartial<DeletePageReq>, I>>(base?: I): DeletePageReq {
    return DeletePageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePageReq>, I>>(object: I): DeletePageReq {
    const message = createBaseDeletePageReq();
    message.page_id = object.page_id ?? 0;
    return message;
  }
};

function createBaseDeletePageRsp(): DeletePageRsp {
  return {};
}

export const DeletePageRsp: MessageFns<DeletePageRsp> = {
  fromJSON(_: any): DeletePageRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeletePageRsp>, I>>(base?: I): DeletePageRsp {
    return DeletePageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeletePageRsp>, I>>(_: I): DeletePageRsp {
    const message = createBaseDeletePageRsp();
    return message;
  }
};

function createBasePage(): Page {
  return {
    page_id: 0,
    page_code: '',
    route: '',
    name: '',
    i18n_name: {},
    ext: '',
    schema: '',
    created_at: 0,
    created_by: '',
    deleted_at: 0,
    deleted_by: ''
  };
}

export const Page: MessageFns<Page> = {
  fromJSON(object: any): Page {
    return {
      page_id: isSet(object.page_id) ? globalThis.Number(object.page_id) : 0,
      page_code: isSet(object.page_code) ? globalThis.String(object.page_code) : '',
      route: isSet(object.route) ? globalThis.String(object.route) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      i18n_name: isObject(object.i18n_name)
        ? Object.entries(object.i18n_name).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {},
      ext: isSet(object.ext) ? globalThis.String(object.ext) : '',
      schema: isSet(object.schema) ? globalThis.String(object.schema) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      created_by: isSet(object.created_by) ? globalThis.String(object.created_by) : '',
      deleted_at: isSet(object.deleted_at) ? globalThis.Number(object.deleted_at) : 0,
      deleted_by: isSet(object.deleted_by) ? globalThis.String(object.deleted_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<Page>, I>>(base?: I): Page {
    return Page.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Page>, I>>(object: I): Page {
    const message = createBasePage();
    message.page_id = object.page_id ?? 0;
    message.page_code = object.page_code ?? '';
    message.route = object.route ?? '';
    message.name = object.name ?? '';
    message.i18n_name = Object.entries(object.i18n_name ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    message.ext = object.ext ?? '';
    message.schema = object.schema ?? '';
    message.created_at = object.created_at ?? 0;
    message.created_by = object.created_by ?? '';
    message.deleted_at = object.deleted_at ?? 0;
    message.deleted_by = object.deleted_by ?? '';
    return message;
  }
};

function createBasePage_I18nNameEntry(): Page_I18nNameEntry {
  return { key: '', value: '' };
}

export const Page_I18nNameEntry: MessageFns<Page_I18nNameEntry> = {
  fromJSON(object: any): Page_I18nNameEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Page_I18nNameEntry>, I>>(base?: I): Page_I18nNameEntry {
    return Page_I18nNameEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Page_I18nNameEntry>, I>>(object: I): Page_I18nNameEntry {
    const message = createBasePage_I18nNameEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

function createBaseGetPageByIdReq(): GetPageByIdReq {
  return { page_id: 0 };
}

export const GetPageByIdReq: MessageFns<GetPageByIdReq> = {
  fromJSON(object: any): GetPageByIdReq {
    return { page_id: isSet(object.page_id) ? globalThis.Number(object.page_id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetPageByIdReq>, I>>(base?: I): GetPageByIdReq {
    return GetPageByIdReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPageByIdReq>, I>>(object: I): GetPageByIdReq {
    const message = createBaseGetPageByIdReq();
    message.page_id = object.page_id ?? 0;
    return message;
  }
};

function createBaseGetPageByIdRsp(): GetPageByIdRsp {
  return { page: undefined };
}

export const GetPageByIdRsp: MessageFns<GetPageByIdRsp> = {
  fromJSON(object: any): GetPageByIdRsp {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetPageByIdRsp>, I>>(base?: I): GetPageByIdRsp {
    return GetPageByIdRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPageByIdRsp>, I>>(object: I): GetPageByIdRsp {
    const message = createBaseGetPageByIdRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseGetEffectivePageReq(): GetEffectivePageReq {
  return { page_code: '' };
}

export const GetEffectivePageReq: MessageFns<GetEffectivePageReq> = {
  fromJSON(object: any): GetEffectivePageReq {
    return { page_code: isSet(object.page_code) ? globalThis.String(object.page_code) : '' };
  },

  create<I extends Exact<DeepPartial<GetEffectivePageReq>, I>>(base?: I): GetEffectivePageReq {
    return GetEffectivePageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEffectivePageReq>, I>>(object: I): GetEffectivePageReq {
    const message = createBaseGetEffectivePageReq();
    message.page_code = object.page_code ?? '';
    return message;
  }
};

function createBaseGetEffectivePageRsp(): GetEffectivePageRsp {
  return { page: undefined };
}

export const GetEffectivePageRsp: MessageFns<GetEffectivePageRsp> = {
  fromJSON(object: any): GetEffectivePageRsp {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<GetEffectivePageRsp>, I>>(base?: I): GetEffectivePageRsp {
    return GetEffectivePageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetEffectivePageRsp>, I>>(object: I): GetEffectivePageRsp {
    const message = createBaseGetEffectivePageRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseListPageReq(): ListPageReq {
  return { page: undefined, project: '', route: '', name: '' };
}

export const ListPageReq: MessageFns<ListPageReq> = {
  fromJSON(object: any): ListPageReq {
    return {
      page: isSet(object.page) ? Page1.fromJSON(object.page) : undefined,
      project: isSet(object.project) ? globalThis.String(object.project) : '',
      route: isSet(object.route) ? globalThis.String(object.route) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListPageReq>, I>>(base?: I): ListPageReq {
    return ListPageReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPageReq>, I>>(object: I): ListPageReq {
    const message = createBaseListPageReq();
    message.page = object.page !== undefined && object.page !== null ? Page1.fromPartial(object.page) : undefined;
    message.project = object.project ?? '';
    message.route = object.route ?? '';
    message.name = object.name ?? '';
    return message;
  }
};

function createBaseListPageRsp(): ListPageRsp {
  return { page: undefined, pages: [] };
}

export const ListPageRsp: MessageFns<ListPageRsp> = {
  fromJSON(object: any): ListPageRsp {
    return {
      page: isSet(object.page) ? Page1.fromJSON(object.page) : undefined,
      pages: globalThis.Array.isArray(object?.pages) ? object.pages.map((e: any) => Page.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListPageRsp>, I>>(base?: I): ListPageRsp {
    return ListPageRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListPageRsp>, I>>(object: I): ListPageRsp {
    const message = createBaseListPageRsp();
    message.page = object.page !== undefined && object.page !== null ? Page1.fromPartial(object.page) : undefined;
    message.pages = object.pages?.map(e => Page.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 低代码平台管理接口
 * smicro:spath=gitit.cc/social/components-service/social-platform/biz/lowcode/handlermgr
 */
export type LowcodeMgrDefinition = typeof LowcodeMgrDefinition;
export const LowcodeMgrDefinition = {
  name: 'LowcodeMgr',
  fullName: 'comm.mgr.platform.lowcode.LowcodeMgr',
  methods: {
    /** 创建页面 */
    createPage: {
      name: 'CreatePage',
      requestType: CreatePageReq,
      requestStream: false,
      responseType: CreatePageRsp,
      responseStream: false,
      options: {}
    },
    /** 更新页面 */
    updatePage: {
      name: 'UpdatePage',
      requestType: UpdatePageReq,
      requestStream: false,
      responseType: UpdatePageRsp,
      responseStream: false,
      options: {}
    },
    /** 删除页面 */
    deletePage: {
      name: 'DeletePage',
      requestType: DeletePageReq,
      requestStream: false,
      responseType: DeletePageRsp,
      responseStream: false,
      options: {}
    },
    /** 根据ID获取页面, 可以获取历史版本的页面. */
    getPageById: {
      name: 'GetPageById',
      requestType: GetPageByIdReq,
      requestStream: false,
      responseType: GetPageByIdRsp,
      responseStream: false,
      options: {}
    },
    /** 获取生效中的页面 */
    getEffectivePage: {
      name: 'GetEffectivePage',
      requestType: GetEffectivePageReq,
      requestStream: false,
      responseType: GetEffectivePageRsp,
      responseStream: false,
      options: {}
    },
    /** 获取页面列表 */
    listPage: {
      name: 'ListPage',
      requestType: ListPageReq,
      requestStream: false,
      responseType: ListPageRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
