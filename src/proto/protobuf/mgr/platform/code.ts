// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/code.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.platform.code';

export enum ErrCode {
  ERR_CODE_OK = 0,
  /** ERR_CODE_DB_FAIL - db 操作失败 */
  ERR_CODE_DB_FAIL = 20001,
  /** ERR_CODE_PARAMETER_WRONG - 入参错误 */
  ERR_CODE_PARAMETER_WRONG = 20002,
  /** ERR_CODE_DOC_CONFIG_WRONG - 配置文本错误 */
  ERR_CODE_DOC_CONFIG_WRONG = 20003,
  /** ERR_CODE_SOURCE_CONFIG_WRONG - 配置 内容源失败 */
  ERR_CODE_SOURCE_CONFIG_WRONG = 20004,
  /** ERR_CODE_NOTIFYCATION_CONFIG_WRONG - 配置推送失败 */
  ERR_CODE_NOTIFYCATION_CONFIG_WRONG = 20005,
  /** ERR_CODE_CANNOT_RELATION_DOC - 无法关联文本 */
  ERR_CODE_CANNOT_RELATION_DOC = 20006,
  /** ERR_CODE_CANNOT_RELATION_SOURCE - 无法关联内容源 */
  ERR_CODE_CANNOT_RELATION_SOURCE = 20007,
  /** ERR_CODE_PUBLISH_TIMER_WRONG - 推送定时器失败 */
  ERR_CODE_PUBLISH_TIMER_WRONG = 20008,
  /** ERR_CODE_ALREADY_NOTIFY - 已经推送 */
  ERR_CODE_ALREADY_NOTIFY = 20009,
  UNRECOGNIZED = -1
}

export function errCodeFromJSON(object: any): ErrCode {
  switch (object) {
    case 0:
    case 'ERR_CODE_OK':
      return ErrCode.ERR_CODE_OK;
    case 20001:
    case 'ERR_CODE_DB_FAIL':
      return ErrCode.ERR_CODE_DB_FAIL;
    case 20002:
    case 'ERR_CODE_PARAMETER_WRONG':
      return ErrCode.ERR_CODE_PARAMETER_WRONG;
    case 20003:
    case 'ERR_CODE_DOC_CONFIG_WRONG':
      return ErrCode.ERR_CODE_DOC_CONFIG_WRONG;
    case 20004:
    case 'ERR_CODE_SOURCE_CONFIG_WRONG':
      return ErrCode.ERR_CODE_SOURCE_CONFIG_WRONG;
    case 20005:
    case 'ERR_CODE_NOTIFYCATION_CONFIG_WRONG':
      return ErrCode.ERR_CODE_NOTIFYCATION_CONFIG_WRONG;
    case 20006:
    case 'ERR_CODE_CANNOT_RELATION_DOC':
      return ErrCode.ERR_CODE_CANNOT_RELATION_DOC;
    case 20007:
    case 'ERR_CODE_CANNOT_RELATION_SOURCE':
      return ErrCode.ERR_CODE_CANNOT_RELATION_SOURCE;
    case 20008:
    case 'ERR_CODE_PUBLISH_TIMER_WRONG':
      return ErrCode.ERR_CODE_PUBLISH_TIMER_WRONG;
    case 20009:
    case 'ERR_CODE_ALREADY_NOTIFY':
      return ErrCode.ERR_CODE_ALREADY_NOTIFY;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return ErrCode.UNRECOGNIZED;
  }
}
