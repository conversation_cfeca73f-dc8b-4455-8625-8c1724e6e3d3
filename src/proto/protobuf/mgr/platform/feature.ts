// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/feature.proto

/* eslint-disable */
import { Page } from '../../api/common/common';
import { OSType, oSTypeFromJSON } from '../../api/common/common-net';

export const protobufPackage = 'comm.mgr.platform.feature';

export enum AppFeatureStatus {
  APP_FEATURE_STATUS_NONE = 0,
  /** APP_FEATURE_STATUS_VALID - 有效 */
  APP_FEATURE_STATUS_VALID = 1,
  /** APP_FEATURE_STATUS_DELETED - 已删除 */
  APP_FEATURE_STATUS_DELETED = 2,
  UNRECOGNIZED = -1
}

export function appFeatureStatusFromJSON(object: any): AppFeatureStatus {
  switch (object) {
    case 0:
    case 'APP_FEATURE_STATUS_NONE':
      return AppFeatureStatus.APP_FEATURE_STATUS_NONE;
    case 1:
    case 'APP_FEATURE_STATUS_VALID':
      return AppFeatureStatus.APP_FEATURE_STATUS_VALID;
    case 2:
    case 'APP_FEATURE_STATUS_DELETED':
      return AppFeatureStatus.APP_FEATURE_STATUS_DELETED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AppFeatureStatus.UNRECOGNIZED;
  }
}

export enum VercFirstPubStatus {
  VERC_FIRST_PUB_STATUS_NONE = 0,
  /** VERC_FIRST_PUB_STATUS_FIRST - 版本第一次发布 */
  VERC_FIRST_PUB_STATUS_FIRST = 1,
  /** VERC_FIRST_PUB_STATUS_NO_FIRST - 版本非第一次发布 */
  VERC_FIRST_PUB_STATUS_NO_FIRST = 2,
  UNRECOGNIZED = -1
}

export function vercFirstPubStatusFromJSON(object: any): VercFirstPubStatus {
  switch (object) {
    case 0:
    case 'VERC_FIRST_PUB_STATUS_NONE':
      return VercFirstPubStatus.VERC_FIRST_PUB_STATUS_NONE;
    case 1:
    case 'VERC_FIRST_PUB_STATUS_FIRST':
      return VercFirstPubStatus.VERC_FIRST_PUB_STATUS_FIRST;
    case 2:
    case 'VERC_FIRST_PUB_STATUS_NO_FIRST':
      return VercFirstPubStatus.VERC_FIRST_PUB_STATUS_NO_FIRST;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return VercFirstPubStatus.UNRECOGNIZED;
  }
}

export interface AppFeatureVerc {
  /** add时忽略 */
  id: number;
  /** 版本号 */
  verc: number;
  /** 系统：1 ANDROID 2 ios */
  os_type: OSType;
  /** 包名 */
  pkg: string;
  /** 是否第一次发布 */
  first_pub_status: VercFirstPubStatus;
  /** 创建时间,unix时间戳 */
  create_at: number;
  create_by: string;
  create_id: string;
  /** 更新时间,unix时间戳 */
  update_at: number;
  update_by: string;
  update_id: string;
  /** 是否已经删除 */
  delete_status: AppFeatureStatus;
}

export interface AddAuditVercReq {
  /** 多个版本号 */
  vercs: number[];
  /** 系统：1 ANDROID 2 ios */
  os_type: OSType;
  /** 包名 */
  pkg: string;
  /** 是否第一次发布 */
  first_pub_status: VercFirstPubStatus;
}

export interface AddAuditVercRsp {
  id: number;
}

export interface ListAuditVercRep {
  page: Page | undefined;
  /** 主键查询 */
  id: number;
  /** 精确匹配 */
  verc: number;
  /** 系统：1 ANDROID 2 ios */
  os_type: OSType;
  /** 是否已经删除 */
  delete_status: AppFeatureStatus;
  /** 创建开始时间 */
  create_at_begin: number;
  /** 创建结束时间 */
  create_at_end: number;
  /** 包名 */
  pkg: string;
  /** 是否第一次发布 */
  first_pub_status: VercFirstPubStatus;
}

export interface ListAuditVercRsp {
  page: Page | undefined;
  data: AppFeatureVerc[];
}

export interface DeleteAuditVercRep {
  /** 删除多个id */
  ids: number[];
}

export interface DeleteAuditVercRsp {}

export interface AppFeatureAuditUser {
  /** add操作忽略 */
  id: number;
  uid: number;
  did: string;
  ip: string;
  isp: string;
  cou: string;
  mod: string;
  /** 来源，内容由后端组成。ostype-特征key:特征value */
  from: string;
  /** 以下数据 在add操作中忽略 */
  create_at: number;
  create_by: string;
  create_id: string;
  /** 更新时间,unix时间戳 */
  update_at: number;
  update_by: string;
  update_id: string;
  /** 是否已经删除 */
  delete_status: AppFeatureStatus;
}

export interface AddAuditUserRep {
  /** 有可能仅有一个uid，但是也有可能有其他信息 */
  auditoer_users: AppFeatureAuditUser[];
}

export interface AddAuditUserRsp {
  id: number;
}

export interface ListAuditUserRep {
  page: Page | undefined;
  /** 主键查询 */
  id: number;
  /** 精确匹配 */
  uid: number;
  /** 是否已经删除 */
  delete_status: AppFeatureStatus;
  /** 创建开始时间 */
  create_at_begin: number;
  /** 创建结束时间 */
  create_at_end: number;
  /** 根据did搜索 */
  did: string;
}

export interface ListAuditUserRsp {
  page: Page | undefined;
  data: AppFeatureAuditUser[];
}

export interface DeleteAuditUserRep {
  /** 删除多个id */
  ids: number[];
}

export interface DeleteAuditUserRsp {}

export interface GetPkgsReq {
  /** 马甲名，如果没有，则不填 */
  sockpuppet: string;
  /** 0 - 所有包 1 - android包名 2- ios包名 */
  os_type: OSType;
  /** 业务名 */
  anm: string;
  /** 业务子代号 */
  sub_anm: string;
}

export interface GetPkgsRsp {
  /** 包名列表 */
  pkgs: PkgOstype[];
}

export interface PkgOstype {
  pkg: string;
  os_type: OSType;
}

function createBaseAppFeatureVerc(): AppFeatureVerc {
  return {
    id: 0,
    verc: 0,
    os_type: 0,
    pkg: '',
    first_pub_status: 0,
    create_at: 0,
    create_by: '',
    create_id: '',
    update_at: 0,
    update_by: '',
    update_id: '',
    delete_status: 0
  };
}

export const AppFeatureVerc: MessageFns<AppFeatureVerc> = {
  fromJSON(object: any): AppFeatureVerc {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      verc: isSet(object.verc) ? globalThis.Number(object.verc) : 0,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      first_pub_status: isSet(object.first_pub_status) ? vercFirstPubStatusFromJSON(object.first_pub_status) : 0,
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      create_id: isSet(object.create_id) ? globalThis.String(object.create_id) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      update_id: isSet(object.update_id) ? globalThis.String(object.update_id) : '',
      delete_status: isSet(object.delete_status) ? appFeatureStatusFromJSON(object.delete_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<AppFeatureVerc>, I>>(base?: I): AppFeatureVerc {
    return AppFeatureVerc.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppFeatureVerc>, I>>(object: I): AppFeatureVerc {
    const message = createBaseAppFeatureVerc();
    message.id = object.id ?? 0;
    message.verc = object.verc ?? 0;
    message.os_type = object.os_type ?? 0;
    message.pkg = object.pkg ?? '';
    message.first_pub_status = object.first_pub_status ?? 0;
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.create_id = object.create_id ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.update_id = object.update_id ?? '';
    message.delete_status = object.delete_status ?? 0;
    return message;
  }
};

function createBaseAddAuditVercReq(): AddAuditVercReq {
  return { vercs: [], os_type: 0, pkg: '', first_pub_status: 0 };
}

export const AddAuditVercReq: MessageFns<AddAuditVercReq> = {
  fromJSON(object: any): AddAuditVercReq {
    return {
      vercs: globalThis.Array.isArray(object?.vercs) ? object.vercs.map((e: any) => globalThis.Number(e)) : [],
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      first_pub_status: isSet(object.first_pub_status) ? vercFirstPubStatusFromJSON(object.first_pub_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<AddAuditVercReq>, I>>(base?: I): AddAuditVercReq {
    return AddAuditVercReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAuditVercReq>, I>>(object: I): AddAuditVercReq {
    const message = createBaseAddAuditVercReq();
    message.vercs = object.vercs?.map(e => e) || [];
    message.os_type = object.os_type ?? 0;
    message.pkg = object.pkg ?? '';
    message.first_pub_status = object.first_pub_status ?? 0;
    return message;
  }
};

function createBaseAddAuditVercRsp(): AddAuditVercRsp {
  return { id: 0 };
}

export const AddAuditVercRsp: MessageFns<AddAuditVercRsp> = {
  fromJSON(object: any): AddAuditVercRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddAuditVercRsp>, I>>(base?: I): AddAuditVercRsp {
    return AddAuditVercRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAuditVercRsp>, I>>(object: I): AddAuditVercRsp {
    const message = createBaseAddAuditVercRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListAuditVercRep(): ListAuditVercRep {
  return {
    page: undefined,
    id: 0,
    verc: 0,
    os_type: 0,
    delete_status: 0,
    create_at_begin: 0,
    create_at_end: 0,
    pkg: '',
    first_pub_status: 0
  };
}

export const ListAuditVercRep: MessageFns<ListAuditVercRep> = {
  fromJSON(object: any): ListAuditVercRep {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      verc: isSet(object.verc) ? globalThis.Number(object.verc) : 0,
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      delete_status: isSet(object.delete_status) ? appFeatureStatusFromJSON(object.delete_status) : 0,
      create_at_begin: isSet(object.create_at_begin) ? globalThis.Number(object.create_at_begin) : 0,
      create_at_end: isSet(object.create_at_end) ? globalThis.Number(object.create_at_end) : 0,
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      first_pub_status: isSet(object.first_pub_status) ? vercFirstPubStatusFromJSON(object.first_pub_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListAuditVercRep>, I>>(base?: I): ListAuditVercRep {
    return ListAuditVercRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditVercRep>, I>>(object: I): ListAuditVercRep {
    const message = createBaseListAuditVercRep();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.verc = object.verc ?? 0;
    message.os_type = object.os_type ?? 0;
    message.delete_status = object.delete_status ?? 0;
    message.create_at_begin = object.create_at_begin ?? 0;
    message.create_at_end = object.create_at_end ?? 0;
    message.pkg = object.pkg ?? '';
    message.first_pub_status = object.first_pub_status ?? 0;
    return message;
  }
};

function createBaseListAuditVercRsp(): ListAuditVercRsp {
  return { page: undefined, data: [] };
}

export const ListAuditVercRsp: MessageFns<ListAuditVercRsp> = {
  fromJSON(object: any): ListAuditVercRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AppFeatureVerc.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAuditVercRsp>, I>>(base?: I): ListAuditVercRsp {
    return ListAuditVercRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditVercRsp>, I>>(object: I): ListAuditVercRsp {
    const message = createBaseListAuditVercRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AppFeatureVerc.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeleteAuditVercRep(): DeleteAuditVercRep {
  return { ids: [] };
}

export const DeleteAuditVercRep: MessageFns<DeleteAuditVercRep> = {
  fromJSON(object: any): DeleteAuditVercRep {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteAuditVercRep>, I>>(base?: I): DeleteAuditVercRep {
    return DeleteAuditVercRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAuditVercRep>, I>>(object: I): DeleteAuditVercRep {
    const message = createBaseDeleteAuditVercRep();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteAuditVercRsp(): DeleteAuditVercRsp {
  return {};
}

export const DeleteAuditVercRsp: MessageFns<DeleteAuditVercRsp> = {
  fromJSON(_: any): DeleteAuditVercRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteAuditVercRsp>, I>>(base?: I): DeleteAuditVercRsp {
    return DeleteAuditVercRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAuditVercRsp>, I>>(_: I): DeleteAuditVercRsp {
    const message = createBaseDeleteAuditVercRsp();
    return message;
  }
};

function createBaseAppFeatureAuditUser(): AppFeatureAuditUser {
  return {
    id: 0,
    uid: 0,
    did: '',
    ip: '',
    isp: '',
    cou: '',
    mod: '',
    from: '',
    create_at: 0,
    create_by: '',
    create_id: '',
    update_at: 0,
    update_by: '',
    update_id: '',
    delete_status: 0
  };
}

export const AppFeatureAuditUser: MessageFns<AppFeatureAuditUser> = {
  fromJSON(object: any): AppFeatureAuditUser {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      ip: isSet(object.ip) ? globalThis.String(object.ip) : '',
      isp: isSet(object.isp) ? globalThis.String(object.isp) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      from: isSet(object.from) ? globalThis.String(object.from) : '',
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      create_by: isSet(object.create_by) ? globalThis.String(object.create_by) : '',
      create_id: isSet(object.create_id) ? globalThis.String(object.create_id) : '',
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0,
      update_by: isSet(object.update_by) ? globalThis.String(object.update_by) : '',
      update_id: isSet(object.update_id) ? globalThis.String(object.update_id) : '',
      delete_status: isSet(object.delete_status) ? appFeatureStatusFromJSON(object.delete_status) : 0
    };
  },

  create<I extends Exact<DeepPartial<AppFeatureAuditUser>, I>>(base?: I): AppFeatureAuditUser {
    return AppFeatureAuditUser.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AppFeatureAuditUser>, I>>(object: I): AppFeatureAuditUser {
    const message = createBaseAppFeatureAuditUser();
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.did = object.did ?? '';
    message.ip = object.ip ?? '';
    message.isp = object.isp ?? '';
    message.cou = object.cou ?? '';
    message.mod = object.mod ?? '';
    message.from = object.from ?? '';
    message.create_at = object.create_at ?? 0;
    message.create_by = object.create_by ?? '';
    message.create_id = object.create_id ?? '';
    message.update_at = object.update_at ?? 0;
    message.update_by = object.update_by ?? '';
    message.update_id = object.update_id ?? '';
    message.delete_status = object.delete_status ?? 0;
    return message;
  }
};

function createBaseAddAuditUserRep(): AddAuditUserRep {
  return { auditoer_users: [] };
}

export const AddAuditUserRep: MessageFns<AddAuditUserRep> = {
  fromJSON(object: any): AddAuditUserRep {
    return {
      auditoer_users: globalThis.Array.isArray(object?.auditoer_users)
        ? object.auditoer_users.map((e: any) => AppFeatureAuditUser.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<AddAuditUserRep>, I>>(base?: I): AddAuditUserRep {
    return AddAuditUserRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAuditUserRep>, I>>(object: I): AddAuditUserRep {
    const message = createBaseAddAuditUserRep();
    message.auditoer_users = object.auditoer_users?.map(e => AppFeatureAuditUser.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAddAuditUserRsp(): AddAuditUserRsp {
  return { id: 0 };
}

export const AddAuditUserRsp: MessageFns<AddAuditUserRsp> = {
  fromJSON(object: any): AddAuditUserRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddAuditUserRsp>, I>>(base?: I): AddAuditUserRsp {
    return AddAuditUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAuditUserRsp>, I>>(object: I): AddAuditUserRsp {
    const message = createBaseAddAuditUserRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListAuditUserRep(): ListAuditUserRep {
  return { page: undefined, id: 0, uid: 0, delete_status: 0, create_at_begin: 0, create_at_end: 0, did: '' };
}

export const ListAuditUserRep: MessageFns<ListAuditUserRep> = {
  fromJSON(object: any): ListAuditUserRep {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      delete_status: isSet(object.delete_status) ? appFeatureStatusFromJSON(object.delete_status) : 0,
      create_at_begin: isSet(object.create_at_begin) ? globalThis.Number(object.create_at_begin) : 0,
      create_at_end: isSet(object.create_at_end) ? globalThis.Number(object.create_at_end) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAuditUserRep>, I>>(base?: I): ListAuditUserRep {
    return ListAuditUserRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditUserRep>, I>>(object: I): ListAuditUserRep {
    const message = createBaseListAuditUserRep();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.id = object.id ?? 0;
    message.uid = object.uid ?? 0;
    message.delete_status = object.delete_status ?? 0;
    message.create_at_begin = object.create_at_begin ?? 0;
    message.create_at_end = object.create_at_end ?? 0;
    message.did = object.did ?? '';
    return message;
  }
};

function createBaseListAuditUserRsp(): ListAuditUserRsp {
  return { page: undefined, data: [] };
}

export const ListAuditUserRsp: MessageFns<ListAuditUserRsp> = {
  fromJSON(object: any): ListAuditUserRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AppFeatureAuditUser.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAuditUserRsp>, I>>(base?: I): ListAuditUserRsp {
    return ListAuditUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAuditUserRsp>, I>>(object: I): ListAuditUserRsp {
    const message = createBaseListAuditUserRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AppFeatureAuditUser.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeleteAuditUserRep(): DeleteAuditUserRep {
  return { ids: [] };
}

export const DeleteAuditUserRep: MessageFns<DeleteAuditUserRep> = {
  fromJSON(object: any): DeleteAuditUserRep {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DeleteAuditUserRep>, I>>(base?: I): DeleteAuditUserRep {
    return DeleteAuditUserRep.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAuditUserRep>, I>>(object: I): DeleteAuditUserRep {
    const message = createBaseDeleteAuditUserRep();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDeleteAuditUserRsp(): DeleteAuditUserRsp {
  return {};
}

export const DeleteAuditUserRsp: MessageFns<DeleteAuditUserRsp> = {
  fromJSON(_: any): DeleteAuditUserRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DeleteAuditUserRsp>, I>>(base?: I): DeleteAuditUserRsp {
    return DeleteAuditUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeleteAuditUserRsp>, I>>(_: I): DeleteAuditUserRsp {
    const message = createBaseDeleteAuditUserRsp();
    return message;
  }
};

function createBaseGetPkgsReq(): GetPkgsReq {
  return { sockpuppet: '', os_type: 0, anm: '', sub_anm: '' };
}

export const GetPkgsReq: MessageFns<GetPkgsReq> = {
  fromJSON(object: any): GetPkgsReq {
    return {
      sockpuppet: isSet(object.sockpuppet) ? globalThis.String(object.sockpuppet) : '',
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0,
      anm: isSet(object.anm) ? globalThis.String(object.anm) : '',
      sub_anm: isSet(object.sub_anm) ? globalThis.String(object.sub_anm) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetPkgsReq>, I>>(base?: I): GetPkgsReq {
    return GetPkgsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPkgsReq>, I>>(object: I): GetPkgsReq {
    const message = createBaseGetPkgsReq();
    message.sockpuppet = object.sockpuppet ?? '';
    message.os_type = object.os_type ?? 0;
    message.anm = object.anm ?? '';
    message.sub_anm = object.sub_anm ?? '';
    return message;
  }
};

function createBaseGetPkgsRsp(): GetPkgsRsp {
  return { pkgs: [] };
}

export const GetPkgsRsp: MessageFns<GetPkgsRsp> = {
  fromJSON(object: any): GetPkgsRsp {
    return { pkgs: globalThis.Array.isArray(object?.pkgs) ? object.pkgs.map((e: any) => PkgOstype.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetPkgsRsp>, I>>(base?: I): GetPkgsRsp {
    return GetPkgsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetPkgsRsp>, I>>(object: I): GetPkgsRsp {
    const message = createBaseGetPkgsRsp();
    message.pkgs = object.pkgs?.map(e => PkgOstype.fromPartial(e)) || [];
    return message;
  }
};

function createBasePkgOstype(): PkgOstype {
  return { pkg: '', os_type: 0 };
}

export const PkgOstype: MessageFns<PkgOstype> = {
  fromJSON(object: any): PkgOstype {
    return {
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      os_type: isSet(object.os_type) ? oSTypeFromJSON(object.os_type) : 0
    };
  },

  create<I extends Exact<DeepPartial<PkgOstype>, I>>(base?: I): PkgOstype {
    return PkgOstype.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PkgOstype>, I>>(object: I): PkgOstype {
    const message = createBasePkgOstype();
    message.pkg = object.pkg ?? '';
    message.os_type = object.os_type ?? 0;
    return message;
  }
};

/** app特征 */
export type AppFeaturesMgrDefinition = typeof AppFeaturesMgrDefinition;
export const AppFeaturesMgrDefinition = {
  name: 'AppFeaturesMgr',
  fullName: 'comm.mgr.platform.feature.AppFeaturesMgr',
  methods: {
    /** 版本管理,无update */
    addAuditVerc: {
      name: 'AddAuditVerc',
      requestType: AddAuditVercReq,
      requestStream: false,
      responseType: AddAuditVercRsp,
      responseStream: false,
      options: {}
    },
    listAuditVerc: {
      name: 'ListAuditVerc',
      requestType: ListAuditVercRep,
      requestStream: false,
      responseType: ListAuditVercRsp,
      responseStream: false,
      options: {}
    },
    deleteAuditVerc: {
      name: 'DeleteAuditVerc',
      requestType: DeleteAuditVercRep,
      requestStream: false,
      responseType: DeleteAuditVercRsp,
      responseStream: false,
      options: {}
    },
    /** 用户管理，无update */
    addAuditUser: {
      name: 'AddAuditUser',
      requestType: AddAuditUserRep,
      requestStream: false,
      responseType: AddAuditUserRsp,
      responseStream: false,
      options: {}
    },
    listAuditUser: {
      name: 'ListAuditUser',
      requestType: ListAuditUserRep,
      requestStream: false,
      responseType: ListAuditUserRsp,
      responseStream: false,
      options: {}
    },
    deleteAuditUser: {
      name: 'DeleteAuditUser',
      requestType: DeleteAuditUserRep,
      requestStream: false,
      responseType: DeleteAuditUserRsp,
      responseStream: false,
      options: {}
    },
    /** 获取包名 */
    getPkgs: {
      name: 'GetPkgs',
      requestType: GetPkgsReq,
      requestStream: false,
      responseType: GetPkgsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
