// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/user.proto

/* eslint-disable */

export const protobufPackage = 'comm.mgr.platform.user';

/** smicro:spath=gitit.cc/social/components-service/social-platform/biz/user/handlermgr/user.go */

export enum UserStatus {
  /** USER_STATUS_NONE - 无意义 */
  USER_STATUS_NONE = 0,
  /** USER_STATUS_NORMAL - 正常 */
  USER_STATUS_NORMAL = 1,
  /** USER_STATUS_ABNORMAL - 异常, 封禁/注销 */
  USER_STATUS_ABNORMAL = 2,
  UNRECOGNIZED = -1
}

export function userStatusFromJSON(object: any): UserStatus {
  switch (object) {
    case 0:
    case 'USER_STATUS_NONE':
      return UserStatus.USER_STATUS_NONE;
    case 1:
    case 'USER_STATUS_NORMAL':
      return UserStatus.USER_STATUS_NORMAL;
    case 2:
    case 'USER_STATUS_ABNORMAL':
      return UserStatus.USER_STATUS_ABNORMAL;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserStatus.UNRECOGNIZED;
  }
}

export enum UserGender {
  /** USER_GENDER_NONE - 无意义 */
  USER_GENDER_NONE = 0,
  /** USER_GENDER_MALE - 男 */
  USER_GENDER_MALE = 1,
  /** USER_GENDER_FEMALE - 女 */
  USER_GENDER_FEMALE = 2,
  UNRECOGNIZED = -1
}

export function userGenderFromJSON(object: any): UserGender {
  switch (object) {
    case 0:
    case 'USER_GENDER_NONE':
      return UserGender.USER_GENDER_NONE;
    case 1:
    case 'USER_GENDER_MALE':
      return UserGender.USER_GENDER_MALE;
    case 2:
    case 'USER_GENDER_FEMALE':
      return UserGender.USER_GENDER_FEMALE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return UserGender.UNRECOGNIZED;
  }
}

export interface BatchGetUserInfoReq {
  uids: number[];
  show_uids: string[];
}

export interface BatchGetUserInfoRsp {
  users: UserInfo[];
}

export interface UserInfo {
  uid: number;
  show_uid: string;
  nickname: string;
  avatar: string;
  gender: UserGender;
  room_id: string;
  online: boolean;
  status: UserStatus;
  registered_at: number;
  /** 注册时的 did */
  did: string;
  /** 国家简码 */
  country: string;
  /** 手机号 */
  mobile: string;
  /** 邮箱 */
  email: string;
  /** 财富等级 */
  wealth_level: number;
  /** 财富值 */
  wealth_value: number;
  /** 魅力等级 */
  charm_level: number;
  /** 魅力值 */
  charm_value: number;
}

function createBaseBatchGetUserInfoReq(): BatchGetUserInfoReq {
  return { uids: [], show_uids: [] };
}

export const BatchGetUserInfoReq: MessageFns<BatchGetUserInfoReq> = {
  fromJSON(object: any): BatchGetUserInfoReq {
    return {
      uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [],
      show_uids: globalThis.Array.isArray(object?.show_uids)
        ? object.show_uids.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetUserInfoReq>, I>>(base?: I): BatchGetUserInfoReq {
    return BatchGetUserInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserInfoReq>, I>>(object: I): BatchGetUserInfoReq {
    const message = createBaseBatchGetUserInfoReq();
    message.uids = object.uids?.map(e => e) || [];
    message.show_uids = object.show_uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetUserInfoRsp(): BatchGetUserInfoRsp {
  return { users: [] };
}

export const BatchGetUserInfoRsp: MessageFns<BatchGetUserInfoRsp> = {
  fromJSON(object: any): BatchGetUserInfoRsp {
    return { users: globalThis.Array.isArray(object?.users) ? object.users.map((e: any) => UserInfo.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetUserInfoRsp>, I>>(base?: I): BatchGetUserInfoRsp {
    return BatchGetUserInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetUserInfoRsp>, I>>(object: I): BatchGetUserInfoRsp {
    const message = createBaseBatchGetUserInfoRsp();
    message.users = object.users?.map(e => UserInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUserInfo(): UserInfo {
  return {
    uid: 0,
    show_uid: '',
    nickname: '',
    avatar: '',
    gender: 0,
    room_id: '',
    online: false,
    status: 0,
    registered_at: 0,
    did: '',
    country: '',
    mobile: '',
    email: '',
    wealth_level: 0,
    wealth_value: 0,
    charm_level: 0,
    charm_value: 0
  };
}

export const UserInfo: MessageFns<UserInfo> = {
  fromJSON(object: any): UserInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      gender: isSet(object.gender) ? userGenderFromJSON(object.gender) : 0,
      room_id: isSet(object.room_id) ? globalThis.String(object.room_id) : '',
      online: isSet(object.online) ? globalThis.Boolean(object.online) : false,
      status: isSet(object.status) ? userStatusFromJSON(object.status) : 0,
      registered_at: isSet(object.registered_at) ? globalThis.Number(object.registered_at) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      mobile: isSet(object.mobile) ? globalThis.String(object.mobile) : '',
      email: isSet(object.email) ? globalThis.String(object.email) : '',
      wealth_level: isSet(object.wealth_level) ? globalThis.Number(object.wealth_level) : 0,
      wealth_value: isSet(object.wealth_value) ? globalThis.Number(object.wealth_value) : 0,
      charm_level: isSet(object.charm_level) ? globalThis.Number(object.charm_level) : 0,
      charm_value: isSet(object.charm_value) ? globalThis.Number(object.charm_value) : 0
    };
  },

  create<I extends Exact<DeepPartial<UserInfo>, I>>(base?: I): UserInfo {
    return UserInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UserInfo>, I>>(object: I): UserInfo {
    const message = createBaseUserInfo();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.gender = object.gender ?? 0;
    message.room_id = object.room_id ?? '';
    message.online = object.online ?? false;
    message.status = object.status ?? 0;
    message.registered_at = object.registered_at ?? 0;
    message.did = object.did ?? '';
    message.country = object.country ?? '';
    message.mobile = object.mobile ?? '';
    message.email = object.email ?? '';
    message.wealth_level = object.wealth_level ?? 0;
    message.wealth_value = object.wealth_value ?? 0;
    message.charm_level = object.charm_level ?? 0;
    message.charm_value = object.charm_value ?? 0;
    return message;
  }
};

export type CommonUserDefinition = typeof CommonUserDefinition;
export const CommonUserDefinition = {
  name: 'CommonUser',
  fullName: 'comm.mgr.platform.user.CommonUser',
  methods: {
    batchGetUserInfo: {
      name: 'BatchGetUserInfo',
      requestType: BatchGetUserInfoReq,
      requestStream: false,
      responseType: BatchGetUserInfoRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
