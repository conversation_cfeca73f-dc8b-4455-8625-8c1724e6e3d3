// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/platform/file_mgr.proto

/* eslint-disable */

export const protobufPackage = 'mgr.gene';

/** smicro:spath=gitit.cc/social/gene/gene-api/biz/file/handlermgr */

export interface GetUploadInfoReq {
  /** 上传场景, 头像: AVATAR, 私聊: IM_CHAT, 房间封面: ROOM_COVER, 公屏: ROOM_MESSAGE, ... */
  scene: string;
  /** 文件类型, 图片: IMAGE, 音频: AUDIO, 视频: VIDEO. ... */
  file_type: string;
  /** 文件 MIME-Type, 图片: image/jpeg, image/png, image/webp, 音频: audio/mpeg, audio/ogg, ... */
  mime_type: string;
}

export interface GetUploadInfoRsp {
  /** 上传地址 */
  put_url: string;
  /** 下载地址 */
  get_url: string;
  /** 文件路径 */
  file_path: string;
}

export interface CopyObjectReq {
  /** 源对象 */
  src_object: string;
  /** 目标对象 */
  dest_object: string;
}

export interface BatchCopyReq {
  objects: CopyObjectReq[];
}

export interface CopyObjectRsp {
  /** 源对象 */
  src_object: string;
  /** 目标对象 */
  dest_object: string;
  /** 目标对象 */
  dest_file_url: string;
}

export interface BatchCopyRsp {
  objects: CopyObjectRsp[];
}

function createBaseGetUploadInfoReq(): GetUploadInfoReq {
  return { scene: '', file_type: '', mime_type: '' };
}

export const GetUploadInfoReq: MessageFns<GetUploadInfoReq> = {
  fromJSON(object: any): GetUploadInfoReq {
    return {
      scene: isSet(object.scene) ? globalThis.String(object.scene) : '',
      file_type: isSet(object.file_type) ? globalThis.String(object.file_type) : '',
      mime_type: isSet(object.mime_type) ? globalThis.String(object.mime_type) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetUploadInfoReq>, I>>(base?: I): GetUploadInfoReq {
    return GetUploadInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUploadInfoReq>, I>>(object: I): GetUploadInfoReq {
    const message = createBaseGetUploadInfoReq();
    message.scene = object.scene ?? '';
    message.file_type = object.file_type ?? '';
    message.mime_type = object.mime_type ?? '';
    return message;
  }
};

function createBaseGetUploadInfoRsp(): GetUploadInfoRsp {
  return { put_url: '', get_url: '', file_path: '' };
}

export const GetUploadInfoRsp: MessageFns<GetUploadInfoRsp> = {
  fromJSON(object: any): GetUploadInfoRsp {
    return {
      put_url: isSet(object.put_url) ? globalThis.String(object.put_url) : '',
      get_url: isSet(object.get_url) ? globalThis.String(object.get_url) : '',
      file_path: isSet(object.file_path) ? globalThis.String(object.file_path) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetUploadInfoRsp>, I>>(base?: I): GetUploadInfoRsp {
    return GetUploadInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetUploadInfoRsp>, I>>(object: I): GetUploadInfoRsp {
    const message = createBaseGetUploadInfoRsp();
    message.put_url = object.put_url ?? '';
    message.get_url = object.get_url ?? '';
    message.file_path = object.file_path ?? '';
    return message;
  }
};

function createBaseCopyObjectReq(): CopyObjectReq {
  return { src_object: '', dest_object: '' };
}

export const CopyObjectReq: MessageFns<CopyObjectReq> = {
  fromJSON(object: any): CopyObjectReq {
    return {
      src_object: isSet(object.src_object) ? globalThis.String(object.src_object) : '',
      dest_object: isSet(object.dest_object) ? globalThis.String(object.dest_object) : ''
    };
  },

  create<I extends Exact<DeepPartial<CopyObjectReq>, I>>(base?: I): CopyObjectReq {
    return CopyObjectReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyObjectReq>, I>>(object: I): CopyObjectReq {
    const message = createBaseCopyObjectReq();
    message.src_object = object.src_object ?? '';
    message.dest_object = object.dest_object ?? '';
    return message;
  }
};

function createBaseBatchCopyReq(): BatchCopyReq {
  return { objects: [] };
}

export const BatchCopyReq: MessageFns<BatchCopyReq> = {
  fromJSON(object: any): BatchCopyReq {
    return {
      objects: globalThis.Array.isArray(object?.objects)
        ? object.objects.map((e: any) => CopyObjectReq.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchCopyReq>, I>>(base?: I): BatchCopyReq {
    return BatchCopyReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCopyReq>, I>>(object: I): BatchCopyReq {
    const message = createBaseBatchCopyReq();
    message.objects = object.objects?.map(e => CopyObjectReq.fromPartial(e)) || [];
    return message;
  }
};

function createBaseCopyObjectRsp(): CopyObjectRsp {
  return { src_object: '', dest_object: '', dest_file_url: '' };
}

export const CopyObjectRsp: MessageFns<CopyObjectRsp> = {
  fromJSON(object: any): CopyObjectRsp {
    return {
      src_object: isSet(object.src_object) ? globalThis.String(object.src_object) : '',
      dest_object: isSet(object.dest_object) ? globalThis.String(object.dest_object) : '',
      dest_file_url: isSet(object.dest_file_url) ? globalThis.String(object.dest_file_url) : ''
    };
  },

  create<I extends Exact<DeepPartial<CopyObjectRsp>, I>>(base?: I): CopyObjectRsp {
    return CopyObjectRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CopyObjectRsp>, I>>(object: I): CopyObjectRsp {
    const message = createBaseCopyObjectRsp();
    message.src_object = object.src_object ?? '';
    message.dest_object = object.dest_object ?? '';
    message.dest_file_url = object.dest_file_url ?? '';
    return message;
  }
};

function createBaseBatchCopyRsp(): BatchCopyRsp {
  return { objects: [] };
}

export const BatchCopyRsp: MessageFns<BatchCopyRsp> = {
  fromJSON(object: any): BatchCopyRsp {
    return {
      objects: globalThis.Array.isArray(object?.objects)
        ? object.objects.map((e: any) => CopyObjectRsp.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchCopyRsp>, I>>(base?: I): BatchCopyRsp {
    return BatchCopyRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchCopyRsp>, I>>(object: I): BatchCopyRsp {
    const message = createBaseBatchCopyRsp();
    message.objects = object.objects?.map(e => CopyObjectRsp.fromPartial(e)) || [];
    return message;
  }
};

export type FileMgrDefinition = typeof FileMgrDefinition;
export const FileMgrDefinition = {
  name: 'FileMgr',
  fullName: 'mgr.gene.FileMgr',
  methods: {
    /** 获取文件上传信息, 返回文件上传的目标地址. */
    getUploadInfo: {
      name: 'GetUploadInfo',
      requestType: GetUploadInfoReq,
      requestStream: false,
      responseType: GetUploadInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 批量copy对象资源 */
    batchCopy: {
      name: 'BatchCopy',
      requestType: BatchCopyReq,
      requestStream: false,
      responseType: BatchCopyRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
