// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/account/user.proto

/* eslint-disable */
import { Page, Sorts } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.account';

/** smicro:spath=gitit.cc/social/components-service/social-account/handler-mgr/usermgr */

export interface User {
  /** 用户UID */
  uid: number;
  /** 用户外显UID */
  show_uid: string;
  /** 用户昵称 */
  nickname: string;
  /** 用户性别 */
  gender: number;
  /** 用户头像 */
  avatar: string;
  /** 用户国家 */
  country: string;
  /** 用户生日 */
  birthday: string;
  /** 客户端语言 */
  language: string;
  /** 最近登录时间 */
  login_at: number;
  /** 最近登录设备和版本 */
  device_version: string;
  /** 最近登录客户端版本 */
  client_version: string;
  /** 状态, 1: 正常 2: 封禁 */
  status: number;
  /** 注册时间 */
  created_at: number;
  /** 更新时间 */
  updated_at: number;
  /** 运营操作时间 */
  operated_at: number;
  /** 运营操作人 */
  operated_by: string;
}

export interface SearchUserReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 排序参数 */
  sorts: Sorts | undefined;
  /** 用户ID, 以逗号分隔. */
  uids: string;
  /** 注册时间开始 */
  created_at_start: number;
  /** 注册时间结束 */
  created_at_end: number;
  /** 终端设备 */
  device_version: string;
  /** 登录时间开始 */
  login_at_start: number;
  /** 登录时间结束 */
  login_at_end: number;
}

export interface SearchUserRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 用户列表 */
  data: User[];
}

export interface ListDeviceVersionReq {}

export interface ListDeviceVersionRsp {
  device_versions: string[];
}

function createBaseUser(): User {
  return {
    uid: 0,
    show_uid: '',
    nickname: '',
    gender: 0,
    avatar: '',
    country: '',
    birthday: '',
    language: '',
    login_at: 0,
    device_version: '',
    client_version: '',
    status: 0,
    created_at: 0,
    updated_at: 0,
    operated_at: 0,
    operated_by: ''
  };
}

export const User: MessageFns<User> = {
  fromJSON(object: any): User {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      show_uid: isSet(object.show_uid) ? globalThis.String(object.show_uid) : '',
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      gender: isSet(object.gender) ? globalThis.Number(object.gender) : 0,
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      country: isSet(object.country) ? globalThis.String(object.country) : '',
      birthday: isSet(object.birthday) ? globalThis.String(object.birthday) : '',
      language: isSet(object.language) ? globalThis.String(object.language) : '',
      login_at: isSet(object.login_at) ? globalThis.Number(object.login_at) : 0,
      device_version: isSet(object.device_version) ? globalThis.String(object.device_version) : '',
      client_version: isSet(object.client_version) ? globalThis.String(object.client_version) : '',
      status: isSet(object.status) ? globalThis.Number(object.status) : 0,
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      operated_at: isSet(object.operated_at) ? globalThis.Number(object.operated_at) : 0,
      operated_by: isSet(object.operated_by) ? globalThis.String(object.operated_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<User>, I>>(base?: I): User {
    return User.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<User>, I>>(object: I): User {
    const message = createBaseUser();
    message.uid = object.uid ?? 0;
    message.show_uid = object.show_uid ?? '';
    message.nickname = object.nickname ?? '';
    message.gender = object.gender ?? 0;
    message.avatar = object.avatar ?? '';
    message.country = object.country ?? '';
    message.birthday = object.birthday ?? '';
    message.language = object.language ?? '';
    message.login_at = object.login_at ?? 0;
    message.device_version = object.device_version ?? '';
    message.client_version = object.client_version ?? '';
    message.status = object.status ?? 0;
    message.created_at = object.created_at ?? 0;
    message.updated_at = object.updated_at ?? 0;
    message.operated_at = object.operated_at ?? 0;
    message.operated_by = object.operated_by ?? '';
    return message;
  }
};

function createBaseSearchUserReq(): SearchUserReq {
  return {
    page: undefined,
    sorts: undefined,
    uids: '',
    created_at_start: 0,
    created_at_end: 0,
    device_version: '',
    login_at_start: 0,
    login_at_end: 0
  };
}

export const SearchUserReq: MessageFns<SearchUserReq> = {
  fromJSON(object: any): SearchUserReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      sorts: isSet(object.sorts) ? Sorts.fromJSON(object.sorts) : undefined,
      uids: isSet(object.uids) ? globalThis.String(object.uids) : '',
      created_at_start: isSet(object.created_at_start) ? globalThis.Number(object.created_at_start) : 0,
      created_at_end: isSet(object.created_at_end) ? globalThis.Number(object.created_at_end) : 0,
      device_version: isSet(object.device_version) ? globalThis.String(object.device_version) : '',
      login_at_start: isSet(object.login_at_start) ? globalThis.Number(object.login_at_start) : 0,
      login_at_end: isSet(object.login_at_end) ? globalThis.Number(object.login_at_end) : 0
    };
  },

  create<I extends Exact<DeepPartial<SearchUserReq>, I>>(base?: I): SearchUserReq {
    return SearchUserReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchUserReq>, I>>(object: I): SearchUserReq {
    const message = createBaseSearchUserReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.sorts = object.sorts !== undefined && object.sorts !== null ? Sorts.fromPartial(object.sorts) : undefined;
    message.uids = object.uids ?? '';
    message.created_at_start = object.created_at_start ?? 0;
    message.created_at_end = object.created_at_end ?? 0;
    message.device_version = object.device_version ?? '';
    message.login_at_start = object.login_at_start ?? 0;
    message.login_at_end = object.login_at_end ?? 0;
    return message;
  }
};

function createBaseSearchUserRsp(): SearchUserRsp {
  return { page: undefined, data: [] };
}

export const SearchUserRsp: MessageFns<SearchUserRsp> = {
  fromJSON(object: any): SearchUserRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => User.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<SearchUserRsp>, I>>(base?: I): SearchUserRsp {
    return SearchUserRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<SearchUserRsp>, I>>(object: I): SearchUserRsp {
    const message = createBaseSearchUserRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => User.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListDeviceVersionReq(): ListDeviceVersionReq {
  return {};
}

export const ListDeviceVersionReq: MessageFns<ListDeviceVersionReq> = {
  fromJSON(_: any): ListDeviceVersionReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListDeviceVersionReq>, I>>(base?: I): ListDeviceVersionReq {
    return ListDeviceVersionReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDeviceVersionReq>, I>>(_: I): ListDeviceVersionReq {
    const message = createBaseListDeviceVersionReq();
    return message;
  }
};

function createBaseListDeviceVersionRsp(): ListDeviceVersionRsp {
  return { device_versions: [] };
}

export const ListDeviceVersionRsp: MessageFns<ListDeviceVersionRsp> = {
  fromJSON(object: any): ListDeviceVersionRsp {
    return {
      device_versions: globalThis.Array.isArray(object?.device_versions)
        ? object.device_versions.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListDeviceVersionRsp>, I>>(base?: I): ListDeviceVersionRsp {
    return ListDeviceVersionRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListDeviceVersionRsp>, I>>(object: I): ListDeviceVersionRsp {
    const message = createBaseListDeviceVersionRsp();
    message.device_versions = object.device_versions?.map(e => e) || [];
    return message;
  }
};

export type UserMgrDefinition = typeof UserMgrDefinition;
export const UserMgrDefinition = {
  name: 'UserMgr',
  fullName: 'comm.mgr.account.UserMgr',
  methods: {
    /** 搜索用户 */
    searchUser: {
      name: 'SearchUser',
      requestType: SearchUserReq,
      requestStream: false,
      responseType: SearchUserRsp,
      responseStream: false,
      options: {}
    },
    /** 拉取所有设备版本 */
    listDeviceVersion: {
      name: 'ListDeviceVersion',
      requestType: ListDeviceVersionReq,
      requestStream: false,
      responseType: ListDeviceVersionRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
