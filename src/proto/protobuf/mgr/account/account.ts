// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/account/account.proto

/* eslint-disable */
import { AccountStatus, accountStatusFromJSON, LoginType, loginTypeFromJSON } from '../../api/account/account';
import { Page, Sorts } from '../../api/common/common';

export const protobufPackage = 'comm.mgr.account';

/** smicro:spath=gitit.cc/social/components-service/social-account/handler-mgr/accountmgr */

/** 账号封禁/解禁类型 */
export enum AccountBanType {
  /** ACCOUNT_BAN_TYPE_UID - 默认按uid */
  ACCOUNT_BAN_TYPE_UID = 0,
  /** ACCOUNT_BAN_TYPE_REG_DID - 按注册did */
  ACCOUNT_BAN_TYPE_REG_DID = 1,
  /** ACCOUNT_BAN_TYPE_REG_IP - 按注册ip */
  ACCOUNT_BAN_TYPE_REG_IP = 2,
  UNRECOGNIZED = -1
}

export function accountBanTypeFromJSON(object: any): AccountBanType {
  switch (object) {
    case 0:
    case 'ACCOUNT_BAN_TYPE_UID':
      return AccountBanType.ACCOUNT_BAN_TYPE_UID;
    case 1:
    case 'ACCOUNT_BAN_TYPE_REG_DID':
      return AccountBanType.ACCOUNT_BAN_TYPE_REG_DID;
    case 2:
    case 'ACCOUNT_BAN_TYPE_REG_IP':
      return AccountBanType.ACCOUNT_BAN_TYPE_REG_IP;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountBanType.UNRECOGNIZED;
  }
}

/** 账号封禁操作类型 */
export enum AccountBanOptType {
  ACCOUNT_BAN_OPT_NONE = 0,
  /** ACCOUNT_BAN_OPT_BAN - 封禁 */
  ACCOUNT_BAN_OPT_BAN = 1,
  /** ACCOUNT_BAN_OPT_UNBAN - 解封 */
  ACCOUNT_BAN_OPT_UNBAN = 2,
  UNRECOGNIZED = -1
}

export function accountBanOptTypeFromJSON(object: any): AccountBanOptType {
  switch (object) {
    case 0:
    case 'ACCOUNT_BAN_OPT_NONE':
      return AccountBanOptType.ACCOUNT_BAN_OPT_NONE;
    case 1:
    case 'ACCOUNT_BAN_OPT_BAN':
      return AccountBanOptType.ACCOUNT_BAN_OPT_BAN;
    case 2:
    case 'ACCOUNT_BAN_OPT_UNBAN':
      return AccountBanOptType.ACCOUNT_BAN_OPT_UNBAN;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountBanOptType.UNRECOGNIZED;
  }
}

/** 账号封禁/解封 批量类型 */
export enum AccountBanBatchType {
  /** ACCOUNT_BAN_BATCH_TYPE_SINGLE - 默认单个 */
  ACCOUNT_BAN_BATCH_TYPE_SINGLE = 0,
  /** ACCOUNT_BAN_BATCH_TYPE_BATCH - 批量 */
  ACCOUNT_BAN_BATCH_TYPE_BATCH = 1,
  UNRECOGNIZED = -1
}

export function accountBanBatchTypeFromJSON(object: any): AccountBanBatchType {
  switch (object) {
    case 0:
    case 'ACCOUNT_BAN_BATCH_TYPE_SINGLE':
      return AccountBanBatchType.ACCOUNT_BAN_BATCH_TYPE_SINGLE;
    case 1:
    case 'ACCOUNT_BAN_BATCH_TYPE_BATCH':
      return AccountBanBatchType.ACCOUNT_BAN_BATCH_TYPE_BATCH;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return AccountBanBatchType.UNRECOGNIZED;
  }
}

export interface BanAccountReq {
  /** 操作类型 */
  ban_opt_type: AccountBanOptType;
  uid: number;
  /** 截止日期 */
  expired_at: number;
  /** 原因 */
  reason: string;
  /** 操作人 */
  operator: string;
  /** uid列表 */
  batch_uid: number[];
  /** 封禁类型 */
  ban_type: AccountBanType;
  /** 封禁批量类型 */
  ban_batch_type: AccountBanBatchType;
  /** 单个did */
  did: string;
  /** did列表 */
  batch_did: string[];
  /** 单个ip */
  ip: string;
  /** ip列表 */
  batch_ip: string[];
}

export interface BanAccountRsp {}

export interface QuickWithdrawThirdAccountReq {
  /** 用户ID */
  uid: number;
}

export interface QuickWithdrawThirdAccountRsp {}

/** 已封禁的账户 */
export interface BannedAccount {
  uid: number;
  nickname: string;
  avatar: string;
  ban_start_at: number;
  ban_end_at: number;
  ban_reason: string;
  operate_at: number;
  operate_by: string;
}

export interface ListBannedAccountReq {
  /** 分页参数 */
  page: Page | undefined;
  /** 排序参数 */
  sorts: Sorts | undefined;
  /** 用户ID, 以逗号分隔. */
  uids: string;
  /** 封禁时间开始 */
  ban_at_start: number;
  /** 封禁时间结束 */
  ban_at_end: number;
}

export interface ListBannedAccountRsp {
  /** 分页参数 */
  page: Page | undefined;
  /** 已封禁账户 */
  banned_accounts: BannedAccount[];
}

export interface BindPhoneNumReq {
  /** 用户uid */
  uid: number;
  /** 区号 */
  phone_area: string;
  /** 号码 */
  phone_num: string;
  /** 操作人 需要传真实操作人用户名 */
  operator: string;
}

export interface BindPhoneNumRsp {}

export interface GetAccountInfoReq {
  /** 用户ID */
  uid: number;
}

export interface GetAccountInfoRsp {
  /** 账号信息 */
  info: AccountInfo | undefined;
}

export interface BatchGetAccountInfoReq {
  /** UID */
  uids: number[];
}

export interface BatchGetAccountInfoRsp {
  /** 账号信息 */
  account_infos: { [key: number]: AccountInfo };
}

export interface BatchGetAccountInfoRsp_AccountInfosEntry {
  key: number;
  value: AccountInfo | undefined;
}

export interface AccountInfo {
  /** 用户ID */
  uid: number;
  /** 区号 */
  area: string;
  /** 手机号 */
  phone: string;
  /** 账号名 */
  account: string;
  /** 账号状态 */
  status: AccountStatus;
  /** 注册信息 */
  register_type: LoginType;
  register_device_info: DeviceInfo | undefined;
  register_time: number;
  /** 最后登录信息 */
  login_type: LoginType;
  login_device_info: DeviceInfo | undefined;
  login_time: number;
}

export interface BanLog {
  id: number;
  /** 截止日期 */
  expired_at: number;
  /** 原因 */
  reason: string;
  /** 操作人 */
  operator: string;
  /** 封禁操作类型 */
  ban_op_type: AccountBanOptType;
  /** 封禁类型 */
  ban_type: AccountBanType;
  /** 封禁内容 */
  ban_type_content: string;
}

export interface GetBanLogReq {
  items: GetBanLogReq_Item[];
}

export interface GetBanLogReq_Item {
  /** 封禁类型 */
  ban_type: AccountBanType;
  /** 封禁内容 */
  ban_type_content: string;
}

export interface GetBanLogRsp {
  logs: BanLog[];
}

/** 设备信息 */
export interface DeviceInfo {
  /** 设备ip */
  ip: string;
  /** 语言 */
  lan: string;
  /** 国家 */
  cou: string;
  /** 设备id */
  did: string;
  /** 平台 */
  pf: string;
  /** 版本号 */
  verc: string;
  /** 手机型号 */
  mod: string;
  /** 手机品牌 */
  brand: string;
  /** 系统平台 */
  os: string;
  /** 操作系统 */
  os_ver: string;
  /** 操作系统 */
  os_verc: string;
  /** 包名 */
  pkg: string;
  /** sim卡 0没有 1有 空代表没传 */
  sim: string;
}

export interface BatchGetAccountsByDidsReq {
  /** 设备id */
  dids: string[];
}

export interface BatchGetAccountsByDidsRsp {
  /** 账号信息 */
  account_infos: AccountInfo[];
}

function createBaseBanAccountReq(): BanAccountReq {
  return {
    ban_opt_type: 0,
    uid: 0,
    expired_at: 0,
    reason: '',
    operator: '',
    batch_uid: [],
    ban_type: 0,
    ban_batch_type: 0,
    did: '',
    batch_did: [],
    ip: '',
    batch_ip: []
  };
}

export const BanAccountReq: MessageFns<BanAccountReq> = {
  fromJSON(object: any): BanAccountReq {
    return {
      ban_opt_type: isSet(object.ban_opt_type) ? accountBanOptTypeFromJSON(object.ban_opt_type) : 0,
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      expired_at: isSet(object.expired_at) ? globalThis.Number(object.expired_at) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      batch_uid: globalThis.Array.isArray(object?.batch_uid)
        ? object.batch_uid.map((e: any) => globalThis.Number(e))
        : [],
      ban_type: isSet(object.ban_type) ? accountBanTypeFromJSON(object.ban_type) : 0,
      ban_batch_type: isSet(object.ban_batch_type) ? accountBanBatchTypeFromJSON(object.ban_batch_type) : 0,
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      batch_did: globalThis.Array.isArray(object?.batch_did)
        ? object.batch_did.map((e: any) => globalThis.String(e))
        : [],
      ip: isSet(object.ip) ? globalThis.String(object.ip) : '',
      batch_ip: globalThis.Array.isArray(object?.batch_ip) ? object.batch_ip.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<BanAccountReq>, I>>(base?: I): BanAccountReq {
    return BanAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanAccountReq>, I>>(object: I): BanAccountReq {
    const message = createBaseBanAccountReq();
    message.ban_opt_type = object.ban_opt_type ?? 0;
    message.uid = object.uid ?? 0;
    message.expired_at = object.expired_at ?? 0;
    message.reason = object.reason ?? '';
    message.operator = object.operator ?? '';
    message.batch_uid = object.batch_uid?.map(e => e) || [];
    message.ban_type = object.ban_type ?? 0;
    message.ban_batch_type = object.ban_batch_type ?? 0;
    message.did = object.did ?? '';
    message.batch_did = object.batch_did?.map(e => e) || [];
    message.ip = object.ip ?? '';
    message.batch_ip = object.batch_ip?.map(e => e) || [];
    return message;
  }
};

function createBaseBanAccountRsp(): BanAccountRsp {
  return {};
}

export const BanAccountRsp: MessageFns<BanAccountRsp> = {
  fromJSON(_: any): BanAccountRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BanAccountRsp>, I>>(base?: I): BanAccountRsp {
    return BanAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanAccountRsp>, I>>(_: I): BanAccountRsp {
    const message = createBaseBanAccountRsp();
    return message;
  }
};

function createBaseQuickWithdrawThirdAccountReq(): QuickWithdrawThirdAccountReq {
  return { uid: 0 };
}

export const QuickWithdrawThirdAccountReq: MessageFns<QuickWithdrawThirdAccountReq> = {
  fromJSON(object: any): QuickWithdrawThirdAccountReq {
    return { uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0 };
  },

  create<I extends Exact<DeepPartial<QuickWithdrawThirdAccountReq>, I>>(base?: I): QuickWithdrawThirdAccountReq {
    return QuickWithdrawThirdAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuickWithdrawThirdAccountReq>, I>>(object: I): QuickWithdrawThirdAccountReq {
    const message = createBaseQuickWithdrawThirdAccountReq();
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseQuickWithdrawThirdAccountRsp(): QuickWithdrawThirdAccountRsp {
  return {};
}

export const QuickWithdrawThirdAccountRsp: MessageFns<QuickWithdrawThirdAccountRsp> = {
  fromJSON(_: any): QuickWithdrawThirdAccountRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<QuickWithdrawThirdAccountRsp>, I>>(base?: I): QuickWithdrawThirdAccountRsp {
    return QuickWithdrawThirdAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<QuickWithdrawThirdAccountRsp>, I>>(_: I): QuickWithdrawThirdAccountRsp {
    const message = createBaseQuickWithdrawThirdAccountRsp();
    return message;
  }
};

function createBaseBannedAccount(): BannedAccount {
  return {
    uid: 0,
    nickname: '',
    avatar: '',
    ban_start_at: 0,
    ban_end_at: 0,
    ban_reason: '',
    operate_at: 0,
    operate_by: ''
  };
}

export const BannedAccount: MessageFns<BannedAccount> = {
  fromJSON(object: any): BannedAccount {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      nickname: isSet(object.nickname) ? globalThis.String(object.nickname) : '',
      avatar: isSet(object.avatar) ? globalThis.String(object.avatar) : '',
      ban_start_at: isSet(object.ban_start_at) ? globalThis.Number(object.ban_start_at) : 0,
      ban_end_at: isSet(object.ban_end_at) ? globalThis.Number(object.ban_end_at) : 0,
      ban_reason: isSet(object.ban_reason) ? globalThis.String(object.ban_reason) : '',
      operate_at: isSet(object.operate_at) ? globalThis.Number(object.operate_at) : 0,
      operate_by: isSet(object.operate_by) ? globalThis.String(object.operate_by) : ''
    };
  },

  create<I extends Exact<DeepPartial<BannedAccount>, I>>(base?: I): BannedAccount {
    return BannedAccount.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BannedAccount>, I>>(object: I): BannedAccount {
    const message = createBaseBannedAccount();
    message.uid = object.uid ?? 0;
    message.nickname = object.nickname ?? '';
    message.avatar = object.avatar ?? '';
    message.ban_start_at = object.ban_start_at ?? 0;
    message.ban_end_at = object.ban_end_at ?? 0;
    message.ban_reason = object.ban_reason ?? '';
    message.operate_at = object.operate_at ?? 0;
    message.operate_by = object.operate_by ?? '';
    return message;
  }
};

function createBaseListBannedAccountReq(): ListBannedAccountReq {
  return { page: undefined, sorts: undefined, uids: '', ban_at_start: 0, ban_at_end: 0 };
}

export const ListBannedAccountReq: MessageFns<ListBannedAccountReq> = {
  fromJSON(object: any): ListBannedAccountReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      sorts: isSet(object.sorts) ? Sorts.fromJSON(object.sorts) : undefined,
      uids: isSet(object.uids) ? globalThis.String(object.uids) : '',
      ban_at_start: isSet(object.ban_at_start) ? globalThis.Number(object.ban_at_start) : 0,
      ban_at_end: isSet(object.ban_at_end) ? globalThis.Number(object.ban_at_end) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListBannedAccountReq>, I>>(base?: I): ListBannedAccountReq {
    return ListBannedAccountReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBannedAccountReq>, I>>(object: I): ListBannedAccountReq {
    const message = createBaseListBannedAccountReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.sorts = object.sorts !== undefined && object.sorts !== null ? Sorts.fromPartial(object.sorts) : undefined;
    message.uids = object.uids ?? '';
    message.ban_at_start = object.ban_at_start ?? 0;
    message.ban_at_end = object.ban_at_end ?? 0;
    return message;
  }
};

function createBaseListBannedAccountRsp(): ListBannedAccountRsp {
  return { page: undefined, banned_accounts: [] };
}

export const ListBannedAccountRsp: MessageFns<ListBannedAccountRsp> = {
  fromJSON(object: any): ListBannedAccountRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      banned_accounts: globalThis.Array.isArray(object?.banned_accounts)
        ? object.banned_accounts.map((e: any) => BannedAccount.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListBannedAccountRsp>, I>>(base?: I): ListBannedAccountRsp {
    return ListBannedAccountRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBannedAccountRsp>, I>>(object: I): ListBannedAccountRsp {
    const message = createBaseListBannedAccountRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.banned_accounts = object.banned_accounts?.map(e => BannedAccount.fromPartial(e)) || [];
    return message;
  }
};

function createBaseBindPhoneNumReq(): BindPhoneNumReq {
  return { uid: 0, phone_area: '', phone_num: '', operator: '' };
}

export const BindPhoneNumReq: MessageFns<BindPhoneNumReq> = {
  fromJSON(object: any): BindPhoneNumReq {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      phone_area: isSet(object.phone_area) ? globalThis.String(object.phone_area) : '',
      phone_num: isSet(object.phone_num) ? globalThis.String(object.phone_num) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : ''
    };
  },

  create<I extends Exact<DeepPartial<BindPhoneNumReq>, I>>(base?: I): BindPhoneNumReq {
    return BindPhoneNumReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindPhoneNumReq>, I>>(object: I): BindPhoneNumReq {
    const message = createBaseBindPhoneNumReq();
    message.uid = object.uid ?? 0;
    message.phone_area = object.phone_area ?? '';
    message.phone_num = object.phone_num ?? '';
    message.operator = object.operator ?? '';
    return message;
  }
};

function createBaseBindPhoneNumRsp(): BindPhoneNumRsp {
  return {};
}

export const BindPhoneNumRsp: MessageFns<BindPhoneNumRsp> = {
  fromJSON(_: any): BindPhoneNumRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<BindPhoneNumRsp>, I>>(base?: I): BindPhoneNumRsp {
    return BindPhoneNumRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BindPhoneNumRsp>, I>>(_: I): BindPhoneNumRsp {
    const message = createBaseBindPhoneNumRsp();
    return message;
  }
};

function createBaseGetAccountInfoReq(): GetAccountInfoReq {
  return { uid: 0 };
}

export const GetAccountInfoReq: MessageFns<GetAccountInfoReq> = {
  fromJSON(object: any): GetAccountInfoReq {
    return { uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0 };
  },

  create<I extends Exact<DeepPartial<GetAccountInfoReq>, I>>(base?: I): GetAccountInfoReq {
    return GetAccountInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAccountInfoReq>, I>>(object: I): GetAccountInfoReq {
    const message = createBaseGetAccountInfoReq();
    message.uid = object.uid ?? 0;
    return message;
  }
};

function createBaseGetAccountInfoRsp(): GetAccountInfoRsp {
  return { info: undefined };
}

export const GetAccountInfoRsp: MessageFns<GetAccountInfoRsp> = {
  fromJSON(object: any): GetAccountInfoRsp {
    return { info: isSet(object.info) ? AccountInfo.fromJSON(object.info) : undefined };
  },

  create<I extends Exact<DeepPartial<GetAccountInfoRsp>, I>>(base?: I): GetAccountInfoRsp {
    return GetAccountInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetAccountInfoRsp>, I>>(object: I): GetAccountInfoRsp {
    const message = createBaseGetAccountInfoRsp();
    message.info = object.info !== undefined && object.info !== null ? AccountInfo.fromPartial(object.info) : undefined;
    return message;
  }
};

function createBaseBatchGetAccountInfoReq(): BatchGetAccountInfoReq {
  return { uids: [] };
}

export const BatchGetAccountInfoReq: MessageFns<BatchGetAccountInfoReq> = {
  fromJSON(object: any): BatchGetAccountInfoReq {
    return { uids: globalThis.Array.isArray(object?.uids) ? object.uids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetAccountInfoReq>, I>>(base?: I): BatchGetAccountInfoReq {
    return BatchGetAccountInfoReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetAccountInfoReq>, I>>(object: I): BatchGetAccountInfoReq {
    const message = createBaseBatchGetAccountInfoReq();
    message.uids = object.uids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetAccountInfoRsp(): BatchGetAccountInfoRsp {
  return { account_infos: {} };
}

export const BatchGetAccountInfoRsp: MessageFns<BatchGetAccountInfoRsp> = {
  fromJSON(object: any): BatchGetAccountInfoRsp {
    return {
      account_infos: isObject(object.account_infos)
        ? Object.entries(object.account_infos).reduce<{ [key: number]: AccountInfo }>((acc, [key, value]) => {
            acc[globalThis.Number(key)] = AccountInfo.fromJSON(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<BatchGetAccountInfoRsp>, I>>(base?: I): BatchGetAccountInfoRsp {
    return BatchGetAccountInfoRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetAccountInfoRsp>, I>>(object: I): BatchGetAccountInfoRsp {
    const message = createBaseBatchGetAccountInfoRsp();
    message.account_infos = Object.entries(object.account_infos ?? {}).reduce<{ [key: number]: AccountInfo }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[globalThis.Number(key)] = AccountInfo.fromPartial(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseBatchGetAccountInfoRsp_AccountInfosEntry(): BatchGetAccountInfoRsp_AccountInfosEntry {
  return { key: 0, value: undefined };
}

export const BatchGetAccountInfoRsp_AccountInfosEntry: MessageFns<BatchGetAccountInfoRsp_AccountInfosEntry> = {
  fromJSON(object: any): BatchGetAccountInfoRsp_AccountInfosEntry {
    return {
      key: isSet(object.key) ? globalThis.Number(object.key) : 0,
      value: isSet(object.value) ? AccountInfo.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<BatchGetAccountInfoRsp_AccountInfosEntry>, I>>(
    base?: I
  ): BatchGetAccountInfoRsp_AccountInfosEntry {
    return BatchGetAccountInfoRsp_AccountInfosEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetAccountInfoRsp_AccountInfosEntry>, I>>(
    object: I
  ): BatchGetAccountInfoRsp_AccountInfosEntry {
    const message = createBaseBatchGetAccountInfoRsp_AccountInfosEntry();
    message.key = object.key ?? 0;
    message.value =
      object.value !== undefined && object.value !== null ? AccountInfo.fromPartial(object.value) : undefined;
    return message;
  }
};

function createBaseAccountInfo(): AccountInfo {
  return {
    uid: 0,
    area: '',
    phone: '',
    account: '',
    status: 0,
    register_type: 0,
    register_device_info: undefined,
    register_time: 0,
    login_type: 0,
    login_device_info: undefined,
    login_time: 0
  };
}

export const AccountInfo: MessageFns<AccountInfo> = {
  fromJSON(object: any): AccountInfo {
    return {
      uid: isSet(object.uid) ? globalThis.Number(object.uid) : 0,
      area: isSet(object.area) ? globalThis.String(object.area) : '',
      phone: isSet(object.phone) ? globalThis.String(object.phone) : '',
      account: isSet(object.account) ? globalThis.String(object.account) : '',
      status: isSet(object.status) ? accountStatusFromJSON(object.status) : 0,
      register_type: isSet(object.register_type) ? loginTypeFromJSON(object.register_type) : 0,
      register_device_info: isSet(object.register_device_info)
        ? DeviceInfo.fromJSON(object.register_device_info)
        : undefined,
      register_time: isSet(object.register_time) ? globalThis.Number(object.register_time) : 0,
      login_type: isSet(object.login_type) ? loginTypeFromJSON(object.login_type) : 0,
      login_device_info: isSet(object.login_device_info) ? DeviceInfo.fromJSON(object.login_device_info) : undefined,
      login_time: isSet(object.login_time) ? globalThis.Number(object.login_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<AccountInfo>, I>>(base?: I): AccountInfo {
    return AccountInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AccountInfo>, I>>(object: I): AccountInfo {
    const message = createBaseAccountInfo();
    message.uid = object.uid ?? 0;
    message.area = object.area ?? '';
    message.phone = object.phone ?? '';
    message.account = object.account ?? '';
    message.status = object.status ?? 0;
    message.register_type = object.register_type ?? 0;
    message.register_device_info =
      object.register_device_info !== undefined && object.register_device_info !== null
        ? DeviceInfo.fromPartial(object.register_device_info)
        : undefined;
    message.register_time = object.register_time ?? 0;
    message.login_type = object.login_type ?? 0;
    message.login_device_info =
      object.login_device_info !== undefined && object.login_device_info !== null
        ? DeviceInfo.fromPartial(object.login_device_info)
        : undefined;
    message.login_time = object.login_time ?? 0;
    return message;
  }
};

function createBaseBanLog(): BanLog {
  return { id: 0, expired_at: 0, reason: '', operator: '', ban_op_type: 0, ban_type: 0, ban_type_content: '' };
}

export const BanLog: MessageFns<BanLog> = {
  fromJSON(object: any): BanLog {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      expired_at: isSet(object.expired_at) ? globalThis.Number(object.expired_at) : 0,
      reason: isSet(object.reason) ? globalThis.String(object.reason) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      ban_op_type: isSet(object.ban_op_type) ? accountBanOptTypeFromJSON(object.ban_op_type) : 0,
      ban_type: isSet(object.ban_type) ? accountBanTypeFromJSON(object.ban_type) : 0,
      ban_type_content: isSet(object.ban_type_content) ? globalThis.String(object.ban_type_content) : ''
    };
  },

  create<I extends Exact<DeepPartial<BanLog>, I>>(base?: I): BanLog {
    return BanLog.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BanLog>, I>>(object: I): BanLog {
    const message = createBaseBanLog();
    message.id = object.id ?? 0;
    message.expired_at = object.expired_at ?? 0;
    message.reason = object.reason ?? '';
    message.operator = object.operator ?? '';
    message.ban_op_type = object.ban_op_type ?? 0;
    message.ban_type = object.ban_type ?? 0;
    message.ban_type_content = object.ban_type_content ?? '';
    return message;
  }
};

function createBaseGetBanLogReq(): GetBanLogReq {
  return { items: [] };
}

export const GetBanLogReq: MessageFns<GetBanLogReq> = {
  fromJSON(object: any): GetBanLogReq {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => GetBanLogReq_Item.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<GetBanLogReq>, I>>(base?: I): GetBanLogReq {
    return GetBanLogReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBanLogReq>, I>>(object: I): GetBanLogReq {
    const message = createBaseGetBanLogReq();
    message.items = object.items?.map(e => GetBanLogReq_Item.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetBanLogReq_Item(): GetBanLogReq_Item {
  return { ban_type: 0, ban_type_content: '' };
}

export const GetBanLogReq_Item: MessageFns<GetBanLogReq_Item> = {
  fromJSON(object: any): GetBanLogReq_Item {
    return {
      ban_type: isSet(object.ban_type) ? accountBanTypeFromJSON(object.ban_type) : 0,
      ban_type_content: isSet(object.ban_type_content) ? globalThis.String(object.ban_type_content) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetBanLogReq_Item>, I>>(base?: I): GetBanLogReq_Item {
    return GetBanLogReq_Item.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBanLogReq_Item>, I>>(object: I): GetBanLogReq_Item {
    const message = createBaseGetBanLogReq_Item();
    message.ban_type = object.ban_type ?? 0;
    message.ban_type_content = object.ban_type_content ?? '';
    return message;
  }
};

function createBaseGetBanLogRsp(): GetBanLogRsp {
  return { logs: [] };
}

export const GetBanLogRsp: MessageFns<GetBanLogRsp> = {
  fromJSON(object: any): GetBanLogRsp {
    return { logs: globalThis.Array.isArray(object?.logs) ? object.logs.map((e: any) => BanLog.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<GetBanLogRsp>, I>>(base?: I): GetBanLogRsp {
    return GetBanLogRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetBanLogRsp>, I>>(object: I): GetBanLogRsp {
    const message = createBaseGetBanLogRsp();
    message.logs = object.logs?.map(e => BanLog.fromPartial(e)) || [];
    return message;
  }
};

function createBaseDeviceInfo(): DeviceInfo {
  return {
    ip: '',
    lan: '',
    cou: '',
    did: '',
    pf: '',
    verc: '',
    mod: '',
    brand: '',
    os: '',
    os_ver: '',
    os_verc: '',
    pkg: '',
    sim: ''
  };
}

export const DeviceInfo: MessageFns<DeviceInfo> = {
  fromJSON(object: any): DeviceInfo {
    return {
      ip: isSet(object.ip) ? globalThis.String(object.ip) : '',
      lan: isSet(object.lan) ? globalThis.String(object.lan) : '',
      cou: isSet(object.cou) ? globalThis.String(object.cou) : '',
      did: isSet(object.did) ? globalThis.String(object.did) : '',
      pf: isSet(object.pf) ? globalThis.String(object.pf) : '',
      verc: isSet(object.verc) ? globalThis.String(object.verc) : '',
      mod: isSet(object.mod) ? globalThis.String(object.mod) : '',
      brand: isSet(object.brand) ? globalThis.String(object.brand) : '',
      os: isSet(object.os) ? globalThis.String(object.os) : '',
      os_ver: isSet(object.os_ver) ? globalThis.String(object.os_ver) : '',
      os_verc: isSet(object.os_verc) ? globalThis.String(object.os_verc) : '',
      pkg: isSet(object.pkg) ? globalThis.String(object.pkg) : '',
      sim: isSet(object.sim) ? globalThis.String(object.sim) : ''
    };
  },

  create<I extends Exact<DeepPartial<DeviceInfo>, I>>(base?: I): DeviceInfo {
    return DeviceInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DeviceInfo>, I>>(object: I): DeviceInfo {
    const message = createBaseDeviceInfo();
    message.ip = object.ip ?? '';
    message.lan = object.lan ?? '';
    message.cou = object.cou ?? '';
    message.did = object.did ?? '';
    message.pf = object.pf ?? '';
    message.verc = object.verc ?? '';
    message.mod = object.mod ?? '';
    message.brand = object.brand ?? '';
    message.os = object.os ?? '';
    message.os_ver = object.os_ver ?? '';
    message.os_verc = object.os_verc ?? '';
    message.pkg = object.pkg ?? '';
    message.sim = object.sim ?? '';
    return message;
  }
};

function createBaseBatchGetAccountsByDidsReq(): BatchGetAccountsByDidsReq {
  return { dids: [] };
}

export const BatchGetAccountsByDidsReq: MessageFns<BatchGetAccountsByDidsReq> = {
  fromJSON(object: any): BatchGetAccountsByDidsReq {
    return { dids: globalThis.Array.isArray(object?.dids) ? object.dids.map((e: any) => globalThis.String(e)) : [] };
  },

  create<I extends Exact<DeepPartial<BatchGetAccountsByDidsReq>, I>>(base?: I): BatchGetAccountsByDidsReq {
    return BatchGetAccountsByDidsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetAccountsByDidsReq>, I>>(object: I): BatchGetAccountsByDidsReq {
    const message = createBaseBatchGetAccountsByDidsReq();
    message.dids = object.dids?.map(e => e) || [];
    return message;
  }
};

function createBaseBatchGetAccountsByDidsRsp(): BatchGetAccountsByDidsRsp {
  return { account_infos: [] };
}

export const BatchGetAccountsByDidsRsp: MessageFns<BatchGetAccountsByDidsRsp> = {
  fromJSON(object: any): BatchGetAccountsByDidsRsp {
    return {
      account_infos: globalThis.Array.isArray(object?.account_infos)
        ? object.account_infos.map((e: any) => AccountInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<BatchGetAccountsByDidsRsp>, I>>(base?: I): BatchGetAccountsByDidsRsp {
    return BatchGetAccountsByDidsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BatchGetAccountsByDidsRsp>, I>>(object: I): BatchGetAccountsByDidsRsp {
    const message = createBaseBatchGetAccountsByDidsRsp();
    message.account_infos = object.account_infos?.map(e => AccountInfo.fromPartial(e)) || [];
    return message;
  }
};

export type AccountMgrDefinition = typeof AccountMgrDefinition;
export const AccountMgrDefinition = {
  name: 'AccountMgr',
  fullName: 'comm.mgr.account.AccountMgr',
  methods: {
    /** 封禁用户操作 */
    banAccount: {
      name: 'BanAccount',
      requestType: BanAccountReq,
      requestStream: false,
      responseType: BanAccountRsp,
      responseStream: false,
      options: {}
    },
    /** 查询已封禁账户列表 */
    listBannedAccount: {
      name: 'ListBannedAccount',
      requestType: ListBannedAccountReq,
      requestStream: false,
      responseType: ListBannedAccountRsp,
      responseStream: false,
      options: {}
    },
    /** 快速注销三方注册用户 */
    quickWithdrawThirdAccount: {
      name: 'QuickWithdrawThirdAccount',
      requestType: QuickWithdrawThirdAccountReq,
      requestStream: false,
      responseType: QuickWithdrawThirdAccountRsp,
      responseStream: false,
      options: {}
    },
    /** 绑定手机号 */
    bindPhoneNum: {
      name: 'BindPhoneNum',
      requestType: BindPhoneNumReq,
      requestStream: false,
      responseType: BindPhoneNumRsp,
      responseStream: false,
      options: {}
    },
    /** 获取账号信息 */
    getAccountInfo: {
      name: 'GetAccountInfo',
      requestType: GetAccountInfoReq,
      requestStream: false,
      responseType: GetAccountInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 查询最新的封禁/解封日志 */
    getLatestBanLog: {
      name: 'GetLatestBanLog',
      requestType: GetBanLogReq,
      requestStream: false,
      responseType: GetBanLogRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取账号信息 */
    batchGetAccountInfo: {
      name: 'BatchGetAccountInfo',
      requestType: BatchGetAccountInfoReq,
      requestStream: false,
      responseType: BatchGetAccountInfoRsp,
      responseStream: false,
      options: {}
    },
    /** 批量获取账号信息通过did */
    batchGetAccountsByDids: {
      name: 'BatchGetAccountsByDids',
      requestType: BatchGetAccountsByDidsReq,
      requestStream: false,
      responseType: BatchGetAccountsByDidsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
