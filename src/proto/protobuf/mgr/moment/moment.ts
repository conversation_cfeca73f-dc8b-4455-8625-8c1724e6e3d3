// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/moment/moment.proto

/* eslint-disable */
import { MomentDraft as MomentDraft3 } from '../../api/moment/moment-api';
import { Comment as Comment2, CommentStats, Moment as Moment1, MomentStats } from '../../api/moment/moment-comm';

export const protobufPackage = 'comm.mgr.moment';

/** 动态 */
export interface Moment {
  /** C端可见信息 */
  base_info: Moment1 | undefined;
  /** 真实统计数据 */
  real_stats: MomentStats | undefined;
}

/** 评论 */
export interface Comment {
  /** C端可见信息 */
  base_info: Comment2 | undefined;
  /** 真实统计数据 */
  real_stats: CommentStats | undefined;
}

/** 发布动态的草稿 */
export interface MomentDraft {
  /** C端的结构，是必须的 */
  draft: MomentDraft3 | undefined;
  /** 发布时间 */
  published_at: number;
  /** 自定义属性, 因为这块会被中台服务端和 OMS 端读取, 所以定义成 map<string, string> 结构便于使用. */
  attribute: { [key: string]: string };
}

export interface MomentDraft_AttributeEntry {
  key: string;
  value: string;
}

function createBaseMoment(): Moment {
  return { base_info: undefined, real_stats: undefined };
}

export const Moment: MessageFns<Moment> = {
  fromJSON(object: any): Moment {
    return {
      base_info: isSet(object.base_info) ? Moment1.fromJSON(object.base_info) : undefined,
      real_stats: isSet(object.real_stats) ? MomentStats.fromJSON(object.real_stats) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Moment>, I>>(base?: I): Moment {
    return Moment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Moment>, I>>(object: I): Moment {
    const message = createBaseMoment();
    message.base_info =
      object.base_info !== undefined && object.base_info !== null ? Moment1.fromPartial(object.base_info) : undefined;
    message.real_stats =
      object.real_stats !== undefined && object.real_stats !== null
        ? MomentStats.fromPartial(object.real_stats)
        : undefined;
    return message;
  }
};

function createBaseComment(): Comment {
  return { base_info: undefined, real_stats: undefined };
}

export const Comment: MessageFns<Comment> = {
  fromJSON(object: any): Comment {
    return {
      base_info: isSet(object.base_info) ? Comment2.fromJSON(object.base_info) : undefined,
      real_stats: isSet(object.real_stats) ? CommentStats.fromJSON(object.real_stats) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Comment>, I>>(base?: I): Comment {
    return Comment.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Comment>, I>>(object: I): Comment {
    const message = createBaseComment();
    message.base_info =
      object.base_info !== undefined && object.base_info !== null ? Comment2.fromPartial(object.base_info) : undefined;
    message.real_stats =
      object.real_stats !== undefined && object.real_stats !== null
        ? CommentStats.fromPartial(object.real_stats)
        : undefined;
    return message;
  }
};

function createBaseMomentDraft(): MomentDraft {
  return { draft: undefined, published_at: 0, attribute: {} };
}

export const MomentDraft: MessageFns<MomentDraft> = {
  fromJSON(object: any): MomentDraft {
    return {
      draft: isSet(object.draft) ? MomentDraft3.fromJSON(object.draft) : undefined,
      published_at: isSet(object.published_at) ? globalThis.Number(object.published_at) : 0,
      attribute: isObject(object.attribute)
        ? Object.entries(object.attribute).reduce<{ [key: string]: string }>((acc, [key, value]) => {
            acc[key] = String(value);
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<MomentDraft>, I>>(base?: I): MomentDraft {
    return MomentDraft.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MomentDraft>, I>>(object: I): MomentDraft {
    const message = createBaseMomentDraft();
    message.draft =
      object.draft !== undefined && object.draft !== null ? MomentDraft3.fromPartial(object.draft) : undefined;
    message.published_at = object.published_at ?? 0;
    message.attribute = Object.entries(object.attribute ?? {}).reduce<{ [key: string]: string }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = globalThis.String(value);
        }
        return acc;
      },
      {}
    );
    return message;
  }
};

function createBaseMomentDraft_AttributeEntry(): MomentDraft_AttributeEntry {
  return { key: '', value: '' };
}

export const MomentDraft_AttributeEntry: MessageFns<MomentDraft_AttributeEntry> = {
  fromJSON(object: any): MomentDraft_AttributeEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : ''
    };
  },

  create<I extends Exact<DeepPartial<MomentDraft_AttributeEntry>, I>>(base?: I): MomentDraft_AttributeEntry {
    return MomentDraft_AttributeEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<MomentDraft_AttributeEntry>, I>>(object: I): MomentDraft_AttributeEntry {
    const message = createBaseMomentDraft_AttributeEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
