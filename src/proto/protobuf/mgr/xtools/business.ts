// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/xtools/business.proto

/* eslint-disable */

export const protobufPackage = 'mgr.pbxtools';

export interface BusinessInfo {
  /** 业务Id */
  id: number;
  /** 业务名称，创建后不可更改，比如halame/ahchat/lucky等 */
  name: string;
  /** 描述 */
  desc: string;
  /** 负责人-邮箱 */
  owner_email: string;
  /** 维护人-邮箱 */
  maintainers_email: string[];
  /** 创建人 */
  creator: string;
  /** 创建时间，unix timestamp, seconds */
  created_at: number;
  /** 更新人 */
  updater: string;
  /** 更新时间 */
  updated_at: number;
  /** 负责人-unionid */
  maintainers_unionid: string[];
  /** 业务的告警webhook */
  alarm_webhook: string;
}

export interface CreateBusinessReq {
  /** 业务名称 */
  name: string;
  /** 描述 */
  desc: string;
  /** 负责人邮箱 */
  owner_email: string;
  /** 维护人-邮箱 */
  maintainers_email: string[];
  /** 负责人-unionid */
  maintainers_unionid: string[];
  /** 业务的告警webhook */
  alarm_webhook: string;
}

export interface CreateBusinessRsp {
  /** 业务Id */
  id: number;
}

export interface EditBusinessReq {
  /** 业务Id */
  id: number;
  /** 描述(可修改) */
  desc: string;
  /** 负责人邮箱(可修改) */
  owner_email: string;
  /** 维护人-邮箱(可修改) */
  maintainers_email: string[];
  /** 负责人-unionid */
  maintainers_unionid: string[];
  /** 业务的告警webhook */
  alarm_webhook: string;
}

export interface EditBusinessRsp {}

export interface ListBusinessReq {}

export interface ListBusinessRsp {
  /** 业务列表 */
  items: BusinessInfo[];
}

export interface ListBusinessByOwnReq {}

export interface ListBusinessByOwnRsp {
  /** 业务列表 */
  items: BusinessInfo[];
}

export interface GetWebhookByServiceReq {
  /** 比如hala-api */
  service_name: string;
  /**  */
  user_email: string;
}

export interface GetWebhookByServiceRsp {
  /** 业务配置的告警webhook，可能为空 */
  webhook: string;
  /** 告警webhook公签，可能为空 */
  webhook_secret: string;
  /** user_email的手机号 */
  user_phone: string;
}

function createBaseBusinessInfo(): BusinessInfo {
  return {
    id: 0,
    name: '',
    desc: '',
    owner_email: '',
    maintainers_email: [],
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0,
    maintainers_unionid: [],
    alarm_webhook: ''
  };
}

export const BusinessInfo: MessageFns<BusinessInfo> = {
  fromJSON(object: any): BusinessInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : [],
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0,
      maintainers_unionid: globalThis.Array.isArray(object?.maintainers_unionid)
        ? object.maintainers_unionid.map((e: any) => globalThis.String(e))
        : [],
      alarm_webhook: isSet(object.alarm_webhook) ? globalThis.String(object.alarm_webhook) : ''
    };
  },

  create<I extends Exact<DeepPartial<BusinessInfo>, I>>(base?: I): BusinessInfo {
    return BusinessInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<BusinessInfo>, I>>(object: I): BusinessInfo {
    const message = createBaseBusinessInfo();
    message.id = object.id ?? 0;
    message.name = object.name ?? '';
    message.desc = object.desc ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    message.maintainers_unionid = object.maintainers_unionid?.map(e => e) || [];
    message.alarm_webhook = object.alarm_webhook ?? '';
    return message;
  }
};

function createBaseCreateBusinessReq(): CreateBusinessReq {
  return { name: '', desc: '', owner_email: '', maintainers_email: [], maintainers_unionid: [], alarm_webhook: '' };
}

export const CreateBusinessReq: MessageFns<CreateBusinessReq> = {
  fromJSON(object: any): CreateBusinessReq {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : [],
      maintainers_unionid: globalThis.Array.isArray(object?.maintainers_unionid)
        ? object.maintainers_unionid.map((e: any) => globalThis.String(e))
        : [],
      alarm_webhook: isSet(object.alarm_webhook) ? globalThis.String(object.alarm_webhook) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateBusinessReq>, I>>(base?: I): CreateBusinessReq {
    return CreateBusinessReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBusinessReq>, I>>(object: I): CreateBusinessReq {
    const message = createBaseCreateBusinessReq();
    message.name = object.name ?? '';
    message.desc = object.desc ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    message.maintainers_unionid = object.maintainers_unionid?.map(e => e) || [];
    message.alarm_webhook = object.alarm_webhook ?? '';
    return message;
  }
};

function createBaseCreateBusinessRsp(): CreateBusinessRsp {
  return { id: 0 };
}

export const CreateBusinessRsp: MessageFns<CreateBusinessRsp> = {
  fromJSON(object: any): CreateBusinessRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateBusinessRsp>, I>>(base?: I): CreateBusinessRsp {
    return CreateBusinessRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateBusinessRsp>, I>>(object: I): CreateBusinessRsp {
    const message = createBaseCreateBusinessRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseEditBusinessReq(): EditBusinessReq {
  return { id: 0, desc: '', owner_email: '', maintainers_email: [], maintainers_unionid: [], alarm_webhook: '' };
}

export const EditBusinessReq: MessageFns<EditBusinessReq> = {
  fromJSON(object: any): EditBusinessReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : [],
      maintainers_unionid: globalThis.Array.isArray(object?.maintainers_unionid)
        ? object.maintainers_unionid.map((e: any) => globalThis.String(e))
        : [],
      alarm_webhook: isSet(object.alarm_webhook) ? globalThis.String(object.alarm_webhook) : ''
    };
  },

  create<I extends Exact<DeepPartial<EditBusinessReq>, I>>(base?: I): EditBusinessReq {
    return EditBusinessReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditBusinessReq>, I>>(object: I): EditBusinessReq {
    const message = createBaseEditBusinessReq();
    message.id = object.id ?? 0;
    message.desc = object.desc ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    message.maintainers_unionid = object.maintainers_unionid?.map(e => e) || [];
    message.alarm_webhook = object.alarm_webhook ?? '';
    return message;
  }
};

function createBaseEditBusinessRsp(): EditBusinessRsp {
  return {};
}

export const EditBusinessRsp: MessageFns<EditBusinessRsp> = {
  fromJSON(_: any): EditBusinessRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<EditBusinessRsp>, I>>(base?: I): EditBusinessRsp {
    return EditBusinessRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditBusinessRsp>, I>>(_: I): EditBusinessRsp {
    const message = createBaseEditBusinessRsp();
    return message;
  }
};

function createBaseListBusinessReq(): ListBusinessReq {
  return {};
}

export const ListBusinessReq: MessageFns<ListBusinessReq> = {
  fromJSON(_: any): ListBusinessReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListBusinessReq>, I>>(base?: I): ListBusinessReq {
    return ListBusinessReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBusinessReq>, I>>(_: I): ListBusinessReq {
    const message = createBaseListBusinessReq();
    return message;
  }
};

function createBaseListBusinessRsp(): ListBusinessRsp {
  return { items: [] };
}

export const ListBusinessRsp: MessageFns<ListBusinessRsp> = {
  fromJSON(object: any): ListBusinessRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => BusinessInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListBusinessRsp>, I>>(base?: I): ListBusinessRsp {
    return ListBusinessRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBusinessRsp>, I>>(object: I): ListBusinessRsp {
    const message = createBaseListBusinessRsp();
    message.items = object.items?.map(e => BusinessInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseListBusinessByOwnReq(): ListBusinessByOwnReq {
  return {};
}

export const ListBusinessByOwnReq: MessageFns<ListBusinessByOwnReq> = {
  fromJSON(_: any): ListBusinessByOwnReq {
    return {};
  },

  create<I extends Exact<DeepPartial<ListBusinessByOwnReq>, I>>(base?: I): ListBusinessByOwnReq {
    return ListBusinessByOwnReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBusinessByOwnReq>, I>>(_: I): ListBusinessByOwnReq {
    const message = createBaseListBusinessByOwnReq();
    return message;
  }
};

function createBaseListBusinessByOwnRsp(): ListBusinessByOwnRsp {
  return { items: [] };
}

export const ListBusinessByOwnRsp: MessageFns<ListBusinessByOwnRsp> = {
  fromJSON(object: any): ListBusinessByOwnRsp {
    return {
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => BusinessInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListBusinessByOwnRsp>, I>>(base?: I): ListBusinessByOwnRsp {
    return ListBusinessByOwnRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListBusinessByOwnRsp>, I>>(object: I): ListBusinessByOwnRsp {
    const message = createBaseListBusinessByOwnRsp();
    message.items = object.items?.map(e => BusinessInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetWebhookByServiceReq(): GetWebhookByServiceReq {
  return { service_name: '', user_email: '' };
}

export const GetWebhookByServiceReq: MessageFns<GetWebhookByServiceReq> = {
  fromJSON(object: any): GetWebhookByServiceReq {
    return {
      service_name: isSet(object.service_name) ? globalThis.String(object.service_name) : '',
      user_email: isSet(object.user_email) ? globalThis.String(object.user_email) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetWebhookByServiceReq>, I>>(base?: I): GetWebhookByServiceReq {
    return GetWebhookByServiceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetWebhookByServiceReq>, I>>(object: I): GetWebhookByServiceReq {
    const message = createBaseGetWebhookByServiceReq();
    message.service_name = object.service_name ?? '';
    message.user_email = object.user_email ?? '';
    return message;
  }
};

function createBaseGetWebhookByServiceRsp(): GetWebhookByServiceRsp {
  return { webhook: '', webhook_secret: '', user_phone: '' };
}

export const GetWebhookByServiceRsp: MessageFns<GetWebhookByServiceRsp> = {
  fromJSON(object: any): GetWebhookByServiceRsp {
    return {
      webhook: isSet(object.webhook) ? globalThis.String(object.webhook) : '',
      webhook_secret: isSet(object.webhook_secret) ? globalThis.String(object.webhook_secret) : '',
      user_phone: isSet(object.user_phone) ? globalThis.String(object.user_phone) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetWebhookByServiceRsp>, I>>(base?: I): GetWebhookByServiceRsp {
    return GetWebhookByServiceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetWebhookByServiceRsp>, I>>(object: I): GetWebhookByServiceRsp {
    const message = createBaseGetWebhookByServiceRsp();
    message.webhook = object.webhook ?? '';
    message.webhook_secret = object.webhook_secret ?? '';
    message.user_phone = object.user_phone ?? '';
    return message;
  }
};

/**
 * 业务服务
 * smicro:spath=gitit.cc/social/components-service/xtools/handler/business
 */
export type BusinessDefinition = typeof BusinessDefinition;
export const BusinessDefinition = {
  name: 'Business',
  fullName: 'mgr.pbxtools.Business',
  methods: {
    /** 创建业务 */
    createBusiness: {
      name: 'CreateBusiness',
      requestType: CreateBusinessReq,
      requestStream: false,
      responseType: CreateBusinessRsp,
      responseStream: false,
      options: {}
    },
    /** 编辑业务 */
    editBusiness: {
      name: 'EditBusiness',
      requestType: EditBusinessReq,
      requestStream: false,
      responseType: EditBusinessRsp,
      responseStream: false,
      options: {}
    },
    /** 查询业务 */
    listBusiness: {
      name: 'ListBusiness',
      requestType: ListBusinessReq,
      requestStream: false,
      responseType: ListBusinessRsp,
      responseStream: false,
      options: {}
    },
    /** 查询业务(拥有权限的业务) */
    listBusinessByOwn: {
      name: 'ListBusinessByOwn',
      requestType: ListBusinessByOwnReq,
      requestStream: false,
      responseType: ListBusinessByOwnRsp,
      responseStream: false,
      options: {}
    },
    /** 通过服务名获取业务告警webhook */
    getWebhookByService: {
      name: 'GetWebhookByService',
      requestType: GetWebhookByServiceReq,
      requestStream: false,
      responseType: GetWebhookByServiceRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
