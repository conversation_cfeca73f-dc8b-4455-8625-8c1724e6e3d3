// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/xtools/service.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'mgr.pbxtools';

export interface ServiceInfo {
  /** 服务Id */
  id: number;
  /** 业务Id */
  business_id: number;
  /** 业务名称 */
  business_name: string;
  /** 服务名称，一旦创建不可更改，比如hala-api/hala-family/ahchat-api等 */
  name: string;
  /** smicro服务名称 */
  smicro_name: string;
  /** 服务类型(1:云主机,2:K8S) */
  server_type: number;
  /** 服务描述 */
  desc: string;
  /** 服务git地址 */
  git_url: string;
  /** 服务api url */
  api_url: string;
  /** 负责人-邮箱 */
  owner_email: string;
  /** 维护人-邮箱 */
  maintainers_email: string[];
  /** 创建人 */
  creator: string;
  /** 创建时间，unix timestamp, seconds */
  created_at: number;
  /** 更新人 */
  updater: string;
  /** 更新时间 */
  updated_at: number;
}

export interface CreateServiceReq {
  /** 业务Id */
  business_id: number;
  /** 服务名称，一旦创建不可更改，比如hala-api/hala-family/ahchat-api等 */
  name: string;
  /** smicro服务名称 */
  smicro_name: string;
  /** 服务类型(1:云主机,2:K8S) */
  server_type: number;
  /** 服务描述 */
  desc: string;
  /** 服务git地址 */
  git_url: string;
  /** 服务api url */
  api_url: string;
  /** 负责人-邮箱 */
  owner_email: string;
  /** 维护人-邮箱 */
  maintainers_email: string[];
}

export interface CreateServiceRsp {
  /** 服务Id */
  id: number;
}

export interface EditServiceReq {
  /** 服务Id */
  id: number;
  /** smicro服务名称 */
  smicro_name: string;
  /** 服务类型(1:云主机,2:K8S) */
  server_type: number;
  /** 服务描述 */
  desc: string;
  /** 服务git地址 */
  git_url: string;
  /** 服务api url */
  api_url: string;
  /** 负责人-邮箱 */
  owner_email: string;
  /** 维护人-邮箱 */
  maintainers_email: string[];
}

export interface EditServiceRsp {}

export interface ListServiceReq {
  page: Page | undefined;
  /** 业务Id */
  business_id: number;
}

export interface ListServiceRsp {
  page: Page | undefined;
  items: ServiceInfo[];
}

export interface RunningStatus {
  /** 概要信息 */
  summer: string;
  /** 详细信息 */
  detail: string;
  /** 连接terminal的token */
  term_token: string;
}

export interface ListRunningStatusReq {
  /** 服务Id */
  id: number;
}

export interface ListRunningStatusRsp {
  statuses: RunningStatus[];
}

export interface GetServiceReq {
  /** 服务Id */
  id: number;
}

export interface GetServiceRsp {
  serviceInfo: ServiceInfo | undefined;
}

function createBaseServiceInfo(): ServiceInfo {
  return {
    id: 0,
    business_id: 0,
    business_name: '',
    name: '',
    smicro_name: '',
    server_type: 0,
    desc: '',
    git_url: '',
    api_url: '',
    owner_email: '',
    maintainers_email: [],
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const ServiceInfo: MessageFns<ServiceInfo> = {
  fromJSON(object: any): ServiceInfo {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      business_id: isSet(object.business_id) ? globalThis.Number(object.business_id) : 0,
      business_name: isSet(object.business_name) ? globalThis.String(object.business_name) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      smicro_name: isSet(object.smicro_name) ? globalThis.String(object.smicro_name) : '',
      server_type: isSet(object.server_type) ? globalThis.Number(object.server_type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      git_url: isSet(object.git_url) ? globalThis.String(object.git_url) : '',
      api_url: isSet(object.api_url) ? globalThis.String(object.api_url) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : [],
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<ServiceInfo>, I>>(base?: I): ServiceInfo {
    return ServiceInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServiceInfo>, I>>(object: I): ServiceInfo {
    const message = createBaseServiceInfo();
    message.id = object.id ?? 0;
    message.business_id = object.business_id ?? 0;
    message.business_name = object.business_name ?? '';
    message.name = object.name ?? '';
    message.smicro_name = object.smicro_name ?? '';
    message.server_type = object.server_type ?? 0;
    message.desc = object.desc ?? '';
    message.git_url = object.git_url ?? '';
    message.api_url = object.api_url ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseCreateServiceReq(): CreateServiceReq {
  return {
    business_id: 0,
    name: '',
    smicro_name: '',
    server_type: 0,
    desc: '',
    git_url: '',
    api_url: '',
    owner_email: '',
    maintainers_email: []
  };
}

export const CreateServiceReq: MessageFns<CreateServiceReq> = {
  fromJSON(object: any): CreateServiceReq {
    return {
      business_id: isSet(object.business_id) ? globalThis.Number(object.business_id) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      smicro_name: isSet(object.smicro_name) ? globalThis.String(object.smicro_name) : '',
      server_type: isSet(object.server_type) ? globalThis.Number(object.server_type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      git_url: isSet(object.git_url) ? globalThis.String(object.git_url) : '',
      api_url: isSet(object.api_url) ? globalThis.String(object.api_url) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<CreateServiceReq>, I>>(base?: I): CreateServiceReq {
    return CreateServiceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateServiceReq>, I>>(object: I): CreateServiceReq {
    const message = createBaseCreateServiceReq();
    message.business_id = object.business_id ?? 0;
    message.name = object.name ?? '';
    message.smicro_name = object.smicro_name ?? '';
    message.server_type = object.server_type ?? 0;
    message.desc = object.desc ?? '';
    message.git_url = object.git_url ?? '';
    message.api_url = object.api_url ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    return message;
  }
};

function createBaseCreateServiceRsp(): CreateServiceRsp {
  return { id: 0 };
}

export const CreateServiceRsp: MessageFns<CreateServiceRsp> = {
  fromJSON(object: any): CreateServiceRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateServiceRsp>, I>>(base?: I): CreateServiceRsp {
    return CreateServiceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateServiceRsp>, I>>(object: I): CreateServiceRsp {
    const message = createBaseCreateServiceRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseEditServiceReq(): EditServiceReq {
  return {
    id: 0,
    smicro_name: '',
    server_type: 0,
    desc: '',
    git_url: '',
    api_url: '',
    owner_email: '',
    maintainers_email: []
  };
}

export const EditServiceReq: MessageFns<EditServiceReq> = {
  fromJSON(object: any): EditServiceReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      smicro_name: isSet(object.smicro_name) ? globalThis.String(object.smicro_name) : '',
      server_type: isSet(object.server_type) ? globalThis.Number(object.server_type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      git_url: isSet(object.git_url) ? globalThis.String(object.git_url) : '',
      api_url: isSet(object.api_url) ? globalThis.String(object.api_url) : '',
      owner_email: isSet(object.owner_email) ? globalThis.String(object.owner_email) : '',
      maintainers_email: globalThis.Array.isArray(object?.maintainers_email)
        ? object.maintainers_email.map((e: any) => globalThis.String(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<EditServiceReq>, I>>(base?: I): EditServiceReq {
    return EditServiceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditServiceReq>, I>>(object: I): EditServiceReq {
    const message = createBaseEditServiceReq();
    message.id = object.id ?? 0;
    message.smicro_name = object.smicro_name ?? '';
    message.server_type = object.server_type ?? 0;
    message.desc = object.desc ?? '';
    message.git_url = object.git_url ?? '';
    message.api_url = object.api_url ?? '';
    message.owner_email = object.owner_email ?? '';
    message.maintainers_email = object.maintainers_email?.map(e => e) || [];
    return message;
  }
};

function createBaseEditServiceRsp(): EditServiceRsp {
  return {};
}

export const EditServiceRsp: MessageFns<EditServiceRsp> = {
  fromJSON(_: any): EditServiceRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<EditServiceRsp>, I>>(base?: I): EditServiceRsp {
    return EditServiceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditServiceRsp>, I>>(_: I): EditServiceRsp {
    const message = createBaseEditServiceRsp();
    return message;
  }
};

function createBaseListServiceReq(): ListServiceReq {
  return { page: undefined, business_id: 0 };
}

export const ListServiceReq: MessageFns<ListServiceReq> = {
  fromJSON(object: any): ListServiceReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      business_id: isSet(object.business_id) ? globalThis.Number(object.business_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListServiceReq>, I>>(base?: I): ListServiceReq {
    return ListServiceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListServiceReq>, I>>(object: I): ListServiceReq {
    const message = createBaseListServiceReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.business_id = object.business_id ?? 0;
    return message;
  }
};

function createBaseListServiceRsp(): ListServiceRsp {
  return { page: undefined, items: [] };
}

export const ListServiceRsp: MessageFns<ListServiceRsp> = {
  fromJSON(object: any): ListServiceRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      items: globalThis.Array.isArray(object?.items) ? object.items.map((e: any) => ServiceInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListServiceRsp>, I>>(base?: I): ListServiceRsp {
    return ListServiceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListServiceRsp>, I>>(object: I): ListServiceRsp {
    const message = createBaseListServiceRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.items = object.items?.map(e => ServiceInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseRunningStatus(): RunningStatus {
  return { summer: '', detail: '', term_token: '' };
}

export const RunningStatus: MessageFns<RunningStatus> = {
  fromJSON(object: any): RunningStatus {
    return {
      summer: isSet(object.summer) ? globalThis.String(object.summer) : '',
      detail: isSet(object.detail) ? globalThis.String(object.detail) : '',
      term_token: isSet(object.term_token) ? globalThis.String(object.term_token) : ''
    };
  },

  create<I extends Exact<DeepPartial<RunningStatus>, I>>(base?: I): RunningStatus {
    return RunningStatus.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RunningStatus>, I>>(object: I): RunningStatus {
    const message = createBaseRunningStatus();
    message.summer = object.summer ?? '';
    message.detail = object.detail ?? '';
    message.term_token = object.term_token ?? '';
    return message;
  }
};

function createBaseListRunningStatusReq(): ListRunningStatusReq {
  return { id: 0 };
}

export const ListRunningStatusReq: MessageFns<ListRunningStatusReq> = {
  fromJSON(object: any): ListRunningStatusReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<ListRunningStatusReq>, I>>(base?: I): ListRunningStatusReq {
    return ListRunningStatusReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRunningStatusReq>, I>>(object: I): ListRunningStatusReq {
    const message = createBaseListRunningStatusReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListRunningStatusRsp(): ListRunningStatusRsp {
  return { statuses: [] };
}

export const ListRunningStatusRsp: MessageFns<ListRunningStatusRsp> = {
  fromJSON(object: any): ListRunningStatusRsp {
    return {
      statuses: globalThis.Array.isArray(object?.statuses)
        ? object.statuses.map((e: any) => RunningStatus.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListRunningStatusRsp>, I>>(base?: I): ListRunningStatusRsp {
    return ListRunningStatusRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListRunningStatusRsp>, I>>(object: I): ListRunningStatusRsp {
    const message = createBaseListRunningStatusRsp();
    message.statuses = object.statuses?.map(e => RunningStatus.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetServiceReq(): GetServiceReq {
  return { id: 0 };
}

export const GetServiceReq: MessageFns<GetServiceReq> = {
  fromJSON(object: any): GetServiceReq {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<GetServiceReq>, I>>(base?: I): GetServiceReq {
    return GetServiceReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetServiceReq>, I>>(object: I): GetServiceReq {
    const message = createBaseGetServiceReq();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseGetServiceRsp(): GetServiceRsp {
  return { serviceInfo: undefined };
}

export const GetServiceRsp: MessageFns<GetServiceRsp> = {
  fromJSON(object: any): GetServiceRsp {
    return { serviceInfo: isSet(object.serviceInfo) ? ServiceInfo.fromJSON(object.serviceInfo) : undefined };
  },

  create<I extends Exact<DeepPartial<GetServiceRsp>, I>>(base?: I): GetServiceRsp {
    return GetServiceRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetServiceRsp>, I>>(object: I): GetServiceRsp {
    const message = createBaseGetServiceRsp();
    message.serviceInfo =
      object.serviceInfo !== undefined && object.serviceInfo !== null
        ? ServiceInfo.fromPartial(object.serviceInfo)
        : undefined;
    return message;
  }
};

export type ServiceDefinition = typeof ServiceDefinition;
export const ServiceDefinition = {
  name: 'Service',
  fullName: 'mgr.pbxtools.Service',
  methods: {
    /** 创建服务 */
    createService: {
      name: 'CreateService',
      requestType: CreateServiceReq,
      requestStream: false,
      responseType: CreateServiceRsp,
      responseStream: false,
      options: {}
    },
    /** 编辑服务 */
    editService: {
      name: 'EditService',
      requestType: EditServiceReq,
      requestStream: false,
      responseType: EditServiceRsp,
      responseStream: false,
      options: {}
    },
    /** 查询服务 */
    listService: {
      name: 'ListService',
      requestType: ListServiceReq,
      requestStream: false,
      responseType: ListServiceRsp,
      responseStream: false,
      options: {}
    },
    /** 查询服务运行状态 */
    listRunningStatus: {
      name: 'ListRunningStatus',
      requestType: ListRunningStatusReq,
      requestStream: false,
      responseType: ListRunningStatusRsp,
      responseStream: false,
      options: {}
    },
    /** 获取服务 */
    getService: {
      name: 'GetService',
      requestType: GetServiceReq,
      requestStream: false,
      responseType: GetServiceRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
