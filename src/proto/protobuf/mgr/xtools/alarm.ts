// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/xtools/alarm.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'mgr.pbxtools';

export enum PromRuleType {
  PROM_RULE_TYPE_NONE = 0,
  /** PROM_RULE_TYPE_QPS - 请求数：个 */
  PROM_RULE_TYPE_QPS = 1,
  /** PROM_RULE_TYPE_VOLA_MULTIPLE - 涨幅倍数：倍 */
  PROM_RULE_TYPE_VOLA_MULTIPLE = 2,
  /** PROM_RULE_TYPE_SUCC_RATE - 失败率：% */
  PROM_RULE_TYPE_SUCC_RATE = 3,
  UNRECOGNIZED = -1
}

export function promRuleTypeFromJSON(object: any): PromRuleType {
  switch (object) {
    case 0:
    case 'PROM_RULE_TYPE_NONE':
      return PromRuleType.PROM_RULE_TYPE_NONE;
    case 1:
    case 'PROM_RULE_TYPE_QPS':
      return PromRuleType.PROM_RULE_TYPE_QPS;
    case 2:
    case 'PROM_RULE_TYPE_VOLA_MULTIPLE':
      return PromRuleType.PROM_RULE_TYPE_VOLA_MULTIPLE;
    case 3:
    case 'PROM_RULE_TYPE_SUCC_RATE':
      return PromRuleType.PROM_RULE_TYPE_SUCC_RATE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PromRuleType.UNRECOGNIZED;
  }
}

export enum PromRuleOperator {
  PROM_RULE_OPERATOR_NONE = 0,
  /** PROM_RULE_OPERATOR_GE - >= */
  PROM_RULE_OPERATOR_GE = 1,
  /** PROM_RULE_OPERATOR_LT - < */
  PROM_RULE_OPERATOR_LT = 2,
  UNRECOGNIZED = -1
}

export function promRuleOperatorFromJSON(object: any): PromRuleOperator {
  switch (object) {
    case 0:
    case 'PROM_RULE_OPERATOR_NONE':
      return PromRuleOperator.PROM_RULE_OPERATOR_NONE;
    case 1:
    case 'PROM_RULE_OPERATOR_GE':
      return PromRuleOperator.PROM_RULE_OPERATOR_GE;
    case 2:
    case 'PROM_RULE_OPERATOR_LT':
      return PromRuleOperator.PROM_RULE_OPERATOR_LT;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return PromRuleOperator.UNRECOGNIZED;
  }
}

/**
 * 请求数（qps） >= threshold
 * 涨幅倍数 >= threshold
 * 成功率 <= threshold，查询字段中必须有code来代表哪个是成功的
 */
export interface PromRule {
  /** 计算公式：譬如 sum by (job, uri0, uri1) (rate(counter_total{uri0='errorlog'}[1m])) */
  expr: string;
  /** 单位为分钟默认为5分钟 */
  for: number;
  /** 描述这个告警 */
  description: string;
  /** 类型：请求数（qps），涨幅倍数，成功率。 */
  ty: PromRuleType;
  /** 请求数（qps）>= threshold , 涨幅倍数 >= threshold ,成功率 <= threshold */
  threshold: number;
  /** 当 ty 为波动率成功率时，可限制一下：qps 大于 某个值 */
  qps_restrict: number;
  /** 成功率时的code，多个时用逗号分开。譬如：0,200 */
  code: string;
  /** 针对于一个告警规则的告警群 */
  webhook: string;
}

export interface AddPromRuleReq {
  /** update 时有效 */
  id: number;
  /** 对应的serviceid */
  service_id: number;
  /** 增加的一个规则 */
  rule: PromRule | undefined;
}

export interface AddPromRuleRsp {
  id: number;
  /** 合并之后的expr */
  dest_expr: string;
  /** 告警名 */
  alarm_name: string;
}

export interface AlarmThreshold {
  /** 时延阈值 */
  delay: number;
  /** qps阈值 */
  qps: number;
  /** qps波动告警，则需qps大于这个值 */
  vola_qps: number;
  /** qps波动阈值 */
  qps_vola: number;
  /** 时延波动阈值 */
  delay_vola: number;
}

export interface AlarmConfig {
  /** 唯一id，add时不要填 */
  id: number;
  /** 对应的serviceid */
  service_id: number;
  /** 告警的methond，支持*结尾的，前缀比较 */
  methond: string;
  /** 告警阈值 */
  threshold: AlarmThreshold | undefined;
  /** 升级，会拨打电话 */
  urgent: AlarmThreshold | undefined;
  /** 告警描述 */
  desc: string;
  /** 操作者，后端自动生成 */
  operator: string;
  /** 创建时间，后端自动生成 */
  create_time: number;
  /** 更新时间，后端自动生成 */
  update_time: number;
}

export interface AddAlarmConfigReq {
  data: AlarmConfig | undefined;
}

export interface AddAlarmConfigRsp {
  id: number;
}

export interface UpdateAlarmConfigReq {
  data: AlarmConfig | undefined;
}

export interface UpdateAlarmConfigRsp {
  id: number;
}

export interface DelAlarmConfigReq {
  ids: number[];
}

export interface DelAlarmConfigRsp {
  count: number;
}

export interface ListAlarmConfigReq {
  page: Page | undefined;
  service_id: number;
  /** 需要毫秒时间戳，默认是unix时间戳 */
  needMilli: boolean;
  methond: string;
}

export interface ListAlarmConfigRsp {
  page: Page | undefined;
  data: AlarmConfig[];
  /** 业务的告警webhook */
  alarm_webhook: string;
}

export interface AlarmSilent {
  /** 唯一id，add时忽略 */
  id: number;
  /** 静默对应的methond，支持*结尾的，前缀比较，最长前缀为最佳匹配 */
  methond: string;
  /** 有效开始时间 */
  valid_begin: number;
  /** 有效结束时间 */
  valid_end: number;
  /** 操作人，add/update时忽略，内部自动赋值 */
  operator: string;
  /** 创建时间点，add/update时忽略，内部自动赋值 */
  create_at: number;
  /** 对应的service id */
  service_id: number;
  /** 对应的告警配置id，也可能没有对应的告警配置 */
  alarm_config_id: number;
  /** 更新时间点，add/update时忽略，内部自动赋值 */
  update_at: number;
}

export interface AddSilentReq {
  data: AlarmSilent | undefined;
}

export interface AddSilentRsp {
  id: number;
}

export interface UpdateSilentReq {
  data: AlarmSilent | undefined;
}

export interface UpdateSilentRsp {}

export interface DelSilentReq {
  ids: number[];
}

export interface DelSilentRsp {}

export interface ListSilentReq {
  page: Page | undefined;
  service_id: number;
  /** 需要毫秒时间戳，默认是unix时间戳 */
  needMilli: boolean;
  methond: string;
}

export interface ListSilentRsp {
  page: Page | undefined;
  data: AlarmSilent[];
}

export interface ServiceInfoForAlarm {
  /** 服务Id */
  id: number;
  /** 业务Id */
  business_id: number;
  /** 业务名称 */
  business_name: string;
  /** 服务名称，一旦创建不可更改，比如hala-api/hala-family/ahchat-api等 */
  name: string;
  /** smicro服务名称 */
  smicro_name: string;
  /** 服务类型(1:云主机,2:K8S) */
  server_type: number;
  /** 服务描述 */
  desc: string;
  /** 服务git地址 */
  git_url: string;
  /** 服务api url */
  api_url: string;
  /** 维护人-phone */
  maintainers_phone: string[];
  /** 创建人 */
  creator: string;
  /** 创建时间，unix timestamp, seconds */
  created_at: number;
  /** 更新人 */
  updater: string;
  /** 更新时间 */
  updated_at: number;
}

export interface ListServiceForAlarmReq {
  page: Page | undefined;
}

export interface ListServiceForAlarmRsp {
  page: Page | undefined;
  services: ServiceInfoForAlarm[];
}

export interface AlarmHistory {
  /** 唯一id */
  id: number;
  /** 对应的serviceid */
  service_id: number;
  /** 对应的alarm config id */
  alarm_config_id: number;
  /** 告警内容 */
  content: string;
  /** 告警人员,现告警到群，无用 */
  users: string;
  /** 告警开始时间 */
  alarm_time: number;
  /** 发送告警时间 */
  send_time: number;
  /** 告警webhook,现无用 */
  webhook: string;
  /** 告警的mthond */
  methond: string;
}

export interface AddAlarmHistoryReq {
  data: AlarmHistory | undefined;
}

export interface AddAlarmHistoryRsp {
  id: number;
}

export interface ListAlarmHistoryReq {
  page: Page | undefined;
  /** service id 必填 */
  service_id: number;
  /** alarm config id 选填，在关联告警配置时填 */
  alarm_config_id: number;
  /** 查询告警开始时间 */
  send_begin_time: number;
  /** 查询告警结束时间 */
  send_end_time: number;
  /** 搜索methond */
  methond: string;
}

export interface ListAlarmHistoryRsp {
  page: Page | undefined;
  data: AlarmHistory[];
}

function createBasePromRule(): PromRule {
  return { expr: '', for: 0, description: '', ty: 0, threshold: 0, qps_restrict: 0, code: '', webhook: '' };
}

export const PromRule: MessageFns<PromRule> = {
  fromJSON(object: any): PromRule {
    return {
      expr: isSet(object.expr) ? globalThis.String(object.expr) : '',
      for: isSet(object.for) ? globalThis.Number(object.for) : 0,
      description: isSet(object.description) ? globalThis.String(object.description) : '',
      ty: isSet(object.ty) ? promRuleTypeFromJSON(object.ty) : 0,
      threshold: isSet(object.threshold) ? globalThis.Number(object.threshold) : 0,
      qps_restrict: isSet(object.qps_restrict) ? globalThis.Number(object.qps_restrict) : 0,
      code: isSet(object.code) ? globalThis.String(object.code) : '',
      webhook: isSet(object.webhook) ? globalThis.String(object.webhook) : ''
    };
  },

  create<I extends Exact<DeepPartial<PromRule>, I>>(base?: I): PromRule {
    return PromRule.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<PromRule>, I>>(object: I): PromRule {
    const message = createBasePromRule();
    message.expr = object.expr ?? '';
    message.for = object.for ?? 0;
    message.description = object.description ?? '';
    message.ty = object.ty ?? 0;
    message.threshold = object.threshold ?? 0;
    message.qps_restrict = object.qps_restrict ?? 0;
    message.code = object.code ?? '';
    message.webhook = object.webhook ?? '';
    return message;
  }
};

function createBaseAddPromRuleReq(): AddPromRuleReq {
  return { id: 0, service_id: 0, rule: undefined };
}

export const AddPromRuleReq: MessageFns<AddPromRuleReq> = {
  fromJSON(object: any): AddPromRuleReq {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      rule: isSet(object.rule) ? PromRule.fromJSON(object.rule) : undefined
    };
  },

  create<I extends Exact<DeepPartial<AddPromRuleReq>, I>>(base?: I): AddPromRuleReq {
    return AddPromRuleReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddPromRuleReq>, I>>(object: I): AddPromRuleReq {
    const message = createBaseAddPromRuleReq();
    message.id = object.id ?? 0;
    message.service_id = object.service_id ?? 0;
    message.rule = object.rule !== undefined && object.rule !== null ? PromRule.fromPartial(object.rule) : undefined;
    return message;
  }
};

function createBaseAddPromRuleRsp(): AddPromRuleRsp {
  return { id: 0, dest_expr: '', alarm_name: '' };
}

export const AddPromRuleRsp: MessageFns<AddPromRuleRsp> = {
  fromJSON(object: any): AddPromRuleRsp {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      dest_expr: isSet(object.dest_expr) ? globalThis.String(object.dest_expr) : '',
      alarm_name: isSet(object.alarm_name) ? globalThis.String(object.alarm_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<AddPromRuleRsp>, I>>(base?: I): AddPromRuleRsp {
    return AddPromRuleRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddPromRuleRsp>, I>>(object: I): AddPromRuleRsp {
    const message = createBaseAddPromRuleRsp();
    message.id = object.id ?? 0;
    message.dest_expr = object.dest_expr ?? '';
    message.alarm_name = object.alarm_name ?? '';
    return message;
  }
};

function createBaseAlarmThreshold(): AlarmThreshold {
  return { delay: 0, qps: 0, vola_qps: 0, qps_vola: 0, delay_vola: 0 };
}

export const AlarmThreshold: MessageFns<AlarmThreshold> = {
  fromJSON(object: any): AlarmThreshold {
    return {
      delay: isSet(object.delay) ? globalThis.Number(object.delay) : 0,
      qps: isSet(object.qps) ? globalThis.Number(object.qps) : 0,
      vola_qps: isSet(object.vola_qps) ? globalThis.Number(object.vola_qps) : 0,
      qps_vola: isSet(object.qps_vola) ? globalThis.Number(object.qps_vola) : 0,
      delay_vola: isSet(object.delay_vola) ? globalThis.Number(object.delay_vola) : 0
    };
  },

  create<I extends Exact<DeepPartial<AlarmThreshold>, I>>(base?: I): AlarmThreshold {
    return AlarmThreshold.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmThreshold>, I>>(object: I): AlarmThreshold {
    const message = createBaseAlarmThreshold();
    message.delay = object.delay ?? 0;
    message.qps = object.qps ?? 0;
    message.vola_qps = object.vola_qps ?? 0;
    message.qps_vola = object.qps_vola ?? 0;
    message.delay_vola = object.delay_vola ?? 0;
    return message;
  }
};

function createBaseAlarmConfig(): AlarmConfig {
  return {
    id: 0,
    service_id: 0,
    methond: '',
    threshold: undefined,
    urgent: undefined,
    desc: '',
    operator: '',
    create_time: 0,
    update_time: 0
  };
}

export const AlarmConfig: MessageFns<AlarmConfig> = {
  fromJSON(object: any): AlarmConfig {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      methond: isSet(object.methond) ? globalThis.String(object.methond) : '',
      threshold: isSet(object.threshold) ? AlarmThreshold.fromJSON(object.threshold) : undefined,
      urgent: isSet(object.urgent) ? AlarmThreshold.fromJSON(object.urgent) : undefined,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      create_time: isSet(object.create_time) ? globalThis.Number(object.create_time) : 0,
      update_time: isSet(object.update_time) ? globalThis.Number(object.update_time) : 0
    };
  },

  create<I extends Exact<DeepPartial<AlarmConfig>, I>>(base?: I): AlarmConfig {
    return AlarmConfig.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmConfig>, I>>(object: I): AlarmConfig {
    const message = createBaseAlarmConfig();
    message.id = object.id ?? 0;
    message.service_id = object.service_id ?? 0;
    message.methond = object.methond ?? '';
    message.threshold =
      object.threshold !== undefined && object.threshold !== null
        ? AlarmThreshold.fromPartial(object.threshold)
        : undefined;
    message.urgent =
      object.urgent !== undefined && object.urgent !== null ? AlarmThreshold.fromPartial(object.urgent) : undefined;
    message.desc = object.desc ?? '';
    message.operator = object.operator ?? '';
    message.create_time = object.create_time ?? 0;
    message.update_time = object.update_time ?? 0;
    return message;
  }
};

function createBaseAddAlarmConfigReq(): AddAlarmConfigReq {
  return { data: undefined };
}

export const AddAlarmConfigReq: MessageFns<AddAlarmConfigReq> = {
  fromJSON(object: any): AddAlarmConfigReq {
    return { data: isSet(object.data) ? AlarmConfig.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<AddAlarmConfigReq>, I>>(base?: I): AddAlarmConfigReq {
    return AddAlarmConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAlarmConfigReq>, I>>(object: I): AddAlarmConfigReq {
    const message = createBaseAddAlarmConfigReq();
    message.data = object.data !== undefined && object.data !== null ? AlarmConfig.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseAddAlarmConfigRsp(): AddAlarmConfigRsp {
  return { id: 0 };
}

export const AddAlarmConfigRsp: MessageFns<AddAlarmConfigRsp> = {
  fromJSON(object: any): AddAlarmConfigRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddAlarmConfigRsp>, I>>(base?: I): AddAlarmConfigRsp {
    return AddAlarmConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAlarmConfigRsp>, I>>(object: I): AddAlarmConfigRsp {
    const message = createBaseAddAlarmConfigRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateAlarmConfigReq(): UpdateAlarmConfigReq {
  return { data: undefined };
}

export const UpdateAlarmConfigReq: MessageFns<UpdateAlarmConfigReq> = {
  fromJSON(object: any): UpdateAlarmConfigReq {
    return { data: isSet(object.data) ? AlarmConfig.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateAlarmConfigReq>, I>>(base?: I): UpdateAlarmConfigReq {
    return UpdateAlarmConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAlarmConfigReq>, I>>(object: I): UpdateAlarmConfigReq {
    const message = createBaseUpdateAlarmConfigReq();
    message.data = object.data !== undefined && object.data !== null ? AlarmConfig.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseUpdateAlarmConfigRsp(): UpdateAlarmConfigRsp {
  return { id: 0 };
}

export const UpdateAlarmConfigRsp: MessageFns<UpdateAlarmConfigRsp> = {
  fromJSON(object: any): UpdateAlarmConfigRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<UpdateAlarmConfigRsp>, I>>(base?: I): UpdateAlarmConfigRsp {
    return UpdateAlarmConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateAlarmConfigRsp>, I>>(object: I): UpdateAlarmConfigRsp {
    const message = createBaseUpdateAlarmConfigRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseDelAlarmConfigReq(): DelAlarmConfigReq {
  return { ids: [] };
}

export const DelAlarmConfigReq: MessageFns<DelAlarmConfigReq> = {
  fromJSON(object: any): DelAlarmConfigReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DelAlarmConfigReq>, I>>(base?: I): DelAlarmConfigReq {
    return DelAlarmConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelAlarmConfigReq>, I>>(object: I): DelAlarmConfigReq {
    const message = createBaseDelAlarmConfigReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDelAlarmConfigRsp(): DelAlarmConfigRsp {
  return { count: 0 };
}

export const DelAlarmConfigRsp: MessageFns<DelAlarmConfigRsp> = {
  fromJSON(object: any): DelAlarmConfigRsp {
    return { count: isSet(object.count) ? globalThis.Number(object.count) : 0 };
  },

  create<I extends Exact<DeepPartial<DelAlarmConfigRsp>, I>>(base?: I): DelAlarmConfigRsp {
    return DelAlarmConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelAlarmConfigRsp>, I>>(object: I): DelAlarmConfigRsp {
    const message = createBaseDelAlarmConfigRsp();
    message.count = object.count ?? 0;
    return message;
  }
};

function createBaseListAlarmConfigReq(): ListAlarmConfigReq {
  return { page: undefined, service_id: 0, needMilli: false, methond: '' };
}

export const ListAlarmConfigReq: MessageFns<ListAlarmConfigReq> = {
  fromJSON(object: any): ListAlarmConfigReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      needMilli: isSet(object.needMilli) ? globalThis.Boolean(object.needMilli) : false,
      methond: isSet(object.methond) ? globalThis.String(object.methond) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAlarmConfigReq>, I>>(base?: I): ListAlarmConfigReq {
    return ListAlarmConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAlarmConfigReq>, I>>(object: I): ListAlarmConfigReq {
    const message = createBaseListAlarmConfigReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.service_id = object.service_id ?? 0;
    message.needMilli = object.needMilli ?? false;
    message.methond = object.methond ?? '';
    return message;
  }
};

function createBaseListAlarmConfigRsp(): ListAlarmConfigRsp {
  return { page: undefined, data: [], alarm_webhook: '' };
}

export const ListAlarmConfigRsp: MessageFns<ListAlarmConfigRsp> = {
  fromJSON(object: any): ListAlarmConfigRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AlarmConfig.fromJSON(e)) : [],
      alarm_webhook: isSet(object.alarm_webhook) ? globalThis.String(object.alarm_webhook) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAlarmConfigRsp>, I>>(base?: I): ListAlarmConfigRsp {
    return ListAlarmConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAlarmConfigRsp>, I>>(object: I): ListAlarmConfigRsp {
    const message = createBaseListAlarmConfigRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AlarmConfig.fromPartial(e)) || [];
    message.alarm_webhook = object.alarm_webhook ?? '';
    return message;
  }
};

function createBaseAlarmSilent(): AlarmSilent {
  return {
    id: 0,
    methond: '',
    valid_begin: 0,
    valid_end: 0,
    operator: '',
    create_at: 0,
    service_id: 0,
    alarm_config_id: 0,
    update_at: 0
  };
}

export const AlarmSilent: MessageFns<AlarmSilent> = {
  fromJSON(object: any): AlarmSilent {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      methond: isSet(object.methond) ? globalThis.String(object.methond) : '',
      valid_begin: isSet(object.valid_begin) ? globalThis.Number(object.valid_begin) : 0,
      valid_end: isSet(object.valid_end) ? globalThis.Number(object.valid_end) : 0,
      operator: isSet(object.operator) ? globalThis.String(object.operator) : '',
      create_at: isSet(object.create_at) ? globalThis.Number(object.create_at) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      alarm_config_id: isSet(object.alarm_config_id) ? globalThis.Number(object.alarm_config_id) : 0,
      update_at: isSet(object.update_at) ? globalThis.Number(object.update_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<AlarmSilent>, I>>(base?: I): AlarmSilent {
    return AlarmSilent.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmSilent>, I>>(object: I): AlarmSilent {
    const message = createBaseAlarmSilent();
    message.id = object.id ?? 0;
    message.methond = object.methond ?? '';
    message.valid_begin = object.valid_begin ?? 0;
    message.valid_end = object.valid_end ?? 0;
    message.operator = object.operator ?? '';
    message.create_at = object.create_at ?? 0;
    message.service_id = object.service_id ?? 0;
    message.alarm_config_id = object.alarm_config_id ?? 0;
    message.update_at = object.update_at ?? 0;
    return message;
  }
};

function createBaseAddSilentReq(): AddSilentReq {
  return { data: undefined };
}

export const AddSilentReq: MessageFns<AddSilentReq> = {
  fromJSON(object: any): AddSilentReq {
    return { data: isSet(object.data) ? AlarmSilent.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<AddSilentReq>, I>>(base?: I): AddSilentReq {
    return AddSilentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSilentReq>, I>>(object: I): AddSilentReq {
    const message = createBaseAddSilentReq();
    message.data = object.data !== undefined && object.data !== null ? AlarmSilent.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseAddSilentRsp(): AddSilentRsp {
  return { id: 0 };
}

export const AddSilentRsp: MessageFns<AddSilentRsp> = {
  fromJSON(object: any): AddSilentRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddSilentRsp>, I>>(base?: I): AddSilentRsp {
    return AddSilentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddSilentRsp>, I>>(object: I): AddSilentRsp {
    const message = createBaseAddSilentRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseUpdateSilentReq(): UpdateSilentReq {
  return { data: undefined };
}

export const UpdateSilentReq: MessageFns<UpdateSilentReq> = {
  fromJSON(object: any): UpdateSilentReq {
    return { data: isSet(object.data) ? AlarmSilent.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<UpdateSilentReq>, I>>(base?: I): UpdateSilentReq {
    return UpdateSilentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSilentReq>, I>>(object: I): UpdateSilentReq {
    const message = createBaseUpdateSilentReq();
    message.data = object.data !== undefined && object.data !== null ? AlarmSilent.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseUpdateSilentRsp(): UpdateSilentRsp {
  return {};
}

export const UpdateSilentRsp: MessageFns<UpdateSilentRsp> = {
  fromJSON(_: any): UpdateSilentRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<UpdateSilentRsp>, I>>(base?: I): UpdateSilentRsp {
    return UpdateSilentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UpdateSilentRsp>, I>>(_: I): UpdateSilentRsp {
    const message = createBaseUpdateSilentRsp();
    return message;
  }
};

function createBaseDelSilentReq(): DelSilentReq {
  return { ids: [] };
}

export const DelSilentReq: MessageFns<DelSilentReq> = {
  fromJSON(object: any): DelSilentReq {
    return { ids: globalThis.Array.isArray(object?.ids) ? object.ids.map((e: any) => globalThis.Number(e)) : [] };
  },

  create<I extends Exact<DeepPartial<DelSilentReq>, I>>(base?: I): DelSilentReq {
    return DelSilentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelSilentReq>, I>>(object: I): DelSilentReq {
    const message = createBaseDelSilentReq();
    message.ids = object.ids?.map(e => e) || [];
    return message;
  }
};

function createBaseDelSilentRsp(): DelSilentRsp {
  return {};
}

export const DelSilentRsp: MessageFns<DelSilentRsp> = {
  fromJSON(_: any): DelSilentRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<DelSilentRsp>, I>>(base?: I): DelSilentRsp {
    return DelSilentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<DelSilentRsp>, I>>(_: I): DelSilentRsp {
    const message = createBaseDelSilentRsp();
    return message;
  }
};

function createBaseListSilentReq(): ListSilentReq {
  return { page: undefined, service_id: 0, needMilli: false, methond: '' };
}

export const ListSilentReq: MessageFns<ListSilentReq> = {
  fromJSON(object: any): ListSilentReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      needMilli: isSet(object.needMilli) ? globalThis.Boolean(object.needMilli) : false,
      methond: isSet(object.methond) ? globalThis.String(object.methond) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListSilentReq>, I>>(base?: I): ListSilentReq {
    return ListSilentReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSilentReq>, I>>(object: I): ListSilentReq {
    const message = createBaseListSilentReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.service_id = object.service_id ?? 0;
    message.needMilli = object.needMilli ?? false;
    message.methond = object.methond ?? '';
    return message;
  }
};

function createBaseListSilentRsp(): ListSilentRsp {
  return { page: undefined, data: [] };
}

export const ListSilentRsp: MessageFns<ListSilentRsp> = {
  fromJSON(object: any): ListSilentRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AlarmSilent.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListSilentRsp>, I>>(base?: I): ListSilentRsp {
    return ListSilentRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListSilentRsp>, I>>(object: I): ListSilentRsp {
    const message = createBaseListSilentRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AlarmSilent.fromPartial(e)) || [];
    return message;
  }
};

function createBaseServiceInfoForAlarm(): ServiceInfoForAlarm {
  return {
    id: 0,
    business_id: 0,
    business_name: '',
    name: '',
    smicro_name: '',
    server_type: 0,
    desc: '',
    git_url: '',
    api_url: '',
    maintainers_phone: [],
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const ServiceInfoForAlarm: MessageFns<ServiceInfoForAlarm> = {
  fromJSON(object: any): ServiceInfoForAlarm {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      business_id: isSet(object.business_id) ? globalThis.Number(object.business_id) : 0,
      business_name: isSet(object.business_name) ? globalThis.String(object.business_name) : '',
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      smicro_name: isSet(object.smicro_name) ? globalThis.String(object.smicro_name) : '',
      server_type: isSet(object.server_type) ? globalThis.Number(object.server_type) : 0,
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      git_url: isSet(object.git_url) ? globalThis.String(object.git_url) : '',
      api_url: isSet(object.api_url) ? globalThis.String(object.api_url) : '',
      maintainers_phone: globalThis.Array.isArray(object?.maintainers_phone)
        ? object.maintainers_phone.map((e: any) => globalThis.String(e))
        : [],
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<ServiceInfoForAlarm>, I>>(base?: I): ServiceInfoForAlarm {
    return ServiceInfoForAlarm.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ServiceInfoForAlarm>, I>>(object: I): ServiceInfoForAlarm {
    const message = createBaseServiceInfoForAlarm();
    message.id = object.id ?? 0;
    message.business_id = object.business_id ?? 0;
    message.business_name = object.business_name ?? '';
    message.name = object.name ?? '';
    message.smicro_name = object.smicro_name ?? '';
    message.server_type = object.server_type ?? 0;
    message.desc = object.desc ?? '';
    message.git_url = object.git_url ?? '';
    message.api_url = object.api_url ?? '';
    message.maintainers_phone = object.maintainers_phone?.map(e => e) || [];
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseListServiceForAlarmReq(): ListServiceForAlarmReq {
  return { page: undefined };
}

export const ListServiceForAlarmReq: MessageFns<ListServiceForAlarmReq> = {
  fromJSON(object: any): ListServiceForAlarmReq {
    return { page: isSet(object.page) ? Page.fromJSON(object.page) : undefined };
  },

  create<I extends Exact<DeepPartial<ListServiceForAlarmReq>, I>>(base?: I): ListServiceForAlarmReq {
    return ListServiceForAlarmReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListServiceForAlarmReq>, I>>(object: I): ListServiceForAlarmReq {
    const message = createBaseListServiceForAlarmReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    return message;
  }
};

function createBaseListServiceForAlarmRsp(): ListServiceForAlarmRsp {
  return { page: undefined, services: [] };
}

export const ListServiceForAlarmRsp: MessageFns<ListServiceForAlarmRsp> = {
  fromJSON(object: any): ListServiceForAlarmRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      services: globalThis.Array.isArray(object?.services)
        ? object.services.map((e: any) => ServiceInfoForAlarm.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListServiceForAlarmRsp>, I>>(base?: I): ListServiceForAlarmRsp {
    return ListServiceForAlarmRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListServiceForAlarmRsp>, I>>(object: I): ListServiceForAlarmRsp {
    const message = createBaseListServiceForAlarmRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.services = object.services?.map(e => ServiceInfoForAlarm.fromPartial(e)) || [];
    return message;
  }
};

function createBaseAlarmHistory(): AlarmHistory {
  return {
    id: 0,
    service_id: 0,
    alarm_config_id: 0,
    content: '',
    users: '',
    alarm_time: 0,
    send_time: 0,
    webhook: '',
    methond: ''
  };
}

export const AlarmHistory: MessageFns<AlarmHistory> = {
  fromJSON(object: any): AlarmHistory {
    return {
      id: isSet(object.id) ? globalThis.Number(object.id) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      alarm_config_id: isSet(object.alarm_config_id) ? globalThis.Number(object.alarm_config_id) : 0,
      content: isSet(object.content) ? globalThis.String(object.content) : '',
      users: isSet(object.users) ? globalThis.String(object.users) : '',
      alarm_time: isSet(object.alarm_time) ? globalThis.Number(object.alarm_time) : 0,
      send_time: isSet(object.send_time) ? globalThis.Number(object.send_time) : 0,
      webhook: isSet(object.webhook) ? globalThis.String(object.webhook) : '',
      methond: isSet(object.methond) ? globalThis.String(object.methond) : ''
    };
  },

  create<I extends Exact<DeepPartial<AlarmHistory>, I>>(base?: I): AlarmHistory {
    return AlarmHistory.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AlarmHistory>, I>>(object: I): AlarmHistory {
    const message = createBaseAlarmHistory();
    message.id = object.id ?? 0;
    message.service_id = object.service_id ?? 0;
    message.alarm_config_id = object.alarm_config_id ?? 0;
    message.content = object.content ?? '';
    message.users = object.users ?? '';
    message.alarm_time = object.alarm_time ?? 0;
    message.send_time = object.send_time ?? 0;
    message.webhook = object.webhook ?? '';
    message.methond = object.methond ?? '';
    return message;
  }
};

function createBaseAddAlarmHistoryReq(): AddAlarmHistoryReq {
  return { data: undefined };
}

export const AddAlarmHistoryReq: MessageFns<AddAlarmHistoryReq> = {
  fromJSON(object: any): AddAlarmHistoryReq {
    return { data: isSet(object.data) ? AlarmHistory.fromJSON(object.data) : undefined };
  },

  create<I extends Exact<DeepPartial<AddAlarmHistoryReq>, I>>(base?: I): AddAlarmHistoryReq {
    return AddAlarmHistoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAlarmHistoryReq>, I>>(object: I): AddAlarmHistoryReq {
    const message = createBaseAddAlarmHistoryReq();
    message.data =
      object.data !== undefined && object.data !== null ? AlarmHistory.fromPartial(object.data) : undefined;
    return message;
  }
};

function createBaseAddAlarmHistoryRsp(): AddAlarmHistoryRsp {
  return { id: 0 };
}

export const AddAlarmHistoryRsp: MessageFns<AddAlarmHistoryRsp> = {
  fromJSON(object: any): AddAlarmHistoryRsp {
    return { id: isSet(object.id) ? globalThis.Number(object.id) : 0 };
  },

  create<I extends Exact<DeepPartial<AddAlarmHistoryRsp>, I>>(base?: I): AddAlarmHistoryRsp {
    return AddAlarmHistoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<AddAlarmHistoryRsp>, I>>(object: I): AddAlarmHistoryRsp {
    const message = createBaseAddAlarmHistoryRsp();
    message.id = object.id ?? 0;
    return message;
  }
};

function createBaseListAlarmHistoryReq(): ListAlarmHistoryReq {
  return { page: undefined, service_id: 0, alarm_config_id: 0, send_begin_time: 0, send_end_time: 0, methond: '' };
}

export const ListAlarmHistoryReq: MessageFns<ListAlarmHistoryReq> = {
  fromJSON(object: any): ListAlarmHistoryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      alarm_config_id: isSet(object.alarm_config_id) ? globalThis.Number(object.alarm_config_id) : 0,
      send_begin_time: isSet(object.send_begin_time) ? globalThis.Number(object.send_begin_time) : 0,
      send_end_time: isSet(object.send_end_time) ? globalThis.Number(object.send_end_time) : 0,
      methond: isSet(object.methond) ? globalThis.String(object.methond) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListAlarmHistoryReq>, I>>(base?: I): ListAlarmHistoryReq {
    return ListAlarmHistoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAlarmHistoryReq>, I>>(object: I): ListAlarmHistoryReq {
    const message = createBaseListAlarmHistoryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.service_id = object.service_id ?? 0;
    message.alarm_config_id = object.alarm_config_id ?? 0;
    message.send_begin_time = object.send_begin_time ?? 0;
    message.send_end_time = object.send_end_time ?? 0;
    message.methond = object.methond ?? '';
    return message;
  }
};

function createBaseListAlarmHistoryRsp(): ListAlarmHistoryRsp {
  return { page: undefined, data: [] };
}

export const ListAlarmHistoryRsp: MessageFns<ListAlarmHistoryRsp> = {
  fromJSON(object: any): ListAlarmHistoryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      data: globalThis.Array.isArray(object?.data) ? object.data.map((e: any) => AlarmHistory.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListAlarmHistoryRsp>, I>>(base?: I): ListAlarmHistoryRsp {
    return ListAlarmHistoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListAlarmHistoryRsp>, I>>(object: I): ListAlarmHistoryRsp {
    const message = createBaseListAlarmHistoryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.data = object.data?.map(e => AlarmHistory.fromPartial(e)) || [];
    return message;
  }
};

/**
 * 告警配置
 * smicro:spath=gitit.cc/social/components-service/xtools/handler/alarm
 */
export type AlarmDefinition = typeof AlarmDefinition;
export const AlarmDefinition = {
  name: 'Alarm',
  fullName: 'mgr.pbxtools.Alarm',
  methods: {
    /** 添加告警配置 */
    addAlarmConfig: {
      name: 'AddAlarmConfig',
      requestType: AddAlarmConfigReq,
      requestStream: false,
      responseType: AddAlarmConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 更新告警配置 */
    updateAlarmConfig: {
      name: 'UpdateAlarmConfig',
      requestType: UpdateAlarmConfigReq,
      requestStream: false,
      responseType: UpdateAlarmConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 删除告警配置 */
    delAlarmConfig: {
      name: 'DelAlarmConfig',
      requestType: DelAlarmConfigReq,
      requestStream: false,
      responseType: DelAlarmConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 查询告警配置 */
    listAlarmConfig: {
      name: 'ListAlarmConfig',
      requestType: ListAlarmConfigReq,
      requestStream: false,
      responseType: ListAlarmConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 添加静默 */
    addSilent: {
      name: 'AddSilent',
      requestType: AddSilentReq,
      requestStream: false,
      responseType: AddSilentRsp,
      responseStream: false,
      options: {}
    },
    /** 更新静默 */
    updateSilent: {
      name: 'UpdateSilent',
      requestType: UpdateSilentReq,
      requestStream: false,
      responseType: UpdateSilentRsp,
      responseStream: false,
      options: {}
    },
    /** 删除静默 */
    delSilent: {
      name: 'DelSilent',
      requestType: DelSilentReq,
      requestStream: false,
      responseType: DelSilentRsp,
      responseStream: false,
      options: {}
    },
    /** 查询静默 */
    listSilent: {
      name: 'ListSilent',
      requestType: ListSilentReq,
      requestStream: false,
      responseType: ListSilentRsp,
      responseStream: false,
      options: {}
    },
    /** 获取服务--给alarm-server调用 */
    listServiceForAlarm: {
      name: 'ListServiceForAlarm',
      requestType: ListServiceForAlarmReq,
      requestStream: false,
      responseType: ListServiceForAlarmRsp,
      responseStream: false,
      options: {}
    },
    /** 添加告警历史--给alarm-server调用 */
    addAlarmHistory: {
      name: 'AddAlarmHistory',
      requestType: AddAlarmHistoryReq,
      requestStream: false,
      responseType: AddAlarmHistoryRsp,
      responseStream: false,
      options: {}
    },
    /** 查询告警历史 */
    listAlarmHistory: {
      name: 'ListAlarmHistory',
      requestType: ListAlarmHistoryReq,
      requestStream: false,
      responseType: ListAlarmHistoryRsp,
      responseStream: false,
      options: {}
    },
    /** 添加/更新/删除prometheus alert rule，删除/查询复用 DelAlarmConfig/ListAlarmConfig */
    addPromRule: {
      name: 'AddPromRule',
      requestType: AddPromRuleReq,
      requestStream: false,
      responseType: AddPromRuleRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
