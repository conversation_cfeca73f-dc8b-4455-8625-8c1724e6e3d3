// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/xtools/monitoring.proto

/* eslint-disable */

export const protobufPackage = 'pbxtools';

export interface ListNodesReq {
  /** 查询业务，为空就返回全量 */
  app: string;
}

export interface Node {
  /** 根据app来获取，如果获取不到，则取默认：anm == "" */
  app: string;
  /** prometheus 节点地址：http://172.21.128.166:9000 */
  addrs: string;
}

export interface ListNodesRsp {
  nodes: Node[];
}

export interface UriLabel {
  /** 标签名：uri0, uri1,... */
  key: string;
  /** 如果为空，则代表all */
  values: string[];
}

export interface Labels {
  /** 用户选择的code，为空，则代表all */
  code: string[];
  /** 用户选择的instance，为空，则代表all */
  instance: string[];
  /** 用户选的uri标签数组，为空则为all：一层层的铺开的，在有多个uri的情况下，则应答的uri比请求的uris多一层。按照顺序来填写，uri0必须放在第一个。 */
  uris: UriLabel[];
}

export interface GetLabelsReq {
  /** 业务 */
  app: string;
  /** 服务，也叫 job */
  service: string;
  /** UTC开始时间，如：2025-01-13T04:40:22.237Z */
  start: string;
  /** UTC结束时间，如：2025-01-13T05:40:22.238Z */
  end: string;
  /** 用户选择的 code，instance，uri， */
  labels: Labels | undefined;
  /** 指标名 */
  metric_name: string;
}

export interface GetLabelsRsp {
  /** 获取到的标签 */
  labels: Labels | undefined;
}

function createBaseListNodesReq(): ListNodesReq {
  return { app: '' };
}

export const ListNodesReq: MessageFns<ListNodesReq> = {
  fromJSON(object: any): ListNodesReq {
    return { app: isSet(object.app) ? globalThis.String(object.app) : '' };
  },

  create<I extends Exact<DeepPartial<ListNodesReq>, I>>(base?: I): ListNodesReq {
    return ListNodesReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListNodesReq>, I>>(object: I): ListNodesReq {
    const message = createBaseListNodesReq();
    message.app = object.app ?? '';
    return message;
  }
};

function createBaseNode(): Node {
  return { app: '', addrs: '' };
}

export const Node: MessageFns<Node> = {
  fromJSON(object: any): Node {
    return {
      app: isSet(object.app) ? globalThis.String(object.app) : '',
      addrs: isSet(object.addrs) ? globalThis.String(object.addrs) : ''
    };
  },

  create<I extends Exact<DeepPartial<Node>, I>>(base?: I): Node {
    return Node.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Node>, I>>(object: I): Node {
    const message = createBaseNode();
    message.app = object.app ?? '';
    message.addrs = object.addrs ?? '';
    return message;
  }
};

function createBaseListNodesRsp(): ListNodesRsp {
  return { nodes: [] };
}

export const ListNodesRsp: MessageFns<ListNodesRsp> = {
  fromJSON(object: any): ListNodesRsp {
    return { nodes: globalThis.Array.isArray(object?.nodes) ? object.nodes.map((e: any) => Node.fromJSON(e)) : [] };
  },

  create<I extends Exact<DeepPartial<ListNodesRsp>, I>>(base?: I): ListNodesRsp {
    return ListNodesRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListNodesRsp>, I>>(object: I): ListNodesRsp {
    const message = createBaseListNodesRsp();
    message.nodes = object.nodes?.map(e => Node.fromPartial(e)) || [];
    return message;
  }
};

function createBaseUriLabel(): UriLabel {
  return { key: '', values: [] };
}

export const UriLabel: MessageFns<UriLabel> = {
  fromJSON(object: any): UriLabel {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      values: globalThis.Array.isArray(object?.values) ? object.values.map((e: any) => globalThis.String(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<UriLabel>, I>>(base?: I): UriLabel {
    return UriLabel.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<UriLabel>, I>>(object: I): UriLabel {
    const message = createBaseUriLabel();
    message.key = object.key ?? '';
    message.values = object.values?.map(e => e) || [];
    return message;
  }
};

function createBaseLabels(): Labels {
  return { code: [], instance: [], uris: [] };
}

export const Labels: MessageFns<Labels> = {
  fromJSON(object: any): Labels {
    return {
      code: globalThis.Array.isArray(object?.code) ? object.code.map((e: any) => globalThis.String(e)) : [],
      instance: globalThis.Array.isArray(object?.instance) ? object.instance.map((e: any) => globalThis.String(e)) : [],
      uris: globalThis.Array.isArray(object?.uris) ? object.uris.map((e: any) => UriLabel.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<Labels>, I>>(base?: I): Labels {
    return Labels.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Labels>, I>>(object: I): Labels {
    const message = createBaseLabels();
    message.code = object.code?.map(e => e) || [];
    message.instance = object.instance?.map(e => e) || [];
    message.uris = object.uris?.map(e => UriLabel.fromPartial(e)) || [];
    return message;
  }
};

function createBaseGetLabelsReq(): GetLabelsReq {
  return { app: '', service: '', start: '', end: '', labels: undefined, metric_name: '' };
}

export const GetLabelsReq: MessageFns<GetLabelsReq> = {
  fromJSON(object: any): GetLabelsReq {
    return {
      app: isSet(object.app) ? globalThis.String(object.app) : '',
      service: isSet(object.service) ? globalThis.String(object.service) : '',
      start: isSet(object.start) ? globalThis.String(object.start) : '',
      end: isSet(object.end) ? globalThis.String(object.end) : '',
      labels: isSet(object.labels) ? Labels.fromJSON(object.labels) : undefined,
      metric_name: isSet(object.metric_name) ? globalThis.String(object.metric_name) : ''
    };
  },

  create<I extends Exact<DeepPartial<GetLabelsReq>, I>>(base?: I): GetLabelsReq {
    return GetLabelsReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLabelsReq>, I>>(object: I): GetLabelsReq {
    const message = createBaseGetLabelsReq();
    message.app = object.app ?? '';
    message.service = object.service ?? '';
    message.start = object.start ?? '';
    message.end = object.end ?? '';
    message.labels =
      object.labels !== undefined && object.labels !== null ? Labels.fromPartial(object.labels) : undefined;
    message.metric_name = object.metric_name ?? '';
    return message;
  }
};

function createBaseGetLabelsRsp(): GetLabelsRsp {
  return { labels: undefined };
}

export const GetLabelsRsp: MessageFns<GetLabelsRsp> = {
  fromJSON(object: any): GetLabelsRsp {
    return { labels: isSet(object.labels) ? Labels.fromJSON(object.labels) : undefined };
  },

  create<I extends Exact<DeepPartial<GetLabelsRsp>, I>>(base?: I): GetLabelsRsp {
    return GetLabelsRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<GetLabelsRsp>, I>>(object: I): GetLabelsRsp {
    const message = createBaseGetLabelsRsp();
    message.labels =
      object.labels !== undefined && object.labels !== null ? Labels.fromPartial(object.labels) : undefined;
    return message;
  }
};

/**
 * 监控相关
 * smicro:spath=gitit.cc/social/components-service/xtools/handler/monitoring
 */
export type MonitoringDefinition = typeof MonitoringDefinition;
export const MonitoringDefinition = {
  name: 'Monitoring',
  fullName: 'pbxtools.Monitoring',
  methods: {
    /** 获取prometheus节点，改由后端统一配置 */
    listNodes: {
      name: 'ListNodes',
      requestType: ListNodesReq,
      requestStream: false,
      responseType: ListNodesRsp,
      responseStream: false,
      options: {}
    },
    /**
     * 获取promehteus label
     *    a. job,时间段 为必选
     *    b. label分三个维度：code，instance，uri。维度之间互相关联
     *    c. uri只能一层一层的展开：uri0 -> uri1 -> uri2
     */
    getLabels: {
      name: 'GetLabels',
      requestType: GetLabelsReq,
      requestStream: false,
      responseType: GetLabelsRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
