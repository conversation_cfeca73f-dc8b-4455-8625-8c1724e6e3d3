// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: protobuf/mgr/xtools/config.proto

/* eslint-disable */
import { Page } from '../../api/common/common';

export const protobufPackage = 'pbxtools';

export interface CreateConfigReq {
  /** 服务Id */
  service_id: number;
  /** 配置key */
  key: string;
  /** 配置value */
  value: string;
  /** 配置说明 */
  desc: string;
}

export interface CreateConfigRsp {
  /** 配置Id */
  config_id: number;
}

export interface EditConfigReq {
  /** 配置Id */
  config_id: number;
  /** 配置value */
  value: string;
  /** 配置说明 */
  desc: string;
}

export interface EditConfigRsp {}

export interface RemoveConfigReq {
  /** 配置Id */
  config_id: number;
}

export interface RemoveConfigRsp {}

export interface ListConfigReq {
  page: Page | undefined;
  /** 服务Id */
  service_id: number;
  /** 服务名称(如果有值,则服务Id失效) */
  service_name: string;
  /** 配置key */
  key: string;
}

export interface ListConfigRsp {
  page: Page | undefined;
  configs: ConfigInfo[];
}

export interface ConfigInfo {
  /** 配置Id */
  config_id: number;
  /** 服务Id */
  service_id: number;
  /** 服务名称 */
  service_name: string;
  /** 配置key */
  key: string;
  /** 配置value */
  value: string;
  /** 配置说明 */
  desc: string;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
  /** 更新人 */
  updater: string;
  /** 更新时间 */
  updated_at: number;
}

export interface ListConfigHistoryReq {
  page: Page | undefined;
  /** 配置Id */
  config_id: number;
}

export interface ListConfigHistoryRsp {
  page: Page | undefined;
  configHistories: ConfigHistoryInfo[];
}

export interface ConfigHistoryInfo {
  /** 配置历史Id */
  config_history_id: number;
  /** 配置Id */
  config_id: number;
  /** 服务Id */
  service_id: number;
  /** 服务名称 */
  service_name: string;
  /** 配置key */
  key: string;
  /** 配置value */
  value: string;
  /** 配置说明 */
  desc: string;
  /** 版本 */
  version: number;
  /** 创建人 */
  creator: string;
  /** 创建时间 */
  created_at: number;
}

function createBaseCreateConfigReq(): CreateConfigReq {
  return { service_id: 0, key: '', value: '', desc: '' };
}

export const CreateConfigReq: MessageFns<CreateConfigReq> = {
  fromJSON(object: any): CreateConfigReq {
    return {
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<CreateConfigReq>, I>>(base?: I): CreateConfigReq {
    return CreateConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateConfigReq>, I>>(object: I): CreateConfigReq {
    const message = createBaseCreateConfigReq();
    message.service_id = object.service_id ?? 0;
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    message.desc = object.desc ?? '';
    return message;
  }
};

function createBaseCreateConfigRsp(): CreateConfigRsp {
  return { config_id: 0 };
}

export const CreateConfigRsp: MessageFns<CreateConfigRsp> = {
  fromJSON(object: any): CreateConfigRsp {
    return { config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<CreateConfigRsp>, I>>(base?: I): CreateConfigRsp {
    return CreateConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<CreateConfigRsp>, I>>(object: I): CreateConfigRsp {
    const message = createBaseCreateConfigRsp();
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseEditConfigReq(): EditConfigReq {
  return { config_id: 0, value: '', desc: '' };
}

export const EditConfigReq: MessageFns<EditConfigReq> = {
  fromJSON(object: any): EditConfigReq {
    return {
      config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0,
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : ''
    };
  },

  create<I extends Exact<DeepPartial<EditConfigReq>, I>>(base?: I): EditConfigReq {
    return EditConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditConfigReq>, I>>(object: I): EditConfigReq {
    const message = createBaseEditConfigReq();
    message.config_id = object.config_id ?? 0;
    message.value = object.value ?? '';
    message.desc = object.desc ?? '';
    return message;
  }
};

function createBaseEditConfigRsp(): EditConfigRsp {
  return {};
}

export const EditConfigRsp: MessageFns<EditConfigRsp> = {
  fromJSON(_: any): EditConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<EditConfigRsp>, I>>(base?: I): EditConfigRsp {
    return EditConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EditConfigRsp>, I>>(_: I): EditConfigRsp {
    const message = createBaseEditConfigRsp();
    return message;
  }
};

function createBaseRemoveConfigReq(): RemoveConfigReq {
  return { config_id: 0 };
}

export const RemoveConfigReq: MessageFns<RemoveConfigReq> = {
  fromJSON(object: any): RemoveConfigReq {
    return { config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0 };
  },

  create<I extends Exact<DeepPartial<RemoveConfigReq>, I>>(base?: I): RemoveConfigReq {
    return RemoveConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveConfigReq>, I>>(object: I): RemoveConfigReq {
    const message = createBaseRemoveConfigReq();
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseRemoveConfigRsp(): RemoveConfigRsp {
  return {};
}

export const RemoveConfigRsp: MessageFns<RemoveConfigRsp> = {
  fromJSON(_: any): RemoveConfigRsp {
    return {};
  },

  create<I extends Exact<DeepPartial<RemoveConfigRsp>, I>>(base?: I): RemoveConfigRsp {
    return RemoveConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<RemoveConfigRsp>, I>>(_: I): RemoveConfigRsp {
    const message = createBaseRemoveConfigRsp();
    return message;
  }
};

function createBaseListConfigReq(): ListConfigReq {
  return { page: undefined, service_id: 0, service_name: '', key: '' };
}

export const ListConfigReq: MessageFns<ListConfigReq> = {
  fromJSON(object: any): ListConfigReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      service_name: isSet(object.service_name) ? globalThis.String(object.service_name) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : ''
    };
  },

  create<I extends Exact<DeepPartial<ListConfigReq>, I>>(base?: I): ListConfigReq {
    return ListConfigReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigReq>, I>>(object: I): ListConfigReq {
    const message = createBaseListConfigReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.service_id = object.service_id ?? 0;
    message.service_name = object.service_name ?? '';
    message.key = object.key ?? '';
    return message;
  }
};

function createBaseListConfigRsp(): ListConfigRsp {
  return { page: undefined, configs: [] };
}

export const ListConfigRsp: MessageFns<ListConfigRsp> = {
  fromJSON(object: any): ListConfigRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      configs: globalThis.Array.isArray(object?.configs) ? object.configs.map((e: any) => ConfigInfo.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<ListConfigRsp>, I>>(base?: I): ListConfigRsp {
    return ListConfigRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigRsp>, I>>(object: I): ListConfigRsp {
    const message = createBaseListConfigRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.configs = object.configs?.map(e => ConfigInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseConfigInfo(): ConfigInfo {
  return {
    config_id: 0,
    service_id: 0,
    service_name: '',
    key: '',
    value: '',
    desc: '',
    creator: '',
    created_at: 0,
    updater: '',
    updated_at: 0
  };
}

export const ConfigInfo: MessageFns<ConfigInfo> = {
  fromJSON(object: any): ConfigInfo {
    return {
      config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      service_name: isSet(object.service_name) ? globalThis.String(object.service_name) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0,
      updater: isSet(object.updater) ? globalThis.String(object.updater) : '',
      updated_at: isSet(object.updated_at) ? globalThis.Number(object.updated_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<ConfigInfo>, I>>(base?: I): ConfigInfo {
    return ConfigInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigInfo>, I>>(object: I): ConfigInfo {
    const message = createBaseConfigInfo();
    message.config_id = object.config_id ?? 0;
    message.service_id = object.service_id ?? 0;
    message.service_name = object.service_name ?? '';
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    message.desc = object.desc ?? '';
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    message.updater = object.updater ?? '';
    message.updated_at = object.updated_at ?? 0;
    return message;
  }
};

function createBaseListConfigHistoryReq(): ListConfigHistoryReq {
  return { page: undefined, config_id: 0 };
}

export const ListConfigHistoryReq: MessageFns<ListConfigHistoryReq> = {
  fromJSON(object: any): ListConfigHistoryReq {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0
    };
  },

  create<I extends Exact<DeepPartial<ListConfigHistoryReq>, I>>(base?: I): ListConfigHistoryReq {
    return ListConfigHistoryReq.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigHistoryReq>, I>>(object: I): ListConfigHistoryReq {
    const message = createBaseListConfigHistoryReq();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.config_id = object.config_id ?? 0;
    return message;
  }
};

function createBaseListConfigHistoryRsp(): ListConfigHistoryRsp {
  return { page: undefined, configHistories: [] };
}

export const ListConfigHistoryRsp: MessageFns<ListConfigHistoryRsp> = {
  fromJSON(object: any): ListConfigHistoryRsp {
    return {
      page: isSet(object.page) ? Page.fromJSON(object.page) : undefined,
      configHistories: globalThis.Array.isArray(object?.configHistories)
        ? object.configHistories.map((e: any) => ConfigHistoryInfo.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<ListConfigHistoryRsp>, I>>(base?: I): ListConfigHistoryRsp {
    return ListConfigHistoryRsp.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListConfigHistoryRsp>, I>>(object: I): ListConfigHistoryRsp {
    const message = createBaseListConfigHistoryRsp();
    message.page = object.page !== undefined && object.page !== null ? Page.fromPartial(object.page) : undefined;
    message.configHistories = object.configHistories?.map(e => ConfigHistoryInfo.fromPartial(e)) || [];
    return message;
  }
};

function createBaseConfigHistoryInfo(): ConfigHistoryInfo {
  return {
    config_history_id: 0,
    config_id: 0,
    service_id: 0,
    service_name: '',
    key: '',
    value: '',
    desc: '',
    version: 0,
    creator: '',
    created_at: 0
  };
}

export const ConfigHistoryInfo: MessageFns<ConfigHistoryInfo> = {
  fromJSON(object: any): ConfigHistoryInfo {
    return {
      config_history_id: isSet(object.config_history_id) ? globalThis.Number(object.config_history_id) : 0,
      config_id: isSet(object.config_id) ? globalThis.Number(object.config_id) : 0,
      service_id: isSet(object.service_id) ? globalThis.Number(object.service_id) : 0,
      service_name: isSet(object.service_name) ? globalThis.String(object.service_name) : '',
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object.value) ? globalThis.String(object.value) : '',
      desc: isSet(object.desc) ? globalThis.String(object.desc) : '',
      version: isSet(object.version) ? globalThis.Number(object.version) : 0,
      creator: isSet(object.creator) ? globalThis.String(object.creator) : '',
      created_at: isSet(object.created_at) ? globalThis.Number(object.created_at) : 0
    };
  },

  create<I extends Exact<DeepPartial<ConfigHistoryInfo>, I>>(base?: I): ConfigHistoryInfo {
    return ConfigHistoryInfo.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ConfigHistoryInfo>, I>>(object: I): ConfigHistoryInfo {
    const message = createBaseConfigHistoryInfo();
    message.config_history_id = object.config_history_id ?? 0;
    message.config_id = object.config_id ?? 0;
    message.service_id = object.service_id ?? 0;
    message.service_name = object.service_name ?? '';
    message.key = object.key ?? '';
    message.value = object.value ?? '';
    message.desc = object.desc ?? '';
    message.version = object.version ?? 0;
    message.creator = object.creator ?? '';
    message.created_at = object.created_at ?? 0;
    return message;
  }
};

/**
 * 配置
 * smicro:spath=gitit.cc/social/components-service/xtools/handler
 */
export type ConfigDefinition = typeof ConfigDefinition;
export const ConfigDefinition = {
  name: 'Config',
  fullName: 'pbxtools.Config',
  methods: {
    /** 新增配置 */
    createConfig: {
      name: 'CreateConfig',
      requestType: CreateConfigReq,
      requestStream: false,
      responseType: CreateConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 编辑配置 */
    editConfig: {
      name: 'EditConfig',
      requestType: EditConfigReq,
      requestStream: false,
      responseType: EditConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 删除配置 */
    removeConfig: {
      name: 'RemoveConfig',
      requestType: RemoveConfigReq,
      requestStream: false,
      responseType: RemoveConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 查询配置列表 */
    listConfig: {
      name: 'ListConfig',
      requestType: ListConfigReq,
      requestStream: false,
      responseType: ListConfigRsp,
      responseStream: false,
      options: {}
    },
    /** 查询配置历史版本列表 */
    listConfigHistory: {
      name: 'ListConfigHistory',
      requestType: ListConfigHistoryReq,
      requestStream: false,
      responseType: ListConfigHistoryRsp,
      responseStream: false,
      options: {}
    }
  }
} as const;

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
