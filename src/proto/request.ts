import { call<PERSON><PERSON><PERSON> } from '@/modules/bridge';
import { isAppWebview } from '@/modules/utils';
import axios from 'axios';

const MOCK_UID = {
  value: ''
};

if (import.meta.env.MODE !== 'production') {
  MOCK_UID.value = new URL(location.href).searchParams.get('debug_uid') || import.meta.env.VITE_MOCK_UID || '';
}

export interface RequestOptions {
  headers?: Record<string, any>;
  token?: string;
  addonPubParams?: Record<string, any>;
  // 长链接请求，在某些场景时需要用到长链接
  keepAliveLink?: boolean;
  // sentry
  sentryCapture?: boolean;
}

/* eslint-disable @typescript-eslint/no-explicit-any */
export interface RequestContext {
  entry: string;
  wsEntry: string;
  service: string;
  publicParams: Record<string, any>;
}

export class CrushURequestError extends Error {
  httpStatus: number;
  code: number;
  constructor(message: string, code: number, httpStatus: number) {
    super(message);
    this.code = code;
    this.message = message;
    this.httpStatus = httpStatus;
  }

  static from(e: unknown) {
    if (e instanceof Error) {
      return new CrushURequestError(e.message, 10000, 10000);
    }

    if (typeof e === 'string') {
      return new CrushURequestError(e, 10000, 10000);
    }

    if (e && e instanceof Object && 'message' in e && 'code' in e && 'httpStatus' in e) {
      return new CrushURequestError(e.message as string, e.code as number, e.httpStatus as number);
    }

    return new CrushURequestError('Unknown Error', 10000, 10000);
  }
}

export interface RequestImpl {
  request: (method: string, data: any, options?: RequestOptions) => Promise<any>;
}

export class DebugRequest implements RequestImpl {
  private readonly context: RequestContext;
  constructor(context: RequestContext) {
    this.context = context;
    console.log('DebugRequest', this.context);
  }

  async request(method: string, data: any, options?: RequestOptions) {
    const baseUrl = window.__INITIAL_STATE__.appData?.data?.proxyUrl || '';
    const config = this.context;
    const inapp = isAppWebview();
    console.log('debugRequest:', inapp, config.publicParams, options);

    const reqTarget = JSON.stringify({
      service: config.service,
      method: method
    });
    const req = {
      url: baseUrl || config.entry,
      method: 'POST',
      data,
      dataType: 'json',
      headers: {
        'Content-Type': 'application/json',
        'X-reqcontrol-bin': reqTarget,
        ...options?.headers
        // 'X-pubpara-bin': JSON.stringify(Object.assign({}, config.publicParams))
        // 'x-debug-uid': MOCK_UID.value
        // 'x-token-bin':
        //   '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
      }
    };

    if (inapp) {
      let pubParam = await callHandler('getPublicParam', {
        param: 'cou,verc,ver,did,pkg'
      });
      if (typeof pubParam === 'string') {
        pubParam = JSON.parse(pubParam);
      }
      const userInfo = await callHandler('getLoginUser');
      req.headers['x-token-bin'] = await callHandler('getPbToken');
      req.headers['X-pubpara-bin'] = JSON.stringify(
        Object.assign({}, config.publicParams, pubParam, { os_type: 1, cou: userInfo.country })
      );
    } else {
      const qs = Object.fromEntries(new URLSearchParams(location.search).entries());
      const { uid, ...rest } = qs;
      req.headers['X-pubpara-bin'] = JSON.stringify(Object.assign({}, config.publicParams, options?.addonPubParams));
      req.headers['x-debug-uid'] = uid || MOCK_UID.value;
      if (options?.token) {
        req.headers['x-token-bin'] = options?.token;
      }
    }

    const resp = await axios(req);
    const { result } = JSON.parse(
      (resp.headers['x-rspcontrol-bin'] as string) ?? `{ "result": { "code": 10000, "message": "Unknown Error" } }`
    );
    const isHttpSuccess = resp.status === 200;
    const isBackendSuccess = Object.keys(result).length === 0;
    const isSuccess = isHttpSuccess && isBackendSuccess;
    const logAction = isSuccess ? console.debug : console.error;

    if (import.meta.env.MODE !== 'production') {
      logAction.call(
        console,
        `[ts-proto-rpc] Executed Request ${reqTarget} to ${config.entry} \ninput: %o \nresponse: %o`,
        data,
        resp
      );
    }

    if (resp.status !== 200) {
      throw {
        httpStatus: resp.status,
        code: 10000,
        message: `${config.service}.${method} failed.`
      };
    }

    // 什么都没有就是成功
    if (Object.keys(result).length === 0) {
      return resp.data;
    }

    throw {
      httpStatus: resp.status,
      code: result.code,
      message: result.message
    };
  }
}

export class AppRequest implements RequestImpl {
  private readonly context: RequestContext;
  constructor(context: RequestContext) {
    this.context = context;
    console.log('AppRequest', this.context);
  }

  /* async request(method: string, data: any, options?: RequestOptions) {
    const config = this.context;
    const { keepAliveLink = false } = options ?? {};
    const resp = await jsbridgeRequest(keepAliveLink ? config.wsEntry : config.entry, data, {
      methodName: method,
      serviceName: config.service,
      connectType: keepAliveLink ? '2' : '1'
    });

    const { code, netCode, netMessage } = resp;
    const externalCode = code ? `c${code}` : netCode ? `n${netCode}` : 'unknown';
    const externalNetMessage = typeof netMessage === 'string' ? netMessage : '';
    const externalMessage =
      `${resp.message || externalNetMessage || resp.toast || 'Unknown Request Error'}` + ` [${externalCode}]`;

    if (code !== 0 || netCode !== 200) {
      throw {
        httpStatus: resp.netCode,
        code: resp.code,
        message: externalMessage
      };
    }

    return resp.body;
  } */
  async request(method: string, data: any) {
    const config = this.context;
    let resp: any;
    try {
      /* resp = JSBridge.netRequest({
        url: config.entry,
        param: data,
        service: config.service,
        method: method
      }); */
      resp = await callHandler('netRequest', {
        url: config.entry,
        param: data,
        service: config.service,
        method: method,
        tmp: 1
      });
      console.log('resp origin:', method, window.atob(resp.body));
      if (resp.body) {
        resp.body = JSON.parse(window.atob(resp.body));
      }
      console.log('resp', method, resp);
      if (resp.code === 0) {
        return resp.body;
      } else {
        throw {
          httpStatus: resp.code,
          code: resp.code,
          message: resp.message
        };
      }
    } catch (error: any) {
      console.log('error', error);
    }
  }
}
