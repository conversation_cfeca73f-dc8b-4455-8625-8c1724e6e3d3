// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: google/protobuf/struct.proto

/* eslint-disable */

export const protobufPackage = 'google.protobuf';

/**
 * `NullValue` is a singleton enumeration to represent the null value for the
 * `Value` type union.
 *
 *  The JSON representation for `NullValue` is JSON `null`.
 */
export enum NullValue {
  /** NULL_VALUE - Null value. */
  NULL_VALUE = 0,
  UNRECOGNIZED = -1
}

export function nullValueFromJSON(object: any): NullValue {
  switch (object) {
    case 0:
    case 'NULL_VALUE':
      return NullValue.NULL_VALUE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return NullValue.UNRECOGNIZED;
  }
}

/**
 * `Struct` represents a structured data value, consisting of fields
 * which map to dynamically typed values. In some languages, `Struct`
 * might be supported by a native representation. For example, in
 * scripting languages like JS a struct is represented as an
 * object. The details of that representation are described together
 * with the proto support for the language.
 *
 * The JSON representation for `Struct` is JSON object.
 */
export interface Struct {
  /** Unordered map of dynamically typed values. */
  fields: { [key: string]: any | undefined };
}

export interface Struct_FieldsEntry {
  key: string;
  value: any | undefined;
}

/**
 * `Value` represents a dynamically typed value which can be either
 * null, a number, a string, a boolean, a recursive struct value, or a
 * list of values. A producer of value is expected to set one of these
 * variants. Absence of any variant indicates an error.
 *
 * The JSON representation for `Value` is JSON value.
 */
export interface Value {
  /** Represents a null value. */
  null_value?: NullValue | undefined;
  /** Represents a double value. */
  number_value?: number | undefined;
  /** Represents a string value. */
  string_value?: string | undefined;
  /** Represents a boolean value. */
  bool_value?: boolean | undefined;
  /** Represents a structured value. */
  struct_value?: { [key: string]: any } | undefined;
  /** Represents a repeated `Value`. */
  list_value?: Array<any> | undefined;
}

/**
 * `ListValue` is a wrapper around a repeated field of values.
 *
 * The JSON representation for `ListValue` is JSON array.
 */
export interface ListValue {
  /** Repeated field of dynamically typed values. */
  values: any[];
}

function createBaseStruct(): Struct {
  return { fields: {} };
}

export const Struct: MessageFns<Struct> & StructWrapperFns = {
  fromJSON(object: any): Struct {
    return {
      fields: isObject(object.fields)
        ? Object.entries(object.fields).reduce<{ [key: string]: any | undefined }>((acc, [key, value]) => {
            acc[key] = value as any | undefined;
            return acc;
          }, {})
        : {}
    };
  },

  create<I extends Exact<DeepPartial<Struct>, I>>(base?: I): Struct {
    return Struct.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Struct>, I>>(object: I): Struct {
    const message = createBaseStruct();
    message.fields = Object.entries(object.fields ?? {}).reduce<{ [key: string]: any | undefined }>(
      (acc, [key, value]) => {
        if (value !== undefined) {
          acc[key] = value;
        }
        return acc;
      },
      {}
    );
    return message;
  },

  wrap(object: { [key: string]: any } | undefined): Struct {
    const struct = createBaseStruct();

    if (object !== undefined) {
      for (const key of Object.keys(object)) {
        struct.fields[key] = object[key];
      }
    }
    return struct;
  },

  unwrap(message: Struct): { [key: string]: any } {
    const object: { [key: string]: any } = {};
    if (message.fields) {
      for (const key of Object.keys(message.fields)) {
        object[key] = message.fields[key];
      }
    }
    return object;
  }
};

function createBaseStruct_FieldsEntry(): Struct_FieldsEntry {
  return { key: '', value: undefined };
}

export const Struct_FieldsEntry: MessageFns<Struct_FieldsEntry> = {
  fromJSON(object: any): Struct_FieldsEntry {
    return {
      key: isSet(object.key) ? globalThis.String(object.key) : '',
      value: isSet(object?.value) ? object.value : undefined
    };
  },

  create<I extends Exact<DeepPartial<Struct_FieldsEntry>, I>>(base?: I): Struct_FieldsEntry {
    return Struct_FieldsEntry.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Struct_FieldsEntry>, I>>(object: I): Struct_FieldsEntry {
    const message = createBaseStruct_FieldsEntry();
    message.key = object.key ?? '';
    message.value = object.value ?? undefined;
    return message;
  }
};

function createBaseValue(): Value {
  return {
    null_value: undefined,
    number_value: undefined,
    string_value: undefined,
    bool_value: undefined,
    struct_value: undefined,
    list_value: undefined
  };
}

export const Value: MessageFns<Value> & AnyValueWrapperFns = {
  fromJSON(object: any): Value {
    return {
      null_value: isSet(object.null_value) ? nullValueFromJSON(object.null_value) : undefined,
      number_value: isSet(object.number_value) ? globalThis.Number(object.number_value) : undefined,
      string_value: isSet(object.string_value) ? globalThis.String(object.string_value) : undefined,
      bool_value: isSet(object.bool_value) ? globalThis.Boolean(object.bool_value) : undefined,
      struct_value: isObject(object.struct_value) ? object.struct_value : undefined,
      list_value: globalThis.Array.isArray(object.list_value) ? [...object.list_value] : undefined
    };
  },

  create<I extends Exact<DeepPartial<Value>, I>>(base?: I): Value {
    return Value.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Value>, I>>(object: I): Value {
    const message = createBaseValue();
    message.null_value = object.null_value ?? undefined;
    message.number_value = object.number_value ?? undefined;
    message.string_value = object.string_value ?? undefined;
    message.bool_value = object.bool_value ?? undefined;
    message.struct_value = object.struct_value ?? undefined;
    message.list_value = object.list_value ?? undefined;
    return message;
  },

  wrap(value: any): Value {
    const result = createBaseValue();
    if (value === null) {
      result.null_value = NullValue.NULL_VALUE;
    } else if (typeof value === 'boolean') {
      result.bool_value = value;
    } else if (typeof value === 'number') {
      result.number_value = value;
    } else if (typeof value === 'string') {
      result.string_value = value;
    } else if (globalThis.Array.isArray(value)) {
      result.list_value = value;
    } else if (typeof value === 'object') {
      result.struct_value = value;
    } else if (typeof value !== 'undefined') {
      throw new globalThis.Error('Unsupported any value type: ' + typeof value);
    }
    return result;
  },

  unwrap(message: any): string | number | boolean | Object | null | Array<any> | undefined {
    if (message.string_value !== undefined) {
      return message.string_value;
    } else if (message?.number_value !== undefined) {
      return message.number_value;
    } else if (message?.bool_value !== undefined) {
      return message.bool_value;
    } else if (message?.struct_value !== undefined) {
      return message.struct_value as any;
    } else if (message?.list_value !== undefined) {
      return message.list_value;
    } else if (message?.null_value !== undefined) {
      return null;
    }
    return undefined;
  }
};

function createBaseListValue(): ListValue {
  return { values: [] };
}

export const ListValue: MessageFns<ListValue> & ListValueWrapperFns = {
  fromJSON(object: any): ListValue {
    return { values: globalThis.Array.isArray(object?.values) ? [...object.values] : [] };
  },

  create<I extends Exact<DeepPartial<ListValue>, I>>(base?: I): ListValue {
    return ListValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<ListValue>, I>>(object: I): ListValue {
    const message = createBaseListValue();
    message.values = object.values?.map(e => e) || [];
    return message;
  },

  wrap(array: Array<any> | undefined): ListValue {
    const result = createBaseListValue();
    result.values = array ?? [];
    return result;
  },

  unwrap(message: ListValue): Array<any> {
    if (message?.hasOwnProperty('values') && globalThis.Array.isArray(message.values)) {
      return message.values;
    } else {
      return message as any;
    }
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isObject(value: any): boolean {
  return typeof value === 'object' && value !== null;
}

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}

export interface StructWrapperFns {
  wrap(object: { [key: string]: any } | undefined): Struct;
  unwrap(message: Struct): { [key: string]: any };
}

export interface AnyValueWrapperFns {
  wrap(value: any): Value;
  unwrap(message: any): string | number | boolean | Object | null | Array<any> | undefined;
}

export interface ListValueWrapperFns {
  wrap(array: Array<any> | undefined): ListValue;
  unwrap(message: ListValue): Array<any>;
}
