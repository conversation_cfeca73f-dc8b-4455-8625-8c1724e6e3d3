// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: google/protobuf/type.proto

/* eslint-disable */
import { Any } from './any';
import { SourceContext } from './source_context';

export const protobufPackage = 'google.protobuf';

/** The syntax in which a protocol buffer element is defined. */
export enum Syntax {
  /** SYNTAX_PROTO2 - Syntax `proto2`. */
  SYNTAX_PROTO2 = 0,
  /** SYNTAX_PROTO3 - Syntax `proto3`. */
  SYNTAX_PROTO3 = 1,
  UNRECOGNIZED = -1
}

export function syntaxFromJSON(object: any): Syntax {
  switch (object) {
    case 0:
    case 'SYNTAX_PROTO2':
      return Syntax.SYNTAX_PROTO2;
    case 1:
    case 'SYNTAX_PROTO3':
      return Syntax.SYNTAX_PROTO3;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Syntax.UNRECOGNIZED;
  }
}

/** A protocol buffer message type. */
export interface Type {
  /** The fully qualified message name. */
  name: string;
  /** The list of fields. */
  fields: Field[];
  /** The list of types appearing in `oneof` definitions in this type. */
  oneofs: string[];
  /** The protocol buffer options. */
  options: Option[];
  /** The source context. */
  source_context: SourceContext | undefined;
  /** The source syntax. */
  syntax: Syntax;
}

/** A single field of a message type. */
export interface Field {
  /** The field type. */
  kind: Field_Kind;
  /** The field cardinality. */
  cardinality: Field_Cardinality;
  /** The field number. */
  number: number;
  /** The field name. */
  name: string;
  /**
   * The field type URL, without the scheme, for message or enumeration
   * types. Example: `"type.googleapis.com/google.protobuf.Timestamp"`.
   */
  type_url: string;
  /**
   * The index of the field type in `Type.oneofs`, for message or enumeration
   * types. The first type has index 1; zero means the type is not in the list.
   */
  oneof_index: number;
  /** Whether to use alternative packed wire representation. */
  packed: boolean;
  /** The protocol buffer options. */
  options: Option[];
  /** The field JSON name. */
  json_name: string;
  /** The string value of the default value of this field. Proto2 syntax only. */
  default_value: string;
}

/** Basic field types. */
export enum Field_Kind {
  /** TYPE_UNKNOWN - Field type unknown. */
  TYPE_UNKNOWN = 0,
  /** TYPE_DOUBLE - Field type double. */
  TYPE_DOUBLE = 1,
  /** TYPE_FLOAT - Field type float. */
  TYPE_FLOAT = 2,
  /** TYPE_INT64 - Field type int64. */
  TYPE_INT64 = 3,
  /** TYPE_UINT64 - Field type uint64. */
  TYPE_UINT64 = 4,
  /** TYPE_INT32 - Field type int32. */
  TYPE_INT32 = 5,
  /** TYPE_FIXED64 - Field type fixed64. */
  TYPE_FIXED64 = 6,
  /** TYPE_FIXED32 - Field type fixed32. */
  TYPE_FIXED32 = 7,
  /** TYPE_BOOL - Field type bool. */
  TYPE_BOOL = 8,
  /** TYPE_STRING - Field type string. */
  TYPE_STRING = 9,
  /** TYPE_GROUP - Field type group. Proto2 syntax only, and deprecated. */
  TYPE_GROUP = 10,
  /** TYPE_MESSAGE - Field type message. */
  TYPE_MESSAGE = 11,
  /** TYPE_BYTES - Field type bytes. */
  TYPE_BYTES = 12,
  /** TYPE_UINT32 - Field type uint32. */
  TYPE_UINT32 = 13,
  /** TYPE_ENUM - Field type enum. */
  TYPE_ENUM = 14,
  /** TYPE_SFIXED32 - Field type sfixed32. */
  TYPE_SFIXED32 = 15,
  /** TYPE_SFIXED64 - Field type sfixed64. */
  TYPE_SFIXED64 = 16,
  /** TYPE_SINT32 - Field type sint32. */
  TYPE_SINT32 = 17,
  /** TYPE_SINT64 - Field type sint64. */
  TYPE_SINT64 = 18,
  UNRECOGNIZED = -1
}

export function field_KindFromJSON(object: any): Field_Kind {
  switch (object) {
    case 0:
    case 'TYPE_UNKNOWN':
      return Field_Kind.TYPE_UNKNOWN;
    case 1:
    case 'TYPE_DOUBLE':
      return Field_Kind.TYPE_DOUBLE;
    case 2:
    case 'TYPE_FLOAT':
      return Field_Kind.TYPE_FLOAT;
    case 3:
    case 'TYPE_INT64':
      return Field_Kind.TYPE_INT64;
    case 4:
    case 'TYPE_UINT64':
      return Field_Kind.TYPE_UINT64;
    case 5:
    case 'TYPE_INT32':
      return Field_Kind.TYPE_INT32;
    case 6:
    case 'TYPE_FIXED64':
      return Field_Kind.TYPE_FIXED64;
    case 7:
    case 'TYPE_FIXED32':
      return Field_Kind.TYPE_FIXED32;
    case 8:
    case 'TYPE_BOOL':
      return Field_Kind.TYPE_BOOL;
    case 9:
    case 'TYPE_STRING':
      return Field_Kind.TYPE_STRING;
    case 10:
    case 'TYPE_GROUP':
      return Field_Kind.TYPE_GROUP;
    case 11:
    case 'TYPE_MESSAGE':
      return Field_Kind.TYPE_MESSAGE;
    case 12:
    case 'TYPE_BYTES':
      return Field_Kind.TYPE_BYTES;
    case 13:
    case 'TYPE_UINT32':
      return Field_Kind.TYPE_UINT32;
    case 14:
    case 'TYPE_ENUM':
      return Field_Kind.TYPE_ENUM;
    case 15:
    case 'TYPE_SFIXED32':
      return Field_Kind.TYPE_SFIXED32;
    case 16:
    case 'TYPE_SFIXED64':
      return Field_Kind.TYPE_SFIXED64;
    case 17:
    case 'TYPE_SINT32':
      return Field_Kind.TYPE_SINT32;
    case 18:
    case 'TYPE_SINT64':
      return Field_Kind.TYPE_SINT64;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Field_Kind.UNRECOGNIZED;
  }
}

/** Whether a field is optional, required, or repeated. */
export enum Field_Cardinality {
  /** CARDINALITY_UNKNOWN - For fields with unknown cardinality. */
  CARDINALITY_UNKNOWN = 0,
  /** CARDINALITY_OPTIONAL - For optional fields. */
  CARDINALITY_OPTIONAL = 1,
  /** CARDINALITY_REQUIRED - For required fields. Proto2 syntax only. */
  CARDINALITY_REQUIRED = 2,
  /** CARDINALITY_REPEATED - For repeated fields. */
  CARDINALITY_REPEATED = 3,
  UNRECOGNIZED = -1
}

export function field_CardinalityFromJSON(object: any): Field_Cardinality {
  switch (object) {
    case 0:
    case 'CARDINALITY_UNKNOWN':
      return Field_Cardinality.CARDINALITY_UNKNOWN;
    case 1:
    case 'CARDINALITY_OPTIONAL':
      return Field_Cardinality.CARDINALITY_OPTIONAL;
    case 2:
    case 'CARDINALITY_REQUIRED':
      return Field_Cardinality.CARDINALITY_REQUIRED;
    case 3:
    case 'CARDINALITY_REPEATED':
      return Field_Cardinality.CARDINALITY_REPEATED;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return Field_Cardinality.UNRECOGNIZED;
  }
}

/** Enum type definition. */
export interface Enum {
  /** Enum type name. */
  name: string;
  /** Enum value definitions. */
  enumvalue: EnumValue[];
  /** Protocol buffer options. */
  options: Option[];
  /** The source context. */
  source_context: SourceContext | undefined;
  /** The source syntax. */
  syntax: Syntax;
}

/** Enum value definition. */
export interface EnumValue {
  /** Enum value name. */
  name: string;
  /** Enum value number. */
  number: number;
  /** Protocol buffer options. */
  options: Option[];
}

/**
 * A protocol buffer option, which can be attached to a message, field,
 * enumeration, etc.
 */
export interface Option {
  /**
   * The option's name. For protobuf built-in options (options defined in
   * descriptor.proto), this is the short name. For example, `"map_entry"`.
   * For custom options, it should be the fully-qualified name. For example,
   * `"google.api.http"`.
   */
  name: string;
  /**
   * The option's value packed in an Any message. If the value is a primitive,
   * the corresponding wrapper type defined in google/protobuf/wrappers.proto
   * should be used. If the value is an enum, it should be stored as an int32
   * value using the google.protobuf.Int32Value type.
   */
  value: Any | undefined;
}

function createBaseType(): Type {
  return { name: '', fields: [], oneofs: [], options: [], source_context: undefined, syntax: 0 };
}

export const Type: MessageFns<Type> = {
  fromJSON(object: any): Type {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      fields: globalThis.Array.isArray(object?.fields) ? object.fields.map((e: any) => Field.fromJSON(e)) : [],
      oneofs: globalThis.Array.isArray(object?.oneofs) ? object.oneofs.map((e: any) => globalThis.String(e)) : [],
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => Option.fromJSON(e)) : [],
      source_context: isSet(object.source_context) ? SourceContext.fromJSON(object.source_context) : undefined,
      syntax: isSet(object.syntax) ? syntaxFromJSON(object.syntax) : 0
    };
  },

  create<I extends Exact<DeepPartial<Type>, I>>(base?: I): Type {
    return Type.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Type>, I>>(object: I): Type {
    const message = createBaseType();
    message.name = object.name ?? '';
    message.fields = object.fields?.map(e => Field.fromPartial(e)) || [];
    message.oneofs = object.oneofs?.map(e => e) || [];
    message.options = object.options?.map(e => Option.fromPartial(e)) || [];
    message.source_context =
      object.source_context !== undefined && object.source_context !== null
        ? SourceContext.fromPartial(object.source_context)
        : undefined;
    message.syntax = object.syntax ?? 0;
    return message;
  }
};

function createBaseField(): Field {
  return {
    kind: 0,
    cardinality: 0,
    number: 0,
    name: '',
    type_url: '',
    oneof_index: 0,
    packed: false,
    options: [],
    json_name: '',
    default_value: ''
  };
}

export const Field: MessageFns<Field> = {
  fromJSON(object: any): Field {
    return {
      kind: isSet(object.kind) ? field_KindFromJSON(object.kind) : 0,
      cardinality: isSet(object.cardinality) ? field_CardinalityFromJSON(object.cardinality) : 0,
      number: isSet(object.number) ? globalThis.Number(object.number) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      type_url: isSet(object.type_url) ? globalThis.String(object.type_url) : '',
      oneof_index: isSet(object.oneof_index) ? globalThis.Number(object.oneof_index) : 0,
      packed: isSet(object.packed) ? globalThis.Boolean(object.packed) : false,
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => Option.fromJSON(e)) : [],
      json_name: isSet(object.json_name) ? globalThis.String(object.json_name) : '',
      default_value: isSet(object.default_value) ? globalThis.String(object.default_value) : ''
    };
  },

  create<I extends Exact<DeepPartial<Field>, I>>(base?: I): Field {
    return Field.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Field>, I>>(object: I): Field {
    const message = createBaseField();
    message.kind = object.kind ?? 0;
    message.cardinality = object.cardinality ?? 0;
    message.number = object.number ?? 0;
    message.name = object.name ?? '';
    message.type_url = object.type_url ?? '';
    message.oneof_index = object.oneof_index ?? 0;
    message.packed = object.packed ?? false;
    message.options = object.options?.map(e => Option.fromPartial(e)) || [];
    message.json_name = object.json_name ?? '';
    message.default_value = object.default_value ?? '';
    return message;
  }
};

function createBaseEnum(): Enum {
  return { name: '', enumvalue: [], options: [], source_context: undefined, syntax: 0 };
}

export const Enum: MessageFns<Enum> = {
  fromJSON(object: any): Enum {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      enumvalue: globalThis.Array.isArray(object?.enumvalue)
        ? object.enumvalue.map((e: any) => EnumValue.fromJSON(e))
        : [],
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => Option.fromJSON(e)) : [],
      source_context: isSet(object.source_context) ? SourceContext.fromJSON(object.source_context) : undefined,
      syntax: isSet(object.syntax) ? syntaxFromJSON(object.syntax) : 0
    };
  },

  create<I extends Exact<DeepPartial<Enum>, I>>(base?: I): Enum {
    return Enum.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Enum>, I>>(object: I): Enum {
    const message = createBaseEnum();
    message.name = object.name ?? '';
    message.enumvalue = object.enumvalue?.map(e => EnumValue.fromPartial(e)) || [];
    message.options = object.options?.map(e => Option.fromPartial(e)) || [];
    message.source_context =
      object.source_context !== undefined && object.source_context !== null
        ? SourceContext.fromPartial(object.source_context)
        : undefined;
    message.syntax = object.syntax ?? 0;
    return message;
  }
};

function createBaseEnumValue(): EnumValue {
  return { name: '', number: 0, options: [] };
}

export const EnumValue: MessageFns<EnumValue> = {
  fromJSON(object: any): EnumValue {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      number: isSet(object.number) ? globalThis.Number(object.number) : 0,
      options: globalThis.Array.isArray(object?.options) ? object.options.map((e: any) => Option.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<EnumValue>, I>>(base?: I): EnumValue {
    return EnumValue.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<EnumValue>, I>>(object: I): EnumValue {
    const message = createBaseEnumValue();
    message.name = object.name ?? '';
    message.number = object.number ?? 0;
    message.options = object.options?.map(e => Option.fromPartial(e)) || [];
    return message;
  }
};

function createBaseOption(): Option {
  return { name: '', value: undefined };
}

export const Option: MessageFns<Option> = {
  fromJSON(object: any): Option {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      value: isSet(object.value) ? Any.fromJSON(object.value) : undefined
    };
  },

  create<I extends Exact<DeepPartial<Option>, I>>(base?: I): Option {
    return Option.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Option>, I>>(object: I): Option {
    const message = createBaseOption();
    message.name = object.name ?? '';
    message.value = object.value !== undefined && object.value !== null ? Any.fromPartial(object.value) : undefined;
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
