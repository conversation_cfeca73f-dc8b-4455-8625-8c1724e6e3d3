// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.3.0
//   protoc               v6.31.1
// source: config/conf.proto

/* eslint-disable */

export const protobufPackage = 'sconfig.lucky.taskconf';

export enum RewardType {
  REWARD_TYPE_NONE = 0,
  REWARD_TYPE_COIN = 1,
  REWARD_TYPE_POINT = 2,
  REWARD_TYPE_CHAT_CARD = 3,
  REWARD_TYPE_VIDEO_CARD = 4,
  REWARD_TYPE_AVATAR_FRAME = 5,
  REWARD_TYPE_CHAT_BUBBLE = 6,
  UNRECOGNIZED = -1
}

export function rewardTypeFromJSON(object: any): RewardType {
  switch (object) {
    case 0:
    case 'REWARD_TYPE_NONE':
      return RewardType.REWARD_TYPE_NONE;
    case 1:
    case 'REWARD_TYPE_COIN':
      return RewardType.REWARD_TYPE_COIN;
    case 2:
    case 'REWARD_TYPE_POINT':
      return RewardType.REWARD_TYPE_POINT;
    case 3:
    case 'REWARD_TYPE_CHAT_CARD':
      return RewardType.REWARD_TYPE_CHAT_CARD;
    case 4:
    case 'REWARD_TYPE_VIDEO_CARD':
      return RewardType.REWARD_TYPE_VIDEO_CARD;
    case 5:
    case 'REWARD_TYPE_AVATAR_FRAME':
      return RewardType.REWARD_TYPE_AVATAR_FRAME;
    case 6:
    case 'REWARD_TYPE_CHAT_BUBBLE':
      return RewardType.REWARD_TYPE_CHAT_BUBBLE;
    case -1:
    case 'UNRECOGNIZED':
    default:
      return RewardType.UNRECOGNIZED;
  }
}

export interface Config {
  reward_conf: TaskRewardPackageConf[];
}

export interface TaskRewardPackageConf {
  package_id: string;
  package_name: string;
  rewards: Reward[];
}

export interface Reward {
  type: RewardType;
  amount: number;
  effective_day: number;
  name: string;
  item_id: string;
}

function createBaseConfig(): Config {
  return { reward_conf: [] };
}

export const Config: MessageFns<Config> = {
  fromJSON(object: any): Config {
    return {
      reward_conf: globalThis.Array.isArray(object?.reward_conf)
        ? object.reward_conf.map((e: any) => TaskRewardPackageConf.fromJSON(e))
        : []
    };
  },

  create<I extends Exact<DeepPartial<Config>, I>>(base?: I): Config {
    return Config.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Config>, I>>(object: I): Config {
    const message = createBaseConfig();
    message.reward_conf = object.reward_conf?.map(e => TaskRewardPackageConf.fromPartial(e)) || [];
    return message;
  }
};

function createBaseTaskRewardPackageConf(): TaskRewardPackageConf {
  return { package_id: '', package_name: '', rewards: [] };
}

export const TaskRewardPackageConf: MessageFns<TaskRewardPackageConf> = {
  fromJSON(object: any): TaskRewardPackageConf {
    return {
      package_id: isSet(object.package_id) ? globalThis.String(object.package_id) : '',
      package_name: isSet(object.package_name) ? globalThis.String(object.package_name) : '',
      rewards: globalThis.Array.isArray(object?.rewards) ? object.rewards.map((e: any) => Reward.fromJSON(e)) : []
    };
  },

  create<I extends Exact<DeepPartial<TaskRewardPackageConf>, I>>(base?: I): TaskRewardPackageConf {
    return TaskRewardPackageConf.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<TaskRewardPackageConf>, I>>(object: I): TaskRewardPackageConf {
    const message = createBaseTaskRewardPackageConf();
    message.package_id = object.package_id ?? '';
    message.package_name = object.package_name ?? '';
    message.rewards = object.rewards?.map(e => Reward.fromPartial(e)) || [];
    return message;
  }
};

function createBaseReward(): Reward {
  return { type: 0, amount: 0, effective_day: 0, name: '', item_id: '' };
}

export const Reward: MessageFns<Reward> = {
  fromJSON(object: any): Reward {
    return {
      type: isSet(object.type) ? rewardTypeFromJSON(object.type) : 0,
      amount: isSet(object.amount) ? globalThis.Number(object.amount) : 0,
      effective_day: isSet(object.effective_day) ? globalThis.Number(object.effective_day) : 0,
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      item_id: isSet(object.item_id) ? globalThis.String(object.item_id) : ''
    };
  },

  create<I extends Exact<DeepPartial<Reward>, I>>(base?: I): Reward {
    return Reward.fromPartial(base ?? ({} as any));
  },
  fromPartial<I extends Exact<DeepPartial<Reward>, I>>(object: I): Reward {
    const message = createBaseReward();
    message.type = object.type ?? 0;
    message.amount = object.amount ?? 0;
    message.effective_day = object.effective_day ?? 0;
    message.name = object.name ?? '';
    message.item_id = object.item_id ?? '';
    return message;
  }
};

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined;

export type DeepPartial<T> = T extends Builtin
  ? T
  : T extends globalThis.Array<infer U>
    ? globalThis.Array<DeepPartial<U>>
    : T extends ReadonlyArray<infer U>
      ? ReadonlyArray<DeepPartial<U>>
      : T extends {}
        ? { [K in keyof T]?: DeepPartial<T[K]> }
        : Partial<T>;

type KeysOfUnion<T> = T extends T ? keyof T : never;
export type Exact<P, I extends P> = P extends Builtin
  ? P
  : P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never };

function isSet(value: any): boolean {
  return value !== null && value !== undefined;
}

export interface MessageFns<T> {
  fromJSON(object: any): T;
  create<I extends Exact<DeepPartial<T>, I>>(base?: I): T;
  fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T;
}
