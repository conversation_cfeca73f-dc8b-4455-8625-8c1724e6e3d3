import { useEffect, useState } from 'react';
import { getLangText } from './login/lib/utils';
import IconApple from './login/images/<EMAIL>';
import { Button, Dialog, Loading, Toast } from 'react-vant';
import DeleteAccount from './login/components/delete-account';
import { AppleConfigType } from './login/lib/constant';
import { getQueryParams } from '@/modules/utils';
import { requestProxy } from '@/modules/request';
import './login/index.scss';

type LoginProp = {
  appName?: string;
  baseUrl: string;
  pkg: string;
  anm: string;
  subanm: string;
  env: string;
  gradient: string;
  appleConfig?: AppleConfigType;
};

// cliend_id: com.duoaa.live.dev
// team_id: 4944NG7349
// key_id: S47WM8267H

export function IOSAccountLogin({ subanm, env, pkg, baseUrl, appleConfig, ...props }: LoginProp) {
  const UserInfoKey = `${subanm}_user_info`;
  const t = getLangText(props.appName || 'Crushu');
  const queryParams = getQueryParams();
  const [userInfo, setUserInfo] = useState<any>({}); // 登录成功后 用户信息
  const [logined, setLogined] = useState(false);
  const [navigatingLoading, setNavigatingLoading] = useState(false);
  const baseParam = { s_time: '********', subanm, anm: 'cu', pkg, pf: 'web' };

  useEffect(() => {
    const userInfo = localStorage.getItem(UserInfoKey);
    if (userInfo && userInfo.length > 20) {
      setUserInfo(JSON.parse(userInfo));
      setLogined(true);
    } else if (queryParams.appleToken) {
      login();
    }
  }, []);

  const login = async () => {
    const l = Toast.loading({
      message: 'Logging in...',
      duration: 0,
      forbidClick: true
    });
    try {
      // 登录接口
      const res = await requestProxy(`${baseUrl}/account/login`, {
        apple_token: queryParams.appleToken,
        login_type: 'apple_login',
        ...baseParam
      });
      if (res.status === 1) {
        const { info, token, msg } = res.data;
        if (info) {
          const baseStr = info.base;
          let newUserInfo: any = {};
          try {
            const baseObj = JSON.parse(baseStr);
            newUserInfo = {
              token,
              headimgurl: baseObj.avatar,
              name: baseObj.nickname,
              uid: baseObj.uid,
              did: baseObj.did
            };
          } catch (error) {
            console.log(error);
          }
          setUserInfo(newUserInfo);
          setLogined(true);
          // 用户信息存入缓存
          localStorage.setItem(UserInfoKey, JSON.stringify(newUserInfo));
        } else {
          Toast.fail(msg);
        }
      } else {
        Toast.fail('login fail!');
      }
    } catch (error: any) {
      console.log(error);
      Toast.fail(error?.msg ?? 'login failed');
    } finally {
      l.clear();
    }
  };
  const logout = () => {
    Dialog.alert({
      closeable: true,
      theme: 'round-button',
      message: <div className="logout-content">{t.logoutConfirm}</div>,
      confirmButtonColor: props.gradient,
      confirmButtonText: t.confirm,
      onConfirm: async () => {
        localStorage.removeItem(UserInfoKey);
        setLogined(false);
        setUserInfo({});
        Toast.info({
          message: 'Logout successfully!',
          className: 'wide-toast'
        });
      }
    });
  };

  /** 注销用户界面操作 */
  const deleteAccount = async () => {
    const { uid, did, token } = userInfo;
    const reqParams: any = {
      uid,
      did,
      ...baseParam
    };
    const res = await requestProxy(`${baseUrl}/account/close`, reqParams, { headers: { 'X-TOKEN': token } }).catch(
      error => {
        // console.log("error: ", error);
        const msg = error.msg ?? 'Delete account fail!';
        Toast.fail(msg);
        if (error.status === 10003) {
          localStorage.removeItem(UserInfoKey);
        } else {
          throw new Error(msg); // 抛出异常
        }
      }
    );
    if (res && res.status === 1) {
      localStorage.removeItem(UserInfoKey);
      setLogined(false);
      setUserInfo({});
      Toast.info({
        message: 'Delete account successfully!',
        className: 'wide-toast'
      });
    }
  };

  return (
    <>
      <div className="login-page flex-center h-screen w-screen flex-col bg-[#f5f5f5] !pt-0">
        {logined ? (
          <div className="content">
            {userInfo?.headimgurl ? <img className="pxw-288 pxh-288 rounded-full" src={userInfo.headimgurl} /> : null}
            <div className="user-name">Nickname: {userInfo?.name ?? '-'}</div>
            <div className="user-id-field">ID: {userInfo?.uid ?? '-'}</div>
            <DeleteAccount pageLang={t} userInfo={userInfo} onDeleteAccount={deleteAccount} />
            <Button className="logout-btn" onClick={logout}>
              {t.logout}
            </Button>
          </div>
        ) : (
          <>
            <h1 className="pxtext-40 pxleading-50 text-[#121212]">{t.loginFirst}</h1>
            <div className="pxtext-32 pxmy-20 w-auto text-[#808080]">{t.loginTip}</div>
            <div className="login-list pxmt-34">
              <div
                className="vww-80 vwh-17 flex-center rounded-[16px] bg-white"
                onClick={() => {
                  setNavigatingLoading(true);
                  location.href = `${baseUrl}/apple/authorize?client=${appleConfig?.appId}&pkg=${pkg}&subanm=${subanm}`;
                }}
              >
                <img src={IconApple} className="pxw-100 pxh-100 rounded-full" alt="" />
                <span className="pxtext-36 pxmx-16 font-medium text-[#333]">{t.Apple}</span>
                {navigatingLoading && <Loading type="spinner" size="24" />}
              </div>
            </div>
          </>
        )}

        <div
          className={`vww-10 vwh-10 lg:pxw-50 lg:pxh-50 absolute left-[3vw] top-[3vw] bg-[url(@/assets/icons/icon-back.png)] bg-contain bg-center bg-no-repeat`}
          onClick={() => history.back()}
        />
      </div>
    </>
  );
}
