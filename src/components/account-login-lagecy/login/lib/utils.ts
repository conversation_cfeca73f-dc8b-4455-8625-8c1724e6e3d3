/**
 * @name 获取浏览器设备信息
 * @returns Object
 */
export function getBrowser() {
  const UA = navigator.userAgent.toLocaleLowerCase() || '';
  const isAndroid = UA.match(/Android/i) ? true : false;
  const isQQ =
    /(iPad|iPhone|iPod).*? (IPad)?QQ\/([.\d]+)/.test(UA) || /\bV1_AND_SQI?_([.\d]+)(.*? QQ\/([.\d]+))?/.test(UA);
  const isIOS = UA.match(/iPhone|iPad|iPod/i) ? true : false;
  const isIpone = UA.indexOf('iphone') > -1 ? true : false;
  const isSafari = /iPhone|iPad|iPod\/([\w.]+).*(safari).*/i.test(UA);
  // 微信
  const isWx = UA.match(/micromessenger/i) ? true : false;
  // 微博
  const isWb = UA.match(/weibo/i) ? true : false;
  const isAndroidChrome = (UA.match(/Chrome\/([\d.]+)/) || UA.match(/CriOS\/([\d.]+)/)) && isAndroid && !isQQ;
  // qq空间
  const isQZ = UA.indexOf('Qzone/') !== -1;
  const isMobile = !!UA.match(/AppleWebKit.*Mobile.*/);
  return {
    isAndroid,
    isQQ,
    isIOS,
    isSafari,
    isWx,
    isWb,
    isAndroidChrome,
    isQZ,
    isIpone,
    isMobile
  };
}

export const formatTypeList = (list, exclude, otherType) => {
  let newList: any = [];
  if (!Array.isArray(exclude) || !exclude.length) {
    newList = JSON.parse(JSON.stringify(list));
  } else {
    newList = list.filter(item => !exclude.includes(item.type));
  }
  if (otherType && otherType.length > 0) {
    for (const otherItem of otherType) {
      switch (otherItem) {
        case 'Account':
          newList.push({
            type: 'Account',
            icon: ''
          });
          break;
        default:
          break;
      }
    }
  }
  return newList;
};

export const getLangText = (appname: string) => ({
  deleteAccount: 'Delete Account',
  logout: 'Log out',
  loginFirst: `Log in to ${appname[0].toUpperCase()}${appname.slice(1)}`,
  loginTip: 'Tips: You can only log in, but cannot register.',
  phoneNumber: 'Phone number',
  password: 'Verification code',
  loginIncorrectTip: 'Incorrect account/code, please try again~',
  confirm: 'Confirm',
  cancel: 'Cancel',
  selectCountryCodeTip: 'Please select the country code!',
  phoneNumberCannotBeEmpty: 'Phone number cannot be empty!',
  passwordCannotBeEmpty: 'Code cannot be empty!',
  CodeIncorrect: 'Verification code is incorrect!',
  Google: 'Google',
  Facebook: 'Facebook',
  Twitter: 'Twitter',
  Snapchat: 'Snapchat',
  Apple: 'Apple',
  Phone: 'Phone',
  Email: 'Email',
  loginFail: 'login failed, please try again!',
  notAllowInH5: 'This account does not exist!',
  logoutConfirm: 'Are you sure to logout?',
  deleteConfirm: 'Are you sure delete account?',
  deleteAccountConfirm:
    'Deleting the account cannot be undone, and the account balance will be cleared. Please proceed with caution.'
});
