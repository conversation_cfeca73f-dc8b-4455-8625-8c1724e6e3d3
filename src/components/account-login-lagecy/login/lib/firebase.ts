import { initializeApp } from 'firebase/app';
import { getAuth, signInWithPopup, signInWithRedirect } from 'firebase/auth';
import { FirebaseConfigType } from './constant';
import { getBrowser } from './utils';
const { isMobile } = getBrowser();

export default (provider: any, firebaseConfig: FirebaseConfigType) => {
  return new Promise<any>((resolve, reject) => {
    const app = initializeApp(firebaseConfig);
    const auth = getAuth(app);
    if (isMobile) {
      signInWithRedirect(auth, provider)
        .then(res => {
          resolve(res);
        })
        .catch(err => reject(err));
    } else {
      signInWithPopup(auth, provider)
        .then(res => {
          resolve(res);
        })
        .catch(err => reject(err));
    }
  });
};
