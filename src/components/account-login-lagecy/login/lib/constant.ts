import { getBrowser } from './utils';
const { isAndroid, isIOS } = getBrowser();
import gpIcon from '../images/<EMAIL>';
import fbIcon from '../images/<EMAIL>';
import twIcon from '../images/<EMAIL>';
import appleIcon from '../images/<EMAIL>';
import phoneIcon from '../images/<EMAIL>';

export interface FirebaseConfigType {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
}

export interface AppleConfigType {
  appId: string;
  team_id?: string;
  key_id?: string;
  key_auth?: string;
}

export interface LoginParams {
  type: string; // 登录类型
  openid?: string; // openid
  area_code?: string; // 手机号登录-国家区号
  phone?: string; // 手机号登录-手机号
  pass?: string; // 手机号登录-密码
}

export interface UserInfo {
  headimgurl?: string;
  name?: string;
  uid?: string | number;
}

export interface IProps {
  primaryColor?: string;
  wrapperClassName?: string;
  pageLang: Record<string, string>;
  onLogin: (params: LoginParams) => Promise<any>;
  onLogout: () => Promise<any>;
  onDeleteAccount: () => Promise<any>;
  excludeType?: Array<'Google' | 'Facebook' | 'Twitter' | 'Snapchat' | 'Apple' | 'Phone'>;
  otherType?: Array<'Account'>;
  userInfo: UserInfo;
  firebaseConfig?: FirebaseConfigType;
  appleConfigType?: AppleConfigType;
  snapchatClientId?: string;
  env?: string;
  pkg?: string;
  subanm?: string;
  baseUrl?: string;
}

export const firebaseConfig: FirebaseConfigType = {
  apiKey: '',
  authDomain: '',
  projectId: '',
  storageBucket: '',
  messagingSenderId: '',
  appId: ''
};

export const LoginType = {
  Google: 'Google',
  Facebook: 'Facebook',
  Twitter: 'Twitter',
  Snapchat: 'Snapchat',
  Apple: 'Apple',
  Phone: 'Phone',
  Account: 'Account'
};

export const LoginApiType = {
  [LoginType.Google]: 'gp',
  [LoginType.Facebook]: 'fb',
  [LoginType.Twitter]: 'tw',
  [LoginType.Snapchat]: 'sc',
  [LoginType.Apple]: 'ap'
};

export const LoginTypeList: Array<any> = [
  {
    type: LoginType.Google,
    icon: gpIcon
  },
  {
    type: LoginType.Facebook,
    icon: fbIcon
  },
  {
    type: LoginType.Twitter,
    icon: twIcon
  },
  isAndroid
    ? null
    : {
        type: LoginType.Apple,
        icon: appleIcon
      },
  {
    type: LoginType.Phone,
    icon: phoneIcon
  }
].filter(i => i);
