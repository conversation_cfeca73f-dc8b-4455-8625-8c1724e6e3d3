@import '../../../assets/styles/common.css';

.login-page {
  padding-top: 276px;
  height: 100vh;
  background: #f5f5f5;
  position: absolute;
  left: 0;
  width: 100%;
  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
  }
  .user-avator {
    width: 180px;
    height: 180px;
    border: 2px solid rgba(255, 255, 255, 0.68);
    border-radius: 50%;
  }
  .user-id,
  .user-name {
    margin-top: 12px;
    font-size: 36px;
    font-weight: 600;
    color: #212121;
    line-height: 50px;
  }
  .user-id-field {
    margin-top: 12px;
    font-size: 24px;
    font-weight: 400;
    color: #908b9f;
  }
  .delete-btn,
  .logout-btn {
    width: 520px;
    height: 92px;
    background: #ffffff;
    border-radius: 16px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28px;
    font-weight: 500;
    color: #ff0000;
  }
  .delete-btn {
    color: #333333;
    margin-top: 70px;
  }
  .login-tip {
    font-size: 28px;
    font-weight: 500;
    color: #808080;
    line-height: 0.8rem;
    text-align: center;
    width: 560px;
    margin: 20px 0;
  }
  .login-list {
    margin-top: 34px;
  }
}
.logout-content {
  font-size: 24px;
  text-align: center;
  margin: 32px 10px;
  line-height: 36px;
  word-break: break-word;
}
.loading-wrapper {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  pointer-events: auto;
}
.rv-toast.rv-popup {
  width: auto;
  max-width: none !important;
}
.login-phone-dialog.rv-popup {
  width: 546px;
}

@media screen and (max-width: 768px) {
  .login-page {
    .user-avator {
      height: 2.88rem;
      width: 2.88rem;
    }
    .user-name {
      font-size: 0.42rem;
      line-height: 0.42rem;
    }
    .user-id-field {
      font-size: 0.4rem;
    }
    .user-id {
      font-size: 20px;
      line-height: 20px;
    }
    .login-tip {
      font-size: 16px;
      width: auto !important;
    }
    .login-item {
      width: 336px;
      height: 65px;
      padding-inline-start: unset;
      justify-content: center;
      .login-icon {
        width: 1rem;
        height: 1rem;
        margin-inline-end: 0.32rem;
      }
      .login-title {
        font-size: 0.36rem;
      }
    }
    .logout-btn,
    .delete-btn {
      width: 92%;
      height: 1rem;
      border-radius: 0.2rem;
      font-size: 0.32rem;
    }
    .delete-btn {
      margin-top: 0.72rem;
      margin-bottom: 0.32rem;
    }
  }
  .login-phone-dialog.rv-popup {
    width: 90%;
    .rv-input__control,
    .rv-field__error-message,
    .rv-space__item,
    .selected-option,
    .rv-cell {
      font-size: 0.32rem;
      line-height: 0.32rem;
    }
    .mobile-input {
      height: 1rem;
      width: 6.1rem;
      border-radius: 0.2rem;
    }
    .mobile-input__code {
      height: 4.2rem;
    }
    .password-input {
      height: 1rem;
      width: 4.2rem;
      border-radius: 0.2rem;
    }
    .send-code-btn {
      width: 1.8rem;
      font-size: 0.28rem;
      height: 1rem;
      line-height: 1rem;
      text-transform: capitalize;
    }
    .cancel-btn,
    .phone-confirm-btn {
      height: 1rem;
      width: 90.5%;
      font-size: 0.32rem;
    }
  }
  .rv-toast.rv-popup {
    font-size: 0.21rem;
    padding: 1rem;
  }
  .rv-dialog.rv-popup {
    .rv-button__text,
    .logout-content {
      font-size: 0.28rem;
      padding: 0.24rem;
    }
  }
  .delete-account-dialog.rv-popup {
    width: 90% !important;
    font-size: 0.28rem;
    padding: 0.24rem;
    .content {
      padding: 0.48rem;
      .user-avator {
        height: 2.4rem;
        width: 2.4rem;
      }
      .user-name {
        font-size: 0.32rem;
        margin-bottom: 0.24rem;
      }
      .tip {
        font-size: 0.28rem;
        line-height: 0.28rem;
        width: 100%;
      }
    }
    .confirm-btn {
      font-size: 0.32rem;
      height: 1rem;
      line-height: 1rem;
      width: 90%;
      border-radius: 0.2rem;
    }
  }
  .rv-picker-column {
    font-size: 0.32rem;
    .country-item__right,
    .country-item__left {
      font-size: 0.32rem;
    }
  }
}
