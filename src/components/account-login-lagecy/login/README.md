# 登录通用UI组件
![效果图](./images/preview.png "登录UI界面")

## 调用方法
```js
import LoginComponent from '@/component/login';
<LoginComponent
  primaryColor="#ffb600"
  wrapperClassName="login-wrapper"
  pageLang={pageLang}
  countryPhoneCodeList={countryPhoneCodeList}
  onLogin={onLogin}
  onLogout={onLogout}
  onDeleteAccount={onDeleteAccount}
  userInfo={{}}
  firebaseConfig={firebaseConfig}
></LoginComponent>
```

## API

| 参数      | 说明         | 类型                    | 默认值 | 备注 |
| --------- | ------------ | ----------------------- | ------ | ---- |
| primaryColor         | 按钮颜色主色调     | `String` \| `undefined`            | #ffb600    |                          |
| wrapperClassName     | 组件类名           | `String` \| `undefined`         | -          |                          |
| pageLang             | 多语言对象         | `Record<string, string>`       | -          |         具体用到的key见下表                 |
| countryPhoneCodeList | 国家区号列表       | `Array<countryPhoneCodeObj>` | -                        | 支持手机号登录时必传 |
| excludeType          | 移除的登录类型列表 | `Array<'Google','Facebook','Twitter','Snapchat','Apple','Phone'>` | `[]` |     |
| userInfo             | 用户信息           | `UserInfo` | -          |                          |
| firebaseConfig       | firebase 配置      | `firebaseConfigType`    | -          |                          |
| snapchatClientId     | snapchat clientId  | `String`   | -          | 支持 snapchat 登录时必传 |
| onLogin              | 登录 Promise 函数  | `(params: LoginParams) => Promise<any>`| -      |      |
| onLogout | 退出登录Promise函数     |`() => Promise<any>`| -      |      |
| onDeleteAccount | 注销账号Promise函数     |`() => Promise<any>` | - | |

## pageLang 组件内部使用的多语言对象
```js
{
  deleteAccount: 'Delete Account',
  logout: 'Log out',
  loginFirst: 'Please log in first',
  loginTip: 'Tips: You can only log in, but cannot register.',
  phoneNumber: 'Phone number',
  password: 'Password',
  loginIncorrectTip: 'Incorrect account/password, please try again~',
  confirm: 'Confirm',
  cancel: 'Cancel',
  selectCountryCodeTip: 'Please select the country code!',
  phoneNumberCannotBeEmpty: 'Phone number cannot be empty!',
  passwordCannotBeEmpty: 'Password cannot be empty!',
  Google: 'Google',
  Facebook: 'Facebook',
  Twitter: 'Twitter',
  Snapchat: 'Snapchat',
  Apple: 'Apple',
  Phone: 'Phone',
  Email: 'Email',
  loginFail: 'login failed, please try again!',
  notAllowInH5: 'This account does not exist!',
  logoutConfirm: 'Are you sure to logout?',
  deleteAccountConfirm: 'After the account is cancelled.it cannot be restored, please confirm carefully?'
}
```

## countryPhoneCodeObj 手机号登录时国家区号下拉选择项
| 参数      | 说明         | 类型                    | 默认值 | 备注 |
| --------- | ------------ | ----------------------- | ------ | ---- |
| country_img         | 国旗图标     | `String`            |    |                          |
| text         | 国家区号下拉展示文案     | `String`            |     |                          |
| value         | 国家手机号区号     | `String` \| `number`            |    |                          |

## UserInfo 用户信息 
| 参数      | 说明         | 类型                    | 默认值 | 备注 |
| --------- | ------------ | ----------------------- | ------ | ---- |
| headimgurl         | 头像     | `String`            |    |                          |
| name         | 昵称     | `String`            |     |                          |
| uid         | uid     | `String` \| `number`            |    |                          |

## firebaseConfigType firebase配置信息（firebase后台直接复制）
| 参数      | 说明         | 类型                    | 默认值 | 备注 |
| --------- | ------------ | ----------------------- | ------ | ---- |
| apiKey         | -     | `String`            |    |                          |
| authDomain         | -     | `String`            |     |                          |
| projectId         | -     | `String`            |    |                          |
| storageBucket         | -     | `String`          |    |                          |
| messagingSenderId         | -     | `String`          |    |                          |
| appId         | -     | `String`         |    |                          |

## LoginParams 登录Promise参数 
| 参数      | 说明         | 类型                    | 默认值 | 备注 |
| --------- | ------------ | ----------------------- | ------ | ---- |
| type         | 登录类型     | `String`            |    |  `<'Google','Facebook','Twitter','Snapchat','Apple','Phone'>`        |
| openid         | openid    | `String`            |     |                          | 
| area_code         | 手机号登录-国家区号     | `String`           |    |              手机号登录时才有            |
| phone         | 手机号登录-手机号     | `String` \| `number`            |    |     手机号登录时才有                     |
| pass         | 手机号登录-密码   | `String` \| `number`            |    |         手机号登录时才有                 |