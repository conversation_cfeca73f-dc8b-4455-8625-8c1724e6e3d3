import { BZ } from '@/proto';
import { ArrowDown } from '@react-vant/icons';
import React, { useState } from 'react';
import { Dialog, Form, Input, Picker, Space, Toast } from 'react-vant';
import CButton from '../CButton';
import LoginBtn from '../login-btn';
import './index.scss';

interface MobileInputValue {
  prefix: string;
  value: string;
}
type MobileInputProps = {
  value?: MobileInputValue;
  onChange?: (value: MobileInputValue) => void;
  columns: any[];
  primaryColor?: string;
  pageLang: Record<string, string>;
};

const COUNTRY_PHONE_CODE_LIST = [
  { text: 'AF', code: '93', value: '36' },
  { text: 'AR', code: '54', value: '21' },
  { text: 'AM', code: '374', value: '33' },
  { text: 'AU', code: '61', value: '6' },
  { text: 'AT', code: '43', value: '2' },
  { text: 'AZ', code: '994', value: '34' },
  { text: 'BH', code: '973', value: '48' },
  { text: 'BD', code: '880', value: '31' },
  { text: 'BR', code: '5', value: '20' },
  { text: 'CA', code: '1', value: '23' },
  { text: 'CN', code: '86', value: '1' },
  { text: 'CO', code: '57', value: '5' },
  { text: 'EG', code: '20', value: '7' },
  { text: 'ET', code: '251', value: '17' },
  { text: 'FR', code: '33', value: '15' },
  { text: 'GE', code: '995', value: '32' },
  { text: 'DE', code: '49', value: '51' },
  { text: 'GR', code: '30', value: '3' },
  { text: 'IN', code: '91', value: '52' },
  { text: 'ID', code: '62', value: '26' },
  { text: 'IR', code: '98', value: '47' },
  { text: 'IQ', code: '964', value: '46' },
  { text: 'IT', code: '39', value: '14' },
  { text: 'JP', code: '81', value: '27' },
  { text: 'JO', code: '962', value: '45' },
  { text: 'KE', code: '254', value: '4' },
  { text: 'KR', code: '82', value: '28' },
  { text: 'KW', code: '965', value: '44' },
  { text: 'LB', code: '961', value: '43' },
  { text: 'LY', code: '218', value: '8' },
  { text: 'MX', code: '52', value: '22' },
  { text: 'MA', code: '212', value: '11' },
  { text: 'NL', code: '31', value: '9' },
  { text: 'NG', code: '234', value: '18' },
  { text: 'OM', code: '968', value: '42' },
  { text: 'PK', code: '92', value: '30' },
  { text: 'PLE', code: '970', value: '38' },
  { text: 'PH', code: '63', value: '25' },
  { text: 'PT', code: '351', value: '12' },
  { text: 'QA', code: '974', value: '41' },
  { text: 'RU', code: '7', value: '19' },
  { text: 'SA', code: '966', value: '40' },
  { text: 'SG', code: '65', value: '49' },
  { text: 'ES', code: '34', value: '13' },
  { text: 'LK', code: '94', value: '29' },
  { text: 'SY', code: '963', value: '39' },
  { text: 'TN', code: '216', value: '10' },
  { text: 'TR', code: '90', value: '35' },
  { text: 'AE', code: '971', value: '50' },
  { text: 'GB', code: '44', value: '16' },
  { text: 'US', code: '1', value: '24' },
  { text: 'YE', code: '967', value: '37' }
];
// 手机号
const MobileInput: React.FC<MobileInputProps> = ({
  value = { prefix: '', value: '' },
  onChange,
  columns,
  pageLang
}) => {
  const trigger = (changedValue: Partial<MobileInputValue>) => {
    onChange?.({ ...value, ...changedValue });
  };

  const onMobileChange = (value: string) => {
    trigger({ value });
  };

  const onPrefixChange = (prefix: string) => {
    trigger({ prefix: prefix });
  };

  return (
    <>
      <Picker
        popup
        value={value.prefix}
        placeholder={false}
        columns={columns}
        onConfirm={onPrefixChange}
        confirmButtonText={pageLang.confirm}
        cancelButtonText={pageLang.cancel}
        className="country-picker"
        optionRender={
          ((option, index) => {
            const { code, text } = option;
            return (
              <div className="country-item">
                <div className="country-item__left">
                  <div className="country-name">{text}</div>
                </div>
                <div className="country-item__right">{`+${code}`}</div>
              </div>
            );
          }) as any
        }
      >
        {(_, selectRow: any, actions) => {
          return (
            <Space className="mobile-input">
              <Space align="center" className="mobile-input__code" onClick={() => actions.open()}>
                <div className="selected-option">
                  <span className="selected-option-text">{selectRow?.text}</span>
                  <span>+{selectRow?.code}</span>
                </div>
                <ArrowDown style={{ display: 'block' }} />
              </Space>
              <Input value={value.value} type="number" className="phone-number-input" onChange={onMobileChange} />
            </Space>
          );
        }}
      </Picker>
    </>
  );
};
// 短信验证码
const CodeInput: any = ({ value, onChange, onSendCode, primaryColor = '' }) => {
  const [timer, setTimer] = useState<number>(0); // 发送成功后定时器 倒计时
  const [timerVar, setTimerVar] = useState<any>(0);

  const trigger = (changedValue: string) => {
    onChange?.(changedValue);
  };
  /** 发送验证码 */
  function triggerSendCode() {
    onSendCode((res: any) => {
      const { status } = res;
      if (status === 1) {
        // 更新倒计时
        countDown(59);
      } else {
        setTimer(0);
      }
    });
  }
  /** 倒计时函数 */
  function countDown(newTimer) {
    clearTimeout(timerVar);
    if (newTimer > 0) {
      setTimer(newTimer);
      const newTimerVar = setTimeout(() => {
        countDown(newTimer - 1);
      }, 1000);
      setTimerVar(newTimerVar);
    } else {
      setTimer(0);
    }
  }

  return (
    <>
      <Input value={value} type="number" onChange={trigger} className="password-input" />
      {timer === 0 ? (
        <div
          className="send-code-btn"
          onClick={triggerSendCode}
          style={{
            // @ts-expect-error 自定义CSS变量
            '--confirm-btn-color': primaryColor
          }}
        >
          send code
        </div>
      ) : (
        <div
          className="send-code-btn send-code-btn-disabled"
          style={{
            // @ts-expect-error 自定义CSS变量
            '--confirm-btn-color': primaryColor
          }}
        >
          {timer}s
        </div>
      )}
    </>
  );
};

export default function LoginPhone(props) {
  const { subanm, pkg, env, onChange, pageLang, baseUrl, primaryColor, ...rest } = props;
  const [completeBtnLoading, setCompleteBtnLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const defaultCountry = COUNTRY_PHONE_CODE_LIST?.[0];

  const checkMobileInput = (_, value: MobileInputValue) => {
    if (value.prefix && value.value) {
      return Promise.resolve();
    }
    if (!value.prefix) Promise.reject(new Error(pageLang.selectCountryCodeTip));
    return Promise.reject(new Error(pageLang.phoneNumberCannotBeEmpty));
  };

  const onFinish = values => {
    const {
      mobile: { prefix, value },
      code
    } = values;
    const newPrefix = getCodeById(prefix);
    setCompleteBtnLoading(true);
    onChange?.({
      phone: `${newPrefix}${value}`,
      code
    })
      .then(() => {
        setVisible(false);
      })
      .catch(err => {
        console.error(err);
      })
      .finally(() => {
        setCompleteBtnLoading(false);
      });
  };
  /** 获取手机号前缀值 */
  function getCodeById(id) {
    let code = '1';
    for (const columnItem of COUNTRY_PHONE_CODE_LIST) {
      if (id === columnItem.value) {
        code = columnItem.code;
        break;
      }
    }
    return code;
  }
  /** 发送短信验证码 */
  async function onSendCode(cbFn?: any) {
    try {
      const mobile = form.getFieldValue('mobile');
      const { prefix, value } = mobile;
      if (!value) {
        Toast(pageLang.phoneNumberCannotBeEmpty);
        return;
      }
      const newPrefix = getCodeById(prefix);
      const phone = `${newPrefix}${value}`;
      const reqParams = { did: '', pkg };
      try {
        const d = await BZ.account.sendPhoneCode
          .request({ phone_without_code: value, phone }, { addonPubParams: reqParams })
          .catch(error => {
            const msg = error?.msg ?? 'send code fail';
            Toast.fail(msg);
          });
        console.log('result:', d);

        // Toast.success('send code success!');
        if (cbFn) {
          cbFn({ msg: 'success', status: 1 });
        }
      } catch (error) {
        console.error(error);
        Toast.fail('send code fail!');
        if (cbFn) {
          cbFn({ msg: 'fail', status: 2 });
        }
      }
    } catch (error: any) {
      const errorTips = error.errorFields[0].errors[0];
      Toast.fail(errorTips);
      if (cbFn) {
        cbFn({ msg: 'fail', status: 2 });
      }
    }
  }

  const btnClick = () => {
    setVisible(true);
  };

  return (
    <>
      <LoginBtn {...rest} pageLang={pageLang} onClick={btnClick}></LoginBtn>
      <Dialog
        visible={visible}
        closeable={true}
        theme="round-button"
        onClose={() => setVisible(false)}
        showConfirmButton={false}
        className="login-phone-dialog"
      >
        <Form
          layout="vertical"
          className="login-phone__form"
          form={form}
          onFinish={onFinish}
          footer={
            <div>
              <CButton
                nativeType="submit"
                className="phone-confirm-btn"
                style={{ '--confirm-btn-color': primaryColor }}
                loading={completeBtnLoading}
              >
                {pageLang.confirm}
              </CButton>
              <CButton
                className="cancel-btn"
                onClick={event => {
                  event.stopPropagation();
                  event.preventDefault();
                  setVisible(false);
                }}
              >
                {pageLang.cancel}
              </CButton>
            </div>
          }
        >
          <Form.Item
            initialValue={{ prefix: defaultCountry?.value, value: '' }}
            name="mobile"
            label={pageLang.phoneNumber}
            className="login-phone__form__item"
            rules={[{ required: true }, { validator: checkMobileInput }]}
          >
            <MobileInput columns={COUNTRY_PHONE_CODE_LIST} pageLang={pageLang} />
          </Form.Item>
          <Form.Item
            name="code"
            label={pageLang.password}
            className="login-phone__form__item"
            rules={[{ required: true, message: pageLang.passwordCannotBeEmpty }]}
          >
            <CodeInput onSendCode={onSendCode} primaryColor={primaryColor} />
          </Form.Item>
        </Form>
      </Dialog>
    </>
  );
}
