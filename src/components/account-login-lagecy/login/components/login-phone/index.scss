.login-phone-dialog {
  width: 588px;
  --rv-cell-font-size: 28px;
  --rv-field-error-message-font-size: 28px;
  --rv-popup-close-icon-size: 32px;
}
.login-phone__form {
  padding-top: 48px;
  .rv-cell--required::before {
    content: '' !important;
  }
  .rv-cell::after {
    border: none;
  }
  &__item {
    display: block;
    .rv-field__label {
      white-space: nowrap;
    }
    .phone-number-input input,
    .password-input input {
      background-color: transparent !important;
      font-size: 28px;
    }
  }
}
.country-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0 24px;
  &__left {
    display: flex;
    align-items: center;
    font-size: 28px;
    padding-left: 10px;
    img {
      width: 60px;
      margin-inline-end: 24px;
    }
  }
  &__right {
    font-size: 28px;
    color: #666;
  }
}
.selected-option {
  display: flex;
  align-items: center;
  font-size: 28px;
  color: var(--rv-input-text-color);
  img {
    width: 60px;
    margin-inline-end: 12px;
  }
  .selected-option-text {
    margin-right: 8px;
  }
}
.mobile-input {
  width: 524px;
  height: 92px;
  background: #f5f4f4;
  border-radius: 16px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  &__code {
    height: 92px;
  }
}
.password-input {
  width: 366px;
  height: 92px;
  background: #f5f4f4;
  border-radius: 16px;
  padding: 0 24px;
}
.send-code-btn {
  margin-left: 10px;
  background: var(--confirm-btn-color, linear-gradient(270deg, #ff7c19 0%, #ff0b4d 100%));
  border-radius: 12px;
  width: 140px;
  height: 50px;
  font-size: 24px;
  line-height: 50px;
  text-align: center;
  color: #fff;
  cursor: pointer;
}
.send-code-btn-disabled {
  background: rgba(0, 0, 0, 0.05);
  color: #908b9f;
  cursor: default;
}
.phone-confirm-btn {
  width: 508px;
  height: 80px;
  background: var(--confirm-btn-color, linear-gradient(270deg, #ff7c19 0%, #ff0b4d 100%));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
  color: #ffffff;
  cursor: pointer;
  border: none;
  margin: 24px auto 0;
}
.phone-confirm-btn-disabled {
  cursor: disabled;
  background: linear-gradient(270deg, rgba(255, 124, 25, 0.3) 0%, rgba(255, 11, 77, 0.3) 100%);
}

.cancel-btn {
  width: 200px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  font-weight: 500;
  color: #b2b2b2;
  cursor: pointer;
  border: none;
  margin: 12px auto 0;
}

.country-picker {
  .rv-picker__confirm {
    color: var(--confirm-action-color);
  }
}
