import React from 'react';
import CButton from '../CButton';
import './index.scss';
function LoginBtn(props) {
  const { type, icon, onClick, pageLang, children, firebaseConfig, ...rest } = props;
  return (
    <CButton className="login-item" key={type} onClick={onClick} firebaseconfig={firebaseConfig} {...rest}>
      {children ? children : null}
      {icon ? <img className="login-icon" src={icon} /> : null}
      <div className="login-title">{pageLang[type]}</div>
    </CButton>
  );
}

export default LoginBtn;
