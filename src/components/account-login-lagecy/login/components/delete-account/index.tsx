import React, { useCallback, useEffect, useState, useRef } from 'react';
import CButton from '../CButton';
import { Dialog } from 'react-vant';
import './index.scss';

function useCountdown() {
  const SEC = 10;
  const [countdown, setCountdown] = useState(SEC);
  const timer = useRef(null as any);
  const countdownStart = useCallback(() => {
    timer.current = window.setInterval(() => {
      setCountdown(c => c - 1);
    }, 1000);
  }, []);
  const handlePause = useCallback(() => {
    window.clearInterval(timer.current);
    timer.current = null;
  }, []);
  const resetCountDown = useCallback(() => {
    setCountdown(SEC);
    window.clearInterval(timer.current);
    timer.current = null;
  }, []);
  useEffect(() => {
    if (countdown <= 0) {
      handlePause();
    }
  }, [countdown]);
  return {
    countdown,
    resetCountDown,
    countdownStart
  } as any;
}
function DeleteAccount(props) {
  const { userInfo, pageLang, primaryColor, onDeleteAccount } = props;
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const { countdown, countdownStart, resetCountDown } = useCountdown();
  useEffect(() => {
    if (visible) {
      countdownStart();
    } else {
      resetCountDown();
    }
  }, [visible]);
  const deleteAccount = () => {
    setConfirmLoading(true);
    onDeleteAccount()
      .then(() => {
        setVisible(false);
      })
      .catch(err => {
        console.error(err);
      })
      .finally(() => {
        setConfirmLoading(false);
      });
  };
  return (
    <>
      <CButton className="delete-btn" onClick={() => setVisible(true)}>
        {pageLang.deleteAccount}
      </CButton>
      <Dialog
        closeable={true}
        theme="round-button"
        title="Delete Account"
        visible={visible}
        className="delete-account-dialog"
        confirmButtonColor={primaryColor}
        confirmButtonText={pageLang.confirm}
        footer={
          <div className="confirm-btn-wrap">
            <CButton className="confirm-btn" disabled={countdown > 0} loading={confirmLoading} onClick={deleteAccount}>
              {`${pageLang.deleteAccount} ${countdown > 0 ? `(${countdown})` : ''}`}
            </CButton>
          </div>
        }
        onClose={() => setVisible(false)}
      >
        <div className="content">
          <img className="user-avator" src={userInfo.headimgurl} />
          <div className="user-name">{userInfo.name}</div>
          <div className="tip">{pageLang.deleteAccountConfirm}</div>
        </div>
      </Dialog>
    </>
  );
}

export default DeleteAccount;
