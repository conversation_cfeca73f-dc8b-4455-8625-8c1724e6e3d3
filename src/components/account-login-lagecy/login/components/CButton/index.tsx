import './index.scss';
import classNames from 'classnames';
import { Loading } from 'react-vant';
const CButton = ({ className, disabled, loading, children, onClick, nativeType: type, ...rest }: any) => {
  return (
    <button
      className={classNames('custom-btn', loading ? 'custom-btn-loading' : null, className)}
      disabled={disabled || loading}
      onClick={onClick}
      type={type}
      {...rest}
    >
      {loading ? <Loading className={'custom-btn__loading'} /> : null}
      {children}
    </button>
  );
};
export default CButton;
