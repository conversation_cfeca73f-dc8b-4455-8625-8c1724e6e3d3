import React, { useState } from 'react';
import { Dialog, Input, Form } from 'react-vant';
import CButton from '../CButton';
import LoginBtn from '../login-btn';
import './index.scss';

/** 账号/密码登录 */
export default function LoginAccount(props) {
  const { subanm, pkg, env, onChange, pageLang, baseUrl, ...rest } = props;
  const [visible, setVisible] = useState(false);
  const [completeBtnLoading, setCompleteBtnLoading] = useState(false);
  const [form] = Form.useForm();

  const btnClick = () => {
    setVisible(true);
  };
  const onFinish = values => {
    const { account, password } = values;
    setCompleteBtnLoading(true);
    onChange?.({ type: rest.type, account, password })
      .then((res: any) => {
        setVisible(false);
      })
      .catch(err => {
        console.error(err);
      })
      .finally((res: any) => {
        setCompleteBtnLoading(false);
      });
  };

  return (
    <>
      <LoginBtn {...rest} pageLang={pageLang} onClick={btnClick}></LoginBtn>
      <Dialog
        visible={visible}
        closeable={true}
        theme="round-button"
        onClose={() => setVisible(false)}
        showConfirmButton={false}
        className="login-account-dialog"
      >
        <Form
          layout="vertical"
          className="login-account__form"
          form={form}
          onFinish={onFinish}
          footer={
            <div>
              <CButton nativeType="submit" className="account-confirm-btn" loading={completeBtnLoading}>
                {pageLang.confirm}
              </CButton>
              <CButton
                className="cancel-btn"
                onClick={event => {
                  event.stopPropagation();
                  event.preventDefault();
                  setVisible(false);
                }}
              >
                {pageLang.cancel}
              </CButton>
            </div>
          }
        >
          <Form.Item
            name="account"
            label={pageLang.account}
            className="login-account__form__item"
            rules={[{ required: true, message: pageLang.passwordCannotBeEmpty }]}
          >
            <Input className="account-input" />
          </Form.Item>
          <Form.Item
            name="password"
            label={pageLang.userPassword}
            className="login-account__form__item"
            rules={[{ required: true, message: pageLang.passwordCannotBeEmpty }]}
          >
            <Input className="password-input" />
          </Form.Item>
        </Form>
      </Dialog>
    </>
  );
}
