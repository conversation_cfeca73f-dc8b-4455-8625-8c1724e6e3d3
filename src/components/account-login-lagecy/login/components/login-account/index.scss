.login-account-dialog {
  width: 588px;
  --rv-cell-font-size: 28px;
  --rv-field-error-message-font-size: 28px;
  --rv-popup-close-icon-size: 32px;
}
.login-account__form {
  padding-top: 48px;
  .rv-cell--required::before {
    content: '' !important;
  }
  .rv-cell::after {
    border: none;
  }
  &__item {
    display: block;
    .rv-field__label {
      white-space: nowrap;
    }
    .account-input input,
    .password-input input {
      background-color: transparent !important;
      font-size: 28px;
    }
  }
  .account-input,
  .password-input {
    width: 366px;
    height: 92px;
    background: #f5f4f4;
    border-radius: 16px;
    padding: 0 24px;
  }
  .account-confirm-btn {
    width: 200px;
    height: 80px;
    background: linear-gradient(270deg, #ff7c19 0%, #ff0b4d 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 500;
    color: #ffffff;
    cursor: pointer;
    border: none;
    margin: 24px auto 0;
  }
  .account-confirm-btn-disabled {
    cursor: disabled;
    background: linear-gradient(270deg, rgba(255, 124, 25, 0.3) 0%, rgba(255, 11, 77, 0.3) 100%);
  }
  .cancel-btn {
    width: 200px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: 500;
    color: #b2b2b2;
    cursor: pointer;
    border: none;
    margin: 12px auto 0;
  }
}