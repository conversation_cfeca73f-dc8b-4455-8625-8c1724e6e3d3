import React from 'react';
import { GoogleAuthProvider, FacebookAuthProvider, TwitterAuthProvider, OAuthProvider } from 'firebase/auth';
import { Toast } from 'react-vant';
import signInAccount from '../../lib/firebase';
import { LoginType } from '../../lib/constant';
import LoginBtn from '../login-btn';

const getProvider = type => {
  switch (type) {
    case LoginType.Google:
      return new GoogleAuthProvider();
    case LoginType.Facebook:
      return new FacebookAuthProvider();
    case LoginType.Twitter:
      return new TwitterAuthProvider();
    case LoginType.Apple:
      return new OAuthProvider('apple.com');
  }
};

export default function LoginFirebase(props) {
  const { pageLang, type, onChange, firebaseConfig } = props;
  const provider = getProvider(type);
  const btnClick = () => {
    // console.log("firebaseConfig: ", firebaseConfig);
    signInAccount(provider, firebaseConfig)
      .then(res => {
        // console.log("res: ", res);
        const { _tokenResponse } = res;
        if (_tokenResponse) {
          const data =
            typeof _tokenResponse.rawUserInfo === 'string'
              ? JSON.parse(_tokenResponse.rawUserInfo || '{}')
              : _tokenResponse.rawUserInfo;
          const obj: any = { type, openid: type === LoginType.Apple ? data?.sub : data?.id };
          onChange?.(obj);
        }
      })
      .catch(err => {
        console.error(err);
        Toast({
          message: pageLang.loginFail
        });
      });
  };
  return <LoginBtn onClick={btnClick} {...props}></LoginBtn>;
}
