import IconBack from '@/assets/icons/icon-back.png';
import classnames from 'classnames';
import { useEffect, useState } from 'react';
import { Button, Dialog, Loading } from 'react-vant';
import DeleteAccount from './components/delete-account';
import LoginAccount from './components/login-account';
import LoginFirebase from './components/login-firebase';
import LoginPhone from './components/login-phone/index-pb';
import './index.scss';
import { IProps, LoginType, LoginTypeList } from './lib/constant';
import { formatTypeList } from './lib/utils';

const LoadingEle = ({ color }) => {
  return (
    <div className="loading-wrapper">
      <Loading color={color} />
    </div>
  );
};

function LoginComponent(props: IProps) {
  const {
    pageLang,
    primaryColor = '#ffb600',
    wrapperClassName = '',
    onLogin,
    onLogout,
    onDeleteAccount,
    excludeType,
    userInfo,
    firebaseConfig,
    env = 'prod',
    pkg = '',
    subanm = '',
    baseUrl,
    otherType
  } = props;
  const actuallyTypeList = formatTypeList(LoginTypeList, excludeType, otherType);
  const [isLoading, setIsLoading] = useState(false);
  const [logined, setLogined] = useState(false);

  useEffect(() => {
    if (!userInfo || !userInfo?.uid) {
      setLogined(false);
    } else {
      setLogined(true);
    }
  }, [userInfo]);

  const login = async params => {
    return new Promise((resolve, reject) => {
      onLogin(params)
        .then(res => {
          resolve(res);
        })
        .catch(err => {
          console.error(err);
          reject(err);
        })
        .finally(() => {
          setIsLoading(false);
        });
    });
  };
  const logout = () => {
    Dialog.alert({
      closeable: true,
      theme: 'round-button',
      message: <div className="logout-content">{pageLang.logoutConfirm}</div>,
      confirmButtonColor: primaryColor,
      confirmButtonText: pageLang.confirm,
      onConfirm: async () => {
        await onLogout();
        setLogined(false);
      }
    });
  };
  const deleteAccount = async () => {
    await onDeleteAccount();
    setLogined(false);
  };
  if (!userInfo) {
    return <LoadingEle color={primaryColor} />;
  }
  return (
    <div className={classnames('login-page', wrapperClassName)}>
      {isLoading ? <LoadingEle color={primaryColor} /> : null}
      {logined ? (
        <div className="content">
          {userInfo?.headimgurl ? <img className="user-avator" src={userInfo.headimgurl} /> : null}
          <div className="user-name">Nickname: {userInfo?.name ?? '-'}</div>
          <div className="user-id-field">ID: {userInfo?.uid ?? '-'}</div>
          <DeleteAccount pageLang={pageLang} userInfo={userInfo} onDeleteAccount={deleteAccount} />
          <Button className="logout-btn" onClick={logout}>
            {pageLang.logout}
          </Button>
        </div>
      ) : (
        <div className="content">
          <div className="user-id">{pageLang.loginFirst}</div>
          <div className="login-tip">{pageLang.loginTip}</div>
          <div className="login-list">
            {actuallyTypeList.map(i => {
              const { type } = i;
              if (type === LoginType.Phone) {
                return (
                  <LoginPhone
                    key={type}
                    env={env}
                    pkg={pkg}
                    subanm={subanm}
                    pageLang={pageLang}
                    primaryColor={primaryColor}
                    onChange={p => login({ ...p, type })}
                    baseUrl={baseUrl}
                    {...i}
                  />
                );
              } else if (type === LoginType.Account) {
                return (
                  <LoginAccount
                    key={type}
                    pageLang={pageLang}
                    onChange={login}
                    firebaseConfig={firebaseConfig}
                    {...i}
                  />
                );
              } else {
                return (
                  <LoginFirebase
                    key={type}
                    pageLang={pageLang}
                    onChange={login}
                    firebaseConfig={firebaseConfig}
                    {...i}
                  />
                );
              }
            })}
          </div>
        </div>
      )}
      <div
        className={`vww-10 vwh-10 lg:pxw-50 lg:pxh-50 absolute left-[3vw] top-[3vw]`}
        style={{ background: `url(${IconBack}) no-repeat center/contain` }}
        onClick={() => history.back()}
      />
    </div>
  );
}

export default LoginComponent;
