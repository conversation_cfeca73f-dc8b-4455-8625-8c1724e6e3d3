import { useState, useEffect } from 'react';
import { AppleConfigType, FirebaseConfigType, IProps } from './login/lib/constant';
import LoginComponent from './login';
import { Toast } from 'react-vant';
import { requestProxy } from '@/modules/request';
import { Helmet } from 'react-helmet';
import { getLangText } from './login/lib/utils';

interface LoginProp {
  firebaseConfig?: FirebaseConfigType;
  appleConfig?: AppleConfigType;
  appName: string;
  excludeType: IProps['excludeType'];
  baseUrl: string;
  pkg: string;
  anm: string;
  subanm: string;
  env: string;
  gradient: string;
}

export function AccountLogin({ subanm, env, pkg, baseUrl, excludeType, appleConfig, ...props }: LoginProp) {
  const UserInfoKey = `${subanm}_user_info`;
  const PAGE_LANG = getLangText(props.appName);
  const [userInfo, setUserInfo] = useState<any>({}); // 登录成功后 用户信息
  const baseParam = { s_time: '********', subanm, anm: 'cu', pkg };

  useEffect(() => {
    // 从缓存获取用户信息
    const userInfoStr = localStorage.getItem(UserInfoKey);
    if (userInfoStr) {
      try {
        setUserInfo(JSON.parse(userInfoStr));
      } catch (error) {
        console.log(error);
      }
    }
  }, []);

  const validPhoneCode = async (phone: string, code: string) => {
    try {
      const res = await requestProxy(
        `${baseUrl}/account/verify_code`,
        {
          phone,
          code,
          ...baseParam
        },
        {
          'X-proxy': true,
          headers: {
            'X-SIGN': true,
            'X-NOT-ENCODE': true
          }
        }
      );
      return res && res.status === 1;
    } catch (error: any) {
      const msg = error?.msg ?? 'login failed';
      Toast.fail(msg);
      return false;
    }
  };

  /** 调用业务服务端接口登录 */
  async function onLogin(params: any) {
    const { type, openid, phone, code } = params;
    const reqParams: any = {
      check_account: 1,
      third_type: '',
      ...baseParam
    };
    // 手机号登录得 校验验证码
    let phoneValid = true;
    switch (type) {
      case 'Google':
        reqParams.third_type = 'gp';
        reqParams.openid = openid;
        break;
      case 'Phone':
        reqParams.third_type = '';
        reqParams.phone = phone;
        reqParams.code = code;
        phoneValid = await validPhoneCode(phone, code);
        break;
      default:
        break;
    }
    if (!phoneValid) {
      Toast.fail(PAGE_LANG.CodeIncorrect);
      throw new Error(PAGE_LANG.CodeIncorrect); // 抛出异常
    }
    const res = await requestProxy(
      `${baseUrl}/account/login`,
      { ...reqParams, ...baseParam },
      {
        'X-proxy': true,
        headers: {
          'X-SIGN': true,
          'X-NOT-ENCODE': true
        }
      }
    ).catch(error => {
      const msg = error?.msg ?? 'login failed';
      Toast.fail(msg);
    });
    if (res) {
      if (res.status === 1) {
        const { info, token, msg } = res.data;
        if (info) {
          const baseStr = info.base;
          let newUserInfo: any = {};
          try {
            const baseObj = JSON.parse(baseStr);
            newUserInfo = {
              token,
              headimgurl: baseObj.avatar,
              name: baseObj.nickname,
              uid: baseObj.uid,
              did: baseObj.did
            };
          } catch (error) {
            console.log(error);
          }
          setUserInfo(newUserInfo);
          // 用户信息存入缓存
          localStorage.setItem(UserInfoKey, JSON.stringify(newUserInfo));
        } else {
          Toast.fail(msg);
        }
      } else {
        Toast.fail('login fail!');
      }
    }
  }

  /** 注销用户界面操作 */
  async function onDeleteAccount() {
    const { uid, did, token } = userInfo;
    const reqParams: any = {
      uid,
      did,
      ...baseParam
    };
    const res = await requestProxy(`${baseUrl}/account/close`, reqParams, {
      'X-proxy': true,
      headers: {
        'X-SIGN': true,
        'X-NOT-ENCODE': true,
        'X-TOKEN': token
      }
    }).catch(error => {
      // console.log("error: ", error);
      const msg = error.msg ?? 'Delete account fail!';
      Toast.fail(msg);
      if (error.status === 10003) {
        localStorage.removeItem(UserInfoKey);
      } else {
        throw new Error(msg); // 抛出异常
      }
    });
    if (res && res.status === 1) {
      localStorage.removeItem(UserInfoKey);
      Toast.info({
        message: 'Delete account successfully!',
        className: 'wide-toast'
      });
    }
  }
  /** 退出登录 */
  async function onLogout() {
    localStorage.removeItem(UserInfoKey);
    Toast.info({
      message: 'Logout successfully!',
      className: 'wide-toast'
    });
  }

  return (
    <>
      {!excludeType?.includes('Apple') && (
        <Helmet>
          <script defer src="https://appleid.cdn-apple.com/appleauth/static/jsapi/appleid/1/en_US/appleid.auth.js" />
        </Helmet>
      )}
      <LoginComponent
        primaryColor={props.gradient}
        wrapperClassName="login-wrapper"
        pageLang={PAGE_LANG}
        excludeType={excludeType}
        onLogin={onLogin}
        onLogout={onLogout}
        onDeleteAccount={onDeleteAccount}
        userInfo={userInfo}
        subanm={subanm}
        env={env}
        pkg={pkg}
        appleConfigType={appleConfig}
        firebaseConfig={props.firebaseConfig}
        baseUrl={baseUrl}
      />
    </>
  );
}
