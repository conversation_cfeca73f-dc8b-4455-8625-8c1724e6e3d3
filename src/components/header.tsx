import BackIcon from '@/assets/icons/icon-back-black.png';
import { closeView, setNavBar } from '@/modules/bridge';
import { ReactNode, useEffect } from 'react';

export const Close = () => closeView(1);

type HeaderProps = {
  title: string;
  close?: boolean;
  onBack?: () => void;
  onExtraClick?: () => void;
  extra?: ReactNode;
  wrapClassName?: string;
  titleClassName?: string;
  backIconClassName?: string;
};

export function Header({
  title,
  extra,
  close,
  onBack,
  onExtraClick,
  wrapClassName,
  titleClassName,
  backIconClassName
}: HeaderProps) {
  const handleBack = () => {
    if (close) {
      Close();
    } else if (onBack) {
      onBack();
    } else {
      window.history.back();
    }
  };
  useEffect(() => {
    setNavBar({ visible: 0 });
  }, []);

  return (
    <div className="pt-84px">
      <div className={`py-20px relative ${wrapClassName}`}>
        <div className="gap-16px flex-between">
          <div className="flex-shrink-0" onClick={handleBack}>
            <img src={BackIcon} alt="Back" className={`h-48px w-48px ${backIconClassName}`} />
          </div>
          <h1
            className={`text-44px overflow-hidden text-ellipsis whitespace-nowrap text-center font-bold ${titleClassName}`}
          >
            {title}
          </h1>
          <div className="min-w-48px flex-shrink-0" onClick={onExtraClick}>
            {extra}
          </div>
        </div>
      </div>
    </div>
  );
}
