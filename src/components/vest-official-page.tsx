import imgApple from '@/assets/app_store_apple.png';
import imgGp from '@/assets/app_store_gp.png';
import { PageProps } from '@/configs/types';
import { formatAndroidVestPageConfig, formatIOSVestPageConfig } from '@/modules/utils';
import { Helmet } from 'react-helmet';
import { Link } from 'react-router-dom';

/** KolKol 风格的页面 */
export function PageTemplate1({
  GradientColors,
  Name,
  Slogan,
  Features,
  Email,
  pkg,
  images,
  appstoreLink,
  enableDelAcc,
  enableStoreEntry
}: PageProps) {
  appstoreLink = appstoreLink || `https://play.google.com/store/apps/details?id=${pkg}`;
  return (
    <>
      <Helmet>
        <title>{Name}</title>
        <style>{`@media (min-width: 768px) {html { font-size: 50px; } body {background: ${GradientColors[1]} linear-gradient(180deg, ${GradientColors[0]} 60%, ${GradientColors[1]} 100%) no-repeat;}}`}</style>
      </Helmet>
      <div
        id="page-wrapper"
        className={`h-full min-h-screen w-full bg-gradient-to-b from-[--start] from-60% to-[--end] lg:min-h-fit`}
        style={{
          // @ts-expect-error 使用自定义变量
          '--start': GradientColors[0],
          '--end': GradientColors[1]
        }}
      >
        <div id="header" className="fixed left-0 top-0 w-full bg-black/50">
          <div className="pxmax-w-750 pxgap-18 pxpx-26 pxpy-22 mx-auto flex items-center justify-between">
            <img className="pxh-104 pxw-104 shrink-0 select-none" src={images.imgLogo} alt="logo" />
            <div className="flex-1">
              <h1 className="pxtext-46 pxleading-56 font-bold text-white">{Name}</h1>
              <p className="pxtext-28 pxleading-30 text-white">{Slogan}</p>
            </div>
            {enableStoreEntry && (
              <a href={appstoreLink}>
                <button
                  className={`flex-center pxh-82 pxw-200 pxtext-32 pxleading-38 shrink-0 select-none rounded-full bg-gradient-to-r from-[--start] to-[--end] text-white`}
                >
                  Download
                </button>
              </a>
            )}
          </div>
        </div>
        <div
          id="page-container"
          className={`flex flex-col items-center bg-contain bg-top bg-no-repeat pt-[150px]`}
          style={{
            backgroundImage: `url(${images.imgBG})`
          }}
        >
          <img className="w-full" src={images.imgHero} alt="hero" />
          <div id="features">
            {Features.map(item => (
              <FeatureItem1 key={item.title} {...item} />
            ))}
          </div>
          <footer className="pxmb-75 pxmt-80 w-full text-white">
            <div
              id="link-wrapper"
              className="pxgap-10 pxtext-22 pxleading-27 flex items-center justify-center text-center"
            >
              <a className="underline" href="/terms">
                Terms of Service
              </a>
              <span className="link-hr">|</span>
              <a className="underline" href="/privacy">
                Privacy Policy
              </a>
              {enableDelAcc && (
                <>
                  <span className="link-hr">|</span>
                  <Link className="underline" to="/account-login">
                    Delete Account
                  </Link>
                </>
              )}
            </div>
            <div id="copyright-wrap" className="pxmt-28 pxtext-18 pxgap-18 mx-auto flex items-center justify-center">
              <div id="contact-us">
                Contact us: <a href={`mailto:${Email}`}>{Email}</a>
              </div>
              <div id="copyright">Copyright © 2024 {Name} </div>
            </div>
          </footer>
        </div>
      </div>
    </>
  );
}

function FeatureItem1({ icon, title, desc }: { icon: string; title: string; desc: string }) {
  return (
    <div className="pxmt-30 pxpx-24 pxpy-20 pxh-140 pxw-642 pxgap-42 rounded-20 flex items-center bg-white/15">
      <img className="pxw-82 shrink-0" src={icon} alt="icon" />
      <div className="flex-1">
        <h3 className="pxtext-30 pxleading-37 text-white">{title}</h3>
        <p className="pxtext-22 pxleading-27 font-normal text-white/70">{desc}</p>
      </div>
    </div>
  );
}

/** Suya 风格的页面 */
export function PageTemplate2({
  Name,
  Slogan,
  Features,
  Email,
  pkg,
  images,
  appstoreLink,
  enableDelAcc,
  enableStoreEntry
}: PageProps) {
  appstoreLink = appstoreLink || `https://play.google.com/store/apps/details?id=${pkg}`;

  return (
    <>
      <Helmet>
        <title>{Name}</title>
        <style>{`@media (min-width: 768px) {html { font-size: 50px; background-color: #271304; }}}`}</style>
      </Helmet>
      <div
        id="page-wrapper"
        className={`h-full min-h-screen w-full lg:min-h-fit`}
        style={{
          background: `#271304 url(${images.imgBG}) no-repeat top/contain`
        }}
      >
        <div className="header pxpt-100 flex-base flex-col">
          <div
            className="m-logo pxw-200 pxh-200 bg-contain bg-center bg-no-repeat"
            style={{ backgroundImage: `url(${images.imgLogo})` }}
          />
          <h1 className="pxtext-72 pxpt-60 font-black italic leading-snug text-white">{Name}</h1>
          <p className="pxtext-52 font-semibold italic leading-none text-white">{Slogan}</p>
        </div>
        <div className={`pxpt-60 flex flex-col items-center`}>
          {enableStoreEntry && (
            <a href={appstoreLink}>
              <img className="pxw-302 pxh-102" src={appstoreLink.includes('apple') ? imgApple : imgGp} alt="hero" />
            </a>
          )}
          <div
            id="feature-title"
            className="pxh-74 pxw-470 pxtext-40 flex-center pxmt-90 pxmb-70 bg-contain bg-center bg-no-repeat font-bold leading-snug text-white"
            style={{ backgroundImage: `url(${images.imgFeatures})` }}
          >
            Highlights Features
          </div>

          <div id="features">
            {Features.map(item => (
              <FeatureItem2 key={item.title} {...item} />
            ))}
          </div>
          <footer className="pxmb-75 pxmt-80 w-full text-white">
            <div
              id="link-wrapper"
              className="pxgap-10 pxtext-24 pxleading-27 flex items-center justify-center text-center [&>a]:underline"
            >
              <a href="/terms">Terms of Service</a>
              <span className="link-hr">|</span>
              <a href="/privacy">Privacy Policy</a>
              {enableDelAcc && (
                <>
                  <span className="link-hr">|</span>
                  <Link to="/account-login">Delete Account</Link>
                </>
              )}
            </div>
            <div id="copyright-wrap" className="pxmt-28 pxtext-24 pxgap-18 mx-auto text-center">
              <div id="contact-us">
                Contact us: <a href={`mailto:${Email}`}>{Email}</a>
              </div>
              <div id="copyright">Copyright © 2025 {Name} </div>
            </div>
          </footer>
        </div>
      </div>
    </>
  );
}

function FeatureItem2({ icon, title, desc }: { icon: string; title: string; desc: string }) {
  return (
    <div className="pxw-670 pxh-130 pxpx-24 pxgap-24 vwmax-w-96 pxmb-30 flex items-center rounded-[26px] border-2 border-[rgba(221,198,255,.41)] bg-white/10">
      <div className="pxw-100 pxh-100 bg-contain bg-center bg-no-repeat" style={{ backgroundImage: `url(${icon})` }} />
      <div className="flex-1">
        <h3 className="pxtext-28 font-bold leading-snug text-white">{title}</h3>
        <p className="pxtext-22 font-normal leading-snug text-white/70">{desc}</p>
      </div>
    </div>
  );
}

export function PageTemplateWithInjectState_Kolkol() {
  const vestPageConfig = formatAndroidVestPageConfig();
  return <PageTemplate1 {...vestPageConfig} />;
}

export function PageTemplateWithInjectState_Suya() {
  const vestPageConfig = formatAndroidVestPageConfig();
  return <PageTemplate2 {...vestPageConfig} />;
}

export function PageTemplateWithInjectState_IOS_Suya() {
  const vestPageConfig = formatIOSVestPageConfig();
  return <PageTemplate2 {...vestPageConfig} />;
}
