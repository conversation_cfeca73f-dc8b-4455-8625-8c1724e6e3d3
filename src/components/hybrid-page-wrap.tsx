import { ReactNode } from 'react';
import { Helmet } from 'react-helmet';
import { ConfigProvider, ConfigProviderProps } from 'react-vant';
import { enUS } from 'react-vant/es/locale';
import JsBridgeRegister from './js-bridge-register';

export default function PageWrapper({
  children,
  title,
  ...prop
}: { children: ReactNode; title: string } & ConfigProviderProps) {
  return (
    <ConfigProvider locale={enUS} {...prop}>
      <JsBridgeRegister />
      <Helmet>
        <title>{title}</title>
      </Helmet>
      {children}
    </ConfigProvider>
  );
}
