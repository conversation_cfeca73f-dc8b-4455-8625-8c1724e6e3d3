import { Helmet } from 'react-helmet';

/**
 * 这是 SDK 通用的 客户端为什么未注入全局变量 window.JSBridge
 * 备用，只需要在页面引入该组件即可
 */
export default function JsBridgeRegister() {
  return (
    <Helmet>
      <script>
        {`
        function setupJavascriptBridge(callback) {
          if (window.JSBridge) { return callback(JSBridge); }
          if (window.JSBridgeCallbacks) { return window.JSBridgeCallbacks.push(callback); }
          window.JSBridgeCallbacks = [callback];
          window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.register && window.webkit.messageHandlers.register.postMessage && window.webkit.messageHandlers.register.postMessage(null);
          window.AndroidJSBridge && window.AndroidJSBridge.register && window.AndroidJSBridge.register();
        };
        setupJavascriptBridge(function (bridge) {});
      `}
      </script>
    </Helmet>
  );
}
