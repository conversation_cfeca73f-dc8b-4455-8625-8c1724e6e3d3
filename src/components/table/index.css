.table {
  width: 100%;
}

.table-header,
.table-row {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.table-header {
  background-color: #fbb562;
  border-radius: 40px 40px 0 0;
  color: #fff;
  font-size: 32px;
  font-weight: bold;
  overflow: hidden;
}
.table-row-wrap {
  background-color: #f8f8fc;
}
.table-row {
  width: 100%;
  margin: 0 auto;
  color: #a54c2d;
}

.table-item {
  display: inline-block;
  text-align: center;
  white-space: pre-wrap;
}
.table-item:not(:last-child) {
  border-right: 1px solid;
  align-self: stretch;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-header .table-item:not(:last-child) {
  /* border-color: #e9a065; */
  padding: 12px;
}

.table-row .table-item:not(:last-child) {
  border-color: #fff;
}

.table-row-wrap:not(:last-child) {
  border-bottom: 1px solid #fff;
}
.table-row-wrap:last-child {
  border-radius: 0 0 26px 26px;
}

.table-row > .table-item {
  padding: 12px 0;
}
