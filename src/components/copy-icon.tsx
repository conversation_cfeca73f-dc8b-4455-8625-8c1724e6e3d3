import { Copy } from '@icon-park/react';
import classNames from 'classnames';
import _copy from 'copy-to-clipboard';
import { Toast } from 'react-vant';

type Prop = Parameters<typeof Copy>[0] & {
  content: string;
};
export function CopyIcon(props: Prop) {
  return (
    <Copy
      theme="outline"
      size="12"
      {...props}
      className={classNames('inline-block', props.className)}
      onClick={() => copy(props.content)}
    />
  );
}

export const copy = (text: string) => {
  if (_copy(text)) {
    Toast.success('Copied!');
  } else {
    Toast.fail('Copy failed!');
  }
};
