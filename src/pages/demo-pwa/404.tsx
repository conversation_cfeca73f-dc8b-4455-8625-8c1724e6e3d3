const NotFound = () => {
  return (
    <>
      <style
        dangerouslySetInnerHTML={{
          __html: `#svg{fill:#0057b8}#svg path:first-child{-webkit-animation:5s linear 40ms infinite pweek;animation:5s linear 40ms infinite pweek}#svg path:first-child:hover,#svg path:nth-child(10):hover,#svg path:nth-child(11):hover,#svg path:nth-child(12):hover,#svg path:nth-child(13):hover,#svg path:nth-child(14):hover,#svg path:nth-child(15):hover,#svg path:nth-child(16):hover,#svg path:nth-child(17):hover,#svg path:nth-child(18):hover,#svg path:nth-child(19):hover,#svg path:nth-child(2):hover,#svg path:nth-child(20):hover,#svg path:nth-child(21):hover,#svg path:nth-child(22):hover,#svg path:nth-child(23):hover,#svg path:nth-child(24):hover,#svg path:nth-child(25):hover,#svg path:nth-child(26):hover,#svg path:nth-child(27):hover,#svg path:nth-child(28):hover,#svg path:nth-child(29):hover,#svg path:nth-child(3):hover,#svg path:nth-child(30):hover,#svg path:nth-child(31):hover,#svg path:nth-child(32):hover,#svg path:nth-child(33):hover,#svg path:nth-child(34):hover,#svg path:nth-child(35):hover,#svg path:nth-child(36):hover,#svg path:nth-child(37):hover,#svg path:nth-child(38):hover,#svg path:nth-child(39):hover,#svg path:nth-child(4):hover,#svg path:nth-child(40):hover,#svg path:nth-child(41):hover,#svg path:nth-child(42):hover,#svg path:nth-child(43):hover,#svg path:nth-child(44):hover,#svg path:nth-child(45):hover,#svg path:nth-child(46):hover,#svg path:nth-child(47):hover,#svg path:nth-child(48):hover,#svg path:nth-child(49):hover,#svg path:nth-child(5):hover,#svg path:nth-child(50):hover,#svg path:nth-child(51):hover,#svg path:nth-child(52):hover,#svg path:nth-child(53):hover,#svg path:nth-child(54):hover,#svg path:nth-child(55):hover,#svg path:nth-child(56):hover,#svg path:nth-child(57):hover,#svg path:nth-child(58):hover,#svg path:nth-child(6):hover,#svg path:nth-child(7):hover,#svg path:nth-child(8):hover,#svg path:nth-child(9):hover{-webkit-animation-play-state:paused;animation-play-state:paused}#svg path:nth-child(2){-webkit-animation:5s linear 80ms infinite pweek;animation:5s linear 80ms infinite pweek}#svg path:nth-child(3){-webkit-animation:5s linear .12s infinite pweek;animation:5s linear .12s infinite pweek}#svg path:nth-child(4){-webkit-animation:5s linear .16s infinite pweek;animation:5s linear .16s infinite pweek}#svg path:nth-child(5){-webkit-animation:5s linear .2s infinite pweek;animation:5s linear .2s infinite pweek}#svg path:nth-child(6){-webkit-animation:5s linear .24s infinite pweek;animation:5s linear .24s infinite pweek}#svg path:nth-child(7){-webkit-animation:5s linear .28s infinite pweek;animation:5s linear .28s infinite pweek}#svg path:nth-child(8){-webkit-animation:5s linear .32s infinite pweek;animation:5s linear .32s infinite pweek}#svg path:nth-child(9){-webkit-animation:5s linear .36s infinite pweek;animation:5s linear .36s infinite pweek}#svg path:nth-child(10){-webkit-animation:5s linear .4s infinite pweek;animation:5s linear .4s infinite pweek}#svg path:nth-child(11){-webkit-animation:5s linear .44s infinite pweek;animation:5s linear .44s infinite pweek}#svg path:nth-child(12){-webkit-animation:5s linear .48s infinite pweek;animation:5s linear .48s infinite pweek}#svg path:nth-child(13){-webkit-animation:5s linear .52s infinite pweek;animation:5s linear .52s infinite pweek}#svg path:nth-child(14){-webkit-animation:5s linear .56s infinite pweek;animation:5s linear .56s infinite pweek}#svg path:nth-child(15){-webkit-animation:5s linear .6s infinite pweek;animation:5s linear .6s infinite pweek}#svg path:nth-child(16){-webkit-animation:5s linear .64s infinite pweek;animation:5s linear .64s infinite pweek}#svg path:nth-child(17){-webkit-animation:5s linear .68s infinite pweek;animation:5s linear .68s infinite pweek}#svg path:nth-child(18){-webkit-animation:5s linear .72s infinite pweek;animation:5s linear .72s infinite pweek}#svg path:nth-child(19){-webkit-animation:5s linear .76s infinite pweek;animation:5s linear .76s infinite pweek}#svg path:nth-child(20){-webkit-animation:5s linear .8s infinite pweek;animation:5s linear .8s infinite pweek}#svg path:nth-child(21){-webkit-animation:5s linear .84s infinite pweek;animation:5s linear .84s infinite pweek}#svg path:nth-child(22){-webkit-animation:5s linear .88s infinite pweek;animation:5s linear .88s infinite pweek}#svg path:nth-child(23){-webkit-animation:5s linear .92s infinite pweek;animation:5s linear .92s infinite pweek}#svg path:nth-child(24){-webkit-animation:5s linear .96s infinite pweek;animation:5s linear .96s infinite pweek}#svg path:nth-child(25){-webkit-animation:5s linear 1s infinite pweek;animation:5s linear 1s infinite pweek}#svg path:nth-child(26){-webkit-animation:5s linear 1.04s infinite pweek;animation:5s linear 1.04s infinite pweek}#svg path:nth-child(27){-webkit-animation:5s linear 1.08s infinite pweek;animation:5s linear 1.08s infinite pweek}#svg path:nth-child(28){-webkit-animation:5s linear 1.12s infinite pweek;animation:5s linear 1.12s infinite pweek}#svg path:nth-child(29){-webkit-animation:5s linear 1.16s infinite pweek;animation:5s linear 1.16s infinite pweek}#svg path:nth-child(30){-webkit-animation:5s linear 1.2s infinite pweek;animation:5s linear 1.2s infinite pweek}#svg path:nth-child(31){-webkit-animation:5s linear 1.24s infinite pweek;animation:5s linear 1.24s infinite pweek}#svg path:nth-child(32){-webkit-animation:5s linear 1.28s infinite pweek;animation:5s linear 1.28s infinite pweek}#svg path:nth-child(33){-webkit-animation:5s linear 1.32s infinite pweek;animation:5s linear 1.32s infinite pweek}#svg path:nth-child(34){-webkit-animation:5s linear 1.36s infinite pweek;animation:5s linear 1.36s infinite pweek}#svg path:nth-child(35){-webkit-animation:5s linear 1.4s infinite pweek;animation:5s linear 1.4s infinite pweek}#svg path:nth-child(36){-webkit-animation:5s linear 1.44s infinite pweek;animation:5s linear 1.44s infinite pweek}#svg path:nth-child(37){-webkit-animation:5s linear 1.48s infinite pweek;animation:5s linear 1.48s infinite pweek}#svg path:nth-child(38){-webkit-animation:5s linear 1.52s infinite pweek;animation:5s linear 1.52s infinite pweek}#svg path:nth-child(39){-webkit-animation:5s linear 1.56s infinite pweek;animation:5s linear 1.56s infinite pweek}#svg path:nth-child(40){-webkit-animation:5s linear 1.6s infinite pweek;animation:5s linear 1.6s infinite pweek}#svg path:nth-child(41){-webkit-animation:5s linear 1.64s infinite pweek;animation:5s linear 1.64s infinite pweek}#svg path:nth-child(42){-webkit-animation:5s linear 1.68s infinite pweek;animation:5s linear 1.68s infinite pweek}#svg path:nth-child(43){-webkit-animation:5s linear 1.72s infinite pweek;animation:5s linear 1.72s infinite pweek}#svg path:nth-child(44){-webkit-animation:5s linear 1.76s infinite pweek;animation:5s linear 1.76s infinite pweek}#svg path:nth-child(45){-webkit-animation:5s linear 1.8s infinite pweek;animation:5s linear 1.8s infinite pweek}#svg path:nth-child(46){-webkit-animation:5s linear 1.84s infinite pweek;animation:5s linear 1.84s infinite pweek}#svg path:nth-child(47){-webkit-animation:5s linear 1.88s infinite pweek;animation:5s linear 1.88s infinite pweek}#svg path:nth-child(48){-webkit-animation:5s linear 1.92s infinite pweek;animation:5s linear 1.92s infinite pweek}#svg path:nth-child(49){-webkit-animation:5s linear 1.96s infinite pweek;animation:5s linear 1.96s infinite pweek}#svg path:nth-child(50){-webkit-animation:5s linear 2s infinite pweek;animation:5s linear 2s infinite pweek}#svg path:nth-child(51){-webkit-animation:5s linear 2.04s infinite pweek;animation:5s linear 2.04s infinite pweek}#svg path:nth-child(52){-webkit-animation:5s linear 2.08s infinite pweek;animation:5s linear 2.08s infinite pweek}#svg path:nth-child(53){-webkit-animation:5s linear 2.12s infinite pweek;animation:5s linear 2.12s infinite pweek}#svg path:nth-child(54){-webkit-animation:5s linear 2.16s infinite pweek;animation:5s linear 2.16s infinite pweek}#svg path:nth-child(55){-webkit-animation:5s linear 2.2s infinite pweek;animation:5s linear 2.2s infinite pweek}#svg path:nth-child(56){-webkit-animation:5s linear 2.24s infinite pweek;animation:5s linear 2.24s infinite pweek}#svg path:nth-child(57){-webkit-animation:5s linear 2.28s infinite pweek;animation:5s linear 2.28s infinite pweek}#svg path:nth-child(58){-webkit-animation:5s linear 2.32s infinite pweek;animation:5s linear 2.32s infinite pweek}@-webkit-keyframes pweek{0%,to{fill:#0057b8}16.66667%{fill:#f11e4a}33.33333%{fill:#237}50%{fill:#229c79}66.66667%{fill:#f8a527}83.33333%{fill:#266d7f}}@keyframes pweek{0%,to{fill:#0057b8}16.66667%{fill:#f11e4a}33.33333%{fill:#237}50%{fill:#229c79}66.66667%{fill:#f8a527}83.33333%{fill:#266d7f}}`
        }}
      ></style>
      <article className="absolute left-[50%] top-[50%] translate-x-[-50%] translate-y-[-50%] text-center">
        <svg version="1.1" id="svg" x="0px" y="0px" width="250px" height="112px" viewBox="0 0 3462.869 1552" enableBackground="new 0 0 3462.869 1552">
          <path d="M806.917,363.94V107.66l221.833,128.139L806.917,363.94" />
          <path fillOpacity="0.7" d="M96.747,501.107V244.822l221.834,128.137L96.747,501.107" />
          <path fillOpacity="0.7" d="M96.747,774.959V518.674l221.834,128.144L96.747,774.959" />
          <path fillOpacity="0.6" d="M323.632,364.14V107.847L101.798,235.989L323.632,364.14" />
          <path fillOpacity="0.5" d="M323.632,637.688V381.404L101.798,509.547L323.632,637.688" />
          <path fillOpacity="0.85" d="M806.917,637.787V381.502l221.833,128.144L806.917,637.787" />
          <path fillOpacity="0.7" d="M1032.306,774.368V518.083L810.481,646.231L1032.306,774.368" />
          <path fillOpacity="0.7" d="M1032.306,501.107V244.822L810.481,372.966L1032.306,501.107" />
          <path fillOpacity="0.7" d="M332.96,911.501V655.216l221.839,128.142L332.96,911.501" />
          <path fillOpacity="0.7" d="M323.632,911.529V655.25L101.798,783.386L323.632,911.529" />
          <path fillOpacity="0.6" d="M806.917,911.137V654.866l221.833,128.127L806.917,911.137" />
          <path fillOpacity="0.85" d="M808.699,1184.994V928.71l221.845,128.137L808.699,1184.994" />
          <path fillOpacity="0.7" d="M1034.097,1321.577v-256.293L812.264,1193.43L1034.097,1321.577" />
          <path fillOpacity="0.7" d="M1034.097,1048.311V792.02L812.264,920.165L1034.097,1048.311" />
          <path fillOpacity="0.6" d="M808.699,1458.338v-256.272l221.845,128.126L808.699,1458.338" />
          <path fillOpacity="0.6" d="M569.971,1048.311V792.02l221.834,128.156L569.971,1048.311" />
          <path fillOpacity="0.5" d="M560.086,1048.311V792.02L338.262,920.176L560.086,1048.311" />
          <path fillOpacity="0.5" d="M796.86,911.334V655.063L575.012,783.194L796.86,911.334" />
          <path d="M1736.824,364.132V107.84l221.829,128.148L1736.824,364.132" />
          <path d="M1973.771,500.708v-256.28l221.834,128.136L1973.771,500.708" />
          <path fillOpacity="0.85" d="M1499.813,500.858V244.571l221.829,128.134L1499.813,500.858" />
          <path fillOpacity="0.9" d="M1726.94,364.132V107.84l-221.825,128.148L1726.94,364.132" />
          <path fillOpacity="0.7" d="M1263.59,910.949v-256.28l221.845,128.137L1263.59,910.949" />
          <path fillOpacity="0.7" d="M1263.59,1184.806V928.523l221.845,128.137L1263.59,1184.806" />
          <path fillOpacity="0.9" d="M1963.708,500.905V244.618L1741.87,372.762L1963.708,500.905" />
          <path fillOpacity="0.6" d="M1490.476,500.905V244.618l-221.834,128.144L1490.476,500.905" />
          <path fillOpacity="0.5" d="M1490.476,1047.533V791.25l-221.834,128.138L1490.476,1047.533" />
          <path fillOpacity="0.7" d="M1263.59,637.208V380.916l221.845,128.142L1263.59,637.208" />
          <path fillOpacity="0.5" d="M1490.476,773.783V517.495l-221.834,128.144L1490.476,773.783" />
          <path fillOpacity="0.85" d="M1973.771,1047.632V791.349l221.834,128.138L1973.771,1047.632" />
          <path fillOpacity="0.7" d="M2199.158,1184.216V927.922l-221.818,128.147L2199.158,1184.216" />
          <path fillOpacity="0.7" d="M2199.158,910.949v-256.28L1977.34,782.811L2199.158,910.949" />
          <path fillOpacity="0.85" d="M1973.771,773.886v-256.29l221.834,128.148L1973.771,773.886" />
          <path fillOpacity="0.7" d="M2199.158,637.201V380.916L1977.34,509.058L2199.158,637.201" />
          <path fillOpacity="0.7" d="M1499.813,1321.34v-256.281l221.829,128.146L1499.813,1321.34" />
          <path fillOpacity="0.7" d="M1490.476,1321.38v-256.293l-221.834,128.146L1490.476,1321.38" />
          <path fillOpacity="0.6" d="M1973.771,1320.986v-256.272l221.834,128.116L1973.771,1320.986" />
          <path fillOpacity="0.6" d="M1736.824,1458.16v-256.292l221.829,128.146L1736.824,1458.16" />
          <path fillOpacity="0.5" d="M1726.94,1458.16v-256.292l-221.825,128.146L1726.94,1458.16" />
          <path fillOpacity="0.5" d="M1963.708,1321.183V1064.91l-221.838,128.126L1963.708,1321.183" />
          <path d="M3138.832,363.94V107.66l221.844,128.139L3138.832,363.94" />
          <path fillOpacity="0.7" d="M2428.662,501.107v-256.29l221.834,128.141L2428.662,501.107" />
          <path fillOpacity="0.7" d="M2428.662,774.959V518.674l221.834,128.144L2428.662,774.959" />
          <path fillOpacity="0.6" d="M2655.537,364.14V107.847l-221.824,128.141L2655.537,364.14" />
          <path fillOpacity="0.5" d="M2655.537,637.688V381.403l-221.824,128.144L2655.537,637.688" />
          <path fillOpacity="0.85" d="M3138.832,637.787V381.502l221.844,128.144L3138.832,637.787" />
          <path fillOpacity="0.7" d="M3364.221,774.368V518.083l-221.819,128.148L3364.221,774.368" />
          <path fillOpacity="0.7" d="M3364.221,501.107V244.822l-221.819,128.145L3364.221,501.107" />
          <path fillOpacity="0.7" d="M2664.88,911.501V655.216l221.824,128.141L2664.88,911.501" />
          <path fillOpacity="0.7" d="M2655.537,911.529V655.25l-221.824,128.136L2655.537,911.529" />
          <path fillOpacity="0.6" d="M3138.832,911.137V654.866l221.844,128.127L3138.832,911.137" />
          <path fillOpacity="0.85" d="M3140.614,1184.993V928.7l221.844,128.146L3140.614,1184.993" />
          <path fillOpacity="0.7" d="M3366.003,1321.577v-256.293l-221.82,128.146L3366.003,1321.577" />
          <path fillOpacity="0.7" d="M3366.003,1048.311V792.02l-221.82,128.146L3366.003,1048.311" />
          <path fillOpacity="0.6" d="M3140.614,1458.338v-256.272l221.844,128.126L3140.614,1458.338" />
          <path fillOpacity="0.6" d="M2901.886,1048.311V792.02l221.834,128.155L2901.886,1048.311" />
          <path fillOpacity="0.5" d="M2892.002,1048.311V792.02l-221.825,128.155L2892.002,1048.311" />
          <path fillOpacity="0.5" d="M3128.78,911.334V655.063l-221.849,128.131L3128.78,911.334" />
        </svg>
        <p className="mt-[20px] text-[32px] text-[#ccc]">Page not found.</p>
      </article>
    </>
  );
};

export default NotFound;
