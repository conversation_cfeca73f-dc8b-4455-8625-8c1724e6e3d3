import { Helmet } from 'react-helmet';
import demoRequest from '@/modules/demo-request';
import { Button } from 'react-vant';

export default function Home() {
  /** 发起请求模拟 */
  async function requestDemo() {
    const dataObj = await demoRequest({ url: 'https://suggest.taobao.com/sug', data: { c: 1 } });
    console.log('dataObj: ', dataObj);
  }
  return (
    <>
      <Helmet>
        <title>只有一个路由应用</title>
      </Helmet>
      <div className="mt-4 border p-[10px]">
        <h1>只有一个路由应用</h1>
        <Button
          className="border"
          onClick={() => {
            requestDemo();
          }}
        >
          点击发起请求
        </Button>
      </div>
    </>
  );
}
