import { Helmet } from 'react-helmet';
import useRootStore from '@/pages/demo-multiple-router/store/root-store';
import { Link } from 'react-router-dom';

export default function SubPage() {
  const { xxx, updateXXX } = useRootStore(state => state);
  return (
    <>
      <Helmet>
        <title>Sub Title</title>
        <meta name="description" content="Sub description" />
      </Helmet>
      <div>
        <p>Demo sub page</p>
        <p>rootStore: {xxx}</p>
        <button
          onClick={() => {
            updateXXX(1);
          }}
        >
          点击更新
        </button>
        <div className="mt-[10px]">
          <Link to="/">跳转主页面</Link>
        </div>
      </div>
    </>
  );
}
