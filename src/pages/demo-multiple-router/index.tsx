import { Helmet } from 'react-helmet';
import useRootStore from './store/root-store';
import { Link, useNavigate } from 'react-router-dom';
import netRequest, { requestMessage } from '@fe-design/utils/net-request';

interface CustomError {
  msg?: string;
  message?: string;
}

export default function Home() {
  const { xxx, updateXXX } = useRootStore(state => state);
  const navigate = useNavigate();

  /** 发起请求模拟 */
  async function requestDemo() {
    const dataObj = await netRequest
      .get('https://suggest.taobao.com/sug', {}, { 'X-showMessage': false })
      .catch((err: CustomError) => {
        const errorMessage = err.msg || err.message || 'An unexpected error occurred.';
        requestMessage.error(errorMessage);
      });
    requestMessage.toast('toast');
    console.log('dataObj: ', dataObj);
  }
  return (
    <section>
      <Helmet>
        <title>多路由应用PWA（即多个页面跳转），可定制当前文件夹下的 App.tsx</title>
        <meta
          name="多路由应用PWA（即多个页面跳转），可定制当前文件夹下的 App.tsx"
          content="多路由应用PWA（即多个页面跳转），可定制当前文件夹下的 App.tsx"
        />
      </Helmet>
      <div>
        <h1>多路由应用PWA（即多个页面跳转），可定制当前文件夹下的 App.tsx</h1>
        <Link className="text-sky-400" target="_blank" to="https://nemo.yuque.com/fe-doc/rpqmwg/qurq6gwaggn89ndq">
          点击查看《SPA 应用开发指引》
        </Link>
      </div>
      <div className="mt-2">
        <p>状态管理</p>
        <p>rootStore: {xxx}</p>
        <button
          className="border"
          onClick={() => {
            updateXXX(2);
          }}
        >
          点击更新状态
        </button>
      </div>
      <div className="mt-[20px] flex flex-row">
        <div className="basis-1/2">
          <Link to="/sub" className="border">
            用{'<'}Link{'/>'}标签跳转子页面
          </Link>
        </div>
        <div className="basis-1/2">
          <div
            className="cursor-pointer border"
            onClick={() => {
              navigate('/sub');
            }}
          >
            使用navigate方法跳转子页面
          </div>
        </div>
      </div>
      <div className="mt-2">
        <button className="border" onClick={requestDemo}>
          请求接口数据
        </button>
      </div>
    </section>
  );
}
