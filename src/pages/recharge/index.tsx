import { useTranslation } from '@/configs/locale';
import './index.css';
import { useState, useEffect, useMemo } from 'react';
import singleTicket from '@/assets/recharge/single-ticket.png';
import iconRefresh from '@/assets/recharge/icon-refresh.png';
import iconHelp from '@/assets/recharge/icon-help.png';
import { Revenue } from '@/proto';
import JsBridgeRegister from '@/components/js-bridge-register';
import { CurrencyType } from '@/proto/protobuf/api/revenue/common';
import { PayInfo, SpayClientType } from '@/proto/protobuf/api/pay/pay';
import { Loading, Popover, Toast } from 'react-vant';
import { useJsbridgeReady } from '@/hooks/jsbridge-ready';
import { UnitMap } from '@/configs';
import { callHandler, report } from '@/modules/bridge';
import { getQueryParams, isAppWebviewV2 } from '@/modules/utils';
import { useUserInfo } from '@/hooks/userinfo';

export default function Page() {
  const t = useTranslation('Recharge');
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();
  const { isLagecy, isInApp } = isAppWebviewV2();

  const [ticketAmount, setTicketAmount] = useState({
    available: 0,
    total: 0
  });
  const {
    data: balance,
    isLoading: isLoadingBalance,
    mutate: refetchBalance
  } = Revenue.revenueWallet.getBalance.query({ currency_types: [CurrencyType.CURRENCY_TYPE_GOLDEN_TICKET] }, isReady);
  useEffect(() => {
    const balanceInfo = balance?.balances[0];
    console.log('balance', balanceInfo);
    setTicketAmount({
      available: balanceInfo?.amount ?? 0,
      total: (balanceInfo?.guarantee_amount ?? 0) + (balanceInfo?.amount ?? 0)
    });
  }, [balance]);

  const { data: skuList, isLoading } = Revenue.revenueSpay.listSku.query(
    { client_type: SpayClientType.SPAY_CLIENT_TYPE_WEB, payment_apps: [], pay_types: [], ext: {} },
    isReady
  );

  // 选择商品和支付方式
  const [selectedSkuIdx, setSelectedSkuIdx] = useState<number>(0);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>();
  // 支付方式列表
  const payMethodList = useMemo(() => {
    if (!skuList) return [];
    const list: Partial<PayInfo>[] = [];
    const sku = skuList.list[selectedSkuIdx];
    sku?.items?.forEach(item => {
      if (item.pay_info) {
        // @ts-expect-error 服务端会注入image到item中，但是proto中并没有定义
        if (item.image) {
          // @ts-expect-error 服务端会注入image到item中，但是proto中并没有定义
          item.pay_info.pay_code_img = item.image;
          item.pay_info.pay_code_name = item.pay_info.name;
        }
        list.push(item.pay_info);
      }
    });
    return list;
  }, [skuList, selectedSkuIdx]);
  // 默认选中第一个支付方式
  useEffect(() => {
    setSelectedPaymentMethod(payMethodList[0]?.pay_code);
  }, [payMethodList]);

  // 提交订单
  const handleCreateOrder = async () => {
    Toast.loading('');
    const sku = skuList?.list[selectedSkuIdx];
    console.log('create order', sku, selectedPaymentMethod);
    const item = sku?.items.find(item => item.pay_info?.pay_code === selectedPaymentMethod);
    if (!item) {
      Toast.fail(t('paymentMethodNotFound'));
      return;
    }
    const res = await Revenue.revenueSpay.createSpayOrder.request({
      client_type: SpayClientType.SPAY_CLIENT_TYPE_WEB,
      item_id: item?.item_id,
      pay_code: selectedPaymentMethod
    });
    Toast.clear();
    if (res.third_party_result?.jump_url) {
      report('golden_ticket_recharge_pay', {
        ...queryParams,
        jump_url: res.third_party_result?.jump_url,
        uid: userInfo?.uid
      });
      if (isLagecy) {
        location.href = res.third_party_result?.jump_url;
      } else {
        callHandler('openUrl', {
          url: `crushu://www.crushu.com/?page=sys_web&url=${res.third_party_result?.jump_url}&from=golden_ticket_recharge`
        });
      }
    } else if (res.google_play_result) {
      Toast.info(t('doNotSupportPayment'));
      /* const callHandlerRes = callHandler('googlePay', {
        createSpayOrderRsp: JSON.stringify(res)
      });
      console.log('callHandlerRes', callHandlerRes); */
    }
  };

  return (
    <>
      <JsBridgeRegister />
      <div className={`flex min-h-[100vh] flex-col px-[4.5vw] pt-[4vw]`}>
        {/* 资产信息 */}
        <div className={`info-wrap relative aspect-[328/100] p-1`}>
          <p className={`text-[36px] font-bold text-[#AB591E]`}>{t('ticketTitle')}</p>
          <div className={`mt-1 flex items-center gap-1`}>
            <img className="h-[80px] w-[80px] align-middle" src={singleTicket} alt="ticket" />
            <p className={`text-[28px] font-medium text-[#BB4402]`}>
              <span className="text-[54px] font-bold">{ticketAmount.total}</span> ({t('available')}:{' '}
              {Math.floor(ticketAmount.available)})
            </p>
          </div>
          <div className="absolute right-1 top-1 flex items-center gap-1">
            <Popover
              trigger="click"
              placement="bottom-end"
              className="help-popover"
              reference={<img className="h-[40px] w-[40px]" src={iconHelp} alt="help" />}
            >
              <div className="w-[460px] p-[20px] text-[26px] text-[#121212]">
                If the number of tickets doesn't update after recharging, refresh the page
              </div>
            </Popover>
            <img
              className={`h-[40px] w-[40px] ${isLoadingBalance ? 'animate-spin' : ''}`}
              src={iconRefresh}
              onClick={() => refetchBalance()}
              alt="refresh"
            />
          </div>
        </div>
        {/* sku列表 */}
        <div className={`mt-[40px] grid grid-cols-3 gap-[28px]`}>
          {skuList?.list?.map((item, index) => {
            console.log('item', item.unit, UnitMap[item.unit]);

            return (
              <SkuItem
                key={item.goods_id}
                amount={item.amount}
                img={item.image}
                price={`${UnitMap[item.unit] ?? ''} ${(item.price / 100).toFixed(2)}`}
                selected={index === selectedSkuIdx}
                onClick={() => setSelectedSkuIdx(index)}
              />
            );
          })}
        </div>
        <div className={`mt-[24px]`}>
          <h2 className={`text-[28px] font-bold text-[#1F1F1F]`}>{t('paymentMethods')}</h2>
          {/* 支付方式列表 */}
          <div className={`mt-[20px]`}>
            {payMethodList.map(item => {
              return (
                <PaymentMethod
                  key={item.pay_code}
                  selected={item.pay_code === selectedPaymentMethod}
                  payInfo={item}
                  onClick={code => {
                    console.log('click', code);
                    setSelectedPaymentMethod(code);
                  }}
                />
              );
            })}
          </div>
        </div>
        {/* 提交订单 */}
        <div className={`mt-8`}>
          <div
            className={`submit-order-btn fixed bottom-[4vh] w-[90vw] rounded-full py-[26px] text-center text-[32px] font-medium text-white`}
            onClick={handleCreateOrder}
          >
            {t('pay')} {isLoading && <Loading style={{ display: 'inline-block' }} />}
          </div>
        </div>
      </div>
    </>
  );
}

function SkuItem(props: { amount: number; price: string; selected: boolean; onClick: () => void; img: string }) {
  const { amount, price, selected, onClick } = props;

  return (
    <div
      className={`sku-item rounded-[20px] bg-[#FDE9D4] p-[4px] pb-0 ${selected ? 'bg-[#FE4458]' : ''}`}
      onClick={onClick}
    >
      <div className={`sku-item-info flex-base flex-col rounded-t-[16px] p-[10px] pb-0`}>
        <img className={`mb-[6px] h-[15.53vw] w-[22.18vw] object-contain`} src={props.img} alt="ticket" />
        <p className={`text-center text-[36px] font-bold text-[#1F1F1F]`}>{amount}</p>
      </div>
      <p className={`py-[8px] text-center text-[28px] font-medium text-[#BB4402] ${selected ? 'text-white' : ''}`}>
        {price}
      </p>
    </div>
  );
}

function PaymentMethod({
  selected,
  payInfo,
  onClick
}: {
  selected: boolean;
  payInfo: Partial<PayInfo>;
  onClick: (name: string) => void;
}) {
  return (
    <div className={`flex-between gap-1`} onClick={() => onClick(payInfo?.pay_code ?? '')}>
      <img className={`h-[11.2vw] w-[13.86vw]`} src={payInfo.pay_code_img || payInfo.image} alt={payInfo.name} />
      <div className={`flex-between -mr-[4.5vw] h-[90px] flex-1 border-b border-[#eee] pr-[4.5vw]`}>
        <p className={`flex-1 text-[28px] font-medium text-[#1F1F1F]`}>{payInfo.pay_code_name || payInfo.name}</p>
        <div className={`h-[40px] w-[40px] shrink-0 rounded-full ${selected ? 'selected' : 'unselected'}`} />
      </div>
    </div>
  );
}
