import JsBridgeRegister from '@/components/js-bridge-register';
import { useJsbridgeReady } from '@/hooks/jsbridge-ready';
import { callHandler } from '@/modules/bridge';
import { useEffect } from 'react';

export default function Test() {
  const isready = useJsbridgeReady();
  useEffect(() => {
    console.log('Test');
  }, []);

  const getParams = () => {
    if (isready) {
      callHandler('getPublicParams', { param: 'cou,anm,verc' }).then(res => {
        console.log(res);
        const r = JSON.parse(res);
        console.log(r.cou);
      });
    }
  };
  return (
    <>
      <JsBridgeRegister />
      <div className="flex h-full w-full items-center justify-center">
        {/* <button className="px-text-24" onClick={getParams}>
          获取参数
        </button> */}
        <button
          className="block py-10"
          onClick={() => {
            console.log('click');

            location.assign(
              'gpay://upi/pay?pa=fcbiz3w6hye@freecharge&pn=SELEADMG&mc=7996&tid=AXIFRCO01102024awf7bav6s5&tr=AXIFRCO01102024awf7bav6s5&am=960.00&cu=INR'
            );
          }}
        >
          Text
        </button>
      </div>
    </>
  );
}
