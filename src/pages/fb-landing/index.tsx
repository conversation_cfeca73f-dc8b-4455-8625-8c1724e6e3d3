import { useLocale } from '@/hooks/use-locale';
import { getQueryParams } from '@/modules/utils';
import { useEffect } from 'react';

export default function App() {
  const qs = getQueryParams();
  const t = useLocale({
    en: { install: 'Install', tip: 'Join our platform and become an anchor, you will have unexpected benefits' },
    hi: { install: 'स्थापित करें', tip: 'हमारे मंच से जुड़ें और एंकर बनें, आपको अप्रत्याशित लाभ होंगे' }
  });
  useEffect(() => {
    // @ts-expect-error any
    !(function (f: any, b: any, e: any, v: any, n: any, t: any, s: any) {
      if (f.fbq) return;
      n = f.fbq = function () {
        // eslint-disable-next-line
        n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments);
      };
      if (!f._fbq) f._fbq = n;
      n.push = n;
      n.loaded = !0;
      n.version = '2.0';
      n.queue = [];
      t = b.createElement(e);
      t.async = !0;
      t.src = v;
      s = b.getElementsByTagName(e)[0];
      s.parentNode.insertBefore(t, s);
    })(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
    // @ts-expect-error any
    window.fbq('init', '1236746151431789');
    // @ts-expect-error any
    window.fbq('track', 'PageView');
  }, []);
  return (
    <div>
      <div className="gap-96px flex h-screen flex-col items-center justify-center text-center">
        <img
          className="h-50vw max-h-480px w-50vw max-w-480px align-middle"
          src="https://res.crushu.net/pool/pub/official/crushu/logo-a.png"
          alt=""
        />
        <p className="w-85vw text-32px">{t('tip')}</p>
        <a
          className="px-94px py-24px text-48px rounded-full bg-gradient-to-r from-[#FF0B4D] to-[#FF7C19] font-bold text-white no-underline"
          href={`https://api.crushu.net/api/dev/redirect/redirect?pkg_name=com.crushu.app&pub=CU_fb${qs.sub ? `&subpub=${qs.sub}` : ''}`}
        >
          {t('install')}
        </a>
      </div>
    </div>
  );
}
