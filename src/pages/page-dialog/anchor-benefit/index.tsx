import JsBridgeRegister from '@/components/js-bridge-register';
import { useUserInfo } from '@/hooks/userinfo';
import { closeView, report } from '@/modules/bridge';
import { getQueryParams, highLight } from '@/modules/utils';
import { useEffect } from 'react';
import '../index.css';
import { hooks } from 'react-vant';
import { useLocale } from '@/hooks/use-locale';
import locale from './locale';

export default function TinyAmount() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();

  useEffect(() => {
    if (userInfo) {
      report('popup_page_show', { type: 'anchor-benefit', ...queryParams, uid: userInfo.uid });
    }
  }, [userInfo]);

  const { current } = hooks.useCountDown({
    time: 5 * 1000,
    autostart: true,
    onChange: () => {}
  });
  const isActive = current.seconds === 0;
  const close = () => isActive && closeView(6);

  return (
    <div className="page flex-center flex-col" onClick={close}>
      <JsBridgeRegister />
      <div
        className="w-90vw h-109vw px-8vw pt-18vw pb-7vw pt-37% relative flex flex-col items-center bg-[url(@/assets/dialog-tiny-amount-recharge/bg.png)] bg-contain bg-center bg-no-repeat leading-snug"
        onClick={e => e.stopPropagation()}
      >
        <h1 className="pxtext-64 text-stroke-3px tracking-2px text-stroke-transparent w-300px top-14% left-9.5% absolute bg-gradient-to-b from-[#FFFBE0] to-[#FFEFCE] bg-clip-text font-black leading-none text-transparent drop-shadow-[0_0_4px_#B5090980]">
          {t('title')}
        </h1>
        <p className="text-32px text-shadow-lg h-3em font-medium leading-tight text-white">
          {/* Active from 10Pm to 1Am for higher earnings! */}
          {highLight(t('description'), 'duration', t('duration'), { color: '#ff0' })}
          {/* Daily active <span className="text-#ff0">≥ 3 hours</span> for higher earnings! */}
        </p>
        <div className="flex-center gap-18px h-204px my-10px relative w-full bg-[url(@/assets/dialog-tiny-amount-recharge/panel.png)] bg-contain bg-center bg-no-repeat text-center">
          <span
            className="text-#EB6323 text-20px -ms-20px pt-110px mt-10px bg-top-center bg-[url(@/assets/dialog-recharge-off/point-single.webp)] bg-no-repeat font-bold"
            style={{ backgroundSize: '80%' }}
          >
            <span>1500 Points/min</span>
          </span>
          <div className="w-80px h-130px mt-10px -ms-16px bg-[url(@/assets/dialog-recharge-off/img-up.png)] bg-contain bg-bottom bg-no-repeat font-bold">
            Up
          </div>
          <span
            className="text-#EB6323 text-28px pt-140px mb-10px bg-top-center bg-[url(@/assets/dialog-recharge-off/point-multi.webp)] bg-no-repeat font-bold"
            style={{ backgroundSize: '100%' }}
          >
            <span>
              <span className="text-#c39505">1800</span> Points/min
            </span>
          </span>

          <span className="text-24px p-38px leading-0.5 right-8px top-20px absolute bg-[url(@/assets/dialog-recharge-off/icon-off.png)] bg-contain bg-center bg-no-repeat font-bold text-white">
            +20%
          </span>
        </div>
        <div
          className={`flex-center text-42px w-x w-560px h-160px rounded-full bg-[url(@/assets/dialog-recharge-off/bg-btn.png)] bg-contain bg-center bg-no-repeat font-black text-white ${isActive ? '' : 'opacity-70'}`}
          onClick={() => {
            if (!isActive) return;
            report('popup_page_click', { type: 'anchor-benefit', ...queryParams, uid: userInfo?.uid });
            close();
          }}
        >
          {t('btn')} {!isActive && <span className="text-24px text-white/50">({current.seconds})</span>}
        </div>
      </div>
      <div className="close mt-2vw" />
    </div>
  );
}
