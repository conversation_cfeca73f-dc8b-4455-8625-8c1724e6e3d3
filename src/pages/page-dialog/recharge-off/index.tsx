import '../index.css';
import { callHandler, closeView, openUrl, report } from '@/modules/bridge';
import { getQueryParams, highLight } from '@/modules/utils';
import JsBridgeRegister from '@/components/js-bridge-register';
import { useLocale } from '@/hooks/use-locale';
import locale from './locale';
import { useEffect } from 'react';
import { useUserInfo } from '@/hooks/userinfo';

window.onload = () => {
  report?.('popup_page_load', { type: 'recharge-discount', ...getQueryParams() });
};
export default function RechargeOff() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();

  useEffect(() => {
    if (userInfo) {
      report('popup_page_show', { type: 'recharge-discount', ...queryParams, uid: userInfo.uid });
    }
  }, [userInfo]);

  return (
    <div className="page flex-center flex-col">
      <JsBridgeRegister />
      <div
        className={`vww-90 vwh-109 vwpx-8 vwpt-18 vwpb-7 flex flex-col items-center bg-[url(@/assets/dialog-recharge-off/bg.png)] bg-contain bg-center bg-no-repeat leading-snug`}
      >
        <h1 className="pxtext-48 text-shadow-lg font-black italic leading-relaxed text-white">{t('title')}</h1>
        <p className="pxtext-36 text-shadow-lg h-[3em] font-bold leading-tight text-white">
          {highLight(t('description'), 'off', t('off', { discount: queryParams.discount || 0 }), {
            color: '#FFF500',
            fontSize: '0.4rem',
            fontWeight: '900'
          })}
        </p>
        <div className="flex-between pxw-520 pxh-180 flex-basis-[180px] relative mb-2.5 mt-1 bg-[url(@/assets/dialog-recharge-off/img-diamond.png)] bg-contain bg-center bg-no-repeat">
          <div className="discount text-shadow-lg pxw-80 pxtext-30 pxtop-38 pxright-30 absolute text-center font-black text-white">
            +{queryParams.discount || 0}%
          </div>
        </div>
        <div
          className="flex-center pxtext-42 pxw-504 pxh-140 rounded-full bg-[url(@/assets/dialog-recharge-off/bg-btn.png)] bg-contain bg-center bg-no-repeat font-black text-white"
          onClick={() => {
            report('popup_page_click', { type: 'recharge-discount', ...queryParams, uid: userInfo?.uid });
            const url = new URL(queryParams.dl);
            url.searchParams.set('from', 'recharge-discount');
            callHandler('openUrl', { url: url.toString() });
            closeView(6);
          }}
        >
          {t('btn')}
        </div>
      </div>
      <div
        className="close vwmt-2"
        onClick={() => {
          report('popup_page_close', { type: 'recharge-discount', ...queryParams, uid: userInfo?.uid });
          closeView(6);
        }}
      />
    </div>
  );
}
