import JsBridgeRegister from '@/components/js-bridge-register';
import { useLocale } from '@/hooks/use-locale';
import { useUserInfo } from '@/hooks/userinfo';
import locale from './locale';
import '../index.css';
import { callHandler, closeView, report } from '@/modules/bridge';
import { getQueryParams, highLight } from '@/modules/utils';
import IconDiamond from '@/assets/dialog-tiny-amount-recharge/diamond.png';
import { Fragment, useEffect } from 'react';

window.onload = () => {
  report?.('popup_page_load', { type: 'tiny-amount-recharge', ...getQueryParams() });
};

export default function TinyAmount() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();

  useEffect(() => {
    if (userInfo) {
      report('popup_page_show', { type: 'tiny-amount-recharge', ...queryParams, uid: userInfo.uid });
    }
  }, [userInfo]);

  return (
    <div className="page flex-center flex-col">
      <JsBridgeRegister />
      <div
        className="w-90vw h-109vw px-8vw pt-18vw pb-7vw pt-37% relative flex flex-col items-center bg-[url(@/assets/dialog-tiny-amount-recharge/bg.png)] bg-contain bg-center bg-no-repeat leading-snug"
        onClick={e => e.stopPropagation()}
      >
        <h1 className="pxtext-64 text-stroke-3px tracking-2px text-stroke-transparent w-300px top-14% left-9.5% absolute bg-gradient-to-b from-[#FFFBE0] to-[#FFEFCE] bg-clip-text font-black leading-none text-transparent drop-shadow-[0_0_4px_#B5090980]">
          {t('title')}
        </h1>
        <p className="text-32px text-shadow-lg h-3em font-medium leading-tight text-white">
          {highLight(t('description'), 'amount', `${queryParams.amount}`, {
            color: '#FFF500',
            fontSize: '0.4rem',
            fontWeight: '900'
          }).map((s, i) => (
            <Fragment key={i}>{s}</Fragment>
          ))}
        </p>
        <div className="flex-center gap-18px h-204px my-10px relative w-full bg-[url(@/assets/dialog-tiny-amount-recharge/panel.png)] bg-contain bg-center bg-no-repeat">
          <img src={IconDiamond} alt="" className="w-176px h-128px" />
          <span className="text-#EB6323 text-64px font-black">×{queryParams.diamonds}</span>
        </div>
        <div
          className="flex-center text-42px w-x w-560px h-160px rounded-full bg-[url(@/assets/dialog-recharge-off/bg-btn.png)] bg-contain bg-center bg-no-repeat font-black text-white"
          onClick={() => {
            report('popup_page_click', { type: 'tiny-amount-recharge', ...queryParams, uid: userInfo?.uid });
            callHandler('tiny_amount_recharge', { uid: userInfo?.uid });
          }}
        >
          {`${t('btn')} ${queryParams.amount}`}
        </div>
      </div>
      <div
        className="close mt-2vw"
        onClick={() => {
          report('popup_page_close', { type: 'tiny-amount-recharge', ...queryParams, uid: userInfo?.uid });
          closeView(6);
        }}
      />
    </div>
  );
}
