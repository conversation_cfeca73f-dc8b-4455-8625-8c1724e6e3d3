import { useLocale } from '@/hooks';
import locale from './locale';
import imgApple from '@/assets/app_store_apple.png';
import NahkiLogo from './images/logo.png';
import bgStarryNightSky from './images/bg_starry_night_sky.png';
import group21606GradationBackground from './images/group_21606_gradation_background.png';
import sloganConnectingThoughts from './images/slogan_connecting_thoughts.png';
import fragmentBackgroundSmilingFace from './images/fragment_background_smiling_face.png';
import polygonAbstractShape from './images/polygon_abstract_shape.png';
import hueSaturationVibrantUiElement from './images/hue_saturation_vibrant_ui_element.png';
import polygonCopy2GradientBackground from './images/polygon_copy_2_gradient_background.png';
import hueSaturationChatBubbleBackground from './images/hue_saturation_chat_bubble_background.png';
import polygonAbstractGradientBackground from './images/polygon_abstract_gradient_background.png';
import { Link } from 'react-router-dom';
import { Helmet } from 'react-helmet';

export default function LandingPage() {
  const t = useLocale(locale);

  return (
    <>
      <Helmet>
        <title>Nahki</title>
      </Helmet>
      <div className="relative flex min-h-screen w-full flex-col">
        <div
          className="h-[1624px] w-full bg-cover bg-center bg-no-repeat"
          style={{ backgroundImage: `url(${bgStarryNightSky})` }}
        >
          {/* Header */}
          <header
            className="pt-33px pb-56px flex w-full items-center justify-between bg-cover px-[32px]"
            style={{
              backgroundImage: `url(${group21606GradationBackground})`
            }}
          >
            <div className="flex items-center">
              <img src={NahkiLogo} alt="Nahki Logo" className="h-[80px] w-[80px]" />
              <h1 className="text-36px ml-[16px] font-bold text-white">Nahki</h1>
            </div>
            <a
              href={window.__INITIAL_STATE__.appData.data.appstoreLink}
              className="inline-block h-[70px] w-[198px] shrink-0"
            >
              <img src={imgApple} alt="Get it on App Store" className="h-full w-full" />
            </a>
          </header>

          {/* Main Content */}
          <main className="mt-[80px] flex flex-col items-center">
            {/* App Logo */}
            <div className="mb-[40px] h-[198px] w-[198px]">
              <img src={NahkiLogo} alt="Nahki App Logo" className="h-full w-full" />
            </div>

            {/* Slogan */}
            <div className="mb-[40px] h-[90px] w-[540px]">
              <img src={sloganConnectingThoughts} alt="Connect Beyond Words" className="h-full w-full" />
            </div>

            {/* Feature Cards */}
            <div className="mt-[20px] flex w-full flex-col gap-[20px] px-[30px]">
              {/* Interactive Chats */}
              <div
                className="relative h-[200px] w-full rounded-[20px] bg-cover bg-center"
                style={{
                  backgroundImage: `url(${fragmentBackgroundSmilingFace})`
                }}
              >
                <div className="absolute left-[30px] top-[30px] flex items-center gap-[10px]">
                  <div className="h-120px w-120px" />
                  <div className="flex flex-col">
                    <h2 className="text-30px text-#9AC2FF flex items-center font-bold">
                      <img src={polygonAbstractShape} className="h-34px w-26px me-10px" />
                      {t('interactiveChatsTitle')}
                    </h2>
                    <p className="text-24px mt-16px text-#D6D6D6 max-w-[500px] leading-tight">
                      {t('interactiveChatsDesc')}
                    </p>
                  </div>
                </div>
              </div>

              {/* Smooth Chats */}
              <div
                className="relative h-[190px] w-full rounded-[20px] bg-cover bg-center"
                style={{
                  backgroundImage: `url(${hueSaturationVibrantUiElement})`
                }}
              >
                <div className="absolute left-[30px] top-[30px] flex flex-col">
                  <h2 className="text-30px text-#FFA4F8 flex items-center font-bold">
                    <img src={polygonCopy2GradientBackground} className="h-34px w-26px me-10px" />{' '}
                    {t('smoothChatsTitle')}
                  </h2>
                  <p className="text-24px mt-16px text-#D6D6D6 max-w-[500px] leading-tight">{t('smoothChatsDesc')}</p>
                </div>
                <div className="absolute right-[30px] top-[60px] h-[90px] w-[90px]">
                  <div className="h-120px w-120px" />
                </div>
              </div>

              {/* Trusted Environment */}
              <div
                className="relative h-[190px] w-full rounded-[20px] bg-cover bg-center"
                style={{
                  backgroundImage: `url(${hueSaturationChatBubbleBackground})`
                }}
              >
                <div className="absolute left-[30px] top-[30px] flex items-center gap-[10px]">
                  <div className="h-120px w-120px" />
                  <div className="flex flex-col">
                    <h2 className="text-30px text-#91F2FF flex items-center font-bold">
                      <img src={polygonAbstractGradientBackground} className="h-34px w-26px me-10px" />{' '}
                      {t('trustedEnvironmentTitle')}
                    </h2>
                    <p className="text-24px mt-16px text-#D6D6D6 max-w-[500px] leading-tight">
                      {t('trustedEnvironmentDesc')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </main>

          {/* Footer */}
          <footer className="mt-[80px] text-center text-white">
            <div className="text-28px flex justify-center gap-[20px]">
              <a href="/terms" className="underline-offset-6 text-white underline">
                {t('termsOfUse')}
              </a>
              <span>|</span>
              <a href="/privacy" className="underline-offset-6 text-white underline">
                {t('privacyPolicy')}
              </a>
              <span>|</span>
              <Link to="/account-login" className="underline-offset-6 text-white underline">
                Delete Account
              </Link>
            </div>
            <p className="mt-[20px] text-[24px]">
              {t('contactUs')}:{' '}
              <a href="mailto:<EMAIL>" className="text-[#5CEFFF]">
                {t('contactEmail')}
              </a>
            </p>
            <p className="mb-[30px] mt-[10px] text-[24px]">{t('copyright')}</p>
          </footer>
        </div>
      </div>
    </>
  );
}
