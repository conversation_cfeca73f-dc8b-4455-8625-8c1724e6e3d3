import { AccountLogin } from '@/components/account-login-lagecy';
import { firebaseConfig, GradientColors, Name, Subanm, PkgMap, Domain, Subpath } from './config';
import { getBaseUrlV2, getEnv } from '@/modules/utils';
export default function Index() {
  const env = getEnv();
  return (
    <AccountLogin
      env={env}
      pkg={PkgMap[env]}
      gradient={`linear-gradient(90deg, ${GradientColors[0]} 0%, ${GradientColors[1]} 100%)`}
      excludeType={['Snapchat', 'Twitter', 'Apple', 'Facebook']}
      appName={Name}
      baseUrl={getBaseUrlV2(Subpath, Domain, env)}
      anm="cu"
      subanm={Subanm}
      firebaseConfig={firebaseConfig[PkgMap[env]]}
    />
  );
}
