import { FirebaseConfigType } from '@/components/account-login-lagecy/login/lib/constant';
import imgIcon1 from './images/icon_pic.png';
import imgIcon2 from './images/icon_exp.png';
import imgIcon3 from './images/icon_secure.png';

/** 按钮渐变色 */
export const GradientColors = ['#FF7425', '#FFD865'] as const;
export const Subanm = 'vidcoo';
export const Name = 'VidCoo';
export const Slogan = 'Connect Smarter, Talk Better';
export const Domain = 'vidcoo.xyz';
export const Subpath = 'vidcoo';
export const Features = [
  {
    icon: imgIcon1,
    title: 'Share Your Life:',
    desc: 'Share What You Want Easily'
  },
  {
    icon: imgIcon2,
    title: 'Ultra-Smooth Experience:',
    desc: 'Enjoy smooth and amazing using experience'
  },
  {
    icon: imgIcon3,
    title: 'Secure & Private:',
    desc: 'Your safety and privacy are our top priority'
  }
];
export const Email = '<EMAIL>';
export const CacheName = `${Subanm}_user_info`;
export const PkgMap = {
  prod: 'app.vidcoo.com',
  test: 'app.vidcoo.com.dev',
  local: 'app.vidcoo.com.dev'
};

export const firebaseConfig: Record<string, FirebaseConfigType> = {
  [PkgMap.prod]: {
    projectId: 'vidcoo-7a157',
    authDomain: 'vidcoo-7a157.firebaseapp.com',
    storageBucket: 'vidcoo-7a157.firebasestorage.app',
    messagingSenderId: '870205543726',
    appId: '1:870205543726:android:19ec87ad60d3f8d620d6bf',
    apiKey: 'AIzaSyApdy82n9aphYaQLJXIjPQif_OWM0FFq50'
  },
  [PkgMap.test]: {
    projectId: 'vidcoo-7a157',
    authDomain: 'vidcoo-7a157.firebaseapp.com',
    storageBucket: 'vidcoo-7a157.firebasestorage.app',
    messagingSenderId: '870205543726',
    appId: '1:870205543726:android:893b967e36bdc74420d6bf',
    apiKey: 'AIzaSyApdy82n9aphYaQLJXIjPQif_OWM0FFq50'
  }
};
