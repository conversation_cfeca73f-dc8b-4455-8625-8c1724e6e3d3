import { FirebaseConfigType } from '@/components/account-login-lagecy/login/lib/constant';
import imgIcon1 from './images/icon_chat.png';
import imgIcon2 from './images/icon_good.png';
import imgIcon3 from './images/icon_video.png';

/** 按钮渐变色 */
export const GradientColors = ['#FBBE00', '#F96C00'] as const;
export const Subanm = 'milna';
export const Name = 'Milna';
export const Slogan = 'Every Chats Matters Here';
export const Domain = 'linko.website';
export const Subpath = 'linko';
export const Features = [
  {
    icon: imgIcon1,
    title: 'Connect Anytime:',
    desc: 'Reach out to friends or know new people effortlessly.'
  },
  {
    icon: imgIcon2,
    title: 'Vibrant Live Interactions:',
    desc: 'Make every chat lively and unforgettable.'
  },
  {
    icon: imgIcon3,
    title: 'Personal Video Calls:',
    desc: 'Step into real-time video calls that bring you closer, no matter the distance.'
  }
];
export const Email = '<EMAIL>';
export const CacheName = `${Subanm}_user_info`;
export const PkgMap = {
  prod: 'pkg',
  test: 'pkg.dev',
  local: 'pkg.dev'
};

export const firebaseConfig: Record<string, FirebaseConfigType> = {
  [PkgMap.prod]: {
    projectId: 'boloji-15092',
    authDomain: 'boloji-15092.firebaseapp.com',
    storageBucket: 'boloji-15092.firebasestorage.app',
    messagingSenderId: '892851476083',
    appId: '1:892851476083:web:6149fd29bbe7d7a7ed7c06',
    apiKey: 'AIzaSyBCDkgevg2Aq9SK0FayMN93C8YghDjQmR4'
  },
  [PkgMap.test]: {
    projectId: 'boloji-15092',
    authDomain: 'boloji-15092.firebaseapp.com',
    storageBucket: 'boloji-15092.firebasestorage.app',
    messagingSenderId: '892851476083',
    appId: '1:892851476083:web:6149fd29bbe7d7a7ed7c06',
    apiKey: 'AIzaSyBCDkgevg2Aq9SK0FayMN93C8YghDjQmR4'
  }
};
