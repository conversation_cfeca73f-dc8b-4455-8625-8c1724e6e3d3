import { AccountLogin } from '@/components/account-login-lagecy';
import { getEnv } from '@/modules/utils';
import { formatAndroidVestPageConfig } from '@/modules/utils';
export default function Index() {
  const env = getEnv();
  const { GradientColors, baseUrl, Name, subanm, pkg, firebaseConfig } = formatAndroidVestPageConfig();

  return (
    <AccountLogin
      env={env}
      pkg={pkg}
      gradient={`linear-gradient(90deg, ${GradientColors[0]} 0%, ${GradientColors[1]} 100%)`}
      excludeType={['Snapchat', 'Twitter', 'Apple', 'Facebook']}
      appName={Name}
      baseUrl={baseUrl}
      anm="cu"
      subanm={subanm}
      firebaseConfig={firebaseConfig}
    />
  );
}
