import { FirebaseConfigType } from '@/components/account-login-lagecy/login/lib/constant';
import imgIcon1 from './images/icon_video.png';
import imgIcon2 from './images/icon_call.png';
import imgIcon3 from './images/icon_chat.png';
import imgBG from './images/img_bg.webp';
import imgLogo from './images/logo.webp';
import imgFeatures from './images/features.png';

/** 按钮渐变色 */
export const GradientColors = ['#FF39A3', '#FFAA81'] as const;
export const Subanm = 'kolstar';
export const Name = 'KolStar';
export const Slogan = 'Connect, Chat, Spark';
export const Domain = 'kolstar.online';
export const Subpath = 'kolstar';
export const Images = {
  imgBG,
  imgLogo,
  imgFeatures
};
export const Features = [
  {
    icon: imgIcon1,
    title: '1-on-1 Live Chats:',
    desc: 'Connect instantly with new people in private video calls.'
  },
  {
    icon: imgIcon2,
    title: 'Smart Calling:',
    desc: 'Find the right conversation partner effortlessly.'
  },
  {
    icon: imgIcon3,
    title: 'Fun & Interactive:',
    desc: 'Use our special tools to enhance your chats.'
  }
];
export const Email = '<EMAIL>';
export const CacheName = `${Subanm}_user_info`;
export const PkgMap = {
  prod: 'kolstar.online.live',
  test: 'kolstar.online.live.dev',
  local: 'kolstar.online.live.dev'
};

export const firebaseConfig: Record<string, FirebaseConfigType> = {
  [PkgMap.prod]: {
    projectId: 'kolstar-86e03',
    authDomain: 'kolstar-86e03.firebaseapp.com',
    storageBucket: 'kolstar-86e03.firebasestorage.app',
    messagingSenderId: '274083833427',
    appId: '1:274083833427:android:ba8b8809d9ebaaddcb48a6',
    apiKey: 'AIzaSyANqHIO42M4pGMyQ3aBgzalccHiWXdIinM'
  },
  [PkgMap.test]: {
    projectId: 'kolstar-86e03',
    authDomain: 'kolstar-86e03.firebaseapp.com',
    storageBucket: 'kolstar-86e03.firebasestorage.app',
    messagingSenderId: '274083833427',
    appId: '1:274083833427:android:761533bb2ab32daecb48a6',
    apiKey: 'AIzaSyANqHIO42M4pGMyQ3aBgzalccHiWXdIinM'
  }
};
