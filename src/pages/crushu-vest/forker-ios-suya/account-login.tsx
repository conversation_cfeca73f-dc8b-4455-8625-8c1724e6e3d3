import { IOSAccountLogin } from '@/components/account-login-lagecy/login-ios-pb';
import { formatIOSVestPageConfig, getEnv } from '@/modules/utils';
export default function Index() {
  const env = getEnv();
  const { GradientColors, baseUrl, Name, subanm, pkg, appleConfig } = formatIOSVestPageConfig();
  return (
    <IOSAccountLogin
      appName={Name}
      gradient={`linear-gradient(90deg, ${GradientColors[0]} 0%, ${GradientColors[1]} 100%)`}
      baseUrl={baseUrl}
      pkg={pkg}
      anm={'cu'}
      subanm={subanm}
      env={env}
      appleConfig={appleConfig}
    />
  );
}
