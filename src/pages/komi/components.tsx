import LevelA from '@/assets/komi/badge/level_A.webp';
import LevelAx from '@/assets/komi/badge/<EMAIL>';
import LevelB from '@/assets/komi/badge/level_B.webp';
import LevelBx from '@/assets/komi/badge/<EMAIL>';
import LevelC from '@/assets/komi/badge/level_C.webp';
import LevelCx from '@/assets/komi/badge/<EMAIL>';
import LevelN from '@/assets/komi/badge/level_N.webp';
import LevelNx from '@/assets/komi/badge/<EMAIL>';
import LevelS from '@/assets/komi/badge/level_S.webp';
import LevelSx from '@/assets/komi/badge/<EMAIL>';
import { callHandler } from '@/modules/bridge';
import { CheckOne } from '@icon-park/react';
import classNames from 'classnames';
import { Picker, PickerProps, Popup } from 'react-vant';
import './components.scss';

const LevelBadge = {
  N: LevelN,
  C: LevelC,
  B: LevelB,
  A: LevelA,
  S: LevelS,
  Nx: LevelNx,
  Cx: LevelCx,
  Bx: LevelBx,
  Ax: LevelAx,
  Sx: LevelSx
};

export function Level({
  level,
  size = 'small',
  className = 'h-70px w-70px'
}: {
  level: string;
  className?: string;
  size?: 'small' | 'big';
}) {
  return (
    <span
      className={classNames('inline-block rounded-full align-middle', className)}
      style={{ background: `url(${LevelBadge[size === 'big' ? `${level}x` : level]}) no-repeat center/contain` }}
    />
  );
}

export function Point({ className }: { className?: string }) {
  return (
    <span
      className={classNames(
        'min-w-20px min-h-20px inline-block bg-[url(@/assets/icons/icon-point.png)] bg-contain bg-center bg-no-repeat align-middle',
        className
      )}
    />
  );
}

export function KomiVantPicker(props: PickerProps) {
  return (
    <Picker
      className="price-picker"
      cancelButtonText={
        <div className="h-48px w-48px bg-[url(@/assets/icons/icon-back-black.png)] bg-contain bg-center bg-no-repeat" />
      }
      confirmButtonText="OK"
      popup={{ round: true, closeOnClickOverlay: true }}
      {...props}
    />
  );
}

export const jumpToWithdrawHis = () => {
  const env = window.__INITIAL_STATE__.env;
  callHandler('openUrl', {
    url: `komi://www.komiapp.live?page=web&url=${env === 'prod' ? 'https://www.komiapp.live' : 'http://test.komiapp.live'}/withdraw-history`
  });
};

export function KomiPicker({
  options,
  value,
  onChange,
  title = 'Select an option',
  visible,
  onClose
}: {
  options: { value: string | number | null; text: string }[];
  value: string;
  onChange: (value: string | number | null) => void;
  title?: string;
  visible: boolean;
  onClose: () => void;
}) {
  return (
    <Popup visible={visible} round position="bottom" onClose={onClose}>
      <div className="p-24px">
        <div className="text-40px mb-16px text-center font-medium">{title}</div>
        <div className="max-h-60vh overflow-auto">
          {options.map(option => (
            <div
              key={option.value!}
              className={classNames(
                'py-32px border-#eee text-32px flex-between b-b last:b-none font-medium',
                value === option.value ? 'text-#914DFF' : 'text-#333'
              )}
              onClick={() => {
                onChange(option.value);
                onClose();
              }}
            >
              <span>{option.text}</span>
              {value === option.value && <CheckOne theme="filled" size={24} fill="#914DFF" />}
            </div>
          ))}
        </div>
      </div>
    </Popup>
  );
}
