import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { getQueryParams } from '@/modules/utils';
import classNames from 'classnames';
import { Point } from '../components';

const RankBadge = {
  1: 'bg-[url(@/assets/komi/iv-icon-rank-1.png)]',
  2: 'bg-[url(@/assets/komi/iv-icon-rank-2.png)]',
  3: 'bg-[url(@/assets/komi/iv-icon-rank-3.png)]'
};

const RankingList = [
  {
    avatar: 'https://res.crushu.net/crushu/user/avatar/12/01/90531201.jpg?x-oss-process=style/hq',
    nickname: '🥰😍Aarohi🥰😘🥰😍',
    point: '3835554'
  },
  {
    avatar: 'https://res.crushu.net/crushu/user/avatar/10/61/21631061.jpg?x-oss-process=style/hq',
    nickname: '🌼🇮🇳pinky❤🧚‍♂🧚‍♂',
    point: '3224709'
  },
  {
    avatar: 'https://res.crushu.net/crushu/user/avatar/31/64/43553164.jpg?x-oss-process=style/hq',
    nickname: '😘S🔴NIYA🥰🥰...',
    point: '2810896'
  },
  {
    avatar: 'https://res.crushu.net/crushu/user/avatar/81/67/81768167.jpg?x-oss-process=style/hq',
    nickname: '💞🧿 soundarya 🧿💞',
    point: '1711247'
  },
  {
    avatar: 'https://res.crushu.net/crushu/user/avatar/74/57/24117457.jpg?x-oss-process=style/hq',
    nickname: 'Honey❤️🤙 🍯',
    point: '1402479'
  }
];

const link = 'https://api.komiapp.live/api/dev/redirect/redirect?pkg_name=com.komi.app&pub=komi_official&subpub=';

export default function Inviting() {
  const qs = getQueryParams();
  const contact = hexToDec(qs.c).toString().replace('.', '-');
  const handleDownload = () => {
    const subpub = JSON.stringify({ invite_code: qs.code });
    const url = `${link}${window.btoa(subpub)}`;
    location.assign(url);
  };
  return (
    <PageWrapper title="Inviting">
      <div className="page bg-#1F1147 pt-80px flex-base flex-col bg-[url(@/assets/komi/iv-bg.jpg)] bg-contain bg-top bg-no-repeat">
        <div
          className={classNames(
            'via-#FEED97 from-#FFFDFB to-#D99B2E bg-gradient-to-b bg-clip-text text-center font-semibold text-transparent',
            qs.n.length > 16 ? 'text-50px' : qs.n.length > 10 ? 'text-70px' : 'text-100px'
          )}
        >
          {qs.n}
        </div>
        <p className="text-30px mb-20px text-white">Invite you to be the anchor of Komi</p>
        <p className="text-24px text-#AA9EF1 flex-base gap-8px">
          My Invitation Code: {qs.code} <CopyIcon content={qs.code} />
        </p>
        <div
          className="w-61.3% mt-50px flex-center text-#F1E9F2 text-34px animate-breathing aspect-[442/79] bg-[url(@/assets/komi/iv-btn.png)] bg-contain bg-center bg-no-repeat"
          onClick={handleDownload}
        >
          Download Komi Now
        </div>
        <p className="mt-57px text-32px text-#E6D2A6 mb-28px font-bold">Last week anchor lncome ranking</p>
        {/* Ranking */}
        <div className="pt-32px px-18px w-95% aspect-[681/679] bg-[url(@/assets/komi/iv-frame-1.png)] bg-contain bg-center bg-no-repeat">
          {RankingList.map((item, i) => (
            <div className="mb-14px b b-#4F30AC bg-#391B65 rounded-8px py-16px px-26px flex-between gap-20px" key={i}>
              {i <= 2 ? (
                <div className={classNames(RankBadge[i + 1], 'w-50px h-60px bg-contain bg-center bg-no-repeat')}></div>
              ) : (
                <div className="text-#A99BCF text-28px w-50px h-60px flex-center font-bold">{i + 1}</div>
              )}
              <div className="avatar w-80px h-80px overflow-hidden rounded-full">
                <img src={item.avatar} className="w-full object-cover" />
              </div>
              <div className="text-#D3C5FA text-30px flex-1 overflow-hidden text-ellipsis whitespace-nowrap">
                {item.nickname}
              </div>
              <div className="w-160px flex-base gap-12px">
                <Point className="w-34px h-34px shrink-0" />
                <span className="text-#FDE375 text-30px">{item.point}</span>
              </div>
            </div>
          ))}
        </div>
        {/* Income Info */}
        <div className="mt-160px px-46px w-95% pt-80px relative aspect-[681/418] bg-[url(@/assets/komi/iv-frame-2.png)] bg-contain bg-center bg-no-repeat">
          <div className="-top-80px left-130px pt-60px text-30px w-63.75% absolute aspect-[459/118] bg-[url(@/assets/komi/iv-frame-title.png)] bg-contain bg-center bg-no-repeat text-center text-white">
            Income
          </div>
          <p className="text-#AA9EF1 text-28px mb-12px">1. 💰Competitive price per unit</p>
          <p className="text-#AA9EF1 text-28px mb-12px">2. 📞 / 🎁 / 📩: 70%</p>
          <p className="text-#AA9EF1 text-28px mb-12px">3. 🆕 Majority calls: 100000+ new users/day</p>
          <p className="text-#AA9EF1 text-28px mb-12px">4. 🎉 More ways to earn money</p>
          <p className="text-#AA9EF1 text-28px mb-12px">5. Calls + Gift Income + Chat Income</p>
          <p className="text-#AA9EF1 text-28px mb-12px">6. Mission Rewards + Activity Rewards</p>
        </div>
        <div className="my-60px flex-center gap-8px text-white/40">
          <span>Agency WhatsApp {contact}</span> <CopyIcon fill="#ffffff66" content={contact} />
        </div>
      </div>
      <style lang="css">{`
        @keyframes breathing {
          0%,
          100% {
            transform: scale(1);
          }
          50% {
            transform: scale(1.1);
          }
        }
        .animate-breathing {
          animation: breathing 0.75s ease-in-out infinite;
          cursor: pointer;
        }
      `}</style>
    </PageWrapper>
  );
}

/**
 * 将36进制（包括小数）转换为10进制数
 * @param hex 36进制的字符串，可能包含小数点
 * @returns 转换后的10进制数
 */
export function hexToDec(hex: string): number {
  const [intPart, fracPart = ''] = hex.split('.');

  // 转换整数部分
  const intValue = parseInt(intPart, 36);

  // 转换小数部分
  let fracValue = 0;
  for (let i = 0; i < fracPart.length; i++) {
    fracValue += parseInt(fracPart[i], 36) * Math.pow(36, -(i + 1));
  }

  return intValue + fracValue;
}
