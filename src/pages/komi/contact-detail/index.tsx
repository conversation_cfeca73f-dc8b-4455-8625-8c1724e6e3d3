import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { BZ } from '@/proto';
import { useEffect, useState } from 'react';

export default function PageName() {
  const t = useLocale({
    en: {
      title: 'Contact Details',
      subTitle: 'Security assurance,please use with confidence.',
      desc: 'Security assurance,please use with confidence. Contact information will not be disclosed to others. Adding contact details is for convenience in case of work-related issues,allowing you to reach official customer service at any time.'
    },
    hi: {
      title: 'संपर्क विवरण',
      subTitle: 'सुरक्षा आश्वासन, कृपया विश्वास के साथ उपयोग करें।',
      desc: 'सुरक्षा आश्वासन, कृपया विश्वास के साथ उपयोग करें। संपर्क जानकारी दूसरों को नहीं दी जाएगी। कार्य से संबंधित मुद्दों के मामले में सुविधा के लिए संपर्क विवरण जोड़ना, आपको किसी भी समय आधिकारिक ग्राहक सेवा तक पहुंचने की अनुमति देता है।'
    }
  });
  const userInfo = useUserInfo();
  const [data, setData] = useState({ whatsapp: '', google_mail: '' });

  useEffect(() => {
    if (!userInfo) {
      return;
    }
    BZ.anchor.getMyContactInfo.request({}).then(res => {
      console.log(res);
      setData(res);
    });
  }, [userInfo]);

  return (
    <PageWrapper title={t('title')}>
      <div className="page px-32px bg-#F4F4FB bg-[url(@/assets/komi/agent-info-bg.jpg)] bg-contain bg-top bg-no-repeat">
        {/* <Header title={t('title')} close /> */}
        <div className="mt-30px mb-50px flex-between gap-30px">
          <div className="w-104px h-130px shrink-0 bg-[url(@/assets/komi/icon-secure.png)] bg-contain bg-center bg-no-repeat" />
          <div className="text-#121212 text-32px font-medium">{t('subTitle')}</div>
        </div>
        {/* Desc */}
        <div className="text-24px text-#85818b leading-40px">{t('desc')}</div>
        <div className="mt-60px">
          <div className="h-42px ps-60px text-#121212 text-32px leading-44px bg-[url(@/assets/komi/icon-whatsapp-black.png)] bg-contain bg-left bg-no-repeat font-medium">
            Whatsapp
          </div>
          <div className="w-588px h-80px ps-50px text-#121212 text-32px flex-base mt-30px rounded-full bg-white">
            {data.whatsapp}
          </div>
        </div>
        <div className="mt-60px">
          <div className="h-42px ps-60px text-#121212 text-32px leading-44px bg-[url(@/assets/komi/icon-gmail-black.png)] bg-contain bg-left bg-no-repeat font-medium">
            Google Mail
          </div>
          <div className="w-588px h-80px ps-50px text-#121212 text-32px flex-base mt-30px rounded-full bg-white">
            {data.google_mail}
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
