import JsBridgeRegister from '@/components/js-bridge-register';
import { useJsbridgeReady, useUserInfo } from '@/hooks';
import { closeView, report } from '@/modules/bridge';
import { getQueryParams } from '@/modules/utils';
import classNames from 'classnames';
import { useEffect } from 'react';
import { hooks } from 'react-vant';
import { Level } from '../components';

export default function TinyAmount() {
  const qs = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  useEffect(() => {
    if (userInfo && isReady) {
      report('popup_page_show', { type: 'anchor-level-change', ...qs, uid: userInfo.uid });
    }
  }, [userInfo, isReady]);

  const { current } = hooks.useCountDown({
    time: 3 * 1000,
    autostart: true
  });
  const close = () => {
    if (current.seconds !== 0) return;
    report('popup_page_close', { type: 'anchor-level-change', ...qs, uid: userInfo?.uid });
    closeView(6);
  };

  return (
    <>
      <JsBridgeRegister />
      <div className="page flex-center relative flex-col bg-black/70" onClick={close}>
        <div className="w-90vw relative flex flex-col items-center leading-snug" onClick={e => e.stopPropagation()}>
          <h1
            className={classNames(
              qs.action === 'up' ? 'text-#ffecbc' : 'text-#8ea6e4',
              'text-64px mb-48px font-bold capitalize'
            )}
          >
            Level {qs.action}
          </h1>
          <Level level={qs.level} className="w-342px h-316px" size="big" />
          {qs.action === 'up' && (
            <div className="top-40% w-85% absolute aspect-[310/115] bg-[url(@/assets/komi/gift-ss.png)] bg-contain bg-center bg-no-repeat" />
          )}
        </div>
      </div>
    </>
  );
}
