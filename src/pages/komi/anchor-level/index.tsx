import GoldCoin from '@/assets/icons/icon-point.png';
import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { Table } from '@/components/table';
import { useLocale, useUserInfo } from '@/hooks';
import { report } from '@/modules/bridge';
import { formatAvatatUrl, getEnv } from '@/modules/utils';
import { BZ } from '@/proto';
import { AnchorLevelConfig } from '@/proto/api/anchor';
import { useEffect, useState } from 'react';
import { Level } from '../components';
import GroupArrows from './images/arrows.png';
import BackgroundGradient from './images/bg.jpg';
import PhoneIcon from './images/icon-call.png';
import locale from './locale';

export default function AnchorLevel() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();

  const [tierList, setTierList] = useState<AnchorLevelConfig[]>([]);
  const [data, setData] = useState({
    currentLevel: 'N',
    nextLevel: 'C',
    profits: 0,
    profitsPoint: 0,
    profitGoal: 200000,
    callDuration: 0,
    callDurationPoint: 0,
    callDurationGoal: 0
  });
  useEffect(() => {
    if (!userInfo) {
      return;
    }
    Promise.all([BZ.anchor.getAnchorLevelConfig.request({}), BZ.anchor.getAnchorLevelInfo.request({})])
      .then(([config, info]) => {
        console.log(config, info);
        // info.current_level = 'B';
        const currentLevelIndex = config.config.findIndex(conf => conf.grade === info.current_level);
        const currentLevel = config.config[currentLevelIndex];
        let nextLevel = config.config[currentLevelIndex + 1];
        if (!nextLevel) {
          nextLevel = config.config[currentLevelIndex];
        }
        setData({
          currentLevel: info.current_level,
          nextLevel: nextLevel?.grade || 'S',
          profits: info.profit || 0,
          profitsPoint: currentLevel?.min_point || 0,
          profitGoal: nextLevel?.min_point || 0,
          callDuration: info.chat_avg_time || 0,
          callDurationPoint: currentLevel?.chat_avg_time || 0,
          callDurationGoal: nextLevel?.chat_avg_time || 0
        });
        setTierList(config.config.filter(e => e.grade !== 'N'));
        report('mine_anchor_level_page', { anchorlevel: info.current_level });
      })
      .catch(err => {
        console.error(err);
      });
  }, [userInfo]);

  const isTopLevel = data.currentLevel === data.nextLevel;

  return (
    <PageWrapper title={t('title')}>
      <div
        className="px-32px pt-72px pb-50px relative min-h-screen"
        style={{ backgroundImage: `url(${BackgroundGradient})`, backgroundSize: 'cover' }}
      >
        {/* User Profile */}
        <div className="gap-44px flex items-center">
          <div className="h-120px w-120px overflow-hidden rounded-full border-[4px]">
            <img
              src={formatAvatatUrl(userInfo?.uid, getEnv())}
              className="h-full w-full object-cover"
              onError={(e: any) => {
                e.target.src = `https://res.komiapp.live/common/user/avatar/default/1_anchor_default_head.png`;
              }}
            />
          </div>
          <div>
            <h2 className="mt-[16px] text-[36px] font-bold">{userInfo?.nickname}</h2>
            <div className="mt-[8px] flex items-center">
              <span className="text-#A5A4A8 text-[28px]">ID:{userInfo?.uid}</span>
              <CopyIcon className="text-#A5A4A8 ms-16px" size="0.28rem" content={userInfo?.uid} />
            </div>
          </div>
        </div>

        {/* Level Progress */}
        <div className="">
          <div
            className="ps-30px pe-34px py-9px mb-62px mt-44px flex-center gap-32px w-full rounded-full"
            style={{ background: 'linear-gradient( 90deg, #4E81FE 0%, #BF46FF 36%, #F44DBB 72%, #FF7A7A 100%)' }}
          >
            <div className="text-[28px] font-medium text-white">
              {t('myCurrentLevel')} <span className="text-36px font-bold">{data.currentLevel}</span>
            </div>
            {!isTopLevel && data.currentLevel !== 'N' && (
              <>
                <div className="flex items-center">
                  <img src={GroupArrows} alt="Arrows" className="mx-[12px] h-[38px] w-[84px]" />
                </div>
                <div className="text-[28px] font-medium text-white">
                  {t('nextLevel')} {data.nextLevel}
                </div>
              </>
            )}
          </div>
        </div>

        {/* Upgrade Requirements Card */}
        <div className="mb-60px rounded-40px pt-44px pb-56px px-32px bg-white shadow-lg">
          <h3 className="pb-24px text-#121212 b-b border-#979797/10 text-32px font-bold">{t('howToUpgrade')}</h3>
          {/* 大于N级 */}
          {data.currentLevel === 'N' ? (
            <>
              <p className="my-12px text-24px text-#A5A4A8 font-medium">{t('anchorUpgradeDescription')}</p>
              <Table
                className="[&_.table-row-wrap_.table-item]:text-28px [&_.table-row-wrap_.table-item]:text-#121212 [&_.table-item]:font-bold"
                widths={['20%', '45%', '35%']}
                header={{
                  cells: [t('th_level'), t('th_14Income'), t('th_avgCallDuration')],
                  className: '!bg-#9273F2 text-white !text-24px font-400 whitespace-pre-wrap',
                  style: undefined
                }}
                data={tierList.map(item => ({
                  cells: [
                    item.grade,
                    <span className="text-#FF8200">
                      {item.min_point > 0 ? `$${item.min_point / 10000}≤` : ''}Income
                      {item.max_point > 0 ? `<$${item.max_point / 10000}` : ''}
                    </span>,
                    <span className="text-#FF8200">{item.chat_avg_time === 0 ? '/' : item.chat_avg_time + 's'}</span>
                  ]
                }))}
              />
            </>
          ) : (
            <>
              {/* Profits Section */}
              <div className="mb-24px pb-24px">
                <p className="text-#121212 mb-16px text-24px pt-16px font-medium">{t('profitsEarned')}</p>
                <div className="flex-base mb-[16px]">
                  <img src={GoldCoin} alt="Gold coin" className="mr-[12px] h-[46px] w-[46px]" />
                  <span className="text-32px font-bold text-[#FF8200]">{data.profits}</span>
                </div>

                {/* Progress Bar */}
                <LevelProgress
                  targetLevel={data.nextLevel}
                  currentLevel={data.currentLevel}
                  process={(data.profits - data.profitsPoint) / (data.profitGoal - data.profitsPoint)}
                  colors={['#FFCC5E', '#FF523B']}
                />

                <div className="flex-base justify-end">
                  {!isTopLevel && (
                    <div className="me-11% w-160px flex-center">
                      <img src={GoldCoin} alt="Gold coin" className="mr-[8px] h-[36px] w-[36px]" />
                      <span className="text-#A5A4A8 text-[28px]">{data.profitsPoint}</span>
                    </div>
                  )}
                  <div className="flex-center w-160px -me-30px">
                    <img src={GoldCoin} alt="Gold coin" className="mr-[8px] h-[36px] w-[36px]" />
                    <span className="text-[28px] text-[#FF8200]">{data.profitGoal}</span>
                  </div>
                </div>
              </div>

              {/* Call Duration Section */}
              <div>
                <p className="border-#979797/10 pt-26px mb-16px text-24px text-#121212 border-t font-medium">
                  {t('profitsEarned')}
                </p>
                <div className="flex-base mb-[16px]">
                  <img src={PhoneIcon} alt="call" className="mr-[12px] h-[46px] w-[46px]" />
                  <span className="text-32px font-bold text-[#617BFF]">{data.callDuration}s</span>
                </div>

                {/* Progress Bar */}
                <LevelProgress
                  targetLevel={data.nextLevel}
                  currentLevel={data.currentLevel}
                  process={
                    (data.callDuration - data.callDurationPoint) / (data.callDurationGoal - data.callDurationPoint)
                  }
                  colors={['#4E80FE', '#C045FD']}
                />

                <div className="flex-base text-28px justify-end">
                  {!isTopLevel && (
                    <div className="me-11% w-160px flex-center">
                      <img src={PhoneIcon} alt="Call" className="mr-[8px] h-[36px] w-[36px]" />
                      <span className="text-#A5A4A8">{data.callDurationPoint}</span>
                    </div>
                  )}
                  <div className="flex-center w-160px -me-30px">
                    <img src={PhoneIcon} alt="Call" className="mr-[8px] h-[36px] w-[36px]" />
                    <span className="text-[#4E80FE]">{data.callDurationGoal}</span>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Rules Section */}
        <div className="ms-10px text-#a5a4a8 text-24px leading-32px">
          <h3 className="text-28px mb-28px font-bold">{t('anchorLevelRules')}</h3>
          <p className="">{t('rule1')}</p>
          <p className="">{t('rule2')}</p>
          <p className="">{t('rule3')}</p>
          <p className="">{t('rule4')}</p>
          <p className="">{t('rule5')}</p>
        </div>
      </div>
    </PageWrapper>
  );
}

type LevelProps = {
  targetLevel: string;
  currentLevel: string;
  /** 0-1  */
  process: number;
  colors: [string, string];
};
function LevelProgress({ targetLevel, currentLevel, process, colors }: LevelProps) {
  const isTopLevel = targetLevel === currentLevel;
  process = Math.max(0, Math.min(1, process));
  return (
    <div
      className="py-20px h-60px relative"
      style={{
        // @ts-expect-error 忽略自定义css变量
        '--f': colors[0],
        '--t': colors[1],
        '--process': `calc(60% + ${isTopLevel ? 30 : (isNaN(process) ? 0 : process) * 30}%)`
      }}
    >
      <div className="h-16px bg-#D9CEFF absolute w-[calc(100%-50px)] rounded-full"></div>
      <div className="h-16px duration-350 absolute w-[--process] rounded-full bg-gradient-to-r from-[--f] to-[--t] transition-all"></div>
      <div className="-top-8px absolute right-0">
        <Level level={targetLevel} />
      </div>
      {!isTopLevel && (
        <div className="-top-4px left-51% absolute">
          <Level level={currentLevel} className="w-60px h-60px" />
        </div>
      )}
    </div>
  );
}
