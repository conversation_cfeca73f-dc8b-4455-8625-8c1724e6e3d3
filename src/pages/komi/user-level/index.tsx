import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { getQueryParams } from '@/modules/utils';
import { useEffect, useState } from 'react';
import locale from './locale';

export default function UserLevel() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  // 模拟用户等级数据
  const [userLevel] = useState({
    currentLevel: 33,
    nextLevel: 1,
    progress: 12.0,
    current: 0,
    total: 5,
    avatar: 'https://via.placeholder.com/80x80' // 占位符头像
  });

  useEffect(() => {
    /* if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    } */
  }, [userInfo, isReady]);

  const upgradeItems = [
    {
      icon: '🎥',
      title: t('videoChat'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '💬',
      title: t('replyMessage'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🎁',
      title: t('receiveGifts'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🖼️',
      title: t('unlockPictures'),
      description: t('expDescription', { points: 300, exp: 1 })
    }
  ];

  return (
    <PageWrapper title={t('title')}>
      <div className="min-h-screen bg-gradient-to-b from-amber-900 via-amber-800 to-amber-900 text-white">
        {/* 用户等级进度区域 */}
        <div className="px-48px pb-48px pt-64px relative">
          {/* 进度圆环 */}
          <div className="h-512px w-512px relative mx-auto flex items-center justify-center">
            {/* 外圆环 - 橙色渐变 */}
            <div className="border-8px absolute inset-0 rounded-full border-orange-500 opacity-30"></div>

            {/* 进度圆环 */}
            <div
              className="border-8px absolute inset-0 rounded-full border-transparent"
              style={{
                background: `conic-gradient(from 0deg, #ff6b35 0%, #ff6b35 ${userLevel.progress * 3.6}deg, transparent ${userLevel.progress * 3.6}deg)`,
                borderRadius: '50%'
              }}
            ></div>

            {/* 用户头像 */}
            <div className="relative z-10">
              <div
                className="h-160px w-160px border-4px rounded-full border-white bg-cover bg-center"
                style={{ backgroundImage: `url(${userLevel.avatar})` }}
              ></div>

              {/* 等级标签 */}
              <div className="-bottom-16px absolute left-1/2 -translate-x-1/2 transform">
                <div className="px-24px py-8px text-28px rounded-full bg-gradient-to-r from-yellow-400 to-orange-400 font-bold text-black">
                  {t('currentLevel', { level: userLevel.currentLevel })}
                </div>
              </div>
            </div>

            {/* 进度百分比 */}
            <div className="left-64px top-32px px-16px py-8px text-24px absolute rounded-full bg-white font-bold text-orange-500">
              {userLevel.progress}%
            </div>

            {/* 进度数值 */}
            <div className="bottom-64px left-64px text-24px absolute text-white/70">
              {t('progress', { current: userLevel.current, total: userLevel.total })}
            </div>

            {/* 下一级别 */}
            <div className="bottom-64px right-64px text-24px absolute text-white/70">
              {t('nextLevel', { level: userLevel.nextLevel })}
            </div>
          </div>
        </div>

        {/* 等级奖励卡片 */}
        <div className="mb-48px px-48px">
          <div className="rounded-32px p-48px border border-white/10 bg-black/30 backdrop-blur-sm">
            <div className="text-center">
              <div className="mb-32px gap-16px flex items-center justify-center">
                <span className="text-48px text-yellow-400">⭐</span>
                <h3 className="text-36px font-bold">{t('levelRewards', { level: 1 })}</h3>
                <span className="text-48px text-yellow-400">⭐</span>
              </div>

              {/* 礼品盒图标区域 */}
              <div className="mb-48px relative">
                <div className="h-256px w-256px rounded-32px text-120px mx-auto flex items-center justify-center bg-gradient-to-br from-red-500 to-red-600">
                  🎁
                </div>
                {/* 装饰性元素 */}
                <div className="text-48px absolute left-1/4 top-0 animate-bounce text-yellow-400">💰</div>
                <div className="top-32px text-40px absolute right-1/4 animate-pulse text-green-400">💚</div>
                <div className="bottom-32px text-36px absolute left-1/3 animate-bounce text-blue-400 delay-300">💎</div>
              </div>

              <button className="px-48px py-24px text-36px w-full rounded-full bg-gradient-to-r from-orange-500 to-yellow-500 font-bold text-black">
                {t('claim')}
              </button>
            </div>
          </div>
        </div>

        {/* 升级方式 */}
        <div className="px-48px">
          <div className="mb-32px gap-16px flex items-center">
            <div className="h-48px w-8px rounded bg-orange-500"></div>
            <h3 className="text-36px font-bold">{t('howToUpgrade')}</h3>
          </div>

          <div className="space-y-24px">
            {upgradeItems.map((item, index) => (
              <div
                key={index}
                className="gap-32px rounded-24px p-32px flex items-center border border-white/10 bg-black/20 backdrop-blur-sm"
              >
                <div className="h-96px w-96px rounded-24px text-48px flex items-center justify-center bg-gradient-to-br from-blue-500 to-purple-600">
                  {item.icon}
                </div>
                <div className="flex-1">
                  <h4 className="mb-8px text-32px font-bold text-white">{item.title}</h4>
                  <p className="text-24px text-white/70">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="h-64px"></div>
      </div>
    </PageWrapper>
  );
}
