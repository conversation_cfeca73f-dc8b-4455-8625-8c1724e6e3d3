import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { getQueryParams } from '@/modules/utils';
import { useEffect, useState } from 'react';
import locale from './locale';

export default function UserLevel() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  // 模拟用户等级数据
  const [userLevel] = useState({
    currentLevel: 33,
    nextLevel: 1,
    progress: 12.0,
    current: 0,
    total: 5,
    avatar: 'https://via.placeholder.com/80x80' // 占位符头像
  });

  useEffect(() => {
    /* if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    } */
  }, [userInfo, isReady]);

  const upgradeItems = [
    {
      icon: '🎥',
      title: t('videoChat'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '💬',
      title: t('replyMessage'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🎁',
      title: t('receiveGifts'),
      description: t('expDescription', { points: 300, exp: 1 })
    },
    {
      icon: '🖼️',
      title: t('unlockPictures'),
      description: t('expDescription', { points: 300, exp: 1 })
    }
  ];

  return (
    <PageWrapper title={t('title')}>
      <div className="min-h-screen bg-gradient-to-b from-amber-900 via-amber-800 to-amber-900 text-white">
        {/* 用户等级进度区域 */}
        <div className="relative px-6 pb-6 pt-8">
          {/* 进度圆环 */}
          <div className="relative mx-auto flex h-64 w-64 items-center justify-center">
            {/* 外圆环 - 橙色渐变 */}
            <div className="absolute inset-0 rounded-full border-4 border-orange-500 opacity-30"></div>

            {/* 进度圆环 */}
            <div
              className="absolute inset-0 rounded-full border-4 border-transparent"
              style={{
                background: `conic-gradient(from 0deg, #ff6b35 0%, #ff6b35 ${userLevel.progress * 3.6}deg, transparent ${userLevel.progress * 3.6}deg)`,
                borderRadius: '50%'
              }}
            ></div>

            {/* 用户头像 */}
            <div className="relative z-10">
              <div
                className="h-20 w-20 rounded-full border-2 border-white bg-cover bg-center"
                style={{ backgroundImage: `url(${userLevel.avatar})` }}
              ></div>

              {/* 等级标签 */}
              <div className="absolute -bottom-2 left-1/2 -translate-x-1/2 transform">
                <div className="rounded-full bg-gradient-to-r from-yellow-400 to-orange-400 px-3 py-1 text-sm font-bold text-black">
                  {t('currentLevel', { level: userLevel.currentLevel })}
                </div>
              </div>
            </div>

            {/* 进度百分比 */}
            <div className="absolute left-8 top-4 rounded-full bg-white px-2 py-1 text-sm font-bold text-orange-500">
              {userLevel.progress}%
            </div>

            {/* 进度数值 */}
            <div className="absolute bottom-8 left-8 text-sm text-white/70">
              {t('progress', { current: userLevel.current, total: userLevel.total })}
            </div>

            {/* 下一级别 */}
            <div className="absolute bottom-8 right-8 text-sm text-white/70">
              {t('nextLevel', { level: userLevel.nextLevel })}
            </div>
          </div>
        </div>

        {/* 等级奖励卡片 */}
        <div className="mb-6 px-6">
          <div className="rounded-2xl border border-white/10 bg-black/30 p-6 backdrop-blur-sm">
            <div className="text-center">
              <div className="mb-4 flex items-center justify-center gap-2">
                <span className="text-yellow-400">⭐</span>
                <h3 className="text-lg font-bold">{t('levelRewards', { level: 1 })}</h3>
                <span className="text-yellow-400">⭐</span>
              </div>

              {/* 礼品盒图标区域 */}
              <div className="relative mb-6">
                <div className="mx-auto flex h-32 w-32 items-center justify-center rounded-2xl bg-gradient-to-br from-red-500 to-red-600 text-6xl">
                  🎁
                </div>
                {/* 装饰性元素 */}
                <div className="absolute left-1/4 top-0 animate-bounce text-2xl text-yellow-400">💰</div>
                <div className="absolute right-1/4 top-4 animate-pulse text-xl text-green-400">💚</div>
                <div className="absolute bottom-4 left-1/3 animate-bounce text-lg text-blue-400 delay-300">💎</div>
              </div>

              <button className="w-full rounded-full bg-gradient-to-r from-orange-500 to-yellow-500 px-6 py-3 text-lg font-bold text-black">
                {t('claim')}
              </button>
            </div>
          </div>
        </div>

        {/* 升级方式 */}
        <div className="px-6">
          <div className="mb-4 flex items-center gap-2">
            <div className="h-6 w-1 rounded bg-orange-500"></div>
            <h3 className="text-lg font-bold">{t('howToUpgrade')}</h3>
          </div>

          <div className="space-y-3">
            {upgradeItems.map((item, index) => (
              <div
                key={index}
                className="flex items-center gap-4 rounded-xl border border-white/10 bg-black/20 p-4 backdrop-blur-sm"
              >
                <div className="flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 text-2xl">
                  {item.icon}
                </div>
                <div className="flex-1">
                  <h4 className="mb-1 font-bold text-white">{item.title}</h4>
                  <p className="text-sm text-white/70">{item.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="h-8"></div>
      </div>
    </PageWrapper>
  );
}
