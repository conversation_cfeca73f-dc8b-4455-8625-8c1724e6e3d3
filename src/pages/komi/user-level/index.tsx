import PageWrapper from '@/components/hybrid-page-wrap';
import { useJsbridgeReady, useLocale, useUserInfo } from '@/hooks';
import { report } from '@/modules/bridge';
import { getQueryParams } from '@/modules/utils';
import { useEffect } from 'react';
import locale from './locale';

export default function PageName() {
  const t = useLocale(locale);
  const queryParams = getQueryParams();
  const userInfo = useUserInfo();
  const isReady = useJsbridgeReady();

  useEffect(() => {
    if (userInfo && isReady) {
      report('popup_page_show', { type: '', ...queryParams, uid: userInfo.uid });
    }
  }, [userInfo, isReady]);

  return (
    <PageWrapper title={t('title')}>
      <div className="page"></div>
    </PageWrapper>
  );
}