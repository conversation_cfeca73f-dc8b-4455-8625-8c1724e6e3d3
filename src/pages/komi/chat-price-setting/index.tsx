import PageWrapper from '@/components/hybrid-page-wrap';
import { Table } from '@/components/table';
import { useLocale, useUserInfo } from '@/hooks';
import { report, setNavBar } from '@/modules/bridge';
import { BZ } from '@/proto';
import { EditTwo } from '@icon-park/react';
import { useEffect, useState } from 'react';
import { KomiPicker, Level } from '../components';
import locale from './locale';

export default function ChatPriceSetting() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();

  // 通话价格设置
  const [price, setPrice] = useState<number | string>(0);
  const [currentLevel, setCurrentLevel] = useState<string>('N');
  const [pickerVisible, setPickerVisible] = useState<boolean>(false);
  const [pickList, setPickList] = useState<any[]>([{ value: 800, text: '800/min' }]);
  const [tierList, setTierList] = useState<{ price: number; grade: string }[]>([]);
  const handlePickerConfirm = (v: string | number | null) => {
    if (!v) {
      if (!price || !pickList.find(item => item.value === price)) {
        setPrice(pickList[0].value!);
        updatePrice(Number(pickList[0].value!));
      }
      setPickerVisible(false);
      return;
    }
    setPickerVisible(false);
    setPrice(v);
    updatePrice(+v);
  };

  useEffect(() => {
    if (!userInfo) return;
    setNavBar({ title: t('title'), visible: 1 });

    BZ.anchor.getVideoCallIncome.request({}).then(info => {
      console.log(info);
      const pickerList: number[] = [];
      const tableList: (typeof tierList)[0][] = [];
      let currentPriceLevel = -1;
      // 最大可选择等级
      const maxSort = info.grade_info.find(c => c.grade === info.current_grade)?.sort || 1;
      info.grade_info.forEach((item, i) => {
        if (info.current_income === item.income && currentPriceLevel === -1) {
          currentPriceLevel = item.sort;
        }
        if (item.sort <= maxSort) {
          pickerList.push(item.income);
        }
        const grade = `${info.grade_info
          .slice(i)
          .map(v => v.grade)
          .join(' / ')}`;
        tableList.push({ grade, price: item.income });
      });
      setPickList(Array.from(new Set(pickerList)).map(v => ({ value: v, text: `${v}/min` })));
      setTierList(tableList);
      setCurrentLevel(info.current_grade || 'N');
      setPrice(info.current_income || 0);
      report('mine_anchor_price_page', { anchorlevel: info.current_grade, price: currentPriceLevel });
    });
  }, [userInfo]);

  const updatePrice = async (price: number) => {
    if (!price) return;
    const currentPriceLevel = tierList.findIndex(item => item.price === price) + 1;
    report('mine_anchor_price_page_edit_click', { anchorlevel: currentLevel, price: currentPriceLevel });
    await BZ.anchor.updateVideoCallIncome.request({ income: price });
  };

  return (
    <PageWrapper title={t('title')}>
      <div className="page pb-50px bg-gradient-to-b from-white to-[#f4f4fb]">
        {/* Panel */}
        <div className="pt-36px pb-76px bg-[url(@/assets/komi/price-panel-bg.jpg)] bg-contain bg-top bg-no-repeat">
          <p className="leading-2 text-30px text-center font-bold text-white/80">{t('panelTitle')}</p>
          <Price size="big" price={price} classNames="text-white py-24px" onEdit={() => setPickerVisible(true)} />
          <KomiPicker
            title={t('popupTitle')}
            visible={pickerVisible}
            value={price as string}
            options={pickList}
            onClose={() => setPickerVisible(false)}
            onChange={handlePickerConfirm}
          />
        </div>
        <div className="px-32px -mt-50px">
          <div className="rounded-40px px-26px py-40px bg-white shadow-lg">
            <h2 className="text-32px text-#ff8200 text-center font-bold">{t('levelTitle')}</h2>
            <p className="text-32px text-#121212 mt-30px mb-44px text-center font-medium">
              {t('myLevel')} - <Level level={currentLevel} />
            </p>
            {/* 价格表 Price Table */}
            <div className="">
              <Table
                className="[&_.table-row-wrap_.table-item]:text-32px [&_.table-row-wrap_.table-item]:text-#121212 [&_.table-item]:font-bold"
                header={{
                  cells: [t('level'), t('price')],
                  className: 'bg-[#FBB562] text-white',
                  style: undefined
                }}
                data={tierList.map(item => ({
                  cells: [
                    item.grade,
                    <Price size={'small'} price={item.price} classNames="inline-flex text-#FF8200 w-210px text-left" />
                  ]
                }))}
                widths={['50%', '50%']}
              />
            </div>
            {/* 附加说明文案 Additional Desc */}
            <p className="text-24px w-90% mt-32px text-#ff5c5c mx-auto text-center font-medium leading-tight">
              {t('levelDesc')}
            </p>
          </div>
          {/* 规则介绍 Rule Desc */}
          <div className="mt-42px text-#a5a4a8 text-24px leading-32px">
            <p className="text-28px mb-28px font-bold">{t('ruleDesc')}</p>
            <p>{t('rule1')}</p>
            <p>{t('rule2')}</p>
            <p>{t('rule3')}</p>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
function Price({
  size,
  price,
  classNames,
  onEdit
}: {
  size: 'small' | 'big';
  price: number | string;
  classNames?: string;
  onEdit?: () => void;
}) {
  return (
    <div className={`flex-center gap-12px ${classNames}`}>
      <div
        className={[
          'bg-[url(@/assets/icons/icon-point.png)] bg-contain bg-center bg-no-repeat',
          size === 'small' ? 'w-46px h-46px' : 'w-84px h-84px'
        ].join(' ')}
      />
      {/* <img src="" className={size === 'small' ? 'w-40px h-40px' : 'w-60px h-60px'} alt="point icon" /> */}
      <div className={[size === 'small' ? 'text-32px' : 'text-48px', 'font-bold'].join(' ')}>{price}/min</div>
      {onEdit && (
        <div className="p-10px rounded-full bg-black/20 leading-none">
          <EditTwo fill="#D8D8D8" size="14" onClick={onEdit} />
        </div>
      )}
    </div>
  );
}
