import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { callHandler } from '@/modules/bridge';
import { BZ } from '@/proto';
import { useEffect, useState } from 'react';

export default function TinyAmount() {
  const t = useLocale({
    en: {
      title: 'My Agent',
      agentName: 'Agent Name',
      btn: 'Message'
    },
    hi: {
      title: 'मेरा एजेंट',
      agentName: 'एजेंट का नाम',
      btn: 'संदेश'
    }
  });
  const userInfo = useUserInfo();
  const [data, setData] = useState({
    guild_avatar: '',
    guild_contact_info: '',
    guild_master_name: '',
    guild_name: '',
    guild_uid: ''
  });
  useEffect(() => {
    BZ.anchor.getMyGuildInfo.request({}).then(res => {
      console.log(res);
      setData(res);
    });
  }, [userInfo]);

  const handleMsg = () => {
    callHandler('openUrl', {
      url: `komi://www.komiapp.live/?page=chat&user_id=${data.guild_uid}`
    });
  };
  return (
    <PageWrapper title={t('title')}>
      <div className="page px-32px bg-#F4F4FB bg-[url(@/assets/komi/agent-info-bg.jpg)] bg-contain bg-top bg-no-repeat">
        {/* <Header title={t('title')} close /> */}
        {/* Agent Name */}
        <div className="mt40px text-center">
          <div className="text-#121212 text-60px font-bold">{data.guild_name}</div>
          <div className="text-#A5A4A8 text-32px mt-24px font-bold">{t('agentName')}</div>
        </div>
        {/* Frame */}
        <div className="mt-50px flex aspect-[328/432] flex-col items-center bg-[url(@/assets/komi/agent-info-frame.png)] bg-contain bg-top bg-no-repeat">
          <h3 className="text-#121212 text-44px pt-36px text-center font-bold">{t('title')}</h3>
          <div className="mt-110px mb-90px w-280px h-280px b-8px b-white overflow-hidden rounded-full">
            <img className="h-full w-full object-cover" src={data.guild_avatar} />
          </div>
          <div className="text-42px font-medium text-white">{data.guild_master_name}</div>
          <div className="text-28px mt-20px mb-34px font-medium text-white/50">ID: {data.guild_uid}</div>
          <div className="text-#121212 text-28px flex-between gap-24px px-36px py-12px rounded-full bg-white/20 font-medium text-white/80">
            <span className="w-48px h-48px bg-[url(@/assets/komi/icon-whatsapp-opa.png)] bg-contain bg-center bg-no-repeat" />
            <span className="flex-grow-1 text-28px">{data.guild_contact_info}</span>
            <span className="h-48px b-r b-4px b-white/80 w-0" />
            <CopyIcon content={data.guild_contact_info} size={16} />
          </div>
          {/* <div
            className="mt-60px text-40px flex-center gap-16px h-120px w-520px b-6px b-#fff text-#924DFF rounded-16px bg-white/80 font-bold"
            onClick={handleMsg}
          >
            <span className="w-40px h-40px bg-[url(@/assets/komi/icon-message.png)] bg-contain bg-center bg-no-repeat" />
            {t('btn')}
          </div> */}
        </div>
      </div>
    </PageWrapper>
  );
}
