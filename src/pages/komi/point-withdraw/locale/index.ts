import { Resource } from 'i18next';

export default {
  en: {
    title: 'Income Withdraw',
    detailsTitle: 'Income Details',
    history: 'History',
    cardTitle: 'Current income',
    details: 'Details',
    myData: 'My Data',
    peroidToday: 'Today',
    peroidYestoday: 'Yesterday',
    peroidWeek: 'This week',
    incomeTotal: 'Total income',
    incomeVideo: 'Video income',
    incomeChat: 'Chat income',
    incomeGift: 'Gift income',
    lastUpdateTime: 'Last update time: {{time}}',
    withdraw: 'Withdraw',
    withdrawal: 'Withdrawal',
    withdrawUSDTips: 'USD withdrawals will be charged a fee, which will be paid by you.',
    withdrawINRTips: 'No fees for INR withdrawl! Real-time settlement!',
    peroid: 'Peroid',
    category: 'Category',
    point: 'Point',
    inputHolder: 'Please enter {{name}}',
    accountInfo: 'Account Info',
    accountLabel: '{{name}} Account',
    nameLabel: '{{name}} Name',
    bankTransfer: 'Bank Transfer',
    phoneNumber: 'Phone number',
    bankAccountNumber: 'Bank Account Number',
    carholderName: 'Carholder name',
    unableWithdraw: 'Unable to withdraw',
    unableWithdrawContent:
      "Your new anchor data does not meet the requirements, the {{points}} points reward can't be withdrawn.",
    gotIt: 'Got it',
    name: 'Name',
    invalidIfsc: 'Invalid IFSC, please check',
    contactTitle: 'Contact information(Just for one)',
    contactPlaceholder: 'Please enter your contact account',
    next: 'Next',
    submitSuc: 'Successfully Submitted',
    submitFail: 'Failed Submitted',
    successTip: 'Waiting for data review, expected tp arrive at 72 hours',
    failTip: 'Please try again',
    failTip2: 'If you have any questions,please consult the official WhatsApp/Telegram account number',
    viewRecord: 'View the records',
    retry: 'Retry',
    noData: 'No data',
    cate_all: 'All',
    cate_video_call_point: 'Video Call',
    cate_chat_point: 'Chat',
    cate_gift_point: 'Receive gifts',
    cate_withdraw_dec_point: 'Withdrawal deduction',
    cate_withdraw_refund_point: 'Withdrawal refund',
    cate_violation_point: 'Violation',
    cate_other_point: 'Other'
  },
  hi: {
    title: 'आय निकासी',
    detailsTitle: 'आय विवरण',
    history: 'इतिहास',
    cardTitle: 'वर्तमान आय',
    details: 'विवरण',
    myData: 'मेरा डेटा',
    peroidToday: 'आज',
    peroidYestoday: 'कल',
    peroidWeek: 'इस सप्ताह',
    incomeTotal: 'आय कुल',
    incomeVideo: 'वीडियो आय',
    incomeChat: 'चैट आय',
    incomeGift: 'उपहार आय',
    lastUpdateTime: 'अंतिम अपडेट समय: {{time}}',
    withdraw: 'निकासी',
    withdrawal: 'निकासी',
    withdrawUSDTips: 'USD निकासी पर शुल्क लगाया जाएगा, जो आपके द्वारा भुगतान किया जाएगा।',
    withdrawINRTips: 'INR निकासी के लिए कोई शुल्क नहीं! रियल-टाइम निपटान!',
    peroid: 'अवधि',
    category: 'श्रेणी',
    point: 'अंक',
    inputHolder: 'कृपया {{name}} दर्ज करें',
    accountInfo: 'खाता जानकारी',
    accountLabel: '{{name}} खाता',
    nameLabel: '{{name}} नाम',
    bankTransfer: 'बैंक हस्तांतरण',
    phoneNumber: 'फोन नंबर',
    bankAccountNumber: 'बैंक खाता संख्या',
    carholderName: 'कार्डधारक का नाम',
    unableWithdraw: 'निकासी करने में असमर्थ',
    unableWithdrawContent:
      'आपका नया एंकर डेटा आवश्यकताओं को पूरा नहीं करता है, {{points}} अंक पुरस्कार निकाला नहीं जा सकता।',
    gotIt: 'समझ गया',
    name: 'नाम',
    invalidIfsc: 'अमान्य IFSC, कृपया जांचें',
    contactTitle: 'संपर्क जानकारी (केवल एक के लिए)',
    contactPlaceholder: 'कृपया अपना संपर्क खाता दर्ज करें',
    next: 'अगला',
    submitSuc: 'सफलतापूर्वक जमा किया गया',
    submitFail: 'जमा करने में विफल',
    successTip: 'डेटा समीक्षा की प्रतीक्षा कर रहा है, 72 घंटे में पहुंचने की उम्मीद है',
    failTip: 'कृपया पुनः प्रयास करें',
    failTip2: 'यदि आपके कोई प्रश्न हैं, तो कृपया आधिकारिक WhatsApp/Telegram खाता संख्या से परामर्श करें',
    viewRecord: 'रिकॉर्ड देखें',
    retry: 'पुनः प्रयास करें',
    noData: 'कोई डेटा नहीं',
    cate_all: 'सभी',
    cate_video_call_point: 'वीडियो कॉल',
    cate_chat_point: 'चैट',
    cate_gift_point: 'उपहार प्राप्त करें',
    cate_withdraw_dec_point: 'निकासी कटौती',
    cate_withdraw_refund_point: 'निकासी वापसी',
    cate_violation_point: 'उल्लंघन',
    cate_other_point: 'अन्य'
  }
} satisfies Resource;
