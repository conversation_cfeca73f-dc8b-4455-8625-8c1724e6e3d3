import IconBankTransfer from '@/assets/komi/icon_bankcard.png';
import IconEPAY from '@/assets/komi/icon_epay.png';
import IconGCash from '@/assets/komi/icon_gcash.png';
import IconPayPal from '@/assets/komi/icon_paypal.png';
import IconUPI from '@/assets/komi/icon_upi.png';
import IconUSDT from '@/assets/komi/icon_usdt.png';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { requestProxy } from '@/modules/request';
import { getQueryParams } from '@/modules/utils';
import { CheckSmall } from '@icon-park/react';
import classNames from 'classnames';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Form, Input, InputProps, Loading, Toast } from 'react-vant';
import { B64IconMap, WithdrawCurrenyEnum, WithdrawTypeEnum } from './constant';
import locale from './locale';

const WithdrawTypeMap = {
  [WithdrawTypeEnum.Paypal]: 'Paypal',
  [WithdrawTypeEnum.USDT]: 'USDT',
  [WithdrawTypeEnum.BankTransfer]: 'Bank Card',
  [WithdrawTypeEnum.UPI]: 'UPI',
  [WithdrawTypeEnum.EPAY]: 'Epay',
  [WithdrawTypeEnum.GCash]: 'Gcash'
};

const WithdrawTypeConfig = {
  [WithdrawCurrenyEnum.USD]: [WithdrawTypeEnum.USDT, WithdrawTypeEnum.Paypal, WithdrawTypeEnum.EPAY],
  [WithdrawCurrenyEnum.INR]: [WithdrawTypeEnum.BankTransfer],
  [WithdrawCurrenyEnum.PHP]: [WithdrawTypeEnum.GCash]
};

export default function WithdrawSubmit() {
  const baseUrl = window.__INITIAL_STATE__.appData.data?.proxyUrl || 'http://test-api.komiapp.live/api/komi';
  const t = useLocale(locale);
  const qs = getQueryParams();
  const navigate = useNavigate();
  const WithdrawInputConfigs: Partial<Record<WithdrawTypeEnum, FormItemProps[]>> = {
    [WithdrawTypeEnum.Paypal]: [
      {
        label: t('accountLabel', { name: 'Paypal' }),
        placeholder: t('inputHolder', { name: t('accountLabel', { name: 'Paypal' }) }),
        name: 'account'
      }
    ],
    [WithdrawTypeEnum.USDT]: [
      {
        label: t('accountLabel', { name: 'USDT' }),
        placeholder: t('inputHolder', { name: t('accountLabel', { name: 'USDT' }) }),
        name: 'account'
      }
    ],
    [WithdrawTypeEnum.EPAY]: [
      {
        label: t('accountLabel', { name: 'EPay' }),
        placeholder: t('inputHolder', { name: t('accountLabel', { name: 'EPay' }) }),
        name: 'account'
      },
      {
        label: t('nameLabel', { name: 'EPay' }),
        placeholder: t('inputHolder', { name: t('nameLabel', { name: 'EPay' }) }),
        name: 'name'
      }
    ],
    [WithdrawTypeEnum.GCash]: [
      {
        label: t('accountLabel', { name: 'GCash' }),
        placeholder: t('inputHolder', { name: t('accountLabel', { name: 'GCash' }) }),
        name: 'account'
      },
      {
        label: t('nameLabel', { name: 'GCash' }),
        placeholder: t('inputHolder', { name: t('nameLabel', { name: 'GCash' }) }),
        name: 'name'
      }
    ],
    [WithdrawTypeEnum.BankTransfer]: [
      {
        label: t('phoneNumber'),
        placeholder: t('inputHolder', { name: t('phoneNumber') }),
        prefix: '+91',
        name: 'phone'
      },
      { label: t('carholderName'), placeholder: t('inputHolder', { name: t('carholderName') }), name: 'cardholder' },
      {
        label: t('bankAccountNumber'),
        placeholder: t('inputHolder', { name: t('bankAccountNumber') }),
        name: 'payment_method'
      },
      { label: 'IFSC', placeholder: 'IFSC(e.x:XXXX012345X)', name: 'ifsc' }
    ]
  };
  const [currency, setCurrency] = useState<WithdrawCurrenyEnum>(
    (qs.wd_type as WithdrawCurrenyEnum) || WithdrawCurrenyEnum.USD
  );
  const [withdrawType, setWithdrawType] = useState<WithdrawTypeEnum>(
    qs.wd_type === WithdrawCurrenyEnum.INR
      ? WithdrawTypeEnum.BankTransfer
      : qs.wd_type === WithdrawCurrenyEnum.PHP
        ? WithdrawTypeEnum.GCash
        : WithdrawTypeEnum.USDT
  );
  const [contactType, setContactType] = useState('Whatsapp');
  const [contact, setContact] = useState('');
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onFinish = async values => {
    console.log(values);
    for (const key in values) {
      if (!values[key]) {
        Toast.info(t('inputHolder', { name: t('accountInfo') }));
        return;
      }
    }
    if (values.ifsc && !/^[A-Z]{4}0[0-9A-Z]{6}$/.test(values.ifsc)) {
      Toast.info(t('invalidIfsc'));
      return;
    }
    if (!contact) {
      Toast.info(t('contactPlaceholder'));
      return;
    }
    const data = { ...values, [contactType.toLowerCase()]: contact };
    if (data.phone) {
      data.phone = `+91${data.phone}`;
    }
    apply(data);
  };

  const apply = async (data: any) => {
    try {
      setLoading(true);
      const submitResult: any = await requestProxy(`${baseUrl}/withdraw/apply`, {
        wd_type: currency,
        withdraw_type: withdrawType,
        points: qs.points,
        amount: qs.amount,
        key: qs.key,
        ...data
      });
      console.log(submitResult);
      if (submitResult.status === 1) {
        Toast.success('apply success');
        navigate('../result/?status=suc', { replace: true });
      }
      return submitResult;
    } catch (error: any) {
      console.error('Apply error:', error);
      Toast.info(error.msg ?? 'apply failed');
      navigate('../result/?status=fail', { replace: true });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    setNavBar({ visible: 1, title: t('title'), left_btn_config: 0 });
  }, []);

  return (
    <PageWrapper title={t('title')}>
      <section className="px-32px page">
        {/* Withdraw info */}
        <div className="mt-20px text-28px flex-between py-20px font-medium">
          <div className="text-#121212">{t('withdrawal')}:</div>
          <div className="text-#cb4d2e text-right">
            <div>
              {qs.points} {t('point')}
            </div>
            <div>
              Arrival ≈ {qs.amount} {qs.wd_type}
            </div>
          </div>
        </div>
        {/* Withdraw Type */}
        <div className="b-t b-b b-#eee py-20px">
          <div className="text-28px leading-32px mb-20px">Type:</div>
          <div className="flex-between gap-row-20px flex-wrap">
            {WithdrawTypeConfig[currency].map(type => (
              <WithdrawItem
                type={type}
                checked={type === withdrawType}
                onClick={() => setWithdrawType(type)}
                key={type}
              />
            ))}
          </div>
        </div>
        {/* Form */}
        <div className="">
          <Form form={form} onFinish={onFinish}>
            {WithdrawInputConfigs[withdrawType]?.map(item => (
              <Form.Item noStyle key={item.name} name={item.name}>
                <FormItem key={item.name} {...item} />
              </Form.Item>
            ))}
          </Form>
        </div>
        <div className="pt-20px">
          <h3 className="text-28px text-#121212 font-medium">{t('contactTitle')}</h3>
          <div className="my-20px flex-base gap-16px">
            {['Whatsapp', 'Telegram'].map(item => (
              <div
                key={item}
                className={classNames(
                  'b h-62px flex-between gap-8px px-36px flex-shrink-0 rounded-full font-medium',
                  contactType === item ? 'bg-#914DFF b-none text-white' : 'text-#666'
                )}
                onClick={() => setContactType(item)}
              >
                {item}
              </div>
            ))}
          </div>
          <Input
            value={contact}
            className={classNames('h-82px px-16px text-28px bg-#f4f4f4 w-full rounded-sm')}
            placeholder={t('contactPlaceholder')}
            prefix={<img src={B64IconMap[contactType]} className="w-42px h-42px" />}
            onChange={e => setContact(e)}
          />
        </div>
        <div
          className={classNames(
            loading && 'brightness-90',
            'py-20px flex-center w-588px mt-50px text-36px from-#914dff to-#de58ff mx-auto rounded-full bg-gradient-to-r font-bold text-white active:brightness-90'
          )}
          onClick={() => !loading && form.submit()}
        >
          {t('next')} {loading && <Loading size={20} style={{ marginLeft: '10px' }} />}
        </div>
      </section>
    </PageWrapper>
  );
}

type WithdrawItemProps = {
  type: WithdrawTypeEnum;
  checked: boolean;
  onClick: () => void;
};
const WithdrawTypeIconMap = {
  [WithdrawTypeEnum.Paypal]: IconPayPal,
  [WithdrawTypeEnum.USDT]: IconUSDT,
  [WithdrawTypeEnum.BankTransfer]: IconBankTransfer,
  [WithdrawTypeEnum.UPI]: IconUPI,
  [WithdrawTypeEnum.EPAY]: IconEPAY,
  [WithdrawTypeEnum.GCash]: IconGCash
};
function WithdrawItem({ type, checked, onClick }: WithdrawItemProps) {
  return (
    <div
      className={classNames(
        `basis-333px b h-75px flex-between gap-8px px-20px flex-shrink-0 rounded-full`,
        checked ? 'b-#459cf0' : 'b-#ddd'
      )}
      onClick={onClick}
    >
      <img src={WithdrawTypeIconMap[type]} className="h-42px w-auto object-cover" />
      <span className="flex-1">{WithdrawTypeMap[type]}</span>
      <span
        className={classNames(
          'h-38px w-38px inline-flex items-center justify-center rounded-full',
          checked ? 'bg-#914DFF' : 'bg-#ddd'
        )}
      >
        <CheckSmall size="15" fill="#fff" />
      </span>
    </div>
  );
}

type FormItemProps = {
  label: string;
} & InputProps;
function FormItem({ label, className, ...props }: FormItemProps) {
  return (
    <div className="py-20px b-b b-#eee flex-base gap-16px text-#121212 font-medium">
      <label className="text-28px leading-32px w-fit flex-shrink-0">{label}</label>
      <Input className={classNames(className, 'h-82px px-16px text-28px bg-#f4f4f4 w-full rounded-sm')} {...props} />
    </div>
  );
}
