import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { getQueryParams } from '@/modules/utils';
import classNames from 'classnames';
import { useEffect } from 'react';
import { jumpToWithdrawHis } from '../components';
import { B64IconMap } from './constant';
import locale from './locale';

export default function WithdrawResult() {
  const t = useLocale(locale);
  const qs = getQueryParams();
  const suc = qs.status === 'suc';

  useEffect(() => {
    setNavBar({ visible: 1, title: t('title'), left_btn_config: 0 });
  }, []);

  return (
    <PageWrapper title={t('title')}>
      <div className="page flex-base flex-col text-center">
        <div className="pt-45% mb-100px">
          <div
            className={classNames(
              suc ? 'bg-[url(@/assets/komi/submit-success.png)]' : 'bg-[url(@/assets/komi/submit-failed.png)]',
              'w-230px h-230px mx-auto bg-contain bg-center bg-no-repeat'
            )}
          />
          <h2 className="text-42px text-#121212 font-bold">{t(suc ? 'submitSuc' : 'submitFail')}</h2>
          <p className="text-#666 text-28px w-600px">{t(suc ? 'successTip' : 'failTip')}</p>
        </div>
        <div
          className="from-#914dff to-#de58ff w-85% h-104px flex-center text-33px rounded-full bg-gradient-to-r font-bold text-white"
          onClick={() => {
            if (suc) {
              jumpToWithdrawHis();
            } else {
              history.back();
            }
          }}
        >
          {t(suc ? 'viewRecord' : 'retry')}
        </div>
        <div className={classNames('py-80px fixed bottom-0 w-full text-center', suc && 'hidden')}>
          <p className="text-#333 text-28px w-90% leading-1.2em mb-25px mx-auto">{t('failTip2')}</p>
          <p className="text-33px mb-16px flex-center gap-24px font-medium">
            <img src={B64IconMap.Whatsapp} className="w-42px h-42px" />
            <span>******-484-4759</span>
            <CopyIcon content="******-484-4759" size="18" fill="#666" />
          </p>
          <p className="text-33px mb-16px flex-center gap-24px font-medium">
            <img src={B64IconMap.Telegram} className="w-42px h-42px" />
            <span>******-484-4759</span>
            <CopyIcon content="******-484-4759" size="18" fill="#666" />
          </p>
        </div>
      </div>
    </PageWrapper>
  );
}
