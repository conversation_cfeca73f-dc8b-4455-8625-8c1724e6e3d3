import Pointlarge from '@/assets/komi/point-large.png';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { requestProxy } from '@/modules/request';
import { formatOptionsOfObj, formatUnix } from '@/modules/utils';
import { BZ } from '@/proto';
import { AnchorIncomeTimeRange } from '@/proto/api/anchor';
import classNames from 'classnames';
import { useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Toast } from 'react-vant';
import { formatAmount } from '../agency-dashboard/util';
import { Point, jumpToWithdrawHis } from '../components';
import locale from './locale';

type AnchorPointResp = {
  region: string;
  today_inr: number;
  today_point: number;
  total_inr: number;
  total_point: number;
};

type WithdrawValidateResp = {
  /* 参与活动所发放的积分 */
  activity_points: number;
  /** 是否参与活动的主播 */
  is_activity: boolean;
  /* 所在公会是否限制提现 */
  is_limit: boolean;
  /* 是否通过校验(可以提现) */
  is_pass: boolean;
};

type WithdrawLevel = {
  amount: number;
  key: string;
  points: number;
};
type WithdrawLevelResp = {
  USD?: WithdrawLevel[];
  INR?: WithdrawLevel[];
};

const IncomeDataPeroidMap = {
  [AnchorIncomeTimeRange.Income_Range_Today]: 'peroidToday',
  [AnchorIncomeTimeRange.Income_Range_Yesterday]: 'peroidYestoday',
  [AnchorIncomeTimeRange.Income_Range_Week]: 'peroidWeek'
};
const IncomeDataPeroidOptions = formatOptionsOfObj(IncomeDataPeroidMap, true);

export default function PointWithdraw() {
  const baseUrl = window.__INITIAL_STATE__.appData.data?.proxyUrl || 'http://test-api.komiapp.live/api/komi';
  const t = useLocale(locale);
  const navigate = useNavigate();
  const userInfo = useUserInfo();

  const [peroid, setPeroid] = useState<AnchorIncomeTimeRange>(AnchorIncomeTimeRange.Income_Range_Today);
  const [currency, setCurrency] = useState('USD');
  const [withdrawData, setWithdrawData] = useState<WithdrawLevelResp>();
  const [withdrawValidate, setWithdrawValidate] = useState<WithdrawValidateResp>();
  const [levels, setLevels] = useState<WithdrawLevel[]>([]);
  const [data, setData] = useState({
    point: 0,
    inrAmount: 0,
    allowWithdraw: false,
    limitWithdraw: false,
    incomeTotal: 0,
    incomeVideo: 0,
    incomeChat: 0,
    incomeGift: 0,
    incomeUpdateTime: 0,
    currencys: [] as string[]
  });
  const amount = useMemo(() => {
    return currency === 'INR' ? data.inrAmount : formatAmount(data.point);
  }, [currency, data]);

  useEffect(() => {
    if (!withdrawData) return;
    setLevels(withdrawData[currency]);
  }, [currency]);

  useEffect(() => {
    if (!userInfo) return;
    getPointsInfo();
    getIncomeInfo(peroid);
    getValidate();
  }, [userInfo]);

  const getIncomeInfo = async (peroid: AnchorIncomeTimeRange) => {
    BZ.anchor.getAnchorIncomeInfo
      .request({
        time_range: peroid
      })
      .then(res => {
        setData(prev => ({
          ...prev,
          incomeTotal: res.total_income,
          incomeVideo: res.video_income,
          incomeChat: res.chat_income,
          incomeGift: res.gift_income,
          incomeUpdateTime: res.update_time
        }));
      })
      .catch(err => {
        console.error('Failed to fetch anchor income info:', err);
        Toast.fail('Failed to fetch income info');
      });
  };
  useEffect(() => {
    getIncomeInfo(peroid);
  }, [peroid]);
  const getPointsInfo = async () => {
    const res = await requestProxy<AnchorPointResp>(baseUrl + '/anchor/get_anchor_point');
    if (res.status === 1) {
      setData(prev => ({
        ...prev,
        point: res.data.total_point,
        inrAmount: res.data.total_inr
      }));
    } else {
      Toast.fail(res.msg);
    }
  };
  const getValidate = async () => {
    const res = await requestProxy<WithdrawValidateResp>(baseUrl + '/withdraw/validate');
    console.log(res);
    if (res.status === 1) {
      setData(prev => ({
        ...prev,
        allowWithdraw: res.data.is_pass,
        limitWithdraw: res.data.is_limit
        // limitWithdraw: true
      }));
      if (res.data.is_pass) {
        getLevels();
      }
      setWithdrawValidate(res.data);
    } else {
      Toast.fail(res.msg);
    }
  };
  const getLevels = async () => {
    const res = await requestProxy<WithdrawLevelResp>(baseUrl + '/withdraw/list_level');
    console.log(res);
    if (res.status === 1) {
      setData(prev => ({
        ...prev,
        currencys: Object.keys(res.data)
      }));
      if (res.data.INR) {
        setLevels(res.data.INR);
        setCurrency('INR');
      } else if (res.data.USD) {
        setLevels(res.data.USD);
      }
      setWithdrawData(res.data);
    } else {
      Toast.fail(res.msg);
    }
  };

  return (
    <PageWrapper title={t('title')}>
      <section className="px-32px pt-20px page">
        {/* <Header
          close
          title={t('title')}
          extra={
            data.allowWithdraw && (
              <span className="text-28px text-#85818B" onClick={jumpToWithdrawHis}>
                {t('history')}
              </span>
            )
          }
        /> */}
        {/* Card */}
        <div
          className="mt-20px pt-32px px-24px pb-46px rounded-20px relative"
          style={{
            background: `url(${Pointlarge}) no-repeat right/120px, linear-gradient(90deg, #FDF4DA 0%, #FDE6C3 100%)`
          }}
        >
          <h3 className="text-28px text-#ab591e mb-40px font-medium">{t('cardTitle')}</h3>
          <div className="flex">
            <Point className="w-60px h-60px" />
            <span className="text-#D9610C ms-8px text-56px leading-60px align-middle font-bold">{data.point}</span>
          </div>
          <div className="mt-20px text-28px text-#D8734E">
            ≈ {amount} {currency}
          </div>
          <div
            onClick={() => navigate(`./details${location.search}`)}
            className="top-20px bg-#E7AD60/30 py-4px pl-14px pr-30px text-#AB591E absolute right-0 rounded-[20px_0_0_20px] bg-[url(@/assets/komi/icon-arrow-right.png)] bg-right bg-no-repeat"
            style={{ backgroundSize: '0.36rem' }}
          >
            {t('details')}
          </div>
        </div>
        {/* My Data */}
        <div className="mt-42px">
          <h2 className="text-32px text-#121212 font-bold">{t('myData')}</h2>
          <div className="mt-20px rounded-32px bg-#F9F0EC overflow-hidden">
            <div className="header text-28px text-#AE4C37 bg-#EEC6BB flex-base">
              {IncomeDataPeroidOptions.map(item => (
                <div
                  key={item.value}
                  className={classNames(
                    { 'bg-#C07D6A text-white': (+item.value as AnchorIncomeTimeRange) === peroid },
                    'py-18px h-full flex-1 text-center'
                  )}
                  onClick={() => setPeroid(+item.value)}
                >
                  {t(item.text)}
                </div>
              ))}
            </div>
            <div className="body p-20px text-28px text-#AE5E37">
              <p className="mb-20px">
                {t('incomeTotal')}: <Point className="w-36px h-36px" />{' '}
                <span className="font-medium">{data.incomeTotal}</span>
              </p>
              <p className="mb-20px">
                {t('incomeVideo')}: <Point className="w-36px h-36px" />{' '}
                <span className="font-medium">{data.incomeVideo}</span>
              </p>
              <p className="mb-20px">
                {t('incomeChat')}: <Point className="w-36px h-36px" />{' '}
                <span className="font-medium">{data.incomeChat}</span>
              </p>
              <p className="">
                {t('incomeGift')}: <Point className="w-36px h-36px" />{' '}
                <span className="font-medium">{data.incomeGift}</span>
              </p>
            </div>
            <div className="footer p-20px border-#F2D8D2 text-#AE5E37/50 border-t">
              {t('lastUpdateTime', { time: formatUnix(data.incomeUpdateTime, 'YYYY-MM-DD HH:mm') })}
            </div>
          </div>
        </div>
        {data.allowWithdraw && (
          <>
            {/* Withdrawal */}
            <div className="mt-42px">
              <h2 className="text-32px text-#121212 flex-between font-bold">
                <span>{t('withdraw')}</span>
                <span
                  className="text-28px text-#AB591E pe-36px bg-[url(@/assets/komi/icon-arrow-right.png)] bg-right bg-no-repeat"
                  style={{ backgroundSize: '0.36rem' }}
                  onClick={jumpToWithdrawHis}
                >
                  {t('history')}
                </span>
              </h2>
              {/* Currency */}

              <div className="mt-20px pb-25px">
                <div className="text-28px text-#AE4C37 flex-base gap-32px">
                  {data.currencys.map(item => (
                    <div
                      key={item}
                      onClick={() => setCurrency(item)}
                      className={classNames(
                        { 'from-#DE58FF to-#914DFF border-none bg-gradient-to-r text-white': item === currency },
                        'py-14px px-68px text-24px b border-#ddd h-full rounded-full text-center'
                      )}
                    >
                      {item}
                    </div>
                  ))}
                </div>
                <p className="mt-20px mb-36px text-28px text-#F50F0F font-medium">{t(`withdraw${currency}Tips`)}</p>
                {data.limitWithdraw && (
                  <div className="text-24px text-#F50F0F/60 mb-20px">Withdrawals has exceeded today</div>
                )}
                <div className="list">
                  {levels.map(item => (
                    <WithdrawLevel
                      key={item.key}
                      point={item.points}
                      price={`${item.amount} ${currency}`}
                      valid={data.limitWithdraw ? false : item.points <= data.point}
                      onClick={() => {
                        navigate(
                          `./submit?wd_type=${currency}&points=${item.points}&amount=${item.amount}&key=${item.key}&${location.search.slice(
                            1
                          )}`
                        );
                      }}
                    />
                  ))}
                </div>
              </div>
            </div>
          </>
        )}
      </section>
    </PageWrapper>
  );
}

function WithdrawLevel({ point, price, valid = true, onClick }) {
  return (
    <div
      className={classNames(
        'rounded-20px gap-10px flex-between py-24px px-20px mb-20px b bg-gradient-to-b to-white',
        valid ? 'from-#FFEBDB b-#FFD1B7' : 'from-#DCDCDC b-#D3D3D3 pointer-events-none'
      )}
      onClick={onClick}
    >
      <Point className={classNames('w-56px h-56px', valid ? '' : 'opacity-50')} />
      <span className={classNames('text-48px flex-1 font-bold', valid ? 'text-#D9610C' : 'text-#666')}>{point}</span>
      <span className={classNames('text-26px', valid ? 'text-#B06033' : 'text-#666')}>Arrival≈{price}</span>
    </div>
  );
}
