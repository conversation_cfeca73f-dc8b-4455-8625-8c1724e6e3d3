import DownArrow from '@/assets/komi/icon-down.png';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { formatOptionsOfObj, formatUnix } from '@/modules/utils';
import { BZ } from '@/proto';
import { PointFlowInfo } from '@/proto/api/anchor';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { List, Toast } from 'react-vant';
import { Empty } from '../agency-dashboard/components/empty';
import { KomiPicker, Point } from '../components';
import locale from './locale';

const PAGE_SIZE = 10;

// 时间区间计算函数
const getTimeRange = (period: string) => {
  const now = dayjs();
  const td = now.endOf('d').unix();

  switch (period) {
    case 'week':
      return [now.startOf('week').unix(), td];
      break;
    case 'lastWeek':
      return [now.subtract(1, 'week').startOf('week').unix(), now.subtract(1, 'week').endOf('week').unix()];
      break;
    case 'last15':
      return [now.subtract(15, 'day').startOf('day').unix(), td];
      break;
    case 'last30':
      return [now.subtract(30, 'day').startOf('day').unix(), td];
      break;
    default:
      return [now.startOf('week').unix(), now.endOf('week').unix()];
  }
};

export default function PointWithdrawDetails() {
  const t = useLocale(locale);

  const [peroid, setPeroid] = useState('week');
  const [category, setCategory] = useState('all');

  const [pickType, setPickType] = useState<PickType>(PickType.PEROID);
  const [pickerVisible, setPickerVisible] = useState(false);

  // 列表数据状态
  const [listData, setListData] = useState<PointFlowInfo[]>([]);
  const [page, setPage] = useState(1);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  const getPointFlow = async (pageNum: number = 1, reset: boolean = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const [start_time, end_time] = getTimeRange(peroid);
      const response = await BZ.anchor.getAnchorPointFlows.request({
        category: category === 'all' ? '' : category,
        page: pageNum,
        size: PAGE_SIZE,
        start_time,
        end_time
      });
      if (reset) {
        setListData(response.list);
        setPage(2);
      } else {
        setListData(prev => [...prev, ...response.list]);
        setPage(prev => prev + 1);
      }

      // 如果返回的数据少于请求的数量，说明没有更多数据了
      setHasMore(response.list.length >= PAGE_SIZE);
    } catch (error) {
      console.error('Failed to fetch point flows:', error);
      Toast.fail('Failed to load data');
    } finally {
      setLoading(false);
    }
  };

  // 加载更多数据
  const loadMoreData = () => {
    if (loading || !hasMore) return Promise.resolve();
    return getPointFlow(page);
  };

  useEffect(() => {
    // 重置数据并加载第一页
    setListData([]);
    setPage(1);
    setHasMore(true);
  }, [peroid, category]);

  useEffect(() => {
    setNavBar({ visible: 1, title: t('detailsTitle'), left_btn_config: 0 });
  }, []);

  return (
    <PageWrapper title={t('detailsTitle')}>
      <section className="page pb-40px">
        {/* Toolbar */}
        <div className="px-32px">
          <div className="text-32px text-#121212 flex-between py-20px font-medium">
            <div
              className="flex-base gap-10px"
              onClick={() => {
                setPickerVisible(true);
                setPickType(PickType.PEROID);
              }}
            >
              <span>{PeroidMap[peroid]}</span>
              <img className="w-36px h-36px" src={DownArrow} />
            </div>
            <div
              className="flex-base gap-10px"
              onClick={() => {
                setPickerVisible(true);
                setPickType(PickType.CATEGORY);
              }}
            >
              <span>{CategoryMap[category]}</span>
              <img className="w-36px h-36px" src={DownArrow} />
            </div>
          </div>
        </div>
        {/* List */}
        <div className="">
          <List finished={!hasMore} onLoad={loadMoreData} offset={30}>
            {listData.map((item, index) => (
              <IncomeItem
                key={`${item.category}-${item.ctime}-${index}`}
                category={item.category}
                time={item.ctime}
                amount={item.chg}
              />
            ))}
          </List>
          {listData.length === 0 && !hasMore && (
            <div className="py-60px text-center">
              <Empty desc={t('noData')} />
            </div>
          )}
        </div>

        {/* Picker */}
        <KomiPicker
          title={pickType === PickType.PEROID ? t('peroid') : t('category')}
          options={pickType === PickType.PEROID ? PeroidOption : CategoryOption}
          value={pickType === PickType.PEROID ? peroid : category}
          onChange={value => {
            if (!value) value = pickType === PickType.CATEGORY ? 'all' : 'week';
            setPickerVisible(false);
            if (pickType === PickType.PEROID) {
              setPeroid(value as string);
            } else {
              setCategory(value as string);
            }
          }}
          visible={pickerVisible}
          onClose={() => setPickerVisible(false)}
        />
        {/* <KomiPicker
          title={pickType === PickType.PEROID ? t('peroid') : t('category')}
          visible={pickerVisible}
          value={pickType === PickType.PEROID ? peroid : category}
          columns={pickType === PickType.PEROID ? PeroidOption : CategoryOption}
          onCancel={() => setPickerVisible(false)}
          onConfirm={(value: string) => {
            if (!value) value = pickType === PickType.CATEGORY ? 'all' : 'week';
            setPickerVisible(false);
            if (pickType === PickType.PEROID) {
              setPeroid(value);
            } else {
              setCategory(value);
            }
          }}
        /> */}
      </section>
    </PageWrapper>
  );
}

enum PickType {
  PEROID,
  CATEGORY
}

const PeroidMap = {
  week: 'This week',
  lastWeek: 'Last week',
  last15: 'Last 15 days',
  last30: 'Last 30 days'
};

const PeroidOption = formatOptionsOfObj(PeroidMap);

const CategoryMap = {
  all: 'All',
  video_call_point: 'Video income',
  chat_point: 'Chat income',
  gift_point: 'Gift income',
  withdraw_dec_point: 'Withdrawal deduction',
  withdraw_refund_point: 'Withdrawal refund',
  violation_point: 'Violation',
  other_point: 'Other'
};
const CategoryOption = formatOptionsOfObj(CategoryMap);

type IncomeItemProps = {
  category: string;
  time: number;
  freeCard?: boolean;
  amount: number;
};

function IncomeItem({ category, time, freeCard = false, amount }: IncomeItemProps) {
  const t = useLocale(locale);
  return (
    <div className="py-28px pl-32px pr-14px flex-between b-b b-#eee">
      <div className="">
        <div className="text-42px text-#0B0C20 font-semibold">
          <span className="align-middle">{t(`cate_${category}`)} </span>
          {freeCard && (
            <span className="w-60px h-48px inline-block bg-[url(@/assets/komi/icon-free-card.png)] bg-contain bg-no-repeat align-middle" />
          )}
        </div>
        <div className="text-24px text-#7B8393 mt-10px">{formatUnix(time)}</div>
      </div>
      <div className="basis-35% flex-base gap-8px flex-shrink-0 justify-end">
        <span className={classNames('text-32px font-bold', +amount < 0 ? 'text-#999' : 'text-#D9610C')}>
          {amount > 0 && '+'}
          {amount}
        </span>
        <Point className="w-48px h-48px" />
      </div>
    </div>
  );
}

function AKomiPicker() {
  return (
    <select>
      <option value="1">1</option>
      <option value="2">2</option>
      <option value="3">3</option>
      <option value="4">4</option>
    </select>
  );
}
