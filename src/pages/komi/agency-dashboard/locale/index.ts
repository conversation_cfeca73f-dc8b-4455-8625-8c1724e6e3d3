import { Resource } from 'i18next';

export default {
  en: {
    // title
    indexTitle: 'Agency dashboard',
    anchorTitle: 'My Anchor list',
    anchorViolationTitle: 'Anchor violation list',
    anchorIncomeTitle: 'Anchor income',
    subAgencyTitle: 'Sub-Agency list',
    subAgencyIncomeTitle: 'Sub-Agency income',
    totalIncomeTitle: 'Total income',
    // entry
    entry_anchorList: 'My Anchor List',
    entry_anchorViolationList: 'Anchor Violation List',
    entry_anchorIncome: 'Anchor Income',
    entry_subAgencyList: 'Sub-Agency List',
    entry_subAgencyIncome: 'Sub-Agency Income',
    entry_totalIncome: 'Total Income',
    entry_withdrawHis: 'Withdrawal History',
    entry_contact: 'Whats App',
    // table header
    th_anchor: 'Anchor',
    th_becomeTime: 'Becoming anchor time',
    th_lastActiveTime: 'Last\n active time',
    th_punish: 'Punish State',
    th_thisWeekViolation: 'This week violation',
    th_totalIncome: 'Total income($)',
    th_todayIncome: 'Today income($)',
    th_thisWeekIncome: 'This week income($)',
    th_lastWeekIncome: 'Last week income($)',
    th_subAgency: 'Sub-agency',
    th_joinTime: 'Join time',
    th_anchorCount: 'Anchor count',
    th_weeklyActive: 'Active this week',
    // common
    search: 'Search',
    noData: 'No data',
    // other
    lastUpdateTime: 'Last updated at {{time}}',
    footer_page_total: "This page's total income",
    footer_week_total: "This week's total income",
    footer_subAgencyTip: 'Only counts sub-agency income ≥$10 this week',
    subAgencyTip: 'Tips: Sub-agency is the agency that you have created.',
    expired: 'Expired',
    totalCommission: 'My {{slot}} Income',
    anchorIncome: 'Anchor Income',
    anchorCommission: 'Anchor Commission',
    subAgencyCommission: 'Sub-agency Income',
    commissionRatio: 'Commission ratio',
    nextLevelGap: '{{gap}} to next level',
    invitation: 'Invitation',
    inviteCode: 'Invite Code: {{code}}',
    contactTip: 'Only show it to your girls,and them can contact you',
    earnAnchorNum: 'Number of My Earning Anchor',
    anchorPayIncome: 'Anchor Paid Income',
    export: 'Export'
  },
  hi: {
    // title
    indexTitle: 'एजेंसी डैशबोर्ड',
    anchorTitle: 'मेरी एंकर सूची',
    anchorViolationTitle: 'एंकर उल्लंघन सूची',
    anchorIncomeTitle: 'एंकर आय',
    subAgencyTitle: 'उप-एजेंसी सूची',
    subAgencyIncomeTitle: 'उप-एजेंसी आय',
    totalIncomeTitle: 'कुल आय',
    // entry
    entry_anchorList: 'मेरी एंकर सूची',
    entry_anchorViolationList: 'एंकर उल्लंघन सूची',
    entry_anchorIncome: 'एंकर आय',
    entry_subAgencyList: 'उप-एजेंसी सूची',
    entry_subAgencyIncome: 'उप-एजेंसी आय',
    entry_totalIncome: 'कुल आय',
    entry_withdrawHis: 'निकासी इतिहास',
    entry_contact: 'व्हाट्सएप',
    // table header
    th_anchor: 'एंकर',
    th_becomeTime: 'एंकर बनने का समय',
    th_lastActiveTime: 'अंतिम सक्रिय समय',
    th_punish: 'दंड स्थिति',
    th_thisWeekViolation: 'इस सप्ताह का उल्लंघन',
    th_totalIncome: 'कुल आय($)',
    th_todayIncome: 'आज की आय($)',
    th_thisWeekIncome: 'इस सप्ताह की आय($)',
    th_lastWeekIncome: 'पिछले सप्ताह की आय($)',
    th_subAgency: 'उप-एजेंसी',
    th_joinTime: 'शामिल होने का समय',
    th_anchorCount: 'एंकर संख्या',
    th_weeklyActive: 'इस सप्ताह सक्रिय',
    // common
    search: 'खोज',
    noData: 'कोई डेटा नहीं',
    // other
    lastUpdateTime: 'अंतिम अपडेट {{time}} पर',
    footer_page_total: 'इस पेज की कुल आय',
    footer_week_total: 'इस सप्ताह की कुल आय',
    footer_subAgencyTip: 'इस सप्ताह केवल उप-एजेंसी आय ≥$10 की गणना करता है',
    subAgencyTip: 'टिप: उप-एजेंसी वह एजेंसी है जो आपने बनाई है।',
    expired: 'समाप्त हो गया',
    totalCommission: 'मेरी {{slot}} आय',
    anchorIncome: 'एंकर आय',
    anchorCommission: 'एंकर कमीशन',
    subAgencyCommission: 'उप-एजेंसी कमीशन',
    commissionRatio: 'कमीशन अनुपात',
    nextLevelGap: 'अगले स्तर तक {{gap}}',
    invitation: 'निमंत्रण',
    inviteCode: 'आमंत्रण कोड: {{code}}',
    contactTip: 'इसे केवल अपनी लड़कियों को दिखाएं, और वे आपसे संपर्क कर सकती हैं',
    earnAnchorNum: 'मेरे कमाई वाले एंकर की संख्या',
    anchorPayIncome: 'एंकर द्वारा भुगतान की गई आय',
    export: 'निर्यात'
  }
} satisfies Resource;
