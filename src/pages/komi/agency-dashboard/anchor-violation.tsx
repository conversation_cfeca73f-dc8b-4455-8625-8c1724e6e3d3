import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { BZ } from '@/proto';
import { GetAnchorViolationListResp_ViolationItem, ViolationListType } from '@/proto/api/anchor';
import { Page } from '@/proto/protobuf/api/common/common';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { List } from 'react-vant';
import { Point } from '../components';
import { Empty } from './components/empty';
import { AnchorInfo } from './components/info';
import { Table } from './components/table';
import locale from './locale';

const PAGE_SIZE = 20;

export default function AnchorViolation() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();
  const [listData, setListData] = useState<GetAnchorViolationListResp_ViolationItem[]>([]);
  const pageCache = useRef<Page>();
  const loading = useRef(false);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setNavBar({ left_btn_config: 0, title: t('anchorViolationTitle'), visible: 1 });
  }, []);

  useEffect(() => {
    setHasMore(true);
  }, [userInfo]);

  const loadMoreData = async () => {
    if (loading.current || !hasMore) return Promise.resolve();
    loading.current = true;
    const res = await BZ.anchor.getAnchorViolationList.request({
      page: pageCache.current || { offset: 0, limit: PAGE_SIZE }
    });
    setListData(prev => [...prev, ...res.list]);
    pageCache.current = res.page;
    setHasMore(res.page!.has_more || res.list.length === PAGE_SIZE || false);
    loading.current = false;
  };

  return (
    <PageWrapper title={t('anchorViolationTitle')}>
      <div className="page bg-#F4F4FB">
        <List offset={30} finished={!hasMore} onLoad={loadMoreData}>
          <Table
            header={{
              cells: [t('th_anchor'), t('th_punish'), t('th_thisWeekViolation')]
            }}
            widths={['40%', '30%', '28%']}
            data={listData.map((item, i) => ({
              cells: [
                <AnchorInfo nickname={item.name} uid={item.uid} avatar={item.avatar} />,
                <PunishState status={item.punish_status} time={+item.punish_time} />,
                item.this_week_violation ? <ViolationItem point={item.this_week_violation} /> : null
              ]
            }))}
          />
        </List>
        {listData.length === 0 && !hasMore && userInfo && (
          <div className="py-60px text-center">
            <Empty desc={t('noData')} />
          </div>
        )}
      </div>
    </PageWrapper>
  );
}

const PunishMap = {
  [ViolationListType.VIOLATION_LIST_TYPE_REDUCE_POINTS]: 'Reduce Points',
  [ViolationListType.VIOLATION_LIST_TYPE_ACCOUNT_BAN]: 'Ban Account',
  [ViolationListType.VIOLATION_LIST_TYPE_DELETE]: 'Delete Account'
};
function PunishState({ status, time }: { status: number; time: number }) {
  return (
    <div>
      <div className="text-24px font-bold">{PunishMap[status]}</div>
      <span className="text-20px text-#85818B/60">{dayjs.unix(time).format('YYYY-MM-DD HH:mm')}</span>
    </div>
  );
}

function ViolationItem({ point = 0 }: { point: number }) {
  return (
    <div className="gap-10px flex-center w-full">
      <span className="text-#ea0b0b text-24px font-medium">
        {/* {+point > 0 && '+'} */}
        {point}
      </span>
      <Point className="w-36px h-36px" />
    </div>
  );
}
