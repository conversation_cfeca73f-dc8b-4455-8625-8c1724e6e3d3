// import { BZ } from '@/proto';
import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { formatUnix } from '@/modules/utils';
import { BZ } from '@/proto';
import { GetSubAgencyListResp_SubAgencyItem } from '@/proto/api/anchor';
import { Page } from '@/proto/protobuf/api/common/common';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { List } from 'react-vant';
import { Empty } from './components/empty';
import { AgencyInfo } from './components/info';
import { Table } from './components/table';
import locale from './locale';

const PAGE_SIZE = 20;

export default function SubAgencyList() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();
  const [listData, setListData] = useState<GetSubAgencyListResp_SubAgencyItem[]>([]);
  const pageCache = useRef<Page>();
  const loading = useRef(false);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setHasMore(true);
  }, [userInfo]);

  const loadMoreData = async (e: boolean | string) => {
    if (loading.current || !hasMore) return Promise.resolve();
    loading.current = true;
    const res = await BZ.anchor.getSubAgencyList.request({
      datetime: +dayjs().format('YYYYMMDD'),
      page: pageCache.current || { offset: 0, limit: PAGE_SIZE }
    });
    setListData(prev => [...prev, ...res.list]);
    pageCache.current = res.page;
    loading.current = false;
    if (!res.page) {
      setHasMore(false);
      return;
    }
    setHasMore(res?.page?.has_more || res.list.length === PAGE_SIZE || false);
  };

  useEffect(() => {
    setNavBar({ left_btn_config: 0, title: t('subAgencyTitle'), visible: 1 });
  }, []);

  return (
    <PageWrapper title={t('subAgencyTitle')}>
      <div className="page pb-48px bg-#F4F4FB">
        <List offset={50} finished={!hasMore} onLoad={loadMoreData}>
          <Table
            className="pb-36px"
            header={{
              cells: [t('th_subAgency'), t('th_joinTime'), t('th_anchorCount'), t('th_weeklyActive')]
            }}
            widths={['35%', '25%', '20%', '20%']}
            data={listData.map((item, i) => ({
              cells: [
                <AgencyInfo nickname={item.name} uid={item.id} />,
                formatUnix(item.join_time, 'YYYY-MM-DD\n HH:mm'),
                item.total_anchor,
                <span className="text-#00BA29">{item.active_anchor}</span>
              ],
              extra: (
                <div className="text-#666 flex-between px-32px mb-8px w-full">
                  <div className="text-#85818B grow">
                    Whatsapp: {item.whatsapp} <CopyIcon fill="#333" content={item.whatsapp} />
                  </div>
                  {item.is_expired && (
                    <div className="text-18px py-6px px-28px bg-#CFCFCF rounded-12px text-white">{t('expired')}</div>
                  )}
                </div>
              )
            }))}
          />
        </List>
        {listData.length === 0 && !hasMore && userInfo && (
          <div className="py-60px text-center">
            <Empty desc={t('noData')} />
          </div>
        )}
        <p className="text-#85818B text-24px bg-#F4F4FB py-8px b-t absolute bottom-0 w-full text-center">
          {t('subAgencyTip')}
        </p>
      </div>
    </PageWrapper>
  );
}
