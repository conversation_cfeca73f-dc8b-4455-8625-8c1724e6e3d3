// import { BZ } from '@/proto';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { formatUnix } from '@/modules/utils';
import { BZ } from '@/proto';
import { GetIncomeListResp_IncomeItem, IncomeListType } from '@/proto/api/anchor';
import { Page } from '@/proto/protobuf/api/common/common';
import { Search } from '@icon-park/react';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { Button, Input, List } from 'react-vant';
import { Empty } from './components/empty';
import { AgencyInfo } from './components/info';
import { Table } from './components/table';
import locale from './locale';
import { formatAmount } from './util';

const PAGE_SIZE = 20;

const t = dayjs();
const start_datetime = +t.format('YYYYMMDD');
const end_datetime = start_datetime;
const week_day = +t.day(1).format('YYYYMMDD');

export default function AgencyIncome() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();
  const [data, setData] = useState({ week_total_income: 0, last_update_time: 0 });
  const [searchValue, setSearchValue] = useState<string>('');
  const [listData, setListData] = useState<GetIncomeListResp_IncomeItem[]>([]);
  const pageCache = useRef<Page>();
  const loading = useRef(false);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setHasMore(true);
  }, [userInfo]);

  const onSearch = () => {
    // 模拟搜索逻辑
    console.log('Searching for:', searchValue);
    setListData([]);
    pageCache.current = undefined;
    setHasMore(true);
    // loadMoreData(searchValue);
  };

  const loadMoreData = async (e: boolean | string) => {
    if (loading.current || !hasMore) return Promise.resolve();
    const business_id = searchValue ? searchValue : undefined;
    loading.current = true;
    const res = await BZ.anchor.getIncomeList.request({
      business_id,
      start_datetime,
      end_datetime,
      week_day,
      income_type: IncomeListType.INCOME_LIST_TYPE_SUB_AGENCY,
      page: pageCache.current || { offset: 0, limit: PAGE_SIZE }
    });
    console.log(res);
    loading.current = false;
    pageCache.current = res.page;
    setHasMore(res.page?.has_more || res.list.length === PAGE_SIZE || false);
    setListData(prev => [...prev, ...res.list]);
    setData({
      week_total_income: res.week_total_income,
      last_update_time: res.last_update_time
    });
  };

  useEffect(() => {
    setNavBar({ left_btn_config: 0, title: t('subAgencyIncomeTitle'), visible: 1 });
  }, []);

  return (
    <PageWrapper title={t('subAgencyIncomeTitle')}>
      <div className="page pb-150px bg-#F4F4FB">
        {/* Search Bar */}
        <div className="sticky left-0 top-0 z-10 bg-white">
          <div className="h-92px flex-between px-16px gap-20px">
            <div className="h-60px px-12px flex-between gap-16px b border-#DADADA grow rounded-full">
              <Search size={18} fill="#837F8A" />
              <Input
                className="text-#121212 w-full"
                placeholder="Search agency id"
                value={searchValue}
                onChange={v => setSearchValue(v)}
              />
            </div>
            <Button
              type="primary"
              disabled={!searchValue}
              round
              size="small"
              color="linear-gradient(90deg, #914DFF, #DE58FF)"
              className="!w-180px !h-60px"
              onClick={onSearch}
            >
              {t('search')}
            </Button>
          </div>
        </div>
        {/* Table */}

        <List offset={50} finished={!hasMore} onLoad={loadMoreData}>
          <Table
            header={{
              wrapClassName: 'top-93px',
              cells: [
                t('th_subAgency'),
                t('th_totalIncome'),
                t('th_todayIncome'),
                t('th_thisWeekIncome'),
                t('th_lastWeekIncome')
              ]
            }}
            widths={['130px', '80px', '80px', '80px', '80px']}
            data={listData.map((item, i) => ({
              cells: [
                <AgencyInfo nickname={item.name} uid={item.id} />,
                formatAmount(item.total_income),
                formatAmount(item.today_income),
                <span className="text-#EA0B0B font-medium">{formatAmount(item.this_week_income)}</span>,
                formatAmount(item.last_week_income)
              ]
            }))}
          />
        </List>
        {listData.length === 0 && !hasMore && (
          <div className="py-60px text-center">
            <Empty desc={t('noData')} />
          </div>
        )}
        {/* Footer Bar */}
        <div className="b-t b-#979797/10 pt-20px pb-20px absolute bottom-0 w-full bg-white">
          <p className="text-24px text-#85818B text-center">
            {t('lastUpdateTime', { time: formatUnix(data.last_update_time) })}
          </p>
          <p className="py-6px text-#85818B text-20px text-center">{t('footer_subAgencyTip')}</p>
          <div className="h-40px flex-between px-24px text-32px mt-8px font-bold">
            <span className="text-#121212">{t('footer_page_total')}</span>
            <span className="text-#EA0B0B">${formatAmount(data.week_total_income)}</span>
          </div>
        </div>
      </div>
    </PageWrapper>
  );
}
