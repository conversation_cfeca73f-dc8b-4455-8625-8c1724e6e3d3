import { CopyIcon, copy } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { formatAvatatUrl, getEnv } from '@/modules/utils';
import { BZ } from '@/proto';
import { GetGuildMasterInfoResp } from '@/proto/api/anchor';
import { Right } from '@icon-park/react';
import { QuestionO } from '@react-vant/icons';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { Fragment, useEffect, useMemo, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Dialog, Popover } from 'react-vant';
import { highLight } from '../../../modules/utils';
import { jumpToWithdrawHis } from '../components';
import locale from './locale';
import { formatAmount } from './util';

export default function AgencyDashboard() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();

  const [data, setData] = useState<GetGuildMasterInfoResp>({
    total_commission: 0,
    anchor_commission: 0,
    sub_agency_commission: 0,
    last_update_times: 0,
    commission_ratio: 0,
    next_commission_ratio: 10,
    current_points: 0,
    next_level_points: 0,
    nickname: '',
    invite_code: '',
    contact_info: ''
  });

  const commissitionProcess = useMemo(() => (data.current_points / data.next_level_points) * 100, [data]);

  useEffect(() => {
    if (!userInfo) {
      return;
    }
    BZ.anchor.getGuildMasterInfo.request({}).then(res => {
      console.log(res);
      res.total_commission = formatAmount(res.total_commission);
      res.anchor_commission = formatAmount(res.anchor_commission);
      res.sub_agency_commission = formatAmount(res.sub_agency_commission);
      setData(o => ({ ...o, ...res }));
    });
  }, [userInfo]);

  return (
    <PageWrapper title={t('indexTitle')} className="to-#F4F4FB bg-gradient-to-b from-white">
      <div className="page px-32px bg-[url(@/assets/komi/guild-home-bg.jpg)] bg-contain bg-top bg-no-repeat">
        {/* Anchor Info */}
        <div className="px-16px py-46px flex-base gap-44px">
          <img
            src={formatAvatatUrl(userInfo?.uid, getEnv())}
            className="w-120px h-120px rounded-full object-cover"
            onError={(e: any) => {
              e.target.src =
                userInfo?.sex === 'male'
                  ? `https://res.komiapp.live/common/user/avatar/default/1_male_default_head.png`
                  : `https://res.komiapp.live/common/user/avatar/default/1_anchor_default_head.png`;
            }}
          />
          <div className="flex flex-col">
            <div className="text-32px mb-10px max-w-400px overflow-hidden text-ellipsis whitespace-nowrap font-bold text-white">
              {data?.nickname}
            </div>
            <p className="text-26px text-white/60">ID: {userInfo?.uid}</p>
          </div>
        </div>
        {/* Panel */}
        <div className="rounded-16px mb-16px px-32px py-20px bg-white shadow-md">
          <div className="flex-between mb-36px">
            <p className="text-32px font-bold">This Week</p>
            <p className="text-20px leading-28px text-#121212 whitespace-pre-line text-right">
              {t('lastUpdateTime', { time: '\n' + dayjs.unix(data.last_update_times).format('YYYY-MM-DD HH:mm') })}
            </p>
          </div>
          <div className="flex-between gap-28px">
            <div className="relative">
              <span className="text-#D14EF0 text-40px font-bold">${data.total_commission}</span>
              <p className="text-24px text-#85818B/60">
                {highLight(
                  t('totalCommission'),
                  'slot',
                  <Popover
                    placement="right"
                    className="align-text-top"
                    reference={<QuestionO className="text-#85818b text-28px" />}
                  >
                    <div className="text-24px p-32px w-520px leading-36px text-#85818B">
                      <p>
                        ${data.total_commission}=${formatAmount(data.current_points)}*{data.commission_ratio}%+$
                        {data.sub_agency_commission}*10%
                      </p>
                      <p>My income=Anchor income*Ratio+Sub-agency income*10%</p>
                      <p className="text-#EA0B0B text-20px">*Sub-agency≥$10, Sub-agency ratio=10%</p>
                    </div>
                  </Popover>
                ).map((item, i) => (
                  <Fragment key={i}>{item}</Fragment>
                ))}
              </p>
            </div>
            <div className="b-r b-#979797/20 h-84px"></div>
            <div>
              <span className="text-#EA0B0B text-40px font-bold">${formatAmount(data.current_points)}</span>
              <p className="text-24px text-#85818B/60">{t('anchorIncome')}</p>
            </div>
            <div className="b-r b-#979797/20 h-84px"></div>
            <div>
              <span className="text-#FF8200 text-40px font-bold">${data.sub_agency_commission}</span>
              <p className="text-24px text-#85818B/60">{t('subAgencyCommission')}</p>
            </div>
          </div>
          {/* Commission ratio */}
          <div className="mt-36px mb-24px">
            <h3 className="text-32px font-bold">{t('commissionRatio')}</h3>
            <div className="text-24px flex-between gap-16px">
              <div className="text-#85818B shrink-0">
                {t('nextLevelGap', { gap: `$${formatAmount(data.next_level_points)}` })}
              </div>
              <div className="flex-between gap-12px">
                <span className="text-#121212">{data.commission_ratio}%</span>
                <div
                  className="bg-#D9CEFF h-16px w-276px overflow-hidden rounded-full"
                  style={
                    {
                      '--progress': `clamp(0%, ${commissitionProcess}%, 100%)`
                    } as React.CSSProperties
                  }
                >
                  <div className="from-#4E80FE to-#C045FD duration-350 h-16px w-[--progress] rounded-full bg-gradient-to-r transition-all"></div>
                </div>
                <span className="text-#121212">{data.next_commission_ratio}%</span>
              </div>
            </div>
          </div>
        </div>
        {/* Invitation */}
        <div
          className="flex-base rounded-16px ps-8% gap-16px py-12px aspect-[328/55] bg-[url(@/assets/komi/inviting-card-bg.png)] bg-contain bg-center bg-no-repeat text-white"
          onClick={() => copy(formatInviteUrl(data))}
        >
          <div className="w-88px h-88px rounded-full" />
          <div>
            <p className="text-36px">{t('invitation')}</p>
            <p className="text-24px flex-base gap-8px" onClick={e => e.stopPropagation()}>
              <span>{t('inviteCode', { code: data.invite_code })}</span>
              <CopyIcon content={data.invite_code} />
            </p>
          </div>
        </div>
        {/* Entry List */}
        <div className="mt-28px">
          <EntryItem
            title={t('entry_anchorList')}
            to={'./anchor-list' + location.search}
            cn={'bg-[url(@/assets/komi/icon-anchor-list.png)]'}
          />
          <EntryItem
            title={t('entry_anchorViolationList')}
            to={'./anchor-violation' + location.search}
            cn={'bg-[url(@/assets/komi/icon-anchor-vio.png)]'}
          />
          <EntryItem
            title={t('entry_anchorIncome')}
            to={'./anchor-income' + location.search}
            cn={'bg-[url(@/assets/komi/icon-anchor-income.png)]'}
          />
          <EntryItem
            title={t('entry_subAgencyList')}
            to={'./sub-agency-list' + location.search}
            cn={'bg-[url(@/assets/komi/icon-guild.png)]'}
          />
          <EntryItem
            title={t('entry_subAgencyIncome')}
            to={'./sub-agency-income' + location.search}
            cn={'bg-[url(@/assets/komi/icon-guild-income.png)]'}
          />
          <EntryItem
            title={t('entry_totalIncome')}
            to={'./income-list' + location.search}
            cn={'bg-[url(@/assets/komi/icon-income.png)]'}
          />
          <EntryItem
            title={t('entry_withdrawHis')}
            action={jumpToWithdrawHis}
            cn={'bg-[url(@/assets/komi/icon-withdraw-black.png)]'}
          />
          <EntryItem
            title={t('entry_contact')}
            action={() => {
              Dialog.alert({
                title: 'Whats App',
                theme: 'round-button',
                message: (
                  <div className="text-28px text-left">
                    <div className="text-#121212 my-36px b b-#dadada px-32px py-16px rounded-full">
                      {data.contact_info || ' '}
                    </div>
                    <p className="text-#85818B text-center">{t('contactTip')}</p>
                  </div>
                ),
                confirmButtonColor: '#D14EF0',
                confirmButtonText: 'OK'
              });
            }}
            cn={'bg-[url(@/assets/komi/icon-whatsapp-black.png)]'}
          />
        </div>
      </div>
    </PageWrapper>
  );
}

function EntryItem({ title, to, action, cn }: { title: string; to?: string; action?: () => void; cn: string }) {
  const navigate = useNavigate();
  return (
    <div
      className="flex-between mb-20px h-80px rounded-12px ps-20px pe-10px gap-16px py-32px w-full bg-white"
      onClick={() => (action ? action() : navigate(to!))}
    >
      <div className={classNames('w-40px h-40px bg-contain bg-center bg-no-repeat', cn)} />
      <p className="text-28px flex-1">{title}</p>
      <div className="flex-center gap-12px">
        <Right size="24" fill="#CFCFCF" />
      </div>
    </div>
  );
}

const formatInviteUrl = (d: GetGuildMasterInfoResp) => {
  const c = d.contact_info ? Number(d.contact_info.replace('-', '.')).toString(36) : '';
  return `${location.origin}/komi/invite?code=${d.invite_code}&c=${c}&n=${encodeURIComponent(d.nickname)}`;
};
