import { ReactNode } from 'react';
import './index.css';

export type TableProps = {
  header: { cells: ReactNode[]; wrapClassName?: string; className?: string; style?: React.CSSProperties };
  data: { cells: ReactNode[]; extra?: ReactNode }[];
  widths: string[];
  className?: string;
};

export function Table(props: TableProps) {
  return (
    <div className={`table ${props.className}`}>
      <div className="table-container">
        <div className={`sticky top-0 bg-white ${props.header?.wrapClassName}`}>
          <div className={`table-header ${props.header?.className}`}>
            {props.header?.cells.map((item, index) => (
              <div
                className="table-item"
                style={{
                  width: props.widths[index],
                  flexBasis: props.widths[index]
                }}
                key={index}
              >
                {item}
              </div>
            ))}
          </div>
        </div>
        {props.data.map((row, index) => (
          <div className="table-row-wrap" key={index}>
            <div className="table-row">
              {row.cells.map((item, index) => (
                <div
                  className="table-item"
                  style={{
                    width: props.widths[index],
                    flexBasis: props.widths[index]
                  }}
                  key={index}
                >
                  {item}
                </div>
              ))}
            </div>
            {row.extra}
          </div>
        ))}
      </div>
    </div>
  );
}
