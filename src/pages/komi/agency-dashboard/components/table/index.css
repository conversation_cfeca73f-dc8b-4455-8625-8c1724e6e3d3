.table {
  width: 100%;
}

.table-header,
.table-row {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}

.table-header {
  background: linear-gradient(315deg, #8b6bf0 0%, #b39df9 100%);
  color: #fff;
  font-size: 28px;
}
.table-row-wrap {
  background-color: #f4f4fb;
}
.table-row {
  width: 100%;
  margin: 0 auto;
  color: #121212;
  font-weight: 500;
}

.table-item {
  display: inline-block;
  text-align: center;
  white-space: pre-wrap;
}
.table-item:not(:last-child) {
  align-self: stretch;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-header .table-item:not(:last-child) {
  /* border-color: #e9a065; */
  padding: 12px;
}

.table-row .table-item:not(:last-child) {
  border-color: #fff;
}

.table-row-wrap:not(:last-child) {
  border-bottom: 1px solid #9797971a;
}

.table-row > .table-item {
  padding: 12px 0;
}
