import { CopyIcon } from '@/components/copy-icon';
import { formatAvatatUrl } from '@/modules/utils';
import classNames from 'classnames';

type Prop = {
  uid: string;
  nickname: string;
  avatar?: string;
  isBan?: boolean;
  isDel?: boolean;
};

export function AnchorInfo({ nickname, uid, avatar = '', isBan, isDel }: Prop) {
  return (
    <div className="flex-base ps-12px gap-16px w-full text-left">
      <div
        className={classNames(
          'w-60px h-60px border-1 relative overflow-hidden rounded-full border-[#aaa]',
          isBan &&
            'after:absolute after:left-0 after:top-0 after:flex after:h-full after:w-full after:items-center after:justify-center after:bg-black/50 after:text-red-400 after:content-["Ban"]',
          isDel &&
            'after:absolute after:left-0 after:top-0 after:flex after:h-full after:w-full after:items-center after:justify-center after:bg-black/50 after:text-red-400 after:content-["Del"]'
        )}
      >
        <img
          src={avatar || formatAvatatUrl(uid)}
          onError={(e: any) => {
            e.target.src = `https://res.komiapp.live/common/user/avatar/default/1_anchor_default_head.png`;
          }}
        />
      </div>
      <div>
        <p className="max-w-200px overflow-hidden text-ellipsis whitespace-nowrap">{nickname}</p>
        <p className="text-#858a8b/60 mt-4px text-20px">
          ID: {uid} <CopyIcon fill="#858a8b" content={uid} />
        </p>
      </div>
    </div>
  );
}
export function AgencyInfo({ nickname, uid }) {
  return (
    <div className="flex-base ps-32px gap-16px w-full text-left">
      <div>
        <p className="max-w-240px overflow-hidden text-ellipsis whitespace-nowrap">{nickname}</p>
        <p className="text-#858a8b/60 mt-4px">
          ID: {uid} <CopyIcon fill="#858a8b" content={uid} />
        </p>
      </div>
    </div>
  );
}
