import { CopyIcon } from '@/components/copy-icon';
import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { formatUnix } from '@/modules/utils';
import { BZ } from '@/proto';
import { GetAnchorListResp_AnchorInfo } from '@/proto/api/anchor';
import { Page } from '@/proto/protobuf/api/common/common';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { List } from 'react-vant';
import { Empty } from './components/empty';
import { AnchorInfo } from './components/info';
import { Table } from './components/table';
import locale from './locale';

const PAGE_SIZE = 20;

export default function AnchorList() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();
  const [listData, setListData] = useState<GetAnchorListResp_AnchorInfo[]>([]);
  const pageCache = useRef<Page>();
  const loading = useRef(false);
  const [hasMore, setHasMore] = useState(false);
  useEffect(() => {
    setNavBar({ left_btn_config: 0, title: t('anchorTitle'), visible: 1 });
  }, []);
  useEffect(() => {
    setHasMore(true);
  }, [userInfo]);

  const loadMoreData = async (e: boolean | string) => {
    if (loading.current || !hasMore) return Promise.resolve();
    loading.current = true;
    const res = await BZ.anchor.getAnchorList.request({
      datetime: +dayjs().format('YYYYMMDD'),
      page: pageCache.current || { offset: 0, limit: PAGE_SIZE }
    });
    setListData(prev => [...prev, ...res.list]);
    pageCache.current = res.page;
    setHasMore(res.page?.has_more || res.list.length === PAGE_SIZE || false);
    loading.current = false;
  };

  return (
    <PageWrapper title={t('anchorTitle')}>
      <div className="page bg-#F4F4FB">
        <List offset={30} finished={!hasMore} onLoad={loadMoreData}>
          <Table
            header={{
              cells: [t('th_anchor'), t('th_becomeTime'), t('th_lastActiveTime')]
            }}
            widths={['46%', '27%', '27%']}
            data={listData.map((item, i) => ({
              cells: [
                <AnchorInfo
                  nickname={item.nickname}
                  avatar={item.avatar}
                  uid={item.uid}
                  isDel={item.is_delete}
                  isBan={item.is_ban}
                />,
                formatUnix(item.become_time, 'YYYY-MM-DD\n HH:mm'),
                formatUnix(item.last_active_time, 'YYYY-MM-DD\n HH:mm')
              ],
              extra: (
                <div className="ps-86px text-#85818B mb-16px">
                  Whatsapp: {item.contact_information} <CopyIcon fill="#121212" content={item.contact_information} />
                </div>
              )
            }))}
          />
        </List>
        {listData.length === 0 && !hasMore && userInfo && (
          <div className="py-60px text-center">
            <Empty desc={t('noData')} />
          </div>
        )}
      </div>
    </PageWrapper>
  );
}
