import PageWrapper from '@/components/hybrid-page-wrap';
import { useLocale, useUserInfo } from '@/hooks';
import { setNavBar } from '@/modules/bridge';
import { BZ } from '@/proto';
import { GetMyTotalIncomeResp_TotalIncomeItem } from '@/proto/api/anchor';
import { Page } from '@/proto/protobuf/api/common/common';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { List } from 'react-vant';
import { Empty } from './components/empty';
import locale from './locale';
import { formatAmount } from './util';

const PAGE_SIZE = 5;
const MAX_LIST_SIZE = 20;

export default function IncomeList() {
  const t = useLocale(locale);
  const userInfo = useUserInfo();

  const [listData, setListData] = useState<GetMyTotalIncomeResp_TotalIncomeItem[]>([]);
  const pageCache = useRef<Page>();
  const loading = useRef(false);
  const [hasMore, setHasMore] = useState(false);

  useEffect(() => {
    setNavBar({ left_btn_config: 0, title: t('totalIncomeTitle'), visible: 1 });
  }, []);

  useEffect(() => {
    setHasMore(true);
  }, [userInfo]);

  const loadMoreData = async (e: boolean | string) => {
    if (loading.current || !hasMore) return Promise.resolve();
    loading.current = true;
    const res = await BZ.anchor.getMyTotalIncome.request({
      page: pageCache.current || { offset: 0, limit: PAGE_SIZE }
    });
    setListData(prev => [...prev, ...res.list]);
    pageCache.current = res.page;
    loading.current = false;
    if (!res.page || listData.length + res.list.length === MAX_LIST_SIZE) {
      setHasMore(false);
      return;
    }
    setHasMore(res.page?.has_more || res.list.length === PAGE_SIZE || false);
  };

  return (
    <PageWrapper title={t('totalIncomeTitle')}>
      <div className="page px-32px pt-36px bg-#F4F4FB">
        <List offset={30} finished={!hasMore} onLoad={loadMoreData}>
          {listData.map(item => (
            <IncomeCard key={item.day_week} {...item} t={t} />
          ))}
        </List>
        {listData.length === 0 && !hasMore && (
          <div className="py-60px text-center">
            <Empty desc={t('noData')} />
          </div>
        )}
      </div>
    </PageWrapper>
  );
}

function IncomeCard({
  t,
  ...prop
}: GetMyTotalIncomeResp_TotalIncomeItem & { t: (key: string, params?: Record<string, string>) => string }) {
  const d = dayjs(prop.day_week.toString());
  return (
    <div className="py-24px px-20px rounded-16px mb-24px bg-white shadow-md">
      <div className="text-#121212 text-28px font-medium">
        {d.format('YYYY-MM-DD')}~{d.add(1, 'week').format('YYYY-MM-DD')}
      </div>
      <div className="bg-#FFEEDC rounded-16px py-20px px-24px">
        <div className="flex-evenly gap-20px border-#979797/10 pb-16px b-b b-#AE5E37/20">
          <div className="flex-1">
            <div className="text-40px text-#D14EF0 font-bold">$ {formatAmount(prop.total_commission)}</div>
            <div className="text-24px text-#AE5E37/50">{t('totalCommission', { slot: '' })}</div>
          </div>
          <div className="flex-1">
            <div className="text-40px text-#EA0B0B font-bold">$ {formatAmount(prop.anchor_commission)}</div>
            <div className="text-24px text-#AE5E37/50">{t('anchorCommission')}</div>
          </div>
          <div className="flex-1">
            <div className="text-40px text-#FF8200 font-bold">$ {formatAmount(prop.sub_agency_commission)}</div>
            <div className="text-24px text-#AE5E37/50">{t('subAgencyCommission')}</div>
          </div>
        </div>
        <div className="text-24px mt-16px text-#AE5E37">
          {t('commissionRatio')}: {prop.commision_ratio}%
        </div>
      </div>
      <div className="bg-#FFEEDC rounded-16px mt-20px px-24px py-16px relative">
        <div className="text-24px text-#AE5E37 leading-relaxed">
          <p>
            {t('earnAnchorNum')}: {prop.number_of_earning_anchor}
          </p>
          <p>
            {t('anchorPayIncome')}: ${formatAmount(prop.anchor_paid_income)}
          </p>
        </div>
        {/* <div className="export right-24px top-30% absolute">
          <div className="flex-base gap-12px text-24px text-#121212 font-bold">
            <Download /> {t('export')}
          </div>
        </div> */}
      </div>
    </div>
  );
}
