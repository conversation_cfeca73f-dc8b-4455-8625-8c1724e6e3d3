import { call<PERSON>and<PERSON> } from '@/modules/bridge';
import { requestProxy } from '@/modules/request';

interface DownloadTokenParams {
  show_uid?: string;
  min_date?: string;
  max_date?: string;
  page?: number;
  page_size?: number;
  period?: ReportType;
  [key: string]: any;
}
export enum ReportType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly'
}

export const exportGuildData = async (params: DownloadTokenParams) => {
  const baseUrl = window?.__INITIAL_STATE__.appData.data.baseUrl;
  const tokenData = await requestProxy(`${baseUrl}/h5/stat/guild/unified-report-download-token`);
  const token = tokenData?.download_token || '';
  const url = `${baseUrl}/h5/stat/guild/unified-report-download?lan=${params.lang}&anm=${params.anm}&uid=${params.uid}&download_token=${token}`;
  const encodedUrl = encodeURIComponent(url); // 转义
  callHandler('openUrl', {
    url: 'crushu://www.crushu.net?page=browser&url=' + encodedUrl
  });
};

/* exportGuildData({
  uid: '',
  lan: '',
  anm: 'cu',
  show_uid: '',
  min_date: getDayFromTimestamp(start),
  max_date: getDayFromTimestamp(end),
  period: ReportType.WEEKLY
}); */
